/**
 * 分类列表
 * @param {*} data 
 * @param {number} data.type 类型：13-背景音乐、14-配音
 * @returns 
 */
export const getCategoryList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/resource/categoryList", {
      params: data
    }).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 设置素材列表
 * @param {*} data 
 * @param {number} data.type 类型：13-背景音乐、14-配音
 * @param {string} data.name 名称
 * @param {number} data.categoryId 分类id (非必须)
 * @param {number} data.pageNo 页码
 * @param {number} data.pageLimit 每页条数
 * @returns 
 */
export const getSourceList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/resource/list", {
      params: data
    }).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 添加素材
 * @param {*} data 
 * @param {number} data.type 类型：13-背景音乐、14-配音
 * @param {string} data.resId 音频资源resId
 * @param {string} data.name 名称
 * @param {string} data.cover 封面id
 * @param {string} data.des 描述
 * @param {number} data.folderId 分类id
 * @param {number} data.fileSize 音频文件大小
 * @param {string} data.fileType 文件类型
 * @returns 
 */
export const addMaterial = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/resource/add", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 修改素材
 * @param {*} data 
 * @param {string} data.resId 音频资源resId
 * @param {string} data.name 名称
 * @param {string} data.cover 封面id
 * @param {string} data.des 描述
 * @param {number} data.folderId 分类id
 * @returns 
 */
export const updateMaterial = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/resource/update", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 删除素材
 * @param {*} data 
 * @param {number} data.resIds 素材id
 * @param {number} data.cover 封面id
 * @returns 
 */
export const deleteMaterial = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/resource/delete", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};
