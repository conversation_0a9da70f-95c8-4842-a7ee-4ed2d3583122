<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){
	out.println("没有权限");
	return;
}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>特殊员工分配设置</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-setCountLimit">
		<!--查询条件 start-->
		<div class="fai-setCountLimit-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<el-form-item label="销售">
					<el-select v-model="form.staff_sacct" filterable>
						<el-option label="请选择" value="-1" ></el-option>
						<el-option v-for="preSale in preSaleList" :label="preSale.nickName" :value="preSale.acct" :key="preSale.acct"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="">
					<el-input v-model="form.count" placeholder="请输入每天最大分配数量"></el-input>
			    </el-form-item>

				<el-form-item>
					<el-button type="primary"  @click="onSubmit">设置</el-button>
					<span>注：系统默认上限60条/人/日（周六日情况下），若增加10则设置70</span>
				</el-form-item>
				<br>
				<el-form-item label="奖励资源">
						<el-select v-model="form.staff_sacct" filterable>
							<el-option label="请选择" value="-1" ></el-option>
							<el-option v-for="preSale in preSaleList" :label="preSale.nickName" :value="preSale.acct" :key="preSale.acct"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="">
						<el-input v-model="form.awardCount" placeholder="请输入每天奖励数量"></el-input>
					</el-form-item>

					<el-form-item>
						<el-button type="primary"  @click="setAwardCount">设置</el-button>
						<!-- <span>注：权重分配用到</span> -->
						<span><a target="_blank" href="http://it.faisco.cn/page/forum/articleDetail.jsp?articleId=3675">奖励资源分配介绍</a></span>
					</el-form-item>
				<br>

				<el-form-item label="新销售">
					<el-input v-model="form.newStaff" placeholder="销售账号如hdsale1"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary"  @click="addNewStaff">新增</el-button>
				</el-form-item>
				<br>
				<el-form-item label="总数量限制">
					<el-input v-model="form.totalLimit" placeholder="请输入每天奖励数量"></el-input>
				</el-form-item>

				<el-form-item>
					<el-button type="primary"  @click="setTotalLimit">设置</el-button>
					<span>注：权重分配用到</span>
				</el-form-item>
			<br>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="fai-setCountLimit-list" v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border max-height="680" show-summary>
				<el-table-column label="姓名" align="center" prop="name"></el-table-column>
				<el-table-column label="销售" align="center" prop="acct" ></el-table-column>
				<el-table-column label="周末最大分配数量" align="center" prop="limitCount" ></el-table-column>
				<el-table-column label="奖励数量" align="center" prop="awardCount" ></el-table-column>
				<el-table-column label="权重分配情况" :show-Overflow-Tooltip=true align="center"  prop="allotStr"  width="530" >
						<template scope="scope">
							<span v-for="preSale in scope.row.allotStr">{{ preSale }}</span>
						</template>
				</el-table-column>
				<el-table-column label="状态" prop="statusStr" align="center" width="130"></el-table-column>
				<el-table-column label="操作" align="center" v-if="this.auth.authAdm || this.auth.hdSaleManage" type="index" width="530"   fixed="right">
					<template slot-scope="scope">
						<div style="float:left ;margin-left:10px">
						   <el-button type="" size="mini" @click="changeOfficial(scope.row.acct)" v-if="scope.row.status == 0">转正</el-button>
						   <el-button type="" size="mini">周六分配</el-button>
						   <el-checkbox v-model="scope.row.saturdayAllot"  @change="changeWeekendAllot(scope.row.acct,'saturdayAllot',scope.row.saturdayAllot)"></el-checkbox>
						   <el-button type="" size="mini" >周日分配</el-button>
						   <el-checkbox v-model="scope.row.sundayAllot" @change="changeWeekendAllot(scope.row.acct,'sundayAllot',scope.row.sundayAllot)"></el-checkbox>
						   <!-- <el-button type="" size="mini" @click="isStopAllot(scope.row.acct,'isStopAllot',scope.row.stopAllot)" v-if="scope.row.stopAllot== false">暂停发放</el-button>
						   <el-button type="" size="mini" @click="isStopAllot(scope.row.acct,'isStopAllot',scope.row.stopAllot)" v-if="scope.row.stopAllot ">继续发放</el-button> -->
						<el-button type="" size="mini" @click="deleteSale(scope.row.acct)">删除</el-button>
						</div>
						<br>
						<div style="float:left; margin-top:10px ;margin-left:10px">
							<el-button type="" size="mini">设置暂停发放资源</el-button>
							<el-checkbox v-model="scope.row.isStopAllot"  @change="isStopAllot(scope.$index,tableData,scope.row.isStopAllot)"></el-checkbox>
							<el-date-picker class="fai-daterange"  type="date" v-model="scope.row.allotStartTime" placeholder="选择日期" :editable=false></el-date-picker>
							-
							<el-date-picker class="fai-daterange" type="date" v-model="scope.row.allotEndTime" placeholder="选择日期" :editable=false></el-date-picker>

						    <!-- <el-button type="" size="mini" @click="delStopAllot(scope.$index,tableData)" v-if="scope.row.allotEndTime!=0">取消暂停</el-button> -->
						</div>
					</template>
				</el-table-column>
				<!--fixed:固定-->
			</el-table>
		</div>

		<!--数据表格 end-->

	</body>


	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-setCountLimit-search',
			data: {
				form: {//这里是为了填充默认值
					staff_sacct: "-1",
					count: "",
					newStaff: "",
					awardCount: 0,
					totalLimit: 0,
				},
				preSaleList: [],
			},
			created:function(){
				Fai.http.post("hdSale_h.jsp?cmd=getSaleListNotAll", "", false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
			},
			methods: {
				onSubmit() {
					if(this.form.staff_sacct == -1){
						this.$message({showClose: true, type: 'error', message: '请指定销售！'});
						return;
					}
					//此处注释，hdoss.src.js封装好的Fai.http.post方法对返回的结果的success值做了判断，会自动弹窗，但是这个接口返回的是 rt 值，这里用原生方式调接口
					Fai.http.post("hdSale_h.jsp?cmd=setCountLimit", {"staff":this.form.staff_sacct,"count":this.form.count}, false).then(result => {
						if(result.rt === 0 && result.success){
							this.$message({showClose: true, type: 'success', message: result.msg});
							getDataList();//操作成功，重新拿一次数据加载数据表格
						}else{
							//Fai.http.post已经封装了错误提示
						}
					});
				},
				addNewStaff(){
					if(this.form.newStaff.trim()==""){
						this.$message({showClose: true, type: 'error', message:"请输入正确账号"});
						return;
					}
					this.$confirm('确定要新增销售 '+this.form.newStaff+' 吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"addSale", "acct":this.form.newStaff}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				},
				setAwardCount(){
					if(this.form.staff_sacct == -1){
						this.$message({showClose: true, type: 'error', message: '请指定销售！'});
						return;
					}
					//此处注释，hdoss.src.js封装好的Fai.http.post方法对返回的结果的success值做了判断，会自动弹窗，但是这个接口返回的是 rt 值，这里用原生方式调接口
					Fai.http.post("hdSale_h.jsp?cmd=setAwardCount", {"staff":this.form.staff_sacct,"awardCount":this.form.awardCount}, false).then(result => {
						if(result.rt === 0 && result.success){
							this.$message({showClose: true, type: 'success', message: result.msg});
							getDataList();//操作成功，重新拿一次数据加载数据表格
						}
					});
				},
				setTotalLimit(){
					Fai.http.post("hdSale_h.jsp?cmd=setTotalLimitCount", {"totalLimit":this.form.totalLimit}, false).then(result => {
						if(result.rt === 0 && result.success){
							this.$message({showClose: true, type: 'success', message: result.msg});
							getDataList();//操作成功，重新拿一次数据加载数据表格
						}
					});
				}


		    }
		});

		var faiDataList = new Vue({
			el: '.fai-setCountLimit-list',
			data: {
				tableData: [],
				nowTime:new Date(),
			},
			created:function(){
				getDataList();//进入页面加载一次数据！
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				deleteSale(acct){
					this.$confirm('确定要删除销售 '+acct+' 吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"delSale", "acct":acct}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				},
				changeOfficial(acct){
					this.$confirm('确定要转正销售 '+acct+' 吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"changeOfficial", "acct":acct}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				},
				changeWeekendAllot(acct,type,updateFlag){
					console.info(acct,type,updateFlag);
					this.$confirm('确定要设置销售 '+acct+' 吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"changeWeekendAllot", "acct":acct,"type":type,"updateFlag":updateFlag}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				},
				//是否暂停发放资源
				 isStopAllot(index,dataList,isStopAllot){

					console.info(isStopAllot);
					var args='';
					if(isStopAllot){
						args= {
								  "cmd":"isStopAllot",
	                              "sid":dataList[index].sid,
	                              "allotStartTime":Fai.tool.getDateTimeStart(dataList[index].allotStartTime),
								  "allotEndTime":Fai.tool.getDateTimeEnd(dataList[index].allotEndTime),
								  "isStopAllot":isStopAllot,
						}
					}else{
						args={
								 "cmd":"isStopAllot",
	                              "sid":dataList[index].sid,
								  "isStopAllot":isStopAllot,
						}
					}
					var tip=isStopAllot?'设置':'取消';
					this.$confirm('确定要'+tip+'暂停发放销售 '+dataList[index].acct+' 的资源吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp",args, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				},
				/* delStopAllot(index,dataList){
					this.$confirm('确定要取消暂停发放销售 '+dataList[index].acct+' 的资源吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp",{"cmd":"delStopAllot", "sid":dataList[index].sid}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格
						});
					}).catch(() => { });
				} */
		    }
		});
		function data(){
		    return {
		        nowTime:new Date()
		    }
		}

		// 获取数据
		function getDataList(){
			Fai.http.post("hdSale_h.jsp?cmd=getSetCountLimitList", "", false).then(result => {
				if(result.success){
					var dataList = result.dataList;
					for(var i= 0;i<dataList.length;i++){
						dataList[i].alotStr;
					}
					faiDataList.tableData = dataList;
					console.info(faiDataList.tableData);
					for(var i=0;i<dataList.length;i++){
						//console.info(dataList[i].allotStartTime+","+dataList[i].allotEndTime);
						if(dataList[i].allotStartTime!=0){
							faiDataList.tableData[i].allotStartTime=Fai.tool.dateFormatter(dataList[i].allotStartTime * 1000);
							faiDataList.tableData[i].allotEndTime=Fai.tool.dateFormatter(dataList[i].allotEndTime * 1000);
						}else{
							faiDataList.tableData[i].allotStartTime=Fai.tool.dateFormatter((new Date).getTime());
							faiDataList.tableData[i].allotEndTime=Fai.tool.dateFormatter((new Date).getTime());
						}
					}
					faiSearchObj.form.totalLimit = result.totalList[0].totalLimitCount;
				}
			});
		}



	</script>

</html>



