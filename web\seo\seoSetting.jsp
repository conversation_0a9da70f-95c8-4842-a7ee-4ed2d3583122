<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="java.sql.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.web.inf.*"%>
<%if(!Auth.checkFaiscoAuth("authSeoManage", false)){out.println("没有权限");return;}%>
<%
	String httpStr = Request.getScheme() + "://";
	String setUrl = Parser.parseString(request.getParameter("url"), "");
	String setTitle = "";
	String setKeywords = "";
	String setDescription = "";
	if(!Str.isEmpty(setUrl)) {
		SysFaiSeo oSeo = (SysFaiSeo)Core.getSysKit(Kid.SYS_FAI_SEO);
		Param seoInfo = oSeo.getTDKInfo(setUrl);
		setTitle = seoInfo.getString(FaiSeoDef.Info.TITLE, "");
		setKeywords = seoInfo.getString(FaiSeoDef.Info.KEYWORD, "");
		setDescription = seoInfo.getString(FaiSeoDef.Info.DESCRIPTION, "");
	}
%>
<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>SEO设置</title>
<script language="javascript" type="text/javascript" src="/js/jquery/jquery-1.4.2.min.js"></script>
<%=FrontEndDef.getUEComponentHtml("ueditor_1_1")%>
<script language="javascript" type="text/javascript" src="/js/comm.js"></script>
<script type="text/javascript" src="/js/SWFUpload2v/swfupload.js"></script>
<script type="text/javascript" src="/js/jquery/form/jquery.form.js"></script>
<%@include file="../link.jsp.inc"%>
<script type="text/javascript" src="../js/comm/vue/vue.min.js"></script>
<script src="<%=FrontEndDef.getResPath("js_faiupload_1_0")%>"></script>
<%=FrontEndDef.getPackage("fa-component", "~1.1.0")%>
<style type="text/css">
	body{
		font-size: 14px;
	}
	input[type="checkbox"] {
	    vertical-align: middle;
	    margin: 3px 3px 3px 4px;
	    width: 13px;
	    height: 13px;
	}
	input[type="radio"] {
	    vertical-align: middle;
	    margin: 3px 3px 3px 4px;
	    width: 14px;
	    height: 20px;
	}
	input[type="text"] {
	    width: 120px;
	}
	.main{
		width: 1400px;
		margin: 20px;
	}
	.title {
	    text-align: right;
	    float: left;
   		width: 225px;
   		font-size: 15px;
   		font-weight: 1000;
	}
	.tip{
		font-size: 12px;
		font-weight: 200;
	}
	table{
		border-collapse:unset;
	}
	tr{
		display: block;
		margin-bottom: 20px;
	}
	td{
		margin: 5px 0;
	}
	table label{
		margin-right: 10px;
		cursor:pointer;
	}
	.platform{
		margin-left: -46px;
	}
	input[type='text'], input[type='password'], input[type='number'], textarea {
		-webkit-appearance: textfield;
	}
	.file-item:hover {
		background-color: rgb(230, 247, 255);
	}
	.isRightContainer{
		display:none;
		position:fixed;
		top:0;
		width:100%;
		height:100%;
		font-size:14px;
		background-color:rgba(0,0,0,0.3);
		align-items:center;
	}
	.isRightConfirm{
		width:400px;
		height:180px;
		border:1px solid #bababa;
		margin:0 auto;
		background-color:#fff;
		border-radius: 4px;
	}
	.isRightContent{
		text-align:center;
		margin:50px 30px;
		font-size:15px;
	}
	.isRightBtn{
		margin-right:30px;
	}
	.isRightBtn>button{
		width:50px;
		float:right;
		margin-left:10px;
		border-radius: 4px;
		background-color: #409EFF;
		border:1px solid #409EFF;
		color: #fff;
	}
	.seoBtn{
		width: 70px;
		height: 30px;
		border-radius: 4px;
		background-color: #409EFF;
		border:1px solid #409EFF;
		color: #fff;
		cursor: pointer;
	}
	#submit{
		margin-top: 20px;
		margin-left: 225px;
	}
	.ps{
		margin: 50px 0;
		color: red;
	}
</style>
</head>
<body>
	<div id="main" class="main">
		<div>
			<table>
				<tbody>
					<tr>
						<td class="title">META Title（栏目标题）<br><span class="tip">针对搜索引擎设置的标题&nbsp;&nbsp;&nbsp;&nbsp;</span></td>
						<td><input id="title" name="title" type="text" value="<%=setTitle%>" style="width:500px;height:30px;"/></td>
					</tr>
					<tr>
						<td class="title">META keywords（栏目关键词）<br><span class="tip">关键字中间用半角逗号隔开&nbsp;&nbsp;&nbsp;&nbsp;</span></td>
						<td><textarea id="keywords" name="keywords" style="width:500px; height:120px;float: left;"></textarea></td>
					</tr>
					<tr>
						<td class="title">META Description（栏目描述）<br><span class="tip">针对搜索引擎设置的网页描述&nbsp;&nbsp;&nbsp;&nbsp;</span></td>
						<td><textarea id="description" name="description" style="width:500px; height:120px;"></textarea></td>
					</tr>
					<tr>
						<td class="title">前台链接&nbsp;</td>
						<td><input id="url" name="url" type="text" value="<%=setUrl%>" style="width:500px;height:30px;"/></td>
					</tr>
				</tbody>
			</table>
			<input type="button" class="seoBtn" id="submit" value="提交" onclick="submit();" />
		</div>
		<div class="ps">注：SEO填写的TKD优先级比默认TDK模板规则优先级高，即单独填写的可覆盖原有的</div>
		<input type="button" class="seoBtn" value="查看详情页" onclick="detail();" />
		<div class="isRightContainer">
			<div class="isRightConfirm">
			    <div class="isRightContent">请先确认新打开的窗口是否为想要设置的页面？</div>
		        <div class="isRightBtn">
					<button onclick="cancelSubmit()">否</button>
		            <button onclick="confirmSubmit()">是</button>
		        </div>
		    </div>
		</div>
	</div>
</body>
<script type="text/javascript">
	$(function(){
		$(".isRightContainer").hide();
		$('#keywords').val("<%=setKeywords%>");
		$('#description').val("<%=setDescription%>");
	});
	function detail() {
		window.location.href = "/seo/seoList.jsp";
	}
	function submit() {
		var title = $('#title').val().trim();
		if(title == null || title == "" || title == undefined){
	    	alert("栏目标题不能为空");
	    	return;
	    }
		var keywords = $('#keywords').val().trim();
		if(keywords == null || keywords == "" || keywords == undefined){
	    	alert("栏目关键词不能为空");
	    	return;
	    }
		var description = $('#description').val().trim();
		if(description == null || description == "" || description == undefined){
	    	alert("栏目描述不能为空");
	    	return;
	    }
	    var url = $('#url').val().trim();
	    if(url == null || url == "" || url == undefined){
	    	alert("链接不能为空");
	    	return;
	    }
	    var openUrl = url;
	    if(openUrl.indexOf("//") != 0 && openUrl.indexOf("http") != 0) {
	    	openUrl = '<%=httpStr%>' + openUrl;
	    }
	    $(".isRightContainer").show(0, showConfirm);
	    window.open(openUrl);
	}

	function confirmSubmit() {
	    var data = {};
	    data.title = $('#title').val().trim();
	    data.keywords = $('#keywords').val().trim();
	    data.description = $('#description').val().trim();
	    data.url = $('#url').val().trim();
	    $(".isRightContainer").hide();
	    $.ajax({
			type: "post",
			url: "./ajax/seo_h.jsp?cmd=addTDK",
			data: data,
			error: function(){
				Fai.ing("服务繁忙，请稍候重试", true);
			},
			success: function(result){
				let res = jQuery.parseJSON(result);
				if(res.success){
					alert("设置成功", true);
					$('#title').val('');
					$('#keywords').val('');
					$('#description').val('');
					$('#url').val('');
				}else{
					alert(res.msg,true);
				}
			}
		});
	}

	function showConfirm() {
		$(this).css("display", "flex");
	}

	function cancelSubmit() {
		$('#title').val('');
		$('#keywords').val('');
		$('#description').val('');
		$('#url').val('');
		$(".isRightContainer").hide();
	}
</script>
</html>