package fai.webhdoss.controller;

import com.alibaba.fastjson.JSON;
import fai.app.AcctDef;
import fai.app.HdActivityPlanDef;
import fai.app.HdGameDef;
import fai.cli.HdOssAffairCli;
import fai.comm.cache.redis.RedisCacheManager;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.web.Core;
import fai.web.inf.Flyer;
import fai.web.inf.HdGame;
import fai.web.inf.Kid;
import fai.web.inf.SysAcct;
import fai.webhdoss.WebHdOss;
import fai.webhdoss.model.vo.HdActivityPlanListVO;
import fai.webhdoss.model.vo.HdActivityPlanVO;
import fai.webhdoss.service.HdActivityPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;

/**
 * <AUTHOR>
 */
@Api(value = "活动方案相关业务接口")
@RestController
@RequestMapping("activity-plan")
public class HdActivityPlanController {

    @Autowired
    private HdActivityPlanService hdActivityPlanService;
    @Autowired
    private HdOssAffairCli hdOssAffairCli;
    @Autowired
    private RedisCacheManager codis;


    /**
     * 通过企业账号获取aid和gameId
     *
     * @param acct
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @GetMapping("aid/{acct}/{gameId}")
    public JsonResult getAidByAcct(@ApiParam("企业账号") @PathVariable("acct") String acct,
                                   @ApiParam("gameId") @PathVariable("gameId") Integer gameId) throws Exception {
        SysAcct sysAcct = (SysAcct) Core.getSysKit(Kid.SYS_ACCT);

        Param acctInfo = sysAcct.getAcctInfo(AcctDef.Atype.CORP, acct);
        if (null == acctInfo) {
            return JsonResult.error("acctInfo not found");
        }
        Integer aid = acctInfo.getInt(AcctDef.Info.AID, 0);

        HdGame hdGame = (HdGame) WebHdOss.getCorpKit(aid, Kid.HD_GAME);
        Param game = hdGame.getGameInfoByField(aid, gameId, new FaiList<String>(Collections.singletonList(HdGameDef.Info.STYLE)));
        if (null == game) {
            return JsonResult.error("game not found");
        }
        int style = game.getInt(HdGameDef.Info.STYLE, -1);
        String nameByStyle = HdGameDef.Style.getNameByStyle(style);

        Ref<Integer> intRef = new Ref<Integer>();
        int rt = hdOssAffairCli.getHdModelIdByStyle(style, intRef);
        HdAssert.judgeNotLog(rt, "getModId err, style=%s", style);

        return JsonResult.success(new Param().setInt(AcctDef.Info.AID, aid)
                                             .setString("prototype", nameByStyle)
                                             .setInt("modId", intRef.value));
    }

    /**
     * 创建方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @PostMapping("add")
    public JsonResult addActivityPlan(@ApiParam("活动方案") @Validated @RequestBody HdActivityPlanVO hdActivityPlanVO) throws Exception {

        String flyerModId = hdActivityPlanVO.getFlyerModId();
        if (!HdAssert.isEmpty(flyerModId)) {
            if (!flyerModId.contains("Z")) {
                return JsonResult.error("找不到对应传单");
            }
            String flyerAid;
            String flyerId;
            try {
                flyerAid = String.valueOf(Integer.parseInt(flyerModId.split("Z")[0], 32));
                flyerId = String.valueOf(Integer.parseInt(flyerModId.split("Z")[1], 32));
            } catch (java.lang.NumberFormatException nfe) {
                Log.logErr("flyerModId err flyerModId : %s", flyerModId);
                return JsonResult.error("找不到对应传单");
            }
            Flyer flyer = (Flyer) Core.getFlyerKit(Integer.parseInt(flyerAid), Kid.FLYER);

            Param flyerInfo = flyer.getFlyerFileId(Integer.parseInt(flyerId));
            if (Str.isEmpty(flyerInfo)) {
                return JsonResult.error("找不到对应传单");
            }
        }

        int id = hdActivityPlanService.addActivityPlan(JSON.toJSONString(hdActivityPlanVO));
        codis.del(HdActivityPlanDef.getTotalKey(hdActivityPlanVO.getTradeId()));
        codis.del(HdActivityPlanDef.getTotalKey());
        Param response = new Param();
        return JsonResult.success(response.setInt(HdActivityPlanDef.Info.ID, id));
    }

    /**
     * 获取方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @GetMapping("{planId}")
    public JsonResult getActivityPlan(@ApiParam("方案编号") @PathVariable("planId") int planId) throws Exception {
        Param activityPlan = hdActivityPlanService.getHdActivityPlan(planId);
        return JsonResult.success(activityPlan);
    }


    /**
     * 获取方案列表
     *
     * @return
     * <AUTHOR>
     */
    @PostMapping("list")
    public JsonResult getActivityPlanList(@ApiParam("活动方案列表排序keys") @RequestBody HdActivityPlanListVO hdActivityPlanListVO) throws Exception {

        int tradeId = hdActivityPlanListVO.getTradeId();
        int relationFlyerState = hdActivityPlanListVO.getRelationFlyerState();
        int limit = hdActivityPlanListVO.getLimit();
        int page = hdActivityPlanListVO.getPage();
        FaiList<HdActivityPlanListVO.SortKey> sorts = hdActivityPlanListVO.getSorts();
        ParamMatcher matcher = new ParamMatcher();
        // 行业id
        if (tradeId > 0) {
            matcher.and(HdActivityPlanDef.Info.TRADE_ID, ParamMatcher.EQ, tradeId);
        }

        // 大于零关联传单
        if (relationFlyerState == 1) {
            matcher.and(HdActivityPlanDef.Info.FLYER_MOD_ID, ParamMatcher.NE, "");
        } else if (relationFlyerState == 0) {
            matcher.and(HdActivityPlanDef.Info.FLYER_MOD_ID, ParamMatcher.EQ,  "");
        }

        SearchArg searchArg = new SearchArg();
        searchArg.start = (page - 1) * limit;
        searchArg.limit = limit;
        searchArg.matcher = matcher;

        if(!HdAssert.isEmpty(sorts)){
            ParamComparator paramComparator = new ParamComparator();
            for (HdActivityPlanListVO.SortKey sort : sorts) {
                paramComparator.addKey(sort.getKey(), sort.getDesc());
            }
            searchArg.cmpor = paramComparator;
        }

        FaiList<Param> hdActivityPlanList = hdActivityPlanService.getHdActivityPlanList(searchArg);

        int total =  hdActivityPlanService.getActivityPlanCount(tradeId, relationFlyerState);

        Param response = new Param();
        response.setList("dataList", hdActivityPlanList);
        response.setInt("total", total);

        return JsonResult.success(response);
    }

    /**
     * 删除指定活动方案
     * @param planId
     * @return
     */
    @PostMapping("del")
    public JsonResult getActivityPlanList(@RequestParam("planId") int planId) throws Exception {
        Param activityPlan = hdActivityPlanService.getHdActivityPlan(planId);
        hdActivityPlanService.delHdActivityPlan(planId);
        codis.del(HdActivityPlanDef.getTotalKey(activityPlan.getInt(HdActivityPlanDef.Info.TRADE_ID, 0)));
        codis.del(HdActivityPlanDef.getTotalKey());
        return JsonResult.success();
    }

    /**
     * 更新活动方案
     * @param planId
     * @return
     */
    @PostMapping("update")
    public JsonResult updateActivityPlanList(@ApiParam("活动方案") @Validated @RequestBody HdActivityPlanVO hdActivityPlanVO) throws Exception {
        String flyerModId = hdActivityPlanVO.getFlyerModId();
        if (!HdAssert.isEmpty(flyerModId)) {
            if (!flyerModId.contains("Z")) {
                return JsonResult.error("找不到对应传单");
            }
            String flyerAid;
            String flyerId;
            try {
                flyerAid = String.valueOf(Integer.parseInt(flyerModId.split("Z")[0], 32));
                flyerId = String.valueOf(Integer.parseInt(flyerModId.split("Z")[1], 32));
            } catch (java.lang.NumberFormatException nfe) {
                Log.logErr("flyerModId err flyerModId : %s", flyerModId);
                return JsonResult.error("找不到对应传单");
            }
            Flyer flyer = (Flyer) Core.getFlyerKit(Integer.parseInt(flyerAid), Kid.FLYER);

            Param flyerInfo = flyer.getFlyerFileId(Integer.parseInt(flyerId));
            if (Str.isEmpty(flyerInfo)) {
                return JsonResult.error("找不到对应传单");
            }
        }
        hdActivityPlanService.updateHdActivityPlan(JSON.toJSONString(hdActivityPlanVO));
        return JsonResult.success();
    }

}
