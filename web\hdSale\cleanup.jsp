<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%
if(!(Web.getDebug()||Session.getSid()==753 ||Session.getSid() ==1224||Session.getSid() ==802 || Session.getSid() == 1355 || Session.getSid() == 877 || Session.getSid() == 1297)){
	return ;
}
%>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>清除离职员工数据</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-cleanup">
		<div class="fai-cleanup-all-list" v-cloak>
			<b style="">所有员工的领取情况</b>
			<el-table :data="tableData" row-key="rowkey" stripe border>
				<el-table-column label="销售" width="180">
					<template slot-scope="scope">
				        <span>{{scope.row.salesAcctStr}} ({{scope.row.salesAcct}})</span>
				      </template>
				</el-table-column>
				<el-table-column label="个人库" prop="person"></el-table-column>
				<el-table-column label="成交库" prop="deal" ></el-table-column>
			</el-table>
		</div>

		<div class="fai-cleanup-presale-list" v-cloak>
			<b style="">直销售前-客户领取情况</b>
			<el-table :data="tableData" row-key="rowkey" stripe border >
				<el-table-column label="" prop="rowName"></el-table-column>
				<el-table-column label="总量" prop="total"></el-table-column>
				<el-table-column label="离职员工领取总量" prop="leaveTotal" ></el-table-column>
			</el-table>
		</div>
		<div class="fai-cleanup-leave-list" v-cloak>
			<b style="">离职员工的领取情况<span style="color:red;">(物理删除)</span></b>
			<el-table :data="tableData" row-key="rowkey" stripe border >
				<el-table-column label="销售">
					<template slot-scope="scope">
				        <span>{{scope.row.salesAcctStr}} ({{scope.row.salesAcct}})</span>
				    </template>
				</el-table-column><!--fixed:固定-->
				<el-table-column label="个人库" prop="person"></el-table-column>
				<el-table-column label="成交库" prop="deal" ></el-table-column>
				<el-table-column label="操作">
					<template slot-scope="scope">
						<el-button type="primary" size="mini" @click="onRelease(scope.$index, tableData, scope.row.salesAcctStr, scope.row.salesAcct)">释放</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</body>

	<script type="text/javascript">
		var faiAllDataList = new Vue({
			el: '.fai-cleanup-all-list',
			data: {
				tableData: [],
			},
			created:function(){
				//getDataList();
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
		    }
		});
		/* 售前 */
        var faiPresaleDataList = new Vue({
			el: '.fai-cleanup-presale-list',
			data: {
				tableData: [],
			},
			created:function(){
				//getDataList();
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
		    }
		});
		/* 离职 */
        var faiLeaveDataList = new Vue({
			el: '.fai-cleanup-leave-list',
			data: {
				tableData: [],
			},
			created:function(){
				getDataList();//仅进入页面加载一次数据！
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				onRelease: function(index, tableData, name, salesAcct){
					this.$confirm('此操作将永久删除"'+name+'('+salesAcct+')"的所有领取数据, 是否继续？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						this.$confirm('请再次确认！ 是否清除"'+name+'('+salesAcct+')"的所有领取数据？', '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						}).then(() => {//再次确定
							Fai.http.post("hdSale_h.jsp?cmd=cleanLeave", {"salesAcct":salesAcct}, false).then(result => {
								if(result.success){
									this.$message({showClose: true, type: 'success', message: result.msg});
									tableData.splice(index, 1);
								}else{
									//Fai.http.post已经封装了错误提示
								}
							});
						}).catch(() => { });
					}).catch(() => { //取消
					});
				}
		    }
		});
        
		// 获取数据
		function getDataList(){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getCleanupList", "", false).then(result => {
				if(result.success){
					faiAllDataList.tableData = result.preSaleList;
					faiPresaleDataList.tableData = result.cusReceiveList;
					faiLeaveDataList.tableData = result.detailList;
				}
			});
		}

	</script>

</html>



