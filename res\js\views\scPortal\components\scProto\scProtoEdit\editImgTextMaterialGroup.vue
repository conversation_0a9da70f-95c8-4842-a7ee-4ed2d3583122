<template>
  <div class="mt-[40px]">
    <div class="mb-[40px]">
      <p class="mb-[10px] text-[16px] font-bold">封面图配置</p>
      <el-form-item label="封面图样式：" prop="cover.styleList" required>
        <el-select v-model="protoInfo.cover.styleList" placeholder="请选择封面图样式" class="!w-[500px]" multiple>
          <el-option v-for="item in confInfo.coverStyleList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="封面图选图：" prop="cover.resList" required :rules="[{ validator: validateResList }]">
        <el-select v-model="protoInfo.cover.resList" placeholder="请选择封面图" class="!w-[500px]" multiple>
          <el-option v-for="item in protoInfo.resFormList" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </div>
    
    <div>
      <p class="mb-[10px] text-[16px] font-bold">次图配置</p>
      <el-form-item label="次图样式：" prop="secondary.styleList" class="mb-[20px]">
        <el-select v-model="protoInfo.secondary.styleList" placeholder="请选择次图样式" class="!w-[500px]" multiple>
          <el-option v-for="item in confInfo.secondaryStyleList" :key="item.key" :label="item.value" :value="item.key"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="次图选图（仅对纯图片样式生效）：" prop="secondary.resList" class="mb-[20px]" required v-if="protoInfo.secondary.styleList.includes(0)" :rules="[{ validator: validateResList }]">
        <el-select v-model="protoInfo.secondary.resList" placeholder="请选择次图选图" class="!w-[500px]" multiple>
          <el-option v-for="item in protoInfo.resFormList" :key="item.id" :label="item.label" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="固定匹配设置（对非纯图片的样式生效）：" class="mb-[20px]" v-if="showFormMatchSetting"> 
        <el-table :data="protoInfo.commList" class="fixMatchSettingTable">
          <el-table-column prop="label" label="标题字段" width="180"></el-table-column>
          <el-table-column prop="desc" label="描述字段" width="180"></el-table-column>
          <el-table-column prop="resList" label="选取素材组">
            <template slot-scope="scope">
              <el-form-item :prop="'commList.' + scope.$index + '.resList'" :rules="[{ validator: validateResList}]">
                <el-select v-model="scope.row.resList" placeholder="请选择素材组" class="!w-[250px]" multiple>
                  <el-option v-for="item in protoInfo.resFormList" :key="item.id" :label="item.label" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditImgTextMaterialGroup',
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    },
    confInfo: {
      type: Object,
      default: () => ({
        coverStyleList: [],
        secondaryStyleList: [],
      })
    },
  },
  data() {
    return {
      
    }
  },
  computed: {
    showFormMatchSetting() {
      return this.protoInfo.secondary.styleList.some(style => style !== 0)
    }
  },
  watch: {
    'protoInfo.resFormList.length': {
      handler(newVal, oldVal) {
        if (newVal < oldVal) {
          // 如果素材组数量减少，假如减少的是选中的素材组，则需要去掉对应的素材组

          // 新素材组ID列表
          const resIds = this.protoInfo.resFormList.map(item => item.id); 
         
          // 封面图素材图
          if (this.protoInfo.cover.resList.some(id => !resIds.includes(id))) {
            this.protoInfo.cover.resList = this.protoInfo.cover.resList.filter(id => resIds.includes(id));
          }

          // 次图素材图
          if (this.protoInfo.secondary.resList.some(id => !resIds.includes(id))) {
            this.protoInfo.secondary.resList = this.protoInfo.secondary.resList.filter(id => resIds.includes(id));
          }
          
          // 固定匹配设置
          this.protoInfo.commList.forEach(item => {
            if (item.resList.some(id => !resIds.includes(id))) {
              item.resList = item.resList.filter(id => resIds.includes(id));
            }
          });
        }
      },
      deep: true,
    }
  },
  methods: {
    validateResList(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error('素材组不能为空'));
        return;
      }
      
      // 检查是否至少有一个选中的素材组是必传的
      const hasRequired = value.some(selectedId => 
        this.protoInfo.resFormList.some(item => item.id === selectedId && item.required)
      );
      
      if (!hasRequired) {
        callback(new Error('素材组必须选择一个必传的素材组'));
        return;
      }
      callback();
    },
  }
}
</script>

<style lang="scss" scoped>
 .fixMatchSettingTable  {
  ::v-deep {
    .el-table__body {
      .cell {
        overflow: visible;
      }
      td {
        padding: 15px 0;
      }
    }
  }
}
</style>
