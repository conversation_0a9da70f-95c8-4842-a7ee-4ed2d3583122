package fai.webhdoss.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Description 互动案例预览窗封装
 * @date 2021/3/14 12:00
 */
@Data
@Accessors(chain = true)
@ApiModel("互动案例预览窗封装消息")
public class HdCasePreViewVO {

    @Valid
    @ApiModelProperty("案例id")
    @NotNull(message = "案例id不为空")
    @Size(min = 1, message = "案例id不正确")
    private Integer caseId;

    @Valid
    @JsonProperty("oAid")
    @ApiModelProperty("旧aid")
    private Integer oAid;

    @Valid
    @JsonProperty("oGameId")
    @ApiModelProperty("旧活动id")
    private String oGameId;

    @Valid
    @ApiModelProperty("logo链接")
    @NotNull(message = "案例id不为空")
    private String logoUrl;

    @Valid
    @ApiModelProperty("原型名称")
    @NotNull(message = "原型不能为空")
    private String prototypeName;

    @ApiModelProperty("是否上架")
    @JsonProperty("isPutOn")
    private Boolean isPutOn;

    @ApiModelProperty("是否投放")
    @JsonProperty("isPut")
    private Boolean isPut;
}
