<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%
    if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
        out.println("没有权限");
        return;
    }%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>续费资源下发</title>
    <%@ include file="/comm/link.jsp.inc"%>
    <%@ include file="/comm/script.jsp.inc"%>
    <link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
</head>
<body id="hdsale-countStat">

<div class="fai-renewResource-search" v-cloak>
    <el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
        <el-form-item label="过期时间">
            <el-date-picker class="fai-date" v-model="form.overdueDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd"></el-date-picker>
            - <el-date-picker class="fai-date" v-model="form.overdueDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
                <el-form-item>
            <el-button icon="el-icon-search" type="primary"  @click="getRenewResource">查询</el-button>
            <el-button icon="el-icon-download" type="primary"  @click="exportExcel">导出资源明细</el-button>
        </el-form-item>
        <br/>
    </el-form>

    <span>续费资源量：</span>{{count}}<br/>

    <el-table :data="form.preSaleList" row-key="rowkey" stripe border max-height="680">
        <el-table-column label="销售" width="200px" fixed>
            <template slot-scope="scope">
                <span>{{scope.row.name}}</span>
            </template>
        </el-table-column width="200px">
        <el-table-column label="分配量" width="300px">
            <template slot-scope="scope">
                <el-input type="text" placeholder="0" v-model="scope.row.allotNum" class="short-input" :label="scope.row.name" :value="scope.row.allotNum" :key="scope.row.acct"></el-input></div>
            </template>
        </el-table-column>

    </el-table>

    <el-button type="primary"  @click="allotResource">确认分配</el-button>
</div>

</body>


<script type="text/javascript">
    var faiDataList = new Vue({
        el: '.fai-renewResource-search',
        data: {
            form: {
                overdueDateBeg: Fai.tool.dateFormatter(new Date()),
                overdueDateEnd: Fai.tool.dateFormatter(new Date()),
                preSaleList: [],
            },
            count: 0
        },
        created:function(){
            Fai.http.post("hdSale/allotRenewResource_h.jsp?cmd=getPreSale", "", false).then(result => {
                if(result.success){
                    this.form.preSaleList = result.dataList;
                }
            });
        },
        methods: {
            getRenewResource(){
                var arg = {
                    "cmd": "getRenewResource",
                    "overdueDateBeg": this.form.overdueDateBeg,
                    "overdueDateEnd": this.form.overdueDateEnd,
                    "exportFlag": false
                }

                Fai.http.post("hdSale/allotRenewResource_h.jsp?cmd=getRenewResource", arg, false).then(result => {
                    if(result.success){
                        faiDataList.count = result.count;
                    }
                });
            },

            exportExcel(){
                var arg = {
                    "cmd": "getRenewResource",
                    "overdueDateBeg": this.form.overdueDateBeg,
                    "overdueDateEnd": this.form.overdueDateEnd,
                    "exportFlag": true
                }

                //导表
                window.location.href =  '/ajax/hdSale/allotRenewResource_h.jsp'+ Fai.tool.parseJsonToUrlParam(arg, true);
            },

            allotResource(){
                var preSaleList = this.form.preSaleList;
                var map = {};
                for(let item of preSaleList) {
                    map[item.acct] = item.allotNum+"";
                }

                var arg = {
                    "cmd": "allotResource",
                    "allotNumMap":JSON.stringify(map),
                    "overdueDateBeg": this.form.overdueDateBeg,
                    "overdueDateEnd": this.form.overdueDateEnd,
                }

                this.$message({
                    type: 'warning',
                    message: '后台请求中...'
                });

                Fai.http.post("hdSale/allotRenewResource_h.jsp?cmd=allotResource", arg, false).then(result => {
                    if(result.success){
                        this.$message({
                            type: 'success',
                            message: result.msg
                        });
                    }
                });
            }
        }

    });



</script>

</html>



