<template>
  <div>
    <el-upload
      v-bind="$attrs" 
      v-on="$attrs"
      action="/api/resource/uploadBgm"
      accept="audio/*"
      name="filedata"
      list-type="text"
      :class="{'hideUploadBtn': hideUploadBtn}"
      :show-file-list="false"
      :file-list="curFileList"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeFileUpload"
      :on-remove="fileRemove"
      :on-success="fileUploadSuccess"
      :on-progress="fileUploadProgress"
      :on-error="fileUploadError"
      :limit="limit"
      :disabled="isUploading"
    >
      <el-button size="mini" type="primary">{{ title }}</el-button>
    </el-upload>
    
    <!-- 上传进度条 -->
    <div v-if="isUploading" class="mt-[10px] mb-[10px] w-[230px]">
      <el-progress 
        :percentage="percentage" 
        :format="percentageFormat"
        :stroke-width="3"
        status="success">
      </el-progress>
    </div>

    <div v-if="curFileList.length > 0" class="mt-[10px]">
      <div v-for="file in curFileList" :key="file.id" class="flex w-full items-center justify-between mb-[10px]">
        <div class="flex items-center">
          <audio :src="file.url" controls class="w-[230px] mr-[10px]"></audio>
          <el-button type="text" @click="fileRemove(file)">删除</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UploadMusic',
  props: {
    title: {
      type: String,
      default: '上传音频'
    },
    // 默认展示文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 限制上传数量
    limit: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      isUploading: false,
      percentage: 0,
      uploadFileName: '',
      curFileList: [],
      dialogAudioUrl: '',
      dialogVisible: false,
    }
  },
  computed: {
    hideUploadBtn() {
      // 上传中或已达到限制时隐藏
      return (this.isUploading && (this.curFileList.length + 1 === this.limit)) || this.curFileList.length >= this.limit;
    },
  },
  created() {
    this.curFileList = this.fileList;
  },
  methods: {
    percentageFormat(percentage) {
      return percentage === 100 ? '上传完成' : `${percentage}%`;
    },
    beforeFileUpload(file) {
      // 添加文件大小限制：20MB
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error('上传音频大小不能超过 20MB!');
        return false;
      }
      
      this.isUploading = true;
      this.percentage = 0;
      this.uploadFileName = file.name;
      return true;
    },
    fileUploadSuccess(response) {
      this.percentage = 100;
      
      if (response.success) {
        this.$message({
          type: "success", 
          message: "文件上传成功！",
        });
        const data = response.data;
        const file = {
          name: data.name,
          url: data.path,  
          id: data.id,
          type: data.type,
          size: data.size,
          duration: data.duration,
        }
        this.$emit('upload-success', file);
        this.curFileList.push(file);
      } else {
        this.$message({
          type: "error",
          message: response.msg || "文件类型不允许！",
        });
      }

      // 延迟重置状态，保证进度条动画完成
      // setTimeout(() => {
        this.isUploading = false;
        this.percentage = 0;
      // }, 1000);
    },
    fileUploadProgress(event, file) {
      this.percentage = Math.round(event.percent);
    },
    fileUploadError(err) {
      this.$message({
        type: "error",
        message: "系统繁忙，请稍后重试！",
      });
      // 错误时立即重置状态，显示上传按钮
      this.isUploading = false;
      this.percentage = 0;
      this.uploadFileName = '';
    },
    fileRemove(file, fileList) {
      const index = this.curFileList.findIndex(item => item.uid === file.uid);
      if (index > -1) {
        this.curFileList.splice(index, 1);
      }
      this.$emit('upload-remove', file);
      console.log(this.curFileList, file, 'fileRemove');
    },
    // 图片预览
    handlePictureCardPreview(file) {
      this.dialogAudioUrl = file.url;
      this.dialogVisible = true;
    }
  }
}
</script>

<style lang="scss" scoped>
// 移除外部样式引入,改用scoped
.el-upload {
  width: 100%;
}
.el-progress {
  margin-bottom: 10px;
}
.hideUploadBtn {
  ::v-deep .el-upload {
    display: none;
  }
}
::v-deep .preview-dialog {
  margin: 100px auto;
  height: 70vh;
  .el-dialog__body {
    height: 100%;
    img {
      height: 100%;
    }
  }
}
::v-deep .el-progress-bar__inner {
  transition: width 0.3s ease;
}

::v-deep .el-progress__text {
  font-size: 12px;
}
</style>
