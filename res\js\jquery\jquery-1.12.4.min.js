/*!
 * jQuery JavaScript Library v1.12.4
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2016-05-20T17:17Z
 */
(function(a,b){if(typeof module==="object"&&typeof module.exports==="object"){module.exports=a.document?b(a,true):function(c){if(!c.document){throw new Error("jQuery requires a window with a document")}return b(c)}}else{b(a)}}(typeof window!=="undefined"?window:this,function(ax,aG){var aY=[];var m=ax.document;var P=aY.slice;var a1=aY.concat;var r=aY.push;var bU=aY.indexOf;var bn={};var bZ=bn.toString;var bJ=bn.hasOwnProperty;var bQ={};var av="1.12.4",L=function(i,cd){return new L.fn.init(i,cd)},y=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,w=/^-ms-/,aH=/-([\da-z])/gi,O=function(cd,i){return i.toUpperCase()};L.fn=L.prototype={jquery:av,constructor:L,selector:"",length:0,toArray:function(){return P.call(this)},get:function(i){return i!=null?(i<0?this[i+this.length]:this[i]):P.call(this)},pushStack:function(i){var cd=L.merge(this.constructor(),i);cd.prevObject=this;cd.context=this.context;return cd},each:function(i){return L.each(this,i)},map:function(i){return this.pushStack(L.map(this,function(ce,cd){return i.call(ce,cd,ce)}))},slice:function(){return this.pushStack(P.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(cf){var cd=this.length,ce=+cf+(cf<0?cd:0);return this.pushStack(ce>=0&&ce<cd?[this[ce]]:[])},end:function(){return this.prevObject||this.constructor()},push:r,sort:aY.sort,splice:aY.splice};L.extend=L.fn.extend=function(){var cd,cj,cf,ce,cl,ck,cg=arguments[0]||{},ch=1,ci=arguments.length,cm=false;if(typeof cg==="boolean"){cm=cg;cg=arguments[ch]||{};ch++}if(typeof cg!=="object"&&!L.isFunction(cg)){cg={}}if(ch===ci){cg=this;ch--}for(;ch<ci;ch++){if((cl=arguments[ch])!=null){for(ce in cl){cd=cg[ce];cf=cl[ce];if(cg===cf){continue}if(cm&&cf&&(L.isPlainObject(cf)||(cj=L.isArray(cf)))){if(cj){cj=false;ck=cd&&L.isArray(cd)?cd:[]}else{ck=cd&&L.isPlainObject(cd)?cd:{}}cg[ce]=L.extend(cm,ck,cf)}else{if(cf!==undefined){cg[ce]=cf}}}}}return cg};L.extend({expando:"jQuery"+(av+Math.random()).replace(/\D/g,""),isReady:true,error:function(i){throw new Error(i)},noop:function(){},isFunction:function(i){return L.type(i)==="function"},isArray:Array.isArray||function(i){return L.type(i)==="array"},isWindow:function(i){return i!=null&&i==i.window},isNumeric:function(cd){var i=cd&&cd.toString();return !L.isArray(cd)&&(i-parseFloat(i)+1)>=0},isEmptyObject:function(cd){var i;for(i in cd){return false}return true},isPlainObject:function(ce){var i;if(!ce||L.type(ce)!=="object"||ce.nodeType||L.isWindow(ce)){return false}try{if(ce.constructor&&!bJ.call(ce,"constructor")&&!bJ.call(ce.constructor.prototype,"isPrototypeOf")){return false}}catch(cd){return false}if(!bQ.ownFirst){for(i in ce){return bJ.call(ce,i)}}for(i in ce){}return i===undefined||bJ.call(ce,i)},type:function(i){if(i==null){return i+""}return typeof i==="object"||typeof i==="function"?bn[bZ.call(i)]||"object":typeof i},globalEval:function(i){if(i&&L.trim(i)){(ax.execScript||function(cd){ax["eval"].call(ax,cd)})(i)}},camelCase:function(i){return i.replace(w,"ms-").replace(aH,O)},nodeName:function(cd,i){return cd.nodeName&&cd.nodeName.toLowerCase()===i.toLowerCase()},each:function(cg,cf){var cd,ce=0;if(ba(cg)){cd=cg.length;for(;ce<cd;ce++){if(cf.call(cg[ce],ce,cg[ce])===false){break}}}else{for(ce in cg){if(cf.call(cg[ce],ce,cg[ce])===false){break}}}return cg},trim:function(i){return i==null?"":(i+"").replace(y,"")},makeArray:function(i,ce){var cd=ce||[];if(i!=null){if(ba(Object(i))){L.merge(cd,typeof i==="string"?[i]:i)}else{r.call(cd,i)}}return cd},inArray:function(cg,ce,cf){var cd;if(ce){if(bU){return bU.call(ce,cg,cf)}cd=ce.length;cf=cf?cf<0?Math.max(0,cd+cf):cf:0;for(;cf<cd;cf++){if(cf in ce&&ce[cf]===cg){return cf}}}return -1},merge:function(ch,cg){var cd=+cg.length,ce=0,cf=ch.length;while(ce<cd){ch[cf++]=cg[ce++]}if(cd!==cd){while(cg[ce]!==undefined){ch[cf++]=cg[ce++]}}ch.length=cf;return ch},grep:function(ce,ch,ci){var ck,cg=[],cf=0,cd=ce.length,cj=!ci;for(;cf<cd;cf++){ck=!ch(ce[cf],cf);if(ck!==cj){cg.push(ce[cf])}}return cg},map:function(cf,cj,cd){var ce,ci,ch=0,cg=[];if(ba(cf)){ce=cf.length;for(;ch<ce;ch++){ci=cj(cf[ch],ch,cd);if(ci!=null){cg.push(ci)}}}else{for(ch in cf){ci=cj(cf[ch],ch,cd);if(ci!=null){cg.push(ci)}}}return a1.apply([],cg)},guid:1,proxy:function(cg,cf){var i,ce,cd;if(typeof cf==="string"){cd=cg[cf];cf=cg;cg=cd}if(!L.isFunction(cg)){return undefined}i=P.call(arguments,2);ce=function(){return cg.apply(cf||this,i.concat(P.call(arguments)))};ce.guid=cg.guid=cg.guid||L.guid++;return ce},now:function(){return +(new Date())},support:bQ});if(typeof Symbol==="function"){L.fn[Symbol.iterator]=aY[Symbol.iterator]}L.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(ce,cd){bn["[object "+cd+"]"]=cd.toLowerCase()});function ba(ce){var i=!!ce&&"length" in ce&&ce.length,cd=L.type(ce);if(cd==="function"||L.isWindow(ce)){return false}return cd==="array"||i===0||typeof i==="number"&&i>0&&(i-1) in ce}var b=
/*!
 * Sizzle CSS Selector Engine v2.2.1
 * http://sizzlejs.com/
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2015-10-17
 */
(function(cn){var cF,cl,cs,cV,cR,dl,c4,cm,ds,cT,c3,cD,cH,cu,df,cz,co,ch,cW,ct="sizzle"+1*new Date(),cO=cn.document,dn=0,c9=0,cd=c2(),de=c2(),cL=c2(),c1=function(du,i){if(du===i){c3=true}return 0},cM=1<<31,cN=({}).hasOwnProperty,dh=[],dk=dh.pop,cP=dh.push,ce=dh.push,cq=dh.slice,ci=function(dx,dw){var dv=0,du=dx.length;for(;dv<du;dv++){if(dx[dv]===dw){return dv}}return -1},cf="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",cv="[\\x20\\t\\r\\n\\f]",cQ="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",db="\\["+cv+"*("+cQ+")(?:"+cv+"*([*^$|!~]?=)"+cv+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+cQ+"))|)"+cv+"*\\]",di=":("+cQ+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+db+")*)|.*)\\)|)",cC=new RegExp(cv+"+","g"),cw=new RegExp("^"+cv+"+|((?:^|[^\\\\])(?:\\\\.)*)"+cv+"+$","g"),c7=new RegExp("^"+cv+"*,"+cv+"*"),cI=new RegExp("^"+cv+"*([>+~]|"+cv+")"+cv+"*"),cy=new RegExp("="+cv+"*([^\\]'\"]*?)"+cv+"*\\]","g"),cX=new RegExp(di),cK=new RegExp("^"+cQ+"$"),c8={ID:new RegExp("^#("+cQ+")"),CLASS:new RegExp("^\\.("+cQ+")"),TAG:new RegExp("^("+cQ+"|[*])"),ATTR:new RegExp("^"+db),PSEUDO:new RegExp("^"+di),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+cv+"*(even|odd|(([+-]|)(\\d*)n|)"+cv+"*(?:([+-]|)"+cv+"*(\\d+)|))"+cv+"*\\)|)","i"),bool:new RegExp("^(?:"+cf+")$","i"),needsContext:new RegExp("^"+cv+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+cv+"*((?:-\\d)?\\d*)"+cv+"*\\)|)(?=[^-]|$)","i")},cj=/^(?:input|select|textarea|button)$/i,cr=/^h\d$/i,cU=/^[^{]+\{\s*\[native \w/,cY=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,cB=/[+~]/,cS=/'|\\/g,da=new RegExp("\\\\([\\da-f]{1,6}"+cv+"?|("+cv+")|.)","ig"),cx=function(du,dw,i){var dv="0x"+dw-65536;return dv!==dv||i?dw:dv<0?String.fromCharCode(dv+65536):String.fromCharCode(dv>>10|55296,dv&1023|56320)},dq=function(){cD()};try{ce.apply((dh=cq.call(cO.childNodes)),cO.childNodes);dh[cO.childNodes.length].nodeType}catch(cJ){ce={apply:dh.length?function(du,i){cP.apply(du,cq.call(i))}:function(dx,dw){var du=dx.length,dv=0;while((dx[du++]=dw[dv++])){}dx.length=du-1}}}function c5(dA,dv,dE,dG){var dx,dD,dy,du,dI,dF,dH,dC,dw=dv&&dv.ownerDocument,dB=dv?dv.nodeType:9;dE=dE||[];if(typeof dA!=="string"||!dA||dB!==1&&dB!==9&&dB!==11){return dE}if(!dG){if((dv?dv.ownerDocument||dv:cO)!==cH){cD(dv)}dv=dv||cH;if(df){if(dB!==11&&(dF=cY.exec(dA))){if((dx=dF[1])){if(dB===9){if((dy=dv.getElementById(dx))){if(dy.id===dx){dE.push(dy);return dE}}else{return dE}}else{if(dw&&(dy=dw.getElementById(dx))&&cW(dv,dy)&&dy.id===dx){dE.push(dy);return dE}}}else{if(dF[2]){ce.apply(dE,dv.getElementsByTagName(dA));return dE}else{if((dx=dF[3])&&cl.getElementsByClassName&&dv.getElementsByClassName){ce.apply(dE,dv.getElementsByClassName(dx));return dE}}}}if(cl.qsa&&!cL[dA+" "]&&(!cz||!cz.test(dA))){if(dB!==1){dw=dv;dC=dA}else{if(dv.nodeName.toLowerCase()!=="object"){if((du=dv.getAttribute("id"))){du=du.replace(cS,"\\$&")}else{dv.setAttribute("id",(du=ct))}dH=dl(dA);dD=dH.length;dI=cK.test(du)?"#"+du:"[id='"+du+"']";while(dD--){dH[dD]=dI+" "+cp(dH[dD])}dC=dH.join(",");dw=cB.test(dA)&&cZ(dv.parentNode)||dv}}if(dC){try{ce.apply(dE,dw.querySelectorAll(dC));return dE}catch(dz){}finally{if(du===ct){dv.removeAttribute("id")}}}}}}return cm(dA.replace(cw,"$1"),dv,dE,dG)}function c2(){var du=[];function i(dv,dw){if(du.push(dv+" ")>cs.cacheLength){delete i[du.shift()]}return(i[dv+" "]=dw)}return i}function dj(i){i[ct]=true;return i}function dp(i){var dv=cH.createElement("div");try{return !!i(dv)}catch(du){return false}finally{if(dv.parentNode){dv.parentNode.removeChild(dv)}dv=null}}function ck(dv,dx){var du=dv.split("|"),dw=du.length;while(dw--){cs.attrHandle[du[dw]]=dx}}function dt(du,i){var dw=i&&du,dv=dw&&du.nodeType===1&&i.nodeType===1&&(~i.sourceIndex||cM)-(~du.sourceIndex||cM);if(dv){return dv}if(dw){while((dw=dw.nextSibling)){if(dw===i){return -1}}}return du?1:-1}function cE(i){return function(dv){var du=dv.nodeName.toLowerCase();return du==="input"&&dv.type===i}}function dr(i){return function(dv){var du=dv.nodeName.toLowerCase();return(du==="input"||du==="button")&&dv.type===i}}function dc(i){return dj(function(du){du=+du;return dj(function(dv,dz){var dw,dy=i([],dv.length,du),dx=dy.length;while(dx--){if(dv[(dw=dy[dx])]){dv[dw]=!(dz[dw]=dv[dw])}}})})}function cZ(i){return i&&typeof i.getElementsByTagName!=="undefined"&&i}cl=c5.support={};cR=c5.isXML=function(i){var du=i&&(i.ownerDocument||i).documentElement;return du?du.nodeName!=="HTML":false};cD=c5.setDocument=function(dv){var du,i,dw=dv?dv.ownerDocument||dv:cO;if(dw===cH||dw.nodeType!==9||!dw.documentElement){return cH}cH=dw;cu=cH.documentElement;df=!cR(cH);if((i=cH.defaultView)&&i.top!==i){if(i.addEventListener){i.addEventListener("unload",dq,false)}else{if(i.attachEvent){i.attachEvent("onunload",dq)}}}cl.attributes=dp(function(dx){dx.className="i";return !dx.getAttribute("className")});cl.getElementsByTagName=dp(function(dx){dx.appendChild(cH.createComment(""));return !dx.getElementsByTagName("*").length});cl.getElementsByClassName=cU.test(cH.getElementsByClassName);cl.getById=dp(function(dx){cu.appendChild(dx).id=ct;return !cH.getElementsByName||!cH.getElementsByName(ct).length});if(cl.getById){cs.find.ID=function(dz,dy){if(typeof dy.getElementById!=="undefined"&&df){var dx=dy.getElementById(dz);return dx?[dx]:[]}};cs.filter.ID=function(dy){var dx=dy.replace(da,cx);return function(dz){return dz.getAttribute("id")===dx}}}else{delete cs.find.ID;cs.filter.ID=function(dy){var dx=dy.replace(da,cx);return function(dA){var dz=typeof dA.getAttributeNode!=="undefined"&&dA.getAttributeNode("id");return dz&&dz.value===dx}}}cs.find.TAG=cl.getElementsByTagName?function(dx,dy){if(typeof dy.getElementsByTagName!=="undefined"){return dy.getElementsByTagName(dx)}else{if(cl.qsa){return dy.querySelectorAll(dx)}}}:function(dx,dB){var dC,dA=[],dz=0,dy=dB.getElementsByTagName(dx);if(dx==="*"){while((dC=dy[dz++])){if(dC.nodeType===1){dA.push(dC)}}return dA}return dy};cs.find.CLASS=cl.getElementsByClassName&&function(dx,dy){if(typeof dy.getElementsByClassName!=="undefined"&&df){return dy.getElementsByClassName(dx)}};co=[];cz=[];if((cl.qsa=cU.test(cH.querySelectorAll))){dp(function(dx){cu.appendChild(dx).innerHTML="<a id='"+ct+"'></a><select id='"+ct+"-\r\\' msallowcapture=''><option selected=''></option></select>";if(dx.querySelectorAll("[msallowcapture^='']").length){cz.push("[*^$]="+cv+"*(?:''|\"\")")}if(!dx.querySelectorAll("[selected]").length){cz.push("\\["+cv+"*(?:value|"+cf+")")}if(!dx.querySelectorAll("[id~="+ct+"-]").length){cz.push("~=")}if(!dx.querySelectorAll(":checked").length){cz.push(":checked")}if(!dx.querySelectorAll("a#"+ct+"+*").length){cz.push(".#.+[+~]")}});dp(function(dy){var dx=cH.createElement("input");dx.setAttribute("type","hidden");dy.appendChild(dx).setAttribute("name","D");if(dy.querySelectorAll("[name=d]").length){cz.push("name"+cv+"*[*^$|!~]?=")}if(!dy.querySelectorAll(":enabled").length){cz.push(":enabled",":disabled")}dy.querySelectorAll("*,:x");cz.push(",.*:")})}if((cl.matchesSelector=cU.test((ch=cu.matches||cu.webkitMatchesSelector||cu.mozMatchesSelector||cu.oMatchesSelector||cu.msMatchesSelector)))){dp(function(dx){cl.disconnectedMatch=ch.call(dx,"div");ch.call(dx,"[s!='']:x");co.push("!=",di)})}cz=cz.length&&new RegExp(cz.join("|"));co=co.length&&new RegExp(co.join("|"));du=cU.test(cu.compareDocumentPosition);cW=du||cU.test(cu.contains)?function(dy,dx){var dA=dy.nodeType===9?dy.documentElement:dy,dz=dx&&dx.parentNode;return dy===dz||!!(dz&&dz.nodeType===1&&(dA.contains?dA.contains(dz):dy.compareDocumentPosition&&dy.compareDocumentPosition(dz)&16))}:function(dy,dx){if(dx){while((dx=dx.parentNode)){if(dx===dy){return true}}}return false};c1=du?function(dy,dx){if(dy===dx){c3=true;return 0}var dz=!dy.compareDocumentPosition-!dx.compareDocumentPosition;if(dz){return dz}dz=(dy.ownerDocument||dy)===(dx.ownerDocument||dx)?dy.compareDocumentPosition(dx):1;if(dz&1||(!cl.sortDetached&&dx.compareDocumentPosition(dy)===dz)){if(dy===cH||dy.ownerDocument===cO&&cW(cO,dy)){return -1}if(dx===cH||dx.ownerDocument===cO&&cW(cO,dx)){return 1}return cT?(ci(cT,dy)-ci(cT,dx)):0}return dz&4?-1:1}:function(dy,dx){if(dy===dx){c3=true;return 0}var dE,dB=0,dD=dy.parentNode,dA=dx.parentNode,dz=[dy],dC=[dx];if(!dD||!dA){return dy===cH?-1:dx===cH?1:dD?-1:dA?1:cT?(ci(cT,dy)-ci(cT,dx)):0}else{if(dD===dA){return dt(dy,dx)}}dE=dy;while((dE=dE.parentNode)){dz.unshift(dE)}dE=dx;while((dE=dE.parentNode)){dC.unshift(dE)}while(dz[dB]===dC[dB]){dB++}return dB?dt(dz[dB],dC[dB]):dz[dB]===cO?-1:dC[dB]===cO?1:0};return cH};c5.matches=function(du,i){return c5(du,null,null,i)};c5.matchesSelector=function(du,dw){if((du.ownerDocument||du)!==cH){cD(du)}dw=dw.replace(cy,"='$1']");if(cl.matchesSelector&&df&&!cL[dw+" "]&&(!co||!co.test(dw))&&(!cz||!cz.test(dw))){try{var i=ch.call(du,dw);if(i||cl.disconnectedMatch||du.document&&du.document.nodeType!==11){return i}}catch(dv){}}return c5(dw,cH,null,[du]).length>0};c5.contains=function(i,du){if((i.ownerDocument||i)!==cH){cD(i)}return cW(i,du)};c5.attr=function(dv,i){if((dv.ownerDocument||dv)!==cH){cD(dv)}var du=cs.attrHandle[i.toLowerCase()],dw=du&&cN.call(cs.attrHandle,i.toLowerCase())?du(dv,i,!df):undefined;return dw!==undefined?dw:cl.attributes||!df?dv.getAttribute(i):(dw=dv.getAttributeNode(i))&&dw.specified?dw.value:null};c5.error=function(i){throw new Error("Syntax error, unrecognized expression: "+i)};c5.uniqueSort=function(dw){var dx,dy=[],du=0,dv=0;c3=!cl.detectDuplicates;cT=!cl.sortStable&&dw.slice(0);dw.sort(c1);if(c3){while((dx=dw[dv++])){if(dx===dw[dv]){du=dy.push(dv)}}while(du--){dw.splice(dy[du],1)}}cT=null;return dw};cV=c5.getText=function(dy){var dx,dv="",dw=0,du=dy.nodeType;if(!du){while((dx=dy[dw++])){dv+=cV(dx)}}else{if(du===1||du===9||du===11){if(typeof dy.textContent==="string"){return dy.textContent}else{for(dy=dy.firstChild;dy;dy=dy.nextSibling){dv+=cV(dy)}}}else{if(du===3||du===4){return dy.nodeValue}}}return dv};cs=c5.selectors={cacheLength:50,createPseudo:dj,match:c8,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(i){i[1]=i[1].replace(da,cx);i[3]=(i[3]||i[4]||i[5]||"").replace(da,cx);if(i[2]==="~="){i[3]=" "+i[3]+" "}return i.slice(0,4)},CHILD:function(i){i[1]=i[1].toLowerCase();if(i[1].slice(0,3)==="nth"){if(!i[3]){c5.error(i[0])}i[4]=+(i[4]?i[5]+(i[6]||1):2*(i[3]==="even"||i[3]==="odd"));i[5]=+((i[7]+i[8])||i[3]==="odd")}else{if(i[3]){c5.error(i[0])}}return i},PSEUDO:function(du){var dv,i=!du[6]&&du[2];if(c8.CHILD.test(du[0])){return null}if(du[3]){du[2]=du[4]||du[5]||""}else{if(i&&cX.test(i)&&(dv=dl(i,true))&&(dv=i.indexOf(")",i.length-dv)-i.length)){du[0]=du[0].slice(0,dv);du[2]=i.slice(0,dv)}}return du.slice(0,3)}},filter:{TAG:function(i){var du=i.replace(da,cx).toLowerCase();return i==="*"?function(){return true}:function(dv){return dv.nodeName&&dv.nodeName.toLowerCase()===du}},CLASS:function(du){var i=cd[du+" "];return i||(i=new RegExp("(^|"+cv+")"+du+"("+cv+"|$)"))&&cd(du,function(dv){return i.test(typeof dv.className==="string"&&dv.className||typeof dv.getAttribute!=="undefined"&&dv.getAttribute("class")||"")})},ATTR:function(du,dv,i){return function(dw){var dx=c5.attr(dw,du);if(dx==null){return dv==="!="}if(!dv){return true}dx+="";return dv==="="?dx===i:dv==="!="?dx!==i:dv==="^="?i&&dx.indexOf(i)===0:dv==="*="?i&&dx.indexOf(i)>-1:dv==="$="?i&&dx.slice(-i.length)===i:dv==="~="?(" "+dx.replace(cC," ")+" ").indexOf(i)>-1:dv==="|="?dx===i||dx.slice(0,i.length+1)===i+"-":false}},CHILD:function(dv,dz,i,dA,dw){var dy=dv.slice(0,3)!=="nth",du=dv.slice(-4)!=="last",dx=dz==="of-type";return dA===1&&dw===0?function(dB){return !!dB.parentNode}:function(dH,dE,dJ){var dB,dL,dO,dG,dI,dD,dF=dy!==du?"nextSibling":"previousSibling",dN=dH.parentNode,dC=dx&&dH.nodeName.toLowerCase(),dK=!dJ&&!dx,dM=false;if(dN){if(dy){while(dF){dG=dH;while((dG=dG[dF])){if(dx?dG.nodeName.toLowerCase()===dC:dG.nodeType===1){return false}}dD=dF=dv==="only"&&!dD&&"nextSibling"}return true}dD=[du?dN.firstChild:dN.lastChild];if(du&&dK){dG=dN;dO=dG[ct]||(dG[ct]={});dL=dO[dG.uniqueID]||(dO[dG.uniqueID]={});dB=dL[dv]||[];dI=dB[0]===dn&&dB[1];dM=dI&&dB[2];dG=dI&&dN.childNodes[dI];while((dG=++dI&&dG&&dG[dF]||(dM=dI=0)||dD.pop())){if(dG.nodeType===1&&++dM&&dG===dH){dL[dv]=[dn,dI,dM];break}}}else{if(dK){dG=dH;dO=dG[ct]||(dG[ct]={});dL=dO[dG.uniqueID]||(dO[dG.uniqueID]={});dB=dL[dv]||[];dI=dB[0]===dn&&dB[1];dM=dI}if(dM===false){while((dG=++dI&&dG&&dG[dF]||(dM=dI=0)||dD.pop())){if((dx?dG.nodeName.toLowerCase()===dC:dG.nodeType===1)&&++dM){if(dK){dO=dG[ct]||(dG[ct]={});dL=dO[dG.uniqueID]||(dO[dG.uniqueID]={});dL[dv]=[dn,dM]}if(dG===dH){break}}}}}dM-=dw;return dM===dA||(dM%dA===0&&dM/dA>=0)}}},PSEUDO:function(i,du){var dv,dw=cs.pseudos[i]||cs.setFilters[i.toLowerCase()]||c5.error("unsupported pseudo: "+i);if(dw[ct]){return dw(du)}if(dw.length>1){dv=[i,i,"",du];return cs.setFilters.hasOwnProperty(i.toLowerCase())?dj(function(dz,dB){var dy,dx=dw(dz,du),dA=dx.length;while(dA--){dy=ci(dz,dx[dA]);dz[dy]=!(dB[dy]=dx[dA])}}):function(dx){return dw(dx,0,dv)}}return dw}},pseudos:{not:dj(function(i){var du=[],dv=[],dw=c4(i.replace(cw,"$1"));return dw[ct]?dj(function(dx,dD,dA,dy){var dB,dC=dw(dx,null,dy,[]),dz=dx.length;while(dz--){if((dB=dC[dz])){dx[dz]=!(dD[dz]=dB)}}}):function(dz,dy,dx){du[0]=dz;dw(du,null,dx,dv);du[0]=null;return !dv.pop()}}),has:dj(function(i){return function(du){return c5(i,du).length>0}}),contains:dj(function(i){i=i.replace(da,cx);return function(du){return(du.textContent||du.innerText||cV(du)).indexOf(i)>-1}}),lang:dj(function(i){if(!cK.test(i||"")){c5.error("unsupported lang: "+i)}i=i.replace(da,cx).toLowerCase();return function(dv){var du;do{if((du=df?dv.lang:dv.getAttribute("xml:lang")||dv.getAttribute("lang"))){du=du.toLowerCase();return du===i||du.indexOf(i+"-")===0}}while((dv=dv.parentNode)&&dv.nodeType===1);return false}}),target:function(i){var du=cn.location&&cn.location.hash;return du&&du.slice(1)===i.id},root:function(i){return i===cu},focus:function(i){return i===cH.activeElement&&(!cH.hasFocus||cH.hasFocus())&&!!(i.type||i.href||~i.tabIndex)},enabled:function(i){return i.disabled===false},disabled:function(i){return i.disabled===true},checked:function(i){var du=i.nodeName.toLowerCase();return(du==="input"&&!!i.checked)||(du==="option"&&!!i.selected)},selected:function(i){if(i.parentNode){i.parentNode.selectedIndex}return i.selected===true},empty:function(i){for(i=i.firstChild;i;i=i.nextSibling){if(i.nodeType<6){return false}}return true},parent:function(i){return !cs.pseudos.empty(i)},header:function(i){return cr.test(i.nodeName)},input:function(i){return cj.test(i.nodeName)},button:function(du){var i=du.nodeName.toLowerCase();return i==="input"&&du.type==="button"||i==="button"},text:function(du){var i;return du.nodeName.toLowerCase()==="input"&&du.type==="text"&&((i=du.getAttribute("type"))==null||i.toLowerCase()==="text")},first:dc(function(){return[0]}),last:dc(function(du,i){return[i-1]}),eq:dc(function(dv,du,i){return[i<0?i+du:i]}),even:dc(function(dw,du){var dv=0;for(;dv<du;dv+=2){dw.push(dv)}return dw}),odd:dc(function(dw,du){var dv=1;for(;dv<du;dv+=2){dw.push(dv)}return dw}),lt:dc(function(dx,dv,du){var dw=du<0?du+dv:du;for(;--dw>=0;){dx.push(dw)}return dx}),gt:dc(function(dx,dv,du){var dw=du<0?du+dv:du;for(;++dw<dv;){dx.push(dw)}return dx})}};cs.pseudos.nth=cs.pseudos.eq;for(cF in {radio:true,checkbox:true,file:true,password:true,image:true}){cs.pseudos[cF]=cE(cF)}for(cF in {submit:true,reset:true}){cs.pseudos[cF]=dr(cF)}function c0(){}c0.prototype=cs.filters=cs.pseudos;cs.setFilters=new c0();dl=c5.tokenize=function(dw,dC){var dv,dy,i,dB,dA,dz,du,dx=de[dw+" "];if(dx){return dC?0:dx.slice(0)}dA=dw;dz=[];du=cs.preFilter;while(dA){if(!dv||(dy=c7.exec(dA))){if(dy){dA=dA.slice(dy[0].length)||dA}dz.push((i=[]))}dv=false;if((dy=cI.exec(dA))){dv=dy.shift();i.push({value:dv,type:dy[0].replace(cw," ")});dA=dA.slice(dv.length)}for(dB in cs.filter){if((dy=c8[dB].exec(dA))&&(!du[dB]||(dy=du[dB](dy)))){dv=dy.shift();i.push({value:dv,type:dB,matches:dy});dA=dA.slice(dv.length)}}if(!dv){break}}return dC?dA.length:dA?c5.error(dw):de(dw,dz).slice(0)};function cp(dx){var dw=0,dv=dx.length,du="";for(;dw<dv;dw++){du+=dx[dw].value}return du}function dd(dx,i,dw){var du=i.dir,dy=dw&&du==="parentNode",dv=c9++;return i.first?function(dB,dA,dz){while((dB=dB[du])){if(dB.nodeType===1||dy){return dx(dB,dA,dz)}}}:function(dD,dB,dA){var dF,dE,dC,dz=[dn,dv];if(dA){while((dD=dD[du])){if(dD.nodeType===1||dy){if(dx(dD,dB,dA)){return true}}}}else{while((dD=dD[du])){if(dD.nodeType===1||dy){dC=dD[ct]||(dD[ct]={});dE=dC[dD.uniqueID]||(dC[dD.uniqueID]={});if((dF=dE[du])&&dF[0]===dn&&dF[1]===dv){return(dz[2]=dF[2])}else{dE[du]=dz;if((dz[2]=dx(dD,dB,dA))){return true}}}}}}}function cg(i){return i.length>1?function(dx,dw,du){var dv=i.length;while(dv--){if(!i[dv](dx,dw,du)){return false}}return true}:i[0]}function cG(dv,dy,dx){var dw=0,du=dy.length;for(;dw<du;dw++){c5(dv,dy[dw],dx)}return dx}function cA(dC,dv,dB,dw,dz){var dx,dD=[],dy=0,dA=dC.length,du=dv!=null;for(;dy<dA;dy++){if((dx=dC[dy])){if(!dB||dB(dx,dw,dz)){dD.push(dx);if(du){dv.push(dy)}}}}return dD}function dm(dv,du,dx,dw,dy,i){if(dw&&!dw[ct]){dw=dm(dw)}if(dy&&!dy[ct]){dy=dm(dy,i)}return dj(function(dH,dG,dB,dI){var dL,dF,dC,dJ=[],dA=[],dK=dG.length,dz=dH||cG(du||"*",dB.nodeType?[dB]:dB,[]),dD=dv&&(dH||!du)?cA(dz,dJ,dv,dB,dI):dz,dE=dx?dy||(dH?dv:dK||dw)?[]:dG:dD;if(dx){dx(dD,dE,dB,dI)}if(dw){dL=cA(dE,dA);dw(dL,[],dB,dI);dF=dL.length;while(dF--){if((dC=dL[dF])){dE[dA[dF]]=!(dD[dA[dF]]=dC)}}}if(dH){if(dy||dv){if(dy){dL=[];dF=dE.length;while(dF--){if((dC=dE[dF])){dL.push((dD[dF]=dC))}}dy(null,(dE=[]),dL,dI)}dF=dE.length;while(dF--){if((dC=dE[dF])&&(dL=dy?ci(dH,dC):dJ[dF])>-1){dH[dL]=!(dG[dL]=dC)}}}}else{dE=cA(dE===dG?dE.splice(dK,dE.length):dE);if(dy){dy(null,dG,dE,dI)}else{ce.apply(dG,dE)}}})}function dg(du){var dw,dz,dx,dA=du.length,dC=cs.relative[du[0].type],dE=dC||cs.relative[" "],dy=dC?1:0,dD=dd(function(i){return i===dw},dE,true),dB=dd(function(i){return ci(dw,i)>-1},dE,true),dv=[function(dH,dG,dF){var i=(!dC&&(dF||dG!==ds))||((dw=dG).nodeType?dD(dH,dG,dF):dB(dH,dG,dF));dw=null;return i}];for(;dy<dA;dy++){if((dz=cs.relative[du[dy].type])){dv=[dd(cg(dv),dz)]}else{dz=cs.filter[du[dy].type].apply(null,du[dy].matches);if(dz[ct]){dx=++dy;for(;dx<dA;dx++){if(cs.relative[du[dx].type]){break}}return dm(dy>1&&cg(dv),dy>1&&cp(du.slice(0,dy-1).concat({value:du[dy-2].type===" "?"*":""})).replace(cw,"$1"),dz,dy<dx&&dg(du.slice(dy,dx)),dx<dA&&dg((du=du.slice(dx))),dx<dA&&cp(du))}dv.push(dz)}}return cg(dv)}function c6(dw,du){var i=du.length>0,dx=dw.length>0,dv=function(dG,dB,dH,dF,dK){var dC,dD,dI,dL=0,dE="0",dM=dG&&[],dN=[],dA=ds,dz=dG||dx&&cs.find.TAG("*",dK),dy=(dn+=dA==null?1:Math.random()||0.1),dJ=dz.length;if(dK){ds=dB===cH||dB||dK}for(;dE!==dJ&&(dC=dz[dE])!=null;dE++){if(dx&&dC){dD=0;if(!dB&&dC.ownerDocument!==cH){cD(dC);dH=!df}while((dI=dw[dD++])){if(dI(dC,dB||cH,dH)){dF.push(dC);break}}if(dK){dn=dy}}if(i){if((dC=!dI&&dC)){dL--}if(dG){dM.push(dC)}}}dL+=dE;if(i&&dE!==dL){dD=0;while((dI=du[dD++])){dI(dM,dN,dB,dH)}if(dG){if(dL>0){while(dE--){if(!(dM[dE]||dN[dE])){dN[dE]=dk.call(dF)}}}dN=cA(dN)}ce.apply(dF,dN);if(dK&&!dG&&dN.length>0&&(dL+du.length)>1){c5.uniqueSort(dF)}}if(dK){dn=dy;ds=dA}return dM};return i?dj(dv):dv}c4=c5.compile=function(du,dw){var dx,dy=[],dz=[],dv=cL[du+" "];if(!dv){if(!dw){dw=dl(du)}dx=dw.length;while(dx--){dv=dg(dw[dx]);if(dv[ct]){dy.push(dv)}else{dz.push(dv)}}dv=cL(du,c6(dz,dy));dv.selector=du}return dv};cm=c5.select=function(dx,dw,dA,dC){var dz,dv,dy,dE,dD,du=typeof dx==="function"&&dx,dB=!dC&&dl((dx=du.selector||dx));dA=dA||[];if(dB.length===1){dv=dB[0]=dB[0].slice(0);if(dv.length>2&&(dy=dv[0]).type==="ID"&&cl.getById&&dw.nodeType===9&&df&&cs.relative[dv[1].type]){dw=(cs.find.ID(dy.matches[0].replace(da,cx),dw)||[])[0];if(!dw){return dA}else{if(du){dw=dw.parentNode}}dx=dx.slice(dv.shift().value.length)}dz=c8.needsContext.test(dx)?0:dv.length;while(dz--){dy=dv[dz];if(cs.relative[(dE=dy.type)]){break}if((dD=cs.find[dE])){if((dC=dD(dy.matches[0].replace(da,cx),cB.test(dv[0].type)&&cZ(dw.parentNode)||dw))){dv.splice(dz,1);dx=dC.length&&cp(dv);if(!dx){ce.apply(dA,dC);return dA}break}}}}(du||c4(dx,dB))(dC,dw,!df,dA,!dw||cB.test(dx)&&cZ(dw.parentNode)||dw);return dA};cl.sortStable=ct.split("").sort(c1).join("")===ct;cl.detectDuplicates=!!c3;cD();cl.sortDetached=dp(function(i){return i.compareDocumentPosition(cH.createElement("div"))&1});if(!dp(function(i){i.innerHTML="<a href='#'></a>";return i.firstChild.getAttribute("href")==="#"})){ck("type|href|height|width",function(du,i,dv){if(!dv){return du.getAttribute(i,i.toLowerCase()==="type"?1:2)}})}if(!cl.attributes||!dp(function(i){i.innerHTML="<input/>";i.firstChild.setAttribute("value","");return i.firstChild.getAttribute("value")===""})){ck("value",function(du,i,dv){if(!dv&&du.nodeName.toLowerCase()==="input"){return du.defaultValue}})}if(!dp(function(i){return i.getAttribute("disabled")==null})){ck(cf,function(du,i,dw){var dv;if(!dw){return du[i]===true?i.toLowerCase():(dv=du.getAttributeNode(i))&&dv.specified?dv.value:null}})}return c5})(ax);L.find=b;L.expr=b.selectors;L.expr[":"]=L.expr.pseudos;L.uniqueSort=L.unique=b.uniqueSort;L.text=b.getText;L.isXMLDoc=b.isXML;L.contains=b.contains;var Y=function(cf,cd,cg){var i=[],ce=cg!==undefined;while((cf=cf[cd])&&cf.nodeType!==9){if(cf.nodeType===1){if(ce&&L(cf).is(cg)){break}i.push(cf)}}return i};var o=function(ce,cd){var i=[];for(;ce;ce=ce.nextSibling){if(ce.nodeType===1&&ce!==cd){i.push(ce)}}return i};var bW=L.expr.match.needsContext;var c=(/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/);var aS=/^.[^:#\[\.,]*$/;function aK(i,cd,ce){if(L.isFunction(cd)){return L.grep(i,function(cg,cf){return !!cd.call(cg,cf,cg)!==ce})}if(cd.nodeType){return L.grep(i,function(cf){return(cf===cd)!==ce})}if(typeof cd==="string"){if(aS.test(cd)){return L.filter(cd,i,ce)}cd=L.filter(cd,i)}return L.grep(i,function(cf){return(L.inArray(cf,cd)>-1)!==ce})}L.filter=function(cf,i,ce){var cd=i[0];if(ce){cf=":not("+cf+")"}return i.length===1&&cd.nodeType===1?L.find.matchesSelector(cd,cf)?[cd]:[]:L.find.matches(cf,L.grep(i,function(cg){return cg.nodeType===1}))};L.fn.extend({find:function(ce){var ch,cg=[],cf=this,cd=cf.length;if(typeof ce!=="string"){return this.pushStack(L(ce).filter(function(){for(ch=0;ch<cd;ch++){if(L.contains(cf[ch],this)){return true}}}))}for(ch=0;ch<cd;ch++){L.find(ce,cf[ch],cg)}cg=this.pushStack(cd>1?L.unique(cg):cg);cg.selector=this.selector?this.selector+" "+ce:ce;return cg},filter:function(i){return this.pushStack(aK(this,i||[],false))},not:function(i){return this.pushStack(aK(this,i||[],true))},is:function(i){return !!aK(this,typeof i==="string"&&bW.test(i)?L(i):i||[],false).length}});var bY,bv=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,bV=L.fn.init=function(i,cf,cd){var ce,cg;if(!i){return this}cd=cd||bY;if(typeof i==="string"){if(i.charAt(0)==="<"&&i.charAt(i.length-1)===">"&&i.length>=3){ce=[null,i,null]}else{ce=bv.exec(i)}if(ce&&(ce[1]||!cf)){if(ce[1]){cf=cf instanceof L?cf[0]:cf;L.merge(this,L.parseHTML(ce[1],cf&&cf.nodeType?cf.ownerDocument||cf:m,true));if(c.test(ce[1])&&L.isPlainObject(cf)){for(ce in cf){if(L.isFunction(this[ce])){this[ce](cf[ce])}else{this.attr(ce,cf[ce])}}}return this}else{cg=m.getElementById(ce[2]);if(cg&&cg.parentNode){if(cg.id!==ce[2]){return bY.find(i)}this.length=1;this[0]=cg}this.context=m;this.selector=i;return this}}else{if(!cf||cf.jquery){return(cf||cd).find(i)}else{return this.constructor(cf).find(i)}}}else{if(i.nodeType){this.context=this[0]=i;this.length=1;return this}else{if(L.isFunction(i)){return typeof cd.ready!=="undefined"?cd.ready(i):i(L)}}}if(i.selector!==undefined){this.selector=i.selector;this.context=i.context}return L.makeArray(i,this)};bV.prototype=L.fn;bY=L(m);var ab=/^(?:parents|prev(?:Until|All))/,Z={children:true,contents:true,next:true,prev:true};L.fn.extend({has:function(cg){var ce,cf=L(cg,this),cd=cf.length;return this.filter(function(){for(ce=0;ce<cd;ce++){if(L.contains(this,cf[ce])){return true}}})},closest:function(cf,ch){var ci,cg=0,ce=this.length,cd=[],cj=bW.test(cf)||typeof cf!=="string"?L(cf,ch||this.context):0;for(;cg<ce;cg++){for(ci=this[cg];ci&&ci!==ch;ci=ci.parentNode){if(ci.nodeType<11&&(cj?cj.index(ci)>-1:ci.nodeType===1&&L.find.matchesSelector(ci,cf))){cd.push(ci);break}}}return this.pushStack(cd.length>1?L.uniqueSort(cd):cd)},index:function(i){if(!i){return(this[0]&&this[0].parentNode)?this.first().prevAll().length:-1}if(typeof i==="string"){return L.inArray(this[0],L(i))}return L.inArray(i.jquery?i[0]:i,this)},add:function(i,cd){return this.pushStack(L.uniqueSort(L.merge(this.get(),L(i,cd))))},addBack:function(i){return this.add(i==null?this.prevObject:this.prevObject.filter(i))}});function a9(cd,i){do{cd=cd[i]}while(cd&&cd.nodeType!==1);return cd}L.each({parent:function(cd){var i=cd.parentNode;return i&&i.nodeType!==11?i:null},parents:function(i){return Y(i,"parentNode")},parentsUntil:function(ce,cd,cf){return Y(ce,"parentNode",cf)},next:function(i){return a9(i,"nextSibling")},prev:function(i){return a9(i,"previousSibling")},nextAll:function(i){return Y(i,"nextSibling")},prevAll:function(i){return Y(i,"previousSibling")},nextUntil:function(ce,cd,cf){return Y(ce,"nextSibling",cf)},prevUntil:function(ce,cd,cf){return Y(ce,"previousSibling",cf)},siblings:function(i){return o((i.parentNode||{}).firstChild,i)},children:function(i){return o(i.firstChild)},contents:function(i){return L.nodeName(i,"iframe")?i.contentDocument||i.contentWindow.document:L.merge([],i.childNodes)}},function(i,cd){L.fn[i]=function(cg,ce){var cf=L.map(this,cd,cg);if(i.slice(-5)!=="Until"){ce=cg}if(ce&&typeof ce==="string"){cf=L.filter(ce,cf)}if(this.length>1){if(!Z[i]){cf=L.uniqueSort(cf)}if(ab.test(i)){cf=cf.reverse()}}return this.pushStack(cf)}});var aO=(/\S+/g);function ap(cd){var i={};L.each(cd.match(aO)||[],function(cf,ce){i[ce]=true});return i}L.Callbacks=function(ck){ck=typeof ck==="string"?ap(ck):L.extend({},ck);var cf,ch,i,cg,cj=[],ci=[],cd=-1,ce=function(){cg=ck.once;i=cf=true;for(;ci.length;cd=-1){ch=ci.shift();while(++cd<cj.length){if(cj[cd].apply(ch[0],ch[1])===false&&ck.stopOnFalse){cd=cj.length;ch=false}}}if(!ck.memory){ch=false}cf=false;if(cg){if(ch){cj=[]}else{cj=""}}},cl={add:function(){if(cj){if(ch&&!cf){cd=cj.length-1;ci.push(ch)}(function cm(cn){L.each(cn,function(cp,co){if(L.isFunction(co)){if(!ck.unique||!cl.has(co)){cj.push(co)}}else{if(co&&co.length&&L.type(co)!=="string"){cm(co)}}})})(arguments);if(ch&&!cf){ce()}}return this},remove:function(){L.each(arguments,function(co,cm){var cn;while((cn=L.inArray(cm,cj,cn))>-1){cj.splice(cn,1);if(cn<=cd){cd--}}});return this},has:function(cm){return cm?L.inArray(cm,cj)>-1:cj.length>0},empty:function(){if(cj){cj=[]}return this},disable:function(){cg=ci=[];cj=ch="";return this},disabled:function(){return !cj},lock:function(){cg=true;if(!ch){cl.disable()}return this},locked:function(){return !!cg},fireWith:function(cn,cm){if(!cg){cm=cm||[];cm=[cn,cm.slice?cm.slice():cm];ci.push(cm);if(!cf){ce()}}return this},fire:function(){cl.fireWith(this,arguments);return this},fired:function(){return !!i}};return cl};L.extend({Deferred:function(ce){var cd=[["resolve","done",L.Callbacks("once memory"),"resolved"],["reject","fail",L.Callbacks("once memory"),"rejected"],["notify","progress",L.Callbacks("memory")]],cf="pending",cg={state:function(){return cf},always:function(){i.done(arguments).fail(arguments);return this},then:function(){var ch=arguments;return L.Deferred(function(ci){L.each(cd,function(ck,cj){var cl=L.isFunction(ch[ck])&&ch[ck];i[cj[1]](function(){var cm=cl&&cl.apply(this,arguments);if(cm&&L.isFunction(cm.promise)){cm.promise().progress(ci.notify).done(ci.resolve).fail(ci.reject)}else{ci[cj[0]+"With"](this===cg?ci.promise():this,cl?[cm]:arguments)}})});ch=null}).promise()},promise:function(ch){return ch!=null?L.extend(ch,cg):cg}},i={};cg.pipe=cg.then;L.each(cd,function(ci,ch){var ck=ch[2],cj=ch[3];cg[ch[1]]=ck.add;if(cj){ck.add(function(){cf=cj},cd[ci^1][2].disable,cd[2][2].lock)}i[ch[0]]=function(){i[ch[0]+"With"](this===i?cg:this,arguments);return this};i[ch[0]+"With"]=ck.fireWith});cg.promise(i);if(ce){ce.call(i,i)}return i},when:function(cf){var ci=0,cd=P.call(arguments),cj=cd.length,ch=cj!==1||(cf&&L.isFunction(cf.promise))?cj:0,cm=ch===1?cf:L.Deferred(),cg=function(cn,cp,co){return function(i){cp[cn]=this;co[cn]=arguments.length>1?P.call(arguments):i;if(co===cl){cm.notifyWith(cp,co)}else{if(!(--ch)){cm.resolveWith(cp,co)}}}},cl,ce,ck;if(cj>1){cl=new Array(cj);ce=new Array(cj);ck=new Array(cj);for(;ci<cj;ci++){if(cd[ci]&&L.isFunction(cd[ci].promise)){cd[ci].promise().progress(cg(ci,ce,cl)).done(cg(ci,ck,cd)).fail(cm.reject)}else{--ch}}}if(!ch){cm.resolveWith(ck,cd)}return cm.promise()}});var bj;L.fn.ready=function(i){L.ready.promise().done(i);return this};L.extend({isReady:false,readyWait:1,holdReady:function(i){if(i){L.readyWait++}else{L.ready(true)}},ready:function(i){if(i===true?--L.readyWait:L.isReady){return}L.isReady=true;if(i!==true&&--L.readyWait>0){return}bj.resolveWith(m,[L]);if(L.fn.triggerHandler){L(m).triggerHandler("ready");L(m).off("ready")}}});function ag(){if(m.addEventListener){m.removeEventListener("DOMContentLoaded",q);ax.removeEventListener("load",q)}else{m.detachEvent("onreadystatechange",q);ax.detachEvent("onload",q)}}function q(){if(m.addEventListener||ax.event.type==="load"||m.readyState==="complete"){ag();L.ready()}}L.ready.promise=function(cf){if(!bj){bj=L.Deferred();if(m.readyState==="complete"||(m.readyState!=="loading"&&!m.documentElement.doScroll)){ax.setTimeout(L.ready)}else{if(m.addEventListener){m.addEventListener("DOMContentLoaded",q);ax.addEventListener("load",q)}else{m.attachEvent("onreadystatechange",q);ax.attachEvent("onload",q);var ce=false;try{ce=ax.frameElement==null&&m.documentElement}catch(cd){}if(ce&&ce.doScroll){(function i(){if(!L.isReady){try{ce.doScroll("left")}catch(cg){return ax.setTimeout(i,50)}ag();L.ready()}})()}}}}return bj.promise(cf)};L.ready.promise();var bo;for(bo in L(bQ)){break}bQ.ownFirst=bo==="0";bQ.inlineBlockNeedsLayout=false;L(function(){var ce,cf,i,cd;i=m.getElementsByTagName("body")[0];if(!i||!i.style){return}cf=m.createElement("div");cd=m.createElement("div");cd.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";i.appendChild(cd).appendChild(cf);if(typeof cf.style.zoom!=="undefined"){cf.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1";bQ.inlineBlockNeedsLayout=ce=cf.offsetWidth===3;if(ce){i.style.zoom=1}}i.removeChild(cd)});(function(){var cd=m.createElement("div");bQ.deleteExpando=true;try{delete cd.test}catch(i){bQ.deleteExpando=false}cd=null})();var bF=function(ce){var cd=L.noData[(ce.nodeName+" ").toLowerCase()],i=+ce.nodeType||1;return i!==1&&i!==9?false:!cd||cd!==true&&ce.getAttribute("classid")===cd};var aa=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,aM=/([A-Z])/g;function bA(ce,cd,cf){if(cf===undefined&&ce.nodeType===1){var i="data-"+cd.replace(aM,"-$1").toLowerCase();cf=ce.getAttribute(i);if(typeof cf==="string"){try{cf=cf==="true"?true:cf==="false"?false:cf==="null"?null:+cf+""===cf?+cf:aa.test(cf)?L.parseJSON(cf):cf}catch(cg){}L.data(ce,cd,cf)}else{cf=undefined}}return cf}function bE(cd){var i;for(i in cd){if(i==="data"&&L.isEmptyObject(cd[i])){continue}if(i!=="toJSON"){return false}}return true}function ao(ch,cd,ci,cg){if(!bF(ch)){return}var cj,cf,cl=L.expando,ck=ch.nodeType,i=ck?L.cache:ch,ce=ck?ch[cl]:ch[cl]&&cl;if((!ce||!i[ce]||(!cg&&!i[ce].data))&&ci===undefined&&typeof cd==="string"){return}if(!ce){if(ck){ce=ch[cl]=aY.pop()||L.guid++}else{ce=cl}}if(!i[ce]){i[ce]=ck?{}:{toJSON:L.noop}}if(typeof cd==="object"||typeof cd==="function"){if(cg){i[ce]=L.extend(i[ce],cd)}else{i[ce].data=L.extend(i[ce].data,cd)}}cf=i[ce];if(!cg){if(!cf.data){cf.data={}}cf=cf.data}if(ci!==undefined){cf[L.camelCase(cd)]=ci}if(typeof cd==="string"){cj=cf[cd];if(cj==null){cj=cf[L.camelCase(cd)]}}else{cj=cf}return cj}function ah(cj,ch,cf){if(!bF(cj)){return}var ce,ci,cd=cj.nodeType,cg=cd?L.cache:cj,ck=cd?cj[L.expando]:L.expando;if(!cg[ck]){return}if(ch){ce=cf?cg[ck]:cg[ck].data;if(ce){if(!L.isArray(ch)){if(ch in ce){ch=[ch]}else{ch=L.camelCase(ch);if(ch in ce){ch=[ch]}else{ch=ch.split(" ")}}}else{ch=ch.concat(L.map(ch,L.camelCase))}ci=ch.length;while(ci--){delete ce[ch[ci]]}if(cf?!bE(ce):!L.isEmptyObject(ce)){return}}}if(!cf){delete cg[ck].data;if(!bE(cg[ck])){return}}if(cd){L.cleanData([cj],true)}else{if(bQ.deleteExpando||cg!=cg.window){delete cg[ck]}else{cg[ck]=undefined}}}L.extend({cache:{},noData:{"applet ":true,"embed ":true,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(i){i=i.nodeType?L.cache[i[L.expando]]:i[L.expando];return !!i&&!bE(i)},data:function(cd,i,ce){return ao(cd,i,ce)},removeData:function(cd,i){return ah(cd,i)},_data:function(cd,i,ce){return ao(cd,i,ce,true)},_removeData:function(cd,i){return ah(cd,i,true)}});L.fn.extend({data:function(cg,cj){var cf,ce,ci,ch=this[0],cd=ch&&ch.attributes;if(cg===undefined){if(this.length){ci=L.data(ch);if(ch.nodeType===1&&!L._data(ch,"parsedAttrs")){cf=cd.length;while(cf--){if(cd[cf]){ce=cd[cf].name;if(ce.indexOf("data-")===0){ce=L.camelCase(ce.slice(5));bA(ch,ce,ci[ce])}}}L._data(ch,"parsedAttrs",true)}}return ci}if(typeof cg==="object"){return this.each(function(){L.data(this,cg)})}return arguments.length>1?this.each(function(){L.data(this,cg,cj)}):ch?bA(ch,cg,L.data(ch,cg)):undefined},removeData:function(i){return this.each(function(){L.removeData(this,i)})}});L.extend({queue:function(ce,cd,cf){var i;if(ce){cd=(cd||"fx")+"queue";i=L._data(ce,cd);if(cf){if(!i||L.isArray(cf)){i=L._data(ce,cd,L.makeArray(cf))}else{i.push(cf)}}return i||[]}},dequeue:function(ch,cg){cg=cg||"fx";var cd=L.queue(ch,cg),ci=cd.length,cf=cd.shift(),i=L._queueHooks(ch,cg),ce=function(){L.dequeue(ch,cg)};if(cf==="inprogress"){cf=cd.shift();ci--}if(cf){if(cg==="fx"){cd.unshift("inprogress")}delete i.stop;cf.call(ch,ce,i)}if(!ci&&i){i.empty.fire()}},_queueHooks:function(ce,cd){var i=cd+"queueHooks";return L._data(ce,i)||L._data(ce,i,{empty:L.Callbacks("once memory").add(function(){L._removeData(ce,cd+"queue");L._removeData(ce,i)})})}});L.fn.extend({queue:function(i,cd){var ce=2;if(typeof i!=="string"){cd=i;i="fx";ce--}if(arguments.length<ce){return L.queue(this[0],i)}return cd===undefined?this:this.each(function(){var cf=L.queue(this,i,cd);L._queueHooks(this,i);if(i==="fx"&&cf[0]!=="inprogress"){L.dequeue(this,i)}})},dequeue:function(i){return this.each(function(){L.dequeue(this,i)})},clearQueue:function(i){return this.queue(i||"fx",[])},promise:function(cg,cj){var cf,ch=1,ck=L.Deferred(),cd=this,ce=this.length,ci=function(){if(!(--ch)){ck.resolveWith(cd,[cd])}};if(typeof cg!=="string"){cj=cg;cg=undefined}cg=cg||"fx";while(ce--){cf=L._data(cd[ce],cg+"queueHooks");if(cf&&cf.empty){ch++;cf.empty.add(ci)}}ci();return ck.promise(cj)}});(function(){var i;bQ.shrinkWrapBlocks=function(){if(i!=null){return i}i=false;var cf,cd,ce;cd=m.getElementsByTagName("body")[0];if(!cd||!cd.style){return}cf=m.createElement("div");ce=m.createElement("div");ce.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px";cd.appendChild(ce).appendChild(cf);if(typeof cf.style.zoom!=="undefined"){cf.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1";cf.appendChild(m.createElement("div")).style.width="5px";i=cf.offsetWidth!==3}cd.removeChild(ce);return i}})();var aP=(/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/).source;var b4=new RegExp("^(?:([+-])=|)("+aP+")([a-z%]*)$","i");var bR=["Top","Right","Bottom","Left"];var bD=function(cd,i){cd=i||cd;return L.css(cd,"display")==="none"||!L.contains(cd.ownerDocument,cd)};function s(ce,i,cg,cm){var ck,cf=1,ci=20,cl=cm?function(){return cm.cur()}:function(){return L.css(ce,i,"")},ch=cl(),cj=cg&&cg[3]||(L.cssNumber[i]?"":"px"),cd=(L.cssNumber[i]||cj!=="px"&&+ch)&&b4.exec(L.css(ce,i));if(cd&&cd[3]!==cj){cj=cj||cd[3];cg=cg||[];cd=+ch||1;do{cf=cf||".5";cd=cd/cf;L.style(ce,i,cd+cj)}while(cf!==(cf=cl()/ch)&&cf!==1&&--ci)}if(cg){cd=+cd||+ch||0;ck=cg[1]?cd+(cg[1]+1)*cg[2]:+cg[2];if(cm){cm.unit=cj;cm.start=cd;cm.end=ck}}return ck}var a0=function(cd,ci,cl,cj,ce,cm,ck){var cf=0,cg=cd.length,ch=cl==null;if(L.type(cl)==="object"){ce=true;for(cf in cl){a0(cd,ci,cf,cl[cf],true,cm,ck)}}else{if(cj!==undefined){ce=true;if(!L.isFunction(cj)){ck=true}if(ch){if(ck){ci.call(cd,cj);ci=null}else{ch=ci;ci=function(cn,i,co){return ch.call(L(cn),co)}}}if(ci){for(;cf<cg;cf++){ci(cd[cf],cl,ck?cj:cj.call(cd[cf],cf,ci(cd[cf],cl)))}}}}return ce?cd:ch?ci.call(cd):cg?ci(cd[0],cl):cm};var aV=(/^(?:checkbox|radio)$/i);var ca=(/<([\w:-]+)/);var bB=(/^$|\/(?:java|ecma)script/i);var cc=(/^\s+/);var f="abbr|article|aside|audio|bdi|canvas|data|datalist|details|dialog|figcaption|figure|footer|header|hgroup|main|mark|meter|nav|output|picture|progress|section|summary|template|time|video";function bT(i){var ce=f.split("|"),cd=i.createDocumentFragment();if(cd.createElement){while(ce.length){cd.createElement(ce.pop())}}return cd}(function(){var ce=m.createElement("div"),cd=m.createDocumentFragment(),i=m.createElement("input");ce.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";bQ.leadingWhitespace=ce.firstChild.nodeType===3;bQ.tbody=!ce.getElementsByTagName("tbody").length;bQ.htmlSerialize=!!ce.getElementsByTagName("link").length;bQ.html5Clone=m.createElement("nav").cloneNode(true).outerHTML!=="<:nav></:nav>";i.type="checkbox";i.checked=true;cd.appendChild(i);bQ.appendChecked=i.checked;ce.innerHTML="<textarea>x</textarea>";bQ.noCloneChecked=!!ce.cloneNode(true).lastChild.defaultValue;cd.appendChild(ce);i=m.createElement("input");i.setAttribute("type","radio");i.setAttribute("checked","checked");i.setAttribute("name","t");ce.appendChild(i);bQ.checkClone=ce.cloneNode(true).cloneNode(true).lastChild.checked;bQ.noCloneEvent=!!ce.addEventListener;ce[L.expando]=1;bQ.attributes=!ce.getAttribute(L.expando)})();var W={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:bQ.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]};W.optgroup=W.option;W.tbody=W.tfoot=W.colgroup=W.caption=W.thead;W.th=W.td;function a(cg,cd){var ce,ch,cf=0,ci=typeof cg.getElementsByTagName!=="undefined"?cg.getElementsByTagName(cd||"*"):typeof cg.querySelectorAll!=="undefined"?cg.querySelectorAll(cd||"*"):undefined;if(!ci){for(ci=[],ce=cg.childNodes||cg;(ch=ce[cf])!=null;cf++){if(!cd||L.nodeName(ch,cd)){ci.push(ch)}else{L.merge(ci,a(ch,cd))}}}return cd===undefined||cd&&L.nodeName(cg,cd)?L.merge([cg],ci):ci}function ac(ce,cd){var cg,cf=0;for(;(cg=ce[cf])!=null;cf++){L._data(cg,"globalEval",!cd||L._data(cd[cf],"globalEval"))}}var M=/<|&#?\w+;/,n=/<tbody/i;function b5(i){if(aV.test(i.type)){i.defaultChecked=i.checked}}function t(ce,cg,cl,cr,ci){var cm,cj,cq,cp,cs,co,cf,ck=ce.length,ch=bT(cg),cd=[],cn=0;for(;cn<ck;cn++){cj=ce[cn];if(cj||cj===0){if(L.type(cj)==="object"){L.merge(cd,cj.nodeType?[cj]:cj)}else{if(!M.test(cj)){cd.push(cg.createTextNode(cj))}else{cp=cp||ch.appendChild(cg.createElement("div"));cs=(ca.exec(cj)||["",""])[1].toLowerCase();cf=W[cs]||W._default;cp.innerHTML=cf[1]+L.htmlPrefilter(cj)+cf[2];cm=cf[0];while(cm--){cp=cp.lastChild}if(!bQ.leadingWhitespace&&cc.test(cj)){cd.push(cg.createTextNode(cc.exec(cj)[0]))}if(!bQ.tbody){cj=cs==="table"&&!n.test(cj)?cp.firstChild:cf[1]==="<table>"&&!n.test(cj)?cp:0;cm=cj&&cj.childNodes.length;while(cm--){if(L.nodeName((co=cj.childNodes[cm]),"tbody")&&!co.childNodes.length){cj.removeChild(co)}}}L.merge(cd,cp.childNodes);cp.textContent="";while(cp.firstChild){cp.removeChild(cp.firstChild)}cp=ch.lastChild}}}}if(cp){ch.removeChild(cp)}if(!bQ.appendChecked){L.grep(a(cd,"input"),b5)}cn=0;while((cj=cd[cn++])){if(cr&&L.inArray(cj,cr)>-1){if(ci){ci.push(cj)}continue}cq=L.contains(cj.ownerDocument,cj);cp=a(ch.appendChild(cj),"script");if(cq){ac(cp)}if(cl){cm=0;while((cj=cp[cm++])){if(bB.test(cj.type||"")){cl.push(cj)}}}}cp=null;return ch}(function(){var ce,cd,cf=m.createElement("div");for(ce in {submit:true,change:true,focusin:true}){cd="on"+ce;if(!(bQ[ce]=cd in ax)){cf.setAttribute(cd,"t");bQ[ce]=cf.attributes[cd].expando===false}}cf=null})();var bG=/^(?:input|select|textarea)$/i,bh=/^key/,F=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,bC=/^(?:focusinfocus|focusoutblur)$/,bz=/^([^.]*)(?:\.(.+)|)/;function V(){return true}function bw(){return false}function az(){try{return m.activeElement}catch(i){}}function bp(ch,ce,i,ci,cg,cd){var cj,cf;if(typeof ce==="object"){if(typeof i!=="string"){ci=ci||i;i=undefined}for(cf in ce){bp(ch,cf,i,ci,ce[cf],cd)}return ch}if(ci==null&&cg==null){cg=i;ci=i=undefined}else{if(cg==null){if(typeof i==="string"){cg=ci;ci=undefined}else{cg=ci;ci=i;i=undefined}}}if(cg===false){cg=bw}else{if(!cg){return ch}}if(cd===1){cj=cg;cg=function(ck){L().off(ck);return cj.apply(this,arguments)};cg.guid=cj.guid||(cj.guid=L.guid++)}return ch.each(function(){L.event.add(this,ce,cg,ci,i)})}L.event={global:{},add:function(cj,cn,cs,cm,ck){var cl,cq,cr,ch,i,cg,cp,ci,co,ce,cf,cd=L._data(cj);if(!cd){return}if(cs.handler){ch=cs;cs=ch.handler;ck=ch.selector}if(!cs.guid){cs.guid=L.guid++}if(!(cq=cd.events)){cq=cd.events={}}if(!(cg=cd.handle)){cg=cd.handle=function(ct){return typeof L!=="undefined"&&(!ct||L.event.triggered!==ct.type)?L.event.dispatch.apply(cg.elem,arguments):undefined};cg.elem=cj}cn=(cn||"").match(aO)||[""];cr=cn.length;while(cr--){cl=bz.exec(cn[cr])||[];co=cf=cl[1];ce=(cl[2]||"").split(".").sort();if(!co){continue}i=L.event.special[co]||{};co=(ck?i.delegateType:i.bindType)||co;i=L.event.special[co]||{};cp=L.extend({type:co,origType:cf,data:cm,handler:cs,guid:cs.guid,selector:ck,needsContext:ck&&L.expr.match.needsContext.test(ck),namespace:ce.join(".")},ch);if(!(ci=cq[co])){ci=cq[co]=[];ci.delegateCount=0;if(!i.setup||i.setup.call(cj,cm,ce,cg)===false){if(cj.addEventListener){cj.addEventListener(co,cg,false)}else{if(cj.attachEvent){cj.attachEvent("on"+co,cg)}}}}if(i.add){i.add.call(cj,cp);if(!cp.handler.guid){cp.handler.guid=cs.guid}}if(ck){ci.splice(ci.delegateCount++,0,cp)}else{ci.push(cp)}L.event.global[co]=true}cj=null},remove:function(ch,cn,cs,ci,cj){var cl,cq,cm,ck,cr,cp,i,cg,co,ce,cf,cd=L.hasData(ch)&&L._data(ch);if(!cd||!(cp=cd.events)){return}cn=(cn||"").match(aO)||[""];cr=cn.length;while(cr--){cm=bz.exec(cn[cr])||[];co=cf=cm[1];ce=(cm[2]||"").split(".").sort();if(!co){for(co in cp){L.event.remove(ch,co+cn[cr],cs,ci,true)}continue}i=L.event.special[co]||{};co=(ci?i.delegateType:i.bindType)||co;cg=cp[co]||[];cm=cm[2]&&new RegExp("(^|\\.)"+ce.join("\\.(?:.*\\.|)")+"(\\.|$)");ck=cl=cg.length;while(cl--){cq=cg[cl];if((cj||cf===cq.origType)&&(!cs||cs.guid===cq.guid)&&(!cm||cm.test(cq.namespace))&&(!ci||ci===cq.selector||ci==="**"&&cq.selector)){cg.splice(cl,1);if(cq.selector){cg.delegateCount--}if(i.remove){i.remove.call(ch,cq)}}}if(ck&&!cg.length){if(!i.teardown||i.teardown.call(ch,ce,cd.handle)===false){L.removeEvent(ch,co,cd.handle)}delete cp[co]}}if(L.isEmptyObject(cp)){delete cd.handle;L._removeData(ch,"events")}},trigger:function(ce,cl,ch,cq){var cg,cm,cp,cr,cd,ck,cj,ci=[ch||m],co=bJ.call(ce,"type")?ce.type:ce,cf=bJ.call(ce,"namespace")?ce.namespace.split("."):[];cp=ck=ch=ch||m;if(ch.nodeType===3||ch.nodeType===8){return}if(bC.test(co+L.event.triggered)){return}if(co.indexOf(".")>-1){cf=co.split(".");co=cf.shift();cf.sort()}cm=co.indexOf(":")<0&&"on"+co;ce=ce[L.expando]?ce:new L.Event(co,typeof ce==="object"&&ce);ce.isTrigger=cq?2:3;ce.namespace=cf.join(".");ce.rnamespace=ce.namespace?new RegExp("(^|\\.)"+cf.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;ce.result=undefined;if(!ce.target){ce.target=ch}cl=cl==null?[ce]:L.makeArray(cl,[ce]);cd=L.event.special[co]||{};if(!cq&&cd.trigger&&cd.trigger.apply(ch,cl)===false){return}if(!cq&&!cd.noBubble&&!L.isWindow(ch)){cr=cd.delegateType||co;if(!bC.test(cr+co)){cp=cp.parentNode}for(;cp;cp=cp.parentNode){ci.push(cp);ck=cp}if(ck===(ch.ownerDocument||m)){ci.push(ck.defaultView||ck.parentWindow||ax)}}cj=0;while((cp=ci[cj++])&&!ce.isPropagationStopped()){ce.type=cj>1?cr:cd.bindType||co;cg=(L._data(cp,"events")||{})[ce.type]&&L._data(cp,"handle");if(cg){cg.apply(cp,cl)}cg=cm&&cp[cm];if(cg&&cg.apply&&bF(cp)){ce.result=cg.apply(cp,cl);if(ce.result===false){ce.preventDefault()}}}ce.type=co;if(!cq&&!ce.isDefaultPrevented()){if((!cd._default||cd._default.apply(ci.pop(),cl)===false)&&bF(ch)){if(cm&&ch[co]&&!L.isWindow(ch)){ck=ch[cm];if(ck){ch[cm]=null}L.event.triggered=co;try{ch[co]()}catch(cn){}L.event.triggered=undefined;if(ck){ch[cm]=ck}}}}return ce.result},dispatch:function(ce){ce=L.event.fix(ce);var ci,ch,cj,cf,cl,cm=[],ck=P.call(arguments),cg=(L._data(this,"events")||{})[ce.type]||[],cd=L.event.special[ce.type]||{};ck[0]=ce;ce.delegateTarget=this;if(cd.preDispatch&&cd.preDispatch.call(this,ce)===false){return}cm=L.event.handlers.call(this,ce,cg);ci=0;while((cf=cm[ci++])&&!ce.isPropagationStopped()){ce.currentTarget=cf.elem;ch=0;while((cl=cf.handlers[ch++])&&!ce.isImmediatePropagationStopped()){if(!ce.rnamespace||ce.rnamespace.test(cl.namespace)){ce.handleObj=cl;ce.data=cl.data;cj=((L.event.special[cl.origType]||{}).handle||cl.handler).apply(cf.elem,ck);if(cj!==undefined){if((ce.result=cj)===false){ce.preventDefault();ce.stopPropagation()}}}}}if(cd.postDispatch){cd.postDispatch.call(this,ce)}return ce.result},handlers:function(cd,cf){var ch,ci,ce,cj,cl=[],cg=cf.delegateCount,ck=cd.target;if(cg&&ck.nodeType&&(cd.type!=="click"||isNaN(cd.button)||cd.button<1)){for(;ck!=this;ck=ck.parentNode||this){if(ck.nodeType===1&&(ck.disabled!==true||cd.type!=="click")){ci=[];for(ch=0;ch<cg;ch++){cj=cf[ch];ce=cj.selector+" ";if(ci[ce]===undefined){ci[ce]=cj.needsContext?L(ce,this).index(ck)>-1:L.find(ce,this,null,[ck]).length}if(ci[ce]){ci.push(cj)}}if(ci.length){cl.push({elem:ck,handlers:ci})}}}}if(cg<cf.length){cl.push({elem:this,handlers:cf.slice(cg)})}return cl},fix:function(ch){if(ch[L.expando]){return ch}var cf,cj,ci,cg=ch.type,ce=ch,cd=this.fixHooks[cg];if(!cd){this.fixHooks[cg]=cd=F.test(cg)?this.mouseHooks:bh.test(cg)?this.keyHooks:{}}ci=cd.props?this.props.concat(cd.props):this.props;ch=new L.Event(ce);cf=ci.length;while(cf--){cj=ci[cf];ch[cj]=ce[cj]}if(!ch.target){ch.target=ce.srcElement||m}if(ch.target.nodeType===3){ch.target=ch.target.parentNode}ch.metaKey=!!ch.metaKey;return cd.filter?cd.filter(ch,ce):ch},props:("altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which").split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(cd,i){if(cd.which==null){cd.which=i.charCode!=null?i.charCode:i.keyCode}return cd}},mouseHooks:{props:("button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement").split(" "),filter:function(cf,ce){var i,cg,ch,cd=ce.button,ci=ce.fromElement;if(cf.pageX==null&&ce.clientX!=null){cg=cf.target.ownerDocument||m;ch=cg.documentElement;i=cg.body;cf.pageX=ce.clientX+(ch&&ch.scrollLeft||i&&i.scrollLeft||0)-(ch&&ch.clientLeft||i&&i.clientLeft||0);cf.pageY=ce.clientY+(ch&&ch.scrollTop||i&&i.scrollTop||0)-(ch&&ch.clientTop||i&&i.clientTop||0)}if(!cf.relatedTarget&&ci){cf.relatedTarget=ci===cf.target?ce.toElement:ci}if(!cf.which&&cd!==undefined){cf.which=(cd&1?1:(cd&2?3:(cd&4?2:0)))}return cf}},special:{load:{noBubble:true},focus:{trigger:function(){if(this!==az()&&this.focus){try{this.focus();return false}catch(i){}}},delegateType:"focusin"},blur:{trigger:function(){if(this===az()&&this.blur){this.blur();return false}},delegateType:"focusout"},click:{trigger:function(){if(L.nodeName(this,"input")&&this.type==="checkbox"&&this.click){this.click();return false}},_default:function(i){return L.nodeName(i.target,"a")}},beforeunload:{postDispatch:function(i){if(i.result!==undefined&&i.originalEvent){i.originalEvent.returnValue=i.result}}}},simulate:function(i,ce,cd){var cf=L.extend(new L.Event(),cd,{type:i,isSimulated:true});L.event.trigger(cf,null,ce);if(cf.isDefaultPrevented()){cd.preventDefault()}}};L.removeEvent=m.removeEventListener?function(ce,cd,i){if(ce.removeEventListener){ce.removeEventListener(cd,i)}}:function(cf,ce,i){var cd="on"+ce;if(cf.detachEvent){if(typeof cf[cd]==="undefined"){cf[cd]=null}cf.detachEvent(cd,i)}};L.Event=function(cd,i){if(!(this instanceof L.Event)){return new L.Event(cd,i)}if(cd&&cd.type){this.originalEvent=cd;this.type=cd.type;this.isDefaultPrevented=cd.defaultPrevented||cd.defaultPrevented===undefined&&cd.returnValue===false?V:bw}else{this.type=cd}if(i){L.extend(this,i)}this.timeStamp=cd&&cd.timeStamp||L.now();this[L.expando]=true};L.Event.prototype={constructor:L.Event,isDefaultPrevented:bw,isPropagationStopped:bw,isImmediatePropagationStopped:bw,preventDefault:function(){var i=this.originalEvent;this.isDefaultPrevented=V;if(!i){return}if(i.preventDefault){i.preventDefault()}else{i.returnValue=false}},stopPropagation:function(){var i=this.originalEvent;this.isPropagationStopped=V;if(!i||this.isSimulated){return}if(i.stopPropagation){i.stopPropagation()}i.cancelBubble=true},stopImmediatePropagation:function(){var i=this.originalEvent;this.isImmediatePropagationStopped=V;if(i&&i.stopImmediatePropagation){i.stopImmediatePropagation()}this.stopPropagation()}};L.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(cd,i){L.event.special[cd]={delegateType:i,bindType:i,handle:function(cg){var ce,ci=this,ch=cg.relatedTarget,cf=cg.handleObj;if(!ch||(ch!==ci&&!L.contains(ci,ch))){cg.type=cf.origType;ce=cf.handler.apply(this,arguments);cg.type=i}return ce}}});if(!bQ.submit){L.event.special.submit={setup:function(){if(L.nodeName(this,"form")){return false}L.event.add(this,"click._submit keypress._submit",function(ce){var cd=ce.target,i=L.nodeName(cd,"input")||L.nodeName(cd,"button")?L.prop(cd,"form"):undefined;if(i&&!L._data(i,"submit")){L.event.add(i,"submit._submit",function(cf){cf._submitBubble=true});L._data(i,"submit",true)}})},postDispatch:function(i){if(i._submitBubble){delete i._submitBubble;if(this.parentNode&&!i.isTrigger){L.event.simulate("submit",this.parentNode,i)}}},teardown:function(){if(L.nodeName(this,"form")){return false}L.event.remove(this,"._submit")}}}if(!bQ.change){L.event.special.change={setup:function(){if(bG.test(this.nodeName)){if(this.type==="checkbox"||this.type==="radio"){L.event.add(this,"propertychange._change",function(i){if(i.originalEvent.propertyName==="checked"){this._justChanged=true}});L.event.add(this,"click._change",function(i){if(this._justChanged&&!i.isTrigger){this._justChanged=false}L.event.simulate("change",this,i)})}return false}L.event.add(this,"beforeactivate._change",function(cd){var i=cd.target;if(bG.test(i.nodeName)&&!L._data(i,"change")){L.event.add(i,"change._change",function(ce){if(this.parentNode&&!ce.isSimulated&&!ce.isTrigger){L.event.simulate("change",this.parentNode,ce)}});L._data(i,"change",true)}})},handle:function(cd){var i=cd.target;if(this!==i||cd.isSimulated||cd.isTrigger||(i.type!=="radio"&&i.type!=="checkbox")){return cd.handleObj.handler.apply(this,arguments)}},teardown:function(){L.event.remove(this,"._change");return !bG.test(this.nodeName)}}}if(!bQ.focusin){L.each({focus:"focusin",blur:"focusout"},function(ce,i){var cd=function(cf){L.event.simulate(i,cf.target,L.event.fix(cf))};L.event.special[i]={setup:function(){var cg=this.ownerDocument||this,cf=L._data(cg,i);if(!cf){cg.addEventListener(ce,cd,true)}L._data(cg,i,(cf||0)+1)},teardown:function(){var cg=this.ownerDocument||this,cf=L._data(cg,i)-1;if(!cf){cg.removeEventListener(ce,cd,true);L._removeData(cg,i)}else{L._data(cg,i,cf)}}}})}L.fn.extend({on:function(cd,i,cf,ce){return bp(this,cd,i,cf,ce)},one:function(cd,i,cf,ce){return bp(this,cd,i,cf,ce,1)},off:function(ce,i,cg){var cd,cf;if(ce&&ce.preventDefault&&ce.handleObj){cd=ce.handleObj;L(ce.delegateTarget).off(cd.namespace?cd.origType+"."+cd.namespace:cd.origType,cd.selector,cd.handler);return this}if(typeof ce==="object"){for(cf in ce){this.off(cf,i,ce[cf])}return this}if(i===false||typeof i==="function"){cg=i;i=undefined}if(cg===false){cg=bw}return this.each(function(){L.event.remove(this,ce,cg,i)})},trigger:function(i,cd){return this.each(function(){L.event.trigger(i,cd,this)})},triggerHandler:function(i,ce){var cd=this[0];if(cd){return L.event.trigger(i,ce,cd,true)}}});var aN=/ jQuery\d+="(?:null|\d+)"/g,bH=new RegExp("<(?:"+f+")[\\s/>]","i"),aQ=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,be=/<script|<style|<link/i,bX=/checked\s*(?:[^=]|=\s*.checked.)/i,aD=/^true\/(.*)/,aX=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,a4=bT(m),k=a4.appendChild(m.createElement("div"));function bf(cd,i){return L.nodeName(cd,"table")&&L.nodeName(i.nodeType!==11?i:i.firstChild,"tr")?cd.getElementsByTagName("tbody")[0]||cd.appendChild(cd.ownerDocument.createElement("tbody")):cd}function b1(i){i.type=(L.find.attr(i,"type")!==null)+"/"+i.type;return i}function al(cd){var i=aD.exec(cd.type);if(i){cd.type=i[1]}else{cd.removeAttribute("type")}return cd}function aC(ck,cf){if(cf.nodeType!==1||!L.hasData(ck)){return}var ch,cg,cd,cj=L._data(ck),ci=L._data(cf,cj),ce=cj.events;if(ce){delete ci.handle;ci.events={};for(ch in ce){for(cg=0,cd=ce[ch].length;cg<cd;cg++){L.event.add(cf,ch,ce[ch][cg])}}}if(ci.data){ci.data=L.extend({},ci.data)}}function S(cf,i){var cg,ce,cd;if(i.nodeType!==1){return}cg=i.nodeName.toLowerCase();if(!bQ.noCloneEvent&&i[L.expando]){cd=L._data(i);for(ce in cd.events){L.removeEvent(i,ce,cd.handle)}i.removeAttribute(L.expando)}if(cg==="script"&&i.text!==cf.text){b1(i).text=cf.text;al(i)}else{if(cg==="object"){if(i.parentNode){i.outerHTML=cf.outerHTML}if(bQ.html5Clone&&(cf.innerHTML&&!L.trim(i.innerHTML))){i.innerHTML=cf.innerHTML}}else{if(cg==="input"&&aV.test(cf.type)){i.defaultChecked=i.checked=cf.checked;if(i.value!==cf.value){i.value=cf.value}}else{if(cg==="option"){i.defaultSelected=i.selected=cf.defaultSelected}else{if(cg==="input"||cg==="textarea"){i.defaultValue=cf.defaultValue}}}}}}function B(cf,cm,cq,cg){cm=a1.apply([],cm);var cl,ch,cn,cj,cp,cd,ck=0,ci=cf.length,cr=ci-1,co=cm[0],ce=L.isFunction(co);if(ce||(ci>1&&typeof co==="string"&&!bQ.checkClone&&bX.test(co))){return cf.each(function(cs){var i=cf.eq(cs);if(ce){cm[0]=co.call(this,cs,i.html())}B(i,cm,cq,cg)})}if(ci){cd=t(cm,cf[0].ownerDocument,false,cf,cg);cl=cd.firstChild;if(cd.childNodes.length===1){cd=cl}if(cl||cg){cj=L.map(a(cd,"script"),b1);cn=cj.length;for(;ck<ci;ck++){ch=cd;if(ck!==cr){ch=L.clone(ch,true,true);if(cn){L.merge(cj,a(ch,"script"))}}cq.call(cf[ck],ch,ck)}if(cn){cp=cj[cj.length-1].ownerDocument;L.map(cj,al);for(ck=0;ck<cn;ck++){ch=cj[ck];if(bB.test(ch.type||"")&&!L._data(ch,"globalEval")&&L.contains(cp,ch)){if(ch.src){if(L._evalUrl){L._evalUrl(ch.src)}}else{L.globalEval((ch.text||ch.textContent||ch.innerHTML||"").replace(aX,""))}}}}cd=cl=null}}return cf}function bM(ci,ce,cd){var ch,cf=ce?L.filter(ce,ci):ci,cg=0;for(;(ch=cf[cg])!=null;cg++){if(!cd&&ch.nodeType===1){L.cleanData(a(ch))}if(ch.parentNode){if(cd&&L.contains(ch.ownerDocument,ch)){ac(a(ch,"script"))}ch.parentNode.removeChild(ch)}}return ci}L.extend({htmlPrefilter:function(i){return i.replace(aQ,"<$1></$2>")},clone:function(cg,ch,ck){var ce,cf,cl,ci,cj,cd=L.contains(cg.ownerDocument,cg);if(bQ.html5Clone||L.isXMLDoc(cg)||!bH.test("<"+cg.nodeName+">")){cl=cg.cloneNode(true)}else{k.innerHTML=cg.outerHTML;k.removeChild(cl=k.firstChild)}if((!bQ.noCloneEvent||!bQ.noCloneChecked)&&(cg.nodeType===1||cg.nodeType===11)&&!L.isXMLDoc(cg)){ce=a(cl);cj=a(cg);for(ci=0;(cf=cj[ci])!=null;++ci){if(ce[ci]){S(cf,ce[ci])}}}if(ch){if(ck){cj=cj||a(cg);ce=ce||a(cl);for(ci=0;(cf=cj[ci])!=null;ci++){aC(cf,ce[ci])}}else{aC(cg,cl)}}ce=a(cl,"script");if(ce.length>0){ac(ce,!cd&&a(cg,"script"))}ce=cj=cf=null;return cl},cleanData:function(cg,ce){var ci,cm,ch,cl,ck=0,cn=L.expando,cd=L.cache,cj=bQ.attributes,cf=L.event.special;for(;(ci=cg[ck])!=null;ck++){if(ce||bF(ci)){ch=ci[cn];cl=ch&&cd[ch];if(cl){if(cl.events){for(cm in cl.events){if(cf[cm]){L.event.remove(ci,cm)}else{L.removeEvent(ci,cm,cl.handle)}}}if(cd[ch]){delete cd[ch];if(!cj&&typeof ci.removeAttribute!=="undefined"){ci.removeAttribute(cn)}else{ci[cn]=undefined}aY.push(ch)}}}}}});L.fn.extend({domManip:B,detach:function(i){return bM(this,i,true)},remove:function(i){return bM(this,i)},text:function(i){return a0(this,function(cd){return cd===undefined?L.text(this):this.empty().append((this[0]&&this[0].ownerDocument||m).createTextNode(cd))},null,i,arguments.length)},append:function(){return B(this,arguments,function(i){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var cd=bf(this,i);cd.appendChild(i)}})},prepend:function(){return B(this,arguments,function(i){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var cd=bf(this,i);cd.insertBefore(i,cd.firstChild)}})},before:function(){return B(this,arguments,function(i){if(this.parentNode){this.parentNode.insertBefore(i,this)}})},after:function(){return B(this,arguments,function(i){if(this.parentNode){this.parentNode.insertBefore(i,this.nextSibling)}})},empty:function(){var ce,cd=0;for(;(ce=this[cd])!=null;cd++){if(ce.nodeType===1){L.cleanData(a(ce,false))}while(ce.firstChild){ce.removeChild(ce.firstChild)}if(ce.options&&L.nodeName(ce,"select")){ce.options.length=0}}return this},clone:function(cd,i){cd=cd==null?false:cd;i=i==null?cd:i;return this.map(function(){return L.clone(this,cd,i)})},html:function(i){return a0(this,function(cg){var cf=this[0]||{},ce=0,cd=this.length;if(cg===undefined){return cf.nodeType===1?cf.innerHTML.replace(aN,""):undefined}if(typeof cg==="string"&&!be.test(cg)&&(bQ.htmlSerialize||!bH.test(cg))&&(bQ.leadingWhitespace||!cc.test(cg))&&!W[(ca.exec(cg)||["",""])[1].toLowerCase()]){cg=L.htmlPrefilter(cg);try{for(;ce<cd;ce++){cf=this[ce]||{};if(cf.nodeType===1){L.cleanData(a(cf,false));cf.innerHTML=cg}}cf=0}catch(ch){}}if(cf){this.empty().append(cg)}},null,i,arguments.length)},replaceWith:function(){var i=[];return B(this,arguments,function(ce){var cd=this.parentNode;if(L.inArray(this,i)<0){L.cleanData(a(this));if(cd){cd.replaceChild(ce,this)}}},i)}});L.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(i,cd){L.fn[i]=function(cf){var cg,ci=0,ch=[],ce=L(cf),cj=ce.length-1;for(;ci<=cj;ci++){cg=ci===cj?this:this.clone(true);L(ce[ci])[cd](cg);r.apply(ch,cg.get())}return this.pushStack(ch)}});var aU,ai={HTML:"block",BODY:"block"};function bg(i,cf){var cd=L(cf.createElement(i)).appendTo(cf.body),ce=L.css(cd[0],"display");cd.detach();return ce}function aE(ce){var cd=m,i=ai[ce];if(!i){i=bg(ce,cd);if(i==="none"||!i){aU=(aU||L("<iframe frameborder='0' width='0' height='0'/>")).appendTo(cd.documentElement);cd=(aU[0].contentWindow||aU[0].contentDocument).document;cd.write();cd.close();i=bg(ce,cd);aU.detach()}ai[ce]=i}return i}var bb=(/^margin/);var bx=new RegExp("^("+aP+")(?!px)[a-z%]+$","i");var D=function(ch,cg,ci,cf){var ce,cd,i={};for(cd in cg){i[cd]=ch.style[cd];ch.style[cd]=cg[cd]}ce=ci.apply(ch,cf||[]);for(cd in cg){ch.style[cd]=i[cd]}return ce};var bS=m.documentElement;(function(){var ci,cg,i,ck,cj,cf,ch=m.createElement("div"),cd=m.createElement("div");if(!cd.style){return}cd.style.cssText="float:left;opacity:.5";bQ.opacity=cd.style.opacity==="0.5";bQ.cssFloat=!!cd.style.cssFloat;cd.style.backgroundClip="content-box";cd.cloneNode(true).style.backgroundClip="";bQ.clearCloneStyle=cd.style.backgroundClip==="content-box";ch=m.createElement("div");ch.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute";cd.innerHTML="";ch.appendChild(cd);bQ.boxSizing=cd.style.boxSizing===""||cd.style.MozBoxSizing===""||cd.style.WebkitBoxSizing==="";L.extend(bQ,{reliableHiddenOffsets:function(){if(ci==null){ce()}return ck},boxSizingReliable:function(){if(ci==null){ce()}return i},pixelMarginRight:function(){if(ci==null){ce()}return cg},pixelPosition:function(){if(ci==null){ce()}return ci},reliableMarginRight:function(){if(ci==null){ce()}return cj},reliableMarginLeft:function(){if(ci==null){ce()}return cf}});function ce(){var cm,cl,cn=m.documentElement;cn.appendChild(ch);cd.style.cssText="-webkit-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%";ci=i=cf=false;cg=cj=true;if(ax.getComputedStyle){cl=ax.getComputedStyle(cd);ci=(cl||{}).top!=="1%";cf=(cl||{}).marginLeft==="2px";i=(cl||{width:"4px"}).width==="4px";cd.style.marginRight="50%";cg=(cl||{marginRight:"4px"}).marginRight==="4px";cm=cd.appendChild(m.createElement("div"));cm.style.cssText=cd.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0";cm.style.marginRight=cm.style.width="0";cd.style.width="1px";cj=!parseFloat((ax.getComputedStyle(cm)||{}).marginRight);cd.removeChild(cm)}cd.style.display="none";ck=cd.getClientRects().length===0;if(ck){cd.style.display="";cd.innerHTML="<table><tr><td></td><td>t</td></tr></table>";cd.childNodes[0].style.borderCollapse="separate";cm=cd.getElementsByTagName("td");cm[0].style.cssText="margin:0;border:0;padding:0;display:none";ck=cm[0].offsetHeight===0;if(ck){cm[0].style.display="";cm[1].style.display="none";ck=cm[0].offsetHeight===0}}cn.removeChild(ch)}})();var bt,bP,br=/^(top|right|bottom|left)$/;if(ax.getComputedStyle){bt=function(cd){var i=cd.ownerDocument.defaultView;if(!i||!i.opener){i=ax}return i.getComputedStyle(cd)};bP=function(cj,cf,i){var ch,cd,ci,ce,cg=cj.style;i=i||bt(cj);ce=i?i.getPropertyValue(cf)||i[cf]:undefined;if((ce===""||ce===undefined)&&!L.contains(cj.ownerDocument,cj)){ce=L.style(cj,cf)}if(i){if(!bQ.pixelMarginRight()&&bx.test(ce)&&bb.test(cf)){ch=cg.width;cd=cg.minWidth;ci=cg.maxWidth;cg.minWidth=cg.maxWidth=cg.width=ce;ce=i.width;cg.width=ch;cg.minWidth=cd;cg.maxWidth=ci}}return ce===undefined?ce:ce+""}}else{if(bS.currentStyle){bt=function(i){return i.currentStyle};bP=function(ch,cf,i){var cj,cd,ci,ce,cg=ch.style;i=i||bt(ch);ce=i?i[cf]:undefined;if(ce==null&&cg&&cg[cf]){ce=cg[cf]}if(bx.test(ce)&&!br.test(cf)){cj=cg.left;cd=ch.runtimeStyle;ci=cd&&cd.left;if(ci){cd.left=ch.currentStyle.left}cg.left=cf==="fontSize"?"1em":ce;ce=cg.pixelLeft+"px";cg.left=cj;if(ci){cd.left=ci}}return ce===undefined?ce:ce+""||"auto"}}}function bi(i,cd){return{get:function(){if(i()){delete this.get;return}return(this.get=cd).apply(this,arguments)}}}var aj=/alpha\([^)]*\)/i,aI=/opacity\s*=\s*([^)]*)/i,bL=/^(none|table(?!-c[ea]).+)/,an=new RegExp("^("+aP+")(.*)$","i"),bm={position:"absolute",visibility:"hidden",display:"block"},U={letterSpacing:"0",fontWeight:"400"},a5=["Webkit","O","Moz","ms"],z=m.createElement("div").style;function d(cd){if(cd in z){return cd}var cf=cd.charAt(0).toUpperCase()+cd.slice(1),ce=a5.length;while(ce--){cd=a5[ce]+cf;if(cd in z){return cd}}}function b6(cd,cf){var cj,ci,i,ch=[],cg=0,ce=cd.length;for(;cg<ce;cg++){ci=cd[cg];if(!ci.style){continue}ch[cg]=L._data(ci,"olddisplay");cj=ci.style.display;if(cf){if(!ch[cg]&&cj==="none"){ci.style.display=""}if(ci.style.display===""&&bD(ci)){ch[cg]=L._data(ci,"olddisplay",aE(ci.nodeName))}}else{i=bD(ci);if(cj&&cj!=="none"||!i){L._data(ci,"olddisplay",i?cj:L.css(ci,"display"))}}}for(cg=0;cg<ce;cg++){ci=cd[cg];if(!ci.style){continue}if(!cf||ci.style.display==="none"||ci.style.display===""){ci.style.display=cf?ch[cg]||"":"none"}}return cd}function aW(i,ce,cf){var cd=an.exec(ce);return cd?Math.max(0,cd[1]-(cf||0))+(cd[2]||"px"):ce}function a3(ch,cf,cd,cj,ce){var cg=cd===(cj?"border":"content")?4:cf==="width"?1:0,ci=0;for(;cg<4;cg+=2){if(cd==="margin"){ci+=L.css(ch,cd+bR[cg],true,ce)}if(cj){if(cd==="content"){ci-=L.css(ch,"padding"+bR[cg],true,ce)}if(cd!=="margin"){ci-=L.css(ch,"border"+bR[cg]+"Width",true,ce)}}else{ci+=L.css(ch,"padding"+bR[cg],true,ce);if(cd!=="padding"){ci+=L.css(ch,"border"+bR[cg]+"Width",true,ce)}}}return ci}function b2(cg,ce,i){var cf=true,ch=ce==="width"?cg.offsetWidth:cg.offsetHeight,cd=bt(cg),ci=bQ.boxSizing&&L.css(cg,"boxSizing",false,cd)==="border-box";if(ch<=0||ch==null){ch=bP(cg,ce,cd);if(ch<0||ch==null){ch=cg.style[ce]}if(bx.test(ch)){return ch}cf=ci&&(bQ.boxSizingReliable()||ch===cg.style[ce]);ch=parseFloat(ch)||0}return(ch+a3(cg,ce,i||(ci?"border":"content"),cf,cd))+"px"}L.extend({cssHooks:{opacity:{get:function(ce,i){if(i){var cd=bP(ce,"opacity");return cd===""?"1":cd}}}},cssNumber:{animationIterationCount:true,columnCount:true,fillOpacity:true,flexGrow:true,flexShrink:true,fontWeight:true,lineHeight:true,opacity:true,order:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{"float":bQ.cssFloat?"cssFloat":"styleFloat"},style:function(cf,cd,ck,ce){if(!cf||cf.nodeType===3||cf.nodeType===8||!cf.style){return}var ci,cj,cl,cg=L.camelCase(cd),i=cf.style;cd=L.cssProps[cg]||(L.cssProps[cg]=d(cg)||cg);cl=L.cssHooks[cd]||L.cssHooks[cg];if(ck!==undefined){cj=typeof ck;if(cj==="string"&&(ci=b4.exec(ck))&&ci[1]){ck=s(cf,cd,ci);cj="number"}if(ck==null||ck!==ck){return}if(cj==="number"){ck+=ci&&ci[3]||(L.cssNumber[cg]?"":"px")}if(!bQ.clearCloneStyle&&ck===""&&cd.indexOf("background")===0){i[cd]="inherit"}if(!cl||!("set" in cl)||(ck=cl.set(cf,ck,ce))!==undefined){try{i[cd]=ck}catch(ch){}}}else{if(cl&&"get" in cl&&(ci=cl.get(cf,false,ce))!==undefined){return ci}return i[cd]}},css:function(ci,ch,cd,ce){var cg,cj,i,cf=L.camelCase(ch);ch=L.cssProps[cf]||(L.cssProps[cf]=d(cf)||cf);i=L.cssHooks[ch]||L.cssHooks[cf];if(i&&"get" in i){cj=i.get(ci,true,cd)}if(cj===undefined){cj=bP(ci,ch,ce)}if(cj==="normal"&&ch in U){cj=U[ch]}if(cd===""||cd){cg=parseFloat(cj);return cd===true||isFinite(cg)?cg||0:cj}return cj}});L.each(["height","width"],function(ce,cd){L.cssHooks[cd]={get:function(cg,cf,i){if(cf){return bL.test(L.css(cg,"display"))&&cg.offsetWidth===0?D(cg,bm,function(){return b2(cg,cd,i)}):b2(cg,cd,i)}},set:function(cg,ch,i){var cf=i&&bt(cg);return aW(cg,ch,i?a3(cg,cd,i,bQ.boxSizing&&L.css(cg,"boxSizing",false,cf)==="border-box",cf):0)}}});if(!bQ.opacity){L.cssHooks.opacity={get:function(cd,i){return aI.test((i&&cd.currentStyle?cd.currentStyle.filter:cd.style.filter)||"")?(0.01*parseFloat(RegExp.$1))+"":i?"1":""},set:function(cg,ch){var cf=cg.style,cd=cg.currentStyle,ce=L.isNumeric(ch)?"alpha(opacity="+ch*100+")":"",i=cd&&cd.filter||cf.filter||"";cf.zoom=1;if((ch>=1||ch==="")&&L.trim(i.replace(aj,""))===""&&cf.removeAttribute){cf.removeAttribute("filter");if(ch===""||cd&&!cd.filter){return}}cf.filter=aj.test(i)?i.replace(aj,ce):i+" "+ce}}}L.cssHooks.marginRight=bi(bQ.reliableMarginRight,function(cd,i){if(i){return D(cd,{display:"inline-block"},bP,[cd,"marginRight"])}});L.cssHooks.marginLeft=bi(bQ.reliableMarginLeft,function(cd,i){if(i){return(parseFloat(bP(cd,"marginLeft"))||(L.contains(cd.ownerDocument,cd)?cd.getBoundingClientRect().left-D(cd,{marginLeft:0},function(){return cd.getBoundingClientRect().left}):0))+"px"}});L.each({margin:"",padding:"",border:"Width"},function(i,cd){L.cssHooks[i+cd]={expand:function(cg){var ce=0,cf={},ch=typeof cg==="string"?cg.split(" "):[cg];for(;ce<4;ce++){cf[i+bR[ce]+cd]=ch[ce]||ch[ce-2]||ch[0]}return cf}};if(!bb.test(i)){L.cssHooks[i+cd].set=aW}});L.fn.extend({css:function(i,cd){return a0(this,function(ci,cg,cj){var cf,ce,ck={},ch=0;if(L.isArray(cg)){cf=bt(ci);ce=cg.length;for(;ch<ce;ch++){ck[cg[ch]]=L.css(ci,cg[ch],false,cf)}return ck}return cj!==undefined?L.style(ci,cg,cj):L.css(ci,cg)},i,cd,arguments.length>1)},show:function(){return b6(this,true)},hide:function(){return b6(this)},toggle:function(i){if(typeof i==="boolean"){return i?this.show():this.hide()}return this.each(function(){if(bD(this)){L(this).show()}else{L(this).hide()}})}});function J(ce,cd,cg,i,cf){return new J.prototype.init(ce,cd,cg,i,cf)}L.Tween=J;J.prototype={constructor:J,init:function(cf,cd,ch,i,cg,ce){this.elem=cf;this.prop=ch;this.easing=cg||L.easing._default;this.options=cd;this.start=this.now=this.cur();this.end=i;this.unit=ce||(L.cssNumber[ch]?"":"px")},cur:function(){var i=J.propHooks[this.prop];return i&&i.get?i.get(this):J.propHooks._default.get(this)},run:function(ce){var cd,i=J.propHooks[this.prop];if(this.options.duration){this.pos=cd=L.easing[this.easing](ce,this.options.duration*ce,0,1,this.options.duration)}else{this.pos=cd=ce}this.now=(this.end-this.start)*cd+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(i&&i.set){i.set(this)}else{J.propHooks._default.set(this)}return this}};J.prototype.init.prototype=J.prototype;J.propHooks={_default:{get:function(i){var cd;if(i.elem.nodeType!==1||i.elem[i.prop]!=null&&i.elem.style[i.prop]==null){return i.elem[i.prop]}cd=L.css(i.elem,i.prop,"");return !cd||cd==="auto"?0:cd},set:function(i){if(L.fx.step[i.prop]){L.fx.step[i.prop](i)}else{if(i.elem.nodeType===1&&(i.elem.style[L.cssProps[i.prop]]!=null||L.cssHooks[i.prop])){L.style(i.elem,i.prop,i.now+i.unit)}else{i.elem[i.prop]=i.now}}}}};J.propHooks.scrollTop=J.propHooks.scrollLeft={set:function(i){if(i.elem.nodeType&&i.elem.parentNode){i.elem[i.prop]=i.now}}};L.easing={linear:function(i){return i},swing:function(i){return 0.5-Math.cos(i*Math.PI)/2},_default:"swing"};L.fx=J.prototype.init;L.fx.step={};var N,bl,bO=/^(?:toggle|show|hide)$/,bN=/queueHooks$/;function af(){ax.setTimeout(function(){N=undefined});return(N=L.now())}function bI(cf,ch){var cg,cd={height:cf},ce=0;ch=ch?1:0;for(;ce<4;ce+=2-ch){cg=bR[ce];cd["margin"+cg]=cd["padding"+cg]=cf}if(ch){cd.opacity=cd.width=cf}return cd}function am(cg,ci,cf){var ce,ch=(h.tweeners[ci]||[]).concat(h.tweeners["*"]),cd=0,i=ch.length;for(;cd<i;cd++){if((ce=ch[cd].call(cf,ci,cg))){return ce}}}function j(cg,ck,i){var ce,cn,ch,cr,cq,co,cj,cm,ci=this,cl={},cd=cg.style,cf=cg.nodeType&&bD(cg),cp=L._data(cg,"fxshow");if(!i.queue){cq=L._queueHooks(cg,"fx");if(cq.unqueued==null){cq.unqueued=0;co=cq.empty.fire;cq.empty.fire=function(){if(!cq.unqueued){co()}}}cq.unqueued++;ci.always(function(){ci.always(function(){cq.unqueued--;if(!L.queue(cg,"fx").length){cq.empty.fire()}})})}if(cg.nodeType===1&&("height" in ck||"width" in ck)){i.overflow=[cd.overflow,cd.overflowX,cd.overflowY];cj=L.css(cg,"display");cm=cj==="none"?L._data(cg,"olddisplay")||aE(cg.nodeName):cj;if(cm==="inline"&&L.css(cg,"float")==="none"){if(!bQ.inlineBlockNeedsLayout||aE(cg.nodeName)==="inline"){cd.display="inline-block"}else{cd.zoom=1}}}if(i.overflow){cd.overflow="hidden";if(!bQ.shrinkWrapBlocks()){ci.always(function(){cd.overflow=i.overflow[0];cd.overflowX=i.overflow[1];cd.overflowY=i.overflow[2]})}}for(ce in ck){cn=ck[ce];if(bO.exec(cn)){delete ck[ce];ch=ch||cn==="toggle";if(cn===(cf?"hide":"show")){if(cn==="show"&&cp&&cp[ce]!==undefined){cf=true}else{continue}}cl[ce]=cp&&cp[ce]||L.style(cg,ce)}else{cj=undefined}}if(!L.isEmptyObject(cl)){if(cp){if("hidden" in cp){cf=cp.hidden}}else{cp=L._data(cg,"fxshow",{})}if(ch){cp.hidden=!cf}if(cf){L(cg).show()}else{ci.done(function(){L(cg).hide()})}ci.done(function(){var cs;L._removeData(cg,"fxshow");for(cs in cl){L.style(cg,cs,cl[cs])}});for(ce in cl){cr=am(cf?cp[ce]:0,ce,ci);if(!(ce in cp)){cp[ce]=cr.start;if(cf){cr.end=cr.start;cr.start=ce==="width"||ce==="height"?1:0}}}}else{if((cj==="none"?aE(cg.nodeName):cj)==="inline"){cd.display=cj}}}function bd(cf,ci){var ce,cd,ch,cg,i;for(ce in cf){cd=L.camelCase(ce);ch=ci[cd];cg=cf[ce];if(L.isArray(cg)){ch=cg[1];cg=cf[ce]=cg[0]}if(ce!==cd){cf[cd]=cg;delete cf[ce]}i=L.cssHooks[cd];if(i&&"expand" in i){cg=i.expand(cg);delete cf[cd];for(ce in cg){if(!(ce in cf)){cf[ce]=cg[ce];ci[ce]=ch}}}else{ci[cd]=ch}}}function h(cd,i,ck){var cl,ci,ch=0,cg=h.prefilters.length,cm=L.Deferred().always(function(){delete cf.elem}),cf=function(){if(ci){return false}var cs=N||af(),cr=Math.max(0,ce.startTime+ce.duration-cs),cp=cr/ce.duration||0,co=1-cp,cq=0,cn=ce.tweens.length;for(;cq<cn;cq++){ce.tweens[cq].run(co)}cm.notifyWith(cd,[ce,co,cr]);if(co<1&&cn){return cr}else{cm.resolveWith(cd,[ce]);return false}},ce=cm.promise({elem:cd,props:L.extend({},i),opts:L.extend(true,{specialEasing:{},easing:L.easing._default},ck),originalProperties:i,originalOptions:ck,startTime:N||af(),duration:ck.duration,tweens:[],createTween:function(cp,cn){var co=L.Tween(cd,ce.opts,cp,cn,ce.opts.specialEasing[cp]||ce.opts.easing);ce.tweens.push(co);return co},stop:function(cp){var co=0,cn=cp?ce.tweens.length:0;if(ci){return this}ci=true;for(;co<cn;co++){ce.tweens[co].run(1)}if(cp){cm.notifyWith(cd,[ce,1,0]);cm.resolveWith(cd,[ce,cp])}else{cm.rejectWith(cd,[ce,cp])}return this}}),cj=ce.props;bd(cj,ce.opts.specialEasing);for(;ch<cg;ch++){cl=h.prefilters[ch].call(ce,cd,cj,ce.opts);if(cl){if(L.isFunction(cl.stop)){L._queueHooks(ce.elem,ce.opts.queue).stop=L.proxy(cl.stop,cl)}return cl}}L.map(cj,am,ce);if(L.isFunction(ce.opts.start)){ce.opts.start.call(cd,ce)}L.fx.timer(L.extend(cf,{elem:cd,anim:ce,queue:ce.opts.queue}));return ce.progress(ce.opts.progress).done(ce.opts.done,ce.opts.complete).fail(ce.opts.fail).always(ce.opts.always)}L.Animation=L.extend(h,{tweeners:{"*":[function(ce,cd){var i=this.createTween(ce,cd);s(i.elem,ce,b4.exec(cd),i);return i}]},tweener:function(ce,cf){if(L.isFunction(ce)){cf=ce;ce=["*"]}else{ce=ce.match(aO)}var cg,cd=0,i=ce.length;for(;cd<i;cd++){cg=ce[cd];h.tweeners[cg]=h.tweeners[cg]||[];h.tweeners[cg].unshift(cf)}},prefilters:[j],prefilter:function(cd,i){if(i){h.prefilters.unshift(cd)}else{h.prefilters.push(cd)}}});L.speed=function(ce,cf,cd){var i=ce&&typeof ce==="object"?L.extend({},ce):{complete:cd||!cd&&cf||L.isFunction(ce)&&ce,duration:ce,easing:cd&&cf||cf&&!L.isFunction(cf)&&cf};i.duration=L.fx.off?0:typeof i.duration==="number"?i.duration:i.duration in L.fx.speeds?L.fx.speeds[i.duration]:L.fx.speeds._default;if(i.queue==null||i.queue===true){i.queue="fx"}i.old=i.complete;i.complete=function(){if(L.isFunction(i.old)){i.old.call(this)}if(i.queue){L.dequeue(this,i.queue)}};return i};L.fn.extend({fadeTo:function(i,cf,ce,cd){return this.filter(bD).css("opacity",0).show().end().animate({opacity:cf},i,ce,cd)},animate:function(ci,cf,ch,cg){var ce=L.isEmptyObject(ci),cd=L.speed(cf,ch,cg),i=function(){var cj=h(this,L.extend({},ci),cd);if(ce||L._data(this,"finish")){cj.stop(true)}};i.finish=i;return ce||cd.queue===false?this.each(i):this.queue(cd.queue,i)},stop:function(ce,i,cd){var cf=function(cg){var ch=cg.stop;delete cg.stop;ch(cd)};if(typeof ce!=="string"){cd=i;i=ce;ce=undefined}if(i&&ce!==false){this.queue(ce||"fx",[])}return this.each(function(){var cj=true,ch=ce!=null&&ce+"queueHooks",cg=L.timers,ci=L._data(this);if(ch){if(ci[ch]&&ci[ch].stop){cf(ci[ch])}}else{for(ch in ci){if(ci[ch]&&ci[ch].stop&&bN.test(ch)){cf(ci[ch])}}}for(ch=cg.length;ch--;){if(cg[ch].elem===this&&(ce==null||cg[ch].queue===ce)){cg[ch].anim.stop(cd);cj=false;cg.splice(ch,1)}}if(cj||!cd){L.dequeue(this,ce)}})},finish:function(i){if(i!==false){i=i||"fx"}return this.each(function(){var ch,ci=L._data(this),cf=ci[i+"queue"],ce=ci[i+"queueHooks"],cd=L.timers,cg=cf?cf.length:0;ci.finish=true;L.queue(this,i,[]);if(ce&&ce.stop){ce.stop.call(this,true)}for(ch=cd.length;ch--;){if(cd[ch].elem===this&&cd[ch].queue===i){cd[ch].anim.stop(true);cd.splice(ch,1)}}for(ch=0;ch<cg;ch++){if(cf[ch]&&cf[ch].finish){cf[ch].finish.call(this)}}delete ci.finish})}});L.each(["toggle","show","hide"],function(ce,cd){var cf=L.fn[cd];L.fn[cd]=function(i,ch,cg){return i==null||typeof i==="boolean"?cf.apply(this,arguments):this.animate(bI(cd,true),i,ch,cg)}});L.each({slideDown:bI("show"),slideUp:bI("hide"),slideToggle:bI("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(i,cd){L.fn[i]=function(ce,cg,cf){return this.animate(cd,ce,cg,cf)}});L.timers=[];L.fx.tick=function(){var cf,cd=L.timers,ce=0;N=L.now();for(;ce<cd.length;ce++){cf=cd[ce];if(!cf()&&cd[ce]===cf){cd.splice(ce--,1)}}if(!cd.length){L.fx.stop()}N=undefined};L.fx.timer=function(i){L.timers.push(i);if(i()){L.fx.start()}else{L.timers.pop()}};L.fx.interval=13;L.fx.start=function(){if(!bl){bl=ax.setInterval(L.fx.tick,L.fx.interval)}};L.fx.stop=function(){ax.clearInterval(bl);bl=null};L.fx.speeds={slow:600,fast:200,_default:400};L.fn.delay=function(cd,i){cd=L.fx?L.fx.speeds[cd]||cd:cd;i=i||"fx";return this.queue(i,function(cg,cf){var ce=ax.setTimeout(cg,cd);cf.stop=function(){ax.clearTimeout(ce)}})};(function(){var i,cd=m.createElement("input"),cg=m.createElement("div"),cf=m.createElement("select"),ce=cf.appendChild(m.createElement("option"));cg=m.createElement("div");cg.setAttribute("className","t");cg.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>";i=cg.getElementsByTagName("a")[0];cd.setAttribute("type","checkbox");cg.appendChild(cd);i=cg.getElementsByTagName("a")[0];i.style.cssText="top:1px";bQ.getSetAttribute=cg.className!=="t";bQ.style=/top/.test(i.getAttribute("style"));bQ.hrefNormalized=i.getAttribute("href")==="/a";bQ.checkOn=!!cd.value;bQ.optSelected=ce.selected;bQ.enctype=!!m.createElement("form").enctype;cf.disabled=true;bQ.optDisabled=!ce.disabled;cd=m.createElement("input");cd.setAttribute("value","");bQ.input=cd.getAttribute("value")==="";cd.value="t";cd.setAttribute("type","radio");bQ.radioValue=cd.value==="t"})();var ay=/\r/g,a7=/[\x20\t\r\n\f]+/g;L.fn.extend({val:function(cf){var i,cd,cg,ce=this[0];if(!arguments.length){if(ce){i=L.valHooks[ce.type]||L.valHooks[ce.nodeName.toLowerCase()];if(i&&"get" in i&&(cd=i.get(ce,"value"))!==undefined){return cd}cd=ce.value;return typeof cd==="string"?cd.replace(ay,""):cd==null?"":cd}return}cg=L.isFunction(cf);return this.each(function(ch){var ci;if(this.nodeType!==1){return}if(cg){ci=cf.call(this,ch,L(this).val())}else{ci=cf}if(ci==null){ci=""}else{if(typeof ci==="number"){ci+=""}else{if(L.isArray(ci)){ci=L.map(ci,function(cj){return cj==null?"":cj+""})}}}i=L.valHooks[this.type]||L.valHooks[this.nodeName.toLowerCase()];if(!i||!("set" in i)||i.set(this,ci,"value")===undefined){this.value=ci}})}});L.extend({valHooks:{option:{get:function(i){var cd=L.find.attr(i,"value");return cd!=null?cd:L.trim(L.text(i)).replace(a7," ")}},select:{get:function(ce){var ck,cd,cj=ce.options,ch=ce.selectedIndex,cg=ce.type==="select-one"||ch<0,cl=cg?null:[],ci=cg?ch+1:cj.length,cf=ch<0?ci:cg?ch:0;for(;cf<ci;cf++){cd=cj[cf];if((cd.selected||cf===ch)&&(bQ.optDisabled?!cd.disabled:cd.getAttribute("disabled")===null)&&(!cd.parentNode.disabled||!L.nodeName(cd.parentNode,"optgroup"))){ck=L(cd).val();if(cg){return ck}cl.push(ck)}}return cl},set:function(ci,cj){var ck,cd,cg=ci.options,ch=L.makeArray(cj),cf=cg.length;while(cf--){cd=cg[cf];if(L.inArray(L.valHooks.option.get(cd),ch)>-1){try{cd.selected=ck=true}catch(ce){cd.scrollHeight}}else{cd.selected=false}}if(!ck){ci.selectedIndex=-1}return cg}}}});L.each(["radio","checkbox"],function(){L.valHooks[this]={set:function(i,cd){if(L.isArray(cd)){return(i.checked=L.inArray(L(i).val(),cd)>-1)}}};if(!bQ.checkOn){L.valHooks[this].get=function(i){return i.getAttribute("value")===null?"on":i.value}}});var bk,b9,C=L.expr.attrHandle,aA=/^(?:checked|selected)$/i,E=bQ.getSetAttribute,Q=bQ.input;L.fn.extend({attr:function(i,cd){return a0(this,L.attr,i,cd,arguments.length>1)},removeAttr:function(i){return this.each(function(){L.removeAttr(this,i)})}});L.extend({attr:function(cg,cf,ch){var ce,i,cd=cg.nodeType;if(cd===3||cd===8||cd===2){return}if(typeof cg.getAttribute==="undefined"){return L.prop(cg,cf,ch)}if(cd!==1||!L.isXMLDoc(cg)){cf=cf.toLowerCase();i=L.attrHooks[cf]||(L.expr.match.bool.test(cf)?b9:bk)}if(ch!==undefined){if(ch===null){L.removeAttr(cg,cf);return}if(i&&"set" in i&&(ce=i.set(cg,ch,cf))!==undefined){return ce}cg.setAttribute(cf,ch+"");return ch}if(i&&"get" in i&&(ce=i.get(cg,cf))!==null){return ce}ce=L.find.attr(cg,cf);return ce==null?undefined:ce},attrHooks:{type:{set:function(i,cd){if(!bQ.radioValue&&cd==="radio"&&L.nodeName(i,"input")){var ce=i.value;i.setAttribute("type",cd);if(ce){i.value=ce}return cd}}}},removeAttr:function(cg,ch){var ce,cd,cf=0,ci=ch&&ch.match(aO);if(ci&&cg.nodeType===1){while((ce=ci[cf++])){cd=L.propFix[ce]||ce;if(L.expr.match.bool.test(ce)){if(Q&&E||!aA.test(ce)){cg[cd]=false}else{cg[L.camelCase("default-"+ce)]=cg[cd]=false}}else{L.attr(cg,ce,"")}cg.removeAttribute(E?ce:cd)}}}});b9={set:function(cd,ce,i){if(ce===false){L.removeAttr(cd,i)}else{if(Q&&E||!aA.test(i)){cd.setAttribute(!E&&L.propFix[i]||i,i)}else{cd[L.camelCase("default-"+i)]=cd[i]=true}}return i}};L.each(L.expr.match.bool.source.match(/\w+/g),function(ce,cd){var cf=C[cd]||L.find.attr;if(Q&&E||!aA.test(cd)){C[cd]=function(ci,ch,cj){var cg,i;if(!cj){i=C[ch];C[ch]=cg;cg=cf(ci,ch,cj)!=null?ch.toLowerCase():null;C[ch]=i}return cg}}else{C[cd]=function(cg,i,ch){if(!ch){return cg[L.camelCase("default-"+i)]?i.toLowerCase():null}}}});if(!Q||!E){L.attrHooks.value={set:function(cd,ce,i){if(L.nodeName(cd,"input")){cd.defaultValue=ce}else{return bk&&bk.set(cd,ce,i)}}}}if(!E){bk={set:function(ce,cf,cd){var i=ce.getAttributeNode(cd);if(!i){ce.setAttributeNode((i=ce.ownerDocument.createAttribute(cd)))}i.value=cf+="";if(cd==="value"||cf===ce.getAttribute(cd)){return cf}}};C.id=C.name=C.coords=function(ce,cd,cf){var i;if(!cf){return(i=ce.getAttributeNode(cd))&&i.value!==""?i.value:null}};L.valHooks.button={get:function(ce,cd){var i=ce.getAttributeNode(cd);if(i&&i.specified){return i.value}},set:bk.set};L.attrHooks.contenteditable={set:function(cd,ce,i){bk.set(cd,ce===""?false:ce,i)}};L.each(["width","height"],function(ce,cd){L.attrHooks[cd]={set:function(i,cf){if(cf===""){i.setAttribute(cd,"auto");return cf}}}})}if(!bQ.style){L.attrHooks.style={get:function(i){return i.style.cssText||undefined},set:function(i,cd){return(i.style.cssText=cd+"")}}}var aR=/^(?:input|select|textarea|button|object)$/i,x=/^(?:a|area)$/i;L.fn.extend({prop:function(i,cd){return a0(this,L.prop,i,cd,arguments.length>1)},removeProp:function(i){i=L.propFix[i]||i;return this.each(function(){try{this[i]=undefined;delete this[i]}catch(cd){}})}});L.extend({prop:function(cg,cf,ch){var ce,i,cd=cg.nodeType;if(cd===3||cd===8||cd===2){return}if(cd!==1||!L.isXMLDoc(cg)){cf=L.propFix[cf]||cf;i=L.propHooks[cf]}if(ch!==undefined){if(i&&"set" in i&&(ce=i.set(cg,ch,cf))!==undefined){return ce}return(cg[cf]=ch)}if(i&&"get" in i&&(ce=i.get(cg,cf))!==null){return ce}return cg[cf]},propHooks:{tabIndex:{get:function(cd){var i=L.find.attr(cd,"tabindex");return i?parseInt(i,10):aR.test(cd.nodeName)||x.test(cd.nodeName)&&cd.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});if(!bQ.hrefNormalized){L.each(["href","src"],function(ce,cd){L.propHooks[cd]={get:function(i){return i.getAttribute(cd,4)}}})}if(!bQ.optSelected){L.propHooks.selected={get:function(cd){var i=cd.parentNode;if(i){i.selectedIndex;if(i.parentNode){i.parentNode.selectedIndex}}return null},set:function(cd){var i=cd.parentNode;if(i){i.selectedIndex;if(i.parentNode){i.parentNode.selectedIndex}}}}}L.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){L.propFix[this.toLowerCase()]=this});if(!bQ.enctype){L.propFix.enctype="encoding"}var H=/[\t\r\n\f]/g;function u(i){return L.attr(i,"class")||""}L.fn.extend({addClass:function(ck){var ce,cd,cl,cf,ci,cg,cj,ch=0;if(L.isFunction(ck)){return this.each(function(i){L(this).addClass(ck.call(this,i,u(this)))})}if(typeof ck==="string"&&ck){ce=ck.match(aO)||[];while((cd=this[ch++])){cf=u(cd);cl=cd.nodeType===1&&(" "+cf+" ").replace(H," ");if(cl){cg=0;while((ci=ce[cg++])){if(cl.indexOf(" "+ci+" ")<0){cl+=ci+" "}}cj=L.trim(cl);if(cf!==cj){L.attr(cd,"class",cj)}}}}return this},removeClass:function(ck){var ce,cd,cl,cf,ci,cg,cj,ch=0;if(L.isFunction(ck)){return this.each(function(i){L(this).removeClass(ck.call(this,i,u(this)))})}if(!arguments.length){return this.attr("class","")}if(typeof ck==="string"&&ck){ce=ck.match(aO)||[];while((cd=this[ch++])){cf=u(cd);cl=cd.nodeType===1&&(" "+cf+" ").replace(H," ");if(cl){cg=0;while((ci=ce[cg++])){while(cl.indexOf(" "+ci+" ")>-1){cl=cl.replace(" "+ci+" "," ")}}cj=L.trim(cl);if(cf!==cj){L.attr(cd,"class",cj)}}}}return this},toggleClass:function(ce,i){var cd=typeof ce;if(typeof i==="boolean"&&cd==="string"){return i?this.addClass(ce):this.removeClass(ce)}if(L.isFunction(ce)){return this.each(function(cf){L(this).toggleClass(ce.call(this,cf,u(this),i),i)})}return this.each(function(){var cg,ch,cf,ci;if(cd==="string"){ch=0;cf=L(this);ci=ce.match(aO)||[];while((cg=ci[ch++])){if(cf.hasClass(cg)){cf.removeClass(cg)}else{cf.addClass(cg)}}}else{if(ce===undefined||cd==="boolean"){cg=u(this);if(cg){L._data(this,"__className__",cg)}L.attr(this,"class",cg||ce===false?"":L._data(this,"__className__")||"")}}})},hasClass:function(cd){var ce,cg,cf=0;ce=" "+cd+" ";while((cg=this[cf++])){if(cg.nodeType===1&&(" "+u(cg)+" ").replace(H," ").indexOf(ce)>-1){return true}}return false}});L.each(("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu").split(" "),function(ce,cd){L.fn[cd]=function(cf,i){return arguments.length>0?this.on(cd,null,cf,i):this.trigger(cd)}});L.fn.extend({hover:function(i,cd){return this.mouseenter(i).mouseleave(cd||i)}});var aZ=ax.location;var bs=L.now();var A=(/\?/);var bc=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;L.parseJSON=function(i){if(ax.JSON&&ax.JSON.parse){return ax.JSON.parse(i+"")}var cd,cf=null,ce=L.trim(i+"");return ce&&!L.trim(ce.replace(bc,function(ci,cg,ch,cj){if(cd&&cg){cf=0}if(cf===0){return ci}cd=ch||cg;cf+=!cj-!ch;return""}))?(Function("return "+ce))():L.error("Invalid JSON: "+i)};L.parseXML=function(ce){var i,cd;if(!ce||typeof ce!=="string"){return null}try{if(ax.DOMParser){cd=new ax.DOMParser();i=cd.parseFromString(ce,"text/xml")}else{i=new ax.ActiveXObject("Microsoft.XMLDOM");i.async="false";i.loadXML(ce)}}catch(cf){i=undefined}if(!i||!i.documentElement||i.getElementsByTagName("parsererror").length){L.error("Invalid XML: "+ce)}return i};var aB=/#.*$/,T=/([?&])_=[^&]*/,ar=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,v=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,p=/^(?:GET|HEAD)$/,aT=/^\/\//,a6=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,b0={},au={},a8="*/".concat("*"),ae=aZ.href,cb=a6.exec(ae.toLowerCase())||[];function K(i){return function(cf,ch){if(typeof cf!=="string"){ch=cf;cf="*"}var ce,cg=0,cd=cf.toLowerCase().match(aO)||[];if(L.isFunction(ch)){while((ce=cd[cg++])){if(ce.charAt(0)==="+"){ce=ce.slice(1)||"*";(i[ce]=i[ce]||[]).unshift(ch)}else{(i[ce]=i[ce]||[]).push(ch)}}}}}function b7(i,ce,cg,cf){var cd={},ch=(i===au);function ci(cj){var ck;cd[cj]=true;L.each(i[cj]||[],function(cm,cl){var cn=cl(ce,cg,cf);if(typeof cn==="string"&&!ch&&!cd[cn]){ce.dataTypes.unshift(cn);ci(cn);return false}else{if(ch){return !(ck=cn)}}});return ck}return ci(ce.dataTypes[0])||!cd["*"]&&ci("*")}function b3(cg,cf){var i,cd,ce=L.ajaxSettings.flatOptions||{};for(cd in cf){if(cf[cd]!==undefined){(ce[cd]?cg:(i||(i={})))[cd]=cf[cd]}}if(i){L.extend(true,cg,i)}return cg}function g(ck,ci,i){var cf,cg,cd,ch,ce=ck.contents,cj=ck.dataTypes;while(cj[0]==="*"){cj.shift();if(cg===undefined){cg=ck.mimeType||ci.getResponseHeader("Content-Type")}}if(cg){for(ch in ce){if(ce[ch]&&ce[ch].test(cg)){cj.unshift(ch);break}}}if(cj[0] in i){cd=cj[0]}else{for(ch in i){if(!cj[0]||ck.converters[ch+" "+cj[0]]){cd=ch;break}if(!cf){cf=ch}}cd=cd||cf}if(cd){if(cd!==cj[0]){cj.unshift(cd)}return i[cd]}}function aq(cn,cf,cj,i){var cd,ci,ck,cg,ce,cm={},cl=cn.dataTypes.slice();if(cl[1]){for(ck in cn.converters){cm[ck.toLowerCase()]=cn.converters[ck]}}ci=cl.shift();while(ci){if(cn.responseFields[ci]){cj[cn.responseFields[ci]]=cf}if(!ce&&i&&cn.dataFilter){cf=cn.dataFilter(cf,cn.dataType)}ce=ci;ci=cl.shift();if(ci){if(ci==="*"){ci=ce}else{if(ce!=="*"&&ce!==ci){ck=cm[ce+" "+ci]||cm["* "+ci];if(!ck){for(cd in cm){cg=cd.split(" ");if(cg[1]===ci){ck=cm[ce+" "+cg[0]]||cm["* "+cg[0]];if(ck){if(ck===true){ck=cm[cd]}else{if(cm[cd]!==true){ci=cg[0];cl.unshift(cg[1])}}break}}}}if(ck!==true){if(ck&&cn["throws"]){cf=ck(cf)}else{try{cf=ck(cf)}catch(ch){return{state:"parsererror",error:ck?ch:"No conversion from "+ce+" to "+ci}}}}}}}}return{state:"success",data:cf}}L.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ae,type:"GET",isLocal:v.test(cb[1]),global:true,processData:true,async:true,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":a8,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":true,"text json":L.parseJSON,"text xml":L.parseXML},flatOptions:{url:true,context:true}},ajaxSetup:function(cd,i){return i?b3(b3(cd,L.ajaxSettings),i):b3(L.ajaxSettings,cd)},ajaxPrefilter:K(b0),ajaxTransport:K(au),ajax:function(cf,ch){if(typeof cf==="object"){ch=cf;cf=undefined}ch=ch||{};var cq,cs,cg,cm,cn,ck,ct,cd,cl=L.ajaxSetup({},ch),cy=cl.context||cl,cx=cl.context&&(cy.nodeType||cy.jquery)?L(cy):L.event,cz=L.Deferred(),cv=L.Callbacks("once memory"),ci=cl.statusCode||{},cw={},cp={},ce=0,cj="canceled",cr={readyState:0,getResponseHeader:function(cA){var i;if(ce===2){if(!cd){cd={};while((i=ar.exec(cm))){cd[i[1].toLowerCase()]=i[2]}}i=cd[cA.toLowerCase()]}return i==null?null:i},getAllResponseHeaders:function(){return ce===2?cm:null},setRequestHeader:function(cA,cB){var i=cA.toLowerCase();if(!ce){cA=cp[i]=cp[i]||cA;cw[cA]=cB}return this},overrideMimeType:function(i){if(!ce){cl.mimeType=i}return this},statusCode:function(cA){var i;if(cA){if(ce<2){for(i in cA){ci[i]=[ci[i],cA[i]]}}else{cr.always(cA[cr.status])}}return this},abort:function(cA){var i=cA||cj;if(ct){ct.abort(i)}co(0,i);return this}};cz.promise(cr).complete=cv.add;cr.success=cr.done;cr.error=cr.fail;cl.url=((cf||cl.url||ae)+"").replace(aB,"").replace(aT,cb[1]+"//");cl.type=ch.method||ch.type||cl.method||cl.type;cl.dataTypes=L.trim(cl.dataType||"*").toLowerCase().match(aO)||[""];if(cl.crossDomain==null){cq=a6.exec(cl.url.toLowerCase());cl.crossDomain=!!(cq&&(cq[1]!==cb[1]||cq[2]!==cb[2]||(cq[3]||(cq[1]==="http:"?"80":"443"))!==(cb[3]||(cb[1]==="http:"?"80":"443"))))}if(cl.data&&cl.processData&&typeof cl.data!=="string"){cl.data=L.param(cl.data,cl.traditional)}b7(b0,cl,ch,cr);if(ce===2){return cr}ck=L.event&&cl.global;if(ck&&L.active++===0){L.event.trigger("ajaxStart")}cl.type=cl.type.toUpperCase();cl.hasContent=!p.test(cl.type);cl.url+=(A.test(cl.url)?"&":"?")+"_TOKEN="+$("#_TOKEN").attr("value");cg=cl.url;if(!cl.hasContent){if(cl.data){cg=(cl.url+=(A.test(cg)?"&":"?")+cl.data);delete cl.data}if(cl.cache===false){cl.url=T.test(cg)?cg.replace(T,"$1_="+bs++):cg+(A.test(cg)?"&":"?")+"_="+bs++}}if(cl.ifModified){if(L.lastModified[cg]){cr.setRequestHeader("If-Modified-Since",L.lastModified[cg])}if(L.etag[cg]){cr.setRequestHeader("If-None-Match",L.etag[cg])}}if(cl.data&&cl.hasContent&&cl.contentType!==false||ch.contentType){cr.setRequestHeader("Content-Type",cl.contentType)}cr.setRequestHeader("Accept",cl.dataTypes[0]&&cl.accepts[cl.dataTypes[0]]?cl.accepts[cl.dataTypes[0]]+(cl.dataTypes[0]!=="*"?", "+a8+"; q=0.01":""):cl.accepts["*"]);for(cs in cl.headers){cr.setRequestHeader(cs,cl.headers[cs])}if(cl.beforeSend&&(cl.beforeSend.call(cy,cr,cl)===false||ce===2)){return cr.abort()}cj="abort";for(cs in {success:1,error:1,complete:1}){cr[cs](cl[cs])}ct=b7(au,cl,ch,cr);if(!ct){co(-1,"No Transport")}else{cr.readyState=1;if(ck){cx.trigger("ajaxSend",[cr,cl])}if(ce===2){return cr}if(cl.async&&cl.timeout>0){cn=ax.setTimeout(function(){cr.abort("timeout")},cl.timeout)}try{ce=1;ct.send(cw,co)}catch(cu){if(ce<2){co(-1,cu)}else{throw cu}}}function co(cD,cF,cA,cC){var i,cH,cG,cE,cI,cB=cF;if(ce===2){return}ce=2;if(cn){ax.clearTimeout(cn)}ct=undefined;cm=cC||"";cr.readyState=cD>0?4:0;i=cD>=200&&cD<300||cD===304;if(cA){cE=g(cl,cr,cA)}cE=aq(cl,cE,cr,i);if(i){if(cl.ifModified){cI=cr.getResponseHeader("Last-Modified");if(cI){L.lastModified[cg]=cI}cI=cr.getResponseHeader("etag");if(cI){L.etag[cg]=cI}}if(cD===204||cl.type==="HEAD"){cB="nocontent"}else{if(cD===304){cB="notmodified"}else{cB=cE.state;cH=cE.data;cG=cE.error;i=!cG}}}else{cG=cB;if(cD||!cB){cB="error";if(cD<0){cD=0}}}cr.status=cD;cr.statusText=(cF||cB)+"";if(i){cz.resolveWith(cy,[cH,cB,cr])}else{cz.rejectWith(cy,[cr,cB,cG])}cr.statusCode(ci);ci=undefined;if(ck){cx.trigger(i?"ajaxSuccess":"ajaxError",[cr,cl,i?cH:cG])}cv.fireWith(cy,[cr,cB]);if(ck){cx.trigger("ajaxComplete",[cr,cl]);if(!(--L.active)){L.event.trigger("ajaxStop")}}}return cr},getJSON:function(i,cd,ce){return L.get(i,cd,ce,"json")},getScript:function(i,cd){return L.get(i,undefined,cd,"script")}});L.each(["get","post"],function(cd,ce){L[ce]=function(i,cg,ch,cf){if(L.isFunction(cg)){cf=cf||ch;ch=cg;cg=undefined}return L.ajax(L.extend({url:i,type:ce,dataType:cf,data:cg,success:ch},L.isPlainObject(i)&&i))}});L._evalUrl=function(i){return L.ajax({url:i,type:"GET",dataType:"script",cache:true,async:false,global:false,"throws":true})};L.fn.extend({wrapAll:function(i){if(L.isFunction(i)){return this.each(function(ce){L(this).wrapAll(i.call(this,ce))})}if(this[0]){var cd=L(i,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){cd.insertBefore(this[0])}cd.map(function(){var ce=this;while(ce.firstChild&&ce.firstChild.nodeType===1){ce=ce.firstChild}return ce}).append(this)}return this},wrapInner:function(i){if(L.isFunction(i)){return this.each(function(cd){L(this).wrapInner(i.call(this,cd))})}return this.each(function(){var ce=L(this),cd=ce.contents();if(cd.length){cd.wrapAll(i)}else{ce.append(i)}})},wrap:function(i){var cd=L.isFunction(i);return this.each(function(ce){L(this).wrapAll(cd?i.call(this,ce):i)})},unwrap:function(){return this.parent().each(function(){if(!L.nodeName(this,"body")){L(this).replaceWith(this.childNodes)}}).end()}});function bK(i){return i.style&&i.style.display||L.css(i,"display")}function G(i){if(!L.contains(i.ownerDocument||m,i)){return true}while(i&&i.nodeType===1){if(bK(i)==="none"||i.type==="hidden"){return true}i=i.parentNode}return false}L.expr.filters.hidden=function(i){return bQ.reliableHiddenOffsets()?(i.offsetWidth<=0&&i.offsetHeight<=0&&!i.getClientRects().length):G(i)};L.expr.filters.visible=function(i){return !L.expr.filters.hidden(i)};var by=/%20/g,a2=/\[\]$/,X=/\r?\n/g,l=/^(?:submit|button|image|reset|file)$/i,aF=/^(?:input|select|textarea|keygen)/i;function e(i,cg,cd,cf){var ce;if(L.isArray(cg)){L.each(cg,function(ci,ch){if(cd||a2.test(i)){cf(i,ch)}else{e(i+"["+(typeof ch==="object"&&ch!=null?ci:"")+"]",ch,cd,cf)}})}else{if(!cd&&L.type(cg)==="object"){for(ce in cg){e(i+"["+ce+"]",cg[ce],cd,cf)}}else{cf(i,cg)}}}L.param=function(ce,cd){var i,cf=[],cg=function(ch,ci){ci=L.isFunction(ci)?ci():(ci==null?"":ci);cf[cf.length]=encodeURIComponent(ch)+"="+encodeURIComponent(ci)};if(cd===undefined){cd=L.ajaxSettings&&L.ajaxSettings.traditional}if(L.isArray(ce)||(ce.jquery&&!L.isPlainObject(ce))){L.each(ce,function(){cg(this.name,this.value)})}else{for(i in ce){e(i,ce[i],cd,cg)}}return cf.join("&").replace(by,"+")};L.fn.extend({serialize:function(){return L.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var i=L.prop(this,"elements");return i?L.makeArray(i):this}).filter(function(){var i=this.type;return this.name&&!L(this).is(":disabled")&&aF.test(this.nodeName)&&!l.test(i)&&(this.checked||!aV.test(i))}).map(function(cd,ce){var cf=L(this).val();return cf==null?null:L.isArray(cf)?L.map(cf,function(i){return{name:ce.name,value:i.replace(X,"\r\n")}}):{name:ce.name,value:cf.replace(X,"\r\n")}}).get()}});L.ajaxSettings.xhr=ax.ActiveXObject!==undefined?function(){if(this.isLocal){return ak()}if(m.documentMode>8){return R()}return/^(get|post|head|put|delete|options)$/i.test(this.type)&&R()||ak()}:R;var aL=0,aw={},aJ=L.ajaxSettings.xhr();if(ax.attachEvent){ax.attachEvent("onunload",function(){for(var i in aw){aw[i](undefined,true)}})}bQ.cors=!!aJ&&("withCredentials" in aJ);aJ=bQ.ajax=!!aJ;if(aJ){L.ajaxTransport(function(i){if(!i.crossDomain||bQ.cors){var cd;return{send:function(ch,cf){var ce,cg=i.xhr(),ci=++aL;cg.open(i.type,i.url,i.async,i.username,i.password);if(i.xhrFields){for(ce in i.xhrFields){cg[ce]=i.xhrFields[ce]}}if(i.mimeType&&cg.overrideMimeType){cg.overrideMimeType(i.mimeType)}if(!i.crossDomain&&!ch["X-Requested-With"]){ch["X-Requested-With"]="XMLHttpRequest"}for(ce in ch){if(ch[ce]!==undefined){cg.setRequestHeader(ce,ch[ce]+"")}}cg.send((i.hasContent&&i.data)||null);cd=function(cl,ck){var cm,co,cj;if(cd&&(ck||cg.readyState===4)){delete aw[ci];cd=undefined;cg.onreadystatechange=L.noop;if(ck){if(cg.readyState!==4){cg.abort()}}else{cj={};cm=cg.status;if(typeof cg.responseText==="string"){cj.text=cg.responseText}try{co=cg.statusText}catch(cn){co=""}if(!cm&&i.isLocal&&!i.crossDomain){cm=cj.text?200:404}else{if(cm===1223){cm=204}}}}if(cj){cf(cm,co,cj,cg.getAllResponseHeaders())}};if(!i.async){cd()}else{if(cg.readyState===4){ax.setTimeout(cd)}else{cg.onreadystatechange=aw[ci]=cd}}},abort:function(){if(cd){cd(undefined,true)}}}}})}function R(){try{return new ax.XMLHttpRequest()}catch(i){}}function ak(){try{return new ax.ActiveXObject("Microsoft.XMLHTTP")}catch(i){}}L.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(i){L.globalEval(i);return i}}});L.ajaxPrefilter("script",function(i){if(i.cache===undefined){i.cache=false}if(i.crossDomain){i.type="GET";i.global=false}});L.ajaxTransport("script",function(ce){if(ce.crossDomain){var cd,i=m.head||L("head")[0]||m.documentElement;return{send:function(cf,cg){cd=m.createElement("script");cd.async=true;if(ce.scriptCharset){cd.charset=ce.scriptCharset}cd.src=ce.url;cd.onload=cd.onreadystatechange=function(ci,ch){if(ch||!cd.readyState||/loaded|complete/.test(cd.readyState)){cd.onload=cd.onreadystatechange=null;if(cd.parentNode){cd.parentNode.removeChild(cd)}cd=null;if(!ch){cg(200,"success")}}};i.insertBefore(cd,i.firstChild)},abort:function(){if(cd){cd.onload(undefined,true)}}}}});var ad=[],at=/(=)\?(?=&|$)|\?\?/;L.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var i=ad.pop()||(L.expando+"_"+(bs++));this[i]=true;return i}});L.ajaxPrefilter("json jsonp",function(cf,i,cg){var ch,cd,ce,ci=cf.jsonp!==false&&(at.test(cf.url)?"url":typeof cf.data==="string"&&(cf.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&at.test(cf.data)&&"data");if(ci||cf.dataTypes[0]==="jsonp"){ch=cf.jsonpCallback=L.isFunction(cf.jsonpCallback)?cf.jsonpCallback():cf.jsonpCallback;if(ci){cf[ci]=cf[ci].replace(at,"$1"+ch)}else{if(cf.jsonp!==false){cf.url+=(A.test(cf.url)?"&":"?")+cf.jsonp+"="+ch}}cf.converters["script json"]=function(){if(!ce){L.error(ch+" was not called")}return ce[0]};cf.dataTypes[0]="json";cd=ax[ch];ax[ch]=function(){ce=arguments};cg.always(function(){if(cd===undefined){L(ax).removeProp(ch)}else{ax[ch]=cd}if(cf[ch]){cf.jsonpCallback=i.jsonpCallback;ad.push(ch)}if(ce&&L.isFunction(cd)){cd(ce[0])}ce=cd=undefined});return"script"}});L.parseHTML=function(cg,cd,cf){if(!cg||typeof cg!=="string"){return null}if(typeof cd==="boolean"){cf=cd;cd=false}cd=cd||m;var ce=c.exec(cg),i=!cf&&[];if(ce){return[cd.createElement(ce[1])]}ce=t([cg],cd,i);if(i&&i.length){L(i).remove()}return L.merge([],ce.childNodes)};var b8=L.fn.load;L.fn.load=function(ce,cj,ch){if(typeof ce!=="string"&&b8){return b8.apply(this,arguments)}var i,cg,cf,cd=this,ci=ce.indexOf(" ");if(ci>-1){i=L.trim(ce.slice(ci,ce.length));ce=ce.slice(0,ci)}if(L.isFunction(cj)){ch=cj;cj=undefined}else{if(cj&&typeof cj==="object"){cg="POST"}}if(cd.length>0){L.ajax({url:ce,type:cg||"GET",dataType:"html",data:cj}).done(function(ck){cf=arguments;cd.html(i?L("<div>").append(L.parseHTML(ck)).find(i):ck)}).always(ch&&function(cl,ck){cd.each(function(){ch.apply(this,cf||[cl.responseText,ck,cl])})})}return this};L.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(cd,ce){L.fn[ce]=function(i){return this.on(ce,i)}});L.expr.filters.animated=function(i){return L.grep(L.timers,function(cd){return i===cd.elem}).length};function bu(i){return L.isWindow(i)?i:i.nodeType===9?i.defaultView||i.parentWindow:false}L.offset={setOffset:function(cd,co,cg){var cj,cf,cm,ci,ck,cp,cn,ch=L.css(cd,"position"),ce=L(cd),cl={};if(ch==="static"){cd.style.position="relative"}ck=ce.offset();cm=L.css(cd,"top");cp=L.css(cd,"left");cn=(ch==="absolute"||ch==="fixed")&&L.inArray("auto",[cm,cp])>-1;if(cn){cj=ce.position();ci=cj.top;cf=cj.left}else{ci=parseFloat(cm)||0;cf=parseFloat(cp)||0}if(L.isFunction(co)){co=co.call(cd,cg,L.extend({},ck))}if(co.top!=null){cl.top=(co.top-ck.top)+ci}if(co.left!=null){cl.left=(co.left-ck.left)+cf}if("using" in co){co.using.call(cd,cl)}else{ce.css(cl)}}};L.fn.extend({offset:function(cd){if(arguments.length){return cd===undefined?this:this.each(function(ci){L.offset.setOffset(this,cd,ci)})}var i,ch,cf={top:0,left:0},ce=this[0],cg=ce&&ce.ownerDocument;if(!cg){return}i=cg.documentElement;if(!L.contains(i,ce)){return cf}if(typeof ce.getBoundingClientRect!=="undefined"){cf=ce.getBoundingClientRect()}ch=bu(cg);return{top:cf.top+(ch.pageYOffset||i.scrollTop)-(i.clientTop||0),left:cf.left+(ch.pageXOffset||i.scrollLeft)-(i.clientLeft||0)}},position:function(){if(!this[0]){return}var ce,cf,i={top:0,left:0},cd=this[0];if(L.css(cd,"position")==="fixed"){cf=cd.getBoundingClientRect()}else{ce=this.offsetParent();cf=this.offset();if(!L.nodeName(ce[0],"html")){i=ce.offset()}i.top+=L.css(ce[0],"borderTopWidth",true);i.left+=L.css(ce[0],"borderLeftWidth",true)}return{top:cf.top-i.top-L.css(cd,"marginTop",true),left:cf.left-i.left-L.css(cd,"marginLeft",true)}},offsetParent:function(){return this.map(function(){var i=this.offsetParent;while(i&&(!L.nodeName(i,"html")&&L.css(i,"position")==="static")){i=i.offsetParent}return i||bS})}});L.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(cd,ce){var i=/Y/.test(ce);L.fn[cd]=function(cf){return a0(this,function(cg,ci,cj){var ch=bu(cg);if(cj===undefined){return ch?(ce in ch)?ch[ce]:ch.document.documentElement[ci]:cg[ci]}if(ch){ch.scrollTo(!i?cj:L(ch).scrollLeft(),i?cj:L(ch).scrollTop())}else{cg[ci]=cj}},cd,cf,arguments.length,null)}});L.each(["top","left"],function(cd,ce){L.cssHooks[ce]=bi(bQ.pixelPosition,function(cf,i){if(i){i=bP(cf,ce);return bx.test(i)?L(cf).position()[ce]+"px":i}})});L.each({Height:"height",Width:"width"},function(i,cd){L.each({padding:"inner"+i,content:cd,"":"outer"+i},function(ce,cf){L.fn[cf]=function(ch,cj){var ci=arguments.length&&(ce||typeof ch!=="boolean"),cg=ce||(ch===true||cj===true?"margin":"border");return a0(this,function(cl,ck,cm){var cn;if(L.isWindow(cl)){return cl.document.documentElement["client"+i]}if(cl.nodeType===9){cn=cl.documentElement;return Math.max(cl.body["scroll"+i],cn["scroll"+i],cl.body["offset"+i],cn["offset"+i],cn["client"+i])}return cm===undefined?L.css(cl,ck,cg):L.style(cl,ck,cm,cg)},cd,ci?ch:undefined,ci,null)}})});L.fn.extend({bind:function(i,ce,cd){return this.on(i,null,ce,cd)},unbind:function(i,cd){return this.off(i,null,cd)},delegate:function(i,cd,cf,ce){return this.on(cd,i,cf,ce)},undelegate:function(i,cd,ce){return arguments.length===1?this.off(i,"**"):this.off(cd,i||"**",ce)}});L.fn.size=function(){return this.length};L.fn.andSelf=L.fn.addBack;if(typeof define==="function"&&define.amd){define("jquery",[],function(){return L})}var bq=ax.jQuery,I=ax.$;L.noConflict=function(i){if(ax.$===L){ax.$=I}if(i&&ax.jQuery===L){ax.jQuery=bq}return L};if(!aG){ax.jQuery=ax.$=L}return L}));