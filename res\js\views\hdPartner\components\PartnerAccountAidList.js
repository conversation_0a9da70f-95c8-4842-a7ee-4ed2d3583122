export default {
	template: `
		<div style="margin: 10px 0 0 20px;">
			<el-form>
				<el-form-item label="公司名称" label-position="left">
					<el-select v-model="type" placeholder="请选择" size="medium" style="width: 300px;" @change="getBindAcctByType">
						<el-option v-for="(item,index) in partnerAcctList" :key="index" :label="item.companyName" :value="item.type"></el-option>
					</el-select>
				</el-form-item>
			</el-form>
			<el-table :data="partnerAccountAidList" stripe border max-height="720" style="width: 600px;margin-top: 30px;">
				<el-table-column v-for="(column,index) in tableColumns" :key="index" :prop="column.prop" :label="column.label"></el-table-column>
			</el-table>
			<el-pagination @size-change="sizeChange" @current-change="currentChanage" layout="total,sizes,prev,pager,next,jumper" :total="totalSize" :page-sizes="[10,50,100,500]" :page-size="limit" :current-page="page" style="margin-top: 30px;"></el-pagination>
		</div>
	`,
	data(){
		return {
			tableColumns: [
				{
					prop: 'company',
					label: '公司名称'
				},
				{
					prop: 'aid',
					label: 'aid'
				},
				{
					prop: 'account',
					label: '账号'
				}
			],
			partnerAcctList: [],
			partnerAccountAidList: [],
			totalSize: 0,
			type: '',
			limit: 10,
			page: 1
		};
	},
	created(){
		Vue.http.post(
			'/ajax/hdPartnerAcct_h.jsp?cmd=getPartnerAcctList',
			{limit: -1, page: -1},
			{emulateJSON: true}
		).then(({data}) => {
			this.partnerAcctList = data.list;
		});
	},
	methods: {
		sizeChange(limit){
			this.limit = limit;
			this.getBindAcctByType();
		},
		currentChanage(page){
			this.page = page;
			this.getBindAcctByType();
		},
		getBindAcctByType(){
			Vue.http.post(
				'/ajax/hdPartnerAcct_h.jsp?cmd=getBindAcctByType',
				{type: this.type, limit: this.limit, page: this.page},
				{emulateJSON: true}
			).then(({data}) => {
				this.partnerAccountAidList = data.list;
				this.totalSize = data.totalSize;
			});
		}
	}
} 