<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>HD销售月度业绩统计</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body>
	<div id="hdsale-meritStatistics">
		<!--页面顶部标题-->
		<div style="text-align: center;">
			互动销售月度业绩统计
		</div>
		<!-- <h4 style="text-align: center;">销售月度业绩统计</h4>
		<a href="payRecord.jsp" style="float:left;">返回</a> -->

		<div id="fai-meritStatistics-month">
			<!--查询条件 start-->
			<el-checkbox label="全部" v-model="allCheck"></el-checkbox>
			<el-checkbox v-for="item in tableList" v-if="item.prop != 'name'" :label="item.label" v-model="item.show" :key="item.prop"></el-checkbox>
			<br/>
			<el-checkbox v-for="item in tableList2" :label="item.label" v-model="item.show" :key="item.prop"></el-checkbox>
			<div class="fai-month-search" v-cloak>
				<%--<div><span style="color:red;">{{month.form.year}}-{{month.form.month}}月</span>的实际消费情况</div>--%>
				<el-form :inline="true" :model="month.form" class="demo-form-inline" size="mini">
				    <el-form-item label="">
						<el-select v-model="month.form.year" filterable>
							<el-option v-for="year in month.yearArr" :label="year.value" :value="year.value" :key="year.id"></el-option>
						</el-select>年
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="month.form.month" filterable>
							<el-option v-for="month in month.monthArr" :label="month.value" :value="month.value" :key="month.id"></el-option>
						</el-select>月
					</el-form-item>
					<el-form-item label="">&nbsp;&nbsp;</el-form-item>
					<el-form-item label="排序规则">
						<el-select v-model="month.form.sort" filterable>
							<el-option label="OSS后台总金额" value="0"></el-option>
<%--							<el-option label="首购金额" value="1"></el-option>--%>
<%--							<el-option label="重购金额" value="2"></el-option>--%>
							<el-option label="总业绩" value="8"></el-option>
							<el-option label="完成率" value="9"></el-option>
							<el-option label="当月arpu" value="3"></el-option>
							<%--<el-option label="前月arpu" value="4"></el-option>--%>
							<%--<el-option label="当月和前月arpu" value="5"></el-option>--%>
							<el-option label="非当月arpu" value="6"></el-option>
							<el-option label="总arpu" value="7"></el-option>
							<el-option label="走单业绩" value="10"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item>
						<el-button icon="el-icon-search" type="primary" @click="monthOnSubmit">查询</el-button>
						<el-button icon="el-icon-download" type=""  @click="monthExportExcel">导出</el-button>
						<el-button type="primary"  @click="submitGoalMoney">录入业绩</el-button>
						<!-- TODO -->
						<!-- <a target="_blank" v-bind:href="month.oldAndNewUrl" style="margin-left: 10px;">新旧账号关联</a> -->
					</el-form-item>
				</el-form>
			</div>
			<!--查询条件 end-->
			<!--数据表格 start-->
			<div class="fai-month-list" v-cloak>
				<el-table :data="month.tableData" row-key="rowkey" stripe border max-height="400" style="width: 100%">
					<%--<el-table-column label="排名" type="index" width="70px"  align="center"></el-table-column><!--fixed:固定-->
					<el-table-column label="咨询师" prop="name" align="center"></el-table-column>
					<el-table-column label="首购金额" prop="firstPrice" align="center"></el-table-column>
					<el-table-column label="首购人数" prop="firstNum" width="140px" align="center"></el-table-column>
					<el-table-column label="重购金额" prop="repeatPrice" width="120px" align="center"></el-table-column>
					<el-table-column label="重购人数" prop="repeatNum" align="center"></el-table-column>
					<el-table-column label="成单数" prop="orderCount" align="center"></el-table-column>
					<el-table-column label="总人数" prop="totalNum" align="center"></el-table-column>
					<el-table-column label="总金额" prop="totalPrice" align="center"></el-table-column>

					<el-table-column label="平均分配总金额" prop="commonPrice" align="center"></el-table-column>

					<el-table-column label="当月arpu" prop="arpu" align="center">
						<template slot-scope="scope">
							<a v-if="scope.row.status == 1 && scope.row.arpu < averageNowMonArpu" style="color: red">{{scope.row.arpu}}</a>
							<a v-else style="color: #444444">{{scope.row.arpu}}</a>
						</template>
					</el-table-column>


					<el-table-column label="非当月arpu" prop="nonNowArpu" align="center">
						<template slot-scope="scope">
							<a v-if="scope.row.status == 1 && scope.row.nonNowArpu < averageNotNowMonArpu" style="color: red">{{scope.row.nonNowArpu}}</a>
							<a v-else style="color: #444444">{{scope.row.nonNowArpu}}</a>
						</template>
					</el-table-column>
					<el-table-column label="本月总arpu" prop="arpuTotal" align="center"></el-table-column>
					<el-table-column label="上月支付本月退款" prop="refundPrice" align="center"></el-table-column>
					<el-table-column label="上月支付本月退款单数" prop="refundCount" align="center"></el-table-column>--%>
					<el-table-column label="排名" type="index" width="70px"  align="center"></el-table-column>
					<el-table-column v-for="item in tableList"  v-if="item.show" :label="item.label" :prop="item.prop"  align="center" :key="item.prop"></el-table-column>
					<el-table-column v-for="item in tableList2"  v-if="item.show" :label="item.label" :prop="item.prop"  align="center" :key="item.prop"></el-table-column>
				</el-table>
			</div>
			<!--数据表格 end-->
		</div>
		<!-- 下面是周的统计 -->

<hr><hr>
		<div id="fai-meritStatistics-week">
			<!--查询条件 start-->
			<div class="fai-week-search" v-cloak>
			<div><span style="color:red;">周一到周日</span>的实际消费情况(业绩前{{week.form.rank}}名)</div>
				<el-form :inline="true" :model="week.form" class="demo-form-inline" size="mini">
					<el-form-item label="时间">
						<el-date-picker class="fai-date" v-model="week.form.periodDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="week.editable" :clearable="week.clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="week.form.periodDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="week.editable" :clearable="week.clearable"></el-date-picker>
					</el-form-item>
					<!-- <el-form-item label="排名">
						<el-select v-model="week.form.rank" filterable>
							<el-option v-for="rank in week.rankArr" :label="rank.value" :value="rank.value" :key="rank.id"></el-option>
						</el-select>
					</el-form-item> -->
					<el-form-item label="排序规则">
						<el-select v-model="week.form.sort" filterable>
							<el-option label="OSS后台总金额" value="0"></el-option>
							<el-option label="首购金额" value="1"></el-option>
							<el-option label="重购金额" value="2"></el-option>
							<el-option label="当月领取当周arpu" value="3"></el-option>
							<%--<el-option label="前月领取当周arpu" value="4"></el-option>--%>
							<%--<el-option label="当月和前月领取当周arpu" value="5"></el-option>--%>
							<el-option label="非当月领取当周arpu" value="6"></el-option>
							<el-option label="总arpu" value="7"></el-option>
						</el-select>
					</el-form-item>

					<el-form-item>
						<el-button type="" @click="beforeWeek">上一周</el-button>
						<el-button type="" @click="nextWeek">下一周</el-button>
						(<span style="color:red;">周时间可以自己定义，默认周一至周日</span>)
					</el-form-item>
					
					<el-form-item>
						<el-button icon="el-icon-search" type="primary" @click="weekOnSubmit">查询</el-button>
						<el-button icon="el-icon-download" type=""  @click="weekExportExcel">导出</el-button>
						<!-- TODO -->
						<!-- <a target="_blank" v-bind:href="week.oldAndNewUrl" style="margin-left: 10px;">新旧账号关联</a> -->
					</el-form-item>
				</el-form>
			</div>
			<!--查询条件 end-->
			<!--数据表格 start-->
			<div class="fai-week-list" v-cloak>
				<el-table :data="week.tableData" row-key="rowkey" stripe border max-height="350" >
					<el-table-column label="排名" type="index" width="70px" align="center"></el-table-column><!--fixed:固定-->
					<el-table-column label="咨询师" prop="name" align="center"></el-table-column>
					<el-table-column label="首购金额" prop="firstPrice" align="center" ></el-table-column>
					<el-table-column label="首购人数" prop="firstNum" align="center"></el-table-column>
					<el-table-column label="重购金额" prop="repeatPrice" align="center"></el-table-column>
					<el-table-column label="重购人数" prop="repeatNum" align="center"></el-table-column>
					<el-table-column label="成单数" prop="orderCount" align="center"></el-table-column>					
					<el-table-column label="总人数" prop="totalNum" align="center"></el-table-column>
					<el-table-column label="总金额" prop="totalPrice" align="center"></el-table-column>

					<el-table-column label="当月领取当周成交额" prop="nowTotalPrice" align="center"></el-table-column>
					<el-table-column label="当月领取数" prop="personCount" align="center"></el-table-column>
					<el-table-column label="当月领取当周arpu" prop="arpu"  width="90px" align="center"></el-table-column>
					<%--<el-table-column label="前月领取当周成交额" prop="preTotalPrice" align="center"></el-table-column>--%>
					<el-table-column label="非当月领取当周成交额" prop="nonNowTotalPrice" align="center"></el-table-column>
					<%--<el-table-column label="前月领取数" prop="prePersonCount" align="center"></el-table-column>--%>
					<el-table-column label="非当月领取数" prop="nonNowPersonCount" align="center"></el-table-column>

					<%--<el-table-column label="前月领取当周arpu" prop="preArpu" width="90px" align="center"></el-table-column>--%>
					<%--<el-table-column label="当月和前月领取当周arpu" width="105px" prop="nowAndPreArpu" align="center"></el-table-column>--%>
					<el-table-column label="非当月领取当周arpu" prop="nonNowArpu" align="center"></el-table-column>
					<el-table-column label="本周总arpu" prop="arpuTotal" align="center"></el-table-column>
				</tr>
				</el-table>
			</div>
			<!--数据表格 end-->
		</div>
	</div>
	</body>
	<script type="text/javascript">

		var meritStatisticsVM = new Vue({
			el: '#hdsale-meritStatistics',
			data: {
			    averageNowMonArpu: '',
				averageNotNowMonArpu: '',
				month: {
					form: {//这里是为了填充默认值
						year:"",
						month:"",
						sort:"0",
						exportFlag: false,
					},
					yearArr:[],
					monthArr:[],
					tableData: [],
					oldAndNewUrl:'',

				},
				allCheck: false,
				tableList: [
					{ show: true, prop: 'name', label: '销售'},
					{ show: true, prop: 'allMoney', label: '总业绩'},
					{ show: true, prop: 'goalMoney', label: '目标业绩'},
					{ show: true, prop: 'remainMoney', label: '还差业绩'},
					{ show: true, prop: 'goalPercentStr', label: '完成率'},
					{ show: true, prop: 'totalPrice', label: 'OSS后台总金额'},
					{ show: true, prop: 'refundPrice', label: '上月支付本月退款金额'},
					{ show: true, prop: 'approveMoney', label: '报备业绩'},
					{ show: true, prop: 'arpu', label: '当月arpu'},
					{ show: true, prop: 'nonNowArpu', label: '非当月arpu'},
					{ show: true, prop: 'arpuTotal', label: '本月总arpu'},
					{ show: true, prop: 'twoYearCount', label: '2年单数'},
				],
				tableList2: [
					{ show: false, prop: 'commonPrice', label: '平均分配总金额'},
					{ show: false, prop: 'orderCount', label: '成单量'},
					{ show: false, prop: 'refundCount', label: '上月支付本月退款单数'},
					{ show: false, prop: 'approveCount', label: '报备单数'},
					{ show: false, prop: 'achievement', label: '走单业绩'},
					{ show: false, prop: 'orderNum', label: '走单量'},
					{ show: false, prop: 'HD', label: '互动'},
					{ show: false, prop: 'PRE_FLYER', label: '传单'},
					{ show: false, prop: 'MP', label: '助手'},
					{ show: false, prop: 'SITE', label: '建站'},
					{ show: false, prop: 'MALL', label: '商城'},
					{ show: false, prop: 'QZ', label: '轻站'},
					{ show: false, prop: 'DJ', label: '代建'},
					{ show: false, prop: 'YUEKE', label: '悦客'},
					{ show: false, prop: 'firstNum', label: '首购人数'},
					{ show: false, prop: 'firstPrice', label: '首购金额'},
					{ show: false, prop: 'repeatNum', label: '重购人数'},
					{ show: false, prop: 'repeatPrice', label: '重购金额'}
				],

				week: {
					form: {//这里是为了填充默认值
						periodDateBeg:"",
						periodDateEnd:"",
						// rank:"7",
						sort:"0",
						exportFlag: false,
					},
					// rankArr: [{"value":7}],
					tableData: [],
					editable: false,
					clearable: false,
					oldAndNewUrl:'',
				},

			},
			created:function(){
				var nowDate = new Date();
				for (var year = 2016; year <= nowDate.getFullYear(); year++) {
					this.month.yearArr.push({"value":year});
				}
				for(var month = 1; month <= 12; month ++){
					this.month.monthArr.push({"value":month});
				}
				this.month.form.year = nowDate.getFullYear();
				this.month.form.month = nowDate.getMonth() + 1;
				this.getMonthDataList(this.month.form);

				var days = nowDate.getDay()==0 ? 7 : nowDate.getDay();//因为周日为0，转变为7
				this.week.form.periodDateBeg =  Fai.tool.dateFormatter(new Date(nowDate.getTime() - 3600 * 24 * (days-1) * 1000));
				this.week.form.periodDateEnd = Fai.tool.dateFormatter(new Date(nowDate.getTime() + 3600 * 24 * (7-days) * 1000));
				this.getWeekDataList(this.week.form);

			},
			watch:{
				allCheck: function(val,oldval){
					if(val){
						for(let i=0; i<this.tableList.length; i++){
							if(this.tableList[i].prop == 'name'){
								continue;
							}
							this.tableList[i].show = true;
						}
						for(let i=0; i<this.tableList2.length; i++){
							this.tableList2[i].show = true;
						}
					}else{
						for(let i=0; i<this.tableList.length; i++){
							if(this.tableList[i].prop == 'name'){
								continue;
							}
							this.tableList[i].show = false;
						}
						for(let i=0; i<this.tableList2.length; i++){
							this.tableList2[i].show = false;
						}
					}
				}
			},
	        updated:function(){
			},
			methods: {
				monthOnSubmit() {
					this.getMonthDataList(this.month.form);
				},
				monthExportExcel(){
					this.month.form.exportFlag = true;
					window.open("/ajax/hdSale_h.jsp?cmd=getMonthMeritStatisticsList" + Fai.tool.parseJsonToUrlParam(this.month.form));
					this.month.form.exportFlag = false;
				},

				weekOnSubmit() {
					this.getWeekDataList(this.week.form);
				},
				beforeWeek() {
					var end = new Date(this.week.form.periodDateBeg).getTime() - 3600*24*1*1000;
					var start = end - 3600*24*6*1000;
					this.week.form.periodDateBeg =  Fai.tool.dateFormatter(start)
					this.week.form.periodDateEnd = Fai.tool.dateFormatter(end);
					this.getWeekDataList(this.week.form);
				},
				nextWeek() {
					var start = new Date(this.week.form.periodDateEnd).getTime() + 3600*24*1*1000;
					var end = start + 3600*24*6*1000;
					this.week.form.periodDateBeg =  Fai.tool.dateFormatter(start)
					this.week.form.periodDateEnd = Fai.tool.dateFormatter(end);
					this.getWeekDataList(this.week.form);
				},
				weekExportExcel(){
					this.week.form.exportFlag = true;
					window.open("/ajax/hdSale_h.jsp?cmd=getWeekMeritStatisticsList" + Fai.tool.parseJsonToUrlParam(this.week.form));
					this.week.form.exportFlag = false;
				},

				//获取月统计数据
				getMonthDataList(urlParam){
					console.log(urlParam);
					Fai.http.post("hdSale_h.jsp?cmd=getMonthMeritStatisticsList", urlParam, false).then(result => {
					console.log(result);
						if(result.success){
							this.month.tableData = result.dataList;
							//TODO
							//this.month.oldAndNewUrl=result.oldAndNewUrl;
						}
                        for(var j = 0,len = result.dataList.length; j < len; j++){
							if(result.dataList[j].name === '销售组'){
                                this.averageNowMonArpu = result.dataList[j].arpu;
                                this.averageNotNowMonArpu = result.dataList[j].nonNowArpu;
							}
                        }
					});
				},
				//获取周统计数据
				getWeekDataList(urlParam){
					console.log(urlParam);
					Fai.http.post("hdSale_h.jsp?cmd=getWeekMeritStatisticsList", urlParam, false).then(result => {
					console.log(result);
						if(result.success){
							this.week.tableData = result.dataList;
							//TODO
							//this.week.oldAndNewUrl=result.oldAndNewUrl;
							// if(result.preSalesCnt > 7 && this.week.rankArr.length == 1){
							// 	for(var i = 1; i < result.preSalesCnt-7; i++){
							// 		this.week.rankArr.push({"value":i+7});
							// 	}
							// }
						}
					});
				},

				//录入业绩
				submitGoalMoney(){
					window.open("goalMoney.jsp");
				}

		    }
		});
	</script>

</html>



