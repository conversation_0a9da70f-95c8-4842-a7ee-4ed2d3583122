<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="fai.app.HdOssStaffDef" %>
<%@ page import="fai.webhdoss.app.HdOssResDef" %>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%--
  销售业务能力认证
  Created by Jachin.
  Date: 2019/11/21
  Time: 10:05
--%>
<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){out.println("没有权限");return;}%>
<html>
<head>
    <title>能力认证</title>
    <script src="http://code.jquery.com/jquery-1.7.0.js"></script>
    <link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_element")%>" />
    <%@ include file="/comm/script.jsp.inc" %>

    <style>
        body{
            /*padding: 10px;*/
            font-size: 14px;
            margin-left: 20px;
        }
        /*滚动条样式*/
        ::-webkit-scrollbar{
            width: 6px;
            height: 6px;
            background-color: #f1f1f1;
        }
        /*滑块样式*/
        ::-webkit-scrollbar-thumb{
            background-color: #b2b2b2;
            width: 16px;
            -webkit-border-radius: 16px;
            -moz-border-radius: 16px;
            border-radius: 16px;
        }
    </style>
</head>

<body>

    <div class="certificate">
        <div>
            <el-row style="padding: 20px 0 0 0;">
                <span style="font-size: 16px">已通过悦客能力认证的销售</span>
            </el-row>
            <el-row style="padding: 20px 0 0 0;">
                <el-button type="primary" size="mini" @click="addSaleCertificate('yk')">添加销售</el-button>
            </el-row>
            <el-row style="padding: 20px 0 0 0;">
                <div style="width: 300px">
                    <el-table
                            :data="ykList"
                            :header-row-style="{'font-size': '14px'}"
                            :header-cell-style="{'text-align':'center'}"
                            :row-style="{'font-size':'12px'}"
                            :cell-style="{'padding':'5px 0','text-align':'center'}"
                            tooltip-effect="light"
                            stripe
                            border
                            size="small"
                            max-height="400">
                        <el-table-column prop="nickName" label="销售"></el-table-column>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-button type="text" @click="removeCertificatedSale(scope.row, 'yk')"><u>删除</u></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-row>
        </div>

        <el-dialog class="dialog" :title="dialogTitle" :visible.sync="dialogVisible" width="600px" :before-close="handleClose" :close-on-click-modal="false">
                <div class="scroll">
                    <el-form size="mini">
                        <el-form-item :label="item.label" v-for="(item, index) in form.otherSaleList" :key="index">
                            <br>
                            <el-checkbox-group v-model="form.saleList" @change="handleCheckBoxChange">
                                <el-checkbox style="width: 140px;margin-left: 10px" name="sidBox" :value="sale.sid" :data-value="sale.sid" :label="sale.nickName" v-for="(sale, index2) in item.list" :key="index2"></el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-form>
                </div>
                <el-footer>

                    <el-form size="mini" inline="">
                        <el-form-item style="text-align: center">
                            <el-button @click="doAddSaleCertificate" type="primary">确定</el-button>
                            <el-button @click="handleClose">取消</el-button>
                        </el-form-item>
                    </el-form>
                </el-footer>
                <div class="footer">
                </div>
            </el-dialog>

    </div>
</body>

<script>
    new Vue({
        el: '.certificate',
        data: {
            ykList: [],
            dialogTitle: "能力认证",
            dialogVisible: false,
            business: "",
            form: {
                otherSaleList: [],
                saleList: [],
                checkSidList: []
            }
        },
        created: function (){
            this.getCertificatedSaleList();
        },
        methods: {
            // 添加销售按钮
            addSaleCertificate: function (business) {
                this.business = business;
                if (business == "yk") {
                    this.dialogTitle = "悦客能力认证"
                }
                this.getOtherSale();
            },

            // 能力认证弹窗关闭
            handleClose: function () {
                this.business = "";
                this.dialogVisible = false;
                this.form.saleList = [];
                this.form.checkSidList = [];
            },
            doAddSaleCertificate: function () {
                var that = this;
                console.log(that.form);
                $.ajax({
                    type: "get",
                    url: '/ajax/ykSale/saleCertificate_h.jsp?cmd=addCertificateSale',
                    data: {
                        sidList: JSON.stringify(that.form.checkSidList),
                        business: that.business
                    },
                    dataType: 'json',
                    error: function () {
                        that.$message.error("服务繁忙，请稍候重试");
                        return false;
                    },
                    success: function (result) {
                        if (result.success) {
                            that.$message.success("添加成功");
                            that.getCertificatedSaleList();
                            that.handleClose();
                        } else {
                            that.$message.error(result.msg);
                        }
                    }
                });
            },

            // 获取其他销售
            getOtherSale: function () {
                var that = this;
                $.ajax({
                    type: "get",
                    url: '/ajax/ykSale/saleCertificate_h.jsp?cmd=getOtherSale',
                    data: {
                        business: that.business
                    },
                    dataType: 'json',
                    error: function () {
                        that.$message.error("服务繁忙，请稍候重试");
                        return false;
                    },
                    success: function (result) {
                        if (result.success) {
                            that.form.otherSaleList = result.data;
                            that.dialogVisible = true;
                        } else {
                            that.$message.error(result.msg);
                        }
                    }
                });
            },

            // 选择销售时，将label值转换为value值
            handleCheckBoxChange: function () {
                // 选中之后，获取value数字
                var text = $("input:checkbox[name='sidBox']:checked").map(function(index,elem) {
                    return Number($(elem).parents(".el-checkbox").attr("data-value"));
                }).get();
                console.log("选中的checkbox的值为："+text);
                this.form.checkSidList = text;
                console.log(this.form.checkSidList);
            },

            getCertificatedSaleList: function () {
                var that = this;
                $.ajax({
                    type: "get",
                    url: '/ajax/ykSale/saleCertificate_h.jsp?cmd=getCertificatedSaleList',
                    data: {
                        business: that.business
                    },
                    dataType: 'json',
                    error: function () {
                        that.$message.error("服务繁忙，请稍候重试");
                        return false;
                    },
                    success: function (result) {
                        if (result.success) {
                            that.ykList = result.data.ykList;
                        } else {
                            that.$message.error(result.msg);
                        }
                    }
                });
            },

            // 移除通过认证的销售
            removeCertificatedSale: function (saleInfo, business) {
                console.log(saleInfo);
                if (!confirm("确定移除认证销售？")) {
                    return;
                }
                var sid = saleInfo.sid;
                if (sid < 1) {
                    this.$message.warning('销售sid错误');
                    return;
                }
                if (business < 1) {
                    this.$message.warning('业务错误');
                    return;
                }
                var that = this;
                $.ajax({
                    type: "get",
                    url: '/ajax/ykSale/saleCertificate_h.jsp?cmd=removeCertificatedSale',
                    data: {
                        sid: sid,
                        business: business
                    },
                    dataType: 'json',
                    error: function () {
                        that.$message.error("服务繁忙，请稍候重试");
                        return false;
                    },
                    success: function (result) {
                        if (result.success) {
                            that.$message.success("删除成功");
                            that.getCertificatedSaleList();
                        } else {
                            that.$message.error(result.msg);
                        }
                    }
                });
            }
        }
    })
</script>

</html>