package fai.webhdoss.advice.aop;


import fai.comm.util.Log;
import fai.hdUtil.JsonResult;
import org.apache.tomcat.util.http.Parameters;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.aop.ProxyMethodInvocation;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 * 将 json流请求的参数一个个set进 ParamterMap中
 * 参考文档 https://blog.csdn.net/tuantuanyua/article/details/81354914
 *         https://train.faisco.biz/wp87wZ
 */

@Component
@Aspect
public class Audience {

    // 将 @RequestBody 注解 的bean 的值设置到  RequestAttribute 上
    private void setBody2RequestAttribute(Object argBean) throws Exception {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();  //获取request 可以从中获取参数或cookie

        Map<String, Object> mapfromBean = JsonResult.bean2map(argBean);//将json流的bean 转为 map

        Map map = request.getParameterMap();
        if ("java.util.Collections$UnmodifiableMap".equals(map.getClass().getName())) {
            // 使用 java.util.Collections$UnmodifiableMap 来做map接受Paramters的是 resin
            if (field2Map4Resion == null) {
                synchronized (this) {
                    if (field2Map4Resion == null) {
                        Field field2Map4ResionTmp = map.getClass().getDeclaredField("m");
                        field2Map4ResionTmp.setAccessible(true);
                        field2Map4Resion = field2Map4ResionTmp;
                    }
                }
            }
            Map mapModify = (Map) field2Map4Resion.get(map);


            for(Map.Entry<String, Object> entry : mapfromBean.entrySet()) {
                if (entry.getValue() != null) {
                    mapModify.put(entry.getKey(), new String[]{String.valueOf(entry.getValue())});
                }
            }

        } else if ("org.apache.catalina.util.ParameterMap".equals(map.getClass().getName())) {
            // tomcat使用的是 org.apache.catalina.util.ParameterMap
            if (field2Map4Tomcat == null) {
                synchronized (this) {
                    if (field2Map4Tomcat == null) {
                        initTomcatField(request);
                        Field field2Map4TomcatTmp = map.getClass().getDeclaredField("locked");
                        field2Map4TomcatTmp.setAccessible(true);
                        field2Map4Tomcat = field2Map4TomcatTmp;
                    }
                }
            }

            Parameters parameters = getRequestTomcatParamters(request);
            if (parameters != null) {
                for(Map.Entry<String, Object> entry : mapfromBean.entrySet()) {
                    if (entry.getValue() != null) {
                        parameters.addParameter(entry.getKey(), String.valueOf(entry.getValue()));
                    }
                }
            }

            field2Map4Tomcat.setBoolean(map, false);
        } else {
            throw new Exception("request.getParameterMap 错误 class：" + map.getClass().getName());
        }



    }

    @Around("execution(* fai.webhdportal.controller.*.*(..))")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint)throws Throwable{

        Object args[] = proceedingJoinPoint.getArgs();//得到传递给目标方法的参数值

        //判断是不是有 参数使用 @RequestBody 注解，并且把使用该注解的参数的值 绑定到 requestAttribute上
        ProxyMethodInvocation proxymthod = (ProxyMethodInvocation) methodInvocationField.get(proceedingJoinPoint);
        Method method = proxymthod.getMethod();
        Annotation[][] pa = method.getParameterAnnotations();
        for (int i = 0; i < pa.length; i++) {
            for (Annotation annotation : pa[i]) {
                if (annotation instanceof RequestBody) {
                    setBody2RequestAttribute(args[i]);
                }
            }
        }
        return proceedingJoinPoint.proceed(args);//返回执行目标方法，并且把转换修改后的参数值传递到controller（目标方法）
    }

    //初始化 tomcat的相关 field
    private void initTomcatField(HttpServletRequest request) throws Exception {
        Class clazz = request.getClass();
        requestField = clazz.getDeclaredField("request");
        requestField.setAccessible(true);

        parametersParsedField = requestField.getType().getDeclaredField("parametersParsed");
        parametersParsedField.setAccessible(true);

        coyoteRequestField = requestField.getType().getDeclaredField("coyoteRequest");
        coyoteRequestField.setAccessible(true);

        parametersField = coyoteRequestField.getType().getDeclaredField("parameters");
        parametersField.setAccessible(true);

        try {
            hashTabArrField = parametersField.getType().getDeclaredField("paramHashValues");//tomcat7以上的属性
        } catch(Exception exception) {
            hashTabArrField = parametersField.getType().getDeclaredField("paramHashStringArray");//tomcat6以下的属性
        }
        hashTabArrField.setAccessible(true);
        Log.logStd("initTomcatField 初始化完成");
    }

    /**
     * tomcat 下，通过request获取不到 真正存放 paramter
     * @param request
     * @return
     * @throws Exception
     */
    private Parameters  getRequestTomcatParamters(HttpServletRequest request) throws Exception {
        Log.logStd("getRequestTomcatParamters ");
        Object innerRequest = requestField.get(request);
        parametersParsedField.setBoolean(innerRequest, true);
        Object coyoteRequestObject = coyoteRequestField.get(innerRequest);
        Object parameterObject = parametersField.get(coyoteRequestObject);
        return (Parameters) parameterObject;
        //return (Map) hashTabArrField.get(parameterObject);
    }

    //aop获取代理方法的反射,通过静态加载实现初始化
    private static Field methodInvocationField = null;
    static {
        try {
            methodInvocationField = MethodInvocationProceedingJoinPoint.class.getDeclaredField("methodInvocation");
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
        methodInvocationField.setAccessible(true);
        Log.logStd("methodInvocationField 初始化 ");
    }


    private volatile Field field2Map4Resion = null;//resin的相关反射

    private volatile Field field2Map4Tomcat = null;//tomcat的相关反射
    private volatile Field requestField = null;//tomcat的相关反射
    private volatile Field parametersParsedField = null;//tomcat的相关反射
    private volatile Field coyoteRequestField = null;//tomcat的相关反射
    private volatile Field parametersField = null;//tomcat的相关反射
    private volatile Field hashTabArrField = null;//tomcat的相关反射

}

