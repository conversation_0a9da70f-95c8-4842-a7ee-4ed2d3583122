import { defineConfig } from '@fai-hd/hd-sfc-cli';

export default defineConfig({
  bundler: 'esbuild',
  jsConfig: {
    input: 'res/js/**/index.mjs',
    output: 'index.js',
    outputPath: '[origin]',
  },
  cssConfig: {
    extract: true,
    input: '[origin]',
    output: '[name].src.css',
    outputPath: 'res/css/',
  },
  tsConfig: {
    useTs: true,
    disableTypeCheck: true,
    rt2PluginOpt: {},
  },
  reloadConfig: {
    port: 5002,
    useHMR: true,
  },
  chokidarConfig: {
    rollupInputFileGlob: 'res/**/index.mjs',
    needReloadFileGlob: ['etc/res_hdoss.conf'],
  },
  assetsConfig: {
    jspName: 'jspName',
    jspPath: 'jspPath',
    jspStrFlag: 'jspStrFlag',
    jspStrFlagAsync: 'jspStrFlagAsync',

    incName: 'res_hdoss.conf',
    incPath: 'etc/res_hdoss.conf',
    incStrFlag: '"resList"	: [',
  },
  alias: { '@': 'res/js' },
  eol: {
    flag: 'lf',
    fileRegex: /\.src\.(css|js)$/,
  },
});
