<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.math.*"%>
<%@ page import="java.util.regex.Matcher"%>
<%@ page import="java.util.regex.Pattern"%>
<%@ page import="fai.cli.PreSaleUtilCli"%>
<%@ page import="fai.cli.BssStatCli"%>
<%@ page import="fai.cli.*"%>
<%@ include file="/comm/ajaxUtil.jsp.inc"%>


<%!
    private static String getLogRecordList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);                            // adm
        boolean leader = Auth.checkFaiscoAuth("authPreSaleLeader", false);                // 直销售前-组长
        boolean isManager = Auth.checkFaiscoAuth("authHDSaleManage", false);
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);

        // 调用oss配置文件拿到直销售前列表;电话组的组长只能看到他们组的领取记录
        //FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");				// 互动销售列表
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> sidList = new FaiList<Integer>();                        // 直销互动销售列表
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        // 查询调整时间
        boolean setOptTimeFlag = Parser.parseBoolean(request.getParameter("setOptTimeFlag"), false);
        boolean exportNew = Parser.parseBoolean(request.getParameter("new"), false);//废弃 权重导出
        if (setOptTimeFlag) {
            FaiList<Integer> tempSidList = WebOss.getAuthSidList("authAdm");
            sidList = tempSidList.clone();
            tempSidList = WebOss.getAuthSidList("authHDSaleManage");                            // 互动销售组长
            sidList.addAll(tempSidList);
        }


        // 获取员工信息
        FaiList<Param> staffList = new FaiList<Param>();
        FaiList<String> sacctList = new FaiList<String>();
        for (int sid : sidList) {
            Param staff = Acct.getStaffInfo(Web.getFaiCid(), sid);
            if (staff == null) {
                continue;
            }
            staffList.add(staff);
            String sacct = staff.getString(StaffDef.Info.SACCT, "");
            sacctList.add(sacct);
        }

        /* 分页参数 */
        int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = Parser.parseInt(request.getParameter("limit"), 10);
        int start = (pageNo - 1) * limit;
        int total = 0;

        //Url.PageUrl pageUrl = new Url.PageUrl("pageNo");
        //String url = pageUrl.url;

        /* 请求参数 */
        int se_aid = Parser.parseInt(request.getParameter("aid"), 0);
        int se_attrType = Parser.parseInt(request.getParameter("attrType"), -1);                        //类型
        boolean exportExcel = Parser.parseBoolean(request.getParameter("exportFlag"), false);           // 导出判断
        String se_sacct = Parser.parseString(request.getParameter("staff_sacct"), "all");               // 操作人
        boolean isOptTime = Parser.parseBoolean(request.getParameter("optDateFlag"), false);            // 是否查询领取时间
        boolean isCreateTime = Parser.parseBoolean(request.getParameter("creDateFlag"), false);         // 是否查询创建时间
        int tag = Parser.parseInt(request.getParameter("tag"), -1);                                     // 资源标签
        int ta = Parser.parseInt(request.getParameter("ta"), -1);                                       // 资源标签
		int allotType = Parser.parseInt(request.getParameter("allotType"), -1); 
		String saleGroup = Parser.parseString(request.getParameter("saleGroup"), "all"); // 组长权限 查询销售分组
        // 设置时间
        Calendar clr1 = Calendar.getInstance();
        //创建时间
        String sbegDate = Parser.parseString(request.getParameter("creDateBeg"), Parser.parseSimpleTime(clr1));
        String sendDate = Parser.parseString(request.getParameter("creDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        // 客户领取痕迹-领取时间
        String optBegTime = Parser.parseString(request.getParameter("optDateBeg"), Parser.parseSimpleTime(clr1));
        String optEndTime = Parser.parseString(request.getParameter("optDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));

        // 查询售前领取记录
        SearchArg searchArg = new SearchArg();

        // 条件
        searchArg.matcher = new ParamMatcher();

        if (isCreateTime) {
            searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.GE, sbegDate + " 00:00:00");
            searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.LE, sendDate + " 23:59:59");
        }
        if (isOptTime) {
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, optBegTime + " 00:00:00");
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, optEndTime + " 23:59:59");
        }
        if (sacctList.size() > 0) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
        }

        if (se_aid != 0) {
            searchArg.matcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.EQ, se_aid);
        }
        //searchArg.matcher.and(PreSaleHdDef.Info.FLAG,ParamMatcher.LAND_NE,PreSaleHdDef.Flag.TEST_ALLOT,PreSaleHdDef.Flag.TEST_ALLOT);
        FaiList<Integer> hd_mp_business=new FaiList<Integer>();
		 switch(allotType){
			case 0: //平均分配资源
				   searchArg.matcher.and(HdSaleRecordDef.Info.FLAG, ParamMatcher.LAND, HdSaleRecordDef.Flag.AVERAGE_ALLOT, HdSaleRecordDef.Flag.AVERAGE_ALLOT);
				   hd_mp_business.add(HdSaleRecordDef.Business.HD);    //互动
				   hd_mp_business.add(HdSaleRecordDef.Business.MP);	//助手
				   searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
				break;
				
			case 1:  //奖励资源
				   searchArg.matcher.and(HdSaleRecordDef.Info.FLAG, ParamMatcher.LAND, HdSaleRecordDef.Flag.IS_AWARD, HdSaleRecordDef.Flag.IS_AWARD);
				   hd_mp_business.add(HdSaleRecordDef.Business.HD);
				   searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
				break;
				
			case 2:   //续费资源
				  hd_mp_business.add(HdSaleRecordDef.Business.HD);
				  searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
				  searchArg.matcher.and(HdSaleRecordDef.Info.TAG,ParamMatcher.EQ,PreSaleHdDef.Tag.RENEW_RESOURCE);
				break;
				
			case 3:   //公海库领取资源
				   hd_mp_business.add(HdSaleRecordDef.Business.HD);
				   searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
				   searchArg.matcher.and(HdSaleRecordDef.Info.TAG,ParamMatcher.EQ,PreSaleHdDef.Tag.GET_GH);
				break;
			case 4:   //领取审批资源
				   hd_mp_business.add(HdSaleRecordDef.Business.HD);    //互动
				   hd_mp_business.add(HdSaleRecordDef.Business.MP);	//助手
				   searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
				   searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE,ParamMatcher.EQ,HdSaleRecordDef.AttrType.APPROVE);
		}
		
		     // 销售分组-权限组
        if (!saleGroup.equals("all")) {
            FaiList<Integer> tmpSidList = WebOss.getAuthSidList(saleGroup);
            FaiList<String> tmpSacctList = new FaiList<String>();
            for (int tmpSid : tmpSidList) {
                String t_sacct = Acct.getSacct(Web.getFaiCid(), tmpSid);
                tmpSacctList.add(t_sacct);
            }
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, tmpSacctList);
        }
		
        if (!se_sacct.equals("all")) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.EQ, se_sacct);
        }
        if (se_attrType != -1) {
            if (se_attrType == HdSaleRecordDef.AttrType.RELEASE) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getReleaseList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.DROP) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getDropList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.GET) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getInitList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.ALLOT) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getAllotList());
            }else if(se_attrType == HdSaleRecordDef.AttrType.CHANGE) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getChangeList());
            }else if(se_attrType == HdSaleRecordDef.AttrType.ENTER_INVALID) {   // 转入无效库
                FaiList<Integer> invalidList = new FaiList<Integer>();
                invalidList.add(HdSaleRecordDef.AttrType.ENTER_INVALID);
                invalidList.add(HdSaleRecordDef.AttrType.SYS_INVALID);
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, invalidList);
            }else {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, se_attrType);
            }
        }
        if (exportNew) {
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, "2018-08-29 00:00:00");
        }
        if (tag != -1) {
        	if(tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
       		 FaiList<Integer> tagTmpList = new FaiList<Integer>();
       		 tagTmpList.add(72);
       		 tagTmpList.add(52);
       		 searchArg.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.NOT_IN, tagTmpList);
        	}else{
        		 searchArg.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.EQ, tag);
        	}
        }
        if (ta != -1  || tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC) {
            FaiList<Param> taList = getTaList();
            FaiList<Integer> trackList = PreSaleHdDef.getTaList(taList, ta);
            if (ta != PreSaleHdDef.PreSaleTa.NOT_IN) {
            	if(tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
                	trackList =PreSaleHdDef.getTaList(taList, 11);
                }
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.IN, trackList);
                if (ta == PreSaleHdDef.PreSaleTa.MOBI) {
                    searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, 0);
                }
                Log.logStd("unique-test ta list = %s", trackList);
            } else {
                //li.未知查询，剔除掉移动放量的ta。
                trackList.add(491);
                trackList.add(493);
                trackList.add(2344);
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, trackList);
            }
        }
        // 调整领取时间
        if (setOptTimeFlag) {
            searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.CREATETIME_CLIENT);
        }

        searchArg.start = start;
        searchArg.limit = limit;
        searchArg.totalSize = new fai.comm.util.Ref<Integer>();
        searchArg.cmpor = new ParamComparator(HdSaleRecordDef.Info.CREATE_TIME, true);

        // 导出的话,limit放大点
        if (exportExcel) {
            searchArg.start = 0;
            searchArg.limit = 10000;
        }


        FaiList<Param> actionList = new FaiList<Param>();
        Dao ossDao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "*";
            if (exportNew) {
                selectArg.table = "hdSaleRecord";
            } else {
                selectArg.table = "hdSaleRecord";
            }
            selectArg.searchArg = searchArg;
            actionList = ossDao.select(selectArg);
        } finally {
            ossDao.close();
        }
        total = searchArg.totalSize.value;
        /* 数据翻译 */
        FaiList<Integer> taAidList = new FaiList<Integer>();
        Map<Integer, String> tagMap = getTagMap();
        for (Param p : actionList) {
            String p_sacct = p.getString(HdSaleRecordDef.Info.SACCT, "");
            int attrType = p.getInt(HdSaleRecordDef.Info.ATTR_TYPE, 0);
            String attrTypeContent = HdSaleRecordDef.getAttrTypeName(attrType);
            String type = attrType != HdSaleRecordDef.AttrType.GET_EMAIL ? "客户" : "邮箱";
            Param tmpSalesAcct = Acct.getStaffInfo(Web.getFaiCid(), p_sacct);
            String p_name = tmpSalesAcct.getString(StaffDef.Info.NAME, "");
            int tagItem = p.getInt("tag", 0);
            String tagName = tagMap.get(tagItem);
            String createTime = Parser.parseString(p.getCalendar(HdSaleRecordDef.Info.CREATE_TIME));
            String receiveTime = Parser.parseString(p.getCalendar(HdSaleRecordDef.Info.RECEIVE_TIME));
            if (tagName == null) {
                tagName = "";
            }
            p.setString("tagName", tagName);
            p.setString("sacctName", p_name);
            p.setString("attrTypeContent", attrTypeContent);
            p.setString("type", type);
            p.setString("createTime", createTime);
            p.setString("receiveTime", receiveTime);
            int flag = p.getInt(HdSaleRecordDef.Info.FLAG, 0);
            if (Misc.checkBit(flag, HdSaleRecordDef.Flag.IS_AWARD)) {
                p.setString("isAward", "是");
            } else {
                p.setString("isAward", "否");
            }

            if (Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_A)) {
                p.setString("allotType", "A");
            } else if (Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_B)) {
                p.setString("allotType", "B");
            } else if (Misc.checkBit(flag, HdSaleRecordDef.Flag.AVERAGE_ALLOT)) {
                p.setString("allotType", "average");
            } else {
                p.setString("allotType", "未知");
            }

            if (p.getString("taGroupName", "").isEmpty()) {//如果没有注册来源组数据，后面加的，旧数据没有
                taAidList.add(p.getInt("aid"));
            }
        }
        if (taAidList.size() > 0) {
            FaiList<Param> taInfoList = null;
            FaiList<Param> taList = null;
            Dao bssMainDao = WebOss.getBssMainDaoMaster();
            try {
                Dao.SelectArg selectArg = new Dao.SelectArg();
                selectArg.field = "aid,ta";//再查aid，用来查创建游戏数
                selectArg.table = "acctStatus";
                SearchArg searchArgAid = new SearchArg();
                searchArgAid.matcher = new ParamMatcher("aid", ParamMatcher.IN, taAidList);
                selectArg.searchArg = searchArgAid;//matcher 一样
                taInfoList = bssMainDao.select(selectArg);
                taList = bssMainDao.executeQuery("select * from ta");
            } catch (Exception e) {
                Log.logErr(e);
            } finally {
                bssMainDao.close();
            }
            Param taParam = new Param(true);
            for (Param taInfo : taList) {
                String taKey = String.valueOf(taInfo.getInt("ta", 0));
                taParam.setParam(taKey, taInfo);
            }
            for (Param acctInfo : taInfoList) {
                String taKey = String.valueOf(acctInfo.getInt("ta", 0));
                String aidKey = String.valueOf(acctInfo.getInt("aid", 0));
                taParam.setParam(aidKey, taParam.getParam(taKey));
            }
            for (Param allotInfo : actionList) {
                if (allotInfo.getString("taGroupName", "").isEmpty()) {  //如果没有注册来源组数据，后面加的，旧数据没有
                    String aidKey = String.valueOf(allotInfo.getInt("aid"));
                    Param taInfo = taParam.getParam(aidKey);
                    if (taInfo != null) {
                        int innerTa = taInfo.getInt("ta", 0);
                        int groupId = taInfo.getInt("groupId", 0);
                        String taGroupName = PreSaleHdDef.getTaNameByTaAndGroupId(groupId, innerTa);
                        allotInfo.setInt("ta",innerTa);
                        allotInfo.setString("taGroupName", taGroupName);

                    }
                }
            }
        }


        // 导出Excel
        if (exportExcel) {

            FaiList<Integer> aidList = new FaiList<Integer>();

            //ta组信息
            FaiList<Param> taList = getTaList();
            HashMap<Integer,Integer> taMap = new HashMap<Integer, Integer>();
            for(Param p : taList){
                int innerTa = p.getInt("ta",0);
                int groupId = p.getInt("groupId",0);
                taMap.put(innerTa,groupId);
            }

            for (Param p : actionList) {
                int aid = p.getInt("aid",0);
                int innerTa = p.getInt("ta",0);
                if(taMap.containsKey(innerTa)){
                    p.setInt("groupId",taMap.get(innerTa));
                }
                aidList.add(aid);
            }

            //开通互动信息
            FaiList<Param> acctInfoList = new FaiList<Param>();
            Dao bssDao = WebOss.getBssMainDao();
            try{
                Dao.SelectArg acctArg = new Dao.SelectArg();
                acctArg.table = "acctStatus";
                acctArg.field = "aid,openHdTime";
                acctArg.searchArg.matcher = new ParamMatcher("aid",ParamMatcher.IN,aidList);
                acctInfoList = bssDao.select(acctArg);
                Log.logDbg("acctInfoList size = %d",acctInfoList.size());
            }finally {
                bssDao.close();
            }

            HashMap<Integer,String> acctMap = new HashMap<Integer, String>();
            for(Param p : acctInfoList){
                int aid = p.getInt("aid",0);
                int openHdTime = p.getInt("openHdTime",0);
                String time = Parser.parseDateString(openHdTime,"yyyy-MM-dd HH:mm:ss");
                acctMap.put(aid, time);
            }

            for (Param p : actionList) {
                int aid = p.getInt("aid",0);
                if(acctMap.containsKey(aid)){
                    p.setString("openHdTime",acctMap.get(aid));
                }
            }

            response.setContentType("application/x-excel");
            out.clear();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("客户领取数据列表.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString("aid", "aid");
            cellKey.setString("createTime", "创建时间");
            cellKey.setString("receiveTime", "领取or释放时间");
            cellKey.setString("sacctName", "账号");
            cellKey.setString("attrTypeContent", "领取类型");
            cellKey.setString("tagName", "资源标签");
            cellKey.setString("type", "领取对象");
            cellKey.setString("action", "内容");
            cellKey.setString("isAward", "是否奖励资源");
            cellKey.setString("taGroupName", "注册来源组");
            cellKey.setString("ta", "ta");
            cellKey.setString("groupId", "ta组");
            cellKey.setString("openHdTime", "开通互动时间");
            if (Session.getSid() == 1224 || Session.getSid() == 753 || isManager) {
                cellKey.setString("allotType", "分配方式");
            }
            OssPoi.exportExcel(cellKey, actionList, outputStream);

            return "{\"success\":true}";
        }

        return "{\"success\":true, \"dataList\":" + actionList + ", \"total\":" + total + "}";
    }
%>

<%
    //cmd处理
    String output = "";
    try{
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            return;
        }else if (cmd.equals("getLogRecordList")){
            output = getLogRecordList(request,response,out);
        }
    }catch (Exception exp){
        PrintUtil.printErr(exp);
        output = WebOss.checkAjaxException(exp);
    }
    out.print(output);

%>