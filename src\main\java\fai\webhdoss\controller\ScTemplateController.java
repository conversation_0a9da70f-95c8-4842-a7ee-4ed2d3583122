package fai.webhdoss.controller;

import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.webhdoss.model.vo.scTemplate.ScTemplateListVO;
import fai.webhdoss.model.vo.scTemplate.ScTemplateVO;
import fai.webhdoss.service.ScTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * @Description 模版
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/18 10:21
 */
@RestController
@RequestMapping("/template")
public class ScTemplateController {

    @Autowired
    private ScTemplateService scTemplateService;

    /**
     * 获取模版信息
     *
     * <AUTHOR> 2025/4/18 16:29
     * @Update HLS 2025/4/18 16:29
     **/
    @RequestMapping("/getScTemplateInfo")
    public JsonResult getScTemplateInfo(@RequestParam(defaultValue = "0") int id, @RequestParam(defaultValue = "0") int protoId) {
        HdAssert.Biz.judgeNotLog(protoId < 1, "protoId 不能都为空");
        return scTemplateService.getScTemplateInfo(id, protoId);
    }

    /**
     * 获取配置信息
     *
     * <AUTHOR> 2025/4/18 16:32
     * @Update HLS 2025/4/18 16:32
     **/
    @RequestMapping("/getConfInfo")
    public JsonResult getConfInfo() {

        return scTemplateService.getConfInfo();
    }

    /**
     * 保存、编辑模版信息
     *
     * <AUTHOR> 2025/4/18 17:04
     * @Update HLS 2025/4/18 17:04
     **/
    @PostMapping("/setScTemplateInfo")
    public JsonResult setScTemplateInfo(@RequestBody @Valid ScTemplateVO vo) {
        if (vo == null) {
            return JsonResult.error("保存信息不能为空呀");
        }

        return scTemplateService.setScTemplateInfo(vo);
    }

    /**
     * 获取列表
     *
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
     **/
    @PostMapping("/getScTemplateList")
    public JsonResult getScTemplateList(@RequestBody ScTemplateListVO vo) {
        if (vo == null) {
            return JsonResult.error("参数 不能为空呀");
        }

        return scTemplateService.getScTemplateList(vo);
    }

    /**
     * 更新状态
     *
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
     **/
    @RequestMapping("/setScTemplateStatus")
    public JsonResult setScTemplateStatus(@RequestParam(defaultValue = "0") int id, @RequestParam(defaultValue = "0") int status) {
        if (id < 1) {
            return JsonResult.error("id 不能为空呀");
        }

        return scTemplateService.setScTemplateStatus(id, status);
    }

    /**
     * 复制模版
     *
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
     **/
    @RequestMapping("/copyScTemplate")
    public JsonResult copyScTemplate(@RequestParam(defaultValue = "0") int id) {
        if (id < 1) {
            return JsonResult.error("id 不能为空呀");
        }

        return scTemplateService.copyScTemplate(id);
    }

    /**
     * 临时资源文件上传
     *
     * <AUTHOR> 2025/5/8 14:27
     * @Update HLS 2025/5/8 14:27
     **/
    @RequestMapping("/uploadTmpFile")
    public JsonResult uploadTmpFile(HttpServletRequest request) throws Exception {
        return scTemplateService.uploadTmpFile(request);
    }

    /**
     * 断点续传
     *
     * <AUTHOR> 2025/8/7 14:04
     * @Update HLS 2025/8/7 14:04
     **/
    @RequestMapping("/advanceUploadFile4Sc")
    public JsonResult advanceUploadFile4Sc(HttpServletRequest request) throws Exception {
        return scTemplateService.advanceUploadFile4Sc(request);
    }

    /**
     * 获取域名信息
     *
     * <AUTHOR> 2025/5/30 16:29
     * @Update HLS 2025/5/30 16:29
     **/
    @RequestMapping("/getDomain")
    public JsonResult getDomain() throws Exception {
        return scTemplateService.getDomain();
    }

}
