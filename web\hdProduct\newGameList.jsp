<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>活动列表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
    </head>

    <body>	
		<!--页面顶部标题-->
		<div style="text-align: center;">
			<h3 style="margin-top: 0px">活动列表</h3>
		</div>

		<!--表单-->
		<div v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<!--aid-->
				<el-form-item label="AID">
					<el-input v-model="form.aid" placeholder="AID" class="fai-wid-100"></el-input>
				</el-form-item>
			</el-form>
		</div>	
			


    </body>




</html>