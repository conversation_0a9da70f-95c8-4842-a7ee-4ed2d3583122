stages:
  - test
  - pro
  - pre

test:
  stage: test      # stage
  only:
    - test          # branch support regex
  script:
    - ssh -p 50805 <EMAIL> << ssh
    - /usr/bin/python /home/<USER>/script/cicd/cicdhd0428.py hdoss  web test '$CI_COMMIT_TITLE' $GITLAB_USER_NAME $CI_JOB_URL
    - ssh

pro:
  stage: pro      # stage
  only:
    - production          # branch support regex
  script:
    - ssh -p 50805 <EMAIL> << ssh
    - /usr/bin/python /home/<USER>/script/cicd/cicdhd0428.py hdoss  web production '$CI_COMMIT_TITLE' $GITLAB_USER_NAME $CI_JOB_URL
    - ssh

pre:
  stage: pre      # stage
  only:
    - pre-production          # branch support regex
  script:
    - ssh -p 50805 <EMAIL> << ssh
    - /usr/bin/python /home/<USER>/script/cicd/cicdhd0428.py hdoss  web pre-production   '$CI_COMMIT_TITLE' $GITLAB_USER_NAME $CI_JOB_URL
    - ssh
