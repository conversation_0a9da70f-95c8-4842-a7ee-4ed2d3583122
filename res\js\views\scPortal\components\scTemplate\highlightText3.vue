<template>
  <div>
    <select @change="onTagChange">
      <option disabled selected>请选择标签</option>
      <option value="highlight">高亮</option>
      <option value="warn">警告</option>
      <option value="note">备注</option>
    </select>
    <el-button @click="clearTag">清除标签</el-button>
    <editor-content :editor="editor" />
  </div>
</template>

<script>

import { Editor, EditorContent } from "@tiptap/vue-2";
import StarterKit from "@tiptap/starter-kit";
import { TagMark } from '@/views/scPortal/extensions/tiptap.js';

/**
 * @see link {https://tiptap.dev/docs/editor|tiptap 富文本编辑器文档}
 */
export default {
  name: "HighlightText3",
  components: {
    EditorContent,
  },

  data() {
    return {
      editor: null,
    };
  },

  mounted() {
    this.editor = new Editor({
      content: "<p>I'm running Tiptap with Vue.js. 🎉</p>",
      extensions: [StarterKit, TagMark],
    });
  },

  beforeDestroy() {
    this.editor.destroy();
  },
  methods: {
    onTagChange(event) {
      const tagType = event.target.value;
      if (!tagType) return;

      const labelText = {
        'highlight': '高亮',
        'warn': '警告',
        'note': '备注'
      }[tagType];

      const { state } = this.editor;
      const { from, to } = state.selection;
      
      if (from === to) return; // 没有选中文本时不执行

      // this.editor.commands.setTag({ 
      //   tagType,
      //   label: labelText
      // });
      this.editor.commands.setMark('tag', {
        tagType,
        label: labelText,
      })
      event.target.selectedIndex = 0;
      console.log(e.target.value);
    },
    clearTag() {
      this.editor.commands.unsetTag()
    },
  },
};
</script>

<style>
.tag {
  padding: 2px 4px;
  border-radius: 3px;
  margin: 0 1px;
}
.tag-highlight {
  background-color: yellow;
}
.tag-warn {
  background-color: red;
  color: white;
}
.tag-note {
  background-color: lightblue;
}

/* 添加标签样式 */
.tag-label {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 4px;
}
.tag-label-highlight {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
}
.tag-label-warn {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
.tag-label-note {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}
</style>
