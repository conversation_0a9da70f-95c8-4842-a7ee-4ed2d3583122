<template>
  <div class="mt-[30px]">
    <p class="mb-[10px] text-[16px] font-bold">模板基础配置</p>
    <el-form-item label="模板名称:" prop="name">
      <el-input minlength="1" maxlength="50" v-model="templateInfo.name" placeholder="请输入模板名称" class="!w-[380px]" />
    </el-form-item>
    <el-form-item label="模板适用行业" prop="industry">
      <el-select v-model="templateInfo.industry" placeholder="请选择模板适用行业" class="!w-[230px]">
        <el-option v-for="item in templateConfInfo.industryList" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="模板适用场景" prop="scene">
      <el-select v-model="templateInfo.scene" placeholder="请选择模板适用场景" class="!w-[230px]">
        <el-option v-for="item in templateConfInfo.sceneList" :key="item.key" :label="item.value" :value="item.key"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="系统关键词">     
      <el-input v-model="templateInfo.sysKeyWords" class="!w-[230px]" disabled />
    </el-form-item>
  </div>
</template>

<script>
// import { getTemplateConfInfo } from "@/views/scPortal/api/scTemplate.js";

export default {
  name: "ResForm",
  data() {
    return {
      isUploading: false,
      percentage: 0,
      industryList: [],
      sceneList: [],
    }
  },
  props: {
    templateInfo: {
      type: Object,
      required: true,
    },
  },
  created() {
    // this.getConfInfo();
  },
  computed: {
    ...Vuex.mapState('scPortal', ['templateConfInfo']),
    sysKeyWordsWatchConfig() {
      return {
        industry: this.templateInfo.industry,
        scene: this.templateInfo.scene,
      };
    },
  },
  watch: {
    sysKeyWordsWatchConfig: {
      handler(newVal) {
        this.getSysKeyWords();
        console.log(newVal, 'sysKeyWordsWatchConfig');
      },
      deep: true,
      immediate: true,
    },

  },
  methods: {
   /**
     * 获取配置信息
     */
    // getConfInfo() {
    //   getTemplateConfInfo().then((res) => {
    //     if (res && res.success) {
    //       this.industryList = res.data.industryList;
    //       this.sceneList = res.data.sceneList;
    //       console.log(this.industryList, this.sceneList, 'getConfInfo');
    //       this.getSysKeyWords();
    //     } else {
    //       this.$message.error(res.message);
    //     }
    //   });
    // },
    /**
     * 获取系统关键词
     */
    getSysKeyWords() {
      this.templateInfo.sysKeyWords = this.templateConfInfo.industryList.find(item => item.key === this.templateInfo.industry)?.value + ', ' + this.templateConfInfo.sceneList.find(item => item.key === this.templateInfo.scene)?.value;
    },
  },
};
</script>

<style scoped></style>
