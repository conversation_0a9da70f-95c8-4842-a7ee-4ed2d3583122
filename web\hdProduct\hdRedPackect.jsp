<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	/*if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}*/
	Param exposeParam = new Param();
	{
		exposeParam.setString( "hdossRoot",WebHdOss.getDomainUrl() );
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>活动列表</title>
		<%=Web.getToken()%>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdRedPackect")%>"/>
    </head>

    <body>	
      <div id="app"></div>
    </body>
	<script type="text/javascript" charset="utf-8">
		var exposeParam = <%=exposeParam%>;
	</script>
	<script src='<%=HdOssResDef.getResPath("js_hdRedPackect")%>' type="text/javascript" charset="utf-8"></script>
</html>