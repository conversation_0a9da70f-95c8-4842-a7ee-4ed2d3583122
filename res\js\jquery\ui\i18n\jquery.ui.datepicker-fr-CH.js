/* Swiss-French initialisation for the jQuery UI date picker plugin. */
/* Written <PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['fr-CH'] = {
		closeText: 'Fermer',
		prevText: '&#x3c;Préc',
		nextText: 'Suiv&#x3e;',
		currentText: '<PERSON><PERSON><PERSON>',
		monthNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON>in',
		'<PERSON><PERSON><PERSON>','A<PERSON><PERSON>t','Septembre','Octobre','Novembre','Décembre'],
		monthNamesShort: ['Jan','Fév','Mar','Avr','<PERSON>','Jun',
		'Jul','Aoû','Sep','Oct','Nov','Déc'],
		dayNames: ['<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>'],
		dayNamesShort: ['Dim','Lun','<PERSON>','Me<PERSON>','Je<PERSON>','V<PERSON>','<PERSON>'],
		dayNamesMin: ['<PERSON>','<PERSON>','Ma','Me','Je','Ve','Sa'],
		weekHeader: 'Sm',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['fr-CH']);
});