﻿<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop.xsd"
		default-lazy-init="true">

    <!-- 拦截器 -->
<!--    <mvc:interceptors>-->
<!--        <mvc:interceptor>-->
<!--            &lt;!&ndash; 对所有的请求拦截使用/** ,对某个模块下的请求拦截使用：/myPath/* &ndash;&gt;-->
<!--            <mvc:mapping path="/**" />-->
<!--            <ref bean="devInterceptor" />-->
<!--        </mvc:interceptor>-->
<!--    </mvc:interceptors>-->

<!--    <bean id="devInterceptor"-->
<!--          class="fai..interceptor.DevInterceptor">-->
<!--    </bean>-->

    <!-- 开启SpringMVC注解模式 -->
    <mvc:annotation-driven>
        <!-- 防止乱码 统一用 utf8 -->
        <mvc:message-converters register-defaults="true">
            <bean class="org.springframework.http.converter.StringHttpMessageConverter">
				 <constructor-arg value="UTF-8" />
				 <property name="supportedMediaTypes"> 
					<list> 
						<value>text/html</value>
						<value>application/json</value>
					</list> 
				</property>
            </bean>
            <!--<bean class="org.springframework.http.converter.json.GsonHttpMessageConverter"/>-->
        </mvc:message-converters>
    </mvc:annotation-driven>

    <!-- <mvc:resources location="/res/fhcms/css/**" mapping="/resource/css/**"/>
    <mvc:resources location="/res/fhcms/js/**" mapping="/resource/js/**"/> -->

    <!-- 静态资源默认servlet配置 -->
    <mvc:default-servlet-handler/>

    <!-- 配置jsp 显示ViewResolver -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <!-- <property name="prefix" value="/WEB-INF/views/"/> -->
        <property name="suffix" value=".jsp"/>
    </bean>

    <!-- 扫描web相关的bean -->
    <context:component-scan base-package="fai.webhdoss.*"/>

    <!-- Swagger 配置 -->
    <mvc:resources location="classpath:/META-INF/resources/" mapping="doc.html"/>
    <mvc:resources location="classpath:/META-INF/resources/webjars/" mapping="/webjars/**"/>

    <!-- 启动AspectJ自动代理 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>
</beans>
