/*--------------客户列表 */

var localTime = new Date();

// v-dialogDrag: 弹窗拖拽属性
Vue.directive('dialogdrag', {
  bind(el, binding, vnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header');
    const dragDom = el.querySelector('.el-dialog');
    //dialogHeaderEl.style.cursor = 'move';
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dragDom.style.cssText += ';top:0px;'
  
    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = (function() {
        if (window.document.currentStyle) { 
            return (dom, attr) => dom.currentStyle[attr];
        } else{
            return (dom, attr) => getComputedStyle(dom, false)[attr];
        }
    })()    
     
    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;
       
      const screenWidth = document.body.clientWidth; // body当前宽度
        const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取) 
         
        const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
        const dragDomheight = dragDom.offsetHeight; // 对话框高度
         
        const minDragDomLeft = dragDom.offsetLeft;
        const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;
         
        const minDragDomTop = dragDom.offsetTop;
        const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;
  
       
      // 获取到的值带px 正则匹配替换
      let styL = sty(dragDom, 'left');
      let styT = sty(dragDom, 'top');
  
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if(styL.includes('%')) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
      }else {
        styL = +styL.replace(/\px/g, '');
        styT = +styT.replace(/\px/g, '');
      };
       
      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离 
                let left = e.clientX - disX;
                let top = e.clientY - disY;
                 
                // 边界处理
               /* if (-(left) > minDragDomLeft) {
                    left = -(minDragDomLeft);
                } else if (left > maxDragDomLeft) {
                    left = maxDragDomLeft;
                }
                 
                if (-(top) > minDragDomTop) {
                    top = -(minDragDomTop);
                } else if (top > maxDragDomTop) {
                    top = maxDragDomTop;
                }*/
  
        // 移动当前元素 
                dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };
  
      document.onmouseup = function (e) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    } 
  }
});

	
var ykSale = new Vue({
	el:'#ykSale-list',
	data: {
		dataList: [],
        parentBoxAuth:{},
		pageData:{
			currentPage:1,
			total:0,
			size:20,
		},
		tabNameDeal:"成交库(0)",
		tabNamePerson:"个人库(0)",
//        tabNameB:"B库(0)",
		tab:1,
		logicImgSrc:'',
		message:{
			messageModelList:[],//短信模板
			messageModel:"",//选择的短信模板
			singleMobile:"",//单个客户手机
			page:1,//当前页
			dataList:[],
			addTimeStart:new Date(),
			addTimeEnd:new Date(),
			total:0,
			sendTime:{
				start:localTime,
				end:localTime,
				check:false
			}
		},
		//修改领取时间
		editReceiveTimeData:{
			aid:0,
			oldReceiveTime:'',
			newReceiveTime:'',
			sale:'',
			picture:'',
			name:'filedata',
			fileId:''
		},
        //修改领取时间
        backToAkuData:{
            aid:0,
            sale:'',
            picture:'',
            name:'filedata',
            fileId:''
        },
		personInfo:{					//个人电话信息
			nickName:"",
			phone:"",
			modelInfoList:[],			//短信模板
		},
		QWRemindInfo:{                 //企微发送列表
			remindList:[],
		},
		reasonStat:{					//未购买原因
			hasGet:0,					//是否已经获取了信息,因为有concat函数，避免多次获取
			value:'全部',
			labelList:[
				{name:"全部",label:"全部"}
			],
			regTime:{					//注册时间
				start:localTime,
				end:localTime,
			},
			totolList:{
				intentList:[
					{
						'intent':'总计',
						'percent':0,
						'互动免费版':0,
						'互动白银版':0,
						'互动铂金版':0,
						'总计':0
					},
				],
				markList:[
					{
						'disBuyReason':'总计',
						'percent':0,
						'互动免费版':0,
						'互动白银版':0,
						'互动铂金版':0,
						'总计':0
					},
				],
			}				//未购买原因统计
		},
        aidMsgDialog:{
            show: false,
			data: '',
			biaoji: '',
            inputNote: '',
			mark: '',
			editMark: '',
			QWRemindTime:new Date(),//填充企微提醒时间
			pickerOptions:{//设置企微提醒时间大于当前时间
		          disabledDate(time) {
		              return time.getTime() < Date.now();
		            }
			},
			index:'',
			sFlag:'重点关注',
			sendMsg:true,
			index:'',
			oldIndex:'',//按最后更新时间排序提交备注会刷新，index是0，销售点击下一个希望是未刷新之前的下一个,用此变量记录
			lastConsultTime:'',//最后咨询客服时间
			oneWeekTalkNum:'',//一周咨询客服次数
			token:''
        },

		biaojiDialog:{
			show: false,
			aid:'',
			data: '',
            intent: '',
            reason: '',
            intentLevel: '',
            intentList: '',
            reasonList:'',
            talkNextTime:'',
            corpMark:''
		},
		buyProductDialog:{
			show:false
		},

		textStyle_10:{
            color: ''
		},
        textStyle_20:{
            color: ''
        },
        textStyle_28:{
            color: ''
        },
        textStyle_follow:{
            color: ''
        },
        textStyle_ban_msg:{
            color: ''
        },

		logicImgVisible:false,
		dialogVisible:false,
		ediReceiveTimePanel:false,
		backToAkuPanel:false,
		libTab:false,

		form:{
			status:this.tab,
			province:"",
			city:"",
			aid:"",
			mark:"",
			memberNumStart:"",
			memberNumEnd:"",
			loginTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			regTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			lastUpdateTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			receiveTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			sendMessageTime:{
				start:localTime,
				end:localTime,
				check:false,
				noSendCheck:false,
			},
			nextTalkTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			firstCreateStoreTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			ta:{
				name:"注册来源",
				value:-1,
				labelList:[]
			},
			intent:{
				name:"标记",
				value:-1,
				labelList:[]
			},
			siteVersion:{
				name:"网站版本",
				value:-1,
				labelList:[]
			},
			hdVersion:{
				name:"互动版本",
				value:-1,
				labelList:[]
			},
			ykVersion:{
				name:"悦客版本",
				value:-1,
				labelList:[]
			},
			spgVersion:{
				name:"轻站版本",
				value:-1,
				labelList:[]
			},
			sortBy:{
				name:"排列顺序",
				value:-1,
				labelList:[]
			},
			saleGroup:{
				value:"all",
				name:"销售分组",
				labelList:[]
			},
			receiveSale:{
				value:"all",
				name:"领取人",
				labelList:[
					{name:"全部",label:"all"},
				]
			},
			tag:{
				name:"标签",
				value:-1,
				labelList:[{name:"全部",label:"-1"}]
			},
			intentLevel:{
				name:"意向度",
				value:-1,
				labelList:[
				]
			},
			hasOpenYk:{
				name:"全部",
				value:-1,
				labelList:[
					{name:"全部",label:-1},	
					{name:"未开通悦客",label:0},
					{name:"已开通悦客",label:1},
				]
			},
			excel:false,
		},
		// navList: [{title:"主页",url:"index"},{title:"互动销售",url:"hdSale"}],
		active:"index",
		title:"个人库",
	},
	created: function(){
		// 刷新的时候回到对应的导航页面
		this.getDataListByArgs();
		this.getDefList();
		this.getMessageModelList();
		this.initBiaojiInfo();
	},
	methods: {
		getSendMessageList(){
			var arg = {
				"cmd":"getSendMessageList",
				"pageNo":this.message.page,
				"begDate":Fai.tool.getDateTimeStart(this.message.addTimeStart),
				"endDate":Fai.tool.getDateTimeEnd(this.message.addTimeEnd),
				"sendTimeStart":Fai.tool.getDateTimeStart(this.message.sendTime.start),
				"sendTimeEnd":Fai.tool.getDateTimeEnd(this.message.sendTime.end),
				"sendTime":this.message.sendTime.check,
				"sale":this.form.receiveSale.value,
			}
			Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				var data = response.data;
				if(data.success){
					this.message.dataList = data.dataList;
					this.message.total = data.total;
				}else{
					this.$message({
						type: 'warning',
						message: data.msg,
					});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		deleteRemind(index,remindList){
			this.$confirm('确定删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				  var arg={
					"cmd":"deleteRemind",
				    "id":remindList[index].id,
					"QWRemindTime":remindList[index].QWRemindTime,
				}
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					var data = response.data;
					if(data.success){
						this.$message({
							type: 'success',
							message: data.msg,
						});
						this.getRemindList();
					}else{
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});          
			  });
		},
		
		
		changeStatus(tab,event){
			 if(tab.index ==="0"){//个人库
			 	this.tab = 1;
			 	this.getDataListByArgs();
			 }else if(tab.index==="1"){//成交库
                this.tab = 2;
                this.getDataListByArgs();
			 }else if(tab.index==="2"){//短信列表
			 	this.tab = 3;
			 	this.getSendMessageList();
			 }else if(tab.index === "3"){//维护个人电话信息
		 		this.getSaleMsg();
			 }
		},
		
		//释放个人库和成交库
		releasePreSale(index,dataList){
			let cmd = this.tab == 1 ? 'releasePreSale':'releasePreSaleDeal';
			alert(cmd);
			this.$confirm('确定释放该客户?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":cmd,
					"aid":dataList[index].aid,
					"salesAcct":dataList[index].salesAcct,
				}
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					let type = 'success';
					if(!response.data.success){
						type = 'warning';
					}
					this.$message({
						type: type,
						message: response.data.msg
					});
					this.getDataListByArgs();
				}, response => {
					this.$message({
						type: 'warning',
						message: '系统错误!'
					});
				});
			
			  }).catch(() => {
					this.$message({
						type: 'info',
						message: '已取消删除'
					});          
			  });
		},
		editReceiveTime(val){
			if(val){//确定上传
				var arg = {
					"cmd":"addPreSaleApprove",
					"aid":this.editReceiveTimeData.aid,
					"fileId":this.editReceiveTimeData.fileId,
					"receiveTime":Fai.tool.dateFormatter(this.editReceiveTimeData.newReceiveTime.getTime(),"yyyy-MM-dd HH:mm:ss"),
					"salesAcct":this.editReceiveTimeData.sale,
					"approveStyle":7,
				}
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					var data = response.data;
					if(data.success){
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}else{
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			}
			this.ediReceiveTimePanel = false;
		},
		//提交领回B库申请
        submitBackToAkuData(val){
            if(val){//确定上传
                var arg = {
                    "cmd":"backToAkuApprove",
                    "aid":this.backToAkuData.aid,
                    "fileId":this.backToAkuData.fileId,
                    "salesAcct":this.backToAkuData.sale,
                    "approveStyle":8,
                }
                Vue.http.post("/ajax/ykSale/approve_h.jsp", arg, {emulateJSON:true}).then(response => {
                    var data = response.data;
                    if(data.success){
                        this.$message({
                            type: 'warning',
                            message: data.msg,
                        });
                    }else{
                        this.$message({
                            type: 'warning',
                            message: data.msg,
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            }
            this.backToAkuPanel = false;
        },
		showEditReceiveTime(index,dataList){
			this.ediReceiveTimePanel = true;
			var data = dataList[index];
			this.editReceiveTimeData.aid = data.aid;
			this.editReceiveTimeData.oldReceiveTime = data.receiveTime;
			this.editReceiveTimeData.sale = data.salesAcct;
		},
		showBackToAkuPanel(index,dataList){
			this.backToAkuPanel = true;
			var data = dataList[index];
			this.backToAkuData.aid = data.aid;
			this.backToAkuData.sale = data.salesAcct;
		},
		//企业数据
		getCorpInfo(aid){
            var arg = {
                "aid":aid,
                "cmd":"getCorpInfo",
            }
            Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
                this.aidMsgDialog.data = response.data;
                this.aidMsgDialog.biaoji = response.data.biaoji;
                if(this.aidMsgDialog.biaoji.sFocus==1){
                	this.aidMsgDialog.sFlag='取消关注';
                }else{
                	this.aidMsgDialog.sFlag='重点关注';
                }
                this.aidMsgDialog.sendMsg=this.aidMsgDialog.biaoji.sendMsg;
                
                let mark = response.data.markName;
                if(typeof obj == "undefined" || obj == null || obj == ""){
                    this.aidMsgDialog.mark = mark;
				}else{
                    this.aidMsgDialog.mark = mark.replace("-----------------------","\n-----------------------\n");
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
		},
		
		//获取客服token
		getToken(aid){
			var args = {
					 "appid":"5ef1e33a54de47f9a7350ac6836989a0",
					 "appsecret":"a6e899500eca498a7583518d9bdc5caf",
				}
				//console.log("startTime="+startTime+",aid="+aid+",endTime=%s"+endTime);
				 Vue.http.post("http://kf.it.faisco.cn /auth/getToken.action",args, {emulateJSON:true}).then(response => {
		                 if(response.body.statusCode == 200){
		                	 this.aidMsgDialog.token = response.body.data.token;
		                	 this.getCustomService(aid);
		                 }else{
		                	 console.log(response.body.statusCode);
		                	 this.$message({
		 	                    type: 'warning',
		 	                    message: 'token获取有误!'
		 	                });
		                 }
		            }, response => {
		                this.$message({
		                    type: 'warning',
		                    message: '系统错误!'
		                });
		            });
		},
		//判断是否有客服记录
		getCustomService(aid){
			var startTime =localTime.getTime() - 7*24*60*60*1000;
			var endTime = localTime.getTime();
			var args = {
				"aid" : aid,
				"appid":"5ef1e33a54de47f9a7350ac6836989a0",
				"appsecret":"a6e899500eca498a7583518d9bdc5caf",
				"token" :  this.aidMsgDialog.token,
				"startTime":startTime,
				"endTime": endTime,
			 }
			 Vue.http.post("http://kf.it.faisco.cn/auth/hd/getLastTimeAndTalkNum.action",args, {emulateJSON:true}).then(response => {
                 if(response.body.statusCode == 200){
                	 this.aidMsgDialog.lastConsultTime = response.body.data.lastTime;
                	 this.aidMsgDialog.oneWeekTalkNum = response.body.data.oneWeektalknum;
                	 console.log(this.aidMsgDialog.lastConsultTime+","+this.aidMsgDialog.oneWeekTalkNum);
                 }else{
                	 this.$message({
 	                    type: 'warning',
 	                    message: '系统错误!'
 	                });
                 }
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
			 
		},
		showAidMsgDialog(index,dataList){
			//备注信息
            this.aidMsgDialog.show = true;
            this.aidMsgDialog.aidUrl = dataList[index].aidUrl;
			let aid = dataList[index].aid;
			this.aidMsgDialog.loginOneWeekCount =dataList[index].loginOneWeekCount;
            this.aidMsgDialog.optOneWeekCount = dataList[index].optOneWeekCount;
            this.aidMsgDialog.firstCreateStoreTime = dataList[index].firstCreateStoreTime;
            this.aidMsgDialog.index=index;
            //this.getToken(aid);
			//企业信息
            this.getCorpInfo(aid);
		},
		//设置特别关注或者取消关注
		setSpecialFocus(aid,flag){
			var arg={
				"cmd":"setSpecialFocus",
				"aid":aid,
				"flag":flag,
			}
			//alert(aid+","+flag);
			let url = "/ajax/ykSale/ykSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: response.body.msg
                    });
					this.getCorpInfo(aid);
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '关注失败!'
                });
            });
		},
		//提交备注
        submitMark(aid,mark,oldmark){
            if(oldmark == undefined){
            	oldmark = ''
			}
            if (typeof mark == "undefined" || mark == null || mark == ""){
                this.$message({
                    type: 'warning',
                    message: '备注不能为空!'
                });
                return;
            }
            var arg = {
                "cmd":"setInfoMark",
				"aid":aid,
				"mark":mark,
				"oldmark":oldmark
            }
            let url = "/ajax/ykSale/ykSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                    this.aidMsgDialog.editMark = "";
                    this.aidMsgDialog.mark = response.body.data.markName;
                    //alert(this.aidMsgDialog.index);
                    //提交完备注后，如果是按照更新时间排序改index就会变成0，所以将当前index赋值给oldIndex
                    // if(this.form.sortBy.value==-1 || this.form.sortBy.value==0){
                    // 	this.aidMsgDialog.oldIndex=this.aidMsgDialog.index;
                    // 	//alert(this.aidMsgDialog.oldIndex);
                    // }
                    for(var i = 0;i < this.dataList.length;i++){
                        if(this.dataList[i].aid == aid){
                            this.dataList[i].mark = mark;
                        }
                    }
                    //this.getDataListByArgs();
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '添加失败!'
                });
            });

		},
		//初始化标记选项
		initBiaojiInfo(){
            Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkIntentList", "", false).then(result => {
                if (result.success) {
                    this.biaojiDialog.intentList = result.data;
                }
            });
            Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkReasonList", "", false).then(result => {
                if (result.success) {
                    this.biaojiDialog.reasonList = result.data;
                }
            });
		},

		//标记对话框
		showBiaojiBox(aid){
            this.biaojiDialog.show = true;
            this.biaojiDialog.aid = aid;
		},

		//标记窗口关闭
        biaojiClose(){
            this.biaojiDialog.show = false;
            this.biaojiDialog.aid = '';
            this.biaojiDialog.reason = '';
            this.biaojiDialog.intent = '';
            this.biaojiDialog.talkNextTime = '';
            this.biaojiDialog.intentLevel = "";
            this.biaojiDialog.corpMark = "";
		},
		//购买产品窗口关闭
		buyProductDialogClose(){
			this.buyProductDialog.show = false;
		},
		
        closeAidDialog(){
        	this.aidMsgDialog.index = '';
        	this.aidMsgDialog.oldIndex='';
        	//console.log("aid index = ",this.aidMsgDialog.index);
		},

		//提交标记表单
        submitBiaoji(){
            let mark = this.biaojiDialog.corpMark;
            if (typeof mark == "undefined" || mark == null || mark == ""){
                this.$message({
                    type: 'warning',
                    message: '备注不能为空!'
                });
                return;
            }
			
			let intent = this.biaojiDialog.intent;
			
            if (typeof intent != "undefined" && intent != null && intent != ""&& intent==5){
                let reason = this.biaojiDialog.reason;
				if (typeof reason == "undefined" || reason == null || reason == ""){
					this.$message({
						type: 'warning',
						message: '原因不能为空'
					});
					return;
                }
            }
			
		

            var arg = {
                "cmd":"setYkReason",
				"aid": this.biaojiDialog.aid,
				"reason":this.biaojiDialog.reason,
                "intent":this.biaojiDialog.intent,
				"talkNextTime":this.biaojiDialog.talkNextTime,
				"reasonVersionInputValue":this.biaojiDialog.intentLevel,
				"corpMark":this.biaojiDialog.corpMark,
            }

            let url = "/ajax/ykSale/ykSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {

				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: '标记成功!'
                    });
					//更新数据
                    this.getCorpInfo(this.biaojiDialog.aid);
                    //this.getDataListByArgs();
                    //改备注
					for(var i = 0;i < this.dataList.length;i++){
						if(this.dataList[i].aid == this.biaojiDialog.aid){
                            this.dataList[i].mark = this.biaojiDialog.corpMark;
                            this.dataList[i].intent = this.getIntentName(this.biaojiDialog.intent);
						}
					}
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
                this.biaojiClose();
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '网络错误，请重试或联系管理员!'
                });
            });
		},
		
		//查看购买产品
        showBuyProduct(){
			this.buyProductDialog.show = true;
		},

		//aid弹窗切换aid pageData:{
        //currentPage:1,
        //total:0,
        //size:20,
   // },
        changeAid(opt,aid){
			let index = -1;
			let listLength = this.pageData.size -1;
            let totalPage = parseInt(this.pageData.total / this.pageData.size) + 1;

            //如果不是按照更新时间排序就可以用这方法，获取当前aid所在index
        	for(var i = 0;i < this.dataList.length;i++){
                if(this.dataList[i].aid == aid){
					index = i;
					this.aidMsgDialog.index= index +1 ;
                }
            }
           // if((this.form.sortBy.value==-1 || this.form.sortBy.value==0) && this.aidMsgDialog.oldIndex !=''){
        	//     if(this.form.sortBy.value==-1){//倒序
        	//     	if(opt=='up'){
           //      		index=this.aidMsgDialog.oldIndex+1;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex-1;
           //      	}else if(opt='down'){
           //      		index=this.aidMsgDialog.oldIndex;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex+1;
           //      	}
           //      	this.aidMsgDialog.index=index+1;
        	//     }else if(this.form.sortBy.value==0){
        	//     	if(opt=='up'){
           //      		index=this.aidMsgDialog.oldIndex;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex-1;
           //      	}else if(opt='down'){
           //      		index=this.aidMsgDialog.oldIndex-1;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex+1;
           //      	}
        	//     	this.aidMsgDialog.index=index+1;
        	//     }
           //
           //  }else{
           //  	//如果不是按照更新时间排序并且没有提交过备注的就可以用这方法，获取当前aid所在index
           //  	for(var i = 0;i < this.dataList.length;i++){
           //          if(this.dataList[i].aid == aid){
    		// 			index = i;
    		// 			this.aidMsgDialog.index= index +1 ;
           //          }
           //      }
           //  }
           

            //上一个aid
            if(opt == 'up'){
            	if(index == 0){//判断是不是第一页，是的话弹窗提示，不是的话获取上一页的最后一个
					if(this.pageData.currentPage == 1){
                        this.$message({
                            type: 'warning',
                            message: '已经是第一个了!'
                        });
					}else{//翻页
                        //this.pageData.currentPage -= 1;
                        this.$message({
                            type: 'warning',
                            message: '这是本页第一个了!'
                        });
					}
				}else if(index == -1){//翻页后找不到aid将index谁为
                    this.getCorpInfo(this.dataList[listLength].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[listLength].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[listLength].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[listLength].optOneWeekCount;
					this.aidMsgDialog.firstCreateStoreTime = this.dataList[listLength].firstCreateStoreTime;
				}else{ 
            		this.getCorpInfo(this.dataList[index-1].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[index-1].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[index-1].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[index-1].optOneWeekCount;
					this.aidMsgDialog.firstCreateStoreTime = this.dataList[index-1].firstCreateStoreTime;
				}
			}


			//下一个aid
			if(opt == 'down'){
				if(this.pageData.currentPage == totalPage ){ //最后一页情况
                    let tempIndex = index + 1;
					if(this.dataList.length == tempIndex){
                        this.$message({
                            type: 'warning',
                            message: '已经是最后一个了!'
                        });
					}else{
                        this.getCorpInfo(this.dataList[index+1].aid);
                        this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                        this.aidMsgDialog.loginOneWeekCount = this.dataList[index+1].loginOneWeekCount;
                        this.aidMsgDialog.optOneWeekCount = this.dataList[index+1].optOneWeekCount;
						this.aidMsgDialog.firstCreateStoreTime = this.dataList[index+1].firstCreateStoreTime;
					}
				}else if(index == listLength){//最后一个情况
					//获取下一页数据
                   // this.pageData.currentPage += 1;销售说不需要自动翻页
                    this.$message({
                        type: 'warning',
                        message: '已经是本页最后一个啦!'
                    });
				}else{
                    this.getCorpInfo(this.dataList[index+1].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[index+1].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[index+1].optOneWeekCount;
					this.aidMsgDialog.firstCreateStoreTime = this.dataList[index+1].firstCreateStoreTime;
				}
			}

		},

		getIntentName(intent){
            switch(intent)
            {
                case 5:
                    return "无意向";
                    break;
                case 1:
                    return "有意向";
                    break;
                case 6:
                    return "未接通";
                    break;
                case 7:
                    return "待跟进";
                    break;
                case 0:
                    return "空";
                    break;
                case 8:
                    return "仅建站";
                    break;
                default:
                    return "未知";
            }
		},

		handleRemove(data){
            this.editReceiveTimeData.fileId = '';
            this.backToAkuData.fileId = '';
		},
		beforeFileUpload: function (file) {
			if (file.size > 5 * 1024 * 1024) {
				this.$message({
					type: "error",
					message: "单个文件超过5MB！"
				});
				return false;
			}
		},
		fileUploadSuccess: function (response) {
			if(response.success){
				this.$message({
					type: "success",
					message: "上传成功!"
				});
				this.editReceiveTimeData.fileId = response.fileId;
                this.backToAkuData.fileId = response.fileId;
			}
		},
		fileUploadError: function (err) {
			var _this = this;
			this.$message({
				type: "error",
				message: "系统繁忙，请稍后重试!"
			});
		},
		sendMessag(index,dataList){
			this.message.messageList = [];
			var single='';
			//console.log(this.sendMessag.index);
			if( typeof  this.sendMessag.index != "undefined"){
				let index = this.aidMsgDialog.index;
				single = dataList[index];
			}else{
				single=dataList[index];
			}
			var sendTime = single.sendTime;
			if(sendTime){
				if((new Date(sendTime).getTime())/1000 > Fai.tool.getDateTimeStart(new Date())){
					this.$message({
						type: 'warning',
						message: '该aid今天已经发送过短信!'
					});
					return;
				}
			}
			this.message.messageList.push(single);
			this.message.singleMobile = single.mobile;
			this.dialogVisible = true;//展示弹窗
		},
		getMessageModelList(){
			var arg = {
				"cmd":"getMessageModelList",
			}
			Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
				this.message.messageModelList = response.data.dataList;
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		
		
		confirmBatch(type){
			if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
		   }else{
			    var that = this;
				this.$confirm('确定批量释放到【公海库】吗?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				  }).then(() => {
					  that.batchTurnStatus(type);
				  }).catch(() => {
					this.$message({
					  type: 'info',
					  message: '已取消删除'
					});          
				  });
		   }
		},
		
		//aid弹窗释放到公海库按钮,复用ajax的
		releaseResource(aid){
			aid = parseInt(aid);
			var aidList = [];//组装参数
			aidList.push(aid);
			this.$confirm('确定释放该客户?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":"batchRelease",
					"aidList":JSON.stringify(aidList),
					"status":this.tab,
				}
				console.log("aidList="+JSON.stringify(aidList));
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					let type = 'success';
					if(!response.data.success){
						type = 'warning';
					}
					
					this.$message({
						type: type,
						message: response.data.msg
					});
					this.getDataListByArgs();
				}, response => {
					this.$message({
						type: 'warning',
						message: '系统错误！'
					});
				});
			
			  }).catch(() => {
					this.$message({
					    type: 'info',
					    message: '已取消删除'
					});          
			  });
		},
	
		
		batchSendMessag(){
			this.message.singleMobile = "";
			if(this.message.messageList == undefined ||  this.message.messageList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
			}
			this.dialogVisible = true;
		},
		handleSelectionChange(val){
			console.log("val = "+val);
			var data = [];
			for(var i=0;i<val.length;i++){
				var single = val[i];
				var sendTime = single.sendTime;
				if(sendTime){
					var sendTimeSecond = new Date(sendTime).getTime() / 1000;
					var date = new Date();
					var today = Fai.tool.getDateTimeStart(new Date());
					var lastTwoDay = new Date(date.getFullYear(), date.getMonth() + 1, -1).getTime() / 1000;
					if(sendTimeSecond > today){//当天已发送过短信
						this.$message({
							type: 'warning',
							message: "aid:"+single.aid+" 当天已发送过短信",
						});
						continue;
					}
					if( today < lastTwoDay && sendTimeSecond > today - 3 * 24 * 60 * 60   ){//3天内发过短信的不可以再发，月底最后2天除外
						this.$message({
							type: 'warning',
							message: "aid:"+single.aid+" 3天内已发送过短信",
						});
						continue;
					}				
				}
				data.push(single);
			}
			this.message.messageList = data;
		},
		handleMessage(send) {//关闭发送短信的弹窗
			this.dialogVisible = false;
			if(send){//如果确定
				if(this.message.messageModel == ""){
					this.$message({
						type: 'warning',
						message: "尚未选择短信模板！",
					});
					return;
				}
				
				var messageSize = this.message.messageList.length;//发送列表Length
				if(messageSize<=0){
					this.message.singleMobile = "";
					return;
				}
				var realMessageList = [];//组装参数
				if(messageSize>1&&this.message.singleMobile==""){//证明是多选
					for(var index = 0;index<messageSize;index++){
						var message = this.message.messageList[index];
						if(message.mobile==""){
							continue;
						}
						var item = {};
						item.aid = parseInt(message.aid);   //其这里是string
						item.mobile = message.mobile;
						realMessageList.push(item);
					}
				}else{//单个发送
					var message = this.message.messageList[0];
					var item = {};
					item.aid = parseInt(message.aid);
					item.mobile = this.message.singleMobile;
					realMessageList.push(item);
				}
				var arg = {
					"cmd":"batchSendMessage",
					"aidList": JSON.stringify(realMessageList),
					"smsId":this.message.messageModel,
				}
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					if(response.data.success){
						this.$message({
							type: 'success',
							message: response.data.msg
						});
					}else{
						this.$message({
							type: 'warning',
							message: response.data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			}
		},
		changeIntent(aid,intent,salesAcct,mark){
			this.$confirm('确定修改?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":"setInfo",
					"aid":aid,
					"intent":intent,
					"salesAcct":salesAcct,
					"mark":mark,
					"type":"intent",
				}
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					if(response.data.success){
						this.$message({
							type: 'success',
							message: '修改成功!'
						});
						this.getDataListByArgs();
					}else{
						this.$message({
							type: 'warning',
							message: response.data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消修改'
				});          
			  });
		},
		getMessageList(){
			var arg = {
				"cmd":"getSaleList",
				"pageNo":this.message.page,
			}
			Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
				this.message.messageModelList = response.data.dataList;
				this.dialogVisible = true;//展示弹窗
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		getDefList(){
			var arg = {
				"cmd":"getPageDef",
				"key":"ykSaleList"
			}
			Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				             
				var ykSaleList = response.data;
				this.form.spgVersion = ykSaleList.spgVersion;
				this.form.hdVersion = ykSaleList.hdVersion;
				this.form.ykVersion = ykSaleList.ykVersion;
				this.form.regBiz = ykSaleList.regBiz;
				this.form.siteVersion = ykSaleList.siteVersion;
				this.form.intent = ykSaleList.intent;
				this.form.ta = ykSaleList.taList;
				this.form.sortBy = ykSaleList.sortBy;
				this.form.saleGroup = ykSaleList.saleGroup;
				this.form.tag = ykSaleList.tag;
				this.form.intentLevel = ykSaleList.intentLevel;
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		getOtherLibList(param){
            this.changStyle(param);
			this.libTab = param;
            this.getDataListByArgs(param);
		},
		changStyle(param){
			this.textStyle_10.color = "";
            this.textStyle_20.color = "";
            this.textStyle_28.color = "";
            this.textStyle_ban_msg.color = "";
            this.textStyle_follow.color = "";

            if(param == 'noContact10Day'){
                this.textStyle_10.color = "grey";
            }else if (param == 'noContact20Day'){
                this.textStyle_20.color = "grey";
            }else if (param == 'noContact28Day'){
                this.textStyle_28.color = "grey";
            }else if (param == 'follow'){
                this.textStyle_follow.color = "grey";
            }else if (param == 'banSendMsgAid'){
                this.textStyle_ban_msg.color = "grey";
            }
		},
		getDataListByArgs(param){
			var excel = "excel"===param;
			var urlParam = {
				cmd:'getYkSaleList2',
				currentPage:this.pageData.currentPage,
                labelIndex:this.tab,
				aid:this.form.aid,
				province:this.form.province,
				city:this.form.city,
				mark:this.form.mark,
				memberNumStart:this.form.memberNumStart,
				memberNumEnd:this.form.memberNumEnd,
				excel:excel,

				loginTimeCheck:this.form.loginTime.check,
				loginTimeStart:Fai.tool.getDateTimeStart(this.form.loginTime.start),
				loginTimeEnd:Fai.tool.getDateTimeEnd(this.form.loginTime.end),
				
				regTimeCheck:this.form.regTime.check,
				regTimeStart:Fai.tool.getDateTimeStart(this.form.regTime.start),
				regTimeEnd:Fai.tool.getDateTimeEnd(this.form.regTime.end),
				
				lastUpdateTimeCheck:this.form.lastUpdateTime.check,
				lastUpdateTimeStart:Fai.tool.getDateTimeStart(this.form.lastUpdateTime.start),
				lastUpdateTimeEnd:Fai.tool.getDateTimeEnd(this.form.lastUpdateTime.end),
				
				receiveTimeCheck:this.form.receiveTime.check,
				receiveTimeStart:Fai.tool.getDateTimeStart(this.form.receiveTime.start),
				receiveTimeEnd:Fai.tool.getDateTimeEnd(this.form.receiveTime.end),
				
				nextTalkTimeCheck:this.form.nextTalkTime.check,
				nextTalkTimeStart:Fai.tool.getDateTimeStart(this.form.nextTalkTime.start),
				nextTalkTimeEnd:Fai.tool.getDateTimeEnd(this.form.nextTalkTime.end),

				sendMessageTimeCheck:this.form.sendMessageTime.check,
				noSendMessageCheck:this.form.sendMessageTime.noSendCheck,
				sendMessageTimeStart:Fai.tool.getDateTimeStart(this.form.sendMessageTime.start),
				sendMessageTimeEnd:Fai.tool.getDateTimeEnd(this.form.sendMessageTime.end),
				
				firstCreateStoreTimeCheck:this.form.firstCreateStoreTime.check,
				firstCreateStoreTimeStart:Fai.tool.getDateTimeStart(this.form.firstCreateStoreTime.start),
				firstCreateStoreTimeEnd:Fai.tool.getDateTimeEnd(this.form.firstCreateStoreTime.end),
				
				ta:this.form.ta.value,
				
				siteVersion:this.form.siteVersion.value,
				hdVersion:this.form.hdVersion.value,
				spgVersion:this.form.spgVersion.value,
				ykVersion:this.form.ykVersion.value,
				
				//regBiz:this.form.regBiz.value,
				sortBy:this.form.sortBy.value,
				saleGroup:this.form.saleGroup.value,
				receiveSale:this.form.receiveSale.value,
				tag:this.form.tag.value,
				intentLevel:this.form.intentLevel.value,
				intent:this.form.intent.value,
				size:this.pageData.size,
				hasOpenYk:this.form.hasOpenYk.value,
                args: this.libTab
			}
			if(excel){	
				window.location.href =  '/ajax/ykSale/ykSale_h.jsp'+Fai.tool.parseJsonToUrlParam(urlParam,true);
				
			}else{
				
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", urlParam, {emulateJSON:true}).then(response => {
					// success
					this.dataList = response.data.dataList;
					this.logicImgSrc = response.data.logicImgSrc;
					this.form.receiveSale.labelList =  response.data.saleList;
					this.pageData.total = response.data.total;
					//预览最后一条备注
					for(var i=0;i<response.data.dataList.length;i++){
						if(response.data.dataList[i].mark.length>0){
							//this.dataList[i].mark=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							var markTmp=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							if(markTmp.indexOf("-")>0){
								markTmp=markTmp.substring(0,markTmp.indexOf("-"));
							}
							 if(markTmp.charAt(0)==('【')){
								this.dataList[i].mark=markTmp.substring(markTmp.indexOf("】")+1);
							}else{
								this.dataList[i].mark=markTmp;
							}
							if(response.data.dataList[i].intent =='无意向'){
								if(markTmp.indexOf("。")>0){
									markTmp = markTmp.substring(markTmp.indexOf("。")+1);
									this.dataList[i].mark=markTmp;
								}
							}
						}
					}
					
					
					if( this.tab===1){
						this.tabNamePerson = "个人库("+this.pageData.total+")";
					}
                    else if(this.tab===2){
						this.tabNameDeal = "成交库("+this.pageData.total+")";
					}
				}, response => {
					console.info("err");
				});
                
			}
		},
		
		handleSizeChange(){

		},
		handleCheckedNoSendSmsChange(noSendCheck){
			if(noSendCheck && this.form.sendMessageTime.check){
				this.form.sendMessageTime.check = false
			}
		},
		handleCheckedSendSmsChange(sendCheck){
			if(sendCheck && this.form.sendMessageTime.noSendCheck){
				this.form.sendMessageTime.noSendCheck = false
			}
		},
		test(){
			console.log("test");
		},
		// handleCurrentChange(val){
		// 	console.info(val);
		// },
		
		
		//获取销售账号和电话信息
		getSaleMsg(){			
			//post获取销售信息 hdSale_h方法-->getPersonTelInfo
			var arg = {
				"cmd":"getPersonTelInfo",
			}
			Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				var data = response.data;
				console.log(data);
				
				var telInfo = data[data.length-1]						//个人信息
				this.personInfo.nickName = telInfo.nickName;
				this.personInfo.phone = telInfo.phone;
				
				data.pop();
				this.personInfo.modelInfoList = data									//短信模板列表
				
				
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		
		//报错个人电话信息
		setPreSaleTel(){
			var arg = {
				"cmd":"setYkSale",
				"name":this.personInfo.nickName,
				"phone":this.personInfo.phone,
			}
			Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				console.log(response.data.msg);
				if(response.data.msg == '设置成功'){
					this.$message({
					type: 'warning',
					message: '设置成功!'
					});
				}else{
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				}
				
								
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
			
		},
		
		reasonStatTabOnClick(){							//未购买原因标签点击事件，获取销售列表
			var arg = {
				"cmd":"getSidList",
			}
			//请求销售列表
			Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				this.reasonStat.labelList = this.reasonStat.labelList.concat(response.data);
				this.reasonStat.hasGet = 1;					
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		
		getReasonStat(type){							//未购买统计&表格下载
			var arg = {
				"cmd":"getReasonStat",
				"regBegTime":Fai.tool.getDateTimeStart(this.reasonStat.regTime.start),
				"regEndTime":Fai.tool.getDateTimeEnd(this.reasonStat.regTime.end),
				"salesAcct":this.reasonStat.value,
				"type":type
			}
			if(type != ""){
				window.location.href =  '/ajax/ykSale/ykSale_h.jsp'+Fai.tool.parseJsonToUrlParam(arg,true)
			
			}else{
				Vue.http.post("/ajax/ykSale/ykSale_h.jsp", arg, {emulateJSON:true}).then(response => {					
					let data = response.data;
					this.reasonStat.totolList.intentList = data['intentList'];
					this.reasonStat.totolList.markList = data['markList'];
						
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});		
			}
			
		},
		//设置企微提醒时间
		addQWRemind(aid,QWRemindTime,QWRemindContent){
			var arg ={
			     "cmd":"addQWRemind",
			     "aid":aid,
			     "QWRemindTime":Fai.tool.dateFormatter(QWRemindTime.getTime(),"yyyy-MM-dd HH:mm:ss"),
			     "QWRemindContent":QWRemindContent
			}
			let url = "/ajax/ykSale/ykSale_h.jsp";
			Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: '设置成功!'
                    });
                   
				}else{
                    this.$message({
                        type: 'warning',
                        message:response.body.msg
                    });
				}
				 this.aidMsgDialog.QWRemindContent = "";
				 this.aidMsgDialog.QWRemindTime=new Date();
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '添加失败!'
                });
            });
		},
		banSendMsg(aid,flag){
			var arg={
					"cmd":"banSendMsg",
					"aid":aid,
					"flag":flag,
			}
			let url = "/ajax/ykSale/ykSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: response.body.msg
                    });
                    this.getCorpInfo(aid);
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '关注失败!'
                });
            });
		},
		
		
	}
})

// //键盘监听
// document.onkeydown=function(event){
//     var e = event || window.event || arguments.callee.caller.arguments[0];
//     //37 , 39
//     console.log(e.keyCode);
//     if(e.keyCode == 39){
//         // hdSale.pageData.currentPage += 1;
// 	}
//
// };


// window.addEventListener("storage", function(event) {
// 	if(event.key == "preSale-hd"){
// 		hdSale.getDataListByArgs();
// 		console.log("success");
// 	}else{
// 		return;
// 	}
// }, false);
