<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.hdUtil.hostConf.HdBaseHostConf" %>

<%
  if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
    out.println("没有权限");
    return;
  }

  int envMode =  Web.getEnvMode();
  Boolean $isPre = envMode == Web.EnvMode.PRE;
  Boolean $debug = Web.getDebug();

  String ossUrl = "http://o." + HdBaseHostConf.getTopHost(Web.getEnvMode());
  String hdUrl = "https://" + HdBaseHostConf.getHdPortalHost(Web.getEnvMode());
%>

<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>活动列表</title>
  <%=Web.getToken()%>
  <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
  <style>
    [v-cloak] {
      display: none;
    }

    a {
      font-size: 12px;
      text-decoration: none;
    }

    .pageTitle {
      margin-bottom: 20px;
      text-align: center;
    }

    .el-input {
      width: 180px;
    }

    .item {
      display: inline-block;
      margin: 6px;
      white-space: nowrap;
      margin-right: 11px;
    }

    .item_1 {
      display: inline;
    }

    .baseLine {
      margin: 6px;
      white-space: nowrap;
    }

    .gameSale {
      display: inline-block;
    }

    .el-table {
      margin-top: 20px;
      width: 100%;
    }

    .el-button+.el-button {
      margin-left: 0;
    }

    .el-table .cell {
      text-align: center;
    }

    .el-select-dropdown__item span {
      pointer-events: none;
    }

    .el-button--text,
    .el-table .cell,
    .el-table--border td:first-child .cell {
      padding: 0;
    }

    .closeGameDialog {
      top: 50%;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
    }

    .closeGameDialog .el-dialog__body {
      padding: 10px 20px;
    }

    .breadcrumbItemClick {
      color: #409eff;
      cursor: pointer;
    }

    .breadcrumbItemClick:hover {
      color: #66b1ff;
    }

    .el-breadcrumb {
      font-size: 32px;
      margin: 60px 0 35px;
      font-weight: bold;
    }

    .report-title {
      font-size: 28px;
      margin: 28px 0 18px;
    }

    .report-content {
      font-size: 26px;
    }

    .gameInformTable {
      margin: 28px 0;
      padding: 0 28px;
    }

    .el-pagination {
      -webkit-display: flex;
      -moz-display: flex;
      -o-display: flex;
      -ms-display: flex;
      display: flex;
      -webkit-justify-content: center;
      -moz-justify-content: center;
      -o-justify-content: center;
      -ms-justify-content: center;
      justify-content: center;
      color: #606266;
    }

    .el-pagination .btn-next .el-icon,
    .el-pagination .btn-prev .el-icon {
      font-size: 16px;
    }

    .el-pagination li,
    .el-pagination button {
      height: 36px;
      line-height: 36px;
    }

    .el-pagination li {
      font-size: 17px;
    }

    .el-pagination .btn-next,
    .el-pagination .btn-prev,
    .el-pager li.btn-quicknext,
    .el-pager li.btn-quickprev {
      color: #606266;
    }

    .previewBox {
      padding: 20px;
      text-align: center;
    }

    .previewBox .linkPicBox {
      display: inline-block;
    }

    .previewBox .linkPicBox img {
      width: 200px;
      height: 200px;
    }

    .previewBox .linkPicBox .downCode {
      width: 100%;
      color: #4381FD;
      margin-left: 28px;
      cursor: pointer;
      text-decoration: underline;
    }

    .previewBox .linkPicBox .showLink {
      text-align: center;
      margin-bottom: 10px;
    }

    .previewBox .linkPicBox .showLink .linkSite {
      word-wrap: break-word;
      word-break: break-all;
      white-space: initial;
      line-height: 1;
      margin: 10px 0;
    }

    .previewBox .linkPicBox .showLink .el-button {
      width: 200px;
      margin-top: 10px;
    }

    .previewBox .linkPicBox .showLink .el-button span {
      width: 100%;
    }
  </style>
</head>
<body>
  <div id="app" v-cloak>
    <div v-show="!showReport">
      <div id="pageTitle" class="pageTitle">活动列表</div>
      <div id="formWrap">
        <div>
          <game-list-collect
            v-for="(item, key) in info1"
            :key="key"
            :info="item"
            v-model="item.model"
          />
        </div>

        <div>
          <game-list-collect
            v-for="(item, key) in info2"
            :key="key"
            :info="item"
            v-model="item.model"
          />
        </div>
        <div class="item">
          <el-input
            size="mini"
            type="text"
            v-model="otherSearch.searchWord"
            placeholder="请输入搜索内容"
          ></el-input>
          <el-button
            type="primary"
            size="mini"
            @click="getGameList('reset')"
          >更新</el-button>
        </div>

        <div class="item">
          状态：{{searchUseTime}}
        </div>
      </div>
      <br>
      <div id="table-div">
        <span>当前页筛选中的游戏数据汇总：</span>
        <span>
          浏览人数总计：{{gameList.data.totalView}} ，玩家人数总计：{{gameList.data.totalPlayer}} ，分享次数总计：{{gameList.data.totalShare}}。
        </span>
        <br />
        <span>
          共{{gameList.data.gameCount}}个活动　 当前页共{{gameList.data.accCount}}个账号
          共{{gameList.data.pageCount}}页　当前第{{otherSearch.pageNo}}页。　每页最多展示100个活动
        </span>
        <a
          v-if="isSearch"
          href="javascript:void(0)"
          @click="otherSearch.pageNo !== 1 && getGameList(-1)"
        >上一页</a>
        <a
          v-if="isSearch"
          href="javascript:void(0)"
          @click="otherSearch.pageNo !== gameList.data.pageCount && getGameList(1)"
        >下一页</a>
      </div>
    </div>
    <template v-if="showReport">
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-breadcrumb-item>
          <span class="breadcrumbItemClick" @click="showReport = false; tableData = gameList.list;">活动列表</span>
        </el-breadcrumb-item>
        <el-breadcrumb-item>
          <span>举报内容</span>
        </el-breadcrumb-item>
      </el-breadcrumb>
    </template>
    <el-table :data="tableData" border>
      <el-table-column prop="id" min-width="35" label="游戏编号"></el-table-column>
      <el-table-column prop="aid" min-width="50" label="aid">
        <a slot-scope="scope" :href="'<%=ossUrl%>/index.jsp?t=cs&u=/cs/corp.jsp?aid=' + scope.row.aid" target="_blank" class="el-button--text">{{scope.row.aid}}</a>
      </el-table-column>
      <el-table-column prop="agentAid" min-width="50" label="agentAid">
        <a slot-scope="scope" :href="'<%=ossUrl%>/index.jsp?t=cs&u=/agent/agent.jsp?aid=' + scope.row.agentAid" target="_blank" class="el-button--text">{{scope.row.agentAid}}</a>
      </el-table-column>
      <el-table-column prop="acct" min-width="50" label="账号">
        <a slot-scope="scope" :href="'<%=hdUrl%>?__tmpaid=' + scope.row.aid + '&hd_r=bXlBY3RpdmU'" target="_blank" class="el-button--text">{{scope.row.acct}}</a>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in tableColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        :min-width="item.minWidth ? item.minWidth : '50'"
      ></el-table-column>
      <el-table-column fixed="right" label="活动版本" min-width="120">
        <template slot-scope="scope">
          <div>{{scope.row.gameVersion}}</div>
          <el-button type="text" size="small" @click="handleCloseGame(scope.row, 1)">
            {{scope.row.isClose ? '恢复(内容违规)' : '关闭(内容违规)'}}
          </el-button>
          <el-button type="text" size="small" @click="handleCloseGame(scope.row, 2)">
            {{scope.row.closeForPush ? '恢复(推送域名)' : '关闭(推送域名)'}}
          </el-button>
          <el-button type="text" size="small" @click="handleWhitelistShare(scope.row)">
            {{scope.row.isWhitelistShare ? '移出白名单' : '加入白名单'}}
          </el-button>
          <el-button type="text" size="small" @click="handleSetTipIllage(scope.row)">
            {{scope.row.isTipIllegal ? '关闭提示' : '提示违规'}}
          </el-button>
          <el-button type="text" size="small" @click="handleCloseFx(scope.row)">
            {{scope.row.isCloseFx ? '恢复展示' : '禁止展示'}}
          </el-button>
          <a :href="scope.row.link" target="_blank" class="el-button--text">游戏链接</a>
          <el-button type="text" size="small" @click="handleToDiscoveryGameList(scope.row)">
            {{discoveryGameIdList.includes(scope.row.aid + '-' + scope.row.id) ? '移出券宝' : '移入券宝'}}</el-button>
          <el-button type="text" size="small" v-if="!showReport" @click="handleGameInform(scope.row)">举报内容</el-button>
          <el-button type="text" size="small" @click="JSON.parse(scope.row.setting).importFilter == 0 ? handleImportCaseFilter(scope.row) : getGameLinkandCode(scope.row)">
            {{JSON.parse(scope.row.setting).importFilter == 0 ? '导入案例筛选库' : '查看复制案例链接'}}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template v-if="showReport">
      <div class="report-title">举报内容</div>
      <div class="report-content">
        举报内容共{{gameInform.count.all}}条, 诱导分享共{{gameInform.count.induced_sharing}}条
        虚假活动共{{gameInform.count.false_activities}}条 政治敏感共{{gameInform.count.political_sensitivity}}条
        色情低俗共{{gameInform.count.pornography}}条 谣言共{{gameInform.count.rumor}}条 其他共{{gameInform.count.other}}条
      </div>
      <div class="gameInformTable">
        <el-table :data="gameInform.list" border>
          <el-table-column
            v-for="(item, index) in [{ prop: 'informRea', label: '投诉原因', width: 4 }, { prop: 'detail', label: '投诉内容', width: 7 }]"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.width"
          />
        </el-table>
      </div>
      <el-pagination
        v-if="gameInform.listCount"
        @current-change="gameInformChange"
        layout="prev, pager, next"
        :total="gameInform.listCount"
      />
    </template>
    <el-dialog
      top="0"
      width="25%"
      title="请设置关闭原因:"
      custom-class="closeGameDialog"
      :visible.sync="closeGameDialog"
    >
      <el-select v-model="closeGameReason">
        <el-option
          v-for="(item, index) in ['政治', '色情', '犯罪', '欺诈', '低俗', '暴恐', '广告', '赌博', '谣言', '可疑', '诱导分享', '其他']"
          :key="index"
          :label="item"
          :value="item"
        />
      </el-select>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="closeGameDialog = false; $message('已取消');">取 消</el-button>
        <el-button style="margin-left: 5px;" size="mini" type="primary" @click="handleCloseGame2">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      width="30%"
      title="复制后"
      :visible.sync="previewBox.showPoup"
    >
      <div class="previewBox">
        <div class="item">
          <div class="linkPicBox">
            <img class="qrcodeImage" :src="previewBox.gameCode">
            <div class="wxScan">
              <span class="scan">微信扫一扫</span>
              <span class="downCode" @click="downCode">下载二维码</span>
              <div class="showLink">
                <div ref="copy" id="code" class="linkSite">{{previewBox.gameLink}}</div>
                <el-button class="copy-btn pointer" @click="copyCode">
                  复制链接
                </el-button>
              </div>
            </div>
            <div>案例活动复制成功，可扫码体验</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</body>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
<script>
  var $targetAid = <%=$debug%> ? 9871760 : <%=$isPre%> ? 26942413 : 24265546;

  var gameListCollect = {
    name: 'gameListCollect',
    template: (
      '<div :class="info.class || \'item\'">' +
        '<template v-if="Array.isArray(info.type)">' +
          '<template v-for="i in info.model.length">' +
            '{{info.describe && info.describe[i-1]}}<el-input ' +
              ':key="i" ' +
              'v-if="info.type[i-1] === \'input\'" ' +
              'size="mini" ' +
              'type="text" ' +
              'v-model="info.model[i-1]" ' +
              '@input="handleChange($event, i-1)"' +
            '/>' +
            '<el-select ' +
              ':key="i" ' +
              'v-else-if="(!info.hide || !info.hide[i-1]) && info.type[i-1] === \'select\' && info.option[i-1] && info.option[i-1].length" ' +
              ':multiple="(info.multiple && info.multiple[i-1]) ? info.multiple[i-1] : false" ' +
              ':clearable="(info.clearable && info.clearable[i-1]) ? info.clearable[i-1] : false" ' +
              'size="mini" ' +
              'value-key="id" ' +
              'placeholder="请选择" ' +
              'v-model="info.model[i-1]" ' +
              '@change="handleChange($event, i-1)"' +
            '>' +
              '<el-option ' +
                'v-for="(item, index) in info.option[i-1]" ' +
                ':label="item.name" ' +
                ':value="item.id" ' +
                ':key="index" ' +
                ':data-index="index"' +
              '/>' +
            '</el-select>' +
            '<el-date-picker ' +
              ':key="i" ' +
              'v-else-if="info.type[i-1] === \'datePicker\'" ' +
              'size="mini" ' +
              'type="daterange" ' +
              'format="yyyy-MM-dd" ' +
              'value-format="yyyy-MM-dd" ' +
              'start-placeholder="始" ' +
              'end-placeholder="止" ' +
              'v-model="info.model[i-1]" ' +
              '@change="handleChange($event, i-1)"' +
            '/>' +
          '</template>' +
        '</template>' +

        '<template v-else>' +
          '{{info.describe && info.describe}}<el-input ' +
            'v-if="info.type === \'input\'" ' +
            'size="mini" ' +
            'type="text" ' +
            'v-model="info.model" ' +
            'value="0" ' +
            '@input="handleChange"' +
          '/>' +
          '<el-select ' +
            'v-else-if="!info.hide && info.type === \'select\'" ' +
            ':multiple="info.multiple" ' +
            ':clearable="info.clearable" ' +
            'size="mini" ' +
            'value-key="id" ' +
            'placeholder="请选择" ' +
            'v-model="info.model" ' +
            '@change="handleChange"' +
          '>' +
            '<el-option ' +
              'v-for="(item, index) in info.option" ' +
              ':label="item.name" ' +
              ':value="item.id" ' +
              ':key="index" ' +
              ':data-index="index"' +
            '/>' +
          '</el-select>' +
          '<el-date-picker ' +
            'v-else-if="info.type === \'datePicker\'" ' +
            'size="mini" ' +
            'type="daterange" ' +
            'format="yyyy-MM-dd" ' +
            'value-format="yyyy-MM-dd" ' +
            'start-placeholder="始" ' +
            'end-placeholder="止" ' +
            'v-model="info.model" ' +
            '@change="handleChange"' +
          '/>' +
        '</template>' +
      '</div>'
    ),
    props: ['value', 'info'],
    methods: {
      handleChange: function (val, index) {
        var _index = event.target.dataset ? event.target.dataset.index : '';
        if (typeof index === 'undefined') {
          this.$emit('input', val);
          this.info.change && this.info.change(val, _index);
        } else {
          var value = JSON.parse(JSON.stringify(this.value));
          value[index] = val;
          this.$emit('input', value);
          this.info.change && this.info.change[index] && this.info.change[index](val, _index);
        }
      }
    },
    data: function () {
      return {
        model: this.value
      }
    }
  }

  var tableHandleOpts;

  var app = new Vue({
    el: '#app',
    components: {
      gameListCollect: gameListCollect
    },
    data: function () {
      return {
        info1: {
          aid: {
            model: 0,
            type: 'input',
            describe: 'aid：',
            search: 'aid'
          },
          agentAid: {
            model: 0,
            type: 'input',
            describe: 'agentAid：',
            search: 'agentAid'
          },
          timetypeActivityDate: {
            describe: ['', ' '],
            model: [1, []],
            type: ['select', 'datePicker'],
            option: [[{
              id: 1,
              name: '创建时间'
            }, {
              id: 2,
              name: '开始时间'
            }, {
              id: 3,
              name: '结束时间'
            }]],
            search: ['timetype'],
            change: ['', this.activityDateChange]
          },
          gstyle: {
            model: -1,
            type: 'select',
            describe: '游戏类型： ',
            search: 'gstyle',
            option: [{
              id: -1,
              name: '全部'
            }]
          },
          view: {
            model: 0,
            type: 'input',
            describe: '浏览人数>=',
            search: 'view'
          },
          play: {
            model: 0,
            type: 'input',
            describe: '玩家人数>=',
            search: 'play'
          },
          play1: {
            model: -1,
            type: 'input',
            describe: '玩家人数<=',
            search: 'play1'
          },
          share: {
            model: 0,
            type: 'input',
            describe: '分享次数>=',
            search: 'share'
          },
          inform: {
            model: 0,
            type: 'input',
            describe: '举报人数>=',
            search: 'inform'
          },
          gameId: {
            model: 0,
            type: 'input',
            describe: '活动号：',
            search: 'gameId'
          },
          indexReplaceNum: {
            model: 0,
            type: 'input',
            describe: '首页替换量>=',
            search: 'indexReplaceNum'
          },
          otherReplaceNum: {
            model: 0,
            type: 'input',
            describe: '其他页替换量>=',
            search: 'otherReplaceNum'
          },
          gplatform: {
            model: -1,
            type: 'select',
            describe: '游戏平台： ',
            option: [{
              id: -1,
              name: '全部'
            }, {
              id: 0,
              name: '直销'
            }, {
              id: 1,
              name: '分销'
            }],
            search: 'gplatform',
          },
          saleCover: {
            model: -1,
            type: 'select',
            describe: '销售成交客户： ',
            option: [{
              id: -1,
              name: '不限'
            }],
            search: 'saleCover',
          },
          transactDate: {
            model: [],
            type: 'datePicker',
            describe: '销售成交时间： ',
            change: this.transactDateChange
          },
          pageLimit: {
            model: 100,
            type: 'input',
            describe: '分页数=',
            search: 'pageLimit'
          },
        },
        info2: {
          gstatus: {
            model: -1,
            type: 'select',
            describe: '游戏状态： ',
            option: [{
              id: -1,
              name: '全部'
            }, {
              id: 0,
              name: '未发布'
            }, {
              id: 1,
              name: '未开始'
            }, {
              id: 2,
              name: '进行中'
            }, {
              id: 3,
              name: '已结束'
            }],
            search: 'gstatus',
          },
          gbuy: {
            model: -1,
            type: 'select',
            describe: '游戏版本： ',
            option: [{
              id: -1,
              name: '全部'
            }, {
              id: 0,
              name: '免费版'
            }, {
              id: 1,
              name: '铂金版'
            }, {
              id: 2,
              name: '白银版'
            }, {
              id: 3,
              name: '门店版'
            }, {
              id: 4,
              name: '钻石版'
            }],
            search: 'gbuy',
          },
          openPurpose: {
            model: -1,
            type: 'select',
            describe: '开通互动用途： ',
            option: [{
              id: -1,
              name: '全部'
            }, {
              id: 0,
              name: '微信吸粉'
            }, {
              id: 1,
              name: '门店引流'
            }, {
              id: 2,
              name: '电商引流'
            }, {
              id: 3,
              name: '现场活动'
            }, {
              id: 4,
              name: '品牌传播'
            }, {
              id: 5,
              name: '其他'
            }, {
              id: 6,
              name: '没有选择,未知'
            }, {
              id: 7,
              name: '学生'
            }],
            search: 'openPurpose',
          },
          key1: {
            model: -1,
            type: 'select',
            describe: '节日： ',
            option: [{
              id: -1,
              name: '全部'
            }],
            search: 'key1',
          },
          key2: {
            model: -1,
            type: 'select',
            describe: '类型： ',
            option: [{
              id: -1,
              name: '全部'
            }],
            search: 'key2',
          },
          key3: {
            model: -1,
            type: 'select',
            describe: '游戏营销： ',
            option: [{
              id: -1,
              name: '全部'
            }],
            search: 'key3',
          },
          acctTrade: {
            model: -1,
            type: 'select',
            describe: '客户行业： ',
            option: [{
              id: -1,
              name: '全部'
            }],
            search: 'acctTrade',
          },
          trade: {
            class: 'baseLine',
            clearable: [true, true],
            model: ['', ''],
            type: ['select', 'select'],
            describe: ['行业分类：（仅行业模板需要填写）： ', ' '],
            option: [[{
              id: 0,
              name: '无'
            }]],
            search: ['trade', 'trade2'],
            change: [this.tradeChange]
          },
          category: {
            class: 'baseLine',
            clearable: [true, true],
            model: ['', ''],
            option: [[{
              name: '无',
              id: 0
            }, {
              name: '抽奖游戏',
              id: 0x1
            }, {
              name: '裂变引流',
              id: 0x2
            }, {
              name: '商业促销',
              id: 0x4
            }, {
              name: '长期活动',
              id: 0x8
            }, {
              name: '投票活动',
              id: 0x10
            }, {
              name: '答题活动',
              id: 0x20
            }, {
              name: '现场活动',
              id: 0x80
            },{
              name: '红包活动',
              id: 0x400
            }], [{
              name: '纯抽奖',
              id: 0x10000
            }, {
              name: '益智类活动',
              id: 0x20000
            }, {
              name: '动作类活动',
              id: 0x40000
            }, {
              name: '手速类活动',
              id: 0x80000
            }, {
              name: '消除类活动',
              id: 0x100000
            }, {
              name: '接物类活动',
              id: 0x200000
            }, {
              name: '跳跃类活动',
              id: 0x400000
            }, {
              name: '反应类活动',
              id: 0x800000
            }]],
            type: ['select', 'select'],
            hide: [false, true],
            describe: ['活动分类（仅原型模板需选）： ', ' '],
            change: [this.category1Change, this.category2Change]
          },
          sceneValue: {
            class: 'baseLine',
            multiple: true,
            clearable: true,
            model: '',
            type: 'select',
            describe: '场景分类（仅原型模板需选）： ',
            option: [{
              name: '品牌传播',
              id: 0x1
            }, {
              name: '获客拉新',
              id: 0x2
            }, {
              name: '付费转化',
              id: 0x4
            }, {
              name: '留存活跃',
              id: 0x8
            }],
            change: this.sceneValueChange
          },
        },
        otherSearch: {
          cmd: 'getGameListByArgsNew',
          whichUpdate: 0,
          pageNo: 1,
          begDate: '',
          endDate: '',
          begPay: '',
          endPay: '',
          category1: '',
          category2: '',
          searchWord: ''
        },
        tableColumn: [{
          prop: 'companyName',
          label: '企业名称',
          minWidth: '80'
        }, {
          prop: 'key1',
          label: '节日',
        }, {
          prop: 'key2',
          label: '类型',
        }, {
          prop: 'key3',
          label: '玩法',
        }, {
          prop: 'indexReplaceNum',
          label: '首页替换量',
        }, {
          prop: 'otherReplaceNum',
          label: '其他页替换量',
        }, {
          prop: 'directpurpose',
          label: '互动开通用途',
        }, {
          prop: 'name',
          label: '活动名称',
          minWidth: '100',
        }, {
          prop: 'gameStyleName',
          label: '游戏类型',
        }, {
          prop: 'status',
          label: '游戏状态',
        }, {
          prop: 'view',
          label: '浏览人数',
        }, {
          prop: 'player',
          label: '玩家人数',
        }, {
          prop: 'share',
          label: '分享次数',
        }, {
          prop: 'reportNum',
          label: '举报人数',
        }, {
          prop: 'createTime',
          label: '创建时间',
        }, {
          prop: 'startTime',
          label: '开始时间',
        }, {
          prop: 'endTime',
          label: '结束时间',
        }],
        searchUseTime: '',
        discoveryGameIdList: [],
        gameList: {
          data: {
            totalView: 0,
            totalPlayer: 0,
            totalShare: 0,
            gameCount: 0,
            accCount: 0,
            pageCount: 0
          },
          list: []
        },
        isSearch: false, // 是否已经搜过
        closeGameReason: '政治',
        closeGameDialog: false,
        showReport: false,
        tableData: [],
        gameInform: {
          count: {},
          list: [],
          listCount: 0
        },
        previewBox: {
          showPoup: false,
          gameCode: '',
          gameLink: ''
        }
      }
    },
    created: function () {
      var start = this.formatDate(Date.now() - 9 * 86400000);
      var end = this.formatDate(Date.now());
      this.info1.timetypeActivityDate.model.splice(1, 1, [start, end]);
      this.info1.transactDate.model = [start, end];

      this.activityDateChange([start, end]);
      this.transactDateChange([start, end]);

      this.initInfo();
    },
    methods: {
      get: function (url, fn) {
        var that = this;
        $.get(url).then(function (res) {
          if (!res.success) {
            console.warn(url);
            return that.$message.error(res.msg);
          };
          fn(res);
        }).fail(function (res) {
          console.warn(url);
          try {
            that.$message.error(JSON.parse(res.responseText).errors);
          } catch (err) {
            that.$message.error(res.statusText);
          }
        });
      },
      post: function (url, data, opts) {
        var that = this;
        var _data = {
          type: 'post',
          url: url,
          data: data
        }
        for (var key in opts) {
          _data[key] = opts[key]
        }

        return $.ajax(_data).then(function (res) {
          if (!res.success) {
            that.$message.error(res.msg);
            return $.Deferred().fail();
          }

          return res;
        }).fail(function (res) {
          try {
            that.$message.error(JSON.parse(res.responseText).errors);
          } catch (err) {
            that.$message.error(res.statusText);
          }
        });
      },
      confirm: function (tips) {
        var that = this;
        return $.Deferred(function (defer) {
          that.$confirm(tips, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            callback: function (action, instance) {
              if (action === 'confirm') {
                defer.resolve();
              } else {
                that.$message('已取消');
              }
            }
          })
        })
      },
      confirmPost: function (opts) {
        var that = this;
        return this.confirm('确认' + opts.tips + '？').then(function () {
          return that.post(opts.url, opts.data).then(function (res) {
            that.$message.success('设置成功');
            return res;
          });
        })
      },
      getCookie: function (name) {
        var arr = document.cookie.match(new RegExp('(^| )' + name + '=([^;]*)(;|$)'));
        if (!arr) return null;

        return encodeURIComponent(arr[2]);
      },
      initInfo: function () {
        var that = this;
        this.get('/api/activityList/getAllGameStyleList', function (res) {
          that.info1.gstyle.option.push.apply(that.info1.gstyle.option, res.data);
        });

        this.get('/api/activityList/getSale', function (res) {
          that.info1.saleCover.option.push.apply(that.info1.saleCover.option, res.data);
        });

        this.get('/api/activityList/getFestival', function (res) {
          that.info2.key1.option.push.apply(that.info2.key1.option, res.data);
        });

        this.get('/api/activityList/getType', function (res) {
          that.info2.key2.option.push.apply(that.info2.key2.option, res.data);
        });

        this.get('/api/activityList/getGameMarket', function (res) {
          that.info2.key3.option.push.apply(that.info2.key3.option, res.data);
        });

        this.get('/api/activityList/getAcctTrade', function (res) {
          that.info2.acctTrade.option.push.apply(that.info2.acctTrade.option, res.data);
          that.info2.trade.option[0].push.apply(that.info2.trade.option[0], res.data);
        });
      },
      getInfoData: function (obj, info) {
        for (var key in info) {
          var item = info[key];

          if (Array.isArray(item.type) && item.search) {
            for (var i = 0; i < item.search.length; i++) {
              item.search[i] && (obj[item.search[i]] = item.model[i]);
            }
          } else {
            item.search && (obj[item.search] = item.model);
          }
        }
      },
      getGameList: function (num) {
        if (num === 'reset') {
          this.otherSearch.pageNo = 1;

          this.get('/api/activityList/getAllDiscoveryGame', function (res) {
            that.discoveryGameIdList = res.data;
          });
        } else {
          this.otherSearch.pageNo += num;
        }

        var data = {};
        this.getInfoData(data, this.info1);
        this.getInfoData(data, this.info2);

        for (var key in this.otherSearch) {
          var item = this.otherSearch[key];

          data[key] = item;
        }

        this.searchUseTime = '查询中';

        var that = this;
        $.ajax({
          type: 'post',
          url: '/api/activityList/getGameListByArgs',
          contentType: 'application/json',
          data: JSON.stringify(data)
        }).then(function (res) {
          if (!res.success) {
            that.searchUseTime = '错误！';
            return
          }
          var data = res.data;
          that.gameList.data = data.data || {
            totalView: 0,
            totalPlayer: 0,
            totalShare: 0,
            gameCount: 0,
            accCount: 0,
            pageCount: 0
          };

          if (that.gameList.data.pageCount > 1) {
            that.isSearch = true;
          } else {
            that.isSearch = false;
          }

          if (!data.list || !data.list.length) {
            that.searchUseTime = '没有数据';

            that.gameList.list = [];
            that.tableData = [];
            return
          }

          that.searchUseTime = '查询完成';

          for (var i = 0; i < data.list.length; i++) {
            var item = data.list[i];
            item.createTime = that.formatDate(item.createTime);
            item.startTime = that.formatDate(item.startTime);
            item.endTime = that.formatDate(item.endTime);
            item.directpurpose = that.openPurpose(item.directpurpose);
            item.gameVersion = that.getGameVersion(item);
            item.isClose = that.checkBit(item.flag, <%=HdGameDef.Flag.CLOSE%>);
            item.closeForPush = that.checkBit(item.flagB, <%=HdGameDef.FlagB.CLOSE_FOR_PUSH%>);
            item.isWhitelistShare = that.checkBit(item.flagB, <%=HdGameDef.FlagB.WHITELIST_SHARE%>);
            item.isTipIllegal = that.checkBit(item.flagB, <%=HdGameDef.FlagB.IS_TIP_ILLEGAL%>);
            item.isCloseFx = that.checkBit(item.flag, <%=HdGameDef.Flag.CLOSEFX%>);
          }

          that.gameList.list = data.list;
          that.tableData = that.gameList.list;
        }).fail(function () {
          that.searchUseTime = '错误！';
        });
      },
      //获取当前时间，格式YYYY-MM-DD
      formatDate: function (date) {
        date = new Date(date);
        var seperator = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
          month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
          strDate = "0" + strDate;
        }
        var currentdate = year + seperator + month + seperator + strDate;
        return currentdate;
      },
      openPurpose: function (purposeIndex) {
        switch (purposeIndex) {
          case 0:
          case 100:
            return '微信吸粉';
          case 1:
          case 101:
            return '门店引流';
          case 2:
          case 102:
            return '电商引流';
          case 103:
          case 3:
            return '现场活动';
          case 104:
          case 4:
            return '品牌传播';
          case 5:
          case 105:
            return '其他';
          case 6:
          case 106:
            return '没有选择,未知';
          case 7:
          case 107:
            return '学生';
          default:
            return '没有选择,未知';
        }
      },
      getGameVersion: function (item) {
        if (this.checkBit(item.flagB, <%=HdGameDef.FlagB.IS_QUICKLY_CREATE%>)) {
          if (item.sub_mobiVer === 1) {
            return '移动端高级版'
          } else {
            return '免费版'
          }
        }
        switch (item.ver) {
          case 0:
            return '免费版'
          case 1:
            return '铂金版'
          case 2:
            return '白银版'
          case 3:
            return '门店版'
          case 4:
            return '钻石版'
          default:
            return ''
        }
      },
      checkBit: function (flag, bit) {
        return (flag & bit) == bit;
      },
      // change
      activityDateChange: function (val) {
        this.otherSearch.begDate = val[0];
        this.otherSearch.endDate = val[1];
      },
      transactDateChange: function (val) {
        this.otherSearch.begPay = val[0];
        this.otherSearch.endPay = val[1];
      },
      sceneValueChange: function (val) {
        var num = 0;
        for (var i = 0; i < val.length; i++) {
          num += val[i]
        }
        this.otherSearch.category2 = num;
      },
      category1Change: function (val) {
        if (val === 0x1) {
          this.info2.category.hide[1] = false;
        } else {
          this.info2.category.hide[1] = true;
        }
        this.otherSearch.category1 = val;
      },
      category2Change: function (val) {
        this.otherSearch.category1 = val + 0x1;
      },
      tradeChange: function (val, index) {
        this.info2.trade.model[1] = '';
        if (!isNaN(parseInt(val)) && this.info2.trade.option[0][index].tradeList2 && this.info2.trade.option[0][index].tradeList2.length) {
          this.info2.trade.option[1] = this.info2.trade.option[0][index].tradeList2;
        } else {
          this.info2.trade.option[1] = [];
        }
      },
      // 点击操作
      handleCloseGame: function (opts, closeType) {
        var that = this;
        var bol = closeType === 1 ? opts.isClose : opts.closeForPush;
        this.confirm('确认' + (bol ? '恢复' : '关闭') + '游戏？').then(function () {
          tableHandleOpts = opts;
          tableHandleOpts.closeType = closeType;

          if (bol) {
            that.handleCloseGame2();
          } else {
            that.closeGameReason = '政治';
            that.closeGameDialog = true;
          }
        });
      },
      handleCloseGame2: function () {
        var that = this;
        this.closeGameDialog = false;

        this.post('/api/activityList/closeGame', {
          aid: tableHandleOpts.aid,
          gameId: tableHandleOpts.id,
          close: tableHandleOpts.closeType === 1 ? !tableHandleOpts.isClose : !tableHandleOpts.closeForPush,
          closeType: tableHandleOpts.closeType,
          reason: that.closeGameReason
        }).then(function (res) {
          that.$message.success('设置成功');
          if (tableHandleOpts.closeType === 1) {
            tableHandleOpts.isClose = !tableHandleOpts.isClose;
          } else if (tableHandleOpts.closeType === 2) {
            tableHandleOpts.closeForPush = !tableHandleOpts.closeForPush;
          }
        });
      },
      handleWhitelistShare: function (opts) {
        this.confirmPost({
          tips: (opts.isWhitelistShare ? '移出' : '加入') + '白名单',
          url: '/api/activityList/whitelistShare',
          data: {
            aid: opts.aid,
            gameId: opts.id,
            whitelist: !opts.isWhitelistShare
          }
        }).then(function (res) {
          opts.isWhitelistShare = !opts.isWhitelistShare;
        });
      },
      handleSetTipIllage: function (opts) {
        this.confirmPost({
          tips: opts.isTipIllegal ? '关闭提示' : '提示违规',
          url: '/api/activityList/setTipIllage',
          data: {
            aid: opts.aid,
            gameId: opts.id,
            operation: !opts.isTipIllegal ? 'illegal' : ''
          }
        }).then(function () {
          opts.isTipIllegal = !opts.isTipIllegal;
        })
      },
      handleCloseFx: function (opts) {
        this.confirmPost({
          tips: (opts.isCloseFx ? '恢复' : '禁止') + '游戏在发现活动中展示',
          url: '/api/activityList/closeFx',
          data: {
            aid: opts.aid,
            gameId: opts.id,
            close: !opts.isCloseFx
          }
        }).then(function () {
          opts.isCloseFx = !opts.isCloseFx;
        })
      },
      handleToDiscoveryGameList: function (opts) {
        var that = this;
        var bol = this.discoveryGameIdList.includes(opts.aid + '-' + opts.id);
        this.confirmPost({
          tips: (bol ? '移出' : '移入') + '券宝',
          url: '/api/activityList/' + (bol ? 'delFromDiscoveryGameList' : 'addToDiscoveryGameList'),
          data: {
            aid: opts.aid,
            gameId: opts.id,
            type: 1
          }
        }).then(function () {
          if (bol) {
            for (var i = 0; i < that.discoveryGameIdList.length; i++) {
              if (that.discoveryGameIdList[i] === opts.aid + '-' + opts.id) {
                that.discoveryGameIdList.splice(i, 1)
                break
              }
            }
          } else {
            that.discoveryGameIdList.push(opts.aid + '-' + opts.id);
          }
        })
      },
      handleGameInform: function (opts) {
        this.showReport = true;
        this.tableData = [opts];
        var that = this;

        this.post('/api/activityList/getGameInformCount', {
          aid: opts.aid,
          gameId: opts.id,
        }).then(function (res) {
          var obj = {};
          for (var i = 0; i < res.data.list.length; i++) {
            var item = res.data.list[i];
            obj[item.name] = item.count;
          }

          that.gameInform.count = obj;
        });

        tableHandleOpts = opts;

        this.gameInformChange(1);
      },
      gameInformChange: function (val) {
        var that = this;

        this.post('/api/activityList/getGameInformList', {
          aid: tableHandleOpts.aid,
          gameId: tableHandleOpts.id,
          page: val,
          limit: 10
        }).then(function (res) {
          that.gameInform.list = res.data.list;
          that.gameInform.listCount = res.data.count;
        });
      },
      handleImportCaseFilter: function (opts) {
        this.previewBox.gameCode = '';
        this.previewBox.gameLink = '';
        var that = this;
        var _data = {};

        this.post('/hdProduct/hdGameCopy_h.jsp?cmd=copyActive&sourceID=' + (opts.aid + 'HD' + opts.id) + '&targetAid=' + 9871760 + '&fromHdOss=true&identify=1', {
          emulateJSON: true
        }, {
          dataType: 'json'
        }).then(function (res) {
          _data.gameCode = res.gameCode;
          _data.gameUrl = res.gameLink;

          return that.post('/api/caseFilter/importCaseFilter', JSON.stringify({
            oAid: opts.aid,
            oGameId: opts.id,
            oAcctName: opts.companyName,
            gameName: opts.gameStyleName,
            caseAid: 9871760,
            gameId: res.targetGameId,
            gameUrl: res.gameLink,
            gameCode: res.gameCode,
            createPerson: that.getCookie('loginSacct')
          }), {
            dataType: "json",
            contentType: 'application/json',
          })
        }).then(function (res) {
          that.getGameList('reset');

          that.$msgbox({
            message: res.data.data,
            title: '提示',
            type: 'success',
            showClose: false,
            callback: function () {
              that.previewBox.showPoup = true;
              that.previewBox.gameCode = _data.gameCode;
              that.previewBox.gameLink = _data.gameUrl;
            }
          });
        });
      },
      getGameLinkandCode: function (opts) {
        this.previewBox.gameCode = '';
        this.previewBox.gameLink = '';

        var that = this;
        this.post('/api/caseFilter/getGameLinkandCode', {
          oAid: opts.aid,
          oGameId: opts.id,
          caseAid: 9871760
        }, {
          dataType: 'json',
        }).then(function (res) {
          that.previewBox.showPoup = true;
          that.previewBox.gameCode = res.data.gameCode;
          that.previewBox.gameLink = res.data.gameUrl;
        });
      },
      downCode: function () {
        window.location.href = '//i.hd.fff.com/qrCode.jsp?type=1&cmd=qrurl&siteUrl=' + encodeURI(this.previewBox.gameLink + '&fromQrcode=true&editQrcode=true&_source=1');
      },
      copyCode: function () {
        // 复制链接
        window.getSelection().removeAllRanges();
        var copyDOM = this.$refs.copy;
        var range = document.createRange();
        range.selectNode(copyDOM);
        window.getSelection().addRange(range);
        try {
          var successful = document.execCommand('copy');
          this.$message.success('复制成功！');
        } catch (err) {
          this.$message.error('复制失败，请手动选择复制');
        }
        window.getSelection().removeAllRanges();
      }
    }
  });
</script>
</html>