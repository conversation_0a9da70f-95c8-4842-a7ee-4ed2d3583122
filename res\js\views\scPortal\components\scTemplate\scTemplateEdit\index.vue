<template>
  <div>
    <el-breadcrumb separator="/" class="mb-[30px]">
      <el-breadcrumb-item
        class="text-[18px] font-bold cursor-pointer"
        @click.native="goBack"
        >模板配置</el-breadcrumb-item
      >
      <el-breadcrumb-item class="text-[18px]">{{
        curTitle
      }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-form :rules="rules" :model="templateInfo" ref="templateForm">
      <el-form-item label="选择原型：" class="mb-[20px]">
        <el-button
          size="small"
          @click="showProtoSelectDialog"
          class="!min-w-[280px] text-left"
          :disabled="!!editInfo"
        >
          {{ selectProtoConfig.protoName || "请选择原型" }}
        </el-button>

        <!-- 原型选择弹窗 -->
        <SelectProtoDialog
          :editType="editType"
          :visible.sync="selectProtoConfig.popupVisible"
          :defaultSelectedProtoId="selectProtoConfig.protoId"
          @confirmProtoSelection="confirmProtoSelection"
        />
      </el-form-item>
      <template v-if="showTemplateContent">
        <!-- 模板基础配置 -->
        <BaseConfig :templateInfo="templateInfo" />
        <!-- 表单配置 -->
        <InputForm :templateInfo="templateInfo" />
        <!-- 素材组配置 -->
        <ResForm :templateInfo="templateInfo" :editType="editType" />
        <!-- 视频示例配置 -->
        <VideoExampleSetting
          :templateInfo="templateInfo"
          v-if="productType === ScProductType.VIDEO"
        />
        <!-- 图文示例配置 -->
        <ImgTextExampleSetting :templateInfo="templateInfo" v-else />
        <div class="flex justify-center mt-[30px]">
          <el-button
            type="primary"
            class="w-[200px] sc-proto-edit-save-btn"
            @click="saveConfig"
            >保存</el-button
          >
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
import InputForm from "./inputForm.vue";
import ResForm from "./resForm.vue";
import BaseConfig from "./baseConfig.vue";
import VideoExampleSetting from "./video/videoExampleSetting.vue";
import ImgTextExampleSetting from "./imgText/imgTextExampleSetting.vue";
import SelectProtoDialog from "./selectProtoDialog.vue";
import { ScProductType, FieldTypeDef } from "@/views/scPortal/config/index.js";
import {
  getScTemplateInfo,
  setScTemplateInfo,
} from "@/views/scPortal/api/scTemplate.js";
import { getResUrl } from "@/views/scPortal/utils/index.js";

export default {
  name: "ScTemplateEdit",
  provide() {
    return {
      templateFormRef: () => this.$refs.templateForm,
    };
  },
  components: {
    InputForm,
    ResForm,
    BaseConfig,
    VideoExampleSetting,
    ImgTextExampleSetting,
    SelectProtoDialog,
  },
  props: {
    /**
     * 创建类型（创建的时候需要传）
     */
    createType: {
      type: Number,
      default: ScProductType.VIDEO,
    },
    /**
     * 编辑信息（编辑的时候需要传）
     * {
     *   id: 0,
     *   protoId: 0,
     * }
     */
    editInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      ScProductType,
      editConfig: {
        // 原型ID
        protoId: 0,
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: ["blur", "change"],
          },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: ["blur", "change"],
          },
        ],
        industry: [
          { required: true, message: "请选择模板适用行业", trigger: "change" },
        ],
        scene: [
          { required: true, message: "请选择模板适用场景", trigger: "change" },
        ],
        "script.title": [
          {
            required: true,
            message: "请输入标题",
            trigger: ["blur", "change"],
          },
        ],
      },
      protoList: [], // 原型列表
      // 图文
      templateInfo: {},
      selectProtoConfig: {
        popupVisible: false, // 原型选择弹窗是否显示
        protoId: 0, // 选中的原型ID
        protoName: "", // 选中的原型名称
      },
    };
  },
  computed: {
    curTitle() {
      return `${this.editInfo ? "编辑" : "添加"}${
        this.editType === ScProductType.VIDEO ? "视频" : "图文"
      }模板配置`;
    },
    productType() {
      return this.templateInfo.type;
    },
    editType() {
      // 编辑类型（编辑时直接获取模板类型，新建时根据createType）
      return this.editInfo ? this.templateInfo.type : this.createType;
    },
    showTemplateContent() {
      return Object.keys(this.templateInfo).length > 0;
    },
  },
  watch: {
    editInfo: {
      handler(newVal) {
        if (newVal) {
          this.getEditTemplateInfo();
        }
      },
      immediate: true,
    },
  },
  created() {},
  methods: {
    goBack() {
      // this.$emit("changeComponent", "scTemplateList");
      this.$router.replace("/scPortal/scTemplate");
      // 触发组件变更事件
      this.$emit("changeComponent", "scTemplateList", {
        isReloadList: true,
      });

      // 切换到编辑类型的tab
      this.$store.commit("scTemplate/curActiveName", String(this.editType));
    },
    /**
     * 显示原型选择弹窗
     */
    showProtoSelectDialog() {
      this.selectProtoConfig.popupVisible = true;
    },
    /**
     * 确认选择原型
     * @param proto 选中的原型
     */
    confirmProtoSelection(proto) {
      this.selectProtoConfig.protoId = proto.id;
      this.selectProtoConfig.protoName = proto.name;
      this.getTemplateInfo({
        protoId: this.selectProtoConfig.protoId,
      });
    },
    /**
     * 获取编辑态的模板信息
     */
    getEditTemplateInfo() {
      this.getTemplateInfo({
        protoId: this.editInfo.protoId,
        id: this.editInfo.id,
      });
      this.selectProtoConfig.protoId = this.editInfo.protoId;
    },
    /**
     * 获取模板信息
     */
    getTemplateInfo(params) {
      getScTemplateInfo(params).then((res) => {
        if (res && res.success) {
          let tempTemplateInfo = res.data;
          // 素材配置视频/图片回显
          tempTemplateInfo.resFormList.forEach((item) => {
            if (item.resId) {
              const coverId =
                this.editType === ScProductType.VIDEO
                  ? item.coverId
                  : item.resId;
              const coverType =
                this.editType === ScProductType.VIDEO
                  ? item.coverType
                  : item.resType;
              item._fileList = [
                {
                  id: item.resId,
                  name: item.label,
                  url: getResUrl(coverType, coverId), // 图片/视频封面
                  videoUrl:
                    this.editType === ScProductType.VIDEO
                      ? getResUrl(item.resType, item.resId)
                      : "", // 视频
                },
              ];
            } else {
              item._fileList = [];
            }
          });
          // 视频/图文封面图回显
          tempTemplateInfo?.cover?.resId &&
            (tempTemplateInfo._coverFileList = [
              {
                id: tempTemplateInfo.cover.resId,
                name: "封面图",
                url: getResUrl(
                  tempTemplateInfo.cover.resType,
                  tempTemplateInfo.cover.resId
                ),
              },
            ]);
          if (this.editType === ScProductType.VIDEO) {
            // 视频
            // 示例视频回显
            tempTemplateInfo?.setting?.video &&
              (tempTemplateInfo._videoFileList = [
                {
                  id: tempTemplateInfo?.setting?.video.resId,
                  name: "示例视频",
                  url: getResUrl(
                    tempTemplateInfo?.setting?.video.coverType,
                    tempTemplateInfo?.setting?.video.coverId
                  ),
                  videoUrl: getResUrl(
                    tempTemplateInfo?.setting?.video.videoType,
                    tempTemplateInfo?.setting?.video.videoId
                  ),
                },
              ]);
          } else {
            // 图文
            // 示例图片次图回显
            if (tempTemplateInfo?.setting?.imgList) {
              tempTemplateInfo._imgFileList = [];
              tempTemplateInfo?.setting?.imgList.forEach((item) => {
                tempTemplateInfo._imgFileList.push({
                  id: item.resId,
                  name: "示例图片",
                  url: getResUrl(item.resType, item.resId),
                });
              });
            }
          }
          // // 表单配置处理默认值
          tempTemplateInfo.inputFormList.forEach((item) => {
            if (!item.desc) {
              // 如果描述为空，则默认填写“请填写”
              item.desc = "请填写" + item.label;
            }
            // lymn-todo 等后端返回这个数据
            if (item.filedType === FieldTypeDef.TAG) {
              item._defaultContentTagsValue = ''
              if (item.defaultContent) {
                item._defaultContentTags = item.defaultContent.split(",");
              } else {
                item._defaultContentTags = [];
              }
            }
          });
          if (this.editInfo) {
            // 编辑态
            this.selectProtoConfig.protoName = tempTemplateInfo.protoName;
          }
          this.templateInfo = tempTemplateInfo;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /**
     * 保存配置
     */
    saveConfig() {
      this.$refs.templateForm.validate((valid) => {
        if (valid) {
          setScTemplateInfo(this.templateInfo).then((res) => {
            if (res && res.success) {
              this.$message.success("保存成功");
              this.goBack();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          this.$message.error("请检查填写内容！");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.proto-select-dialog {
  .el-table {
    margin-top: 20px;
  }
}
</style>
