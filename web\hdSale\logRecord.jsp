<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}
%>
<%
if(!Web.getDebug() && WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE) && WebOss.getSid() != 877 && WebOss.getSid() != 1355){
	out.println("请到新销售系统进行操作");
	return;
} 
%>s


<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>售前领取记录</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-logRecord">


		<!--查询条件 start-->
		<div class="fai-logRecord-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
			    <el-form-item label="创建时间">
			    	<el-checkbox v-model="form.creDateFlag"></el-checkbox>
					<el-date-picker class="fai-date" v-model="form.creDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.creDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
			    <el-form-item label="操作日期(领取or释放)">
			    	<el-checkbox v-model="form.optDateFlag"></el-checkbox>
					<el-date-picker class="fai-date" v-model="form.optDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.optDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
			    <el-form-item label="AID">
					<el-input v-model="form.aid" placeholder="AID" class="fai-wid-100"></el-input>
				</el-form-item>
			    <el-form-item label="类型">
					<el-select v-model="form.attrType" filterable>
						<el-option label="全部" value="-1"></el-option>
						<el-option label="主动领取" value="32"></el-option>
						<el-option label="自动分配" value=33></el-option>
						<el-option label="系统释放" value="30"></el-option>
						<el-option label="主动释放" value="31"></el-option>
						<el-option label="主动释放-成交库" value="34"></el-option>
						<el-option label="B库转入" value="35"></el-option>
						<el-option label="主动转入" value="44"></el-option>
						<el-option label="公海库领取" value="43"></el-option>
						<el-option label="管理员分配" value="50"></el-option>
						<el-option label="转入无效库" value="61"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="资源标签" >
					<el-select v-model="form.tag" filterable >
						<el-option v-for="item in tagList" :label="item.name" :value="item.label" :key="item.label"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="账号" >
					<el-select v-model="form.staff_sacct" filterable >
						<el-option label="所有" value="all"></el-option>
						<el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sacct" :key="preSale.sacct"></el-option>
					</el-select>
				</el-form-item>
				<%-- <el-form-item>
					<el-checkbox v-model="form.setOptTimeFlag">调整领取时间</el-checkbox>
					<el-button type="" @click="recentTenDays">最近10天</el-button>
					<el-button type="" @click="recentMonth">最近1个月</el-button>
				</el-form-item> --%>
				<el-form-item label="注册来源">
					<el-select v-model="form.ta" filterable>
						<el-option label="所有" value="-1"></el-option>
						<el-option label="建站来源" value="1"></el-option>
						<el-option label="互动推广" value="3"></el-option>
						<el-option label="微传单推广" value="4"></el-option>
						<el-option label="线下广告" value="6"></el-option>
						<el-option label="移动端推广" value="7"></el-option>
						<el-option label="自来" value="8"></el-option>
						<el-option label="SEO" value="10"></el-option>
						<el-option label="活动引流" value="11"></el-option>
						<el-option label="未知" value="9"></el-option>
						<el-option label="移动端放量" value="12"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="分配方式">
					<el-select v-model="form.allotType" filterable>
						<el-option label="全部" value="-1"></el-option>
						<el-option label="平均分配" value="0"></el-option>
						<el-option label="奖励资源" value="1"></el-option>
						<el-option label="续费资源" value="2"></el-option>
						<el-option label="公海库领取" value="3"></el-option>
						<el-option label="审批领取" value="4"></el-option>
					</el-select>
				</el-form-item>
					<el-form-item label="销售分组">
					<el-select v-model="form.saleGroup" filterable>
						<el-option label="全部" value="all"></el-option>
						<el-option label="营销产品部" value="authHDSaleEnergy"></el-option>
						<el-option label="虚拟销售组" value="authHDSaleAI"></el-option>
						<el-option label="销售1组" value="authHDSale1"></el-option>
						<el-option label="销售2组" value="authHDSale2"></el-option>
						<el-option label="销售3组" value="authHDSale3"></el-option>
						<el-option label="销售4组" value="authHDSale4"></el-option>
						<el-option label="销售5组" value="authHDSale5"></el-option>
					</el-select>
				</el-form-item>
				
				

				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
					<el-button icon="el-icon-download" type="primary"  @click="exportExcel">导出</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="fai-logRecord-list" v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border>
				<el-table-column label="序列号" type="index" width="70px" fixed></el-table-column><!--fixed:固定-->
				<el-table-column label="创建时间" prop="createTime" width="200px"></el-table-column>
				<el-table-column label="领取or释放 时间" prop="receiveTime" width="200px"></el-table-column>
				<el-table-column label="AID" width="100px">
					<template slot-scope="scope">
						<a v-bind:href="'http://hd.oss.aaa.cn/#//oss.aaa.cn/module/cs/corp.jsp?aid=' + scope.row.aid" target='_blank'>{{scope.row.aid}}</a>
					</template>
				</el-table-column>
				<el-table-column label="操作账号" prop="sacctName" width="120px"></el-table-column>
				<el-table-column label="操作类型" prop="attrTypeContent" width="120px"></el-table-column>
				<el-table-column label="内容" prop="action" width="350" :show-Overflow-Tooltip=true></el-table-column>
				<el-table-column label="资源标签" prop="tagName"  ></el-table-column>
				<el-table-column label="注册来源" prop="taGroupName"  ></el-table-column>
				<el-table-column label="奖励资源" prop="isAward" width="80px" ></el-table-column>

			</el-table>
			<div class="block">
    			<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" layout="total, sizes, prev, pager, next, jumper" 
      					:current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total">
    			</el-pagination>
 			</div>
		</div>
		
		<!--数据表格 end-->

	</body>


	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-logRecord-search',
			data: {
				form: {//这里是为了填充默认值
					creDateFlag: true,
					creDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 3*24*3600*1000),
					creDateEnd: Fai.tool.dateFormatter(new Date()),
					optDateFlag: false,
					optDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 3*24*3600*1000),
					optDateEnd: Fai.tool.dateFormatter(new Date()),
					attrType: "-1",
					staff_sacct: "all",
					aid: "",
					exportFlag: false,
					setOptTimeFlag: false,
					currentPage: 1,
					limit: 10,
					tag:-1,
					ta:"-1",
					allotType:"-1",
				},
				
				preSaleList: [],
				tagList: [],
				editable: false,
				clearable: false,
				
				
			},
			created:function(){
				Fai.http.post("hdSale_h.jsp?cmd=getPreSale", "", false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
				getDataList(this.form);
				
				//-------------------begin 获取标签资源类型-------------------------------//
				var arg = {
					"cmd":"getTagList",
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
					console.log(response.data);
					this.tagList  = response.data;
				}, response => {
					this.$message({
						type: 'warning',
						message: '系统错误!'
					});
				});
				//-------------------end 获取标签资源类型-------------------------------//
				
			},
			methods: {
				onSubmit() {
					this.form.exportFlag = false;
					//检查至少填写一个日期条件
					var checkDateFlag = false;
					var dateCondList = ['creDateFlag', 'optDateFlag'];
					for(var i = 0; i < dateCondList.length; i++){
						var dateTime = this.form[dateCondList[i]];
						if(dateTime != null && dateTime != ""){
							checkDateFlag = true;
							//this.$message({showClose: true, message: '日期校验通过！', type: 'success'});
							break;
						}
					}
					if(!checkDateFlag){
						this.$message({showClose: true, type: 'error', message: '必须勾选其中一个时间（创建时间、领取or释放 时间）！'});
						return ;
					}
					getDataList(this.form);
				},
				exportExcel(){
					this.form.exportFlag = true;
					console.info(this.form);
					window.open("/ajax/hdSale/logRecord_h.jsp?cmd=getLogRecordList" + Fai.tool.parseJsonToUrlParam(this.form));
				},
				// exportExcelNew(){
				// 	this.form.exportFlag = true;
				// 	window.open("/ajax/hdSale_h.jsp?cmd=getLogRecordList&new=true" + Fai.tool.parseJsonToUrlParam(this.form));
				// 	this.form.exportFlag = false;
				// },
				recentTenDays(){
					this.form.creDateEnd = Fai.tool.dateFormatter(new Date());
					this.form.creDateBeg = Fai.tool.dateFormatter(new Date().getTime() - 10*24*3600*1000);
					this.form.optDateEnd = Fai.tool.dateFormatter(new Date());
					this.form.optDateBeg = Fai.tool.dateFormatter(new Date().getTime() - 10*24*3600*1000);
				},
				recentMonth(){
					this.form.creDateEnd = Fai.tool.dateFormatter(new Date());
					this.form.creDateBeg = Fai.tool.dateFormatter(new Date().getTime() - 30*24*3600*1000);//暂时按30天/月算，后续优化（月份减一）
					this.form.optDateEnd = Fai.tool.dateFormatter(new Date());
					this.form.optDateBeg = Fai.tool.dateFormatter(new Date().getTime() - 30*24*3600*1000);
				},
				

			}
		});
		
		var faiDataList = new Vue({
			el: '.fai-logRecord-list',
			data: {
				tableData: [],
				currentPage: 1,
				limit: 10,
				total: 0,
			},
			created:function(){
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				handleSizeChange(val) {
                    faiSearchObj.form.exportFlag = false;
					faiSearchObj.form.limit = val;
		        	getDataList(faiSearchObj.form);
			    },
			    handleCurrentChange(val) {
                    faiSearchObj.form.exportFlag = false;
			    	faiSearchObj.form.currentPage = val;
			        getDataList(faiSearchObj.form);
			    }
		    }
		});
        
		// 获取数据
		function getDataList(urlParam){
			// 查询数据
			Fai.http.post("hdSale/logRecord_h.jsp?cmd=getLogRecordList", urlParam, false).then(result => {
                if(result.success){
                    faiDataList.tableData = result.dataList;
                    faiDataList.total = result.total;
                }
            });
		}

	</script>

</html>



