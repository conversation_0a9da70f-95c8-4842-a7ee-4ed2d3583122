<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.app.*"%>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>HD-OSS</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
	</head>
	<style type="text/css">
	</style>
	<body class="fai-body">
		<div class="el-container is-vertical">
			<!--头部-->
			<div class="el-header fai-header">
				<!--头部左边部分-->
				<div class="fai-header-left">
					<span class="fai-login"></span>
				</div>
				<!--头部中间部分-->
				<div class="fai-header-middle">
					<%@ include file="/comm/topNav.jsp.inc"%>
				</div>
				<!--头部右边部分-->
				<div class="fai-header-rigth" >
					你好，{{this.auth.staff.sacct}}
					<el-button type="info" @click="logout()" size="small" style="margin: 15px;">{{button}}</el-button>
				</div>
			</div>
			<div class="fai-main">
				<div class="fai-main-left">
					<%@ include file="/comm/leftNav.jsp.inc"%>
				</div>
				<div class="fai-main-rigth">
					<iframe id="fai-iframe" v-if="isUseIframe" v-bind:src="src" style="width:100%;height:100%" frameborder="0" scrolling="yes"></iframe>
					<router-view v-else></router-view>
				</div>
			</div>
			<div class="el-footer">
			</div>
		</div>
	</body>
	<script type="text/javascript">
		var faiIfram = new Vue({
			el: '.fai-main-rigth',
			data: {
				src: "",
				isUseIframe: true
			},
			router: initViewRouter(),
			store: initStore()
		})
		var headerRight = new Vue({
			el:".fai-header-rigth",
			data: {
				button:"退出",
			},
			methods:{
				logout(){
						this.$confirm('确定退出系统？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
						}).then(() => {
							var arg = {
								"cmd":"logout",
							}
							Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
								this.$message({
								type: 'success',
								message: '退出成功!'
								});
								window.location.reload();
							}, response => {
								this.$message({
								type: 'warning',
								message: '系统错误!'
								});
							});
						
						}).catch(() => {
							this.$message({
							type: 'info',
							message: '已取消退出'
							});          
						});
				},
			},
		});
	</script>
</html>