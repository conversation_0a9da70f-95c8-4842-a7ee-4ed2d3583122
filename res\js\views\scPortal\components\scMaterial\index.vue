<template>
  <div class="h-full overflow-auto p-[0_30px] pb-[100px]">
    <keep-alive include="ScMaterialList">
      <component 
        :is="component" 
        :editInfo="editInfo"
        :isReloadList="isReloadList"
        @changeComponent="changeComponent" />
    </keep-alive>
  </div>
</template>

<script>  
import ScMaterialList from './scMaterialList/index.vue';
import ScBgMusicEdit from './scBgMusicEdit/index.vue';
import ScDubbingEdit from './scDubbingEdit/index.vue';
import { ScMaterialType } from '@/views/scPortal/config/index.js';
export default {
  name: 'ScMaterial',
  components: {
    ScMaterialList,
    ScBgMusicEdit,
    ScDubbingEdit,
  },
  data() {
    return {
      component: 'ScMaterialList',
      activeType: ScMaterialType.BG_MUSIC, // 切换的类型
      editInfo: {}, // 编辑信息
      isReloadList: false, // 是否刷新列表
    }
  },
  created() {
    this.initDomainInfo();
  },  
  methods: {
    /**
     * 初始化域名信息
     */
    initDomainInfo() {
      this.$store.dispatch('scPortal/getDomainInfo').catch(error => {
        this.$message.error(error);
      });
    },
    changeComponent(component, data = {}) {
      this.component = component
      const { editInfo = {}, isReloadList = false } = data
      this.editInfo = editInfo
      this.isReloadList = isReloadList
      console.log(this.activeType, this.editInfo, 'changeComponent123');
    }
  },
}
</script>

<style scoped>

</style>