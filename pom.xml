<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>hdoss</groupId>
    <artifactId>hdoss</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <!--版本控制-->
    <parent>
        <groupId>HdPomCoreParent</groupId>
        <artifactId>HdPomCoreParent</artifactId>
        <version>1.0.0</version>
    </parent>


    <properties>
        <hdportal-version>1.0.0</hdportal-version>
        <fai-webkchome-version>1.0.0</fai-webkchome-version>
        <guava-version>20.0</guava-version>
    </properties>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <repository_id>nexus-id</repository_id>
                <repository_name>snapshots repository</repository_name>
                <repository_url>http://maven.faidev.cc/repository/maven-snapshots/</repository_url>
                <!--本部门的jar 版本-->
                <cpDir>${basedir}/web/lib</cpDir>
            </properties>
        </profile>

        <profile>
            <id>pro</id>
            <properties>
                <repository_id>nexus-id</repository_id>
                <repository_name>release repository</repository_name>
                <repository_url>http://maven.faidev.cc/repository/maven-releases/</repository_url>
                <!--本部门的jar 版本-->
               <cpDir>${basedir}/web/lib</cpDir>
            </properties>
        </profile>

        <profile>
            <id>can</id>
            <properties>
            	<cpDir>${basedir}/web/lib</cpDir>
            </properties>
        </profile>

    </profiles>

    <dependencies>
        <!-- 互动的包 -->
    	<dependency>
            <groupId>fai-hdUtil</groupId>
            <artifactId>fai-hdUtil</artifactId>
            <version>${fai-hdUtil-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-app-hd</groupId>
            <artifactId>fai-app-hd</artifactId>
            <version>${fai-app-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-entity-hd</groupId>
            <artifactId>fai-entity-hd</artifactId>
            <version>${fai-entity-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-hd</groupId>
            <artifactId>fai-cli-hd</artifactId>
            <version>${fai-cli-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-inf-hd</groupId>
            <artifactId>fai-cli-inf-hd</artifactId>
            <version>${fai-cli-inf-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    	 <dependency>
            <groupId>fai-web-inf-hd</groupId>
            <artifactId>fai-web-inf-hd</artifactId>
            <version>${fai-web-inf-hd-version}</version>
             <exclusions>
                 <exclusion>
                     <artifactId>*</artifactId>
                     <groupId>*</groupId>
                 </exclusion>
             </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-hd</groupId>
            <artifactId>fai-web-hd</artifactId>
            <version>${fai-web-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-hd</groupId>
            <artifactId>fai-web-kit-hd</artifactId>
            <version>${fai-web-kit-hd-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>hdportal</groupId>
            <artifactId>hdportal</artifactId>
            <version>${hdportal-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>tencentcloud-sdk-java</artifactId>
                    <groupId>com.tencentcloudapi</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>gson</artifactId>
                    <groupId>fai-third-party</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 速创部门jar -->
        <dependency>
            <groupId>fai-web-sc</groupId>
            <artifactId>fai-web-sc</artifactId>
            <version>${fai-web-sc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-app-sc</groupId>
            <artifactId>fai-app-sc</artifactId>
            <version>${fai-app-sc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fai-entity-sc</groupId>
            <artifactId>fai-entity-sc</artifactId>
            <version>${fai-entity-sc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fai-cli-sc</groupId>
            <artifactId>fai-cli-sc</artifactId>
            <version>${fai-cli-sc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>fai-comm-util-sc</groupId>
            <artifactId>fai-comm-util-sc</artifactId>
            <version>${fai-comm-util-sc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        
    
        <!-- 悦客部门jar-->
        <dependency>
            <groupId>fai-app-yk</groupId>
            <artifactId>fai-app-yk</artifactId>
            <version>${fai-app-yk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-yk</groupId>
            <artifactId>fai-cli-yk</artifactId>
            <version>${fai-cli-yk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-yk</groupId>
            <artifactId>fai-web-inf-yk</artifactId>
            <version>${fai-web-inf-yk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-yk</groupId>
            <artifactId>fai-web-yk</artifactId>
            <version>${fai-web-yk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-yk</groupId>
            <artifactId>fai-web-kit-yk</artifactId>
            <version>${fai-web-kit-yk-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       

        <!-- 架构部门jar-->
        <dependency>
            <groupId>fai-elasticSearch</groupId>
            <artifactId>fai-elasticSearch</artifactId>

        </dependency>
        <dependency>
            <groupId>fai-comm-zkcli</groupId>
            <artifactId>fai-comm-zkcli</artifactId>
            <version>${fai-comm-zkcli-version}</version>
        </dependency>
		<dependency>
            <groupId>fai-webgateway-client</groupId>
            <artifactId>fai-webgateway-client</artifactId>
            <version>${fai-webgateway-client-version}</version>
        </dependency>
        <dependency>
            <groupId>fai-comm-mq</groupId>
            <artifactId>fai-comm-mq</artifactId>
            <version>${fai-comm-mq-version}</version>
        </dependency>
        <dependency>
            <groupId>fai-comm-util-arch</groupId>
            <artifactId>fai-comm-util-arch</artifactId>
            <version>${fai-comm-util-arch-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
	        <groupId>fai-comm-mskit</groupId>
	    	<artifactId>fai-comm-mskit</artifactId>
	    	<version>${fai-comm-mskit-version}</version>
        </dependency>
        <dependency>
            <groupId>fai-app-arch</groupId>
            <artifactId>fai-app-arch</artifactId>
            <version>${fai-app-arch-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-arch</groupId>
            <artifactId>fai-cli-arch</artifactId>
            <version>${fai-cli-arch-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-arch</groupId>
            <artifactId>fai-web-inf-arch</artifactId>
            <version>${fai-web-inf-arch-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-arch</groupId>
            <artifactId>fai-web-kit-arch</artifactId>
            <version>${fai-web-kit-arch-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-comm</groupId>
            <artifactId>fai-web-comm</artifactId>
            <version>${fai-web-comm-version}</version>
	    <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
		<dependency>
            <groupId>fai-app-fdp</groupId>
            <artifactId>fai-app-fdp</artifactId>
            <version>${fai-app-fdp-version}</version>
        </dependency>
		<dependency>
            <groupId>fai-cli-fdp</groupId>
            <artifactId>fai-cli-fdp</artifactId>
            <version>${fai-cli-fdp-version}</version>
        </dependency>
        <dependency>
            <groupId>fai-comm-fdpdata</groupId>
            <artifactId>fai-comm-fdpdata</artifactId>
            <version>${fai-comm-fdpdata-version}</version>
        </dependency>
        <dependency>
            <groupId>fai-sdk-fdpdata</groupId>
            <artifactId>fai-sdk-fdpdata</artifactId>
            <version>${fai-sdk-fdpdata-version}</version>
        </dependency>
        
       
       <!-- 快图部门jar-->
        <dependency>
            <groupId>fai-app-media</groupId>
            <artifactId>fai-app-media</artifactId>
            <version>${fai-app-media-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-media</groupId>
            <artifactId>fai-cli-media</artifactId>
            <version>${fai-cli-media-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-media</groupId>
            <artifactId>fai-web-inf-media</artifactId>
            <version>${fai-web-kit-media-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-media</groupId>
            <artifactId>fai-web-kit-media</artifactId>
            <version>${fai-web-kit-media-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-media</groupId>
            <artifactId>fai-web-media</artifactId>
            <version>${fai-web-media-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <!-- 快车部门jar-->
        <dependency>
            <groupId>fai-comm-util-kc</groupId>
            <artifactId>fai-comm-util-kc</artifactId>
            <version>${fai-comm-util-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-app-kc</groupId>
            <artifactId>fai-app-kc</artifactId>
            <version>${fai-app-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-kc</groupId>
            <artifactId>fai-cli-kc</artifactId>
            <version>${fai-cli-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-kc</groupId>
            <artifactId>fai-web-inf-kc</artifactId>
            <version>${fai-web-inf-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-kc</groupId>
            <artifactId>fai-web-kit-kc</artifactId>
            <version>${fai-web-kit-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kc</groupId>
            <artifactId>fai-web-kc</artifactId>
            <version>${fai-web-kc-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-webkchome</groupId>
            <artifactId>fai-webkchome</artifactId>
            <version>${fai-webkchome-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        
        
    	<!-- 建站部门jar-->
        <dependency>
            <groupId>fai-app-site</groupId>
            <artifactId>fai-app-site</artifactId>
            <version>${fai-app-site-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-site</groupId>
            <artifactId>fai-cli-site</artifactId>
            <version>${fai-cli-site-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-site</groupId>
            <artifactId>fai-web-inf-site</artifactId>
            <version>${fai-web-inf-site-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-site</groupId>
            <artifactId>fai-web-kit-site</artifactId>
            <version>${fai-web-kit-site-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-site</groupId>
            <artifactId>fai-web-site</artifactId>
            <version>${fai-web-site-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <dependency>
		  <groupId>fai-website</groupId>
		  <artifactId>fai-website</artifactId>
		  <version>${fai-website-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
        
        
        <!-- 探鼠业务jar-->
        <dependency>
            <groupId>fai-app-ts</groupId>
            <artifactId>fai-app-ts</artifactId>
            <version>${fai-app-ts-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-ts</groupId>
            <artifactId>fai-cli-ts</artifactId>
            <version>${fai-cli-ts-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-ts</groupId>
            <artifactId>fai-web-inf-ts</artifactId>
            <version>${fai-web-inf-ts-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-ts</groupId>
            <artifactId>fai-web-kit-ts</artifactId>
            <version>${fai-web-kit-ts-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-ts</groupId>
            <artifactId>fai-web-ts</artifactId>
            <version>${fai-web-ts-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       
        
        <!-- 运营部门的jar-->
        <dependency>
            <groupId>fai-comm-util-opt</groupId>
            <artifactId>fai-comm-util-opt</artifactId>
            <version>${fai-comm-util-opt-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>FaiTradeStation</groupId>
            <artifactId>FaiTradeStationSvr-interfaces</artifactId>
            <version>${FaiTradeStationSvr-interfaces-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-app-opt</groupId>
            <artifactId>fai-app-opt</artifactId>
            <version>${fai-app-opt-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-opt</groupId>
            <artifactId>fai-cli-opt</artifactId>
            <version>${fai-cli-opt-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-opt</groupId>
            <artifactId>fai-web-inf-opt</artifactId>
            <version>${fai-web-inf-opt-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-opt</groupId>
            <artifactId>fai-web-kit-opt</artifactId>
            <version>${fai-web-kit-opt-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-opt</groupId>
            <artifactId>fai-web-opt</artifactId>
            <version>${fai-web-opt-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
		  <groupId>fai-webportal</groupId>
		  <artifactId>fai-webportal</artifactId>
		  <version>${fai-webportal-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
        
        
        <!-- 助手的包 -->
		<dependency>
            <groupId>fai-app-mp</groupId>
            <artifactId>fai-app-mp</artifactId>
            <version>${fai-app-mp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
		<dependency>
			<groupId>fai-cli-mp</groupId>
			<artifactId>fai-cli-mp</artifactId>
			<version>${fai-cli-mp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-kit-mp</groupId>
			<artifactId>fai-web-kit-mp</artifactId>
			<version>${fai-web-kit-mp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-inf-mp</groupId>
			<artifactId>fai-web-inf-mp</artifactId>
			<version>${fai-web-inf-mp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-mp</groupId>
			<artifactId>fai-web-mp</artifactId>
			<version>${fai-web-mp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		
        <!-- 销售功能的包 -->
        <dependency>
            <groupId>fai-comm-util-mkp</groupId>
            <artifactId>fai-comm-util-mkp</artifactId>
            <version>${fai-comm-util-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-app-mkp</groupId>
            <artifactId>fai-app-mkp</artifactId>
            <version>${fai-app-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-cli-mkp</groupId>
            <artifactId>fai-cli-mkp</artifactId>
            <version>${fai-cli-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-inf-mkp</groupId>
            <artifactId>fai-web-inf-mkp</artifactId>
            <version>${fai-web-inf-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-kit-mkp</groupId>
            <artifactId>fai-web-kit-mkp</artifactId>
            <version>${fai-web-kit-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>fai-web-mkp</groupId>
            <artifactId>fai-web-mkp</artifactId>
            <version>${fai-web-mkp-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       
        
        <!-- 传单功能的包 -->
        <dependency>
            <groupId>fai-app-flyer</groupId>
            <artifactId>fai-app-flyer</artifactId>
            <version>${fai-app-flyer-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
			<groupId>fai-cli-flyer</groupId>
			<artifactId>fai-cli-flyer</artifactId>
			<version>${fai-cli-flyer-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-kit-flyer</groupId>
			<artifactId>fai-web-kit-flyer</artifactId>
			<version>${fai-web-kit-flyer-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-inf-flyer</groupId>
			<artifactId>fai-web-inf-flyer</artifactId>
			<version>${fai-web-inf-flyer-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>fai-web-flyer</groupId>
			<artifactId>fai-web-flyer</artifactId>
			<version>${fai-web-flyer-version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
		</dependency>

        <!-- 中台文件包 -->
        <dependency>
            <groupId>MgFiles</groupId>
            <artifactId>MgFilesInfSvr-interfaces</artifactId>
            <version>1.0.18</version>
        </dependency>
        <dependency>
            <groupId>fai-comm-middleground</groupId>
            <artifactId>fai-comm-middleground</artifactId>
            <version>1.0.31</version>
        </dependency>

        <!-- lombok关包 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
            <scope>provided</scope>
        </dependency>

        <!-- 第三方包 -->
        <dependency>
            <groupId>fai-third-party</groupId>
            <artifactId>gif4j_pro_trial_crack</artifactId>
            <version>2.3</version>
        </dependency>
        <dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>alipay-util</artifactId>
		  <version>1.0.0</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>alipay-api</artifactId>
		  <version>1.0.0</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>AisinoClient</artifactId>
		  <version>1.0.0</version>
		</dependency>
        <dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>CommercialClient</artifactId>
		  <version>1.0.2</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>fastdfs_client_java</artifactId>
		  <version>1.24</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>filterbuilder</artifactId>
		  <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>image4j</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>javaee</artifactId>
		    <version>16</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>javacsv</artifactId>
		    <version>2.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>jaxen-1.1-beta-6</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>jl</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>joinpay-util</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>JOpenSurf</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>sunpkcs11</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>tenpay-util</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>thumbelina</artifactId>
		    <version>1.1.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>lire</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>MigrateToolArch</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>pinyin4j</artifactId>
		    <version>2.5.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>sax</artifactId>
		    <version>2.0.1</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>qconf-client</artifactId>
		    <version>1.2.2</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>sanselan-0.97-incubator</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>sfnttool</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>sitecapturer</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>wsdl4j</artifactId>
		    <version>1.6.2</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>xercesImpl</artifactId>
		    <version>2.9.1</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>zxing-core</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		    <groupId>fai-third-party</groupId>
		    <artifactId>zxing-javase</artifactId>
		    <version>1.0.0</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>sogou-api-java-gateway</artifactId>
		  <version>2.2.1</version>
		</dependency>
		<dependency>
		  <groupId>fai-third-party</groupId>
		  <artifactId>sougouAPI</artifactId>
		  <version>1.0.0</version>
		</dependency>
		
        <dependency>
		  <groupId>commpay</groupId>
		  <artifactId>commpay</artifactId>
		  <version>1.0.0</version>
		</dependency>
		<dependency>
		  <groupId>commpay-util</groupId>
		  <artifactId>commpay-util</artifactId>
		  <version>1.0.0</version>
		</dependency>
		
		<!-- servlet相关表，紧用于编辑器 -->
		<dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>provided</scope>
        </dependency>
		
		<!-- mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.10</version>
        </dependency>
        
        <!-- 其他第三方包 -->
        <dependency>
	        <groupId>commons-fileupload</groupId>
	  		<artifactId>commons-fileupload</artifactId>
	  		<version>1.2.1</version>
  		</dependency>
  		<dependency>
		  <groupId>commons-httpclient</groupId>
		  <artifactId>commons-httpclient</artifactId>
		  <version>3.0.1</version>
		</dependency>
		<dependency>
		  <groupId>commons-codec</groupId>
		  <artifactId>commons-codec</artifactId>
		  <version>1.7</version>
		</dependency>
		<dependency>
		  <groupId>commons-lang</groupId>
		  <artifactId>commons-lang</artifactId>
		  <version>2.4</version>
		</dependency>
		<dependency>
		    <groupId>org.bouncycastle</groupId>
		    <artifactId>bcprov-jdk15on</artifactId>
		    <version>1.54</version>
		</dependency>	
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-math3</artifactId>
		    <version>3.0</version>
		</dependency>	
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-pool2</artifactId>
		    <version>2.4.2</version>
		</dependency>		
		<dependency>
		    <groupId>com.caucho</groupId>
		    <artifactId>hessian</artifactId>
		    <version>4.0.38</version>
		</dependency>	
		<dependency>
		    <groupId>org.htmlparser</groupId>
		    <artifactId>htmllexer</artifactId>
		    <version>2.1</version>
		</dependency>
		<dependency>
		    <groupId>org.htmlparser</groupId>
		    <artifactId>htmlparser</artifactId>
		    <version>2.1</version>
		</dependency>	
		<dependency>
		    <groupId>org.apache.httpcomponents</groupId>
		    <artifactId>httpclient-cache</artifactId>
		    <version>4.1.2</version>
		</dependency>
		<dependency>
		    <groupId>org.codehaus.jackson</groupId>
		    <artifactId>jackson-mapper-lgpl</artifactId>
		    <version>1.7.1</version>
		</dependency>
		<dependency>
		    <groupId>com.sun.mail</groupId>
		    <artifactId>javax.mail</artifactId>
		    <version>1.4.4</version>
		</dependency>
		<dependency>	 
		  <groupId>org.slf4j</groupId>
		  <artifactId>jcl-over-slf4j</artifactId>
		  <version>1.6.6</version>
		</dependency>
  		<dependency>
		    <groupId>org.jdom</groupId>
		    <artifactId>jdom</artifactId>
		    <version>1.1</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
		  	<artifactId>jsoup</artifactId>
		 	<version>1.8.2</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
 			<artifactId>jul-to-slf4j</artifactId>
		 	<version>1.6.6</version>
		</dependency>		
		<dependency>
			<groupId>org.slf4j</groupId>
  			<artifactId>slf4j-log4j12</artifactId>
		 	<version>1.6.6</version>
		</dependency>	
		<dependency>
			<groupId>org.slf4j</groupId>
  			<artifactId>slf4j-nop</artifactId>
		 	<version>1.7.2</version>
		</dependency>
		<dependency>
	  		<groupId>sunjce_provider</groupId>
		  	<artifactId>sunjce_provider</artifactId>
		  	<version>1.8</version>
		</dependency>
		<dependency>
			<groupId>net.coobird</groupId>
		  	<artifactId>thumbnailator</artifactId>
		  	<version>0.4.8</version>
		</dependency>		
		<dependency>
		  <groupId>org.projectlombok</groupId>
		  <artifactId>lombok</artifactId>
		  <version>1.18.8</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.lucene</groupId>
		  <artifactId>lucene-core</artifactId>
		  <version>4.0.0</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.lucene</groupId>
		  <artifactId>lucene-analyzers-common</artifactId>
		  <version>4.0.0</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.lucene</groupId>
		  <artifactId>lucene-queryparser</artifactId>
		  <version>4.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.neethi</groupId>
	    	<artifactId>neethi</artifactId>
	    	<version>3.0.0</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.poi</groupId>
		    <artifactId>poi-scratchpad</artifactId>
		    <version>3.8</version>
		</dependency>
		<dependency>
			<groupId>org.apache.ws.xmlschema</groupId>
	    	<artifactId>xmlschema-core</artifactId>
	    	<version>2.0</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.zookeeper</groupId>
		    <artifactId>zookeeper</artifactId>
		    <version>3.4.5</version>
		</dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava-version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- 腾讯云tts -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-tts</artifactId>
            <version>3.1.1154</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.8</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
			<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                        <filesets>
                        <!-- 删除文件夹lib -->
                        <fileset>
                                <directory>${cpDir}</directory>
                        </fileset>
                        </filesets>
                </configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <outputDirectory>${cpDir}</outputDirectory>
                </configuration>
            </plugin>


            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${cpDir}</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>



</project>

