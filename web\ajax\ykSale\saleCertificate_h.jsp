<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.Core" %>
<%@ page import="fai.web.inf.SysCrmSale" %>
<%@ page import="fai.app.SalesSystemDef" %>
<%@ page import="fai.weboss.WebOss" %>
<%--悦客销售能力认证--%>
<%!
    private static final class Key {
        public static final String DEPARTMENT = "department";
        public static final String BUSINESS = "business";
        public static final String LIST = "list";
        public static final String CMD = "cmd";
        public static final String LABEL = "label";
        public static final String SID = "sid";
        public static final String SID_LIST = "sidList";
    }
    private static final class Message {
        public static final String NO_AUTH = "没有权限";
        public static final String NO_METHOD = "没有找到方法";
        public static final String SYS_ERR = "系统错误";
        public static final String ARG_ERR_DEPARTMENT = "参数错误，没有指定部门";
        public static final String GET_SALE_ERR = "获取其他部门销售失败";
        public static final String ARG_ERR_SID = "参数错误，销售员工id为0";
        public static final String ARG_ERR_BUSINESS = "参数错误，业务为空";
        public static final String GET_BIZ_ERROR = "获取销售已有业务权限失败";
        public static final String REMOVE_ERR = "删除失败";
        public static final String ARG_ERR_SID_LIST = "参数错误，请选择要添加的销售";
    }
%>
<%!
    // 获取已认证的销售列表
    private FaiList<Param> getSaleList(int department) throws Exception {
        FaiList<Param> saleList = new FaiList<Param>();

        FaiList<Integer> list = new FaiList<Integer>();
        list.add(department);
        FaiList<Integer> allDepartMentList = new FaiList<Integer>();
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.MH);
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.HD);
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.ZH);
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.YK);
//        allDepartMentList.removeAll(list);

        FaiList<Integer> bizList = new FaiList<Integer>();
        bizList.add(SalesSystemDef.SaleInfo.InchargeBiz.YK);

        SearchArg searchArg_sale = new SearchArg();
        searchArg_sale.matcher = new ParamMatcher();
        searchArg_sale.matcher.and(SalesSystemDef.SaleInfo.DEPARTMENT, ParamMatcher.IN, allDepartMentList);

        SysCrmSale sysCrmSale = Core.getSysKit(SysCrmSale.class); // 接口

        int rt = sysCrmSale.getSaleInfoListBySearch(searchArg_sale, saleList);
        if (rt != Errno.OK) {
            Log.logErr("getSaleList::getSaleInfoListBySearch err;rt=%s;", rt);
            return new FaiList<Param>();
        }

        FaiList<Param> resultList = new FaiList<Param>();
        for (Param saleInfo : saleList) {
            int sid = saleInfo.getInt(SalesSystemDef.SaleInfo.SID, 0);

            // 循环查各销售业务权限
            FaiList<Param> saleBizList = new FaiList<Param>();
            rt = sysCrmSale.getSaleInchargeBizBySid(sid, saleBizList);
            if (rt != Errno.OK) {
                Log.logErr("getSaleList::getSaleInchargeBizBySid err;rt=%s;sid=%d;", rt,sid);
                continue;
            }

            for (Param saleBizInfo : saleBizList) {
                int saleBiz = saleBizInfo.getInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, -1);
                if (bizList.contains(saleBiz)) {
                    Param saleInfo_clone = saleInfo.clone();
                    saleInfo_clone.setInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, saleBiz);
                    resultList.add(saleInfo_clone);   // 如果拥有权限，则说明经过了认证
//                    break;
                }
            }
        }

        return resultList;
    }

    // 获取业务值
    private int getInchargeBiz(String businessStr) {
        int business = -1;

        if ("yk".equals(businessStr)) {
            business = SalesSystemDef.SaleInfo.InchargeBiz.YK;
        }
        return business;
    }

   // 获取已经通过认证的销售
    private String getCertificatedSaleList(HttpServletRequest request) throws Exception {
        int department = SalesSystemDef.SaleInfo.SaleDepartment.YK; // 悦客部门

        // 获取已经通过悦客认证的销售
        FaiList<Param> saleList = getSaleList(department);
        FaiList<Param> ykList = Misc.getList(saleList, SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, SalesSystemDef.SaleInfo.InchargeBiz.YK);

        ykList = ykList == null ? new FaiList<Param>() : ykList;

        Param data = new Param();
        data.setList("ykList", ykList);
        return new Result().setData(data).toJson();
    }

    // 获取其他部门的销售
    private String getOtherSale(HttpServletRequest request) throws Exception {
        String departmentStr = Parser.parseString(request.getParameter(Key.BUSINESS), "");

        int business = getInchargeBiz(departmentStr);

        if (departmentStr.isEmpty() || business == -1) {
            return new Result(Errno.ARGS_ERROR, false, Message.ARG_ERR_BUSINESS).toJson();
        }
        SysCrmSale sysCrmSale = Core.getSysKit(SysCrmSale.class); // 接口

        // 获取已经认证过的销售列表
        FaiList<Integer> sidAlreadyCerList = new FaiList<Integer>(); // 已经认证的sid列表
        int department = SalesSystemDef.SaleInfo.SaleDepartment.YK; // 悦客部门
        FaiList<Param> saleList = getSaleList(department);
        saleList = Misc.getList(saleList, SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, business);
        saleList = saleList == null ? new FaiList<Param>() : saleList;
        for (Param param : saleList) {
            sidAlreadyCerList.add(param.getInt(SalesSystemDef.SaleInfo.SID, 0));
        }

        // 需要的部门
        FaiList<Integer> departmentList = new FaiList<Integer>();
        departmentList.add(SalesSystemDef.SaleInfo.SaleDepartment.MH);
        departmentList.add(SalesSystemDef.SaleInfo.SaleDepartment.HD);
        departmentList.add(SalesSystemDef.SaleInfo.SaleDepartment.ZH);
        departmentList.add(SalesSystemDef.SaleInfo.SaleDepartment.YK);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(SalesSystemDef.SaleInfo.DEPARTMENT, ParamMatcher.IN, departmentList);
        searchArg.matcher.and(SalesSystemDef.SaleInfo.SID, ParamMatcher.NOT_IN, sidAlreadyCerList);

        FaiList<Param> list = new FaiList<Param>();
        int rt = sysCrmSale.getSaleInfoListBySearch(searchArg, list);
        if (rt != Errno.OK) {
            Log.logErr("getOtherSale::getSaleInfoListBySearch err;rt=%d;", rt);
            return new Result(rt, false, Message.GET_SALE_ERR).toJson();
        }

        FaiList<Param> mhList = Misc.getList(list, SalesSystemDef.SaleInfo.DEPARTMENT, SalesSystemDef.SaleInfo.SaleDepartment.MH);
        FaiList<Param> hdList = Misc.getList(list, SalesSystemDef.SaleInfo.DEPARTMENT, SalesSystemDef.SaleInfo.SaleDepartment.HD);
        FaiList<Param> zhList = Misc.getList(list, SalesSystemDef.SaleInfo.DEPARTMENT, SalesSystemDef.SaleInfo.SaleDepartment.ZH);
        FaiList<Param> ykList = Misc.getList(list, SalesSystemDef.SaleInfo.DEPARTMENT, SalesSystemDef.SaleInfo.SaleDepartment.YK);

        if (mhList == null) {
            mhList = new FaiList<Param>();
        }
        if (hdList == null) {
            hdList = new FaiList<Param>();
        }
        if (zhList == null) {
            zhList = new FaiList<Param>();
        }
        if (ykList == null) {
            ykList = new FaiList<Param>();
        }

        FaiList<Param> resultList = new FaiList<Param>();
        Param info;

        info = new Param();
        info.setString(Key.LABEL, "门户销售");
        info.setList(Key.LIST, mhList);
        resultList.add(info);

        info = new Param();
        info.setString(Key.LABEL, "互动销售");
        info.setList(Key.LIST, hdList);
        resultList.add(info);

        info = new Param();
        info.setString(Key.LABEL, "增值销售");
        info.setList(Key.LIST, zhList);
        resultList.add(info);

/*
        info = new Param();
        info.setString(Key.LABEL, "悦客销售");
        info.setList(Key.LIST, ykList);
        resultList.add(info);
*/


        return new Result().setData(resultList).toJson();
    }

    private String removeCertificatedSale(HttpServletRequest request) throws Exception {

        int sid = Parser.parseInt(request.getParameter(Key.SID), 0);
        String businessStr = Parser.parseString(request.getParameter(Key.BUSINESS), "");

        int business = getInchargeBiz(businessStr);

        if (sid == 0) {
            return new Result(Errno.ARGS_ERROR, false, Message.ARG_ERR_SID).toJson();
        }
        if (businessStr.isEmpty() || business == -1) {
            return new Result(Errno.ARGS_ERROR, false, Message.ARG_ERR_BUSINESS).toJson();
        }

        SysCrmSale sysCrmSale = Core.getSysKit(SysCrmSale.class); // 接口

        FaiList<Param> hasBizInfoList = new FaiList<Param>();
        int rt = sysCrmSale.getSaleInchargeBizBySid(sid, hasBizInfoList);
        if (rt != Errno.OK) {
            Log.logErr("getSaleInchargeBizBySid err;rt=%d;", rt);
            return new Result(rt, false, Message.GET_BIZ_ERROR).toJson();
        }

        FaiList<Integer> delBizList = new FaiList<Integer>();
        delBizList.add(business);

        FaiList<Integer> hasBizList = new FaiList<Integer>();
        for (Param info : hasBizInfoList) {
            hasBizList.add(info.getInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, -1));
        }
        hasBizList.removeAll(delBizList);

        Log.logStd("set bizList=%s;", hasBizList);
        rt = sysCrmSale.setSaleInchargeBiz(sid, WebOss.getSacct(sid), hasBizList);
        if (rt != Errno.OK) {
            Log.logErr("setSaleInchargeBiz err;rt=%d;", rt);
            return new Result(rt, false, Message.REMOVE_ERR).toJson();
        }

        return new Result().toJson();
    }

    private String addCertificateSale(HttpServletRequest request) throws Exception {

        String sidListStr = Parser.parseString(request.getParameter(Key.SID_LIST), "");
        String businessStr = Parser.parseString(request.getParameter(Key.BUSINESS), "");

        int business = getInchargeBiz(businessStr);

        FaiList<Integer> sidList = FaiList.parseIntList(sidListStr);

        if (sidListStr.isEmpty() || sidList == null || sidList.isEmpty()) {
            return new Result(Errno.ARGS_ERROR, false, Message.ARG_ERR_SID_LIST).toJson();
        }
        if (businessStr.isEmpty() || business == -1) {
            return new Result(Errno.ARGS_ERROR, false, Message.ARG_ERR_BUSINESS).toJson();
        }

        SysCrmSale sysCrmSale = Core.getSysKit(SysCrmSale.class); // 接口

        int cnt = 0;
        // 循环为每一个销售添加业务权限
        for (int sid : sidList) {
            FaiList<Param> hasBizInfoList = new FaiList<Param>();
            int rt = sysCrmSale.getSaleInchargeBizBySid(sid, hasBizInfoList);
            if (rt != Errno.OK) {
                Log.logErr("getSaleInchargeBizBySid err;rt=%d;", rt);
                return new Result(rt, false, Message.GET_BIZ_ERROR).toJson();
            }

            FaiList<Integer> hasBizList = new FaiList<Integer>();
            for (Param info : hasBizInfoList) {
                hasBizList.add(info.getInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, -1));
            }
            hasBizList.add(business); // 每一个销售都增加一个相应业务的权限

            Log.logStd("set bizList=%s;", hasBizList);
            rt = sysCrmSale.setSaleInchargeBiz(sid, WebOss.getSacct(sid), hasBizList);
            if (rt != Errno.OK) {
                Log.logErr("setSaleInchargeBiz err;rt=%d;", rt);
                String errMsg = "添加销售失败，添加成功数：" + cnt;
                return new Result(rt, false, errMsg).toJson();
            }
            cnt ++;
        }
        return new Result().toJson();
    }
%>


<%
    String output = "";
    try {
        String cmd = Parser.parseString(request.getParameter("cmd"), "");
        if (Str.isEmpty(cmd)) {
            output = new Result(Errno.ARGS_ERROR, false, Message.NO_METHOD).toJson();
        }

        else if ("getOtherSale".equals(cmd)) {
            output = getOtherSale(request);
        }

        else if ("getCertificatedSaleList".equals(cmd)) {
            output = getCertificatedSaleList(request);
        }
        else if ("removeCertificatedSale".equals(cmd)) {
            output = removeCertificatedSale(request);
        }

        else if ("addCertificateSale".equals(cmd)) {
            output = addCertificateSale(request);
        }

    } catch (Exception e) {
        output = new Result(Errno.ERROR, false, Message.SYS_ERR).toJson();
    }

    out.print(output);

%>