package fai.webhdoss.service;

import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scTemplate.ScTemplateListVO;
import fai.webhdoss.model.vo.scTemplate.ScTemplateVO;

import javax.servlet.http.HttpServletRequest;

public interface ScTemplateService {
    JsonResult getScTemplateInfo(int id, int protoId);

    JsonResult getConfInfo();

    JsonResult setScTemplateInfo(ScTemplateVO vo);

    JsonResult getScTemplateList(ScTemplateListVO vo);

    JsonResult setScTemplateStatus(int id, int status);

    JsonResult copyScTemplate(int id);

    JsonResult uploadTmpFile(HttpServletRequest request) throws Exception;

    JsonResult getDomain();

    JsonResult advanceUploadFile4Sc(HttpServletRequest request) throws Exception;
}
