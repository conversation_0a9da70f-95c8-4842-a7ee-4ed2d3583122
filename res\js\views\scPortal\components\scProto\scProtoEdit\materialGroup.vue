<!-- 素材组配置 -->
<template>
  <div class="mt-[40px] material-group">
    <p class="mb-[10px] text-[16px] font-bold">素材组配置</p>
    <el-form-item label="素材组数量配置：" prop="materialNum" class="mb-[20px]">
      <el-input-number
        size="small"
        :value="resFormList.length"
        @change="handleMaterialNumChange"
        :min="0"
        :max="50"
      ></el-input-number>
    </el-form-item>
    <el-table :data="resFormList" class="material-group__table">
      <el-table-column prop="id" label="素材组ID" width="100">
      </el-table-column>
      <el-table-column prop="label" label="素材名称" width="270">
        <template slot-scope="scope">
          <el-form-item :prop="'resFormList.' + scope.$index + '.label'" :rules="[{ required: true, message: '请输入素材名称', trigger: ['blur', 'change'] }]">
            <el-input class="!w-[230px]" minlength="1" maxlength="50" v-model="scope.row.label" placeholder="请输入素材名称"></el-input>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column prop="required" label="是否必须上传" width="180">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.required"></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="modifyMouth"
        label="是否开启改嘴型功能"
        width="180"
        v-if="editType === ScProductType.VIDEO"
      >
        <template slot-scope="scope">
          <el-switch v-model="scope.row.modifyMouth"></el-switch>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { ScProductType } from "@/views/scPortal/config/index.js";

export default {
  name: "MaterialGroup",
  props: {
    /**
     * 素材组列表
     */
    resFormList: {
      type: Array,
      default: () => [],
    },
    /**
     * 素材组类型
     */
    editType: {
      type: Number,
      default: ScProductType.VIDEO,
    },
  },
  data() {
    return {
      ScProductType,
    };
  },
  methods: {
    /**
     * 创建新的素材组项
     * @param {number} index 索引号
     * @returns {Object} 新的素材组配置项
     */
    createMaterialItem(index) {
      return {
        id: index + 1,
        required: false,
        type: this.editType,
        label: `素材组${index + 1}`,
        modifyMouth: false,
      };
    },

    /**
     * 处理素材组数量变化
     * @param {number} newNum 新的数量
     */
    handleMaterialNumChange(newNum) {
      const currentLength = this.resFormList.length;
      
      if (newNum > currentLength) {
        // 增加素材组
        const newItems = Array.from({ length: newNum - currentLength }, (_, index) => 
          this.createMaterialItem(currentLength + index)
        );
        this.$emit('update:resFormList', [...this.resFormList, ...newItems]);
      } else {
        // 减少素材组
        this.$emit('update:resFormList', this.resFormList.slice(0, newNum));
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.material-group {
  &__table {
    ::v-deep .el-table {
      .cell {
        overflow: visible;
      }
    }
  }
}
</style>
