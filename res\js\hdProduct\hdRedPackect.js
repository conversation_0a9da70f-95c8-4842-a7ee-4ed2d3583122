new Vue({
  el: "#app",
  data: {
    searchNum: null,
    list: null,
    pageNo: 1,
    pageSize: 10,
    totalSize: 0,
  },
  methods: {
    handlePageChange(pageNo) {
      this.pageNo = pageNo;
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageNo = 1;
      this.pageSize = pageSize;
      this.getList();
    },
    getList() {
      const loading = this.$loading({
        text: "正在加载，请稍候...",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let url = `/api/blackAid/list?_TOKEN=${document
        .querySelector("#_TOKEN")
        .getAttribute("value")}&pageNo=${this.pageNo}&pageSize=${
        this.pageSize
      }`;
      if (this.searchNum) {
        url += `&aid=${this.searchNum}`;
      }
      fetch(url)
        .then((response) => response.json())
        .then((data) => {
          this.list = data.data;
          this.totalSize = data.totalSize;
        })
        .catch((error) => {
          console.error("Error getting list:", error);
        })
        .finally(() => {
          loading.close();
        });
    },
    handleSearch() {
      this.pageNo = 1;
      this.getList();
    },
    handleUnban(row) {
      // 改用elementui的确认弹窗
      this.$confirm("确认要对该用户进行解除封禁操作？", "解除封禁", {
        confirmButtonText: "是",
        cancelButtonText: "否",
      }).then(() => {
        this.unban(row);
      });
    },
    unban(row) {
      const loading = this.$loading({
        text: "正在解除封禁，请稍候...",
        background: "rgba(0, 0, 0, 0.7)",
      });

      fetch(
        `/api/blackAid/unbanned?aid=${row.aid}&_TOKEN=${document
          .querySelector("#_TOKEN")
          .getAttribute("value")}`,
        { method: "POST" }
      )
        .then((response) => response.json())
        .then(() => {
          this.$message({ type: "success", message: "解除封禁成功" });
          this.getList();
        })
        .catch((error) => {
          console.error("Error unban:", error);
        })
        .finally(() => {
          loading.close();
        });
    },
    formatTimestamp(timestamp) {
      if (timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString("zh-CN", { hour12: false });
      }
      return "—";
    },
    formatStatus(status) {
      return status === 0 ? "封禁中" : "已解封";
    },
  },
  mounted() {
    this.getList();
  },

  template: `
    <el-main>
      <el-row>
        <el-col :span="8">
          <el-input v-model.number="searchNum" placeholder="搜索aid"></el-input>
          <el-button size="small" style="margin-left: 8px;" type="primary" @click="handleSearch">搜索</el-button>
        </el-col>
      </el-row>
      <el-row style="margin-top: 20px">
        <el-table :data="list" style="width: 100%">
          <el-table-column prop="agentAid" label="FID" width="100">
            <template slot-scope="scope">
              {{ scope.row.agentAid || "—" }}
            </template>
          </el-table-column>
          <el-table-column prop="aid" label="AID" width="100"></el-table-column>
          <el-table-column prop="create_time" label="封禁时间" width="200">
            <template slot-scope="scope">
              {{ formatTimestamp(scope.row.create_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="reasonList" label="封禁类型" width="200">
            <template slot-scope="scope">
              {{ scope.row.reasonList.join("，") || "—" }}
            </template>
          </el-table-column>
          <el-table-column prop="relieve_time" label="解封时间" width="200">
            <template slot-scope="scope">
              {{ formatTimestamp(scope.row.relieve_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template slot-scope="scope">
              {{ formatStatus(scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column prop="relieveSidName" label="操作人" width="100"></el-table-column>
          <el-table-column label="Actions" width="180">
            <template slot-scope="scope">
              <el-button type="text" v-if="scope.row.status === 0" @click="handleUnban(scope.row)">
                解除封禁
              </el-button>
              <el-button type="text" v-else>—</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-row style="margin-top: 20px">
        <el-pagination layout="sizes, prev, pager, next" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="totalSize" @size-change="handleSizeChange" @current-change="handlePageChange">
        </el-pagination>
      </el-row>
    </el-main>
  `,
});
