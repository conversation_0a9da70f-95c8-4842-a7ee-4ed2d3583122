package fai.webhdoss.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Description 互动案例预览窗封装
 * @date 2021/3/14 12:00
 */
@Data
@Accessors(chain = true)
@ApiModel("互动模板列表vo")
public class HdModelListVO {

    /** 节日 */
    int festivel = -1;

    /** 类型 */
    int type = -1;

    /** 设计师 */
    int designer = -1;

    /** 发布 */
    int pub = -1;

    /** 热点*/
    int hotPoint = -1;

    /** 原型*/
    int proType = -1;

    /** 账号 */
    int acct = -1;

    int pageLimit = 100;

    /** 游戏id */
    int gstyle = -1;

    /** 样板名称 */
    int modelName = -1;
}
