﻿html,body,#invasitigateBox{
	height: 100%;
}
.container .menuBox{
    padding-left: 20px;
}
.menuBox .investigateInfo{
    display: inline-block;
    width: 80px;
}
.menuBox .invastigate .el-input{
	width: 300px;
} 
.menuBox .invastigate .button-group {
	display: inline-block;
    margin-left: 190px;
}
.poupInvastigate {
	width: 707px;
	height: 696px;
	overflow: hidden;
}
.poupInvastigate .el-message-box__content{
	padding: 0px;
}
.poupInvastigate .poupContent{
	padding: 0px;
}
.poupInvastigate  .header{
	font-size: 14px;
	color: #ffffff;
	height: 48px;
	line-height: 48px;
	padding-left: 20px;
	background: #744cff;
	position: relative;
}
.poupInvastigate  .header .closeBtn{
	position: absolute;
	right: 20px;
	top: 50%;
	width: 12px;
	height: 12px;
	transform: translate(0%,-50%);
	background: url('../image/invesigate/invastigaeClose.png') no-repeat;
	background-size: 100% 100%;
	cursor: pointer;
	opacity: 0.5;
}
.poupInvastigate  .questionBox{
	background: #ffffff;
	color: #535353;
	overflow: auto;
	height: 648px;
	padding: 0px 20px;
}
.poupInvastigate  .questionBox .quesition .questionInner{
	margin-top: 18px;
	font-size: 14px;
}
.poupInvastigate  .questionBox .quesition .answerBox{
	padding-top: 8px;
}
.poupInvastigate  .questionBox .quesition .clearFix{
	overflow: hidden;
}
.poupInvastigate  .questionBox .quesition .answerItem{
	float: left;
	height: 28px;
	line-height: 28px;
	font-size: 14px;
	padding: 0px 16px;
	border: 1px solid #dadada;
	position: relative;
	border-radius: 4px;
	cursor: pointer;
	margin-right: 10px;
	margin-top: 10px;
}
.poupInvastigate  .questionBox .quesition .answerItem:hover{
	border: 1px solid #754bff;
}
.poupInvastigate  .questionBox .quesition .answerItem.active{
	border: 1px solid #754bff;
}
.poupInvastigate  .questionBox .quesition .answerItem.active:after{
	content: "";
	position: absolute;
	right: -5px;
	top: -5px;
	width: 13px;
	height: 13px;
	background: url("../image/invesigate/active.png")no-repeat;
	background-size: 100% 100%;
	z-index: 99;
}
.poupInvastigate  .questionBox .quesition .otherInput{
	margin-top: 10px;
}
.poupInvastigate  .questionBox .quesition .otherInput.hide{
	display: none;
}
.poupInvastigate  .questionBox .quesition .otherInput input{
	border: 1px solid #dadada;
	width: 648px;
	height: 40px;
	border-radius: 4px;
	padding-left: 12px;
	font-size: 14px;
	outline: none;
	color: #b2b2b2;
}
.poupInvastigate  .questionBox .quesition .otherInput input:hover{
	border: 1px solid #754bff !important;
}
.poupInvastigate  .questionBox .quesition .otherInput:hover input{
	border: 1px solid #754bff !important;
}
.poupInvastigate  .questionBox .quesition .otherInput input:focus{
	border: 1px solid #754bff !important;
	color: #535353 !important;
}
.poupInvastigate  .questionBox #submitButtom{
	width: 200px;
	height: 40px;
	color: #ffffff;
	background: #754bff;
	text-align: center;
	line-height: 40px;
	margin: 30px auto;
	cursor: pointer;
	border-radius: 20px;
}
.poupInvastigate  .questionBox #submitButtom:hover{
	background: #6742e1;
}