<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	/*if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}*/
	Param exposeParam = new Param();
	{
		exposeParam.setString( "hdossRoot",WebHdOss.getDomainUrl() );
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>活动列表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_invastiGate")%>"/>
    </head>

    <body>	
		<div 
			class="container" 
			id="invasitigateBox" 
			v-loading="isLoading"
			element-loading-text="拼命加载中"
			element-loading-spinner="el-icon-loading"
			element-loading-background="rgba(0, 0, 0, 0.8)">
			<!--页面顶部标题-->
			<div style="text-align: center;">
				<h3 style="margin-top: 0px">{{title}}</h3>
			</div>

			<!--表单-->
			<div class="menuBox">
				<el-form :inline="true" :model="formQuery" ref="formQuery" :rules="rules" class="invastigate">
					<div >
						<el-form-item prop="invastiNum" >
							<span class="investigateInfo">问卷编码</span>
							<el-input type="text" v-model.number="formQuery.invastiNum" placeholder="问卷编码"></el-input>
						</el-form-item>
						<el-form-item prop="invastiName">
							<span class="investigateInfo">问卷名称</span>
							<el-input type="text" v-model="formQuery.invastiName" placeholder="问卷名称"></el-input>
						</el-form-item>
					</div>
					<div>
						<el-form-item prop="invastiRemark">
							<span class="investigateInfo">备注</span>
							<el-input type="text" v-model="formQuery.invastiRemark" placeholder="备注"></el-input>
						</el-form-item>
						<div class="button-group">
							<el-button type="primary" @click="dialogFormVisible = true" icon="el-icon-plus" class="searchBtn">新增</el-button>
							<el-button type="primary" @click="searchInvasi('formQuery')" icon="el-icon-search" class="searchBtn">查询</el-button>
						</div>
					</div>
				</el-form>
			</div>
			<div class="showTable">
				<template>
					<el-table 
						:data="tableData" 
						border
						style="width: 100%" 
						 >
						<el-table-column prop="id" label="问卷编码" width="100">
						</el-table-column>
						<el-table-column prop="title" label="问卷名称" width="300">
						</el-table-column>
						<el-table-column prop="total" label="样本数量" width="100">
						</el-table-column>
						<el-table-column prop="remark" label="备注" width="300">
						</el-table-column>
						<el-table-column prop="detail" label="详细数据" width="100">
							<template slot-scope="scope">
								<el-button @click="handleLookInfo(scope.row)" type="text" size="small">查看</el-button>
							</template>
						</el-table-column>
						<el-table-column prop="info" label="问卷内容" width="100">
							<template slot-scope="scope">
								<el-button @click="handleSee(scope.row)" type="text" size="small">预览</el-button>
							</template>
						</el-table-column>
					</el-table>
				</template>
				<div>
					<el-pagination
						background
						layout="prev, pager, next"
						:current-page.sync="formQuery.page"
						:page-size="formQuery.pageSize"
						:total="formQuery.allPageNum">
					</el-pagination>
				</div>
			</div>
			<!-- Form -->
			<el-dialog title="新增问卷" :visible.sync="dialogFormVisible">
				<el-form :model="invasiFormData">
					<el-form-item label="id" :label-width="formLabelWidth">
						<el-input v-model="invasiFormData.id" auto-complete="off"></el-input>
					</el-form-item>
					<el-form-item label="问卷编码" :label-width="formLabelWidth">
						<el-input v-model="invasiFormData.type" auto-complete="off"></el-input>
					</el-form-item>
					<el-form-item label="问卷名称" :label-width="formLabelWidth">
						<el-input v-model="invasiFormData.title" auto-complete="off"></el-input>
					</el-form-item>
					<el-form-item label="样本内容" :label-width="formLabelWidth">
						<el-input v-model="invasiFormData.questions" auto-complete="off"></el-input>
					</el-form-item>
					<el-form-item label="备注" :label-width="formLabelWidth">
						<el-input v-model="invasiFormData.remark" auto-complete="off"></el-input>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="cancelAddInvasi">取 消</el-button>
					<el-button type="primary" @click="addInvasi">确 定</el-button>
				</div>
			</el-dialog>
		</div>	
    </body>
	<script type="text/javascript" charset="utf-8">
		var exposeParam = <%=exposeParam%>;
	</script>
	<script src='<%=HdOssResDef.getResPath("js_invastiGate")%>' type="text/javascript" charset="utf-8"></script>
</html>