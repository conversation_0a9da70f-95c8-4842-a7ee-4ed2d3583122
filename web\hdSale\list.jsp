<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.webhdoss.WebHdOss"%>


<%
	if(!WebHdOss.checkSession(response)){
		return;
	}
	if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
		out.println(Auth.checkFaiscoAuth("authHDSale", false));
		out.println("没有权限");
		return;
	} 

	if(!Web.getDebug() && WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE) && WebOss.getSid() != 1355 ){
		out.println("请到新销售系统进行操作");
		return;
	} 
	
%>



<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>客户列表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
		<!-- <style type="text/css">
          .el-dialog__header{
               background-color: #9D9D9D !important;
           }
           .el-dialog__title{
              color: #FFF !important;
           }
        </style>
		 -->

		<style type="text/css">
			.underlineClass{
				text-decoration: underline;
			}
		</style>
		<style>
		  .transfer-footer {
		    margin-left: 20px;
		    padding: 6px 5px;
		  }
		</style>
    </head>
    <body >
		<div id = "hdSale-list">
			<!--unique:因成交库增加短信发送功能，故把原来放在个人库pane的短信发送框放在外面来-->
			<el-dialog v-cloak title="发送短信给" :visible.sync="dialogVisible" width="30%">
				<div class="messageBox">
					<div>
						短信模板
						<el-select v-model="message.messageModel">
							<el-option v-for="select in message.messageModelList" :label="select.modelName" :value="select.id" :key="select.id"></el-option>
						</el-select>
					</div>
					<div v-if="message.singleMobile!=''">客户手机<el-input  v-model="message.singleMobile" placeholder="请输入号码" ></el-input></div>
				</div>
				<span slot="footer" class="dialog-footer">
					<el-button @click="handleMessage(false)">取 消</el-button>
					<el-button type="primary" @click="handleMessage(true)">确 定</el-button>
				</span>
			</el-dialog>

            <el-dialog  v-cloak title="标记" :visible.sync="biaojiDialog.show" width="30%" center @close="biaojiClose()" >

                <el-form label-width="120px">
                    <el-form-item label="标记:">
                        <el-select id="intent" v-model="biaojiDialog.intent">
                            <el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="原因:">
                        <el-select id="reason" v-model="biaojiDialog.reason">
                            <el-option v-for="reason in biaojiDialog.reasonList" v-if="reason.fatherId == 0" :key="reason.reason"
                                       :label='reason.reason' :value="reason.reason"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="企微提醒时间:">
                        <el-date-picker
                                v-model="biaojiDialog.talkNextTime"
                                :picker-options="biaojiDialog.pickerOptions"
                                format="yyyy-MM-dd HH:mm:ss"
                                type="datetime"
                                placeholder="下次联系时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="企微提醒内容:">
						<el-select v-model="biaojiDialog.remindContent" style="float:center;; width:48%;">
						    <el-option  label="现在不方便，等会儿联系。" value="0"></el-option>
							<el-option  label="意向购买需要回访。" value="1"></el-option>
							<el-option  label="下了订单，联系不上。" value="2"></el-option>
							<el-option  label="其他，看销售系统备注。" value="3"></el-option>
						</el-select>
                    </el-form-item>
                    <el-form-item label="当前意向度:">
                        <el-radio-group v-model="biaojiDialog.intentLevel" size="small">
                            <el-radio-button :label="1">A</el-radio-button>
                            <el-radio-button :label="2">B</el-radio-button>
                            <el-radio-button :label="3">C</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="备注:">
                        <el-input type="textarea" v-model="biaojiDialog.corpMark" :rows="4"></el-input>
                    </el-form-item>
                    <div style="float: right;padding-left: 5%;padding-top: 10px">
                        <el-button @click="biaojiClose()" size="mini" >取消</el-button>
                        <el-button type="primary" @click="submitBiaoji" size="mini" >提交</el-button>
                    </div>
                </el-form>
            </el-dialog>
			
			 <el-dialog  v-cloak title="购买产品列表" :visible.sync="buyProductDialog.show" width="40%" center @close="buyProductDialogClose()" >
			      
                       <div v-if="aidMsgDialog.data.buyProduct">
                            <div style="text-align:left" v-for="item in aidMsgDialog.data.buyProduct">{{item.payProductName}} {{item.expireTime}} 总价格:{{item.vipUnitPrice*item.payTotalMonths}}元</div>
				       </div>
	
             </el-dialog>


			<el-dialog v-dialogdrag   v-cloak title="基本信息" :visible.sync="aidMsgDialog.show" @close="closeAidDialog" width="50%" center >
				<div style="padding-left: 5%">
					<div style=" float:left; width:35%;">
						AID：<a v-bind:href="aidMsgDialog.aidUrl" target='_blank' >{{aidMsgDialog.data.aid}}</a><br>
						账号：<a v-text="aidMsgDialog.data.aacct"></a><br>
						企业名称：<a v-text="aidMsgDialog.data.name"></a><br>
						<span v-if="tab != 8">实名手机：<a v-text="aidMsgDialog.data.signupMobile"></a><br></span>
						<span v-if="tab != 8">验证手机：<a v-text="aidMsgDialog.data.verifyPhone"></a><br></span>
						注册邮箱：<a v-text="aidMsgDialog.data.email"></a><br>
						验证邮箱：<a v-text="aidMsgDialog.data.verifyEmail"></a><br>
						负责人：<a v-text="aidMsgDialog.data.regPersonName"></a><br>
						<span v-if="tab != 8">电话：<a v-text="aidMsgDialog.data.regPhoneName"></a><br></span>
						<span v-if="tab != 8">手机：<a v-text="aidMsgDialog.data.regMobileName"></a><br></span>
						行业：<a v-text="aidMsgDialog.data.tradeName"></a><br>
					</div>
					<div style=" float:left; width:35%;">
						<div>
							注册来源：<a v-text="aidMsgDialog.data.taName"></a><br>
							注册主体：<a v-text="aidMsgDialog.data.regSubject"></a><br>
							注册用途：<a v-text="aidMsgDialog.data.corpGoalName"></a><br>
							注册时间：<a v-text="aidMsgDialog.data.createTimeName"></a><br>
							<%--登录时间：<a v-text="aidMsgDialog.data.loginTimeName"></a><br> --%>
							进入互动平台时间：<a v-text="aidMsgDialog.data.intoSystemName"></a><br>
							互动开通时间：<a v-text="aidMsgDialog.data.openHDTimeName"></a><br>
							助手开通时间：<a v-text="aidMsgDialog.data.openWXTimeName"></a><br>
							传单开通时间：<a v-text="aidMsgDialog.data.openFlyerTimeName"></a><br>
							建站开通时间：<a v-text="aidMsgDialog.data.openJzTimeName"></a><br>	
							悦客开通时间：<a v-text="aidMsgDialog.data.openYkTimeName"></a><br>	
                            门户产品购买：<a v-if="aidMsgDialog.data.buyProduct"  style="cursor: pointer;" @click="showBuyProduct">是</a>
							<a v-else>否</a>
							<br>								
						</div>
					</div>
					<div style=" float:left; width:30%;">
						一周登录次数： <a v-text="aidMsgDialog.loginOneWeekCount"></a><br>
						一周操作次数： <a v-text="aidMsgDialog.optOneWeekCount"></a><br>
						关键字：<a v-text="aidMsgDialog.data.tp"></a><br>
						首次创建游戏时间：<br><a v-text="aidMsgDialog.firstCreateGameTime"></a><br>
						<span v-if="tab != 8">业务：<a v-text="aidMsgDialog.data.business" ></a><br/></span>
						<span v-if="tab != 8">归属库：<a v-text="aidMsgDialog.biaoji.status" ></a><br/></span>
						
						<span v-if="tab != 8"> 一周咨询客服次数：<a v-text="aidMsgDialog.data.oneWeekTalkNum"></a><br></span>
						<span v-if="tab != 8">最后咨询客服时间 ：<br><a v-text="aidMsgDialog.data.lastConsultTime"></a><br></span>
						
					</div>

				</div>

				<!-- <div style="clear:both ; padding-top: 2%;padding-left: 5%">
					企微提醒时间：<el-date-picker v-model="aidMsgDialog.QWRemindTime" type="datetime"placeholder="选择日期时间"
					:picker-options="aidMsgDialog.pickerOptions" format="yyyy-MM-dd HH:mm:ss"></el-date-picker><br>
					<div style="float: left; width: 90%;padding-top: 5px">
						提醒内容：<el-input style="width: 90%" v-model="aidMsgDialog.QWRemindContent"  placeholder="请输入提醒内容" resize="none"  ></el-input>
					</div>
					<div style="float: right; width: 10% ; text-align: center ;padding-top: 5px ">
						<el-button type="primary" @click="addQWRemind(aidMsgDialog.data.aid,aidMsgDialog.QWRemindTime,aidMsgDialog.QWRemindContent)" size="mini" >设置</el-button>
					</div>
				</div>  -->

				<div style="clear:both ; padding-top: 2%;padding-left: 5%">
					<%--<iframe :src="'/hdSale/showHdBoxOut.jsp?iframeId=fai-parent-hd-1&aid='+aidMsgDialog.data.aid" id="fai-parent-hd-1" frameborder="0" scrolling="no" style="width:100%;height:45px;"></iframe>--%>
					标记(意向)：<span @click="showBiaojiBox(aidMsgDialog.data.aid)"
                                 style="cursor: pointer;text-decoration: underline;color: #666666;"
                                 type="primary" size="mini">
                        {{aidMsgDialog.biaoji.intentName}}
                    </span>&nbsp&nbsp
					<a :href="'//'+HdDef.url.getOssDomain+'/index.jsp?t=bss&amp;u=%2Fbss%2FcorpLogin.jsp%3Faid%3D'+aidMsgDialog.data.aid" target="_blank">登录情况</a>&nbsp&nbsp
					<a :href="'//'+HdDef.url.getOssDomain+'/index.jsp?t=cs&u=%2Fcs%2ForderSearch.jsp?aid='+aidMsgDialog.data.aid" target="_blank">订单列表</a>&nbsp&nbsp
					<a :href="'//'+HdDef.url.getOssDomain+'/index.jsp?t=cs&amp;u=/cs/couponList.jsp?acct='+aidMsgDialog.data.aacct" target="_blank">现金券情况</a>&nbsp&nbsp
					<el-button @click.native.prevent="sendMessag(aidMsgDialog.index,dataList)"  type="text" size="small">发送短信</el-button>
					<a :href="'//'+'<%=Web.getOssDomain()%>'+'/labelRes/labelResInfo.jsp?aid=' + aidMsgDialog.data.aid + '&updateFrom='+'<%=OptLabelResDef.UpdateFrom.HD_SALE%>'" target="_blank" style="color:red;font-weight:bold;"  v-if="tab != 8">更新人肉标签</a><br>
					<el-tag style="margin: 0px 0px 10px 76px"><a :href="'//'+HdDef.url.getHdPortalDomain+'?__tmpaid='+aidMsgDialog.data.aid" target='_blank'>互动管理平台</a></el-tag>&nbsp&nbsp
					<el-tag><a :href="'//'+HdDef.url.getPortalHost+'?__tmpaid='+aidMsgDialog.data.aid" target='_blank'>企业中心</a></el-tag>&nbsp&nbsp
					<el-tag><a :href="'//'+HdDef.url.getFlyerManageDomain+'/manage/flyerManage.jsp?__tmpaid='+aidMsgDialog.data.aid" target='_blank'>微传单管理平台</a></el-tag>&nbsp
					<el-tag><a :href="'//'+HdDef.url.getWxastDomain+'?__tmpaid='+aidMsgDialog.data.aid" target="_blank">助手管理平台</a></el-tag>&nbsp
					<el-tag><a :href="'//'+HdDef.url.getYkDomain+'/qrCode.jsp?cmd=genCsUrl&yid=1&aid='+aidMsgDialog.data.aid" target='_blank'>悦客商家端</a></el-tag>&nbsp&nbsp
					<el-tag v-if="aidMsgDialog.lastConsultTime"><a :href="'http://it.faisco.cn/page/serviceSession/index.jsp#/sessionRecord?aid='+aidMsgDialog.data.aid" target='_blank' >客服会话</a></el-tag>&nbsp&nbsp
					<el-button v-if="tab != 8" @click="setSpecialFocus(aidMsgDialog.data.aid,aidMsgDialog.biaoji.flag)"  plain style="padding-right:12px;padding-left:12px;padding-top:9px;padding-bottom:9px;font-size:12px;" type="primary" size="medium"> {{aidMsgDialog.sFlag}}</el-button>&nbsp
					<el-button v-if="tab != 8" @click="banSendMsg(aidMsgDialog.data.aid,aidMsgDialog.biaoji.flag)"  plain style="padding-right:12px;padding-left:12px;padding-top:9px;padding-bottom:9px;font-size:12px;" type="primary" size="medium">{{aidMsgDialog.sendMsg?'禁止发送短信':'继续发送短信'}}</el-button>&nbsp
                    <el-button v-if="!(tab == 8 || tab == 9)" @click="releaseRecourse(aidMsgDialog.data.aid)"  plain style="background-color:red; border: 1px solid red; padding-right:12px;padding-left:12px;padding-top:9px;padding-bottom:9px;font-size:12px;color:white;" type="primary" size="medium">释放到公海</el-button>&nbsp
                    <el-button v-if="tab == 1 || tab == 2 || tab == 3" @click="showInvalidDialog" size="medium" plain style="background-color:#dedede; border: 1px solid #D0D0D0;;font-size:12px;color: black"><i class="el-icon-warning"></i>&nbsp;转入无效库</el-button>&nbsp;
				</div>

				<div style="padding-top: 2%;padding-left: 2%;padding-right: 2%;">
					<el-input type="textarea"  class="fai-textarea"  :rows="6" :disabled="true" resize="none" :value="aidMsgDialog.mark"></el-input>
					<div style="width: 100%">
						<div style="float: left; width: 90%;padding-top: 2px">
							<el-input style="width: 100%" v-model="aidMsgDialog.editMark"  placeholder="请输入备注" resize="none"  ></el-input>
						</div>
						<div style="float: right; width: 10% ; text-align: center ;padding-top: 2px ">
							<el-button type="primary" @click="submitMark(aidMsgDialog.data.aid,aidMsgDialog.editMark,aidMsgDialog.mark)" size="mini" >提交</el-button>
						</div>
					</div>

				</div>

				<div style=" clear: both; width: 100%;padding-top: 10px;">
					<el-button plain @click="changeAid('up',aidMsgDialog.data.aid)" type="info" size="mini" >< 上一个</el-button>
					
					<el-button plain @click="changeAid('down',aidMsgDialog.data.aid)"type="info" size="mini" style="float: right"  >下一个 ></el-button> 
				</div>

			</el-dialog>
			
			<!-- 用户自定义页面  zhs  -->
			<el-dialog v-dialogdrag   v-cloak title="用户自定义页面" :visible.sync="showCustomPagelog.show" @close="commitCustomPageInfo" width="30%" center>
						<div style="text-align: center;" >
						    <el-transfer
						      style="text-align: left; display: inline-block;"
						      v-model="showCustomPagelog.value"
						      filterable
						      :titles="['选项', '需要选项']"
						      :button-texts="['到左边', '到右边']"
						      :format="{
						        noChecked:'${total}' ,
						        hasChecked: '${checked}/${total}'
						      }"
						      :data="showCustomPagelog.data">
						    </el-transfer>
						</div>
			</el-dialog>

			<%--修改领取时间 dialog--%>
			<el-dialog v-cloak title="修改领取时间" :visible.sync="ediReceiveTimePanel" width="30%">
				<div class="editTimeBox">
					<div class="box"><span>图片资料</span>
						<el-upload
						class="upload-demo"
						action="../ajax/hdSale_h.jsp?cmd=upLoadFile"
						:limit= 1
						:before-upload="beforeFileUpload"
						:on-remove="handleRemove"
						:name="editReceiveTimeData.name"
						:on-success="fileUploadSuccess"
						list-type="picture">
						<el-button size="small" type="primary">点击上传</el-button>
						<div slot="tip" class="el-upload__tip">文件大小不超过5m</div>
					  </el-upload>
					</div>
					<div  class="box"><span>客户aid</span><el-input  v-model="editReceiveTimeData.aid" :disabled="true" placeholder="请输入内容" ></el-input></div>
					<div  class="box"><span>销售</span><el-input  v-model="editReceiveTimeData.sale"  :disabled="true"  placeholder="请输入内容" ></el-input></div>
					<div  class="box"><span>旧领取时间</span><el-input  v-model="editReceiveTimeData.oldReceiveTime" :disabled="true" placeholder="请输入内容" ></el-input></div>
					<div  class="box">新领取时间
						<el-date-picker class="demonstration"  type="datetime" default-time="12:00:00" v-model="editReceiveTimeData.newReceiveTime" format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期" :editable=false></el-date-picker>
					</div>
				</div>
				<span slot="footer" class="dialog-footer">
					<el-button @click="editReceiveTime(false)">取 消</el-button>
					<el-button type="primary" @click="editReceiveTime(true)">确 定</el-button>
				</span>
			</el-dialog>

			<%--B库申领回A库 dialog--%>
			<el-dialog v-cloak title="申领回A库" :visible.sync="backToAkuPanel" width="30%">
				<div class="editTimeBox">
					<div class="box"><span>图片资料</span>
						<el-upload
								class="upload-demo"
								action="../ajax/hdSale_h.jsp?cmd=upLoadFile"
								:limit= 1
								:before-upload="beforeFileUpload"
								:on-remove="handleRemove"
								:name="backToAkuData.name"
								:on-success="fileUploadSuccess"
								list-type="picture">
							<el-button size="small" type="primary">点击上传</el-button>
							<div slot="tip" class="el-upload__tip">文件大小不超过5m</div>
						</el-upload>
					</div>
					<div  class="box"><span>客户aid</span><el-input  v-model="backToAkuData.aid" :disabled="true" placeholder="请输入内容" ></el-input></div>
					<div  class="box"><span>销售</span><el-input  v-model="backToAkuData.sale"  :disabled="true"  placeholder="请输入内容" ></el-input></div>
				</div>
				<span slot="footer" class="dialog-footer">
					<el-button @click="submitBackToAkuData(false)">取 消</el-button>
					<el-button type="primary" @click="submitBackToAkuData(true)">确 定</el-button>
				</span>
			</el-dialog>

			<%-- 转入无效库弹窗 --%>
			<el-dialog v-cloak title="入库类型" :visible.sync="isShowInvalidDialog" width="250px">
				<div style="height: 140px;width: 200px;margin: auto">
					<el-form size="mini">
                        <el-form-item label="入库类型">
                            <el-select v-model="invalidType">
                                <el-option :value="1" label="学生"></el-option>
                                <el-option :value="2" label="U+资源"></el-option>
                                <el-option :value="3" label="代理商"></el-option>
                                <el-option :value="4" label="已换账号购买"></el-option>
                                <el-option :value="5" label="态度恶劣"></el-option>
                                <el-option :value="6" label="内部账号"></el-option>
                                <el-option :value="7" label="其他详见备注"></el-option>
                            </el-select>
                        </el-form-item>
						<el-form-item v-if="invalidType == 4 || invalidType == 7">
							<el-input placeholder="请输入备注" style="width: 188px;" type="textarea" v-model="invalidMark" :rows="5" resize="none"></el-input>
						</el-form-item>
                    </el-form>
				</div>
				<span slot="footer" class="dialog-footer" style="text-align: center">
					<el-button @click="isShowInvalidDialog = false">取 消</el-button>
					<el-button type="primary" @click="transToInvalid">确认转入</el-button>
				</span>
			</el-dialog>

			<!-- 当月库 -->
			<el-tabs v-cloak type="border-card" @tab-click="changeStatus">
				<el-tab-pane v-bind:label="this.tabNamePerson">
					<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
						<!--查询条件 start-->
						<div id="select" style="display: flex; flex-direction: row; flex-wrap: wrap;">
							<!--输入框条件 start-->
								<!-- <span>
									A/B库
									<el-select v-model="form.A_BLib.value">
									    <el-option label="全部" value="-1" ></el-option>
										<el-option label="A库" value="0" ></el-option>
							            <el-option label="B库" value="1"></el-option>
									</el-select>
								</span> -->
								<span v-if="isShowInPage('aid')">aid<el-input  class="short-input" v-model="form.aid" ></el-input></span>
								<!-- <span>省份<el-input class="short-input" v-model="form.province"  ></el-input></span>
								<span>城市<el-input  class="short-input" v-model="form.city" ></el-input></span>		 -->
								<span v-if="isShowInPage('mark')">备注<el-input  class="short-input" v-model="form.mark" ></el-input></span>
								<span v-if="isShowInPage('joinNumStart')">参与人数<el-input class="short-input" v-model="form.joinNumStart"   ></el-input>-<el-input class="short-input" v-model="form.joinNumEnd"  ></el-input></span>
								 
								<span v-if="isShowInPage('ta')">
									注册来源
									<el-select v-model="form.ta.value">
									    
										<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								<span v-if="isShowInPage('hasCorpName')">
									有企业名字
									<el-select v-model="form.hasCorpName.value">
										<el-option v-for="select in form.hasCorpName.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								
								<span v-if="isShowInPage('siteVersion')">
									网站版本
									<el-select v-model="form.siteVersion.value">
										<el-option v-for="select in form.siteVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span v-if="isShowInPage('hdVersion')">
									互动版本
									<el-select v-model="form.hdVersion.value">
										<el-option v-for="select in form.hdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('cdVersion')">
									微传单版本
									<el-select v-model="form.cdVersion.value">
										<el-option v-for="select in form.cdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								
								<!-- <span>
									注册用途
									<el-select v-model="form.goal.value">
										<el-option v-for="select in form.goal.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span> -->
								<span v-if="isShowInPage('hasOpenYk')">
									开通悦客
									<el-select v-model="form.hasOpenYk.value">
										<el-option v-for="select in form.hasOpenYk.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('wxVersion')">
									助手版本
									<el-select v-model="form.wxVersion.value">
										<el-option v-for="select in form.wxVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								<span v-if="isShowInPage('business')">
									业务
									<el-select v-model="form.business.value">
										<el-option v-for="select in form.business.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
							<span v-if="isShowInPage('regTimeCheck')">
								<span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.regTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							<span v-if="isShowInPage('needloginTime')">
								<span class="demonstration">登陆时间:  <el-checkbox v-model="form.loginTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.loginTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('firstCreateGameTimeCheck')">
								<span class="demonstration">首次创建游戏时间:  <el-checkbox v-model="form.firstCreateGameTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.firstCreateGameTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.firstCreateGameTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>	
								
						
							
							<span v-if="isShowInPage('coupon')">
									有现金券且未过期
									<el-select v-model="form.coupon.value">
										<el-option v-for="select in form.coupon.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<span v-if="isShowInPage('isWeekendResource')">
									是否周末分配的资源
									<el-select v-model="form.isWeekendResource.value">
										<el-option v-for="select in form.isWeekendResource.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							
							<span v-if="isShowInPage('allotType')">
									分配方式
									<el-select v-model="form.allotType.value">
										<el-option v-for="select in form.allotType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							
							
							<span v-if="isShowInPage('receiveTimeCheck')">
								<span class="demonstration">领取时间: <el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('lastUpdateTimeCheck')">
								<span class="demonstration">最后更新时间:  <el-checkbox v-model="form.lastUpdateTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.lastUpdateTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.lastUpdateTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>				
															
							<span v-if="isShowInPage('nextTalkTimeCheck')">
									<span class="demonstration">下次联系时间:  <el-checkbox v-model="form.nextTalkTime.check"></el-checkbox></span>
									<el-date-picker class="fai-daterange"  type="date" v-model="form.nextTalkTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
									<el-date-picker class="fai-daterange" type="date" v-model="form.nextTalkTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('sendMessageTimeCheck')">
								<span class="demonstration"><span class="demonstration">(未<el-checkbox @change="handleCheckedNoSendSmsChange" v-model="form.sendMessageTime.noSendCheck"></el-checkbox>)</span>发送短信时间:<el-checkbox v-model="form.sendMessageTime.check" @change="handleCheckedSendSmsChange"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.sendMessageTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.sendMessageTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							
							<!-- <span>   2019-10-17 奖励资源选项下线
								奖励资源
								<el-select v-model="form.isAward.value">
									<el-option v-for="select in form.isAward.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span> -->
							
								
							<!--日期条件 end-->
							<!--下拉框条件 start-->
								
								<span v-if="isShowInPage('saleGroup')">
									销售分组
									<el-select v-model="form.saleGroup.value">
										<el-option v-for="select in form.saleGroup.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('receiveSale')">
									领取人
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('tag')">
									标签
									<el-select v-model="form.tag.value">
										<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span v-if="isShowInPage('intent')">
									标记
									<el-select v-model="form.intent.value">
										<el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('intentLevel')">
									意向度
									<el-select v-model="form.intentLevel.value">
										<el-option v-for="select in form.intentLevel.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								<span v-if="isShowInPage('args')">
									未联系时间
									<el-select v-model="form.args.value">
										<el-option v-for="select in form.args.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								
								<span v-if="isShowInPage('sortBy')">
									排列顺序
									<el-select v-model="form.sortBy.value">
										<el-option v-for="select in form.sortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('oneWeekSortBy')">
									第二个排列顺序
									<el-select v-model="form.oneWeekSortBy.value">
										<el-option v-for="select in form.oneWeekSortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								
								<el-form-item >
									<el-button type="primary"  @click="getDataListByArgs" v-if="isShowInPage('search')">查询</el-button>
			
									<el-button type="primary" v-if="(isShowInPage('excel'))&&(this.auth.authAdm || this.auth.hdSaleManage||<%=WebHdOss.getSid()==877%>)"  @click="getDataListByArgs('excel')">导出</el-button>
									
								</el-form-item>
								<el-form-item >
									<el-button type="primary"  @click="batchSendMessag()" v-if="isShowInPage('batchMessange')">批量短信</el-button>
									<el-button type="primary" style="background-color:red; border: 1px solid red;" @click="confirmBatch('release')" v-if="isShowInPage('batchRelease')">批量释放</el-button>
									
								</el-form-item>
								
								<el-form-item >
								<el-button type="primary"  @click="batchTurnStatus('changeNowToA')" v-if="isShowInPage('batchToA')">批量转入A库</el-button>
								<el-button type="primary"  @click="batchTurnStatus('changeToB')" v-if="isShowInPage('batchToB')">批量转入B库</el-button>
								</el-form-item>
								
								<span style="color:red" v-if="isShowInPage('mark')">注：选择领取人才能查询备注</span>
								<el-form-item v-if="this.auth.authAdm">
									<el-button type="text" size="small" v-if="isShowInPage('allocationLogic')"><a target="_blank" href="http://it.faisco.cn/page/forum/articleDetail.jsp?articleId=2771" >资源分配逻辑</a></el-button>
									<el-button type="text" size="small" @click="logicImgVisible = true" v-if="isShowInPage('displayLogic')">展示逻辑图</el-button>
								</el-form-item>
							&nbsp;&nbsp;&nbsp;&nbsp;
								<el-form-item >
									<el-button type="text" size="small" @click="getOtherLibList('follow')"  v-if="isShowInPage('focusAndStopSend')"><a class="underlineClass" v-bind:style="textStyle_follow">重点关注</a></el-button>
									<el-button type="text" size="small" @click="getOtherLibList('banSendMsgAid')" v-if="isShowInPage('prohibitSendList')"><a class="underlineClass"  v-bind:style="textStyle_ban_msg" >禁止发送短信列表</a></el-button>
								</el-form-item>
								<el-form-item >
								<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd1')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan15Day')">批量禁用优惠(15天)</el-button>
								<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd2')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan')">批量禁用优惠(手动)</el-button>
								<el-button type="primary"  @click="batchCouponStopStatus('open')" style="background:" v-if="isShowInPage('batchNoBan')">批量解除禁用</el-button>
								<el-button class="el-icon-setting" @click="showCustomPageInfo()" ></el-button>
								</el-form-item>
								<br/>
								<el-dialog title="逻辑图" :visible.sync="logicImgVisible" style="padding-left:300px" :fullscreen=true >								  
								  <img v-bind:src="logicImgSrc"/>
								</el-dialog>
						</div>
						<!--查询条件 end-->
						<!--表格数据展示 start-->
						<br/>
						<div>
							<!-- <el-form-item >
								<el-button type="primary"  @click="batchSendMessag()">批量短信</el-button>
							</el-form-item> -->
							<el-table :data="dataList" style="width: 1000" max-height="620" stripe  @selection-change="handleSelectionChange">
								<el-table-column fixed="left" type="selection" width="55">全选</el-table-column>
								<el-table-column fixed="left" label="操作" width="150"  align="center">
								<template slot-scope="scope">
									<el-button v-if="auth.authAdm"  @click.native.prevent="releasePreSale(scope.$index, dataList)" type="text" size="small">释放</el-button>
									<el-button @click.native.prevent="sendMessag(scope.$index, dataList)" type="text" size="small">发送短信</el-button>
								</template>
								</el-table-column>
								<el-table-column  label="标记"   width="120" align="center">
									<template slot-scope="scope">
										<el-select v-model="scope.row.intent" style="float:center"  @change="changeIntent(scope.row.aid,scope.row.intent,scope.row.salesAcct,scope.row.mark)">
											<el-option  label="空" value="0"></el-option>
											<el-option  label="有意向" value="1"></el-option>
											<el-option  label="无意向" value="5"></el-option>
											<el-option  label="未接通" value="6"></el-option>
											<el-option  label="待跟进" value="7"></el-option>
											<el-option  label="仅建站" value="8"></el-option>
										</el-select>
										<!-- <el-button @click.native.prevent="deleteRow(scope.$index, dataList)" type="text" size="small" style="float:right">编辑</el-button> -->
									</template>
									</el-table-column>
								<el-table-column  prop="aid" label="aid" width="90"  align="center">
									<template slot-scope="scope">
										<%--<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>--%>
										<%--//TODO--%>
										<a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
									</template>
								</el-table-column>
								<el-table-column  prop="mark"  :show-Overflow-Tooltip=true label="备注" width="150"  align="center" class="mark"> </el-table-column>
								<el-table-column  prop="name" label="企业名称" width="100" :show-Overflow-Tooltip=true  align="center"></el-table-column>
								<el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column>
								<el-table-column  prop="loginTime" label="最后登陆时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center">
									<template slot-scope="scope">
										<el-button @click="showEditReceiveTime(scope.$index, dataList)" size="small" >{{scope.row.receiveTime}}</el-button>
									</template>
								</el-table-column>
								<el-table-column  prop="ta" label="注册来源" width="120"  align="center"></el-table-column>
								<el-table-column  prop="lastUpdateTime" label="最后更新时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="consult" label="咨询客服" width="120"  align="center"></el-table-column>
								<el-table-column  prop="tp" label="关键字"  :show-Overflow-Tooltip=true width="120"  align="center"></el-table-column>
								<el-table-column  prop="coupon" label="优惠禁用" width="160"  align="center"></el-table-column>
								<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
								<el-table-column  prop="firstCreateGameTime" label="首次创建游戏时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="sendTime" label="上次发送短信时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="loginOneWeekCount" label="一周登陆次数" width="130"  align="center"></el-table-column>
								<el-table-column  prop="optOneWeekCount" label="一周操作次数" width="130"  align="center"></el-table-column>
								<el-table-column  prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true   align="center"></el-table-column>
								<el-table-column  prop="isWeekendResource" label="是否周末分配的资源" width="160"  align="center"></el-table-column>
								<el-table-column  prop="salesAcct" label="销售" width="80"  align="center"></el-table-column>
							</el-table>
						</div>
						<!--表格数据展示 end-->
						<!--分页 end-->
						<div class="block">
							<el-pagination @size-change="handleSizeChange" @current-change="getDataListByArgs(libTab)" :current-page.sync="pageData.currentPage"
								:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
							</el-pagination>
						</div>
					</el-form>
					
				</el-tab-pane>

				<el-tab-pane v-bind:label="this.tabNameA">
					<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
						<!--查询条件 start-->
						<div id="select" style="display: flex; flex-direction: row; flex-wrap: wrap;">
							<!--输入框条件 start-->
							<span v-if="isShowInPage('aid')">aid<el-input  class="short-input" v-model="form.aid" ></el-input></span>
							<!-- <span>省份<el-input class="short-input" v-model="form.province"  ></el-input></span>
							<span>城市<el-input  class="short-input" v-model="form.city" ></el-input></span>		 -->
							<span v-if="isShowInPage('mark')">备注<el-input  class="short-input" v-model="form.mark" ></el-input></span>
							<span v-if="isShowInPage('joinNumStart')">参与人数<el-input class="short-input" v-model="form.joinNumStart"   ></el-input>-<el-input class="short-input" v-model="form.joinNumEnd"  ></el-input></span>
							<span v-if="isShowInPage('ta')">
								注册来源
								<el-select v-model="form.ta.value">
									
									<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('hasCorpName')">
								有企业名字
								<el-select v-model="form.hasCorpName.value">
									<el-option v-for="select in form.hasCorpName.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							
							
							<span  v-if="isShowInPage('siteVersion')">
								网站版本
								<el-select v-model="form.siteVersion.value">
									<el-option v-for="select in form.siteVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('hdVersion')">
								互动版本
								<el-select v-model="form.hdVersion.value">
									<el-option v-for="select in form.hdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							<span v-if="isShowInPage('cdVersion')">
								微传单版本
								<el-select v-model="form.cdVersion.value">
									<el-option v-for="select in form.cdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							

							<span v-if="isShowInPage('hasOpenYk')">
								开通悦客
								<el-select v-model="form.hasOpenYk.value">
									<el-option v-for="select in form.hasOpenYk.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							
							<span v-if="isShowInPage('wxVersion')">
									助手版本
									<el-select v-model="form.wxVersion.value">
										<el-option v-for="select in form.wxVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<br/>  
							<span v-if="isShowInPage('business')">
								业务
								<el-select v-model="form.business.value">
									<el-option v-for="select in form.business.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('regTimeCheck')">
								<span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.regTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							<span v-if="isShowInPage('needloginTime')">
								<span class="demonstration">登陆时间:  <el-checkbox v-model="form.loginTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.loginTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span  v-if="isShowInPage('firstCreateGameTimeCheck')">
								<span class="demonstration">首次创建游戏时间:  <el-checkbox v-model="form.firstCreateGameTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.firstCreateGameTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.firstCreateGameTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>	
								
						
							
							<span v-if="isShowInPage('coupon')">
									有现金券且未过期
									<el-select v-model="form.coupon.value">
										<el-option v-for="select in form.coupon.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<span v-if="isShowInPage('isWeekendResource')">
									是否周末分配的资源
									<el-select v-model="form.isWeekendResource.value">
										<el-option v-for="select in form.isWeekendResource.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<br/>
							<span v-if="isShowInPage('allotType')">
									分配方式
									<el-select v-model="form.allotType.value">
										<el-option v-for="select in form.allotType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							
							
							<span v-if="isShowInPage('receiveTimeCheck')">
								<span class="demonstration">领取时间: <el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('lastUpdateTimeCheck')">
								<span class="demonstration">最后更新时间:  <el-checkbox v-model="form.lastUpdateTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.lastUpdateTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.lastUpdateTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>				
															
							<span v-if="isShowInPage('nextTalkTimeCheck')">
									<span class="demonstration">下次联系时间:  <el-checkbox v-model="form.nextTalkTime.check"></el-checkbox></span>
									<el-date-picker class="fai-daterange"  type="date" v-model="form.nextTalkTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
									<el-date-picker class="fai-daterange" type="date" v-model="form.nextTalkTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('sendMessageTimeCheck')">
								<span class="demonstration"><span class="demonstration">(未<el-checkbox @change="handleCheckedNoSendSmsChange" v-model="form.sendMessageTime.noSendCheck"></el-checkbox>)</span>发送短信时间:<el-checkbox v-model="form.sendMessageTime.check" @change="handleCheckedSendSmsChange"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.sendMessageTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.sendMessageTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							
							<!-- <span>
								奖励资源
								<el-select v-model="form.isAward.value">
									<el-option v-for="select in form.isAward.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span> -->
							
								
							<!--日期条件 end-->
							<!--下拉框条件 start-->
								
								<span v-if="isShowInPage('saleGroup')">
									销售分组
									<el-select v-model="form.saleGroup.value">
										<el-option v-for="select in form.saleGroup.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('receiveSale')">
									领取人
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>
								<span  v-if="isShowInPage('tag')">
									标签
									<el-select v-model="form.tag.value">
										<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span v-if="isShowInPage('intent')">
									标记
									<el-select v-model="form.intent.value">
										<el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('intentLevel')">
									意向度
									<el-select v-model="form.intentLevel.value">
										<el-option v-for="select in form.intentLevel.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								<span v-if="isShowInPage('args')">
									未联系时间
									<el-select v-model="form.args.value">
										<el-option v-for="select in form.args.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								<span v-if="isShowInPage('sortBy')">
									排列顺序
									<el-select v-model="form.sortBy.value">
										<el-option v-for="select in form.sortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('oneWeekSortBy')">
									第二个排列顺序
									<el-select v-model="form.oneWeekSortBy.value">
										<el-option v-for="select in form.oneWeekSortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
							
							<el-form-item>
								<el-button type="primary"  @click="getDataListByArgs" v-if="isShowInPage('search')">查询</el-button>
								
								<el-button type="primary" v-if="(isShowInPage('excel'))&&(this.auth.authAdm || this.auth.hdSaleManage||<%=WebHdOss.getSid()==877%>)" @click="getDataListByArgs('excel')">导出</el-button>
							
							</el-form-item>
							<el-form-item >
								<el-button type="primary"  @click="batchSendMessag()" v-if="isShowInPage('batchMessange')">批量短信</el-button>
								<el-button type="primary" style="background-color:red; border: 1px solid red;" @click="confirmBatch('release')" v-if="isShowInPage('batchRelease')">批量释放</el-button>
								<el-button type="primary"  @click="batchTurnStatus('changeToB')" v-if="isShowInPage('batchToB')">批量转入B库</el-button>
								<!-- <el-button type="primary"  @click="batchTurnStatus('changeToCurMonth')">批量转入当月库</el-button> -->
							</el-form-item>
							<span style="color:red" v-if="isShowInPage('mark')">注：选择领取人才能查询备注</span>
							
							<el-form-item >
								<el-button type="text" size="small" @click="getOtherLibList('follow')" v-if="isShowInPage('focusAndStopSend')"><a class="underlineClass" v-bind:style="textStyle_follow" >重点关注</a></el-button>
								<el-button type="text" size="small" @click="getOtherLibList('banSendMsgAid')" v-if="isShowInPage('prohibitSendList')"><a class="underlineClass"  v-bind:style="textStyle_ban_msg" >禁止发送短信列表</a></el-button>
							</el-form-item>
							<el-form-item >
								<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd1')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan15Day')">批量禁用优惠(15天)</el-button>
								<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd2')" style="background:red;border-color:red;"  v-if="isShowInPage('batchBan')">批量禁用优惠(手动)</el-button>
								<el-button type="primary"  @click="batchCouponStopStatus('open')" style="background:" v-if="isShowInPage('batchNoBan')">批量解除禁用</el-button>
								<el-button class="el-icon-setting" @click="showCustomPageInfo()" ></el-button>
							</el-form-item>
							
						</div>
						<br/>
						<!--查询条件 end-->
						<!--表格数据展示 start-->
						<div>
							<!-- <el-form-item >
								<el-button type="primary"  @click="batchSendMessag()">批量短信</el-button>
							</el-form-item> -->
							<el-table :data="dataList" style="width: 1000" max-height="620" stripe  @selection-change="handleSelectionChange">
								<el-table-column type="selection" width="55">全选</el-table-column>
								<el-table-column fixed="left" label="操作" width="200"  align="center">
									<template slot-scope="scope">
										<el-button v-if="auth.authAdm"  @click.native.prevent="releasePreSale(scope.$index, dataList)" type="text" size="small">释放</el-button>
										<el-button @click.native.prevent="turnStatus(scope.$index, dataList,'invalid')" type="text" size="small">转入B库</el-button>
										<el-button @click.native.prevent="sendMessag(scope.$index, dataList)" type="text" size="small">发送短信</el-button>
										<!-- <el-button  @click.native.prevent="showBackToAkuPanel(scope.$index, dataList)" type="text" size="small">申领回A库</el-button> -->
									</template>
								</el-table-column>
								<el-table-column  label="标记"   width="120" align="center">
									<template slot-scope="scope">
										<el-select v-model="scope.row.intent" style="float:center"  @change="changeIntent(scope.row.aid,scope.row.intent,scope.row.salesAcct,scope.row.mark)">
											<el-option  label="空" value="0"></el-option>
											<el-option  label="有意向" value="1"></el-option>
											<el-option  label="无意向" value="5"></el-option>
											<el-option  label="未接通" value="6"></el-option>
											<el-option  label="待跟进" value="7"></el-option>
											<el-option  label="仅建站" value="8"></el-option>
										</el-select>
										<!-- <el-button @click.native.prevent="deleteRow(scope.$index, dataList)" type="text" size="small" style="float:right">编辑</el-button> -->
									</template>
								</el-table-column>
								<el-table-column  prop="aid" label="aid" width="90"  align="center">
									<template slot-scope="scope">
										<a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
									</template>
								</el-table-column>
								<el-table-column  prop="mark"  :show-Overflow-Tooltip=true label="备注" width="150"  align="center" class="mark"> </el-table-column>
								<el-table-column  prop="name" label="企业名称" width="100" :show-Overflow-Tooltip=true  align="center"></el-table-column>
								<el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column>
								<el-table-column  prop="loginTime" label="最后登陆时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
								<el-table-column  prop="ta" label="注册来源" width="120"  align="center"></el-table-column>
								<el-table-column  prop="consult" label="咨询客服" width="120"  align="center"></el-table-column>
								<el-table-column  prop="tp" label="关键字"  :show-Overflow-Tooltip=true width="120"  align="center"></el-table-column>
								<el-table-column  prop="coupon" label="优惠禁用" width="160"  align="center"></el-table-column>
								<el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center">
									<template slot-scope="scope">
										<el-button @click="showEditReceiveTime(scope.$index, dataList)" size="small" >{{scope.row.receiveTime}}</el-button>
									</template>
								</el-table-column>
								<el-table-column  prop="firstCreateGameTime" label="首次创建游戏时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="salesAcct" label="销售" width="80"  align="center"></el-table-column>
								<el-table-column  prop="sendTime" label="上次发送短信时间" width="160"  align="center"></el-table-column>
								<el-table-column  prop="loginOneWeekCount" label="一周登陆次数" width="130"  align="center"></el-table-column>
								<el-table-column  prop="optOneWeekCount" label="一周操作次数" width="130"  align="center"></el-table-column>
								<el-table-column  prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true   align="center"></el-table-column>
								<el-table-column  prop="lastUpdateTime" label="最后更新时间" width="160"  align="center"></el-table-column>
								
							</el-table>
						</div>
						<!--表格数据展示 end-->
						<!--分页 end-->
						<div class="block">
							<el-pagination @size-change="handleSizeChange" @current-change="getDataListByArgs" :current-page.sync="pageData.currentPage"
										   :page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
							</el-pagination>
						</div>
					</el-form>
				</el-tab-pane>

				<el-tab-pane v-bind:label="this.tabNameB">
						<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
								<!--查询条件 start-->
								<div id="select" style="display: flex; flex-direction: row; flex-wrap: wrap;">
									<!--输入框条件 start-->
										<!--输入框条件 start-->
							<span  v-if="isShowInPage('aid')">aid<el-input  class="short-input" v-model="form.aid" ></el-input></span>
							<!-- <span>省份<el-input class="short-input" v-model="form.province"  ></el-input></span>
							<span>城市<el-input  class="short-input" v-model="form.city" ></el-input></span>	 -->	
							<span v-if="isShowInPage('mark')">备注<el-input  class="short-input" v-model="form.mark" ></el-input></span>
							<span v-if="isShowInPage('joinNumStart')">参与人数<el-input class="short-input" v-model="form.joinNumStart"   ></el-input>-<el-input class="short-input" v-model="form.joinNumEnd"  ></el-input></span>
							 
							<span v-if="isShowInPage('ta')">
								注册来源
								<el-select v-model="form.ta.value">
									
									<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							
							<span v-if="isShowInPage('hasCorpName')">
								有企业名字
								<el-select v-model="form.hasCorpName.value">
									<el-option v-for="select in form.hasCorpName.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							
							
							<span v-if="isShowInPage('siteVersion')">
								网站版本
								<el-select v-model="form.siteVersion.value">
									<el-option v-for="select in form.siteVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('hdVersion')">
								互动版本
								<el-select v-model="form.hdVersion.value">
									<el-option v-for="select in form.hdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							<span v-if="isShowInPage('cdVersion')">
								微传单版本
								<el-select v-model="form.cdVersion.value">
									<el-option v-for="select in form.cdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('hasOpenYk')">
								开通悦客
								<el-select v-model="form.hasOpenYk.value">
									<el-option v-for="select in form.hasOpenYk.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>
							
							<span v-if="isShowInPage('wxVersion')">
									助手版本
									<el-select v-model="form.wxVersion.value">
										<el-option v-for="select in form.wxVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
							<br/>  
							<span v-if="isShowInPage('business')">
								业务
								<el-select v-model="form.business.value">
									<el-option v-for="select in form.business.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span>

							<span v-if="isShowInPage('regTimeCheck')">
								<span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.regTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							<span v-if="isShowInPage('needloginTime')">
								<span class="demonstration">登陆时间:  <el-checkbox v-model="form.loginTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.loginTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('firstCreateGameTimeCheck')">
								<span class="demonstration">首次创建游戏时间:  <el-checkbox v-model="form.firstCreateGameTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.firstCreateGameTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.firstCreateGameTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>	
								
						
							
							<span v-if="isShowInPage('coupon')">
									有现金券且未过期
									<el-select v-model="form.coupon.value">
										<el-option v-for="select in form.coupon.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<span v-if="isShowInPage('isWeekendResource')">
									是否周末分配的资源
									<el-select v-model="form.isWeekendResource.value">
										<el-option v-for="select in form.isWeekendResource.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<br/>
							<span v-if="isShowInPage('allotType')">
									分配方式
									<el-select v-model="form.allotType.value">
										<el-option v-for="select in form.allotType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							
							
							<span v-if="isShowInPage('receiveTimeCheck')">
								<span class="demonstration">领取时间: <el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('lastUpdateTimeCheck')">
								<span class="demonstration">最后更新时间:  <el-checkbox v-model="form.lastUpdateTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.lastUpdateTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.lastUpdateTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>				
															
							<span v-if="isShowInPage('nextTalkTimeCheck')">
									<span class="demonstration">下次联系时间:  <el-checkbox v-model="form.nextTalkTime.check"></el-checkbox></span>
									<el-date-picker class="fai-daterange"  type="date" v-model="form.nextTalkTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
									<el-date-picker class="fai-daterange" type="date" v-model="form.nextTalkTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							<span v-if="isShowInPage('sendMessageTimeCheck')">
								<span class="demonstration"><span class="demonstration">(未<el-checkbox @change="handleCheckedNoSendSmsChange" v-model="form.sendMessageTime.noSendCheck"></el-checkbox>)</span>发送短信时间:<el-checkbox v-model="form.sendMessageTime.check" @change="handleCheckedSendSmsChange"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.sendMessageTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.sendMessageTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							
							
							<!-- <span>
								奖励资源
								<el-select v-model="form.isAward.value">
									<el-option v-for="select in form.isAward.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span> -->
							

								
							<!--日期条件 end-->
							<!--下拉框条件 start-->
								
								<span v-if="isShowInPage('saleGroup')">
									销售分组
									<el-select v-model="form.saleGroup.value">
										<el-option v-for="select in form.saleGroup.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('receiveSale')">
									领取人
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('tag')">
									标签
									<el-select v-model="form.tag.value">
										<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span  v-if="isShowInPage('intent')">
									标记
									<el-select v-model="form.intent.value">
										<el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span  v-if="isShowInPage('intentLevel')">
									意向度
									<el-select v-model="form.intentLevel.value">
										<el-option v-for="select in form.intentLevel.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								
								
								<span v-if="isShowInPage('sortBy')">
									排列顺序
									<el-select v-model="form.sortBy.value">
										<el-option v-for="select in form.sortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('oneWeekSortBy')">
									第二个排列顺序
									<el-select v-model="form.oneWeekSortBy.value">
										<el-option v-for="select in form.oneWeekSortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
										<%--TODO--%>
										<%-- <span>
											A/B库
											<el-select v-model="form.A_BLib.value">
												<el-option label="A库" value="0" ></el-option>
							                    <el-option label="B库" value="1"></el-option>
											</el-select>
										</span> --%>
										
										<el-form-item>
											<el-button type="primary"  @click="getDataListByArgs" v-if="isShowInPage('search')">查询</el-button>
										</el-form-item>
										<el-form-item >
											<el-button type="primary"  @click="batchSendMessag()" v-if="isShowInPage('batchMessange')">批量短信</el-button>
											<el-button type="primary" style="background-color:red; border: 1px solid red;" @click="confirmBatch('release')" v-if="isShowInPage('batchRelease')">批量释放</el-button>
								            <el-button type="primary"  @click="batchTurnStatus('changeToA')" v-if="isShowInPage('batchToA')">批量转入A库</el-button>
										</el-form-item>
										<el-form-item v-if="this.auth.authAdm || this.auth.hdSaleManage">
											<el-button type="primary"  @click="getDataListByArgs('excel')" v-if="isShowInPage('excel')">导出</el-button>
											<el-button type="text" size="small" v-if="isShowInPage('allocationLogic')"><a target="_blank" href="http://it.faisco.cn/page/forum/articleDetail.jsp?articleId=2771" >资源分配逻辑</a></el-button>
										</el-form-item>
										<el-form-item >
											<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd1')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan15Day')">批量禁用优惠(15天)</el-button>
											<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd2')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan')">批量禁用优惠(手动)</el-button>
											<el-button type="primary"  @click="batchCouponStopStatus('open')" style="background:"  v-if="isShowInPage('batchNoBan')">批量解除禁用</el-button>
											<el-button class="el-icon-setting" @click="showCustomPageInfo()" ></el-button>
										</el-form-item>
										<span style="color:red" v-if="isShowInPage('mark')">注：选择领取人才能查询备注</span>
								</div>
								<br/>
								<!--查询条件 end-->
								<!--表格数据展示 start-->
								<%--B库--%>
								<div>
									<el-table :data="dataList" style="width: 100%" max-height="630" stripe @selection-change="handleSelectionChange">

										<el-table-column type="selection" width="55">全选</el-table-column>
										<el-table-column fixed="left" label="操作" width="200"  align="center"> 
										<template slot-scope="scope">
											<el-button v-if="auth.authAdm"  @click.native.prevent="releasePreSale(scope.$index, dataList)" type="text" size="small" >释放</el-button>
											<el-button @click.native.prevent="turnStatus(scope.$index, dataList,'historian')" type="text" size="small">转入A库</el-button>
											<el-button @click.native.prevent="sendMessag(scope.$index, dataList)" type="text" size="small">发送短信</el-button>
										</template>
										</el-table-column>
										<el-table-column  prop="aid" label="aid" width="90"  align="center">
									        <template slot-scope="scope">
										      <%--<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>--%>
										      <%--//TODO--%>
										      <a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
									        </template>
								        </el-table-column>
								        <el-table-column  prop="mark" label="备注" width="150" :show-Overflow-Tooltip=true  align="center" ></el-table-column>
										<el-table-column  prop="name" label="企业名称" :show-Overflow-Tooltip=true width="100"  align="center"></el-table-column>
										<el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column>
										<el-table-column  prop="loginTime" label="最后登陆时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
										<el-table-column  prop="ta" label="注册来源" width="120"  align="center"></el-table-column>
										<el-table-column  prop="consult" label="咨询客服" width="120"  align="center"></el-table-column>
										<el-table-column  prop="tp" label="关键字" :show-Overflow-Tooltip=true width="120"  align="center"></el-table-column>
										<el-table-column  prop="coupon" label="优惠禁用" width="160"  align="center"></el-table-column>
										<el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="firstCreateGameTime" label="首次创建游戏时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="salesAcct" label="销售" width="80"  align="center"></el-table-column>
										<el-table-column  prop="loginOneWeekCount" label="一周登陆次数" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="一周操作次数" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="网站用途" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="网站版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="互动版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="微传单版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true align="center"></el-table-column>
									</el-table>
								</div>
								<!--表格数据展示 end-->
								<!--分页 end-->
								<div class="block">
										<el-pagination @size-change="handleSizeChange" @current-change="getDataListByArgs" :current-page.sync="pageData.currentPage" 
											:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
										</el-pagination>
								</div>
							</el-form>
				</el-tab-pane>


				<el-tab-pane v-bind:label="this.tabNameDeal">
						<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
								<!--查询条件 start-->
								<div id="select" style="display: flex; flex-direction: row; flex-wrap: wrap;">
								<!-- <span>
									A/B库
									<el-select v-model="form.A_BLib.value">
									    <el-option label="全部" value="-1" ></el-option>
										<el-option label="A库" value="0" ></el-option>
							            <el-option label="B库" value="1"></el-option>
									</el-select>
								</span> -->
								<span v-if="isShowInPage('aid')">aid<el-input  class="short-input" v-model="form.aid" ></el-input></span>
								<!-- <span>省份<el-input class="short-input" v-model="form.province"  ></el-input></span>
								<span>城市<el-input  class="short-input" v-model="form.city" ></el-input></span>		 -->
								<span v-if="isShowInPage('mark')">备注<el-input  class="short-input" v-model="form.mark" ></el-input></span>
								<span v-if="isShowInPage('joinNumStart')">参与人数<el-input class="short-input" v-model="form.joinNumStart"   ></el-input>-<el-input class="short-input" v-model="form.joinNumEnd"  ></el-input></span>

								<span v-if="isShowInPage('ta')">
									注册来源
									<el-select v-model="form.ta.value">
									    
										<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span v-if="isShowInPage('hasCorpName')">
									有企业名字
									<el-select v-model="form.hasCorpName.value">
										<el-option v-for="select in form.hasCorpName.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>


								<span v-if="isShowInPage('siteVersion')">
									网站版本
									<el-select v-model="form.siteVersion.value">
										<el-option v-for="select in form.siteVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<!-- <span>
									注册产品
									<el-select v-model="form.regBiz.value">
										<el-option v-for="select in form.regBiz.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span> -->
								<span v-if="isShowInPage('hdVersion')">
									互动版本
									<el-select v-model="form.hdVersion.value">
										<el-option v-for="select in form.hdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('cdVersion')">
									微传单版本
									<el-select v-model="form.cdVersion.value">
										<el-option v-for="select in form.cdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

								<span v-if="isShowInPage('hasOpenYk')">
									开通悦客
									<el-select v-model="form.hasOpenYk.value">
										<el-option v-for="select in form.hasOpenYk.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('wxVersion')">
									助手版本
									<el-select v-model="form.wxVersion.value">
										<el-option v-for="select in form.wxVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<br/>
								<span v-if="isShowInPage('business')">
									业务
									<el-select v-model="form.business.value">
										<el-option v-for="select in form.business.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
							<span v-if="isShowInPage('regTimeCheck')">
								<span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.regTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>
							<span v-if="isShowInPage('needloginTime')">
								<span class="demonstration">登陆时间:  <el-checkbox v-model="form.loginTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.loginTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>

							<span v-if="isShowInPage('firstCreateGameTimeCheck')">
								<span class="demonstration">首次创建游戏时间:  <el-checkbox v-model="form.firstCreateGameTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.firstCreateGameTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.firstCreateGameTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>



							<span v-if="isShowInPage('coupon')">
									有现金券且未过期
									<el-select v-model="form.coupon.value">
										<el-option v-for="select in form.coupon.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>
							<span v-if="isShowInPage('isWeekendResource')"> 
									是否周末分配的资源
									<el-select v-model="form.isWeekendResource.value">
										<el-option v-for="select in form.isWeekendResource.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>

							<span v-if="isShowInPage('allotType')">
									分配方式
									<el-select v-model="form.allotType.value">
										<el-option v-for="select in form.allotType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
							</span>


							<span v-if="isShowInPage('receiveTimeCheck')">
								<span class="demonstration">领取时间: <el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>

							<span v-if="isShowInPage('lastUpdateTimeCheck')">
								<span class="demonstration">最后更新时间:  <el-checkbox v-model="form.lastUpdateTime.check"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.lastUpdateTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.lastUpdateTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>

							<span v-if="isShowInPage('nextTalkTimeCheck')">
									<span class="demonstration">下次联系时间:  <el-checkbox v-model="form.nextTalkTime.check"></el-checkbox></span>
									<el-date-picker class="fai-daterange"  type="date" v-model="form.nextTalkTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
									<el-date-picker class="fai-daterange" type="date" v-model="form.nextTalkTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>

							<span v-if="isShowInPage('sendMessageTimeCheck')">
								<span class="demonstration"><span class="demonstration">(未<el-checkbox @change="handleCheckedNoSendSmsChange" v-model="form.sendMessageTime.noSendCheck"></el-checkbox>)</span>发送短信时间:<el-checkbox v-model="form.sendMessageTime.check" @change="handleCheckedSendSmsChange"></el-checkbox></span>
								<el-date-picker class="fai-daterange"  type="date" v-model="form.sendMessageTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="form.sendMessageTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							</span>


							<!-- <span>
								奖励资源
								<el-select v-model="form.isAward.value">
									<el-option v-for="select in form.isAward.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								</el-select>
							</span> -->


							<!--日期条件 end-->
							<!--下拉框条件 start-->

								<span v-if="isShowInPage('saleGroup')">
									销售分组
									<el-select v-model="form.saleGroup.value">
										<el-option v-for="select in form.saleGroup.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('receiveSale')">
									领取人
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('tag')">
									标签
									<el-select v-model="form.tag.value">
										<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<!-- <span>
									时间段
									<el-select v-model="form.timeRange.value">
										<el-option v-for="select in form.timeRange.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span> -->

								<span v-if="isShowInPage('intent')">
									标记
									<el-select v-model="form.intent.value">
										<el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('intentLevel')">
									意向度
									<el-select v-model="form.intentLevel.value">
										<el-option v-for="select in form.intentLevel.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>


								<span v-if="isShowInPage('sortBy')">
									排列顺序
									<el-select v-model="form.sortBy.value">
										<el-option v-for="select in form.sortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span v-if="isShowInPage('oneWeekSortBy')">
									第二个排列顺序
									<el-select v-model="form.oneWeekSortBy.value">
										<el-option v-for="select in form.oneWeekSortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>

										<el-form-item>
											<el-button type="primary"  @click="getDataListByArgs" v-if="isShowInPage('search')">查询</el-button>
										</el-form-item>
										<el-form-item >
											<el-button type="primary"  @click="batchSendMessag()" v-if="isShowInPage('batchMessange')">批量短信</el-button>
										</el-form-item>
										<el-form-item v-if="this.auth.authAdm || this.auth.hdSaleManage">
											<el-button type="primary"  @click="getDataListByArgs('excel')" v-if="isShowInPage('excel')">导出</el-button>
											<el-button type="text" size="small" v-if="isShowInPage('allocationLogic')"><a target="_blank" href="http://it.faisco.cn/page/forum/articleDetail.jsp?articleId=2771">资源分配逻辑</a></el-button>
										</el-form-item>
										&nbsp;&nbsp;&nbsp;&nbsp;
										<el-form-item>
											<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd1')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan15Day')">批量禁用优惠(15天)</el-button>
											<el-button type="primary"  @click="batchCouponStopStatus('stop','mtd2')" style="background:red;border-color:red;" v-if="isShowInPage('batchBan')">批量禁用优惠(手动)</el-button>
											<el-button type="primary"  @click="batchCouponStopStatus('open')" style="background:" v-if="isShowInPage('batchNoBan')">批量解除禁用</el-button>
											<el-button class="el-icon-setting" @click="showCustomPageInfo()" ></el-button>
										</el-form-item>
										<span style="color:red" v-if="isShowInPage('mark')">注：选择领取人才可以查询备注</span>

								</div>
								<!--查询条件 end-->
								<!--表格数据展示 start-->
								<%--成交库--%>
								<div>
									<el-table :data="dataList" style="width: 100%" max-height="630" stripe @selection-change="handleSelectionChange">

										<el-table-column type="selection" width="55">全选</el-table-column>
										<el-table-column fixed="left" label="操作" width="150"  align="center">
										<template slot-scope="scope">
											<el-button v-if="auth.authAdm"  @click.native.prevent="releasePreSale(scope.$index, dataList)" type="text" size="small" >释放</el-button>
											<el-button @click.native.prevent="sendMessag(scope.$index, dataList)" type="text" size="small">发送短信</el-button>
										</template>
										</el-table-column>
										<el-table-column  prop="aid" label="aid" width="90"  align="center">
											<template slot-scope="scope">
												<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>
											</template>
										</el-table-column>
										<el-table-column  prop="name" label="企业名称" :show-Overflow-Tooltip=true width="100"  align="center"></el-table-column>
										<el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column>
										<el-table-column  prop="loginTime" label="最后登陆时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
										<el-table-column  prop="ta" label="注册来源" width="120"  align="center"></el-table-column>
										<el-table-column  prop="tp" label="关键字" :show-Overflow-Tooltip=true width="120"  align="center"></el-table-column>
										<el-table-column  prop="coupon" label="优惠禁用" width="160"  align="center"></el-table-column>
										<el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="firstCreateGameTime" label="首次创建游戏时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="salesAcct" label="销售" width="80"  align="center"></el-table-column>
										<el-table-column  prop="loginOneWeekCount" label="一周登陆次数" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="一周操作次数" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="网站用途" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="网站版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="互动版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="微传单版本" width="130"  align="center"></el-table-column>
										<el-table-column  prop="mark" label="备注" width="150" :show-Overflow-Tooltip=true  align="center" ></el-table-column>
										<el-table-column  prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true align="center"></el-table-column>
									</el-table>
								</div>
								<!--表格数据展示 end-->
								<!--分页 end-->
								<div class="block">
										<el-pagination @size-change="handleSizeChange" @current-change="getDataListByArgs" :current-page.sync="pageData.currentPage"
											:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
										</el-pagination>
								</div>
							</el-form>
				</el-tab-pane>
				
				

				<el-tab-pane label="短信发送记录">
							<el-form>
								<span class="demonstration">添加时间:</span>
								<el-date-picker class="fai-daterange"  type="date" v-model="message.addTimeStart" placeholder="选择日期" :editable=false></el-date-picker>-
								<el-date-picker class="fai-daterange" type="date" v-model="message.addTimeEnd" placeholder="选择日期" :editable=false></el-date-picker>
								

								<span class="demonstration">发送时间:  <el-checkbox v-model="message.sendTime.check"></el-checkbox></span>
									<el-date-picker class="fai-daterange"  type="date" v-model="message.sendTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
									<el-date-picker class="fai-daterange" type="date" v-model="message.sendTime.end" placeholder="选择日期" :editable=false></el-date-picker>
								</span>
								
								<span>
									发送人
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>

								<el-button type="primary" size="small"  @click="getSendMessageList">查询</el-button>
							</el-form>
							<div>
								<el-table :data="message.dataList" style="width: 100%" max-height="600" stripe>
									<el-table-column  prop="saleName" label="销售" width="120"  align="center"></el-table-column>
									<el-table-column  prop="busType" label="业务类型" width="80"  align="center"></el-table-column>
									<el-table-column  prop="aid" label="客户aid" width="120"  align="center"></el-table-column>
									<el-table-column  prop="phone" label="电话号码" width="160" align="center"></el-table-column>
									<el-table-column  prop="sendStatus" label="发送状态" width="120"  align="center"></el-table-column>
									<el-table-column  prop="createTime" label="发送时间" width="220"  align="center"></el-table-column>
									<el-table-column  prop="id" label="短信NO." width="160"  align="center"></el-table-column>
									<el-table-column  prop="smsContent" label="短信模板" width="460"  :show-Overflow-Tooltip=true  align="center"></el-table-column>
								</el-table>
							</div>
							<div class="block">
									<el-pagination @size-change="handleSizeChange" @current-change="getSendMessageList" :current-page.sync="message.page" 
										:page-size="10" layout="total,prev, pager, next, jumper" :total="message.total">
									</el-pagination>
							</div>
				</el-tab-pane>
				
				<el-tab-pane label="短信信息编辑"  >						
					<el-form>
						<span>客户经理：  <el-input v-model="personInfo.nickName"  placeholder="请输入内容" disabled ></el-input></span><br><br>
						<span>联系电话：  <el-input v-model="personInfo.phone"   placeholder="请输入内容" clearable ></el-input></span>
						<el-button size="small" type="primary" v-on:click="setPreSaleTel" >保存</el-button>
					</el-form>		 
					<div>
						<el-table :data="personInfo.modelInfoList" style="width: 100%" max-height="900" stripe>
							<el-table-column  prop="modelType" label="短信类型" width="80"  align="center"></el-table-column>
							<el-table-column  prop="modelName" label="短信模板" width="80"  align="center"></el-table-column>
							<el-table-column  prop="modelText" label="短信内容" width="900"  align="center"></el-table-column>
							<el-table-column  prop="check" label="审核状态" width="150" align="center"></el-table-column>
						</el-table>
					</div>
											
				</el-tab-pane>
				
				
				<!-- 显示未发企微的信息  writting zhs -->
				<el-tab-pane label="企微发送提醒队列" >
						<div id="select">
							<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
								<span>提醒时间：<el-date-picker class="fai-daterange"  type="date" v-model="QWRemindInfo.remindStartTime" placeholder="选择日期" :editable=false></el-date-picker>-</span>
								<el-date-picker class="fai-daterange" type="date" v-model="QWRemindInfo.remindEndTime" placeholder="选择日期" :editable=false></el-date-picker>
								<span>
									是否已提醒:
									<el-select v-model="form.isSend.value">
										<el-option v-for="select in form.isSend.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
									</el-select>
								</span>
								<span>
									销售：
									<el-select v-model="form.receiveSale.value">
										<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
									</el-select>
								</span>
								<el-form-item>
									<el-button type="primary"  @click="getRemindList(false)">查询</el-button>
									<el-button type="primary" v-if="this.auth.authAdm || this.auth.hdSaleManage||<%=WebHdOss.getSid()==877%>"  @click="getRemindList(true)">导出</el-button>		
								</el-form-item>
							</el-form> 
						</div>
					    <div>
							<el-table :data="QWRemindInfo.remindList" style="width: 100%" max-height="600" stripe>
								<el-table-column type="index"  label="序号" width="200px"  align="center">
							   	 	<template scope="scope">{{scope.$index+1+(QWRemindInfo.page-1)*10}} </template>
							   	 </el-table-column>
								<el-table-column  prop="aid" label="aid" width="200px"  align="center">
									<template slot-scope="scope">
										<a @click="showAidMsgDialog(scope.$index, QWRemindInfo.remindList)">{{scope.row.aid}}</a>
									</template>
								</el-table-column>
								<el-table-column prop="nickName" label="销售" width="200px" align="center"></el-table-column>
								<el-table-column  prop="QWRemindTime" label="提醒时间" width="400px"  align="center"></el-table-column>
								<el-table-column  prop="QWRemindContent" label="提醒内容" width="900px"  align="center"></el-table-column>
								<el-table-column fixed="right" label="操作" width="200px"  align="center">
								<template slot-scope="scope">
										<el-button @click.native.prevent="deleteRemind(scope.$index,QWRemindInfo.remindList)" type="text" size="small" :disabled="new Date(QWRemindInfo.remindList[scope.$index].QWRemindTime)<new Date()">刪除</el-button>
								</template>
								</el-table-column>
							</el-table>
						</div>
						<div class="block">
							<el-pagination @size-change="handleSizeChange" @current-change="getRemindList('false')" :current-page.sync="QWRemindInfo.page" 
									:page-size="10" layout="total,prev, pager, next, jumper" :total="QWRemindInfo.total">
							</el-pagination>
						</div> 
					
				</el-tab-pane>
				
                <el-tab-pane v-bind:label="this.tabNameGh">
						<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
								<!--查询条件 start-->
								<div id="select" style="height:100px">
									<!--输入框条件 start-->
										<span>aid<el-input class="short-input" style="width:120px;" v-model="form.aid" placeholder="请输入内容" ></el-input></span>
										<span>
								           <span class="demonstration">首次创建游戏时间:  <el-checkbox v-model="form.firstCreateGameTime.check"></el-checkbox></span>
								           <el-date-picker class="fai-daterange"  type="date" v-model="form.firstCreateGameTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
								           <el-date-picker class="fai-daterange" type="date" v-model="form.firstCreateGameTime.end" placeholder="选择日期" :editable=false></el-date-picker>
							            </span>	
										<span>参与人数<el-input class="short-input" v-model="form.joinNumStart"  placeholder="请输入内容" ></el-input>-<el-input class="short-input"  v-model="form.joinNumEnd"  placeholder="请输入内容"  ></el-input></span>
										<span>
											注册来源
											<el-select v-model="form.ta.value">
												<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
											</el-select>
										</span>
										
										<span>
											<span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
											<el-date-picker class="fai-daterange"  type="date" v-model="form.regTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
											<el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期" :editable=false></el-date-picker>
										</span>
										<span>
											网站版本
											<el-select v-model="form.siteVersion.value">
												<el-option v-for="select in form.siteVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
											</el-select>
										</span>
										<!-- <span>
											互动版本
											<el-select v-model="form.hdVersion.value">
												<el-option v-for="select in form.hdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
											</el-select>
										</span> -->
										<span>
											微传单版本
											<el-select v-model="form.cdVersion.value">
												<el-option v-for="select in form.cdVersion.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
											</el-select>
										</span>
										<span>
								                                   有企业名字
								             <el-select v-model="form.hasCorpName.value">
									            <el-option v-for="select in form.hasCorpName.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
								             </el-select>
							            </span>
										<span>
											排列顺序
											<el-select v-model="form.sortBy.value">
												<el-option label="最后登录时间倒序" value="1"></el-option>
												<el-option label="最后登录时间顺序" value="2"></el-option>
												<el-option label="最后注册时间倒序" value="3"></el-option>
												<el-option label="最后注册时间顺序" value="4"></el-option>
												<el-option label="一周登录次数倒序" value="5"></el-option>
												<el-option label="一周登录次数顺序" value="6"></el-option>
												<el-option label="一周操作次数倒序" value="7"></el-option>
												<el-option label="一周操作次数顺序" value="8"></el-option>
											</el-select>
										</span>
										<span>
											第二个排列顺序
											<el-select v-model="form.oneWeekSortBy.value">
												<el-option v-for="select in form.oneWeekSortBy.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
											</el-select>
										</span>
										<el-form-item>
											<el-button type="primary"  @click="getDataListGh()">查询</el-button>
										</el-form-item>
										<el-form-item >
											<el-button type="primary"  @click="batchReceivePreSale()">批量领取</el-button>
										</el-form-item>
								</div>
								<!--查询条件 end-->
								<!--表格数据展示 start -->
								
								<div>
									<el-table :data="dataList" style="width: 1000 ;" height="670" stripe @selection-change="handleSelectionChange">

										<el-table-column type="selection" width="55">全选</el-table-column>
										<!-- <el-table-column fixed="left" label="操作" width="150"  align="center"> 
										<template slot-scope="scope">
											<el-button v-if="auth.authAdm"  @click.native.prevent="receivePreSale(scope.$index, dataList)" type="text" size="small" >领取</el-button>
											<el-button @click.native.prevent="sendMessag(scope.$index, dataList)" type="text" size="small">发送短信</el-button>
										</template>
										</el-table-column> -->
										<el-table-column  prop="aid" label="aid" width="90"  align="center">
											<template slot-scope="scope">
												<a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
											</template>
										</el-table-column>
										<el-table-column  prop="name" label="企业名称" :show-Overflow-Tooltip=true width="100"  align="center"></el-table-column>
										<!-- <el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column> -->
										<el-table-column  prop="loginTime" label="最后登陆时间" width="160"  align="center"></el-table-column>
										<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
										<el-table-column  prop="mark" label="备注" width="150" :show-Overflow-Tooltip=true  align="center" ></el-table-column>
										<el-table-column  prop="ta" label="注册来源" width="120"  align="center"></el-table-column>
										<el-table-column  prop="tp" label="关键字" :show-Overflow-Tooltip=true width="120"  align="center"></el-table-column>
										<!-- <el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center"></el-table-column> -->
										<el-table-column  prop="firstCreateGameTime" label="首次创建游戏时间" width="160"  align="center"></el-table-column>
										<!-- <el-table-column  prop="salesAcct" label="销售" width="80"  align="center"></el-table-column> -->
										
										<el-table-column  prop="loginOneWeekCount" label="一周登陆次数" width="130"  align="center"></el-table-column>
										<el-table-column  prop="optOneWeekCount" label="一周操作次数" width="130"  align="center"></el-table-column>
										
										<!-- <el-table-column  prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true align="center"></el-table-column> -->
									</el-table>
								</div>
								<!--表格数据展示 end-->
								<!--分页 end-->
								<div class="block">
										<el-pagination @size-change="handleSizeChange" @current-change="getDataListGh()" :current-page.sync="pageGhData.currentPage" 
											:page-size="pageGhData.size" layout="total,prev, pager, next, jumper" :total="pageGhData.total">
										</el-pagination>
								</div>
							</el-form>
				</el-tab-pane>


				<el-tab-pane v-bind:label="this.tabNameInvalid">
					<el-form inline :model="invalidForm" size="mini">
						<el-form-item label="aid"><el-input v-model="invalidForm.aid"></el-input></el-form-item>
						<el-form-item label="注册来源">
							<el-select v-model="invalidForm.ta">
								<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item label="转入销售">
							<el-select v-model="invalidForm.salesAcct">
								<el-option value="" label="全部"></el-option>
								<el-option v-for="select in form.receiveSale.labelList" :label="select.nickName" :value="select.acct" :key="select.acct"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="是否已付款">
							<el-select v-model="invalidForm.isPay">
								<el-option :value="0" label="全部"></el-option>
								<el-option :value="1" label="有付款"></el-option>
								<el-option :value="2" label="无付款"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="入库类型">
							<el-select v-model="invalidForm.invalidType">
								<el-option :value="0" label="全部"></el-option>
								<el-option :value="1" label="学生"></el-option>
								<el-option :value="2" label="U+资源"></el-option>
								<el-option :value="3" label="代理商"></el-option>
								<el-option :value="4" label="已换账号购买"></el-option>
								<el-option :value="5" label="态度恶劣"></el-option>
								<el-option :value="6" label="内部账号"></el-option>
								<el-option :value="7" label="其他详见备注"></el-option>
								<el-option :value="8" label="组织机构-个人"></el-option>
								<el-option :value="9" label="组织机构-学生"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="付款日期">
							<el-checkbox v-model="invalidForm.payTimeSelect"></el-checkbox>
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.payTimeBeg" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>-
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.payTimeEnd" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>
						</el-form-item>
						<el-form-item label="入库日期">
							<el-checkbox v-model="invalidForm.createTimeSelect"></el-checkbox>
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.createTimeBeg" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>-
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.createTimeEnd" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>
						</el-form-item>
						<el-form-item label="最近登录日期">
							<el-checkbox v-model="invalidForm.loginTimeSelect"></el-checkbox>
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.loginTimeBeg" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>-
							<el-date-picker type="date" value-format="yyyy-MM-dd" v-model="invalidForm.loginTimeEnd" placeholder="选择日期" :editable=false style="width: 130px"></el-date-picker>
						</el-form-item>
						<el-form-item>
							<el-button type="primary" @click="getInvalidDataList(false)">查询</el-button>
							<el-button type="primary" v-if="this.auth.authAdm || this.auth.hdSaleManage" @click="getInvalidDataList(true)">导出</el-button>
							<el-button type="danger" v-if="this.auth.authAdm || this.auth.hdSaleManage" @click="releaseInvalid">批量转出</el-button>
						</el-form-item>
					</el-form>

					<el-table
							:data="dataList"
							:header-row-style="{'font-size': '14px'}"
							:header-cell-style="{'text-align':'center'}"
							:row-style="{'font-size':'12px'}"
							:cell-style="{'padding':'5px 0','text-align':'center'}"
							tooltip-effect="light"
							stripe
							border
							size="small"
							@selection-change="handleSelectionChange">
						<el-table-column type="selection" width="55">全选</el-table-column>
						<el-table-column prop="aid" label="aid">
							<template slot-scope="scope">
								<a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
							</template>
						</el-table-column>
						<el-table-column prop="salesAcct" label="转入销售"></el-table-column>
						<el-table-column prop="invalidType" label="入库类型"></el-table-column>
						<el-table-column prop="ta" label="注册来源"></el-table-column>
						<el-table-column prop="createTime" label="入库时间" min-width="120"></el-table-column>
						<el-table-column prop="loginTime" label="最后登录时间" min-width="120"></el-table-column>
						<el-table-column prop="payTime" label="付款时间" min-width="120"></el-table-column>
						<el-table-column prop="price" label="付款金额"></el-table-column>
						<el-table-column prop="invalidMark" label="备注"></el-table-column>
						<el-table-column prop="hdVersion" label="互动版本"></el-table-column>
						<el-table-column prop="flyerVersion" label="微传单版本"></el-table-column>
						<el-table-column prop="mpVersion" label="公众号助手版本"></el-table-column>
					</el-table>

					<el-pagination @current-change="changePage"
                                   :current-page="pageInvalid.currentPage"
                                   :page-size="20"
                                   layout="total, prev, pager, next, jumper"
                                   :total="pageInvalid.total">
					</el-pagination>
				</el-tab-pane>
			</el-tabs>
		</div>
    </body>
	<script src="<%=HdOssResDef.getResPath("js_hdSale")%>" type="text/javascript" charset="utf-8"></script>
	<script type="text/javascript" charset="utf-8">

	</script>

</html>

