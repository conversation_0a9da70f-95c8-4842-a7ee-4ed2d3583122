<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.lang.*"%>
<%@ page import="java.sql.*"%>
<%@ page import="java.util.regex.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="java.text.SimpleDateFormat"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%
//if(!Auth.checkFaiscoAuth("authHdManage|authAd", false)){out.println("没有权限");return;}%>
<%
/*获得所有已经发布的活动样板 */
SysFaiPortal oPortal = (SysFaiPortal)Core.getSysKit(Kid.SYS_FAI_PORTAL);
SearchArg searchArg = new SearchArg();
ParamMatcher matcher = new ParamMatcher(HdModelDef.Info.PROPERTY,ParamMatcher.EQ,HdModelDef.Property.ISPUB);
searchArg.matcher = matcher;

//FaiList<Param> list = oPortal.getHdModelList(searchArg);

HdOss hdOss = (HdOss)WebOss.getCorpKit(Kid.HD_OSS);
Log.logDbg("unique-test hdoss = %s",hdOss);
FaiList<Param> list = hdOss.getHdModelList(searchArg);
String listJson = list.toJson();

%>

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>创建活动-广告配置</title>
	<%@ page import="fai.web.inf.*"%>
	<%@ page import="fai.comm.util.*"%>
	<link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
	<%=Web.getToken()%>
	<%=FrontEndDef.getJSsdkComponentScript(9000)%>
	<%
		int test = Parser.parseInt(request.getParameter("test"), 0);
		Boolean _debug = test == 3;
		int bannerType = Parser.parseInt(request.getParameter("id"), 1); //指bannerId
		int bannerId5_Type = Parser.parseInt(request.getParameter("type"), 0); //bannerId=5时，区分三个banner类型为【0-活动指南/1-B1/2-B2】
	%>
	<style>
		body {
			margin: 0;
			font-size: 14px;
			line-height: 1.2;
		}

		.title {
			font-weight: bold;
			font-size: 14px;
			padding-left: 7px;
		}

		.swiper-item {
			/* margin-left: 60px; */
		}

		.desc {
			margin: 0;
			color: #aaa;
		}

		.inline {
			display: inline-block;
		}

		[v-cloak] {
			display: none;
		}

		.padding-left {
			padding-left: 7px;
		}

		.preview {
			font-size: 14px;
			text-decoration: underline;
			margin: 0;
			padding-left: 5px;
			color: #666;
			cursor: pointer;
		}

		.preview-box {
			position: fixed;
			z-index: 10;
			left: 96px;
			top: 0;
			width: 1111px;
			height: 200px;
			overflow: hidden;
			background: lightblue;
			border: 1px solid #aaa;
		}

		.preview-mask {
			position: absolute;
			z-index: 2;
			left: 50%;
			top: 0;
			width: 2000px;
			margin-left: -1000px;
			height: 100%;
		}

		.preview-mask .pic {
			width: 100%;
			height: 100%;
			display: block;
		}

		.preview-mask .btn {
			position: absolute;
			z-index: 11;
			cursor: pointer;
			transform: translateX(32px);
		}

		.upload-preview-img img {
			max-width: 100%;
		}

		.debug {
			position: fixed;
			top: 30px;
			right: 100px;
			z-index: 10;
			width: 450px;
		}

		/* 倒计时 start*/
		.timerContainer {
			position: absolute;
			z-index: 11;
			cursor: pointer;
			left: 1000px;
			top: 20px;
		}
		.timerContainer .timerImg {
			position: absolute;
			z-index: -1;
			left: 50%;
			top: 50%;
			transform: translate(-50%, -50%);
		}
		.timerContainer .timer {
			position: absolute;
			z-index: 20;
			width: 145px;
			height: 20px;
			line-height: 20px;
  			text-align: center;
		}
		.timerContainer .timer span {
			width: 25px;
			display: inline-block;
			margin-right: 10px;
			font-size: 20px;
			font-weight: bold;
			text-align: right;
		}
		.timerContainer .timer span:nth-last-of-type(1) {
			margin-right: 0px;
		}
		/* 倒计时 end*/
		.hide {
			display: none;
		}
		.moreSetting.fa-collapse-borderless > .fa-collapse-item,
		.moreSetting.fa-collapse > .fa-collapse-item {
			border: none;
		}
		.moreSetting.fa-collapse > .fa-collapse-item > .fa-collapse-header {
			color: #4381fd;
		}
		#app .fa-anchor {
			margin-left: 16px;
    		margin-top: 10px;
		}
		#app .fa-anchor-wrapper {
			position: fixed;
		}
		form.el-form.formBox {
			margin-left: 150px;
		}
		.bannerTitle-box {
			margin-bottom: 10px;
			vertical-align: middle;
		}
		.bannerTitle {
			margin-right: 10px;
			font-size: 20px;
			font-weight: 500;
		}
		.disabled {
			background-color: #eee;
		}
		
	</style>
</head>
<body>
	
	<div id="app">
		<fa-anchor>
			<fa-anchor-link href="#bannerA" :title="openABTest ? 'A类' : '全量'">
				<fa-anchor-link 
					v-for="n in ANumber"
					:key="n"
					v-show="!(bannerId == 5 && bannerId5_Type != n)"
					:href="'#Abanner' + n"
					:title="getBannerTitle(n)"
				>
				</fa-anchor-link>
			</fa-anchor-link>
			<fa-anchor-link href="#bannerB" title="B类" v-if="openABTest">
				<fa-anchor-link 
					v-for="n in BNumber"
					:key="n"
					v-show="!(bannerId == 5 && bannerId5_Type != n)"
					:href="'#Bbanner' + n"
					:title="getBannerTitle(n)"
				>
				</fa-anchor-link>
			</fa-anchor-link>
			<fa-anchor-link href="#saveBtn" title="去保存" />
		</fa-anchor>
		<el-form
			class="form formBox"
			ref="form"
			label-width="80px"
			v-cloak
			v-loading="loading.state"
			:element-loading-text="loading.text"
		>
			<el-form-item label="AB测试：" v-if="bannerId != 11">
				<el-switch v-model="openABTest" active-text="开启" inactive-text="关闭"></el-switch>
			</el-form-item>

			<div>
				<p class="title" v-show="openABTest">A类（aid % 2 == 0）</p>
				<el-form id="bannerA" class="A" rel="A" label-width="95px" size="mini">
					<el-form-item v-if="false" label="banner数量">
						<el-select v-model.number="ANumber" style="width: 80px;" @change="handleNumChange('A', ANumber)">
							<el-option v-for="(n, i) in 11" :key="i" :value="i">{{ i }}</el-option>
						</el-select>
					</el-form-item>
					<fa-button type="dashed" v-if="showAddBannerBtn" @click="changeClassify('A', 0, 'add')">新增banner</fa-button>
					<div
						v-for="(n, i) in ANumber"
						v-show="i < ANumber && !(bannerId == 5 && bannerId5_Type != n)"
						:key="n"
						:id="'Abanner' + n"
					>
						<fa-divider class="bannerTitle-box" orientation="left">
							<span class="bannerTitle">{{getBannerTitle(n)}}</span>
							<fa-button v-if="ANumber > 1 && showAddBannerBtn" type="danger" @click="changeClassify('A', n, 'delete')">删除banner</fa-button>
						</fa-divider>
						<swiper-slide-form
							class="A"
							:class="n + 'hhhhh'"
							:classify="A[i]"
							:ref="'A' + n"
							:n="n"
							:bannerid="bannerId"
							@preview-change="previewChange"
							@preview-hide="previewHide"
						></swiper-slide-form>
						<fa-button type="dashed" v-if="showAddBannerBtn" @click="changeClassify('A', n, 'add')">新增banner</fa-button>
					</div>
				</el-form>
				<p class="title" v-show="openABTest">B类（aid % 2 != 0）</p>
				<el-form id="bannerB" class="B" rel="B" label-width="95px" size="mini" v-show="openABTest">
					<el-form-item v-if="false" label="banner数量">
						<el-select v-model.number="BNumber" style="width: 80px;" @change="handleNumChange('B', BNumber)">
							<el-option v-for="(n, i) in 11" :key="i" :value="i">{{ i }}</el-option>
						</el-select>
					</el-form-item>
					<fa-button type="dashed" v-if="showAddBannerBtn" @click="changeClassify('B', 0, 'add')">新增banner</fa-button>
					<div 
						v-for="(n, i) in BNumber"
						:key="n"
						:id="'Bbanner' + n"
						v-show="!(bannerId == 5 && bannerId5_Type != n)"
					>
						<fa-divider class="bannerTitle-box" orientation="left">
							<span class="bannerTitle">{{getBannerTitle(n)}}</span>
							<fa-button v-if="BNumber > 1 && showAddBannerBtn" type="danger" @click="changeClassify('B', n, 'delete')">删除banner</fa-button>
						</fa-divider>
						<swiper-slide-form
							class="B"
							:classify="B[i]"
							:ref="'B' + n"
							:n="n"
							:bannerid="bannerId"
							@preview-change="previewChange"
							@preview-hide="previewHide"
						></swiper-slide-form>
						<fa-button type="dashed" v-if="showAddBannerBtn" @click="changeClassify('B', n, 'add')">新增banner</fa-button>
					</div>
				</el-form>
			</div>
			

			<p class="padding-left">备注：统计dogid为{{dogidList[bannerId]}}</p>
			<p class="padding-left">
				<el-button id="saveBtn" type="primary" size="small" @click.stop="save">保存</el-button>
			</p>
		</el-form>

		<div
			class="preview-box"
			v-show="preview.show"
			:style="{
				width: preview.width + 'px',
				height: preview.height + 'px',
				left: preview.left + 'px',
				top: preview.top + 'px'
			}"
			@mouseenter="preview.show = true;"
			@mouseleave="previewHide"
			v-cloak
		>
			<div class="preview-mask">
				<img class="pic" :src="preview.bannerSrc">
				<img
					class="btn"
					:src="preview.btnSrcCurrent"
					@mouseenter="preview.btnSrcCurrent = preview.btnSrcHover"
					@mouseleave="preview.btnSrcCurrent = preview.btnSrc"
					:style="{
						left: preview.x + 'px',
						top: preview.y + 'px'
					}"
				>
				<div
					class="timerContainer"
					:style="{
						left: preview.timerPositionX + 'px',
						top: preview.timerPositionY + 'px'
					}"
					v-show="preview.showTimer"
				>
					<div class="timer" :style="{ color: '#' + preview.timerColor }">
						<img class="timerImg" :src="preview.timerSrc">
						<span>{{day}}</span>
						<span>{{hour}}</span>
						<span>{{minute}}</span>
						<span>{{second}}</span>
					</div>
				</div>
			</div>
		</div>
		<% if (_debug) { %>
			<div class="debug">
				<h2>（测试模式时才会显示）</h2>
				<h3>开启AB测试 {{ openABTest }}</h3>
				<h3>A类的轮播图数量 {{ ANumber }}</h3>
				<h3>B类的轮播图数量 {{ BNumber }}</h3>
				<div>
					<h4>A的数据</h4>
					<p>{{ A }}</p>
				</div>
				<div>
					<h4>B的数据</h4>
					<p>{{ B }}</p>
				</div>
			</div>
		<% } %>
	</div>

	<script type="text/x-template" id="swiper-slide-form">
		<el-form class="swiper-item" :model="classify" label-width="140px" size="mini">
			<%-- banner位 --%>
			<el-form-item :label="bannerid != 5 ? '广告banner：' : ['活动方案', 'B1活动广告位', 'B2活动广告位'][n - 1]" prop="bannerSrc" :rules="[rules.required]">
				<file-upload @fileUploadSuccess="setClassifyProp('bannerSrc', $event.path)" :previewImage="classify.bannerSrc">
					<span slot="desc">图片尺寸建议为{{imgSizeList['bannerSize'][bannerid]}}（或等比例大小）</span>
				</file-upload>
			</el-form-item>
			
			<%-- 按钮位 --%>
			<div v-show="!settingForNewVer">
				<%-- 按钮图片 --%>
				<el-form-item style="vertical-align: top;" label="按钮(常规)：" class="inline">
					<file-upload @fileUploadSuccess="setClassifyProp('btnSrc', $event.path)" :previewImage="classify.btnSrc">
						<span slot="desc">图片尺寸建议为{{imgSizeList['btnSize'][bannerid-1]}}（或等比例大小）</span>
					</file-upload>
				</el-form-item>

				<el-form-item style="vertical-align: top;" label="按钮(hover)：" class="inline" prop="btnSrcHover">
					<file-upload @fileUploadSuccess="setClassifyProp('btnSrcHover', $event.path)" :previewImage="classify.btnSrcHover">
						<span slot="desc">图片尺寸建议为{{imgSizeList['btnSize'][bannerid-1]}}（或等比例大小）</span>
					</file-upload>
				</el-form-item>

				<%-- 按钮位置 --%>
				<div>
					<el-form-item class="inline" label="按钮位置：" prop="btnPositionX" v-if="bannerid != 11">
						<div class="inline el-input el-input--mini el-input--prefix el-input--suffix" style="width: 100px; margin-right: 10px;">
							<input :value="classify.btnPositionX" @input="handleInput($event, 'btnPositionX')" @blur="handleInputBlur('btnPositionX')" class="el-input__inner" />
							<span class="el-input__prefix">
								<span>x:</span>
							</span>
							<span class="el-input__suffix">
								<span class="el-input__suffix-inner">
									<span>px</span>
								</span>
								<i class="el-input__icon el-input__validateIcon el-icon-circle-close"></i>
							</span>
						</div>
					</el-form-item>

					<el-form-item class="inline" prop="btnPositionY" label-width="0px" v-if="bannerid != 11">
						<div class="inline el-input el-input--mini el-input--prefix el-input--suffix" style="width: 100px; margin-right: 10px;">
							<input :value="classify.btnPositionY" @input="handleInput($event, 'btnPositionY')" @blur="handleInputBlur('btnPositionY')" class="el-input__inner" />
							<span class="el-input__prefix">
								<span>y:</span>
							</span>
							<span class="el-input__suffix">
								<span class="el-input__suffix-inner">
									<span>px</span>
								</span>
								<i class="el-input__icon el-input__validateIcon el-icon-circle-close"></i>
							</span>
						</div>

						<p class="preview inline" @mouseenter="previewChange($event, classify)" @mouseleave="previewHide">预览效果</p>
					</el-form-item>
				</div>
			</div>

			<el-form-item label="跳转到：" prop="link" v-if="bannerid != 11">
				<el-radio-group v-model="classify.isLink">
					<!--<el-radio-button :label="false">路径</el-radio-button>
					<el-radio-button :label="true">链接</el-radio-button>-->					
					<el-radio-button :label="2">路径</el-radio-button>					
					<el-radio-button :label="3">链接</el-radio-button>
					<el-radio-button :label="4" v-if="bannerid != 2">活动样板</el-radio-button>		
					<%-- 5管理平台已经用于特殊类型callback --%>
					<el-radio-button :label="6" v-if="settingForNewVer">关键词结果</el-radio-button>		
					<el-radio-button :label="7" v-if="settingForNewVer">节日类型</el-radio-button>		
					<el-radio-button :label="8" v-if="settingForNewVer">引流模块</el-radio-button>		
				</el-radio-group>
				<el-input v-show="classify.isLink==3" v-model.trim="classify.link" style="width: 400px; margin-top: 10px; display: block;"></el-input>
			</el-form-item>

			<el-form-item v-show="classify.isLink==2">
				<el-select v-model="classify.innerLink">
					<el-option v-for="(val, key) in innerLinkMapForNewVer" :key="key" :label="val" :value="key"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item v-show="classify.isLink==4">
				<el-select v-model="classify.id" filterable placeholder="查找">
					<el-option v-for="item in templateArray" :key="item.id" :label="item.name" :value="item.id"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item v-if="classify.isLink==6" prop="keywords" :rules="[rules.Keywords]">
				<el-input v-model.trim="classify.keywords" style="width: 400px; margin-top: 10px; display: block;"></el-input>
			</el-form-item>

			<el-form-item v-show="classify.isLink==7" >
				<el-select v-model="classify.festivalId" filterable placeholder="查找">
					<el-option v-for="item in festivalMap" :key="item.festivalId" :label="item.name" :value="item.festivalId"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item v-show="classify.isLink==8" >
				<el-select v-model="classify.hdGuideId" filterable placeholder="查找">
					<el-option v-for="item in hdGuideMap" :key="item.hdGuideId" :label="item.name" :value="item.hdGuideId"></el-option>
				</el-select>
			</el-form-item>

			<el-form-item label="曝光srcId：" class="inline el-input--mini" prop="showSrcid" v-if="bannerid != 11">
				<input :value="classify.showSrcid" @input="handleInput($event, 'showSrcid')" @blur="handleInputBlur('showSrcid')" class="el-input__inner inline" style="width: 60px" />
			</el-form-item>

			<el-form-item label="点击srcId：" class="inline el-input--mini" prop="clickSrcid" v-if="bannerid != 11">
				<input :value="classify.clickSrcid" @input="handleInput($event, 'clickSrcid')" @blur="handleInputBlur('clickSrcid')" class="el-input__inner inline" style="width: 60px" />
			</el-form-item>

			<%-- 官网alt --%>
			<el-form-item v-if="bannerid == 3" label="alt：" class="inline el-input--mini" prop="clickSrcid" v-if="bannerid != 11">
				<input v-if="" :value="classify.alt" @input="handleInput($event, 'alt')" @blur="handleInputBlur('alt')" class="el-input__inner inline" />
			</el-form-item>

			<fa-collapse :bordered="false" class="moreSetting">
				<fa-collapse-panel header="更多设置">
					<%-- 上下线时间 --%>
					<div v-if="![5].includes(bannerid)">
						<el-form-item label="banner上线时间：" class="inline" prop="showType">
							<el-radio-group v-model="classify.showType">
								<el-radio :label="0">立即生效</el-radio>
								<el-radio :label="1">定时上下线：</el-radio>
							</el-radio-group>
						</el-form-item>

						<el-form-item prop="showStartTime" class="inline" label-width="0px">
							<el-date-picker
								v-model="classify.showStartTime"
								type="datetime"
								placeholder="选择日期时间"
								:picker-options="startDatePicker"
								@change="changeBannerTime('showStartTime')"
							>
							</el-date-picker>
						</el-form-item>
						至
						<el-form-item prop="showEndTime" class="inline" label-width="0px">
							<el-date-picker
								v-model="classify.showEndTime"
								type="datetime"
								placeholder="选择日期时间"
								:picker-options="endDatePicker"
								@change="changeBannerTime('showEndTime')"
							>
							</el-date-picker>
						</el-form-item>
					</div>

					<%-- 倒计时 --%>
					<div v-if="bannerid == 1">
						<div style="border-top: 2px dashed #ffffff;">
							<el-form-item label="显示倒计时：" class="inline" prop="showTimer" style="margin-top: 18px;">
								<el-radio-group v-model="classify.showTimer">
									<el-radio :label="1">是</el-radio>
									<el-radio :label="0">否</el-radio>
								</el-radio-group>
							</el-form-item>
						</div>

						<el-form-item style="vertical-align: top;" label="倒计时图标：" prop="timerSrc">
							<file-upload @fileUploadSuccess="setClassifyProp('timerSrc', $event.path)" :previewImage="classify.timerSrc">
								<span slot="desc">图片尺寸建议为150*40（或等比例大小）</span>
							</file-upload>
						</el-form-item>

						<el-form-item style="vertical-align: top;" label="倒计时文字色值：" prop="timerColor">
							<div class="inline el-input el-input--mini el-input--prefix el-input--suffix" style="width: 110px; margin-right: 10px;">
								<input :value="classify.timerColor" @input="inputTimerColor($event)" @blur="handleInputBlur('timerColor')" class="el-input__inner" />
								<span class="el-input__prefix">
									<span>#</span>
								</span>
							</div>
						</el-form-item>

						<div style="margin-bottom: 18px; border-bottom: 2px dashed #ffffff;">
							<el-form-item class="inline" label="倒计时位置：" prop="timerPositionX">
								<div class="inline el-input el-input--mini el-input--prefix el-input--suffix" style="width: 100px; margin-right: 10px;">
									<input :value="classify.timerPositionX" @input="handleInput($event, 'timerPositionX')" @blur="handleInputBlur('timerPositionX')" class="el-input__inner" />
									<span class="el-input__prefix">
										<span>x:</span>
									</span>
									<span class="el-input__suffix">
										<span class="el-input__suffix-inner">
											<span>px</span>
										</span>
										<i class="el-input__icon el-input__validateIcon el-icon-circle-close"></i>
									</span>
								</div>
							</el-form-item>

							<el-form-item class="inline" prop="timerPositionY" label-width="0px">
								<div class="inline el-input el-input--mini el-input--prefix el-input--suffix" style="width: 100px; margin-right: 10px;">
									<input :value="classify.timerPositionY" @input="handleInput($event, 'timerPositionY')" @blur="handleInputBlur('timerPositionY')" class="el-input__inner" />
									<span class="el-input__prefix">
										<span>y:</span>
									</span>
									<span class="el-input__suffix">
										<span class="el-input__suffix-inner">
											<span>px</span>
										</span>
										<i class="el-input__icon el-input__validateIcon el-icon-circle-close"></i>
									</span>
								</div>

								<p class="preview inline" @mouseenter="previewChange($event, classify)" @mouseleave="previewHide">预览效果</p>
							</el-form-item>
						</div>
					</div>
					
					<%-- 展示权限类型1[开通产品/版本/时间] --%>
					<div v-if="bannerid == 1 || settingForNewVer">
						<el-form-item label="展示权限：" class="inline">
							<el-select v-model="openOtherProducts" @change="changePermission($event, classify)" :disabled="bannerid == 6">
								<el-option v-for="(val, key) in openOtherProductsStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="admHdVer" class="inline" label-width="0px">
							<el-select v-model="admHdVer" @change="changePermission($event, classify)" :disabled="bannerid == 6">
								<el-option v-for="(val, key) in admHdVerStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="openHdTime" class="inline" label-width="0px" >
							<el-select v-model="openHdTime" @change="changePermission($event, classify)" :disabled="bannerid == 6">
								<el-option v-for="(val, key) in openHdTimeStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="payHdVerTime" class="inline" label-width="0px">
							<el-select v-model="payHdVerTime" v-show="admHdVer>1" :disabled="bannerid == 6">
								<el-option v-for="(val, key) in payHdVerTimeStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
							<el-button type="primary" :disabled="!addPermission" @click.stop="toAddPermission">添加</el-button>
						</el-form-item>
					</div>

					<%-- 展示权限类型2[登录态/版本] --%>
					<div v-if="[3, 4].includes(bannerid)">
						<el-form-item label="展示权限：" prop="user" class="inline">
							<el-select v-model="user" @change="changePermission($event, classify)">
								<el-option v-for="(val, key) in userStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
						</el-form-item>

						<el-form-item prop="wsHdVer" class="inline" label-width="0px">
							<el-select v-model="wsHdVer" v-show="user>1">
								<el-option v-for="(val, key) in wsHdVerStatus" :key="key" :label="val" :value="key"></el-option>
							</el-select>
							<el-button type="primary" :disabled="!addPermission" @click.stop="toAddPermission">添加</el-button>
						</el-form-item>
					</div>

					<%-- 展示权限[tag] --%>
					<el-form-item style="padding-bottom: 18px;">
						<el-tag
							v-for="(tag, key) in classify.showPermission"
							:key="key"
							style="margin-right: 5px;"
							closable
							@close="toDeletePermission(tag)">
							{{tag}}
						</el-tag>
					</el-form-item>
					
				</fa-collapse-panel>
			</fa-collapse>

		</el-form>
	</script>

	<script type="text/x-template" id="file-upload">
		<div class="file-upload-wrap">
			<el-upload
				class="inline"
				action="/ajax/advanceUpload.jsp?cmd=upload&maxWidth=10000&maxHeight=10000&imgMode=2"
				accept="image/*"
				:name="fileUploadName"
				:show-file-list="false"
				:before-upload="beforeFileUpload"
				:on-success="fileUploadSuccess"
				:on-progress="fileUploadProgress"
				:on-error="fileUploadError"
			>
				<el-button type="primary">上传图片</el-button>
			</el-upload>
			<p class="desc inline"><slot name="desc"></slot></p>
			<transition name="el-list">
				<div style="min-height: 15px; width: 340px;"><el-progress v-show="uploading" :percentage="percentage"></el-progress></div>
			</transition>
			<p style="margin: 0; width: 340px;"><img style="max-width: 100%;" v-show="previewImage" :src="previewImage" /></p>
		</div>
	</script>

	<% if (_debug) { %>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_polyfill")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_axios")%>"></script>
	<% } else { %>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_polyfill")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_axios")%>"></script>
	<% } %>
	<%=FrontEndDef.getPackage("fa-component", "~1.1.0")%>
	<script>
		// 1: 管理平台-创建活动（旧） 2: 管理平台-我的消息（新/旧） 3: 互动官网-首页banner 4: 互动官网-产品价格 5: B1/B2/活动指南（新管理平台原先使用的）6: B1(新) 7: B2（新）8：活动指南（新）9：C3（新）
		(function (Vue, axios, ELEMENT) {
			var bannerType = <%=bannerType%>;
			var bannerId5_Type = <%=bannerId5_Type%>;
			var isHdPortalBanner = ![3, 4].includes(bannerType);
			function getDataFormat (bannerId) {
				var authType = isHdPortalBanner ? 0 : 1;
				return {
					bannerSrc: "",
					btnSrc: "",
					btnSrcHover: "",
					btnPositionX: 0,
					btnPositionY: 0,
					isLink: 0,
					link: "",
					innerLink: "",
					id: 0,//游戏模板id
					keywords: '', //关键词结果
					festivalId: 2, //节日类型Id，默认元旦
					hdGuideId: 0, //引流类型Id
					showSrcid: 0,
					clickSrcid: 0,
					showTimer: 0,
					timerSrc: "",
					timerPositionX: 0,
					timerPositionY: 0,
					timerColor: 'FFFFFF',
					showType: 0,
					showStartTime: "",
					showEndTime: "",
					bannerPosition: 1,
					authType: authType, //0-互动管理平台 1-互动官网
					admAuthShow: [], //互动管理平台展示权限
					wsAuthShow: [], //互动官网展示权限
					showPermission: [], //展示在页面上的展示权限字符串
					alt: ""
				};
			}

			function isImageType (type) {
				return (type >= 1 && type <= 6);
			}

			// 设置拦截器
			axios.interceptors.response.use(function (response) {
				var data = response.data;

				if (data.rt) {
					ELEMENT.Message({
						type: "error",
						message: data.msg || "系统繁忙，请稍后重试"
					});
					return Promise.reject(response);
				}

				return Promise.resolve(response.data);
			}, function (error) {
				ELEMENT.Message({
					type: "error",
					message: "系统繁忙，请稍后重试"
				});
				return Promise.reject(error);
			});
			var api = (function (ajax) {
				function getHdBannerNew (bannerId) {
					return ajax({
						method: "get",
						url: "/ajax/hdProduct_h.jsp",
						params: {
							cmd: "getHdBannerNew",
							id: bannerId
						}
					});
				}
				function setHdBannerNew (data, bannerId) {
					return ajax({
						method: "post",
						url: "/ajax/hdProduct_h.jsp?cmd=setHdBannerNew",
						dataType: "json",
						data: 'openABTest=' + Number(data.openABTest) + '&data=' + encodeURIComponent(data.bannerData) + '&id=' + bannerId,
					});
				}

				function getAllFestivalKey (data, bannerId) {
					return ajax({
						method: "get",
						url: "http://i.hd.fff.com/api/util/getAllFestivalKey",
					});
				}

				return {
					getHdBannerNew: getHdBannerNew,
					setHdBannerNew: setHdBannerNew,
					getAllFestivalKey: getAllFestivalKey
				};
			})(axios);

			var components = (function () {
				function getCaretPosition (field) {
					var caretPosition = 0;

					if (field.selectionStart) {
						caretPosition = field.selectionStart
					}

					return caretPosition;
				}
				var fileUpload = {
					data: function () {
						return {
							uploading: false,
							percentage: 0,
							fileUploadName: "filedata",
							path: this.previewImage
						};
					},
					props: ["previewImage"],
					template: "#file-upload",
					methods: {
						toggleUploading: function (force) {
							var _this = this;
							var uploading = force !== void 0 ? force : !_this.uploading;

							setTimeout(function () {
								_this.$nextTick(function () {
									_this.uploading = uploading;
								});
							}, uploading ? 0 : 100);
						},
						beforeFileUpload: function (file) {
							if (file.size > 5 * 1024 * 1024) {
								this.$message.error("单个文件超过5MB！");
								return false;
							}

							this.toggleUploading(true);
							this.percentage = 0;
						},
						fileUploadSuccess: function (response, file, fileList) {
							if (isImageType(response.type)) {
								this.$message.success("文件 " + response.name + " 上传成功！");
								this.previewImage = response.path;
								this.$emit("fileUploadSuccess", response);
							} else {
								this.$message.error("文件 " + response.name + " 类型不允许！");
							}
							this.toggleUploading(false);
						},
						fileUploadProgress: function (event, file, fileList) {
							this.percentage = Math.round(event.percent);
						},
						fileUploadError: function (err) {
							var _this = this;
							this.$message.error("系统繁忙，请稍后重试");
							this.toggleUploading(false);
						}
					}
				};

				//将listJson转成Json对象
				var listArrayJson = <%=listJson%>;
				//	console.log(listArrayJson);
				var arr = [];//[{"id":id,"name":"xxx"}...]
				for(var i=0; i<listArrayJson.length; ++i){
					var id = listArrayJson[i].id;
					var name = id+"-"+listArrayJson[i].name;
					var json = {"id":id,"name":name};
					arr.push(json);
				}

				var festivalKeyIdList = JSON.parse('{"keyIdList":[2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,63,18,65,19,20,21,22,25,26,24,23,66,27,28,29,30,31,32,34,33,35,36,37,38,39,40,41,43],"festivalMap":{"2":{"name":"元旦节","date":"2020-1-1,2021-1-1"},"3":{"name":"腊八节","date":"2020-1-2,2021-1-20"},"4":{"name":"小年","date":"2020-1-17,2021-2-4"},"5":{"name":"除夕","date":"2020-1-24,2021-2-11"},"6":{"name":"春节","date":"2020-1-25,2021-2-12"},"7":{"name":"立春","date":"2020-2-4,2021-2-3"},"8":{"name":"元宵节","date":"2020-2-8,2021-2-26"},"9":{"name":"情人节","date":"2020-2-14,2021-2-14"},"10":{"name":"惊蛰","date":"2020-3-5,2021-3-5"},"11":{"name":"妇女节","date":"2020-3-8,2021-3-8"},"12":{"name":"植树节","date":"2020-3-12,2021-3-12"},"13":{"name":"白色情人节","date":"2020-3-14,2021-3-14"},"14":{"name":"春分","date":"2020-3-20,2021-3-20"},"15":{"name":"愚人节","date":"2020-4-1,2021-4-1"},"16":{"name":"清明节","date":"2020-4-4,2021-4-4"},"17":{"name":"复活节","date":"2020-4-12,2021-4-4"},"18":{"name":"劳动节","date":"2020-5-1,2021-5-1"},"19":{"name":"母亲节","date":"2020-5-10,2021-5-9"},"20":{"name":"吃货节","date":"2020-5-17,2021-5-17"},"21":{"name":"520情人节","date":"2020-5-20,2021-5-20"},"22":{"name":"儿童节","date":"2020-6-1,2021-6-1"},"23":{"name":"端午节","date":"2020-6-25,2021-6-14"},"24":{"name":"父亲节","date":"2020-6-21,2021-6-20"},"25":{"name":"618","date":"2020-6-18,2021-6-18"},"26":{"name":"夏至","date":"2020-6-21,2021-6-21"},"27":{"name":"建军节","date":"2020-8-1,2021-8-1"},"28":{"name":"七夕","date":"2020-8-25,2021-8-14"},"29":{"name":"开学季","date":"2020-9-1,2021-9-1"},"30":{"name":"中元节","date":"2020-9-2,2021-8-22"},"31":{"name":"教师节","date":"2020-9-10,2021-9-10"},"32":{"name":"秋分","date":"2020-9-22,2021-9-23"},"33":{"name":"国庆节","date":"2020-10-1,2021-10-1"},"34":{"name":"中秋节","date":"2020-10-1,2021-9-21"},"35":{"name":"重阳节","date":"2020-10-25,2021-10-14"},"36":{"name":"万圣节","date":"2020-11-1,2021-11-1"},"37":{"name":"立冬","date":"2020-11-7,2021-11-7"},"38":{"name":"双十一","date":"2020-11-11,2021-11-11"},"39":{"name":"感恩节","date":"2020-11-26,2021-11-25"},"40":{"name":"双十二","date":"2020-12-12,2021-12-12"},"41":{"name":"冬至","date":"2020-12-21,2021-12-21"},"43":{"name":"圣诞节","date":"2020-12-25,2021-12-25"},"63":{"name":"世界地球日","date":"2020-4-22,2021-4-22"},"65":{"name":"五四青年节","date":"2020-5-4,2021-5-4"},"66":{"name":"建党节","date":"2020-7-1,2021-7-1"}}}');
				var festivalMap = [];
				var festivalMap_old = festivalKeyIdList.festivalMap;
				// festivalMap[0] = '全部';
				for(var festivalId in festivalMap_old) {
					festivalMap.push({
						festivalId: parseInt(festivalId),
						name: festivalMap_old[festivalId].name
					});
				}
				console.log(festivalMap);

				var hdGuideMap = [
					{hdGuideId: 1, name: "互动多主体卡"},
					{hdGuideId: 2, name: "产品端新增升级钻石版单次活动购买弹窗"},
					{hdGuideId: 3, name: "活动嵌入小程序的二维码弹窗"},
				];
				//console.log(arr);

				var swiperSlideForm = {
					props: ["classify", "n", "bannerid"], //props必须用小写，否则无效
					data: function () {
						return {
							rules: {
								required: { required: true, message: "该项为必填" },
								startEndTime: { required: true, message: "选择了定时上下线时间，该项为必填" },
								Keywords: { required: true, message: "尚未填写关键词，请填写" },
								number: { type: "number", message: "请输入数字", trigger: "blur" }
							},
							innerLinkMap: {
								newActive: "创建活动",
								myActive: "我的活动",
								cavIndex: "核销管理",
								myInfo: "个人中心",
								giftCenter: "礼品中心",
								employee: "员工权限",
								staffLog: "操作日志",
								payService: "付费服务"
							},
							innerLinkMapForNewVer: {
								myActive: "我的活动",
								cavIndex: "核销管理",
								myInfo: "账号信息",
								editHostInfo: "主办单位",
								authService: "授权服务",
								giftCenter: "礼品中心",
								employee: "员工权限",
								staffLog: "操作日志"
							},
							openOtherProductsStatus: [
								"不限其他产品开通情况",
								"未开通建站",
								"未开通商城",
								"未开通轻站",
								"未开通传单",
								"未开通快图",
								"未开通悦客",
								"未开通助手",
								"未开通教育",
								"已开通建站",
								"已开通商城",
								"已开通轻站",
								"已开通传单",
								"已开通快图",
								"已开通悦客",
								"已开通助手",
								"已开通教育"
							],
							admHdVerStatus: [
								"不限版本",
								"互动免费版用户",
								"互动白银版用户",
								"互动铂金版用户",
								"互动钻石版用户",
								"互动门店版用户",
								"互动星钻版用户",
								"互动集团版用户"
							],
							openHdTimeStatus: [
								"不限开通互动时间",
								"开通互动≤7日",
								"开通互动≤15日",
								"开通互动≤30日",
								"开通互动≤60日",
								"开通互动≤90日",
								"开通互动>7日",
								"开通互动>15日",
								"开通互动>30日",
								"开通互动>60日",
								"开通互动>90日"
							],
							payHdVerTimeStatus: [
								"不限付费互动版本时间",
								"付费互动版本≤7日",
								"付费互动版本≤15日",
								"付费互动版本≤30日",
								"付费互动版本≤60日",
								"付费互动版本≤90日",
								"付费互动版本>7日",
								"付费互动版本>15日",
								"付费互动版本>30日",
								"付费互动版本>60日",
								"付费互动版本>90日"
							],
							userStatus: ["全量用户", "访客态用户", "登录态用户"],
							wsHdVerStatus: ["不限版本", "互动免费版", "互动白银版", "互动铂金版", "互动钻石版", "互动门店版", "互动星钻版", "互动集团版"],
							templateArray: arr, //模板列表
							//节日类型列表
							festivalMap: festivalMap,
							//引流类型列表
							hdGuideMap: hdGuideMap,
							addPermission: false,
							openOtherProducts: "不限其他产品开通情况",
							admHdVer: "不限版本",
							openHdTime: "不限开通互动时间",
							payHdVerTime: "不限付费互动版本时间", //展示互动管理平台
							user: "全量用户",
							wsHdVer: "不限版本", //展示互动官网
							bannerPosition: 1,
							startDatePicker: this.beginDate(),
							endDatePicker: this.processDate(),
							imgSizeList: {
								bannerSize: {
									1: '880*352',
									2: '280*100',
									3: '1920*600',
									4: '1920*280',
									5: '304*168',
									6: '480*180',
									7: '880*352',
									8: '304*168',
									9: '304*168',
									10: '304*168',
									11: '1920*950',
								},
								btnSize: ['150*40', '150*40', '150*40', '150*40', '150*40', '150*40', '150*40', '150*40', '150*40', '150*40', '224*63']
							},
							// 1: 管理平台-创建活动（旧） 2: 管理平台-我的消息（新/旧） 3: 互动官网-首页banner 4: 互动官网-产品价格 5: B1/B2/活动指南（新管理平台原先使用的）6: C1(新) 7: A1(新) 8: B1（新）9：B2（新） 10: 活动指南（新）
							settingForNewVer: [2, 5, 6, 7, 8, 9, 10].includes(bannerType)
						};
					},
					watch: {
						
					},
					template: "#swiper-slide-form",
					components: {
						"file-upload": fileUpload
					},
					methods: {
						previewChange: function (event, data) {
							this.$emit("preview-change", event, data);
						},
						previewHide: function () {
							this.$emit("preview-hide");
						},
						setClassifyProp: function (prop, value) {
							var that = this;
							that.$nextTick(function () {
								that.classify[prop] = value;
							});
						},
						handleInputBlur: function (prop) {
							if (this.classify[prop] == "") {
								this.$set(this.classify, prop, 0);
							}
						},
						inputTimerColor: function(event) {
							var value = event.target.value;
							if(value.length > 6) { return; }
							this.$set(this.classify, 'timerColor', value);
						},
						handleInput: function (event, prop) {
							var input = event.target;
							var value = input.value;
							if(prop != 'alt') {
								value = value.replace(/[^\d]/g, "");
							}
							var caretPosition;

							this.$set(this.classify, prop, value);

							if (input.value !== value) {
								caretPosition = getCaretPosition(event.target);
								input.value = value;
								input.setSelectionRange(caretPosition, caretPosition - 1);
							}
						},
						//以下【运营】互动广告位可配置化 wuyanting
						changePermission: function(event, data) { //改变展示权限时触发，判断是否显示添加按钮
							var addPermission = false;
							var auths = this.getAuths();
							if(this.classify.authType == 0) { //0-互动管理平台 1-互动官网
								addPermission = !(auths.openOtherProducts == 0 && auths.admHdVer == 0 && auths.openHdTime == 0); // 默认状态为“不限版本-不限开通互动时间”，默认状态时，“添加”按钮禁用，banner对进入管理平台的用户展示；
							}else{
								addPermission = !(auths.user == 0 && auths.wsHdVer == 0); // 默认状态为“全量用户”，此时“添加”按钮禁用；
							}
							this.addPermission = addPermission; 
						},
						getNewPermissionStatus: function(ver) {
							switch(ver) {
								case 0: //不限版对应0
									return 0;
								case 1: //免费版对应1
									return 1;
								case 2: //白银版对应1
									return 3;
								case 3: //铂金版对应2
									return 2;
								case 4: //门店版对应5
									return 5;
								case 5: //钻石版对应4
									return 4;
								case 6: //星钻版对应6
									return 6;
								case 7: //集团版对应7
									return 7
							}
						},
						toAddPermission: function() { //增加展示权限
							var authShowString = '';
							var auths = this.getAuths();
							if(this.classify.authType == 0) {
								var admAuthShow = {};
								var admHdVer = auths.admHdVer; //版本
								var openHdTime = auths.openHdTime; //开通时间
								var payHdVerTime = auths.payHdVerTime; //版本付费时间
								var openOtherProducts = auths.openOtherProducts; //开通其他产品的情况
								authShowString += this.openOtherProductsStatus[openOtherProducts] + ' ';
								authShowString += this.admHdVerStatus[admHdVer] + ' ';
								authShowString += this.openHdTimeStatus[openHdTime] + ' ';
								if(admHdVer > 1) { //付费版
									authShowString += this.payHdVerTimeStatus[payHdVerTime];
									admAuthShow.payHdVerTimeStatus = payHdVerTime-1;
								}
								admHdVer = this.getNewPermissionStatus(admHdVer); //修正版本对应数字
								admAuthShow.hdVerStatus = admHdVer-1;
								admAuthShow.openHdTimeStatus = openHdTime-1;

								// 开通其他产品情况 openOtherProductsStatus 新增教育, openOtherProducts 做相应改变
								if(openOtherProducts == 8) {	// 表示选择了 未开通教育 后端对应为15
									openOtherProducts = 16;
								}else if(openOtherProducts > 8 && openOtherProducts <= 15) {
									openOtherProducts--;
								}else if(openOtherProducts > 15) {	// 表示选择了 已开通教育 后端对应为16
									openOtherProducts++;
								}

								admAuthShow.otherProductOpenSituation = openOtherProducts-1;
								this.classify.admAuthShow.push(admAuthShow); // 管理平台-传给后端的展示权限的数组
							}else{
								var wsAuthShow = {};
								var user = auths.user; //登录状态
								var wsHdVer = auths.wsHdVer; //版本
								authShowString += this.userStatus[user] + ' ';
								user = user == 0 ? (user-1) : user;
								if(user == 2) { //登录态用户
									authShowString += this.wsHdVerStatus[wsHdVer];
									wsHdVer = this.getNewPermissionStatus(wsHdVer); //修正版本对应数字
									wsAuthShow.hdVerStatus = wsHdVer-1;
								}
								wsAuthShow.userStatus = user;
								this.classify.wsAuthShow.push(wsAuthShow); // 官网-传给后端的展示权限的数组
							}
							this.classify.showPermission.push(authShowString); //展示在页面上的字符串
						},
						toDeletePermission: function(tag) { //删除权限
							var tagIndex = this.classify.showPermission.indexOf(tag)
							this.classify.showPermission.splice(tagIndex, 1);
							if(this.classify.authType == 0) { //管理平台
								this.classify.admAuthShow.splice(tagIndex, 1);
							}else{ //官网
								this.classify.wsAuthShow.splice(tagIndex, 1);
							}
						},
						setAuth: function(auth) {
							var reg = /^[1-9]+[0-9]*]*$/;
							return reg.test(auth) ? auth : 0;
						},
						getAuths: function() { //选择权限，转化-1=>0初始化值
							var auths = {};
							if(this.classify.authType == 0) {
								auths.openOtherProducts = this.setAuth(this.openOtherProducts);
								auths.admHdVer = this.setAuth(this.admHdVer);
								auths.openHdTime = this.setAuth(this.openHdTime);
								auths.payHdVerTime = this.setAuth(this.payHdVerTime);
							}else{
								auths.user = this.setAuth(this.user);
								auths.wsHdVer = this.setAuth(this.wsHdVer);
							}
							return auths;
						},
						beginDate: function(){
							var _this = this;
							return {
								disabledDate(time){
									if (_this.classify.showEndTime) {  //如果结束时间不为空，则小于结束时间
										return time.getTime() < Date.now() || (new Date(_this.classify.showEndTime).getTime() < time.getTime());
									} else{
										return time.getTime() < Date.now();
									}
								}
							}
						},
						processDate: function() {
							var _this = this;
							return {
								disabledDate(time) {
									if(_this.classify.showStartTime) {
										return new Date(_this.classify.showStartTime) > time.getTime(); //开始时间不为空，结束时间大于开始时间
									}else{
										return time.getTime() < Date.now(); //开始时间不选，结束时间最大值大于等于当天
									}
								}
							}
						},
						changeBannerTime: function(type) {
							var selectTime = this.classify[type].getTime();
							this.classify[type] = selectTime;
						}
					},
					mounted: function() {
					}
				};

				return {
					swiperSlideForm: swiperSlideForm
				};
			})();

			var app = new Vue({
				el: "#app",
				data: {
					loading: {
						state: true,
						text: "拼命加载中..."
					},
					openABTest: false,
					preview: {
						show: false,
						top: 0,
						x: 0,
						y: 0,
						bannerSrc: "",
						btnSrcCurrent: "",
						btnSrc: "",
						btnSrcHover: "",
						timerSrc: "",
						timerPositionX: 0,
						timerPositionY: 0,
						width: 1111,
						height: 200,
					},
					day: 1,
					hour: 10,
					minute: 20,
					second: 50, // 倒计时 天-时-分-秒
					A: [],
					B: [],
					ANumber: 0,
					BNumber: 0,
					bannerId: bannerType, 
					bannerId5_Type: bannerId5_Type,
					dogidList: {
						1: 1000419, 
						2: 1000143, 
						3: 1000277, 
						4: 1000277, 
						5: 1000419, 
						6: 1000419, 
						7: 1000419, 
						8: 1000419, 
						9: 1000419,
						10: 1000419
					},
					limitBannerType: [].includes(bannerType),
					isHdPortalBanner: isHdPortalBanner,
				},
				computed: {
					showAddBannerBtn: function() {
						return [6, 11].indexOf(this.bannerId) < 0;
					}
				},
				components: {
					"swiper-slide-form": components.swiperSlideForm
				},
				methods: {
					getBannerTitle: function(n) {
						if (this.bannerId == 5) {
							return ['活动方案', 'B1活动广告位', 'B2活动广告位'][n - 1];
						}else if (this.bannerId == 6) {
							return ['免费版广告位', '付费版广告位'][n - 1];
						}else {
							return n + '-banner';
						}
					},
					// 改变banner数量
					handleNumChange: function (classify, val) {
						while (this[classify].length < val) {
							this[classify].push(getDataFormat(this.bannerId));
						}
					},
					changeClassify: function(classify, index, opt) {
						var _this = this;
						if(opt == 'add') {
							_this[classify].splice(index, 0, getDataFormat(this.bannerId));
							_this[classify + 'Number']++;
						}else if(opt == 'delete'){
							this.$confirm({
								title: '即将删除[' + index + '-广告banner]',
								content: '点击“确认”，即将此位的数据和广告位都清除，排序在后面banner则向前移一位；点击“取消”，则无改动',
								onOk() {
									setTimeout(function() {
										_this[classify].splice(index-1, 1);
										_this[classify + 'Number']--;
									});
								},
								onCancel() {},
							});
							
						}
					},
					previewChange: function (event, data) {
						var offset = event.target.getBoundingClientRect();
						var bannerId = this.bannerId;
						this.preview.height = 200;
						if(bannerId == 3) {
							this.preview.height = 600;
						}else if(bannerId == 4){
							this.preview.height = 280;

						}
						this.preview.top = offset.top + offset.height;
						this.preview.x = data.btnPositionX;
						this.preview.y = data.btnPositionY;
						this.preview.bannerSrc = data.bannerSrc;
						this.preview.btnSrc = data.btnSrc;
						this.preview.btnSrcHover = data.btnSrcHover;
						this.preview.btnSrcCurrent = data.btnSrc;
						this.preview.timerSrc = data.timerSrc;
						this.preview.timerPositionX = data.timerPositionX;
						this.preview.timerPositionY = data.timerPositionY;
						this.preview.timerColor = data.timerColor;
						this.preview.showTimer = data.showTimer == 1;
						this.preview.show = true;
						data.showType && data.showEndTime && this.countTime(data.showEndTime);
					},
					previewHide: function () {
						this.preview.show = false;
						clearTimeout(this.timer);
					},
					// 校验
					validateForm: function (callback) {
						var i, len;
						var _this = this;
						var count = 0;
						var flag = false;

						function validate (valid) {
							if (flag) return;

							if (!valid) {
								flag = true;
								callback(false);
								return;
							}
							count++;

							if (count === (_this.ANumber + (_this.openABTest ? _this.BNumber : 0))) {
								flag = true;
								callback(true);
								return;
							}
						}

						if (this.ANumber === 0) {
							if (this.openABTest) {
								if (this.BNumber === 0) {
									callback(true);
								}
							} else {
								callback(true);
								return;
							}
						}

						for (i = 1, len = this.ANumber; i <= len; i++) {
							this.$refs['A' + i][0].$children[0].validate(validate);
						}

						if (this.openABTest) {
							for (i = 1, len = this.BNumber; i <= len; i++) {
								this.$refs['B' + i][0].$children[0].validate(validate);
							}
						}
					},			
					// 设置倒计时
					countTime: function(daoqiTime) {
						var _this = this;
						var oneDayTime = 24 * 60 * 60 * 1000;
						var timer;
						var coutLastTime = function(lessTime){
							var dayTxt = '';
							var dayCout = Math.floor( lessTime / oneDayTime );
							if( lessTime > oneDayTime ){
								lessTime = lessTime - (dayCout * oneDayTime);
							}
							var hours = Math.floor( lessTime / (60 * 60 * 1000) );
							lessTime -= hours * 60 * 60 * 1000;
							var minuts = Math.floor( lessTime / (60 * 1000) );
							lessTime -= minuts * 60 * 1000;
							var second = Math.floor( lessTime / 1000 );
							if( dayCout < 10 ){
								dayCout = '0' + dayCout;
							}
							if( hours < 10 ){
								hours = '0' + hours;
							}
							if( minuts < 10 ){
								minuts = '0' + minuts;
							}
							if( second < 10 ){
								second = '0' + second;
							}
							return [dayCout, hours, minuts, second];
						};
						var startPunch = function(){
							var timeDef = function(){
								var nowTime = _this.getServerTime();
								if( nowTime >= daoqiTime ){
									clearTimeout(_this.timer);
								}else{
									var lastTime = coutLastTime(daoqiTime - nowTime);
									_this.day = lastTime[0];
									_this.hour = lastTime[1];
									_this.minute = lastTime[2]; 
									_this.second = lastTime[3];
									//console.log('day:' + _this.day + ' hour:' + _this.hour + ' minute:' + _this.minute + ' second:' + _this.second);
									_this.timer = setTimeout(timeDef, 1000);
								}
							};
							timeDef();
						};
						startPunch();
					},
					// 系统时间
					getServerTime: function() {
						return new Date().getTime();
					},
					// 初始化时合并模板对象
					extend: function(target, options, isA) { 
						for (name in options) {
							if(name == "link" && target[name]) {
								target[name] = decodeURIComponent(target[name]); //对链接解码
							}else{
								!target[name] && (target[name] = options[name]);
							}
						}
						return target;  
					},
					//判断是否重复冲突
					isRepeat: function isRepeat(arr){
						arr = arr.sort();
						for(var i=0, len = arr.length;i<len;i++){
							if(arr[i]==arr[i+1]){
								return true;
							}
						}
						return false;
					},
					canSave: function(type) {
						var bannerList = JSON.parse(JSON.stringify(this[type]));
						var number = type + 'Number';
						var srcIdList = [];
						
						for(var i = 0; i < this[number]; i++) {
							var bannerInfo = bannerList[i];
							if(this.bannerId == 1 && bannerInfo.showTimer && bannerInfo.showType == 0) {
								this.$alert('当前页面：选择了显示倒计时的选项，请选择定时上下线！', {
									confirmButtonText: '确定',
								});
								return false;
							}
							//showtype==1时，判断是否填入日期
							if(bannerInfo.showType == 1 && (bannerInfo.showStartTime == 0 || bannerInfo.showStartTime == 0)) {
								this.$alert('当前页面：选择了定时上下线的选项，请填入时间！', {
									confirmButtonText: '确定',
								});
								return false;
							}
							bannerInfo.authType = this.isHdPortalBanner ? 0 : 1;
							// 将String转为int，兼容官网中java获取属性
							bannerInfo.showSrcid = Number(bannerInfo.showSrcid);
							bannerInfo.clickSrcid = Number(bannerInfo.clickSrcid);
							bannerInfo.btnPositionX = Number(bannerInfo.btnPositionX);
							bannerInfo.btnPositionY = Number(bannerInfo.btnPositionY);
							bannerInfo.link = encodeURIComponent(bannerInfo.link); //对链接编码，否则数据会被截断
							srcIdList.push(bannerInfo.showSrcid);
							srcIdList.push(bannerInfo.clickSrcid);
						}
						var bannerSrcIdIsRepeat = this.isRepeat(srcIdList);
						return {
							bannerList: bannerList,
							isRepeat: bannerSrcIdIsRepeat
						};
					},
					save: function () {
						var _this = this;
						var bannerListA = this.canSave('A').bannerList;
						var isRepeatA = this.canSave('A').isRepeat;
						var bannerListB = this.canSave('B').bannerList;
						var isRepeatB = this.canSave('B').isRepeat;
						
						if((this.openABTest && (isRepeatA || isRepeatB)) || isRepeatA) {
							_this.$message.error('当前页面：存在 srcId 排序重复，请检查！')
							return;
						}
						this.validateForm(function callback (valid) {
							if (!valid) {
								_this.$message.error("尚有信息未填写或填写错误，请先填写或修改");
								return;
							}
							var bannerData= JSON.stringify({
									dataList: {
										A: bannerListA,
										B: bannerListB
									},
									ANumber: _this.ANumber,
									BNumber: _this.BNumber
								});
							api.setHdBannerNew({
								openABTest: _this.openABTest,
								bannerData: bannerData,
							}, _this.bannerId).then(function (result) {
								_this.$message.success("保存成功!");
							});
						});
					},
 				},
				beforeCreate: function () {
					var _this = this;
					_this.bannerId = _this.bannerId ? _this.bannerId : bannerType;
					_this.setBannerData = function (result) {
						var data = result.data;
						var bannerData = data.data || JSON.stringify({
							dataList: {
								A: [],
								B: []
							},
							ANumber: 0,
							BNumber: 0
						});
						bannerData = JSON.parse(bannerData);

						_this.$nextTick(function () {
							_this.$set(_this.loading, "state", false);
							_this.openABTest = !!data.openABTest;
							var _bannerData = getDataFormat(_this.bannerId); //banner模板
							var A = bannerData.dataList.A;
							var B = bannerData.dataList.B;
							if (_this.bannerId == 5) {
								_this.ANumber = 3;
								_this.BNumber = 3;
							}else if (_this.bannerId == 6) {
								_this.ANumber = 2;
								_this.BNumber = 2;
							}else {
								_this.ANumber = bannerData.ANumber;
								_this.BNumber = bannerData.BNumber;
							}
							//兼容旧数据，旧数据新增对象属性的初始化值
							for(var i = 0; i < _this.ANumber; i++) {
								A[i] = _this.extend(A[i] || {}, _bannerData);
							}
							for(var i = 0; i < _this.BNumber; i++) {
								B[i] = _this.extend(B[i] || {}, _bannerData);
							}
							// 兼容旧数据，去掉排序以及设置banner个数，改为新增删除按钮设置banner
							if(_this.ANumber < A.length) {
								A = A.slice(0, _this.ANumber);
							}
							if(_this.BNumber < B.length) {
								B = B.slice(0, _this.BNumber);
							}
							function compare(property){
								return function(a,b){
									var value1 = a[property];
									var value2 = b[property];
									return value1 - value2;
								}
							}
							A.sort(compare('bannerPosition'));
							B.sort(compare('bannerPosition'));
							_this.A = A;
							_this.B = B;
						});
					}
					_this.getHdBanner = api.getHdBannerNew(_this.bannerId).then(_this.setBannerData).catch(function () {
						_this.loading.text = "数据加载失败，请刷新页面重试!";
					});
				},
				created: function() {
					console.log("aa");
					console.log(this.bannerId5_Type);
				}
			});
		} (Vue, axios, ELEMENT));
	</script>
</body>
</html>
