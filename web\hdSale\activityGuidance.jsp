<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.webhdoss.app.*" %>


<%
    if (!WebHdOss.checkSession(response)) {
        return;
    }
    if (!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)) {
        out.println("没有权限");
        return;
    }
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>活动引导</title>
    <%@ include file="/comm/link.jsp.inc" %>
    <%@ include file="/comm/script.jsp.inc" %>
    <link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>"/>

</head>
<body>
<div id="hdSale-activityGuidance">

    <el-dialog  v-cloak title="基本信息" :visible.sync="aidMsgDialog.show" @close="closeAidDialog" width="50%" center >
        <div style="padding-left: 5%">
            <div style=" float:left; width:35%;">
                AID：<a v-bind:href="aidMsgDialog.aidUrl" target='_blank' >{{aidMsgDialog.data.aid}}</a><br>
                账号：<a v-text="aidMsgDialog.data.aacct"></a><br>
                企业名称：<a v-text="aidMsgDialog.data.name"></a><br>
                实名手机：<a v-text="aidMsgDialog.data.signupMobile"></a><br>
                验证手机：<a v-text="aidMsgDialog.data.verifyPhone"></a><br>
                注册邮箱：<a v-text="aidMsgDialog.data.email"></a><br>
                验证邮箱：<a v-text="aidMsgDialog.data.verifyEmail"></a><br>
                负责人：<a v-text="aidMsgDialog.data.regPersonName"></a><br>
                电话：<a v-text="aidMsgDialog.data.regPhoneName"></a><br>
                手机：<a v-text="aidMsgDialog.data.regMobileName"></a><br>
                行业：<a v-text="aidMsgDialog.data.tradeName"></a><br>
            </div>
            <div style=" float:left; width:35%;">
                <div>
                    注册来源：<a v-text="aidMsgDialog.data.taName"></a><br>
                    注册主体：<a v-text="aidMsgDialog.data.regSubject"></a><br>
                    注册用途：<a v-text="aidMsgDialog.data.corpGoalName"></a><br>
                    注册时间：<a v-text="aidMsgDialog.data.createTimeName"></a><br>
                    进入管理平台时间：<a v-text="aidMsgDialog.data.intoSystemName"></a><br>
                    互动开通时间：<a v-text="aidMsgDialog.data.openHDTimeName"></a><br>
                    助手开通时间：<a v-text="aidMsgDialog.data.openWXTimeName"></a><br>
                    网站开通时间：<a v-text="aidMsgDialog.data.openJzTimeName"></a><br>
                    邮箱开通时间：<a v-text="aidMsgDialog.data.openMailTimeName"></a><br>
                    传单开通时间：<a v-text="aidMsgDialog.data.openFlyerTimeName"></a><br>
                    小程序开通时间：<a v-text="aidMsgDialog.data.openProgramTimeName"></a><br>
                </div>
            </div>
        </div>
        <div style="clear:both ; padding-top: 2%;padding-left: 5%">

        </div>
        <div style="padding-top: 2%;padding-left: 2%;padding-right: 2%;">
            <el-input type="textarea"  class="fai-textarea"  :rows="6" :disabled="true" resize="none" :value="aidMsgDialog.mark"></el-input>
            <div style="width: 100%">
                <div style="float: left; width: 90%;padding-top: 2px">
                    <el-input style="width: 100%" v-model="aidMsgDialog.editMark"  placeholder="请输入备注" resize="none"  ></el-input>
                </div>
                <div style="float: right; width: 10% ; text-align: center ;padding-top: 2px ">
                    <el-button type="primary" @click="submitMark(aidMsgDialog.data.aid,aidMsgDialog.editMark,aidMsgDialog.mark)" size="mini" >提交</el-button>
                </div>
            </div>
        </div>
        <div style=" clear: both; width: 100%;padding-top: 10px;">
            <el-button plain @click="changeAid('up',aidMsgDialog.data.aid)" type="info" size="mini" >< 上一个</el-button>

            <el-button plain @click="changeAid('down',aidMsgDialog.data.aid)"type="info" size="mini" style="float: right"  >下一个 ></el-button>
        </div>

    </el-dialog>

    <el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
        <!-- 查询条件start -->
        <div id="select" style="height:40px">
            <span>aid<el-input v-model="form.aid"></el-input></span>
            <span>领取人
                <el-select v-model="form.pm.value">
                    <el-option v-for="select in form.pm.labelList" :label="select.name" :value="select.label"
                               :key="select.label"></el-option>
                </el-select>
            </span>
            <span>注册来源
                <el-select v-model="form.ta.value">
                    <el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label"
                               :key="select.label"></el-option>
                </el-select>
            </span>
            <span>标记
                <el-select v-model="form.intent.value">
                    <el-option v-for="select in form.intent.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
                </el-select>
            </span>
            <span>
                <span class="demonstration">注册时间:  <el-checkbox v-model="form.regTime.check"></el-checkbox></span>
                <el-date-picker class="fai-daterange" type="date" v-model="form.regTime.start" placeholder="选择日期"
                                :editable=false></el-date-picker>-
                <el-date-picker class="fai-daterange" type="date" v-model="form.regTime.end" placeholder="选择日期"
                                :editable=false></el-date-picker>
            </span>
            <span>
                <span class="demonstration">领取时间: <el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
                <el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
                <el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
            </span>

            <span>
                 <el-form-item>
                    <el-button type="primary" @click="getActivityList">查询</el-button>
                </el-form-item>
            </span>
        </div>
        <!-- 查询条件end -->
        <!-- 分配start -->
        <div id="allot" v-if="this.auth.authAdm || this.auth.hdSaleManage" style="height:40px">
            <span>领取人
                <el-select v-model="form.pm.value">
                    <el-option v-for="select in form.pm.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
                </el-select>
            </span>
            <span>
                <span class="demonstration">登陆时间: <el-checkbox v-model="form.loginTime.check"></el-checkbox></span>
                <el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.start" placeholder="选择日期" :editable=false></el-date-picker>-
                <el-date-picker class="fai-daterange" type="date" v-model="form.loginTime.end" placeholder="选择日期" :editable=false></el-date-picker>
            </span>
            <span>分配数量<el-input v-model="allot.allotCount" class="short-input"></el-input></span>
            <el-button type="primary" size="mini" @click="setActivityGuidance()">分配</el-button>
            <el-button type="primary" size="mini" style="margin-left: 30px; background-color: red; border: 1px solid red" @click="checkReleaseCount()">释放</el-button>
        </div>
        <!-- 分配end -->
    </el-form>

    <%--表格--%>
    <div>
        <el-table :data="dataList" style="width: 1000" max-height="620" stripe @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55">全选</el-table-column>

            <el-table-column  prop="aid" label="aid" width="90"  align="center">
                <template slot-scope="scope">
                    <%--<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>--%>
                    <a @click="showAidMsgDialog(scope.$index, dataList)">{{scope.row.aid}}</a>
                </template>
            </el-table-column>
            <el-table-column  label="标记"   width="130" align="center">
                <template slot-scope="scope">
                    <el-select v-model="scope.row.intent" style="float:center"  @change="changeIntent(scope.row.aid,scope.row.intent)">
                        <el-option  label="空" value="0"></el-option>
                        <el-option  label="有需求" value="1"></el-option>
                        <el-option  label="无需求" value="2"></el-option>
                        <el-option  label="未接通" value="3"></el-option>
                        <el-option  label="待跟进" value="4"></el-option>
                        <el-option  label="已指导" value="5"></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="mark" :show-Overflow-Tooltip=true label="备注" width="300" align="center" class="mark"></el-table-column>
            <el-table-column prop="name" label="企业名称" width="100" :show-Overflow-Tooltip=true align="center"></el-table-column>
            <el-table-column prop="mobile" label="注册手机" width="120" align="center"></el-table-column>
            <el-table-column prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
            <%--<el-table-column prop="ta" label="注册来源" width="120" align="center"></el-table-column>--%>
            <el-table-column prop="receiveTime" label="领取时间" width="160" align="center"></el-table-column>
            <el-table-column prop="acct" label="指导员" width="160" align="center"></el-table-column>
            <%--<el-table-column prop="tag" label="标签" width="150" :show-Overflow-Tooltip=true align="center"></el-table-column>--%>
            <el-table-column prop="loginTime" label="最后登录时间" width="160" align="center"></el-table-column>
            <el-table-column prop="updateTime" label="最后更新时间" width="160" align="center"></el-table-column>
        </el-table>
    </div>
    <!--表格数据展示 end-->
    <!--分页 end-->
    <div class="block">
        <el-pagination @size-change="handleSizeChange" @current-change="getActivityList()" :current-page.sync="pageData.currentPage"
                       :page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
        </el-pagination>
    </div>


</div>

</body>
<script type="text/javascript">
    var localTime = new Date();
    var activityGuidance = new Vue({
        el: '#hdSale-activityGuidance',
        data: {
            form: {
                aid: '',
                mark: '',
                pm: {
                    value: '',
                    name: '全部',
                    labelList: [
                        {name: "全部", label: ""},
                    ]
                },
                ta: {
                    name: '全部',
                    value: '-1',
                    labelList: [
                        {name: "全部", label: "-1"},
                    ]
                },
                intent: {
                    name: '全部',
                    value: '-1',
                    labelList: [
                        {name: "全部", label: "-1"},
                    ]
                },
                regTime: {
                    check: '',
                    start: localTime,
                    end: localTime
                },
                receiveTime: {
                    check: '',
                    start: localTime,
                    end: localTime
                },
                loginTime: {
                    check: '',
                    start: localTime,
                    end: localTime
                }
            },
            dataList: [],
            allot: {
                sacct: '',
                allotCount: ''
            },
            pageData:{
                currentPage:1,
                total:0,
                size:10,
            },
            aidMsgDialog: {
                show: false,
                aidUrl: '',
                index: '',
                data: '',
                mark: '',
                editMark: '',
            }
        },
        created: function () {
            this.initPage();
            this.getActivityList();
        },
        updated: function () {
        },
        methods: {
            //页面信息初始化
            initPage(){
                var arg = {"cmd": "getPageMessage"}
                Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON: true}).then(response => {
                    console.log(response);
                    let data = response.data;
                    this.form.intent.labelList = data.intent;
                    this.form.pm.labelList = data.pm;
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },
            getActivityList() {
                var arg = {
                    "cmd": "getActivityList",
                    "aid": this.form.aid,
                    "acct": this.form.pm.value,
                    "intent": this.form.intent.value,
                    "currentPage": this.pageData.currentPage,
                    "size": this.pageData.size,
                    "receiveTimeCheck": this.form.receiveTime.check,
                    "receiveTimeStart": Fai.tool.getDateTimeStart(this.form.receiveTime.start),
                    "receiveTimeEnd": Fai.tool.getDateTimeEnd(this.form.receiveTime.end),
                    "regTimeCheck": this.form.regTime.check,
                    "regTimeStart": Fai.tool.getDateTimeStart(this.form.regTime.start),
                    "regTimeEnd": Fai.tool.getDateTimeEnd(this.form.regTime.end),

                }
                Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON: true}).then(response => {
                    console.log(response.data)
                    this.dataList =  response.data.dataList;
                    this.pageData.total = response.data.total;
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },
            setActivityGuidance() {
                var arg = {
                    "cmd": "setActivityGuidance",
                    "sacct": this.form.pm.value,
                    "allotCount": this.allot.allotCount,
                    "loginTimeCheck": this.form.loginTime.check,
                    "loginTimeStart": Fai.tool.getDateTimeStart(this.form.loginTime.start),
                    "loginTimeEnd": Fai.tool.getDateTimeEnd(this.form.loginTime.end),
                }
                this.$message({
                    type: 'warning',
                    message: '后台请求中...'
                });
                Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON: true}).then(response => {
                    if (response.body.success == true) {
                        this.$message({
                            type: 'success',
                            message: response.body.msg
                        });
                    } else {
                        this.$message({
                            type: 'warning',
                            message: response.body.msg
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },

            // 先查询有多少条需要释放给前端回显
            checkReleaseCount() {
                var arg = {
                    "cmd": "checkReleaseCount",
                    "acct": this.form.pm.value,
                    "intent": this.form.intent.value
                }
                let that = this;

                Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON: true}).then(response => {
                    if (response.body.success == true) {
                        that.$confirm('确定释放 '+ response.body.count +" 条记录", '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        }).then(() => {
                            // 真正执行释放逻辑
                            var arg = {
                                "cmd":'releaseActivityGuidance',
                                "acct": this.form.pm.value,
                                "intent": this.form.intent.value
                            }
                            Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON:true}).then(response => {
                                this.$message({
                                    type: 'success',
                                    message: response.body.msg
                                });
                                this.getActivityList();
                            }, response => {
                                this.$message({
                                    type: 'warning',
                                    message: '系统错误!'
                                });
                            });

                        }).catch(() => {
                            this.$message({
                                type: 'info',
                                message: '已取消删除'
                            });
                        });
                    } else {
                        this.$message({
                            type: 'warning',
                            message: response.body.msg
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },

            handleSelectionChange() {
            },
            changeIntent(aid,intent){
                this.$confirm('确定修改?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    var arg = {
                        "cmd":"changeIntent",
                        "aid":aid,
                        "intent":intent,
                    }
                    Vue.http.post("/ajax/hdSale/activityGuidance_h.jsp", arg, {emulateJSON:true}).then(response => {
                        if(response.data.success){
                            this.$message({
                                type: 'success',
                                message: '修改成功!'
                            });
                        }else{
                            this.$message({
                                type: 'warning',
                                message: response.data.msg,
                            });
                        }
                    }, response => {
                        this.$message({
                            type: 'warning',
                            message: '系统错误!'
                        });
                    });

                }).catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消修改'
                    });
                });
            },
            handleSizeChange(){

            },
            showAidMsgDialog(index,dataList){
                //备注信息
                this.aidMsgDialog.show = true;
                let aid = dataList[index].aid;
                this.aidMsgDialog.aidUrl = dataList[index].aidUrl;
                this.aidMsgDialog.index=index;
                //企业信息
                this.getCorpInfo(aid);
            },
            getCorpInfo(aid){
                var arg = {
                    "aid":aid,
                    "cmd":"getCorpInfo",
                }
                Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
                    this.aidMsgDialog.data = response.data;
                    let mark = response.data.markName;
                    if(typeof obj == "undefined" || obj == null || obj == ""){
                        this.aidMsgDialog.mark = mark;
                    }else{
                        this.aidMsgDialog.mark = mark.replace("-----------------------","\n-----------------------\n");
                    }
                    console.log(response.data);
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },
            submitMark(aid,mark,oldmark){
                if(oldmark == undefined){
                    oldmark = ''
                }
                if (typeof mark == "undefined" || mark == null || mark == ""){
                    this.$message({
                        type: 'warning',
                        message: '备注不能为空!'
                    });
                    return;
                }
                var arg = {
                    "cmd":"setInfoMark",
                    "aid":aid,
                    "mark":mark,
                    "oldmark":oldmark
                }
                let url = "/ajax/hdSale_h.jsp";
                Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
                    if(response.body.success == true){
                        this.$message({
                            type: 'success',
                            message: '添加成功!'
                        });
                        this.aidMsgDialog.editMark = "";
                        this.aidMsgDialog.mark = response.body.data.markName;
                        for(var i = 0;i < this.dataList.length;i++){
                            if(this.dataList[i].aid == aid){
                                this.dataList[i].mark = mark;
                            }
                        }
                    }else{
                        this.$message({
                            type: 'warning',
                            message: response.body.msg
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '添加失败!'
                    });
                });

            },
            changeAid(opt,aid){
                let index = -1;
                let listLength = this.pageData.size -1;
                let totalPage = parseInt(this.pageData.total / this.pageData.size) + 1;

                //如果不是按照更新时间排序就可以用这方法，获取当前aid所在index
                for(var i = 0;i < this.dataList.length;i++){
                    if(this.dataList[i].aid == aid){
                        index = i;
                        this.aidMsgDialog.index= index +1 ;
                    }
                }

                //上一个aid
                if(opt == 'up'){
                    if(index == 0){//判断是不是第一页，是的话弹窗提示，不是的话获取上一页的最后一个
                        if(this.pageData.currentPage == 1){
                            this.$message({
                                type: 'warning',
                                message: '已经是第一个了!'
                            });
                        }else{//翻页
                            this.pageData.currentPage -= 1;
                            this.$message({
                                type: 'warning',
                                message: '正在跳转上一页，稍后再点击一次!'
                            });
                        }
                    }else if(index == -1){//翻页后找不到aid将index谁为
                        this.getCorpInfo(this.dataList[listLength].aid);
                        this.aidMsgDialog.aidUrl = this.dataList[listLength].aidUrl;
                    }else{
                        this.getCorpInfo(this.dataList[index-1].aid);
                        this.aidMsgDialog.aidUrl = this.dataList[index-1].aidUrl;
                    }
                }


                //下一个aid
                if(opt == 'down'){
                    if(this.pageData.currentPage == totalPage ){ //最后一页情况
                        let tempIndex = index + 1;
                        if(this.dataList.length == tempIndex){
                            this.$message({
                                type: 'warning',
                                message: '已经是最后一个了!'
                            });
                        }else{
                            this.getCorpInfo(this.dataList[index+1].aid);
                            this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                        }
                    }else if(index == listLength){//最后一个情况
                        //获取下一页数据
                        this.pageData.currentPage += 1;
                        this.$message({
                            type: 'warning',
                            message: '正在跳转下一页，稍后再点击一次!'
                        });
                    }else{
                        this.getCorpInfo(this.dataList[index+1].aid);
                        this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                    }
                }

            },
            closeAidDialog(){
                this.aidMsgDialog.index = '';
                this.aidMsgDialog.oldIndex='';
                //console.log("aid index = ",this.aidMsgDialog.index);
            },
        },

    })

</script>
</html>