/**
 * 获取分类列表
 * @param {*} data 
 * @param {number} data.name：非必填，按名称筛选 
 * @param {number} data.pageNo 页码
 * @param {number} data.pageLimit 每页条数
 * @returns 
 */
export const getCategoryList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/category/list", {
      params: data
    }).then(response => {
      resolve(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 添加分类
 * @param {*} data 
 * @param {string} data.name 分类名称
 * @returns 
 */
export const addCategory = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/category/add", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/*
 * 修改分类
 * @param {*} data 
 * @param {number} data.id 分类id
 * @param {string} data.name 分类名称
 * @returns 
 */
export const updateCategory = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/category/update", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 删除分类
 * @param {*} data 
 * @param {number} data.id 分类id
 * @returns 
 */
export const deleteCategory = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/category/delete", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};
