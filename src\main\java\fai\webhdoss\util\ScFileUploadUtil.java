package fai.webhdoss.util;

import fai.app.FileStgDef;
import fai.app.HdImgDef;
import fai.app.ScResDef;
import fai.cli.FileStgCli;
import fai.cli.HdImgCli;
import fai.cli.ScResCli;
import fai.cli.VideoSnapCli;
import fai.comm.jnetkit.server.fai.RemoteStandResult;
import fai.comm.util.*;
import fai.entity.res.ScResEntity;
import fai.entity.res.ScResExtraModal;
import fai.fileupload.utils.FileUrlUtils;
import fai.hdUtil.rpc.HdFaiClientProxyFactory;
import fai.web.*;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.nio.ByteBuffer;
import java.util.Calendar;
import java.util.List;

/**
 * 上传组件，cv TsFileUpload
 *
 * <AUTHOR> 2025/5/9 14:32
 * @Update HLS 2025/5/9 14:32
 **/
public class ScFileUploadUtil {
    /**
     * 文件上传
     *
     * <AUTHOR> 2025/5/9 17:17
     * @Update HLS 2025/5/9 17:17
     **/
    public static int uploadFile4Sc(ServletContext context, HttpServletRequest request, int scResType, int aid, Param info, int limitM) {
        int rt;
        int flow = Core.getFlow();

        FormRequest freq = new FormRequest();
        freq.setSizeMax((long) limitM * 1024 * 1024);

        rt = freq.attachServlet(context, request);
        if (rt != Errno.OK) {
            if (rt == FormRequest.ErrnoAttach.FILE_SIZE_LIMIT) {
                info.setInt(FileUpload.Info.LIMIT, limitM);
                return rt;
            }

            if (rt == FormRequest.ErrnoAttach.FILE_TYPE_INVALID) {
                return rt;
            }
            return rt;
        }

        // check max size limit
        // 这一步要放在attachServlet下面，因为该次request如果没有经过attachServlet的话，就会被拒绝，原因未知
        int requestSize = request.getContentLength();
        if (requestSize > limitM * 1024 * 1024) {
            info.setInt(FileUpload.Info.LIMIT, limitM);
            rt = FileUpload.ErrnoUpload.FILE_SIZE_LIMIT;
            return rt;
        }

        String ctrl = Parser.parseString(request.getParameter("ctrl"), "filedata");
        FormFile file = freq.getFile(ctrl);
        if (file == null) {
            rt = FileUpload.ErrnoUpload.FILE_CTRL_NOT_FOUND;
            Log.logErr(rt, "ctrl not found;flow=%d;aid=%d;ctrl=%s;", flow, aid, ctrl);
            return rt;
        }

        String name = Parser.parseString(request.getParameter("filename"), "");
        name = Str.isEmpty(name) ? Parser.parseString(file.getFileName(), "") : name;
        ByteBuffer fileBuf = ByteBuffer.wrap(file.get());

        // 如果不是img类型，则不做处理
        int fileType = FileEx.getFileType(name);
        if (FileEx.isImgType(fileType) || FileEx.Type.SVG == fileType) {
            rt = uploadImg4Sc(fileBuf, name, aid, scResType, info);
        } else {
            rt = uploadFileSc(fileBuf, name, aid, scResType, info);
        }

        info.setInt(FileUpload.Info.TYPE, fileType);
        info.setString(FileUpload.Info.NAME, name);
        info.setString(FileUpload.Info.PATH, getScFileUrl(info.getString(FileUpload.Info.ID, "")));

        return rt;
    }

    public static int uploadImg4Sc(ByteBuffer content, String name, int aid, int scResType, Param info) {
        int rt;
        int flow = Core.getFlow();
        int fileType = FileEx.getFileType(name);

        Ref<String> idRef = new Ref<>();
        Ref<Rect> sizeRef = new Ref<>();

        // 上传图片
        Ref<Integer> fileSizeRef = new Ref<>();
        rt = addScImg(content, scResType, name, aid, idRef, sizeRef, fileSizeRef);
        if (rt != Errno.OK) {
            Log.logErr(rt, "add ts img error;flow=%d;aid=%d;", flow, aid);

            if (sizeRef.value != null) {
                info.setInt(FileUpload.Info.WIDTH, sizeRef.value.width);
                info.setInt(FileUpload.Info.HEIGHT, sizeRef.value.height);
            }

            if (rt == FileStgDef.Errno.ERROR_IMG_FORMAT) {
                rt = FileUpload.ErrnoUpload.FILE_TYPE_FORMAT_ERROR;
                return rt;
            }

            return rt;
        }

        String id = idRef.value;

        info.setObject(FileUpload.Info.WIDTH, sizeRef.value.width);
        info.setObject(FileUpload.Info.HEIGHT, sizeRef.value.height);
        info.setInt(FileUpload.Info.TYPE, fileType);
        info.setString(FileUpload.Info.ID, id);
        info.setInt(FileUpload.Info.SIZE, fileSizeRef.value);// 这个之前是setLong的
        info.setString(FileUpload.Info.SIZE + "Name", TsMisc.formatFileSizeToStandard(fileSizeRef.value));// 这个之前是setLong的

        info.setLong(FileUpload.Info.CREATE_TIME, Calendar.getInstance().getTimeInMillis());
        info.setString(FileUpload.Info.NAME, name);
        return rt;
    }

    public static int addScImg(ByteBuffer imgBuf, int scResType, String imgName, int aid, Ref<String> dstIdRef, Ref<Rect> desSizeRef, Ref<Integer> fileSizeRef) {
        int rt;

        final int maxWidth = 0; // 0表示不缩放
        final int maxHeight = 0; // 0表示不缩放
        final int mode = Img.Mode.SCALE_DEFLATE_WIDTH;

        FileStgCli stgCli = FaiCliFactory.createCli(FileStgCli.class, Core.getFlow());
        ScResCli scResCli = HdFaiClientProxyFactory.createProxy(ScResCli.class);

        if (desSizeRef == null) {
            desSizeRef = new Ref<>();
        }

        int imgType = Img.parseImgType(imgBuf);
        if (imgType == FileEx.Type.BMP) {
            // bmp的图片要压缩为jpg
            Ref<ByteBuffer> jpgBuf = new Ref<>();
            if (!Img.toJpg(imgBuf, jpgBuf, maxWidth, maxHeight, mode, desSizeRef)) {
                rt = FileStgDef.Errno.ERROR_IMG_FORMAT;
                Log.logErr(rt, "bmp to jpg fail");
                return rt;
            }
            imgBuf = jpgBuf.value;
            imgType = FileEx.Type.JPEG;
        } else if (imgType != FileEx.Type.SVG) { // svg不用缩放
            Ref<ByteBuffer> desBuf = new Ref<>();
            if (!Img.resize(imgBuf, desBuf, maxWidth, maxHeight, mode, desSizeRef)) {
                rt = FileStgDef.Errno.ERROR_IMG_FORMAT;
                Log.logErr(rt, "img resize fail; imgType=%s", imgType);
                return rt;
            }
            imgBuf = desBuf.value;
        }

        if (desSizeRef.value == null) {
            // 图片尺寸未知，重新获取
            desSizeRef.value = Img.getRect(imgBuf);
        }
        if (desSizeRef.value == null) {
            desSizeRef.value = new Rect(0, 0);
        }

        int app = FileStgDef.App.SC_FILE;
        int width = desSizeRef.value.width;
        int height = desSizeRef.value.height;

        String id = FileStgDef.genImgId(app, imgType, false, width, height);
        rt = stgCli.addImgWithThumb(app, aid, id, imgBuf);
        if (rt != Errno.OK) {
            App.logErr(rt, "add ts img file error;aid=%d;app=%d;id=%s;", aid, app, id);
            return rt;
        }

        if (fileSizeRef == null) {
            fileSizeRef = new Ref<>();
        }
        fileSizeRef.value = imgBuf.limit();

        if (scResType == ScResDef.Type.OPT_VOICE || scResType == ScResDef.Type.OPT_BGM) {
            scResType = ScResDef.Type.OPT; // 背景音乐和配音的封面，都归类到默认的opt类型下
        }

        // 保存
        ScResEntity resEntity = new ScResEntity();
        resEntity.setAid(aid);
        resEntity.setResId(id);
        resEntity.setName(imgName);
        resEntity.setType(scResType);
        resEntity.setStatus(ScResDef.Status.TEMP);
        resEntity.setFileType(imgType);
        resEntity.setFileSize(Long.valueOf(fileSizeRef.value));
        RemoteStandResult standResult = scResCli.addRes(aid, resEntity);
        if (!standResult.isSuccess()) {
            App.logErr(rt, "add ts img file error;aid=%d;app=%d;id=%s;", aid, app, id);
            return rt;
        }

        dstIdRef.value = id;
        return rt;
    }

    public static int uploadFileSc(ByteBuffer content, String name, int aid, int scResType, Param info) {
        int rt;
        int flow = Core.getFlow();
        int app = FileStgDef.App.SC_FILE;
        int fileType = FileEx.getFileType(name);

        String id = FileStgDef.genFileId(app, fileType, false);
        rt = addFile(content, aid, id, app);
        if (rt != Errno.OK) {
            Log.logErr(rt, "upload ts file error;flow=%d;aid=%d;", flow, aid);
            return rt;
        }

        List<ScResEntity> resList = new FaiList<>();

        // 视频文件需要截取首帧图保存
        rt = snapVideo(id, aid, scResType, fileType, resList, info);
        if (rt != Errno.OK) {
            Log.logErr(rt, "snap video error;flow=%d;aid=%d;id=%s;", flow, aid, id);
            return rt;
        }


        int duration = getDuration(aid, id, fileType);

        // 保存
        int size = content.limit();
        ScResCli scResCli = HdFaiClientProxyFactory.createProxy(ScResCli.class);
        ScResEntity entity = new ScResEntity();
        entity.setAid(aid);
        entity.setResId(id);
        entity.setName(name);
        entity.setType(scResType);
        entity.setStatus(ScResDef.Status.TEMP);
        entity.setFileType(fileType);
        entity.setFileSize(Parser.parseLong(String.valueOf(size), 0L));
        ScResExtraModal modal = new ScResExtraModal();
        modal.setDuration(duration);
        entity.setExtraModal(modal);

        resList.add(entity);
        for (ScResEntity scResEntity : resList) {
            RemoteStandResult standResult = scResCli.addRes(aid, scResEntity);
            if (!standResult.isSuccess()) {
                Log.logErr(rt, "add  file error;aid=%d;app=%d;id=%s;", aid, app, id);
                return rt;
            }
        }

        info.setString(FileUpload.Info.NAME, name);
        info.setInt(FileUpload.Info.SIZE, size);
        info.setString(FileUpload.Info.SIZE + "Name", TsMisc.formatFileSizeToStandard(size));
        info.setString(FileUpload.Info.ID, id);
        info.setInt("duration", duration);

        return rt;
    }

    private static int getDuration(int aid, String id, int fileType) {
        try {
            VideoSnapCli videoSnapCli = FaiCliFactory.createCli(VideoSnapCli.class, Core.getFlow());
            Ref<Long> durationRef = new Ref<>();
            int rt = videoSnapCli.getDuration(aid, getScFileUrl(id, true), false, durationRef);
            if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
                Log.logErr(rt, "getDuration error;id=%s;fileType=%d", id, fileType);
                return 0;
            }
            return Math.toIntExact(durationRef.value);
        } catch (Exception e) {
            Log.logErr(Errno.ERROR, "getDuration exp;id=%s;fileType=%d", id, fileType);
            return 0;
        }
    }

    public static int addFile(ByteBuffer buffer, int aid, String id, int app) {
        int rt = Errno.ERROR;
        FileStgCli stgCli = new FileStgCli(Core.getFlow());
        if (!stgCli.init()) {
            Log.logErr(rt, "FileStgCli init err");
            return rt;
        }

        rt = stgCli.addFile(app, aid, id, buffer, false);
        if (rt != 0) {
            Log.logErr(rt, "add file error");
            return rt;
        }

        return rt;
    }

    /**
     * 截取视频首帧图
     *
     * @param id        视频id
     * @param aid       用户id
     * @param scResType 资源类型
     * @param fileType  文件类型
     * @param resList   资源列表，用来记录要保存的资源数据
     * @param info      上传信息，返回给前端
     * @return
     */
    public static int snapVideo(String id, int aid, int scResType, int fileType, List<ScResEntity> resList, Param info) {
        int rt;
        if (FileEx.Type.MP4 != fileType && FileEx.Type.MOV != fileType) { // 视频文件才需要截取首帧图
            return Errno.OK;
        }

        VideoSnapCli videoSnapCli = FaiCliFactory.createCli(VideoSnapCli.class, Core.getFlow());
        String path = getScFileUrl(id, true);
        Param videoInfo = new Param();
        rt = videoSnapCli.get(aid, path, 1, false, FileStgDef.App.SC_FILE, false, FileStgDef.DEFAULT_FOLDER_ID, "", videoInfo);
        if (rt != Errno.OK) {
            Log.logErr(rt, "videoSnapCli get error;aid=%d;id=%s,path=%s", aid, id, path);
        }

        String firstImgId = videoInfo.getString("id", "");
        Param idDef = FileStgDef.parseFileId(firstImgId);
        int firstImgType = FileEx.Type.JPEG;
        if (idDef != null && !idDef.isEmpty()) {
            firstImgType = idDef.getInt(FileStgDef.IdDef.TYPE);
        }

        info.setString("firstImgId", firstImgId);
        info.setInt("firstImgType", firstImgType);
        info.setString("firstImgUrl", getScFileUrl(firstImgId));

        // 保存资源
        ScResEntity entity = new ScResEntity();
        entity.setAid(Web.getFaiCid());
        entity.setResId(firstImgId);
        entity.setName(firstImgId + "." + FileEx.getImgSuffixByType(firstImgType));
        entity.setType(scResType);
        entity.setStatus(ScResDef.Status.TEMP);
        entity.setFileType(fileType);
        entity.setFileSize(0L); // 批量insert时，fileSize不能为null，给个默认值0

        // 和视频一起保存，需要和视频一样添加extra，不然接口会报错
        ScResExtraModal modal = new ScResExtraModal();
        modal.setDuration(0);
        entity.setExtraModal(modal);

        resList.add(entity);
        return rt;
    }

    public static String getScFileUrl(String id) {
        return getScFileUrl(id, false);
    }

    public static String getScFileUrl(String id, boolean isInternal) {
        id = FileStgDef.fixFileId(id);

        // 获取文件信息
        int fileType = -1;
        if (!id.isEmpty()) {
            Param idDef = FileStgDef.parseFileId(id);
            if (idDef != null && !idDef.isEmpty()) {
                fileType = idDef.getInt(FileStgDef.IdDef.TYPE);
            }
        }

        if (fileType == -1) {
            fileType = FileEx.Type.UNKNOWN;
        }

        // 如果是图片的话，鑫哥要求返回webp
        if (FileEx.isImgType(fileType)) {
            fileType = FileEx.Type.WEBP;
        }

        // 生成文件后缀
        String fileTypeFmt = FileEx.toFileFormat(fileType);
        if (!fileTypeFmt.isEmpty()) {
            fileTypeFmt = "." + fileTypeFmt;
        }

        // dep域名不可用
        int envModel = Web.getEnvMode() == Web.EnvMode.PRE ? Web.EnvMode.SVR : Web.getEnvMode();
        return isInternal ? "http://" + FaiFile.getInternalViewUrl(Web.getFaiCid(), FileStgDef.App.SC_FILE, 0, id, fileType, envModel) : getViewUrl(Web.getFaiCid(), FileStgDef.App.SC_FILE, 0, false, id) + fileTypeFmt;
    }

    // 由于架构没有提供方法可以传递域名，这里cv fai.fileupload.utils.FileUrlUtils.getViewUrl方法修改
    public static String getViewUrl(int aid, int app, int folderId, boolean tmp, String fileId) {
        try {
            StringBuilder sb = new StringBuilder(256);
            sb.append(ScFileStg.getScFileRoot(aid, FileStgDef.App.SC_FILE));
            sb.append("/");
            sb.append(FileStgDef.parseFileId(fileId).getInt(FileStgDef.IdDef.TYPE));
            sb.append("/");
            if (folderId > 0) {
                sb.append(folderId);
                sb.append("/");
            }
            sb.append(fileId);
            return sb.toString();
        } catch (Exception e) {
            Log.logErr(e, "getViewUrl err;aid=%d;app=%d;folderId=%d;fileId=%s;", aid, app, folderId, fileId);
        }
        return null;
    }

    /**
     * 文件上传（支持断点续传）
     *
     * <AUTHOR> 2025/5/9 17:17
     * @Update HLS 2025/5/9 17:17
     **/
    public static int advanceUploadFile4Sc(ServletContext context, HttpServletRequest request, int scResType, int aid, Param info) throws Exception {
        int rt = Errno.ERROR;
        long initTime = System.currentTimeMillis();
        long endTime = initTime;
        int flow = Core.getFlow();
        FormFile file = null;

        try {
            FormRequest freq = new FormRequest();
            // 因为为ajax上传 避免请求被拒绝
            freq.setSizeMax(FileUpload.Limit.SPLIT_MAX_SIZE);
            // 判断文件大小的任务根据totalSize判断
            // 所以设置为分片最大长度
            Param fileInfo = new Param();
            rt = freq.advanceAttachServlet(context, request, fileInfo);
            if (rt != Errno.OK) {
                if (rt == FormRequest.ErrnoAttach.FILE_SIZE_LIMIT) {
                    info.setInt(FileUpload.Info.LIMIT, FileUpload.Limit.SPLIT_MAX_SIZE);
                    return rt;
                }
                if (rt == FormRequest.ErrnoAttach.FILE_TYPE_INVALID) {
                    return rt;
                }
                return rt;
            }

            long totalSize = Parser.parseLong(fileInfo.getString("totalSize"), 0L);// 判断单个文件大小

            // 拿到文件
            String ctrl = request.getParameter("ctrl");
            if (ctrl == null || ctrl.isEmpty()) {
                ctrl = "filedata";
            }
            file = freq.getFile(ctrl);
            if (file == null) {
                rt = FileUpload.ErrnoUpload.FILE_CTRL_NOT_FOUND;
                Log.logErr(rt, "ctrl not found;flow=%d;aid=%d;ctrl=%s", flow, aid, ctrl);
                return rt;
            }

            String name = file.getFileName();
            if (name == null) {
                name = "";
            }
            info.setString(FileUpload.Info.NAME, name);

            ByteBuffer content = ByteBuffer.wrap(file.get());

            // 通过文件后缀名来判断类型
            String id = "";
            int fileType = FileEx.getFileType(name);

            String complete = fileInfo.getString("complete");
            String initSize = fileInfo.getString("initSize");
            boolean append;// 是否追加
            boolean tmpFile;// 是否保存在临时文件
            int app = FileStgDef.App.SC_FILE;
            String fileMd5 = fileInfo.getString("fileMd5");

            if (FileEx.isImgType(fileType) || FileEx.Type.SVG == fileType) {
                // 图片文件直接处理，不支持断点续传
                rt = uploadImg4Sc(content, name, aid, scResType, info);
                if (rt != Errno.OK) {
                    return rt;
                }
                id = info.getString(FileUpload.Info.ID, "");
            } else {
                // 非图片文件支持断点续传
                if ("true".equals(complete)) {
                    // 完成上传，从临时文件复制到正式文件
                    append = true;
                    id = FileStgDef.genFileId(app, fileType, false);
                    tmpFile = true;

                    FileStg fileStg = new FileStg(aid);
                    rt = fileStg.appendFolderFile(content, 0, app, fileMd5, tmpFile, append);
                    if (rt != Errno.OK) {
                        Log.logErr(rt, "append file in filestg err;flow=%d;aid=%d;", flow, aid);
                        return rt;
                    }

                    // 文件转正
                    FileStgCli stgCli = FaiCliFactory.createCli(FileStgCli.class, Core.getFlow());
                    rt = stgCli.copyFileByName(app, aid, 0, fileMd5, id, tmpFile);
                    if (rt != Errno.OK) {
                        Log.logErr(rt, "copy file from tmp error;flow=%d;aid=%d;", flow, aid);
                        return rt;
                    }

                    // 完成上传后，执行视频截帧和资源管理逻辑
                    rt = processCompletedFile(id, name, aid, scResType, fileType, totalSize, info);
                    if (rt != Errno.OK) {
                        return rt;
                    }
                } else {
                    // 继续追加分片到临时文件
                    append = true;
                    tmpFile = true;

                    // 只校验第一片
                    if (initSize != null && initSize.equals("0")) {
                        // 如果文件后缀为flv，判断flv格式是否正确
                        if (fileType == FileEx.Type.FLV && fileType != FileEx.parseFileType(content)) {
                            rt = FileUpload.ErrnoUpload.FILE_FLV_FORMAT_ERROR;
                            return rt;
                        }
                    }

                    FileStg fileStg = new FileStg(aid);
                    rt = fileStg.appendFolderFile(content, 0, app, fileMd5, tmpFile, append);
                    if (rt != Errno.OK) {
                        Log.logErr(rt, "append file to filestg err;flow=%d;aid=%d;", flow, aid);
                        return rt;
                    }

                    info.setString("status", complete);
                    info.setString("fileMd5", fileMd5);
                    return Errno.OK;
                }
            }

            info.setInt(FileUpload.Info.TYPE, fileType);
            info.setString(FileUpload.Info.ID, id);
            info.setString(FileUpload.Info.PATH, getScFileUrl(id));
            info.setLong(FileUpload.Info.CREATE_TIME, Calendar.getInstance().getTimeInMillis());

            return rt;
        } finally {
            endTime = System.currentTimeMillis();
            // 记录上传耗时统计
            Log.logDbg("advanceUploadFile4Sc耗时统计;flow=%d;aid=%d;size=%d;耗时=%d;rt=%d",
                    flow, aid, file == null ? 0 : file.getSize(), (endTime - initTime), rt);
        }
    }

    /**
     * 处理完成上传的文件（视频截帧和资源管理）
     */
    private static int processCompletedFile(String id, String name, int aid, int scResType, int fileType, long totalSize, Param info) {
        int rt;
        int flow = Core.getFlow();
        int app = FileStgDef.App.SC_FILE;

        List<ScResEntity> resList = new FaiList<>();

        // 视频文件需要截取首帧图保存
        rt = snapVideo(id, aid, scResType, fileType, resList, info);
        if (rt != Errno.OK) {
            Log.logErr(rt, "snap video error;flow=%d;aid=%d;id=%s;", flow, aid, id);
            return rt;
        }

        int duration = getDuration(aid, id, fileType);

        // 保存资源
        ScResCli scResCli = HdFaiClientProxyFactory.createProxy(ScResCli.class);
        ScResEntity entity = new ScResEntity();
        entity.setAid(aid);
        entity.setResId(id);
        entity.setName(name);
        entity.setType(scResType);
        entity.setStatus(ScResDef.Status.TEMP);
        entity.setFileType(fileType);
        entity.setFileSize(totalSize);
        if (duration > 0) {
            ScResExtraModal modal = new ScResExtraModal();
            modal.setDuration(duration);
            entity.setExtraModal(modal);
        }
        resList.add(entity);

        // 资源上次
        RemoteStandResult standResult = scResCli.batchAddRes(aid, resList);
        if (!standResult.isSuccess()) {
            Log.logErr(rt, "add file error;aid=%d;app=%d;id=%s;", aid, app, id);
            return rt;
        }

        info.setString(FileUpload.Info.NAME, name);
        info.setInt(FileUpload.Info.SIZE, (int) totalSize);
        info.setString(FileUpload.Info.SIZE + "Name", TsMisc.formatFileSizeToStandard((int) totalSize));
        info.setString(FileUpload.Info.ID, id);
        info.setInt("duration", duration);

        return rt;
    }

}