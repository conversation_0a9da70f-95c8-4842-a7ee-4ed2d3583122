<template>
  <div class="sc-proto-edit">
    <el-breadcrumb separator="/" class="mb-[30px]">
      <el-breadcrumb-item
        class="text-[18px] font-bold cursor-pointer"
        @click.native="goBack"
        >原型配置</el-breadcrumb-item
      >
      <el-breadcrumb-item class="text-[18px]">{{
        curTitle
      }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-form :model="protoInfo" :rules="rules" ref="protoForm">
      <el-form-item label="apiKey：" class="mb-[20px]">
        <el-input
          v-model="editConfig.apiKey"
          placeholder="请输入dify工作流对应的api_key"
          class="mr-[20px] !w-[300px]"
          :disabled="!!editId"
        ></el-input>
        <el-button
          type="primary"
          size="small"
          class="w-[100px]"
          @click="syncConfig"
          >同步配置</el-button
        >
        <EditApikey v-if="!!editId" :protoInfo="protoInfo" @editApiKey="editApiKey" />
        
      </el-form-item>
      <template v-if="showProtoContent">
        <!-- 名称 -->
        <el-form-item label="名称：" prop="name" class="mb-[20px]">
          <el-input
            v-model="protoInfo.name"
            minlength="1"
            maxlength="200"
            placeholder="请输入名称"
            class="!w-[300px]"
          ></el-input>
        </el-form-item>
        <!-- 原型表单 -->
        <FormFieldsGroup
          :protoInfo="protoInfo"
          :filedTypeList="confInfo.filedTypeList"
        />
        <!-- 素材组配置 -->
        <MaterialGroup
          :editType="editType"
          :protoInfo="protoInfo"
          :resFormList.sync="protoInfo.resFormList"
        />
        <!-- 视频类型-剪辑逻辑配置 -->
        <EditVideoMaterialGroup :protoInfo="protoInfo" v-if="showVideoGroup" />
        <!-- 图文类型 -->
        <EditImgTextMaterialGroup
          :protoInfo="protoInfo"
          :confInfo="confInfo"
          v-else
        />
        <div class="flex justify-center mt-[30px]">
          <el-button
            type="primary"
            class="w-[200px] sc-proto-edit-save-btn"
            @click="saveConfig"
            >保存</el-button
          >
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
import FormFieldsGroup from "./formFieldsGroup.vue";
import MaterialGroup from "./materialGroup.vue";
import EditVideoMaterialGroup from "./editVideoMaterialGroup.vue";
import EditImgTextMaterialGroup from "./editImgTextMaterialGroup.vue";
import EditApikey from "./editApikey.vue";
import { ScProductType } from "@/views/scPortal/config/index.js";
import {
  syncProto,
  setScProtoInfo,
  getConfInfo,
} from "@/views/scPortal/api/scProto.js";

export default {
  name: "ScProtoEdit",
  components: {
    FormFieldsGroup,
    MaterialGroup,
    EditVideoMaterialGroup,
    EditImgTextMaterialGroup,
    EditApikey,
  },
  props: {
    /**
     * 创建类型（创建的时候需要传）
     */
    createType: {
      type: Number,
      default: ScProductType.VIDEO,
    },
    /**
     * 编辑id（编辑的时候需要传）
     */
    editId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      ScProductType,
      editConfig: {
        apiKey: "",
      },
      /**
       * 配置信息
       */
      confInfo: {
        coverStyleList: [], // 封面图样式
        subgraphStyleList: [], // 次图样式
        filedTypeList: [], // 表单文本类型
      },
      apiKeyEditInfo: {
        _showEditApiKey: false,
        _apiKey: "",
      },
      /**
       * 表单验证规则
       */
      rules: {
        name: [
          {
            required: true,
            message: "请输入名称",
            trigger: ["blur", "change"],
          },
        ],
        "cover.styleList": [
          {
            required: true,
            message: "请选择封面图样式",
            trigger: ["blur", "change"],
          },
        ],
        "secondary.styleList": [
          {
            required: true,
            message: "请选择次图样式",
            trigger: ["blur", "change"],
          },
        ],
      },
      protoInfo: {},
    };
  },
  watch: {
    editId: {
      handler(newVal) {
        if (newVal) {
          // 编辑态
          this.getEditProtoInfo();
        }
      },
      immediate: true,
    },
    protoInfo: {
      handler() {
        // 当 protoInfo 变化时，更新校验
        this.$refs.protoForm.validate();
      },
      deep: true,
    },
  },
  created() {
    console.log('created')
    this.initData();
  },
  computed: {
    curTitle() {
      return `${this.editId ? "编辑" : "添加"}${
        this.editType === ScProductType.VIDEO ? "视频" : "图文"
      }原型配置`;
    },
    showVideoGroup() {
      return this.editType === ScProductType.VIDEO;
    },
    showProtoContent() {
      return Object.keys(this.protoInfo).length > 0;
    },
    // 编辑类型（编辑时直接获取原型类型，新建时根据createType）
    editType() {
      return this.editId ? this.protoInfo.type : this.createType;
    },
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取分类配置信息
      this.getConfInfoList();
    },
    /**
     * 通过输入的apikey同步配置
     */
    syncConfig() {
      if (this.editId) {
        // 编辑时，同步配置需要传递apikey和id
        this.syncProtoInfo({
          id: this.editId,
          apiKey: this.editConfig.apiKey.trim(),
        });
      } else {
        // 创建时，同步配置需要传递type和apikey
        this.syncProtoInfo({
          type: this.createType,
          apiKey: this.editConfig.apiKey.trim(),
        });
      }
    },
    /**
     * 保存配置
     */
    saveConfig() {
      // 提交表单
      this.$refs.protoForm.validate((valid) => {
        if (valid) {
          // this.$message.success('验证通过！');
          // 提交逻辑
          setScProtoInfo(JSON.stringify(this.protoInfo)).then((res) => {
            if (res.success) {
              this.$message.success("保存成功！");
              this.goBack();
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          this.$message.error("请检查填写内容！");
        }
      });
    },
    /**
     * 返回列表
     */
    goBack() {
      this.$emit("changeComponent", "scProtoList", {
        isReloadList: true,
      });
    },
    /**
     * 同步配置
     * @param {Object} params 同步参数
     * @param {Number} params.type 原型类型(创建时传)
     * @param {String} params.apiKey apiKey(创建时传，编辑时修改apikey传新的apikey)
     * @param {Number} params.id 原型id(编辑时传)
     */
    syncProtoInfo(params) {
      syncProto(params).then((res) => {
        if (res.success) {
          let newData = res.data;
          if (!newData?.resFormList?.length) {
            // 同步后如果素材组没数据，则将原先的素材组数据赋值给新的数据
            if (this.protoInfo?.resFormList?.length) {
              Object.assign(newData, {
                resFormList: this.protoInfo.resFormList
              })
            } else {
              newData.resFormList = [];
            }
          }
          this.protoInfo = newData;

          if (this.editId) {
            this.editConfig.apiKey = this.protoInfo.apiKey;
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    /**
     * 获取配置信息
     */
    getConfInfoList() {
      getConfInfo().then((res) => {
        if (res.success) {
          this.confInfo = res.data;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    /**
     * 获取编辑原型信息
     */
    getEditProtoInfo() {
      this.syncProtoInfo({
        id: this.editId,
      });
    },
    showEditApiKey() {
      this.apiKeyEditInfo._showEditApiKey = true;
      this.apiKeyEditInfo._apiKey = this.protoInfo.apiKey;
    },
    /**
     * 修改apikey(编辑态才有)
     * @param apiKey 新的apikey
     */
    editApiKey(apiKey) {
      // 重新获取配置
      this.syncProtoInfo({
        id: this.editId,
        apiKey,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.sc-proto-edit-save-btn {
  ::v-deep span {
    float: none;
  }
}
</style>
