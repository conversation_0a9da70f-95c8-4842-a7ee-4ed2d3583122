<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.util.regex.Matcher"%>
<%@ page import="java.util.regex.Pattern"%>
<%@ page import="fai.cli.PreSaleUtilCli"%>

<%!
/* 公共方法 */
	private FaiList<Param> getColList(FaiList<String> colKeyList)throws Exception{
		FaiList<Param> colList = new FaiList<Param>();
		for(String key:colKeyList){
			Param param = new Param();
			param.setString(MSOfficeConverter.Col.KEY,key);
			param.setInt( MSOfficeConverter.Col.WIDTH,20 );
			colList.add(param);
		}
		return colList;
	}
	
	private static void dealBeforeConvert ( HttpServletRequest request, HttpServletResponse response, JspWriter out , String fileName , FaiList<Param> optionList )throws Exception{
		out.clear();
		String agent = request.getHeader("User-Agent");
		agent = agent == null ? "" : agent.toUpperCase();
		response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent,fileName) + "\""); 
		response.setContentType("application/vnd.ms-excel");
		MSOfficeConverter.excelConverter(response.getOutputStream(), optionList);
		//out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
	}
/* 公共方法 */

	//领取资源数据。注册来源，注册用途，注册时间，领取时间，首次创建游戏时间
	private String getResourceDetail(HttpServletRequest request, HttpServletResponse response, JspWriter out ) throws Exception{
		/*查询条件*/
		String startDate = Parser.parseString(request.getParameter("receiveDateBeg"), "2018-05-01")+" 00:00:00";
		String endDate = Parser.parseString(request.getParameter("receiveDateEnd"), "2018-05-30")+" 23:59:59";
		String sacct = Parser.parseString(request.getParameter("staff_sacct"), "all");
		
		FaiList<Param> dataList = new FaiList<Param>();//存最终结果集
		
		//获取注册来源信息
		FaiList<Param> taList = new FaiList<Param>();
		//获取全部员工信息
		Staff oStaff = (Staff) Core.getCorpKit(Web.getFaiCid(), Kid.STAFF);
		FaiList<Param> staffList = oStaff.getStaffList();
		
		Dao ossBsDao = WebOss.getOssBsDaoNew();
		Dao bssMainDao = WebOss.getBssMainDao();
		try{
			//获取aid和对应的领取时间
			String ossSql = "select aid, sacct, date_format(receiveTime, '%Y-%m-%d %H:%i:%s') receiveTime"
							+ " from hdSaleRecord "
							+ " where receiveTime >\'" +  startDate + "\' and receiveTime<\'"+endDate+"\'";
			if(!"all".equals(sacct)){
				ossSql += " and sacct='" + sacct + "'";
			}
			dataList = ossBsDao.executeQuery(ossSql);
			if(dataList == null){
				return "{\"success\":false,\"msg\":\"暂无领取记录!\"}";
			}

			FaiList<Integer> aidList = new FaiList<Integer>();
			for(Param item : dataList){
				aidList.add(item.getInt("aid"));
			}
			Dao.SelectArg sel = new Dao.SelectArg();
			sel.field = "aid, ta, companyGoal, from_unixtime(regTime, '%Y-%m-%d %H:%i:%s') as regTime";
			sel.table ="acctStatus";
			SearchArg searchArg = new SearchArg();
			searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
			sel.searchArg = searchArg;
			FaiList<Param> acctList = bssMainDao.select(sel);
			Param acctParams = new Param(true);
			for(Param item: acctList){
				acctParams.setParam(item.getInt("aid",0)+"", item);
			}
			for(Param item : dataList){
				Param acctInfo = acctParams.getParam(item.getInt("aid")+"", new Param());
				int ta = acctInfo.getInt("ta", 0);
				int companyGoal = acctInfo.getInt("companyGoal", 0);
				String regTime = acctInfo.getString("regTime", "");
				
				item.setInt("ta",ta);
				item.setInt("companyGoal",companyGoal);
				item.setString("regTime",regTime);
			}
			
			sel = new Dao.SelectArg();
			sel.field= "aid, from_unixtime(min(createTime), '%Y-%m-%d %H:%i:%s') as createTime";
			sel.table="hdTemplateGame";
			sel.group="aid";
			searchArg = new SearchArg();
			searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
			sel.searchArg = searchArg;
			FaiList<Param> gameList = bssMainDao.select(sel);
			Param gameParams = new Param(true);
			for(Param item: gameList){
				gameParams.setParam(item.getInt("aid",0)+"", item);
			}
			
			for(Param item : dataList){
				Param gameInfo = gameParams.getParam(item.getInt("aid")+"", new Param());
				String createTime = gameInfo.getString("createTime", "");
				
				item.setString("createTime", createTime);
			}
			String taSql = "select * from ta";
			taList = bssMainDao.executeQuery(taSql);
		}finally{
			ossBsDao.close();
			bssMainDao.close();
		}
		FaiList<String> colKeyList = new FaiList<String>();
		colKeyList.add("aid");
		colKeyList.add("sacct");
		colKeyList.add("ta");
		colKeyList.add("companyGoal");
		colKeyList.add("regTime");
		colKeyList.add("receiveTime");
		colKeyList.add("createTime");
		
		FaiList<Param> colList = getColList(colKeyList);
		FaiList<Param> rowList = new FaiList<Param>();               //要导出Excel文件的信息
		Param p = new Param();
		p.setString("aid" , "AID");
		p.setString("sacct" , "销售");
		p.setString("ta" , "注册来源");
		p.setString("companyGoal" , "注册用途");
		p.setString("regTime" , "注册时间");
		p.setString("receiveTime" , "领取时间");
		p.setString("createTime" , "首次创建游戏时间");
		rowList.add(p);
		
		
		//数据翻译
		for(Param dataInfo : dataList){
			// 获取员工的姓名
			sacct = dataInfo.getString("sacct");
			Param staffInfo = Misc.getFirst(staffList, StaffDef.Info.SACCT, sacct);
			if(staffInfo == null) {
				staffInfo = new Param();
			}
			String sacctName = staffInfo.getString(StaffDef.Info.NAME, "");
			
			//数据翻译
			int ta = dataInfo.getInt("ta");
			int companyGoal = dataInfo.getInt("companyGoal");
			String regTime = dataInfo.getString("regTime");
			String receiveTime = dataInfo.getString("receiveTime");
			String createTime = dataInfo.getString("createTime");
			
			
			//excel每行数据
			Param data = new Param();
			data.setInt("aid", dataInfo.getInt("aid"));
			data.setString("sacct", sacctName + "  (" + sacct + ")");
			data.setString("ta", PreSaleHdDef.getTaName(taList, ta));
			data.setString("companyGoal", BssStatDef.CompanyGoal.getCorpGoalName(companyGoal));
			data.setString("regTime", regTime);
			data.setString("receiveTime", receiveTime);
			data.setString("createTime", createTime);
			
			rowList.add(data);
		}
		FaiList<Param> optionList = new FaiList<Param>();
		Param option = new Param();
		int outputCount = rowList.size();
		option.setString( MSOfficeConverter.Option.SHEET_NAME,"1-3" );
		option.setList( MSOfficeConverter.Option.COL_LIST,colList );
		option.setList( MSOfficeConverter.Option.ROW_LIST,rowList );
		optionList.add( option );
		String fileName = "销售系统资源明细" + startDate + "~" + endDate + ".xls";
		dealBeforeConvert( request,response,out,fileName,optionList );
		return "suc" ;
	}

%>

<%
	String output = "";
	try{
		if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false) && Session.getSid()!=121){
			out.print("{\"success\":false, \"msg\":\"没有权限\"}");
			return;
		}
		
		String cmd = request.getParameter("cmd");
		if (cmd == null) {
			return;
		}else if (cmd.equals("getResourceDetail")) {
			output = getResourceDetail(request,response,out);
		}
		
	}catch (Exception exp){
		Log.logErr(exp);
		output = WebOss.checkAjaxException(exp);
	}
	out.print(output);
%>



	