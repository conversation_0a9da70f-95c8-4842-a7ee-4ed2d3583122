(function(){'use strict';var script = {
  name: "hdClearAsset",
  data() {
    return {
      form: {
        clearAsset: {
          aid: "",
          giftType: ""
        },
        clearPartAsset: {
          aid: "",
          giftType: "",
          clearPartNum: 0
        }
      },
      giftList: [],
      tableData: []
    };
  },
  created() {
    this.getGiftTypeList();
    this.getDeleteFlowList();
  },
  methods: {
    handleClearAssetClick() {
      const { aid, giftType } = this.form.clearAsset;
      if (!aid || !giftType)
        return this.$message.error("参数错误");
      this.clearAsset(
        `/api/gift/clearAsset?aid=${aid}&isClearPart=false&giftType=${giftType}`
      );
    },
    handleClearPartAssetClick() {
      const { aid, giftType, clearPartNum } = this.form.clearPartAsset;
      if (!aid || !giftType)
        return this.$message.error("参数错误");
      if (clearPartNum <= 0)
        return this.$message.error("清空数量需要大于0");
      this.clearAsset(
        `/api/gift/clearAsset?aid=${aid}&giftType=${giftType}&isClearPart=true&clearPartNum=${clearPartNum}`
      );
    },
    request(opts) {
      return new Promise((resolve, reject) => {
        $.ajax(opts).then((res) => {
          try {
            res = JSON.parse(res);
          } catch (err) {
          }
          const { success, msg } = res;
          if (!success)
            return reject(msg);
          resolve(res);
        }).fail(() => {
          reject();
        });
      }).catch((msg = "服务器繁忙，请稍后重试") => {
        this.$message.error(msg);
        return Promise.reject(msg);
      });
    },
    getGiftTypeList() {
      this.request({
        type: "get",
        url: "/api/gift/getGiftTypeList"
      }).then(({ data }) => {
        const { giftListBak, jdgiftListBak } = data;
        this.giftList = [
          ...giftListBak.map((item) => {
            const type = +Object.keys(item)[0];
            return { type, name: item[type] };
          }),
          ...jdgiftListBak.map((item) => {
            const type = +Object.keys(item)[0];
            return { type, name: item[type] };
          })
        ];
      });
    },
    clearAsset(url) {
      this.$confirm("确定要清空资产？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.request({
          type: "post",
          url
        }).then(() => {
          this.getDeleteFlowList();
          this.$message({
            message: "操作成功",
            type: "success"
          });
        });
      });
    },
    getDeleteFlowList() {
      this.request({
        type: "get",
        url: "/api/gift/getDeleteFlowList"
      }).then(({ list }) => {
        this.tableData = list.map((item) => {
          const { aid, createTime, isAll, num, staffLabel, typeBname } = item;
          return {
            action: `清空${isAll ? "全部" : "部分"}礼品：aid${aid}`,
            details: `${staffLabel} 清空礼品：${typeBname}`,
            createTime: moment(createTime).format("YYYY年MM月DD日 HH:mm:ss"),
            specifics: `清空数量：${num}`
          };
        });
      });
    }
  }
};function normalizeComponent(template, style, script, scopeId, isFunctionalTemplate, moduleIdentifier
/* server only */
, shadowMode, createInjector, createInjectorSSR, createInjectorShadow) {
  if (typeof shadowMode !== 'boolean') {
    createInjectorSSR = createInjector;
    createInjector = shadowMode;
    shadowMode = false;
  } // Vue.extend constructor export interop.


  var options = typeof script === 'function' ? script.options : script; // render functions

  if (template && template.render) {
    options.render = template.render;
    options.staticRenderFns = template.staticRenderFns;
    options._compiled = true; // functional template

    if (isFunctionalTemplate) {
      options.functional = true;
    }
  } // scopedId


  if (scopeId) {
    options._scopeId = scopeId;
  }

  var hook;

  if (moduleIdentifier) {
    // server build
    hook = function hook(context) {
      // 2.3 injection
      context = context || // cached call
      this.$vnode && this.$vnode.ssrContext || // stateful
      this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional
      // 2.2 with runInNewContext: true

      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__;
      } // inject component styles


      if (style) {
        style.call(this, createInjectorSSR(context));
      } // register component module identifier for async chunk inference


      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier);
      }
    }; // used by ssr in case component is cached and beforeCreate
    // never gets called


    options._ssrRegister = hook;
  } else if (style) {
    hook = shadowMode ? function (context) {
      style.call(this, createInjectorShadow(context, this.$root.$options.shadowRoot));
    } : function (context) {
      style.call(this, createInjector(context));
    };
  }

  if (hook) {
    if (options.functional) {
      // register for functional component in vue file
      var originalRender = options.render;

      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context);
        return originalRender(h, context);
      };
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate;
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook];
    }
  }

  return script;
}

var normalizeComponent_1 = normalizeComponent;var isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());
function createInjector(context) {
  return function (id, style) {
    return addStyle(id, style);
  };
}
var HEAD;
var styles = {};

function addStyle(id, css) {
  var group = isOldIE ? css.media || 'default' : id;
  var style = styles[group] || (styles[group] = {
    ids: new Set(),
    styles: []
  });

  if (!style.ids.has(id)) {
    style.ids.add(id);
    var code = css.source;

    if (css.map) {
      // https://developer.chrome.com/devtools/docs/javascript-debugging
      // this makes source maps inside style tags work properly in Chrome
      code += '\n/*# sourceURL=' + css.map.sources[0] + ' */'; // http://stackoverflow.com/a/26603875

      code += '\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(css.map)))) + ' */';
    }

    if (!style.element) {
      style.element = document.createElement('style');
      style.element.type = 'text/css';
      if (css.media) style.element.setAttribute('media', css.media);

      if (HEAD === undefined) {
        HEAD = document.head || document.getElementsByTagName('head')[0];
      }

      HEAD.appendChild(style.element);
    }

    if ('styleSheet' in style.element) {
      style.styles.push(code);
      style.element.styleSheet.cssText = style.styles.filter(Boolean).join('\n');
    } else {
      var index = style.ids.size - 1;
      var textNode = document.createTextNode(code);
      var nodes = style.element.childNodes;
      if (nodes[index]) style.element.removeChild(nodes[index]);
      if (nodes.length) style.element.insertBefore(textNode, nodes[index]);else style.element.appendChild(textNode);
    }
  }
}

var browser = createInjector;/* script */
const __vue_script__ = script;

/* template */
var __vue_render__ = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"hdClearAsset",attrs:{"id":"hdClearAsset"}},[_c('h3',[_vm._v("清空第三方礼品资产（话费和京东e卡）")]),_vm._v(" "),_c('el-form',{attrs:{"inline":true,"model":_vm.form}},[_c('el-form-item',{attrs:{"label":"aid："}},[_c('el-input',{attrs:{"placeholder":"填入aid","size":"small"},model:{value:(_vm.form.clearAsset.aid),callback:function ($$v) {_vm.$set(_vm.form.clearAsset, "aid", $$v);},expression:"form.clearAsset.aid"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"礼品类型："}},[_c('el-select',{attrs:{"filterable":"","clearable":"","placeholder":"选择礼品类型","size":"small"},model:{value:(_vm.form.clearAsset.giftType),callback:function ($$v) {_vm.$set(_vm.form.clearAsset, "giftType", $$v);},expression:"form.clearAsset.giftType"}},_vm._l((_vm.giftList),function(item){return _c('el-option',{key:item.type,attrs:{"label":item.name,"value":item.type}})}),1)],1),_vm._v(" "),_c('el-form-item',[_c('el-button',{attrs:{"type":"primary","size":"small"},on:{"click":_vm.handleClearAssetClick}},[_vm._v("清空")])],1)],1),_vm._v(" "),_c('el-divider',{staticClass:"hr"}),_vm._v(" "),_c('h3',[_vm._v("清空部分第三方礼品资产（话费和京东e卡以及实物礼品件）")]),_vm._v(" "),_c('el-form',{attrs:{"inline":true,"model":_vm.form}},[_c('el-form-item',{attrs:{"label":"aid："}},[_c('el-input',{attrs:{"placeholder":"填入aid","size":"small"},model:{value:(_vm.form.clearPartAsset.aid),callback:function ($$v) {_vm.$set(_vm.form.clearPartAsset, "aid", $$v);},expression:"form.clearPartAsset.aid"}})],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"礼品类型："}},[_c('el-select',{attrs:{"filterable":"","clearable":"","placeholder":"选择礼品类型","size":"small"},model:{value:(_vm.form.clearPartAsset.giftType),callback:function ($$v) {_vm.$set(_vm.form.clearPartAsset, "giftType", $$v);},expression:"form.clearPartAsset.giftType"}},_vm._l((_vm.giftList),function(item,index){return _c('el-option',{key:index,attrs:{"label":item.name,"value":item.type}})}),1)],1),_vm._v(" "),_c('el-form-item',{attrs:{"label":"扣除数量："}},[_c('el-input-number',{attrs:{"placeholder":"扣除数量","size":"small","min":0},model:{value:(_vm.form.clearPartAsset.clearPartNum),callback:function ($$v) {_vm.$set(_vm.form.clearPartAsset, "clearPartNum", $$v);},expression:"form.clearPartAsset.clearPartNum"}})],1),_vm._v(" "),_c('el-form-item',[_c('el-button',{attrs:{"type":"primary","size":"small"},on:{"click":_vm.handleClearPartAssetClick}},[_vm._v("清空")])],1)],1),_vm._v(" "),_c('el-divider',{staticClass:"hr"}),_vm._v(" "),_c('h3',[_vm._v("礼品清空日志")]),_vm._v(" "),_c('el-table',{staticStyle:{"width":"100%"},attrs:{"data":_vm.tableData,"stripe":"","height":_vm.tableData.length ? 500 : 'auto'}},[_c('el-table-column',{attrs:{"prop":"action","min-width":200,"label":"操作行为"}}),_vm._v(" "),_c('el-table-column',{attrs:{"prop":"details","min-width":370,"label":"操作详情"}}),_vm._v(" "),_c('el-table-column',{attrs:{"prop":"createTime","min-width":200,"label":"操作时间"}}),_vm._v(" "),_c('el-table-column',{attrs:{"prop":"specifics","min-width":150,"label":"明细"}})],1)],1)};
var __vue_staticRenderFns__ = [];

  /* style */
  const __vue_inject_styles__ = function (inject) {
    if (!inject) return
    inject("data-v-3cc4a6ac_0", { source: ".hdClearAsset[data-v-3cc4a6ac]{margin:20px}.hdClearAsset h3[data-v-3cc4a6ac]{color:#535353}.hdClearAsset .hr[data-v-3cc4a6ac]{margin-top:0}", map: undefined, media: undefined });

  };
  /* scoped */
  const __vue_scope_id__ = "data-v-3cc4a6ac";
  /* module identifier */
  const __vue_module_identifier__ = undefined;
  /* functional template */
  const __vue_is_functional_template__ = false;
  /* style inject SSR */
  
  /* style inject shadow dom */
  

  
  const __vue_component__ = /*#__PURE__*/normalizeComponent_1(
    { render: __vue_render__, staticRenderFns: __vue_staticRenderFns__ },
    __vue_inject_styles__,
    __vue_script__,
    __vue_scope_id__,
    __vue_is_functional_template__,
    __vue_module_identifier__,
    false,
    browser,
    undefined,
    undefined
  );new Vue({ render: (h) => h(__vue_component__) }).$mount('#app');})();