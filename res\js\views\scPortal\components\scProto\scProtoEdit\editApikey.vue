<template>
  <div class="inline-block ml-[10px]">
    <el-button
      class="w-[100px]"
      type="default"
      size="small"
      slot="reference"
      @click="showEditApiKey"
      >修改apikey</el-button
    >
    <el-dialog
      title="修改apikey"
      :visible.sync="apiKeyEditInfo._showEditApiKey"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="p-[20px]">  
        <el-input v-model="apiKeyEditInfo._apiKey" placeholder="请输入apikey" class="!w-full" />
        <p>修改apikey确认保存后，当前原型将直接替换为最新的dify工作流，请谨慎操作！</p>
        <div class="flex justify-end mt-[20px]">
          <el-button type="default" size="small" @click="apiKeyEditInfo._showEditApiKey = false">取消</el-button>
          <el-button type="primary" size="small" @click="editApiKey">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "EditApikey",
  props: {
    protoInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      apiKeyEditInfo: {
        _showEditApiKey: false, // 是否显示修改apikey弹窗
        _apiKey: "", // 修改的apikey
      },
    };
  },
  methods: {
    showEditApiKey() {
      this.apiKeyEditInfo._showEditApiKey = true;
      this.apiKeyEditInfo._apiKey = this.protoInfo.apiKey;
    },
    editApiKey() {
      this.apiKeyEditInfo._showEditApiKey = false;
      if (this.apiKeyEditInfo._apiKey !== this.protoInfo.apiKey) {
        // 修改了apikey
        this.$emit("editApiKey", this.apiKeyEditInfo._apiKey);
      }
    },
  },
};
</script>
