<template>
  <div class="h-full overflow-auto p-[0_30px] pb-[100px]">
    <keep-alive include="ScTemplateList">
      <component
        :is="component"
        @changeComponent="changeComponent"
        :editInfo="editInfo"
        :createType="createType"
      />
    </keep-alive>
  </div>
</template>

<script>
import ScTemplateList from "./scTemplateList/index.vue";
import ScTemplateEdit from "./scTemplateEdit/index.vue";
import { ScProductType } from "@/views/scPortal/config/index.js";

export default {
  name: "ScTemplate",
  components: {
    ScTemplateList,
    ScTemplateEdit,
  },
  data() {
    return {
      component: "ScTemplateList",
      editInfo: null,
      createType: ScProductType.VIDEO,
    };
  },
  created() {
    this.initConfInfo();
    this.parseUrlParams();
  },
  methods: {
    /**
     * 初始化配置信息和域名信息
     */
    initConfInfo() {
      this.$store.dispatch("scPortal/getTemplateConfInfo").catch((error) => {
        this.$message.error(error);
      });
      this.$store.dispatch("scPortal/getDomainInfo").catch((error) => {
        this.$message.error(error);
      });
      this.$store.dispatch("scPortal/getStaffInfo").catch((error) => {
        this.$message.error(error);
      });
    },
    changeComponent(
      component,
      { editInfo = null, createType = ScProductType.VIDEO } = {}
    ) {
      this.component = component;
      this.editInfo = editInfo;
      this.createType = createType;
    },
    /**
     * 解析 URL 参数并处理
     */
    parseUrlParams() {
      // 方法1：使用 Vue Router 获取查询参数
      if (this.$route && this.$route.query) {
        const { id, protoId } = this.$route.query;
        if (id && protoId) {
          // 如果有这两个参数，直接跳转到编辑页面
          this.changeComponent("ScTemplateEdit", {
            editInfo: {
              id: parseInt(id),
              protoId: parseInt(protoId),
            },
          });
        }
      }
    },
  },
};
</script>

<style lang="scss">
@import "../../styles/elementui.scss";
</style>
