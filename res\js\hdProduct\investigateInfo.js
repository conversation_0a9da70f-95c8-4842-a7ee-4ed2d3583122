( () => {
    let invastiGateInfo = new Vue({
        el : "#investigateInfo",
        data : {
            isLoading : false,
            pageInfo: {
                page:1,
                pageSize : 10,
                total : 1,
                pageArray :[10,50,100,500],
            },
            tableData : [],
            formQuery:{
                aid:"",
                startTime:"",
                endTime:"",
                isPayVersion:0,
            },
            options:[
                {
                    value:0,
                    label:"全部"
                },{
                    value:1,
                    label:"是"
                },{
                    value:2,
                    label:"不是"
                }
            ],
            type: exposeParam.coupType
        },
        methods:{
            getPageInfo : function(){
                this.isLoading = true;
                let arg = {
                    "cmd" : "getResearchQuestions4Oss",
                    "type": exposeParam.coupType,
                }
                Object.assign(arg,this.pageInfo,this.formQuery);
                delete arg.pageArray;
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {
                    let data = response.data;
                    this.isLoading = false;
                    if( data.success ){
                        let showArray = JSON.parse(data.data);
                        if( exposeParam.coupType == 3 ){
                            fixArray(showArray);
                        }
                        console.log(showArray);
                        if( showArray ){
                            this.tableData = showArray;
                            this.pageInfo.total = data.total;
                        }
                    }else{
                        this.$message({
                            type: 'warning',
                            message: data.msg
                        });
                    }                  
                }, response => {
                    this.isLoading = false;
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },
            changeSize : function(val){
                this.pageInfo.pageSize = val;
                this.getPageInfo();
            },
            chagePageInfo : function(val){
                this.pageInfo.page = val;
                this.getPageInfo();
            },
            searchInvasi : function(formName){
                this.getPageInfo();
            },
            downLoadInfo : () => {
                let arg = {
                    "cmd" : "exportResearch",
                    "type": exposeParam.coupType,
                };
                Object.assign(arg,invastiGateInfo.formQuery);
                arg = Fai.tool.parseJsonToUrlParam(arg,true);
                window.location.href =  '/ajax/hdProduct_h.jsp'+ arg;
            }
        }
    });
    invastiGateInfo.getPageInfo();
    let fixArray = function(array){
        if( !array )return;
        array.forEach( (mainData,index) => {
            // mainData.answerArray = [];
            // let answer = JSON.parse(mainData.answer);
            // let chineseNum = ["一","二","三","四","五","六","七","八"];
            // [1,2,3].forEach( (data,theIndex) => {
            //     let key = 'attenInfo'+data;
            //     let question = {
            //         q:"第一题题目（"+data+")",
            //         a:mainData[key]
            //     }
            //     mainData.answerArray.push(question);
            // });
            // let nowInto = 1;
            // ["profession1","sexInfo1","ageInfo1","studyInfo1","accountBelong1","assitance1","companyName1"].forEach( (data,theIndex) =>{
            //     let chinessNumNow = chineseNum[nowInto++];
            //     let answer = mainData[data];
            //     let question = {
            //         q:"第"+chinessNumNow+"题题目",
            //         a:answer ? answer : "（空）"
            //     }
            //     mainData.answerArray.push(question);
            // } )
            // mainData.useTime /= 1000; 
            // mainData.useTime += "s";
            // mainData.isPayVersion = mainData.isPayVersion ? "是" :"不是";
            mainData.assitance = mainData.assitance ? mainData.assitance : "(空)";
            mainData.company = mainData.company ? mainData.company : "(空)";
        })
    }
} )()