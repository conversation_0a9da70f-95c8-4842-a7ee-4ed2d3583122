<template>
  <div class="sc-material-list">
    <el-tabs v-model="activeName">
      <el-tab-pane
        label="背景音乐"
        :name="String(ScMaterialType.BG_MUSIC)"
      ></el-tab-pane>
      <el-tab-pane
        label="配音"
        :name="String(ScMaterialType.DUBBING)"
      ></el-tab-pane>
    </el-tabs>
    <div class="mb-[20px] sc-list-header">
      <el-button
        type="primary"
        @click="addMaterial(ScMaterialType.DUBBING)"
        v-if="activeName === String(ScMaterialType.DUBBING)"
        >添加配音</el-button
      >
      <el-button
        type="primary"
        @click="addMaterial(ScMaterialType.BG_MUSIC)"
        v-else
        >添加背景音乐</el-button
      >
    </div>
    <div class="flex mb-[20px] sc-list-filter">
      <el-input
        class="mr-[30px] sc-list-input !w-[240px]"
        v-model.trim="filterData.name"
        placeholder="请输入搜索名称"
        size="small"
        :clearable="true"
        @keyup.enter.native="searchList"
        @clear="searchList"
      />
      <!-- 分类 -->
      <div>
        <span class="mr-[5px] text-[14px]">分类:</span>
        <el-select
          size="small"
          v-model="filterData.categoryId"
          placeholder="请选择分类"
          class="mr-[30px] sc-list-select !w-[160px]"
        >
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>
      <!-- 配音-音色类型 -->
      <div v-show="activeName === String(ScMaterialType.DUBBING)">
        <span class="mr-[5px] text-[14px]">音色类型:</span>
        <el-select
          v-model="filterData.extraType"
          class="mr-[30px] sc-list-select !w-[160px]"
          placeholder="请选择分类"
          size="small"
        >
          <el-option
            v-for="item in extraTypeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </div>

      <el-button size="small" type="primary" @click="searchList"
        >搜索
      </el-button>
    </div>
    <el-table :data="dataList">
      <el-table-column label="ID" prop="id" width="80" />
      <el-table-column label="名称" prop="name" />
      <el-table-column
        label="音色ID"
        prop="voiceType"
        v-if="activeName === String(ScMaterialType.DUBBING)"
        width="200"
      />
      <el-table-column
        label="音色类型"
        prop="extraType"
        v-if="activeName === String(ScMaterialType.DUBBING)"
        width="180"
      >
        <template slot-scope="scope">
          {{
            scope.row.extraType ? DubbingExtraType[scope.row.extraType] : "-"
          }}
        </template>
      </el-table-column>
      <el-table-column label="分类" prop="categoryName" width="100" />
      <el-table-column
        label="屏蔽自动推荐"
        prop="blockAuto"
        width="120"
        v-if="activeName === String(ScMaterialType.DUBBING)"
      >
        <template slot-scope="scope">
          <span
            :class="scope.row.blockAuto ? 'text-[#409EFF]' : 'text-[#909399]'"
            >{{ scope.row.blockAuto ? "是" : "否" }}</span
          >
        </template>
      </el-table-column>
      <el-table-column label="封面" width="150">
        <template slot-scope="scope">
          <img
            :src="getImgUrl(scope.row)"
            alt="封面"
            class="w-[60px] h-[60px] cursor-pointer"
            v-if="scope.row.cover"
            @click="handleShowImg(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="音频内容" prop="content" width="300">
        <template slot-scope="scope">
          <audio class="!w-[250px]" :src="getAudioUrl(scope.row)" controls />
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="mt-[20px] flex justify-end"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageConfig.pageNo"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageConfig.pageLimit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageConfig.total"
    >
    </el-pagination>
    <previewImg
      :img-url="getImgUrl(showImgDialogData)"
      v-if="showImgDialog"
      @close="showImgDialog = false"
    />
  </div>
</template>

<script>
import previewImg from "@/views/scPortal/components/common/previewImg.vue";
import {
  DubbingExtraType,
  ScMaterialType,
} from "@/views/scPortal/config/index.js";
import {
  deleteMaterial,
  getCategoryList,
  getSourceList,
} from "@/views/scPortal/api/scMaterial.js";
import { getResUrl } from "@/views/scPortal/utils/index.js";
import { assign } from "lodash";

export default {
  name: "ScMaterialList",
  components: {
    previewImg,
  },
  props: {
    isReloadList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      DubbingExtraType,
      activeName: String(ScMaterialType.BG_MUSIC),
      ScMaterialType,
      filterOptions: [
        {
          value: "id",
          label: "ID",
        },
        {
          value: "name",
          label: "名称",
        },
        {
          value: "category",
          label: "分类",
        },
      ],
      filterData: {
        name: "", // 搜索名称
        categoryId: 0, // 分类id
        type: ScMaterialType.BG_MUSIC,
        extraType: -1,
      },
      dataList: [],
      pageConfig: {
        pageNo: 1,
        pageLimit: 10,
        total: 0,
      },
      categoryList: [], // 分类列表
      showImgDialogData: {}, // 显示图片对话框数据
      showImgDialog: false, // 显示图片对话框
      extraTypeList: [
        {
          id: -1,
          name: "全部",
        },
        {
          id: 1,
          name: "腾讯-大模型音色",
        },
        {
          id: 2,
          name: "腾讯-精品音色",
        },
        {
          id: 3,
          name: "火山引擎",
        },
      ],
    };
  },
  watch: {
    activeName: {
      handler(val) {
        this.resetFilterData();
        this.searchList();
        this.getCategoryList();
      },
      immediate: true,
    },
  },
  created() {
    // this.getDataList();
  },
  activated() {
    if (this.isReloadList) {
      this.searchList();
    }
  },
  methods: {
    resetFilterData() {
      Object,
        assign(this.filterData, {
          name: "",
          categoryId: 0,
        });
    },
    searchList() {
      this.pageConfig.pageNo = 1;
      this.getDataList();
    },
    getDataList() {
      let newParams = {
        name: this.filterData.name,
        type: Number(this.activeName),
        categoryId: this.filterData.categoryId,
        extraType: this.filterData.extraType,
        pageNo: this.pageConfig.pageNo,
        pageLimit: this.pageConfig.pageLimit,
      };
      // TODO: 实现获取数据列表的逻辑
      getSourceList(newParams).then((res) => {
        if (res.success) {
          this.dataList = res.data;
          this.pageConfig.total = res.totalSize;
        } else {
          this.$message.error(res.msg || "系统错误，请稍后再试");
        }
      });
    },
    getCategoryList() {
      getCategoryList({
        type: Number(this.activeName),
      }).then((res) => {
        console.log(res, "getCategoryList");
        if (res.success) {
          this.categoryList = res.data || [];
        } else {
          this.$message.error(res.msg || "系统错误，请稍后再试");
        }
      });
    },
    /**
     * 获取图片URL
     * @param row
     */
    getImgUrl(row) {
      if (row.cover) {
        return getResUrl(row.coverType, row.cover);
      }
      return "";
    },
    /**
     * 获取音频URL
     * @param row
     */
    getAudioUrl(row) {
      if (row.resId) {
        return getResUrl(row.fileType, row.resId);
      }
      return "";
    },
    /**
     * 编辑素材
     * @param row
     */
    handleEdit(row) {
      if (this.activeName === String(ScMaterialType.BG_MUSIC)) {
        this.$emit("changeComponent", "ScBgMusicEdit", {
          editInfo: row,
        });
      } else {
        this.$emit("changeComponent", "ScDubbingEdit", {
          editInfo: row,
        });
      }
    },
    /**
     * 删除素材
     * @param row
     */
    handleDelete(row) {
      this.$confirm("确定删除该素材吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        deleteMaterial({
          resId: row.resId,
          cover: row.cover,
        }).then((res) => {
          if (res.success) {
            this.$message.success("删除成功");
            this.getDataList();
          }
        });
      });
    },
    /**
     * 添加素材
     * @param type
     */
    addMaterial(type) {
      if (type === ScMaterialType.BG_MUSIC) {
        this.$emit("changeComponent", "ScBgMusicEdit");
      } else {
        this.$emit("changeComponent", "ScDubbingEdit");
      }
    },
    handleSizeChange(size) {
      this.pageConfig.pageLimit = size;
      this.getDataList();
    },
    handleCurrentChange(page) {
      this.pageConfig.pageNo = page;
      this.getDataList();
    },
    handleShowImg(row) {
      this.showImgDialog = true;
      this.showImgDialogData = row;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item {
  font-size: 16px;
  font-weight: bold;
}
</style>
