<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.math.*"%>
<%@ page import="java.util.regex.Matcher"%>
<%@ page import="java.util.regex.Pattern"%>
<%@ page import="fai.cli.PreSaleUtilCli"%>
<%@ page import="fai.cli.BssStatCli"%>
<%@ page import="fai.cli.*"%>

<%!
    //关注or取关aid
    private String focus(HttpServletRequest request) throws Exception{
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if(aid <= 0){
            Log.logStd("aid is not correct , aid = %d",aid);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        String sql =  "update acctPreSaleHd set flag = flag ^ 0x80 where aid = " + aid;
        Dao dao = WebOss.getOssBsDao();
        try{
            int rt = dao.executeUpdate(sql);
            if(rt != Errno.OK){
                Log.logStd("rt = %d, update err aid = %d",aid);
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        }finally {
            dao.close();
        }
        Log.logStd("change focus ok aid = %d",aid);
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }
%>


<%!
    //互动专用关注or取关aid
    private String focus4Crm(HttpServletRequest request) throws Exception{
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        String saleAcct = WebOss.getSacct();
        if(aid <= 0){
            Log.logStd("aid is not correct , aid = %d",aid);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        SysCrmCustomer sysCrmCustomer = Core.getSysKit(SysCrmCustomer.class);
        
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(SalesSystemDef.AcctResource.AID,ParamMatcher.EQ,aid);
        searchArg.matcher.and(SalesSystemDef.AcctResource.STATE,ParamMatcher.EQ,SalesSystemDef.State.RECEIVED);
        searchArg.matcher.and(SalesSystemDef.AcctResource.SALE_ACCT,ParamMatcher.EQ,saleAcct);
        searchArg.matcher.and(SalesSystemDef.AcctResource.DEPARTMENT,ParamMatcher.EQ,SalesSystemDef.Department.HD);
        
        FaiList<Param> resourceList = new FaiList<Param>();
        int rt = sysCrmCustomer.getResourceListBySearch(searchArg,resourceList);
        if(rt != Errno.OK){
        	return "{\"success\":false, \"msg\":\"系统有误，请稍候重试\"}";
        }
        
        if(resourceList == null || resourceList.size() != 1){
        	return "{\"success\":true, \"msg\":\"非本人操作，操作失败 ！\"}";
        }
        
        int id = resourceList.get(0).getInt(SalesSystemDef.AcctResource.ID,0);
        int focusOn = resourceList.get(0).getInt(SalesSystemDef.AcctResource.FOCUS_ON,0);
        
        if(id == 0){
        	Log.logErr("id err ; aid = %s",aid);
        	return "{\"success\":false, \"msg\":\"数据有误\"}";
        }
        
        Param info = new Param();
        info.setInt(SalesSystemDef.AcctResource.FOCUS_ON,focusOn == 0?1:0);
        
        rt = sysCrmCustomer.updateResource(new ParamMatcher(SalesSystemDef.AcctResource.ID,ParamMatcher.EQ,id),new ParamUpdater(info));
        if(rt != Errno.OK){
        	return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        
        
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }
%>


<%!

    //禁止or允许发送短信
    private String banSendMessage(HttpServletRequest request)throws Exception{
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if(aid <= 0){
            Log.logStd("aid is not correct , aid = %d",aid);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        String sql =  "update acctPreSaleHd set flag = flag ^ 0x200 where aid = " + aid;
        Dao dao = WebOss.getOssBsDao();
        try{
            int rt = dao.executeUpdate(sql);
            if(rt != Errno.OK){
                Log.logStd("rt = %d, update err aid = %d",aid);
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        }finally {
            dao.close();
        }
        Log.logStd("banSendMessage ok aid = %d",aid);
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }

%>


<%!

    //禁止or允许发送短信
    private String banSendMessage4Crm(HttpServletRequest request)throws Exception{
	
	   int aid = Parser.parseInt(request.getParameter("aid"), 0);
	   String saleAcct = WebOss.getSacct();
	   if(aid <= 0){
	       Log.logStd("aid is not correct , aid = %d",aid);
	       return "{\"success\":false, \"msg\":\"操作失败\"}";
	   }
	   SysCrmCustomer sysCrmCustomer = Core.getSysKit(SysCrmCustomer.class);
	   
	   SearchArg searchArg = new SearchArg();
	   searchArg.matcher = new ParamMatcher(SalesSystemDef.AcctResource.AID,ParamMatcher.EQ,aid);
	   searchArg.matcher.and(SalesSystemDef.AcctResource.STATE,ParamMatcher.EQ,SalesSystemDef.State.RECEIVED);
	   searchArg.matcher.and(SalesSystemDef.AcctResource.SALE_ACCT,ParamMatcher.EQ,saleAcct);
	   searchArg.matcher.and(SalesSystemDef.AcctResource.DEPARTMENT,ParamMatcher.EQ,SalesSystemDef.Department.HD);
	   
	   FaiList<Param> resourceList = new FaiList<Param>();
	   int rt = sysCrmCustomer.getResourceListBySearch(searchArg,resourceList);
	   if(rt != Errno.OK){
	   	return "{\"success\":false, \"msg\":\"系统有误，请稍候重试\"}";
	   }
	   
	   if(resourceList == null || resourceList.size() != 1){
	   	return "{\"success\":true, \"msg\":\"非本人操作，操作失败 ！\"}";
	   }
	   
	   int id = resourceList.get(0).getInt(SalesSystemDef.AcctResource.ID,0);
	   int banSendMsg = resourceList.get(0).getInt(SalesSystemDef.AcctResource.BAN_SEND_MSG,0);
	   
	   if(id == 0){
	   	Log.logErr("id err ; aid = %s",aid);
	   	return "{\"success\":false, \"msg\":\"数据有误\"}";
	   }
	   
	   Param info = new Param();
	   info.setInt(SalesSystemDef.AcctResource.BAN_SEND_MSG,banSendMsg == 0?1:0);
	   
	   rt = sysCrmCustomer.updateResource(new ParamMatcher(SalesSystemDef.AcctResource.ID,ParamMatcher.EQ,id),new ParamUpdater(info));
	   if(rt != Errno.OK){
	   	return "{\"success\":false, \"msg\":\"操作失败\"}";
	   }
	   
       return "{\"success\":true, \"msg\":\"操作成功\"}";
}

%>


<%!
    //关注or取关aid
    private String yk_focus(HttpServletRequest request) throws Exception{
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if(aid <= 0){
            Log.logStd("aid is not correct , aid = %d",aid);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        String sql =  "update acctPreSaleYk set flag = flag ^ 0x80 where aid = " + aid;
        Dao dao = WebHdOss.getYkOssDaoMaster();
        try{
            int rt = dao.executeUpdate(sql);
            if(rt != Errno.OK){
                Log.logStd("rt = %d, update err aid = %d",aid);
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        }finally {
            dao.close();
        }
        Log.logStd("change focus ok aid = %d",aid);
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }
%>

<%!

    //禁止or允许发送短信
    private String yk_banSendMessage(HttpServletRequest request)throws Exception{
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if(aid <= 0){
            Log.logStd("aid is not correct , aid = %d",aid);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
        
        String sql =  "update acctPreSaleYk set flag = flag ^ 0x200 where aid = " + aid;
        Dao dao = WebHdOss.getYkOssDaoMaster();
        try{
            int rt = dao.executeUpdate(sql);
            if(rt != Errno.OK){
                Log.logStd("rt = %d, update err aid = %d",aid);
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        }finally {
            dao.close();
        }
        Log.logStd("banSendMessage ok aid = %d",aid);
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }

%>


<%
    //cmd处理
    String output = "";
    try{
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            return;
        }else if (cmd.equals("focus")){
            output = focus(request);
        }else if(cmd.equals("banSendMessage")){
            output = banSendMessage(request);
        }else if (cmd.equals("yk_focus")){
            output = yk_focus(request);
        }else if(cmd.equals("yk_banSendMessage")){
            output = yk_banSendMessage(request);
        }else if(cmd.equals("focus4Crm")){
            output = focus4Crm(request);
        }else if(cmd.equals("banSendMessage4Crm")){
            output = banSendMessage4Crm(request);
        }
        
    }catch (Exception exp){
        PrintUtil.printErr(exp);
        output = WebOss.checkAjaxException(exp);
    }

    out.print(output);

%>