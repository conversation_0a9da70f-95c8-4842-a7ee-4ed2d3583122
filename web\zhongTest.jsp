<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.hdUtil.PrintUtil"%>
<%@ page import="fai.webhdportal.*"%>
<%@ page import="fai.webhdoss.WebHdOss" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>


<%!
    private static void dealBeforeConvert ( HttpServletRequest request, HttpServletResponse response, JspWriter out , String fileName , FaiList<Param> optionList )throws Exception{
        out.clear();//
        String agent = request.getHeader("User-Agent");
        agent = agent == null ? "" : agent.toUpperCase();
        response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent,fileName) + "\"");
        response.setContentType("application/vnd.ms-excel");
        MSOfficeConverter.excelConverter(response.getOutputStream(),optionList);
        //out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
    }

    /*
     * 需求1020183
     *
     * 用户范围：在2020/2/20-2020/2/24期间，登录互动管理平台的用户，并符合以下条件：
     * i.未开通商城；
     * ii.开通互动≥7天；
     * iii.创建过属于【商业促销】分类的活动（创建时间不限）
     */
    public static String getMallABTestData(HttpServletRequest request, HttpServletResponse response, JspWriter out ) throws Exception{
        int rt = Errno.ERROR;
        int flow = Core.getFlow();
        FaiList<Param> aidList = new FaiList<Param>();
        FaiList<Param> aidList2 = new FaiList<Param>();
        FaiList<Param> list = new FaiList<Param>();
        Dao dao = WebOss.getBssMainDao();
        if(dao == null){
            rt = Errno.DAO_CONN_ERROR;
            Log.logErr(rt, "get dao err");//
            return "err";
        }
        try {
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher("time", ParamMatcher.GE, 1582128000);
            searchArg.matcher.and("time", ParamMatcher.LT, 1582560000);
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.table = "hdLogin2020";
            selectArg.field = "aid, time";
            selectArg.group = "aid";
            selectArg.searchArg = searchArg;
            aidList = dao.select(selectArg);
            Log.logDbg("zhongdbg sql=%s", searchArg.matcher.getSql());

            searchArg.cmpor = new ParamComparator("time", true);
            aidList2 = dao.select(selectArg);

            Map<Integer, Integer> aid2Time = new HashMap<Integer, Integer>();
            for(Param item : aidList2){
                int aid = item.getInt("aid");
                int time = item.getInt("time");
                aid2Time.put(aid, time);
            }

            int size = aidList.size() > 100 ? 100 : aidList.size();
            for(int i = 0; i< size; i++){
                Param item = aidList.get(i);
                int aid = item.getInt("aid");
                int time = item.getInt("time");
                int lastTime = aid2Time.get(aid);

                Calendar openMallTime = null;
                long payTime = 0;
                int gameCount = 0;

                SiteProp siteProp = (SiteProp) Core.getCorpKit(aid, Kid.SITE_PROP);
                Param sitePropInfo = siteProp.getSiteProp();
                int sitePropflag = sitePropInfo.getInt(SitePropDef.Info.FLAG);
                boolean openMall = !Misc.checkBit(sitePropflag, SitePropDef.Flag.DIS_OPEN_MALL);

                if(openMall){
                    openMallTime = sitePropInfo.getCalendar(SitePropDef.Info.MALL_OPEN_TIME, Calendar.getInstance());
                    //期间内第一次登陆前就开通了商城，continue
                    if(openMallTime.getTimeInMillis()/1000 < time){
                        continue;
                    }
                }

                HdProf hdProf = (HdProf) Core.getCorpKit(aid, Kid.HD_PROF);
                Param profInfo = hdProf.getProf();
                Calendar createTime = profInfo.getCalendar(HdProfDef.Info.CREATE_TIME, Calendar.getInstance());
                if(lastTime - createTime.getTimeInMillis()/1000 < 604800){
                    // Log.logDbg("zhongdbg aid=%s, lastTime=%s, createTime=%s",aid, lastTime, createTime.getTimeInMillis()/1000);
                    continue;
                }

                HdGame hg = (HdGame) WebHdPortal.getCorpKit(aid, Kid.HD_GAME);
                ParamMatcher matcher = new ParamMatcher(HdGameDef.Info.AID, ParamMatcher.EQ, aid);
                matcher.and(HdGameDef.Info.FLAG, ParamMatcher.LAND, HdGameDef.Flag.ISFROMMODEL, HdGameDef.Flag.ISFROMMODEL);
                ParamMatcher orMatcher = new ParamMatcher();
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.PTCHL_ZF);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.JSMS_ZF);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.QMKJ_ZF);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.JSMS_ZF_DSP);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.PTCHL_ZF_DSP);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.JJP);
                orMatcher.or(HdGameDef.Info.STYLE, ParamMatcher.EQ, HdGameDef.Style.QMKJ_ZF_DSP);

                matcher.and(orMatcher);
                int count = hg.getGameCount(matcher);
                if(count == 0){
                    continue;
                }
                if(openMall){
                    searchArg = new SearchArg();
                    searchArg.matcher = new ParamMatcher("aid", ParamMatcher.EQ, aid);
                    selectArg = new Dao.SelectArg();
                    selectArg.searchArg = searchArg;
                    selectArg.table = "mallStatusByCompany";
                    selectArg.field = "firstPayTime";

                    Param data = dao.selectFirst(selectArg);
                    payTime = data.getLong("firstPayTime", 0L);
                }

                matcher = new ParamMatcher(HdGameDef.Info.AID, ParamMatcher.EQ, aid);
                gameCount = hg.getGameCount(matcher);


                Param data = new Param();
                data.setInt("aid", aid);
                data.setInt("gameCount", gameCount);
                if(openMall){
                    data.setString("openMallTime", Parser.parseSimpleTime(openMallTime));
                    if(payTime != 0){
                        Calendar calendar = Calendar.getInstance();
                        calendar.setTimeInMillis(payTime);
                        data.setString("payTime", Parser.parseSimpleTime(calendar));
                    }else{
                        data.setString("payTime", "-");
                    }
                }else{
                    data.setString("openMallTime", "-");
                    data.setString("payTime", "-");
                }
                list.add(data);
            }
        }finally {
            dao.close();
        }

        FaiList<Param> colList = new FaiList<Param>();
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "aid");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "openMallTime");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "payTime");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "gameCount");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
        Param p = new Param();
        p.setString("aid", "aid");
        p.setString("openMallTime", "开通商城时间");
        p.setString("payTime", "首次付费商城时间");
        p.setString("gameCount", "互动游戏创建数");
        rowList.add(p);
        rowList.addAll(list);
        FaiList<Param> optionList = new FaiList<Param>();
        Param option = null;
        int outputCount = rowList.size();
        if (outputCount <= 65535) {                                                                                        //65536是office2003 sheet的行上限
            option = new Param();
            option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet");
            option.setList(MSOfficeConverter.Option.COL_LIST, colList);
            option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
            optionList.add(option);
        } else {
            //xls一张sheet只能存储65536条数据，因此超出分成多张sheet
            for (int i = 1; i <= outputCount; i = i + 65535) {
                option = new Param();
                int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
                option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet" + i + "~" + (indexCount - 1));
                option.setList(MSOfficeConverter.Option.COL_LIST, colList);
                FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
                sheetData.add(0, p);                                                                                       //在每个工作表首行加上列名字段
                option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
                optionList.add(option);
            }
        }
        String fileName = "导出数据.xls";
        dealBeforeConvert( request,response,out,fileName, optionList );
        return "suc";
    }

    private String getOrder(HttpServletRequest request, HttpServletResponse response, JspWriter out ) throws Exception{
        FaiList<Param> list = new FaiList<Param>();
        Dao dao = WebOss.getBssMainDao();
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        try{
            FaiList<Integer> productIdList = new FaiList<Integer>();
            productIdList.add(115);
            productIdList.add(123);
            productIdList.add(164);
            productIdList.add(228);

            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher("productId", ParamMatcher.IN, productIdList);
            searchArg.matcher.and("status", ParamMatcher.EQ, 10);
            searchArg.matcher.and("firstPayTime", ParamMatcher.GT, 0);
            searchArg.matcher.and("payTime", ParamMatcher.GE, 1582992000);
            searchArg.matcher.and("payTime", ParamMatcher.LT, 1585670400);
            searchArg.matcher.and("prop2", ParamMatcher.EQ, 1);

            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.searchArg = searchArg;
            selectArg.table = "acctOrderItem";
            selectArg.field = "aid, orderId";

            list = dao.select(selectArg);
        }finally {
            dao.close();
        }
        OssFaiOrder oSysOrder = new OssFaiOrder();
        OssSale ossSale = new OssSale();
        SysCrmCustomer sysCrmCustomer = Core.getSysKit(SysCrmCustomer.class); //crm接口

        for(Param item : list){
            int aid = item.getInt("aid");
            int orderId = item.getInt("orderId");
            faiTradeStationBaseApi.changeAid(aid);

            Param info = new Param();
            ossSale.setSaleInfo(aid, info);
            String saleName_hd = info.getString("saleName_hd", "");


            Param order = oSysOrder.getOrderInfo(aid, orderId);
            double price = order.getDouble(FaiOrderDef.Info.PRICE);
            int productId = item.getInt(FaiOrderDef.Item.PRODUCT_ID);
            int productType = FaiProductDef.getType(productId);
            String name = faiTradeStationBaseApi.getName(productId);
            if( productType == FaiProductDef.Type.HD_GAME ){
                int gameId = item.getInt( FaiOrderDef.ItemHdGame.GAME_ID );
                HdGame hdGame = (HdGame)Core.getCorpKit(aid,Kid.HD_GAME);
                try{
                    Param ginfo = hdGame.getGameInfo(gameId);
                    if( ginfo == null || ginfo.isEmpty() ){
                        name = name + "（hd" + gameId + "）";
                    }else{
                        String gname = ginfo.getString( HdGameDef.Info.NAME );
                        name = name + "（"+ gname +" hd" + gameId + "）";
                    }
                }catch(Exception e){

                }
            }
            item.setDouble("price", price);
            item.setString("name", name);
            item.setString("saleName", saleName_hd);
        }

        FaiList<Param> colList = new FaiList<Param>();
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "aid");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "orderId");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "price");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "name");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "saleName");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
        Param p = new Param();
        p.setString("aid", "aid");
        p.setString("orderId", "订单id");
        p.setString("price", "付费金额");
        p.setString("name", "付费产品");
        p.setString("saleName", "销售名称");
        rowList.add(p);
        rowList.addAll(list);
        FaiList<Param> optionList = new FaiList<Param>();
        Param option = null;
        int outputCount = rowList.size();
        if (outputCount <= 65535) {                                                                                        //65536是office2003 sheet的行上限
            option = new Param();
            option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet");
            option.setList(MSOfficeConverter.Option.COL_LIST, colList);
            option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
            optionList.add(option);
        } else {
            //xls一张sheet只能存储65536条数据，因此超出分成多张sheet
            for (int i = 1; i <= outputCount; i = i + 65535) {
                option = new Param();
                int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
                option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet" + i + "~" + (indexCount - 1));
                option.setList(MSOfficeConverter.Option.COL_LIST, colList);
                FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
                sheetData.add(0, p);                                                                                       //在每个工作表首行加上列名字段
                option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
                optionList.add(option);
            }
        }
        String fileName = "付费.xls";
        dealBeforeConvert( request,response,out,fileName, optionList );
        return "suc";
    }

    private String getMteduVipList(HttpServletRequest request, HttpServletResponse response, JspWriter out ) throws Exception{
        FaiList<Param> list = new FaiList<Param>();
        HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdMteduVipCodeDef.Info.VER, ParamMatcher.EQ, 1);
        searchArg.matcher.and(HdMteduVipCodeDef.Info.AID, ParamMatcher.NE, "");
        int rt = hdOss.getMteduVipCodeList(searchArg, list);
        if(rt != Errno.OK){
            return "err";
        }

        FaiList<Param> colList = new FaiList<Param>();
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "aid");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "code");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "ver");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }

        FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
        Param p = new Param();
        p.setString("aid", "aid");
        p.setString("code", "激活码");
        p.setString("ver", "版本");
        rowList.add(p);
        rowList.addAll(list);
        FaiList<Param> optionList = new FaiList<Param>();
        Param option = null;
        int outputCount = rowList.size();
        if (outputCount <= 65535) {                                                                                        //65536是office2003 sheet的行上限
            option = new Param();
            option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet");
            option.setList(MSOfficeConverter.Option.COL_LIST, colList);
            option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
            optionList.add(option);
        } else {
            //xls一张sheet只能存储65536条数据，因此超出分成多张sheet
            for (int i = 1; i <= outputCount; i = i + 65535) {
                option = new Param();
                int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
                option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet" + i + "~" + (indexCount - 1));
                option.setList(MSOfficeConverter.Option.COL_LIST, colList);
                FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
                sheetData.add(0, p);                                                                                       //在每个工作表首行加上列名字段
                option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
                optionList.add(option);
            }
        }
        String fileName = "馒头商学院.xls";
        dealBeforeConvert( request,response,out,fileName, optionList );
        return "suc";
    }

%>

<%
Param saleAcctInfo = Acct.getStaffInfo(1, Session.getSid());
    Log.logDbg("zhongdbg saleAcctInfo=%s", saleAcctInfo);
    /*if (!Web.isDev() && Session.getSid() != 1224 && Session.getSid() != 1324 && Session.getSid() != 1355 && Session.getSid() != 802 ) {
        out.println("没有权限");
        return;
    }*/
    String output = "";
    String cmd = request.getParameter("cmd");
    if (cmd == null) {
        return;
    }
    try {
        if("getMallABTestData".equals(cmd)){
            output = getMallABTestData(request,response,out);
        }else if("getOrder".equals(cmd)){
            output = getOrder(request,response,out);
        }else if("getMteduVipList".equals(cmd)){
            output = getMteduVipList(request,response,out);
        }


        out.print(output);
    } catch (Exception e) {
        PrintUtil.printStackTrace(e, 1, "zhongText");
    }
%>
