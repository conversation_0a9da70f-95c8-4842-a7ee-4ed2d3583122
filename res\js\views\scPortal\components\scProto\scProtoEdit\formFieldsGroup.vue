<!-- 素材组配置 -->
<template>
  <div class="mt-[40px]">
    <p class="mb-[10px] text-[16px] font-bold">原型表单</p>
    <el-table
      :data="protoInfo.inputFormList">
      <el-table-column
        prop="label"
        label="表单名称（label）"
        width="400">
      </el-table-column>
      <el-table-column
        prop="variable"
        label="字段名称（variable）"
        width="200">
      </el-table-column>
      <el-table-column
        prop="required"
        label="是否必填"
        width="100">
        <template slot-scope="scope">
          <p>{{ scope.row.required ? '是' : '否' }}</p>
        </template>
      </el-table-column>
      <el-table-column
        label="字段类型"
        width="200"
      >
        <template slot-scope="scope">
          <el-select v-model="scope.row.filedType" class="!w-[180px]" placeholder="请选择字段类型">
            <!-- <el-option label="文本输入" :value="InputType.TEXT"></el-option>
            <el-option label="标签输入" :value="InputType.TAG"></el-option> -->
            <el-option v-for="item in filedTypeList" :key="item.key" :label="item.value" :value="item.key"></el-option>
          </el-select>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>


<script>
import { InputType } from '@/views/scPortal/config/index.js'

export default {
  name: 'MaterialGroup',
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    },
    filedTypeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      InputType,
      materialNum: 1
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

