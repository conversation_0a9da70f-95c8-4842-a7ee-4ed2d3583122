<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>活动列表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
    </head>

    <body>	
		<div class="container" id="wxTemplateMsgBox" >
			<!--页面顶部标题-->
			<div style="text-align: center;">
				<h3 style="margin-top: 0px">{{title}}</h3>
			</div>

			<!--表单-->
			<div class="menuBox">
				<el-form :inline="true" :model="taskForm" ref="taskForm" class="wxTemplateMsg">
					<el-form-item>
						<span class="taskIdInput">taskId</span>
						<el-input type="text" v-model="taskForm.taskId" placeholder="taskId"></el-input>
						<el-button type="primary" @click="searchTask()" class="searchBtn">查询</el-button>
						<el-button type="primary" @click="exportTask()" class="searchBtn">导出</el-button>
					</el-form-item>
				</el-form>
			</div>
			<div class="tableBox" v-show="!isFirst">
				<template>
					<el-table :data="tableData" border style="width: 100%" max-height="600">
						<el-table-column prop="aid" label="aid" width="100"></el-table-column>
						<el-table-column prop="openId" label="openId" width="300"></el-table-column>
						<el-table-column prop="unsubscribe" label="取消关注" width="100"></el-table-column>
						<el-table-column prop="unsubscribeTime" label="取消关注时间"></el-table-column>
					</el-table>
				</template>
				<div>
					<el-pagination
						@size-change="handleSizeChange"
						@current-change="handleCurrentChange"
						:current-page="taskForm.currentPage"
						:page-sizes="[20, 50, 100, 200]"
						:page-size="taskForm.pageSize"
						layout="total, sizes, prev, pager, next, jumper"
						:total="taskForm.total">
					</el-pagination>
				</div>
			</div>
		</div>	
    </body>
	<script type="text/javascript" charset="utf-8">
		let invastiGate = new Vue({
			el : "#wxTemplateMsgBox",
			data : {
				title : "微信模板消息数据",
				total : 0,
				currentPage : 1,
				taskForm :{
					taskId : '',
					total : 0,
					currentPage : 1,
					pageSize: 20
				},
				tableData :[],
				isFirst : true
			},
			methods:{
				handleSizeChange: function(val){
					this.taskForm.pageSize = val;
					getMsgRecordList(this, {
						"cmd": "getMsgRecordList",
						"taskId": this.taskForm.taskId,
						"page": 1,
						"limit": val,
					});
				},
				handleCurrentChange: function(val){
					getMsgRecordList(this, {
						"cmd": "getMsgRecordList",
						"taskId": this.taskForm.taskId,
						"page": val,
						"limit": this.taskForm.pageSize,
					});
				},
				searchTask: function(){
					getMsgRecordList(this, {
						"cmd": "getMsgRecordList",
						"taskId": this.taskForm.taskId,
						"page": 1,
						"limit": this.taskForm.pageSize,
					});
				},
				exportTask: function(){
					window.location.href = "/ajax/hdProduct_h.jsp?cmd=exportMsgRecordList&taskId=" + this.taskForm.taskId; 
				}
			}
		});

		function getMsgRecordList(_this, arg){
			Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON: true}).then(response => {
				let data = response.data;
				if( data.success ){
					_this.tableData = data.list;
					_this.taskForm.total = data.total;
					_this.isFirst = false;
				}else{
					_this.tableData = [];
					_this.taskForm.total = 0;
					_this.$message({
						type: 'warning',
						message: data.msg + "(taskId:" + (_this.taskForm.taskId ? _this.taskForm.taskId : "空") + ")"
					});
				}                  
			}, response => {
				_this.tableData = [];
				_this.taskForm.total = 0;
				_this.$message({
					type: 'warning',
					message: '系统错误!'
				});
			}); 
		}
	</script>
</html>