package fai.webhdoss.service.impl;

import com.alibaba.fastjson.JSON;
import fai.cli.HdOssAffairCli;
import fai.comm.util.*;
import fai.webhdoss.model.vo.HdCaseFilterVO;
import fai.webhdoss.service.HdCaseFilterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class HdCaseFilterServiceImpl implements HdCaseFilterService {

    @Autowired
    private HdOssAffairCli hdOssAffairCli;

    /**
     * 将活动列表的某一个活动导入案例筛选库
     * @param hdCaseFilterVO
     * @return
     */
    @Override
    public String importCaseFilter(HdCaseFilterVO hdCaseFilterVO) {
        if (hdCaseFilterVO.getoAid() == 0){
            return "原始aid不能为空";
        }
        if (hdCaseFilterVO.getoGameId() == 0){
            return "原始oGameId不能为空";
        }
        if (hdCaseFilterVO.getCaseAid() == 0){
            return "案例Aid不能为空";
        }
        if (hdCaseFilterVO.getGameId() == 0){
            return "复制后活动id不能为空";
        }
        //设置导入时间
        hdCaseFilterVO.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm:ss")));
        //该标志位设为 0 表示该案例没有从案例筛选库导入案例库
        hdCaseFilterVO.setFlag(0);

        String hdCaseFilter = JSON.toJSONString(hdCaseFilterVO);
        Ref<String> ref = new Ref<>();
        int rt = hdOssAffairCli.importCaseFilter(hdCaseFilter, ref);
        if (rt != Errno.OK){
            Log.logStd("error:rt="+rt);
            return "导入失败";
        }
        return ref.value;
    }

    /**
     * 将案例筛选库的某一个活动导入案例库
     * @param id
     * @return
     */
    @Override
    public String importCase(int id) {
        Ref<String> ref = new Ref<>();

        int rt = hdOssAffairCli.importCase(id, ref);
        if (rt != Errno.OK){
            return "导入失败";
        }
        return ref.value;

    }

    /**
     * 将案例筛选库的某一个活动删除
     * @param id
     * @return
     */
    @Override
    public String deleteCaseFilter(int id) {
        Ref<String> ref = new Ref<>();

        int rt = hdOssAffairCli.deleteCaseFilter(id, ref);
        if (rt != Errno.OK){
            return "删除失败";
        }
        return ref.value;
    }

    /**
     * 编辑案例筛选库的某一个活动
     * @param hdCaseFilterVO
     * @return
     */
    @Override
    public String modifyCaseFilter(HdCaseFilterVO hdCaseFilterVO) {
        if(hdCaseFilterVO.getId() == 0){
            return "id无效";
        }
        Param param = new Param();
        param.setInt("id",hdCaseFilterVO.getId());

        if(!"".equals(hdCaseFilterVO.getoAcctName())){
            param.setString("oAcctName",hdCaseFilterVO.getoAcctName());
        }
        if(!"".equals(hdCaseFilterVO.getTrade())){
            param.setString("trade",hdCaseFilterVO.getTrade());
        }
        if(!"".equals(hdCaseFilterVO.getScene())){
            param.setString("scene",hdCaseFilterVO.getScene());
        }
        if(!"".equals(hdCaseFilterVO.getLabel())){
            param.setString("label",hdCaseFilterVO.getLabel());
        }
        if(!"".equals(hdCaseFilterVO.getActivityBright())){
            param.setString("activityBright",hdCaseFilterVO.getActivityBright());
        }
        if(!"".equals(hdCaseFilterVO.getActiveFunc())){
            param.setString("activeFunc",hdCaseFilterVO.getActiveFunc());
        }
        String data = param.toJson();

        Ref<String> ref = new Ref<>();
        int rt = hdOssAffairCli.modifyCaseFilter(data, ref);
        if (rt != Errno.OK){
            return "修改失败";
        }
        return ref.value;
    }

    /**
     * 案例筛选库查询
     * @return
     */
    @Override
    public int getCaseFilterList(Param param, Ref<List> dataListRef, Ref<Integer> totalSizeRef) {

        if(param.getInt("page") == null || param.getInt("limit") == null){
            Log.logStd("page="+param.getInt("page")+", limit="+param.getInt("limit"));
            return Errno.ERROR;
        }
        if (param.getInt("oAid") == null){
            param.remove("oAid");
        }
        if (param.getInt("oGameId") == null){
            param.remove("oGameId");
        }
        if (param.getInt("gameId") == null){
            param.remove("gameId");
        }
        if (param.getString("createPerson") == null || "".equals(param.getString("createPerson"))){
            param.remove("createPerson");
        }
        String createTimeStart = param.getString("createTimeStart");
        if(createTimeStart != null && !"".equals(createTimeStart)){
            createTimeStart += " 00:00:00";
            param.setString("createTimeStart",createTimeStart);
        }else{
            param.remove("createTimeStart");
        }
        String createTimeEnd = param.getString("createTimeEnd");
        if(createTimeEnd != null && !"".equals(createTimeEnd)){
            createTimeEnd += " 23:59:59";
            param.setString("createTimeEnd",createTimeEnd);
        }else{
            param.remove("createTimeEnd");
        }
        return hdOssAffairCli.getCaseFilterList(param, dataListRef, totalSizeRef);
    }

    @Override
    public String getGameLinkandCode(String data) {
        Ref<String> ref = new Ref<>();

        int rt = hdOssAffairCli.getGameLinkandCode(data, ref);
        if (rt != Errno.OK){
            return "获取失败";
        }
        return ref.value;
    }

    @Override
    public Param getCaseFilterById(int id) {
        return hdOssAffairCli.getCaseFilterById(id);
    }
}
