<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>活动列表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
    </head>

    <body>	
        <div id='errorTable'>
            <el-row>
                <el-col :span="1" >
                    <el-button type="primary"  @click="clickAddErrorCode('open')">申请错误码</el-button>
                </el-col>
                <el-col :span="5">
                    <el-form :model="addCode" >
                            <el-form-item label="" :label-width="formLabelWidth">
                                <el-input type="text" v-model="scode" auto-complete="off" placeholder="错误码(可直接输入数字)"></el-input>
                                <el-button @click="searchErrorCode"   type="text">查询</el-button> 
                            </el-form-item>
                    </el-form>
                </el-col>
            
            </el-row>
        
            <el-dialog title="申请错误码" :visible.sync="dialogFormVisible" >
                <el-form :model="addCode" >
                    <el-form-item label="*说明" :label-width="formLabelWidth">
                        <el-input type="textarea" :rows="3" v-model="addCode.description" auto-complete="off"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="clickAddErrorCode('cancel')">取 消</el-button>
                    <el-button type="primary" @click="clickAddErrorCode('add')">确 定</el-button>
                </div>
            </el-dialog>

            <div>
                <el-table :data="errorCodeInfo"  border style="width: 100%" max-height="600" >
                    <el-table-column  label="操作" width="100"  align="center"> 
                        <template slot-scope="scope">
                            <el-button @click.native.prevent="deleteErrorCode(scope.$index, errorCodeInfo)" type="text" size="small">删除</el-button>  
                                        
                        </template>
                    </el-table-column>
                    <el-table-column  prop="code" label="错误码" width="160"  align="center"></el-table-column>
                    <el-table-column  prop="description" label="说明" width="800"  ></el-table-column>
                    
                </el-table>
            </div>
            <div class="block">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="page" 
                    :page-sizes="[10, 20, 50, 100]" :page-size="limit" layout="prev, sizes,pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        
        </div>
			
    </body>


<script>
    var errorTable = new Vue({
        el: '#errorTable',
        data: {
            errorCodeInfo: [],
            addCode:{
                description: ''
            },
            scode:'',
            page: 1,
            limit:10,
            total:0,
            dialogFormVisible: false,
            formLabelWidth: '60px'
        },
        created: function(){
            this.getErrorCodeList();
        },
        methods: {
            handleSizeChange(val) {
                this.limit = val;
                this.getErrorCodeList();
			},
            handleCurrentChange(val) {
                this.page = val;
                this.getErrorCodeList();
            },
            clickAddErrorCode(action){
                if(action == 'open'){
                    this.dialogFormVisible=true;
                    return;
                }else if(action == 'cancel'){
                    this.dialogFormVisible=false;
                    return;
                }else{
                    var arg = {
					    "cmd":"genErrorCode",
					    "description":this.addCode.description,
                    }               
                    Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {
                        let data = response.data;
                        if(data.success == true){
                            this.$message({
                                type: 'success',
                                message: '申请成功，错误码为'+data.msg
                            });
                            this.getErrorCodeList();
                        }else{
                            this.$message({
                                type: 'warning',
                                message: data.msg
                            });
                        }                  
                    }, response => {
                        this.$message({
                            type: 'warning',
                            message: '系统错误!'
                        });
                    }); 
                    
                    this.addCode.description='';      
                    this.dialogFormVisible=false;   
                    
                }
                                        
            },
            //删除
            deleteErrorCode(index,codeList){
                if(!confirm("是否确认删除"+codeList[index].code+" ?")){
				    return;
			    }
                var arg = {
                    "cmd":"deleteErrorCode",	
                    "code":codeList[index].code		    
                }
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {  
                    let data = response.data;
                    if(data.success = true){
                        this.$message({
                            type: 'success',
                            message: '删除成功!'
                        });
                        this.getErrorCodeList();
                    }else{ 
                        this.$message({
                            type: 'warning',
                            message: '删除失败!'
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                }); 
            },

            getErrorCodeList(){
                var arg = {
                    "cmd":"getErrorCodeList",	
                    "page":this.page,
                    "limit":this.limit		    
                }
                console.log(arg);
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {  
                    console.info(response.data)        
                    this.errorCodeInfo = response.data.codeList;
                    this.total = response.data.total;
                    
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                }); 
        
            },

            searchErrorCode(){
                var arg = {
                    "cmd":"searchErrorCode",	
                    "code":this.scode		    
                }
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {  
                    let data = response.data;
                    if(data.success = false){
                        this.$message({
                            type: 'warning',
                            message: '没有找到!'
                        });
                    }else{ 
                        this.errorCodeInfo = data;
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                }); 
            },
            
           
        }
    })

</script>

</html>