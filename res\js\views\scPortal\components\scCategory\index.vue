<template>
  <div class="h-full overflow-auto p-[0_30px_10px] pb-[100px]">
    <component :is="component"></component>
  </div>
</template>

<script>
import ScCategoryList from './scCategoryList/index.vue';

export default {
  name: "ScCategory",
  components: { 
    ScCategoryList,
  },
  data() {
    return {
      component: 'ScCategoryList',
    };
  },
  created() {
    
  },
  methods: {
   
  },
};
</script>

<style lang="scss">
@import "../../styles/elementui.scss";
</style>
