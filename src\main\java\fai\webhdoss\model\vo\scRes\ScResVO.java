package fai.webhdoss.model.vo.scRes;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 素材资源VO
 *
 * <AUTHOR> 2025/5/28 18:35
 * @Update jachin 2025/5/28 18:35
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScResVO {

    private int type = -1;          // 资源类型
    private String voiceType;      // 音色id
    private Integer extraType;       // 音色类型
    private String resId = "";      // 资源id
    private String name = "";       // 名字
    private String cover;           // 封面
    private String des;             // 描述
    private int categoryId = -1;    // 文件夹id 用作分类
    private int fileType = -1;      // 文件类型
    private long fileSize = 0;      // 文件大小
    private int id = -1;            // 更新的id
    private Integer duration;       // 时长
    private Boolean blockAuto;      // 屏蔽资源进入自动推荐
}
