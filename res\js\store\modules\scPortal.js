import { getTemplateConfInfo, getDomain } from '@/views/scPortal/api/scTemplate.js';
import { ScProductType } from '@/views/scPortal/config/index.js';


export default {
  namespaced: true, // 建议设置为true，使用命名空间
  state: {
    // 模板配置信息
    templateConfInfo: {
      industryList: [], // 行业列表
      sceneList: [], // 场景列表
    },
    // 域名信息
    domainInfo: { 
      scUsrResOssRoot: '', // oss资源域名
    },
    curActiveName: String(ScProductType.VIDEO), // 当前切换的tab
  },
  mutations: {
    setTemplateConfInfo(state, payload) {
      state.templateConfInfo = payload;
    },
    domainInfo(state, payload) {
      state.domainInfo = payload;
    },
    curActiveName(state, payload) {
      state.curActiveName = payload;
    }
  },
  actions: {
    getTemplateConfInfo({ commit, state }) {
      if (state.templateConfInfo.industryList.length > 0) {
        return Promise.resolve();
      }
      return getTemplateConfInfo().then(res => {
        if (res && res.success) {
          commit('setTemplateConfInfo', res.data);
          return res;
        }
        return Promise.reject(res.message || '获取配置信息失败');
      });
    },
    getDomainInfo({ commit, state }) {
      if (state.domainInfo.scUsrResOssRoot) {
        return Promise.resolve();
      }
      return getDomain().then(res => {
        if (res && res.success) {
          commit('domainInfo', res.data);
          return res;
        }
        return Promise.reject(res.message || '获取域名信息失败');
      });
    },
  },
 
};

