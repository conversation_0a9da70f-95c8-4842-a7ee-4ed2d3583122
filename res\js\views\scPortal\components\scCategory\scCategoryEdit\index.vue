<template>
  <el-dialog :visible="true" :title="curTitle" width="400px" @close="handleClose">
    <el-form class="p-[20px] min-h-[100px]" ref="categoryForm" :model="categoryInfo" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input class="!w-[300px]" v-model="categoryInfo.name" placeholder="请输入名称" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="saveConfig">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { updateCategory, addCategory } from '@/views/scPortal/api/scCategory.js';
import { ScMaterialType, ScMaterialTypeName } from '@/views/scPortal/config/index.js';

export default {
  name: "scCategoryEdit",
  props: {
    editType: {
      type: Number,
      default: ScMaterialType.BG_MUSIC,
    },
    /**
     * 编辑信息
     */
    editInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {  
    return {
      dialogVisible: false,
      categoryInfo: {
        name: "", // 名称
        type: ScMaterialType.BG_MUSIC,
      },
      rules: {
        name: {
          required: true,
          message: "请输入名称",
          trigger: ['submit']
        }
      }
    };
  },
  computed: {
    curTitle() {
      const typeName = ScMaterialTypeName[this.editType];
      return Object.keys(this.editInfo).length ? `编辑${typeName}分类` : `添加${typeName}分类`;
    },
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val;
      },
      immediate: true,
    },
    editInfo: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.categoryInfo = {...val};
        } else {
          this.categoryInfo = {
            name: "", // 名称
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      this.hideDialog();
      this.$emit("close");
    },
    /**
     * 保存
     */
    saveConfig() {
      this.$refs.categoryForm.validate(valid => {
        if (valid) {
          Object.assign(this.categoryInfo, {
            type: this.editType,
          })
          if (Object.keys(this.editInfo).length > 0) {
            updateCategory(this.categoryInfo).then(res => {
              if (res.success) {
                this.$message.success('保存成功');
                this.$emit('confirm');
                this.hideDialog();
              } else {
                this.$message.error(res.msg || '系统错误，请稍后再试');
              }
            });
          } else {
            addCategory(this.categoryInfo).then(res => {
              if (res.success) {
                this.$message.success('保存成功');
                this.$emit('confirm');
                this.hideDialog();
              } else {
                this.$message.error(res.msg || '系统错误，请稍后再试');
              }
            });
          }

        }
      })
    },
    hideDialog() {
      this.$emit('update:visible', false);
    },
  }
}
</script>

<style lang="scss" scoped></style>
