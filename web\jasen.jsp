<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="java.util.Map.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.webhdportal.*"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.sql.*"%>
<%@ page import="java.io.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.webhdportal.*"%>
<%@ page import="java.net.HttpURLConnection"%>
<%@ page import="java.net.InetSocketAddress"%>
<%@ page import="java.net.Proxy"%>
<%@ page import="java.nio.ByteBuffer"%>
<%@ page import="java.net.URL"%>
<%@ page import="java.io.InputStream"%>
<%@ page import="java.io.ByteArrayOutputStream"%>
<%@ page import="java.io.IOException"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.comm.util.Ref" %>
<%@ page import="java.util.Date" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.web.*"%>
<%@ page import="fai.webwxast.*"%>
<%@ page import="java.util.concurrent.TimeUnit" %>
<%if (!Web.getDebug() && Session.getSid()!=753)return;%>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title></title>
<style type="text/css">
</style>
<%

Dao dao = WebOss.getOssBsDao();
Dao bssDao = WebOss.getBssMainDao();
try{
	FaiList<Param> aidList = dao.executeQuery("select aid,createTime from agentConsultingIntention where createTime>'2018-09-01 00:00:00' and createTime<'2018:12:01 00:00:00'");
	Set<Integer> aidSet = new HashSet<Integer>();
	if(aidList != null){
		for(Param item:aidList){
			aidSet.add(item.getInt("aid"));
		}
	}
	FaiList<Integer> aidIntList = new FaiList<Integer>(aidSet);
	Dao.SelectArg sltArg = new Dao.SelectArg();
	sltArg.table = "hdSaleRecord";
	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
	sltArg.field= "max(receiveTime) as receiveTime,aid";
	sltArg.group = "aid";
	FaiList<Param> receiveList = dao.select(sltArg);
	
	sltArg = new Dao.SelectArg();
	sltArg.table = "acctOrderItem";
	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
	FaiList<Integer> hdProductList = new FaiList<Integer>();
	hdProductList.add(115);//铂金
	hdProductList.add(123);//白银
	hdProductList.add(124);//铂金
	hdProductList.add(164);//门店
	hdProductList.add(228);//钻石
	sltArg.searchArg.matcher.and("productId",ParamMatcher.IN,hdProductList);
	sltArg.field = "max(payTime) as payTime,aid,productId";
	sltArg.group = "aid";
	FaiList<Param> payHdList = bssDao.select(sltArg);
	if(payHdList == null){
		Log.logErr("yansen payHdList=null err");
	}
	

	sltArg = new Dao.SelectArg();
	sltArg.table = "acctOrderItem";
	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
	FaiList<Integer> cdProductList = new FaiList<Integer>();
	cdProductList.add(125);//微传单专享版
	cdProductList.add(154);//微传单付费模板
	sltArg.searchArg.matcher.and("productId",ParamMatcher.IN,cdProductList);
	sltArg.field = "max(payTime) as payTime,aid,productId";
	sltArg.group = "aid";
	FaiList<Param> payCdList = bssDao.select(sltArg);

	Param hdParam = new Param(true);
	Param cdParam = new Param(true);
	for(Param item:payHdList){
		hdParam.setParam(item.getInt("aid")+"",item);
	}
	for(Param item:payCdList){
		cdParam.setParam(item.getInt("aid")+"",item);
	}
	if(aidList != null){
		for(Param item:aidList){
			String key = String.valueOf(item.getInt("aid"));
			Param cdPay = cdParam.getParam(key);
			if(cdPay != null){
				int productId = cdPay.getInt("productId",0);
				int payTime = cdPay.getInt("payTime",0);
				String name = productId==125?"微传单专享版":"微传单付费模板";
				item.setString("付费产品",name);
				item.setString("最近付费时间",Parser.parseDateString(payTime,"yyyy-MM-dd HH:mm:ss"));
				item.setInt("payTime",payTime);
			}
			Param hdPay = hdParam.getParam(key);
			if(hdPay != null){
				int productId = hdPay.getInt("productId",0);
				int payTime = hdPay.getInt("payTime",0);
				String name = "";
				if(productId == 115 || productId == 124){
					name = "铂金版";
				}else if(productId == 123){
					name = "白银版";
				}else if(productId == 164){
					name = "门店版";
				}else if(productId == 228){
					name = "钻石版";
				}else{
					name=hdPay.toJson();
				}
				item.setString("付费产品",name);
				item.setInt("payTime",payTime);
				item.setString("最近付费时间",Parser.parseDateString(payTime,"yyyy-MM-dd HH:mm:ss"));
			}
		}
	}

	Log.logDbg("yansen aidList=%s",aidList);
}finally{
	dao.close();
	bssDao.close();
}


%>

</head>
<body>
	
</body>
</html>
<script>

	var data={};

</script>


