<template>
  <div>
    <select @change="onTagChange">
      <option disabled selected>请选择标签</option>
      <option value="highlight">高亮</option>
      <option value="warn">警告</option>
      <option value="note">备注</option>
    </select>
    <el-button @click="clearTag">清除标签</el-button>

    <editor-content :editor="editor" />
  </div>
</template>

<script>
import { Editor, EditorContent } from 'tiptap'
import TagMark from '@/views/scPortal/extensions/index.js';

export default {
  name: 'HighlightText',
  components: {
    EditorContent,
  },
  data() {
    return {
      editor: null,
    }
  },
  mounted() {
    console.log('测试hightlight')
    this.editor = new Editor({
      extensions: [
        // new StarterKit(),
        new TagMark(),
      ],
      content: '<p>选中任意文字后，选择标签插入。</p>',
    })
  },
  methods: {
    onTagChange(event) {
      const tagType = event.target.value;
      if (!tagType) return;

      const labelText = {
        'highlight': '高亮',
        'warn': '警告',
        'note': '备注'
      }[tagType];

      const { state } = this.editor;
      const { from, to } = state.selection;
      
      if (from === to) return; // 没有选中文本时不执行

      this.editor.commands.setTag({ 
        tagType,
        label: labelText
      });
      
      event.target.selectedIndex = 0;
    },
    clearTag() {
      this.editor.commands.unsetTag()
    },
  },
  beforeDestroy() {
    this.editor.destroy()
  },
}
</script>

<style>
.tag {
  padding: 2px 4px;
  border-radius: 3px;
  margin: 0 1px;
}
.tag-highlight {
  background-color: yellow;
}
.tag-warn {
  background-color: red;
  color: white;
}
.tag-note {
  background-color: lightblue;
}

/* 添加标签样式 */
.tag-label {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 4px;
}
.tag-label-highlight {
  background-color: #fff3cd;
  border: 1px solid #ffeeba;
  color: #856404;
}
.tag-label-warn {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
.tag-label-note {
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}
</style>
