package fai.webhdoss.service.impl;

import com.alibaba.fastjson.JSON;
import fai.app.FileStgDef;
import fai.app.ScProtoDef;
import fai.app.ScResDef;
import fai.app.ScTemplateDef;
import fai.cli.ScProtoCli;
import fai.cli.ScResCli;
import fai.cli.ScTemplateCli;
import fai.comm.jnetkit.server.fai.RemoteStandResult;
import fai.comm.util.*;
import fai.comm.util.config.ScConfigKey;
import fai.entity.ScProtoEntity;
import fai.entity.ScTemplateEntity;
import fai.hdUtil.HdMisc;
import fai.hdUtil.HdTimer;
import fai.hdUtil.JsonResult;
import fai.hdUtil.config.HdConfigUtil;
import fai.hdUtil.exception.HdAssert;
import fai.hdUtil.json.HdJacksonUtils;
import fai.web.Core;
import fai.web.FileUpload;
import fai.web.ScFileStg;
import fai.web.Web;
import fai.webhdoss.model.vo.scTemplate.*;
import fai.webhdoss.service.ScTemplateService;
import fai.webhdoss.util.ScFileUploadUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ScTemplateServiceImpl implements ScTemplateService {
    @Autowired
    private ScProtoCli scProtoCli;
    @Autowired
    private ScTemplateCli scTemplateCli;
    @Autowired
    private ScResCli scResCli;

    /**
     * 这里有两种情况
     * 1.选择原型返回模版信息（只传protoId）
     * 2.编辑模版（同时传id+protoId）
     **/
    @Override
    public JsonResult getScTemplateInfo(int id, int protoId) {
        // 返回数据
        Param resultInfo = new Param();

        // 先获取原型数据作为基础
        RemoteStandResult standResult = scProtoCli.getScProtoInfoById(protoId);
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "原型不存在，请刷新后重试");

        ScProtoEntity scProtoEntity = standResult.getObject(ScProtoDef.Protocol.Key.INFO, ScProtoEntity.class);
        HdAssert.Biz.judgeNotLog(scProtoEntity == null, "原型不存在，请刷新后重试");

        // 填充常规数据
        resultInfo.setInt(ScTemplateDef.Info.PROTO_ID, scProtoEntity.getId());
        resultInfo.setInt(ScTemplateDef.Info.TYPE, scProtoEntity.getType());
        resultInfo.setString(ScTemplateDef.Info.NAME, scProtoEntity.getName());
        resultInfo.setInt(ScTemplateDef.Info.INDUSTRY, ScTemplateDef.Industry.GENERAL);
        resultInfo.setInt(ScTemplateDef.Info.SCENE, ScTemplateDef.Scene.STORE_PROMOTION);
        resultInfo.setParam(ScTemplateDef.Info.COVER, new Param());
        resultInfo.setString(ScTemplateDef.Info.SYS_KEY_WORDS, "");
        resultInfo.setParam(ScTemplateDef.Info.SETTING, new Param());

        // 视频需要补充视频脚本数据返回
        if (scProtoEntity.getType() == ScProtoDef.Type.VIDEO){
            Param scriptInfo = Param.parseParamNullIsEmpty(scProtoEntity.getScript());
            FaiList<Param> resFormList = FaiList.parseParamList(scProtoEntity.getResForm(), new FaiList<>());
            Param resNameMap = HdMisc.listToParam(resFormList, ScProtoDef.ResForm.ID, ScProtoDef.ResForm.LABEL);
            FaiList<Param> scriptCommList = scriptInfo.getListNullIsEmpty(ScProtoDef.Script.COMM);
            for (Param p : scriptCommList) {
                FaiList<Integer> resIdList = p.getListNullIsEmpty(ScProtoDef.Script.Comm.RES_LIST);
                FaiList<String> resNameList = new FaiList<>();
                for (Integer i : resIdList) {
                    String resName = resNameMap.getString(i + "", "");
                    resNameList.add(resName);
                }

                p.setList(ScProtoDef.Script.Comm.RES_LIST, resNameList);
            }

            resultInfo.setList("scriptCommList", scriptCommList);
        }

        // 同步原型时，获取默认的配置api提示词
        Param configInfo = new Param();
        if (id < 1){
            configInfo = HdConfigUtil.getConfigInfo(ScConfigKey.SC_DEFAULT_AI_PROMPT);
        }

        // 输入数据
        FaiList<Param> inputFormList = FaiList.parseParamList(scProtoEntity.getInputForm(), new FaiList<>());
        FaiList<Param> tmpList = new FaiList<>();
        for (Param p : inputFormList) {
            String p_variable = p.getString(ScProtoDef.InputForm.VARIABLE, "");

            // 拼接数据
            Param tmpInfo = new Param();
            tmpInfo.setString(ScTemplateDef.InputForm.VARIABLE, p_variable);
            tmpInfo.setString(ScTemplateDef.InputForm.LABEL, p.getString(ScProtoDef.InputForm.LABEL, ""));
            tmpInfo.setBoolean(ScProtoDef.InputForm.REQUIRED, p.getBoolean(ScProtoDef.InputForm.REQUIRED, false));
            tmpInfo.setInt(ScProtoDef.InputForm.FILED_TYPE, p.getInt(ScProtoDef.InputForm.FILED_TYPE, ScProtoDef.FiledType.TEXT));
            tmpInfo.setString(ScTemplateDef.InputForm.DESC, "");
            tmpInfo.setString(ScTemplateDef.InputForm.DEFAULT_CONTENT, "");
            tmpInfo.setBoolean(ScTemplateDef.InputForm.OPEN_AI_TIP, false);
            tmpInfo.setString(ScTemplateDef.InputForm.API_KEY, "");
            tmpInfo.setString(ScTemplateDef.InputForm.PROMPT, "");

            // 拼接默认提示词数据
            if (id < 1 && configInfo.containsKey(p_variable)){
                tmpInfo.setBoolean(ScTemplateDef.InputForm.OPEN_AI_TIP, true);
                tmpInfo.setString(ScTemplateDef.InputForm.PROMPT, configInfo.getString(p_variable, ""));
            }

            tmpList.add(tmpInfo);
        }
        resultInfo.setList("inputFormList", tmpList);

        // 脚本数据
        Param scriptInfo = new Param();
        scriptInfo.setString(ScTemplateDef.Script.TITLE, "");
        scriptInfo.setString(ScTemplateDef.Script.CONTENT, "");
        resultInfo.setParam(ScTemplateDef.Info.SCRIPT, scriptInfo);

        // 资源数据
        FaiList<Param> resFormList = FaiList.parseParamList(scProtoEntity.getResForm(), new FaiList<>());
        tmpList = new FaiList<>();
        for (Param p : resFormList) {
            Param tmpInfo = new Param();
            tmpInfo.setInt(ScTemplateDef.ResForm.ID, p.getInt(ScProtoDef.ResForm.ID, 0));
            tmpInfo.setString(ScTemplateDef.ResForm.LABEL, p.getString(ScProtoDef.ResForm.LABEL, ""));
            tmpInfo.setString(ScTemplateDef.ResForm.RES_ID, "");
            tmpInfo.setString(ScTemplateDef.ResForm.COVER_ID, "");
            tmpInfo.setInt(ScTemplateDef.ResForm.COVER_TYPE, 0);
            tmpInfo.setInt(ScTemplateDef.ResForm.RES_TYPE, 0);
            tmpInfo.setString("protoResLabel", p.getString(ScProtoDef.ResForm.LABEL, ""));

            tmpList.add(tmpInfo);
        }
        resultInfo.setList("resFormList", tmpList);

        // 新建同步
        if (id < 1) {
            return JsonResult.success(resultInfo);
        }

        // 编辑
        standResult = scTemplateCli.getScTemplateInfoById(id);
        HdAssert.Biz.judgeNotLog(standResult.getRt() == Errno.NOT_FOUND, "模版不存在，请刷新后重试");
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, "获取模版信息失败，请稍后重试");

        ScTemplateEntity entity = standResult.getObject(ScTemplateDef.Protocol.Key.INFO, ScTemplateEntity.class);
        HdAssert.Biz.judgeNotLog(entity == null, "模版不存在，请稍后重试");

        // 拼接返回的数据
        resultInfo.setInt(ScTemplateDef.Info.ID, entity.getId());
        resultInfo.setString(ScTemplateDef.Info.NAME, entity.getName());
        resultInfo.setInt(ScTemplateDef.Info.INDUSTRY, entity.getIndustry());
        resultInfo.setInt(ScTemplateDef.Info.SCENE, entity.getScene());
        resultInfo.setParam(ScTemplateDef.Info.COVER, Param.parseParamNullIsEmpty(entity.getCover()));
        resultInfo.setString(ScTemplateDef.Info.SYS_KEY_WORDS, entity.getSysKeyWords());
        resultInfo.setParam(ScTemplateDef.Info.SETTING, Param.parseParamNullIsEmpty(entity.getSetting()));
        resultInfo.setString("protoName", scProtoEntity.getName());

        // inputForm 补充旧数据
        FaiList<Param> oldList = FaiList.parseParamList(entity.getInputForm());
        Param oldMap = HdMisc.listToParam(oldList, ScTemplateDef.InputForm.VARIABLE);
        FaiList<Param> baseList = resultInfo.getListNullIsEmpty("inputFormList");
        for (Param p : baseList) {
            String p_variable = p.getString(ScTemplateDef.InputForm.VARIABLE, "");

            // 补充db数据补充
            p.assign(oldMap.getParamNullIsEmpty(p_variable));
        }

        // PM要求模版需要有序
        Param inputMap = ScMisc.listToParam(baseList, ScTemplateDef.InputForm.VARIABLE);
        baseList = new FaiList<>();
        for (Param p : oldList) {
            String p_variable = p.getString(ScTemplateDef.InputForm.VARIABLE, "");

           Param tmpInfo = (Param) inputMap.remove(p_variable);
           if (ScMisc.isEmpty(tmpInfo)){
               continue;
           }

            baseList.add(tmpInfo);
        }
        for (String s : inputMap.keySet()) {
            baseList.add(inputMap.getParam(s));
        }
        resultInfo.setList("inputFormList", baseList);

        // script 补充旧数据
        scriptInfo = Param.parseParamNullIsEmpty(entity.getScript());
        resultInfo.setParam(ScTemplateDef.Info.SCRIPT, scriptInfo);

        // 补充素材数据
        oldList = FaiList.parseParamList(entity.getResForm());
        oldMap = HdMisc.listToParam(oldList, ScTemplateDef.ResForm.ID);
        baseList = resultInfo.getListNullIsEmpty("resFormList");
        for (Param p : baseList) {
            int p_id = p.getInt(ScTemplateDef.ResForm.ID, 0);
            Param tmpInfo = oldMap.getParamNullIsEmpty(p_id + "");

            // 补充db数据补充
            p.assign(tmpInfo);
        }
        resultInfo.setList("resFormList", baseList);

        return JsonResult.success(resultInfo);
    }

    @Override
    public JsonResult getConfInfo() {
        FaiList<Param> industryList = HdMisc.refDefList(ScTemplateDef.Industry.class);
        FaiList<Param> sceneList = HdMisc.refDefList(ScTemplateDef.Scene.class);

        // 获取域名返回
        Param returnInfo = new Param();
        returnInfo.setList("industryList", industryList);
        returnInfo.setList("sceneList", sceneList);

        return JsonResult.success(returnInfo);
    }

    @Override
    public JsonResult setScTemplateInfo(ScTemplateVO vo) {
        int id = vo.getId();

        // 前置校验
        HdAssert.Biz.judgeNotLog(CollectionUtil.isEmpty(vo.getInputFormList()), Errno.ARGS_ERROR, "模版表单不能为空");
        HdAssert.Biz.judgeNotLog(CollectionUtil.isEmpty(vo.getResFormList()), Errno.ARGS_ERROR, "模版素材组不能为空");


        ScTemplateEntity oldEntity = null;
        if (id > 0) {
            // 查询数据，对比apiKey是否一致
            RemoteStandResult standResult = scTemplateCli.getScTemplateInfoById(id);
            HdAssert.Biz.judgeNotLog(standResult.getRt() == Errno.NOT_FOUND, standResult.getRt(), "原型不存在，请刷新后重试");

            oldEntity = standResult.getObject(ScTemplateDef.Protocol.Key.INFO, ScTemplateEntity.class);
        }

        // 资源转换
        switchResInfo(oldEntity, vo);

        // 拼接数据
        ScTemplateEntity entity = new ScTemplateEntity();
        entity.setProtoId(vo.getProtoId());
        entity.setType(vo.getType());
        entity.setName(vo.getName());
        entity.setIndustry(vo.getIndustry());
        entity.setScene(vo.getScene());
        entity.setCover(JSON.toJSONString(vo.getCover()));
        entity.setSysKeyWords(vo.getSysKeyWords());
        entity.setSetting(JSON.toJSONString(vo.getSetting()));
        entity.setInputForm(JSON.toJSONString(vo.getInputFormList()));
        entity.setScript(HdJacksonUtils.toString(vo.getScript()));
        entity.setResForm(JSON.toJSONString(vo.getResFormList()));

        RemoteStandResult remoteStandResult = null;
        if (id > 0) {
            entity.setId(id);
            remoteStandResult = scTemplateCli.setScTemplateInfoById(entity);
        } else {
            entity.setStatus(ScTemplateDef.Status.DELIST);
            remoteStandResult = scTemplateCli.addScTemplateInfo(entity);
        }

        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "保存失败，请稍后重试");

        // 组装数据保存
        return JsonResult.success("保存成功");
    }

    /**
     * 资源转换保存
     *
     * <AUTHOR> 2025/5/14 10:41
     * @Update HLS 2025/5/14 10:41
     **/
    private void switchResInfo(ScTemplateEntity oldEntity, ScTemplateVO vo) {
        // 获取所有的资源id
        Set<String> oldResIdSet = new HashSet<>();
        if (oldEntity != null) {
            oldResIdSet = getEntityRes(oldEntity);
        }

        // 获取用户新增的资源
        Set<String> newResIdSet = new HashSet<>();

        // setting中的资源
        ScTemplateSettingVO setting = vo.getSetting();
        ScTemplateVideoVO video = setting.getVideo();
        if (video != null) {
            newResIdSet.add(video.getVideoId());
            newResIdSet.add(video.getCoverId());
        }

        List<ScTemplateCommResVO> imgInfoList = setting.getImgList();
        if (imgInfoList != null){
            imgInfoList.forEach(p -> {
                newResIdSet.add(p.getResId());
            });
        }

        // 其他资源
        ScTemplateCommResVO cover = vo.getCover();
        if (cover != null) {
            newResIdSet.add(cover.getResId());
        }
        for (ScTemplateResFormVO p : vo.getResFormList()) {
            newResIdSet.add(p.getResId());
            newResIdSet.add(p.getCoverId());
        }

        // 数据对比
        oldResIdSet.remove("");
        newResIdSet.remove("");
        oldResIdSet.remove(null);
        newResIdSet.remove(null);

        Set<String> addSet = new HashSet<>();
        for (String s : newResIdSet) {
            if (Str.isEmpty(s)){
                continue;
            }

            if (oldResIdSet.contains(s)) {
                oldResIdSet.remove(s);
            } else {
                addSet.add(s);
            }
        }

        // 操作数据
        if (!addSet.isEmpty()) {
            RemoteStandResult standResult = scResCli.turnTmpResToNormal(Web.getFaiCid(), new ArrayList<>(addSet));
            HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "新增资源转换失败，请稍后重试");
        }

        if (!oldResIdSet.isEmpty()) {
            RemoteStandResult standResult = scResCli.batchDelRes(Web.getFaiCid(), new ArrayList<>(oldResIdSet));
            HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "删除资源失败，请稍后重试");
        }
    }

    @Override
    public JsonResult getScTemplateList(ScTemplateListVO vo) {
        // 获取参数
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(ScTemplateDef.Info.STATUS, ParamMatcher.NE, ScTemplateDef.Status.DEL);
        HdMisc.setMatcher(vo.getType() >= 0, searchArg.matcher, ScTemplateDef.Info.TYPE, ParamMatcher.EQ, vo.getType());
        HdMisc.setMatcher(vo.getStatus() >= 0, searchArg.matcher, ScTemplateDef.Info.STATUS, ParamMatcher.EQ, vo.getStatus());
        HdMisc.setMatcher(!Str.isEmpty(vo.getCreateTimeStart()) && !Str.isEmpty(vo.getCreateTimeEnd()), searchArg.matcher, ScTemplateDef.Info.CREATE_TIME, ParamMatcher.GE, vo.getCreateTimeStart());
        HdMisc.setMatcher(!Str.isEmpty(vo.getCreateTimeEnd()) && !Str.isEmpty(vo.getCreateTimeStart()), searchArg.matcher, ScTemplateDef.Info.CREATE_TIME, ParamMatcher.LE, vo.getCreateTimeEnd());
        if (!Str.isEmpty(vo.getKeyword()) && !Str.isEmpty(vo.getKey())) {
            // 名称做模糊搜索，其他做精准搜索
            if (ScTemplateDef.Info.NAME.equals(vo.getKey())) {
                searchArg.matcher.and(ScTemplateDef.Info.NAME, ParamMatcher.LK, vo.getKeyword());
            } else if ("all".equals(vo.getKey())) {
                // 全部搜索
                ParamMatcher orPM = new ParamMatcher(ScTemplateDef.Info.NAME, ParamMatcher.LK, vo.getKeyword());
                if (StringUtils.isNumeric(vo.getKeyword())) {
                    orPM.or(ScTemplateDef.Info.PROTO_ID, ParamMatcher.EQ, Integer.valueOf(vo.getKeyword()));
                    orPM.or(ScTemplateDef.Info.ID, ParamMatcher.EQ, Integer.valueOf(vo.getKeyword()));
                }
                searchArg.matcher.and(orPM);
            } else {
                searchArg.matcher.and(vo.getKey(), ParamMatcher.EQ, vo.getKeyword());
            }
        }

        searchArg.start = vo.getOffset();
        searchArg.limit = vo.getPageLimit();
        searchArg.cmpor = new ParamComparator(ScTemplateDef.Info.ID, true);
        searchArg.totalSize = new Ref<>(0);

        RemoteStandResult remoteStandResult = scTemplateCli.getScTemplateList(searchArg, "", "");
        if (remoteStandResult.getRt() == Errno.NOT_FOUND) {
            return JsonResult.success(new FaiList<>());
        }
        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "获取列表失败，请稍后重试");

        List<ScTemplateEntity> list = remoteStandResult.getObject(ScTemplateDef.Protocol.Key.INFO_LIST, List.class);
        int totalSize = remoteStandResult.getObject(ScTemplateDef.Protocol.Key.TOTAL_SIZE, Integer.class);

        // 获取原型信息
        FaiList<Integer> protoIdList = list.stream().map(ScTemplateEntity::getProtoId).collect(Collectors.toCollection(FaiList::new));
        Map<String, String> protoMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(protoIdList)){
            searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(ScProtoDef.Info.ID, ParamMatcher.IN, protoIdList);

            remoteStandResult = scProtoCli.getScProtoList(searchArg, ScProtoDef.Info.ID + "," + ScProtoDef.Info.NAME, "");
            if (remoteStandResult.getRt() == Errno.OK){
                List<ScProtoEntity> protoList = remoteStandResult.getObject(ScProtoDef.Protocol.Key.INFO_LIST, List.class);
                protoMap = protoList.stream().collect(Collectors.toMap(p -> p.getId() + "", ScProtoEntity::getName, (a, b) -> a));
            }
        }

        // 拼接参数
        FaiList<Param> returnList = new FaiList<>();
        for (ScTemplateEntity p : list) {
            Param tmpInfo = new Param();
            tmpInfo.setInt(ScTemplateDef.Info.ID, p.getId());
            tmpInfo.setInt(ScTemplateDef.Info.PROTO_ID, p.getProtoId());
            tmpInfo.setString(ScTemplateDef.Info.TYPE, ScTemplateDef.Type.getName(p.getType()));
            tmpInfo.setString(ScTemplateDef.Info.NAME, p.getName());
            tmpInfo.setInt(ScTemplateDef.Info.STATUS, p.getStatus());
            tmpInfo.setString("statusName", ScTemplateDef.Status.getName(p.getStatus()));
            tmpInfo.setString(ScTemplateDef.Info.INDUSTRY, ScTemplateDef.Industry.getName(p.getIndustry()));
            tmpInfo.setString(ScTemplateDef.Info.SCENE, ScTemplateDef.Scene.getName(p.getScene()));
            tmpInfo.setString(ScTemplateDef.Info.UPDATE_TIME, Parser.parseSecondString(p.getUpdateTime()));
            tmpInfo.setString(ScTemplateDef.Info.CREATE_TIME, Parser.parseSecondString(p.getCreateTime()));

            // 补充原型名称
            tmpInfo.setString("protoName", Optional.ofNullable(protoMap.get(p.getProtoId() + "")).orElse(""));

            returnList.add(tmpInfo);
        }

        JsonResult result = JsonResult.success(returnList);
        result.setTotalSize(totalSize);
        return result;
    }

    @Override
    public JsonResult setScTemplateStatus(int id, int status) {
        // 获取参数
        RemoteStandResult standResult = scTemplateCli.getScTemplateInfoById(id);
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "模版不存在，请刷新重试");

        ScTemplateEntity oldEntity = standResult.getObject(ScTemplateDef.Protocol.Key.INFO, ScTemplateEntity.class);

        // 拼接参数
        ScTemplateEntity entity = new ScTemplateEntity();
        entity.setId(id);
        entity.setStatus(status);

        standResult = scTemplateCli.setScTemplateInfoById(entity);
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, "设置失败，请稍后重试");

        // 如果是删除，则干掉资源
        if (status == ScTemplateDef.Status.DELIST) {
            Set<String> entityRes = getEntityRes(oldEntity);
            if (!entityRes.isEmpty()) {
                standResult = scResCli.batchDelRes(Web.getFaiCid(), new ArrayList<>(entityRes));
                HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "删除资源失败，请稍后重试");
            }
        }

        return JsonResult.success("设置成功");
    }

    /**
     * 获取entity中的资源信息
     *
     * <AUTHOR> 2025/5/15 15:27
     * @Update HLS 2025/5/15 15:27
     **/
    private Set<String> getEntityRes(ScTemplateEntity entity) {
        // 资源转存一份
        Set<String> resIdSet = new HashSet<>();

        // 封面图
        Param coverInfo = Param.parseParamNullIsEmpty(entity.getCover());
        resIdSet.add(coverInfo.getString(ScTemplateDef.Cover.RES_ID, ""));

        // 上传视频/图片资源
        Param newSetting = Param.parseParamNullIsEmpty(entity.getSetting());
        FaiList<Param> imgList = newSetting.getListNullIsEmpty(ScTemplateDef.Setting.IMG_LIST);
        Param videoInfo = newSetting.getParamNullIsEmpty(ScTemplateDef.Setting.VIDEO);
        for (Param p : imgList) {
            resIdSet.add(p.getString(ScTemplateDef.Setting.RES_ID, ""));
        }
        resIdSet.add(videoInfo.getString(ScTemplateDef.Setting.VIDEO_ID, ""));
        resIdSet.add(videoInfo.getString(ScTemplateDef.Setting.COVER_ID, ""));

        // 素材组资源
        FaiList<Param> resFormList = FaiList.parseParamList(entity.getResForm());
        for (Param p : resFormList) {
            resIdSet.add(p.getString(ScTemplateDef.ResForm.RES_ID, ""));
        }

        resIdSet.remove("");
        return resIdSet;
    }

    @Override
    public JsonResult copyScTemplate(int id) {
        // 获取参数
        RemoteStandResult standResult = scTemplateCli.getScTemplateInfoById(id);
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "模版不存在，请刷新重试");

        ScTemplateEntity entity = standResult.getObject(ScTemplateDef.Protocol.Key.INFO, ScTemplateEntity.class);

        ScTemplateEntity newEntity = new ScTemplateEntity();
        newEntity.setName(entity.getName());
        newEntity.setProtoId(entity.getProtoId());
        newEntity.setType(entity.getType());
        newEntity.setIndustry(entity.getIndustry());
        newEntity.setScene(entity.getScene());
        newEntity.setInputForm(entity.getInputForm());
        newEntity.setScript(entity.getScript());
        newEntity.setResForm(entity.getResForm());
        newEntity.setSetting(entity.getSetting());
        newEntity.setCover(entity.getCover());
        newEntity.setSysKeyWords(entity.getSysKeyWords());
        newEntity.setStatus(ScTemplateDef.Status.DELIST);

        standResult = scTemplateCli.addScTemplateInfo(newEntity);
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, "复制失败，请稍后重试");

        // 新增资源
        Set<String> newResIdSet = getEntityRes(entity);
        if (!newResIdSet.isEmpty()) {
            standResult = scResCli.turnTmpResToNormal(Web.getFaiCid(), new ArrayList<>(newResIdSet));
            HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "新增资源转换失败，请稍后重试");
        }

        return JsonResult.success("复制成功");
    }

    @Override
    public JsonResult uploadTmpFile(HttpServletRequest request) throws Exception {
        // 大小限制：100M
        int limitM = 100;

        Log.logStd("hls=== Core.getFlow()[%s];", Core.getFlow());

        Param info = new Param();
        int rt = ScFileUploadUtil.uploadFile4Sc(request.getSession().getServletContext(), request, ScResDef.Type.OPT_TEMPLATE, Web.getFaiCid(), info, limitM);

        return getResult(rt, info);
    }

    private JsonResult getResult(int rt, Param info) {
        JsonResult result = new JsonResult(rt);

        switch (rt) {
            case Errno.OK:
                info.setBoolean("success", true);
                result.setMsg("上传成功");
                result.setData(info);
                return result;
            case FileUpload.ErrnoUpload.FILE_SIZE_LIMIT:
                int limit = info.getInt(FileUpload.Info.LIMIT);
                result.setMsg("上传失败：单个文件的大小超过" + limit + "M");
                return result;
            case FileUpload.ErrnoUpload.IMG_SIZE_LIMIT:
                limit = info.getInt(FileUpload.Info.LIMIT);
                result.setMsg("上传失败：文件大小超过" + limit + "M，请压缩处理后再上传");
                return result;
            case FileUpload.ErrnoUpload.FILE_CTRL_NOT_FOUND:
                result.setMsg("上传失败：请尝试更换您的浏览器");
                return result;
            case FileUpload.ErrnoUpload.FILE_TYPE_INVALID:
                result.setMsg("上传失败：文件格式不合法（只能设置jpg、jpeg、gif、bmp或png格式的图片）");
                return result;
            case FileUpload.ErrnoUpload.FILE_NAME_EXIST:
                result.setMsg("上传失败：文件名已经存在（请修改文件名后再试）");
                return result;
            case FileUpload.ErrnoUpload.FILE_TYPE_FORMAT_ERROR:
                Integer width = info.getInt(FileUpload.Info.WIDTH);
                Integer height = info.getInt(FileUpload.Info.HEIGHT);
                if (width != null && height != null) {
                    result.setMsg("压缩图片错误，建议缩小图片为" + width + "*" + height + "以内");
                    return result;
                } else {
                    result.setMsg("读取图片错误，建议您先使用图像处理软件转换图片格式");
                    return result;
                }
            case FileUpload.ErrnoUpload.FILE_MP3_FORMAT_ERROR:
                result.setMsg("您上传的文件不是标准的mp3格式，请检查后再试");
                return result;
            case FileUpload.ErrnoUpload.FILE_FLV_FORMAT_ERROR:
                result.setMsg("您上传的文件不是标准的flv格式，请检查后再试");
                return result;
            case Errno.SVR_READONLY:
                result.setMsg("系统维护中，暂时不能保存，请稍后重试");
                return result;
            case FileUpload.ErrnoUpload.OVER_LIMIT:
                result.setMsg("上传失败：超过当前网站版本的资源库容量");
                return result;
            default:
                result.setMsg("系统错误 请稍后再试");
                return result;
        }
    }

    @Override
    public JsonResult getDomain() {
        // 获取域名返回
        Param returnInfo = new Param();
        returnInfo.setString("scUsrResOssRoot", ScFileStg.getScFileRoot(Web.getFaiCid(), FileStgDef.App.SC_FILE));

        return JsonResult.success(returnInfo);
    }

    @Override
    public JsonResult advanceUploadFile4Sc(HttpServletRequest request) throws Exception {
        Param info = new Param();
        HdTimer timer = new HdTimer("advanceUploadFile4Sc");
        timer.begin("total");
        int rt = ScFileUploadUtil.advanceUploadFile4Sc(request.getSession().getServletContext(), request, ScResDef.Type.OPT_TEMPLATE, Web.getFaiCid(), info);
        timer.end("total");
        Log.logStd("hls=== timer.getAll()[%s];", timer.getAll());
        return getResult(rt, info);
    }
}
