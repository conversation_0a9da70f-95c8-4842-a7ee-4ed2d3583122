package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description 原型中resForm字段VO
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/17 16:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoResFormVO {
    // 手动自增id
    private Integer id;
    // 素材类型：预留，同外部type，0-视频，1-图片
    private Integer type;
    // 素材名称
    private String label;
    // 是否是必填
    private Boolean required;
    // 是否开启改嘴型功能
    private Boolean modifyMouth;
}
