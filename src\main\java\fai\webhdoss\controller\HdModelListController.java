package fai.webhdoss.controller;


import fai.app.HdModelDef;
import fai.app.HdTradeDef;
import fai.app.StaffDef;
import fai.cli.HdOssAffairCli;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.web.Core;
import fai.web.HdWebDef;
import fai.web.Web;
import fai.web.WebException;
import fai.web.inf.Kid;
import fai.web.inf.Staff;
import fai.webhdoss.WebHdOss;
import fai.webhdoss.model.vo.HdModelListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * 互动模板列表相关
 * <AUTHOR>
 * @date 20210712
 */
@RestController
@Api(value = "互动模板列表")
@RequestMapping("/HdModelList")
public class HdModelListController {

    @Autowired
    @Qualifier(value = "hdModelListSearch")
    List<BiConsumer<HdModelListVO, ParamMatcher>> searchList;

    @Autowired
    HdOssAffairCli hdOssAffairCli;



    /**
     * 互动模板列表删除权限校验
     * <AUTHOR>
     * @date 20210712
     * @return JsonResul
     */
    @CrossOrigin(methods = {RequestMethod.POST,RequestMethod.GET,RequestMethod.OPTIONS})
    @RequestMapping("/DeleteCheck")
    @ApiOperation(value = "互动模板列表删除权限校验", httpMethod = "GET")
    public JsonResult deleteCheck() {
        try {
            int aid = WebHdOss.getSid();
            if (0 == aid) {
                return JsonResult.error("未登录");
            }
            //有删除权限为true，否则为false
            if (Web.getDebug()) {
                return JsonResult.ok(Arrays.asList(425,4294,4326,112).contains(aid));
            }else{
                return JsonResult.ok(Arrays.asList(995).contains(aid));
            }

        } catch (Exception e) {
            return JsonResult.error();
        }
    }

    /**
     * 获取所有设计师
     * <AUTHOR>
     * @date 20211103
     * @return JsonResult
     */
    @CrossOrigin(methods = {RequestMethod.POST,RequestMethod.GET,RequestMethod.OPTIONS})
    @RequestMapping("/getAllDesigner")
    @ApiOperation(value = "获取所有设计师", httpMethod = "GET")
    public JsonResult getAllDesigner() {
        try {

            FaiList<Integer> designerSidList = Web.getConf("oss").getList("authUI");
            Staff oStaff = (Staff) Core.getCorpKit(Web.getFaiCid(), Kid.STAFF);
            FaiList<Param> designerList = oStaff.getStaffList();
            designerList.removeIf(i->!designerSidList.contains(i.getInt(StaffDef.Info.SID)));
            return JsonResult.success(designerList);
        } catch (Exception e) {
            return JsonResult.error();
        }
    }

    /**
     * 获取所有原型
     * <AUTHOR>
     * @date 20211103
     * @return JsonResult
     */
    @GetMapping("/getAllPrototypes")
    @ApiOperation(value = "获取所有原型", httpMethod = "GET")
    public JsonResult getAllPrototypes(){
        try{
            FaiList<Param> hdPortalConf = ConfPool.getConf(HdWebDef.PortalConfDef.PORTAL_CONF_KEY).getList("styleList");

            String json = hdPortalConf.toJson();
            FaiList<Param> prototypes = FaiList.parseParamList(json);
            //第一个不是活动原型
            prototypes.remove(0);

            List<HashMap<String,Object>> resultList = new ArrayList<>();
            for (Param prototype : prototypes) {
                HashMap<String, Object> map = new HashMap<>(2,1);
                map.put("id", prototype.getInt("id",-1));
                FaiList<Param> info = prototype.getList("templateList");
                String styleName = "";
                if(info != null){
                    styleName = info.get(0).getString("name");
                }
                map.put("name", styleName);
                resultList.add(map);
            }

            return JsonResult.success(resultList);
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 获取所有节日
     * <AUTHOR>
     * @date 20211104
     * @return JsonResult
     */
    @GetMapping("/getAllFestival")
    @ApiOperation(value = "获取所有节日", httpMethod = "GET")
    public JsonResult getAllFestival(){
        try{
            return JsonResult.success(HdTradeDef.getNewGroup(HdTradeDef.Id.FESTIVAL_FOR_TWO, false));
        }catch (Exception e){
            return JsonResult.error();
        }
    }


    /**
     * 搜索模板列表
     * <AUTHOR>
     * @date 20211104
     * @return JsonResult
     */
    @PostMapping("/search")
    @ApiOperation(value = "搜索模板列表", httpMethod = "Post")
    public JsonResult search(HdModelListVO hdModelListVO){

        try{
            SearchArg searchArg = new SearchArg();
            ParamMatcher matcher = new ParamMatcher();
            
            //解析查询条件
            searchList.forEach(i->i.accept(hdModelListVO, matcher));
            searchArg.matcher = matcher;
            FaiList<Param> infoList = new FaiList<Param>();
            int rt = hdOssAffairCli.getHdModelList(searchArg, infoList);
            if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
                throw new WebException(rt, "get list error");
            }
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            //转换成前端展示的形式
            for (Param param : infoList) {
                //是否推荐
                String isRecommend = Misc.checkBit(param.getInt(HdModelDef.Info.FLAG), HdModelDef.Flag.ISRECOM)?"是":"否";
                param.setString("isRecommend", isRecommend);
                param.remove(HdModelDef.Info.FLAG);
                //样板属性
                param.setString("property", getPropertyStr(param.getInt(HdModelDef.Info.PROPERTY)));
                //发布时间
                Date pubdate = param.getCalendar("pubdate").getTime();
                param.setString("pubdate", df.format(pubdate));
            }

            return JsonResult.success(infoList);
        }catch (Exception e){
            return JsonResult.error(e.getMessage());
        }
    }

    /**
     * 获取样板属性名称
     */
    public static String getPropertyStr(int property){
        switch (property){
            case HdModelDef.Property.ISPUB:
                return "已发布";
            case HdModelDef.Property.NOPUB:
                return "未发布";
            case HdModelDef.Property.ISMINAPPCARD:
                return "劵宝发布";
            case HdModelDef.Property.IS_JLCJ_APP:
                return "锦鲤抽奖小程序发布";
            default:
                return "";
        }
    }
}
