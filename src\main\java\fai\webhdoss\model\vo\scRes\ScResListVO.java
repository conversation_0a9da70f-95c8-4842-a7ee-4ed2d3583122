package fai.webhdoss.model.vo.scRes;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *  列表查询vo
 * <AUTHOR> 2025/5/28 18:35
 * @Update jachin 2025/5/28 18:35
**/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScResListVO {

    private int type = -1; // 类型

    private int categoryId = -1; // 文件夹id

    private String name = ""; // 名字

    private int pageNo = 1; // 分页

    private int pageLimit = 20; // 分页大小

    private int extraType = -1;

    public Integer getOffset() {
        return (pageNo - 1) * pageLimit;
    }

}
