package fai.webhdoss.service;

import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.comm.util.Ref;
import fai.webhdoss.model.vo.HdCaseFilterVO;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public interface HdCaseFilterService {

    /**
     * 将活动列表的某一个活动导入案例筛选库
     * @param hdCaseFilterVO
     * @return
     */
    String importCaseFilter(HdCaseFilterVO hdCaseFilterVO);

    /**
     * 将案例筛选库的某一个活动导入案例库
     * @param id
     * @return
     */
    String importCase(int id);

    /**
     * 将案例筛选库的某一个活动删除
     * @param id
     * @return
     */
    String deleteCaseFilter(int id);

    /**
     * 编辑案例筛选库的某一个活动
     * @param hdCaseFilterVO
     * @return
     */
    String modifyCaseFilter(HdCaseFilterVO hdCaseFilterVO);


    /**
     * 案例筛选库查询
     * @return
     */
    int getCaseFilterList(Param param, Ref<List> dataListRef, Ref<Integer> totalSizeRef);

    /**
     * 获取筛选案例的活动链接和二维码
     * @param data
     * @return
     */
    String getGameLinkandCode(String data);

    /**
     * 通过id查询案例筛选库
     * @return
     */
    Param getCaseFilterById(int id);

}
