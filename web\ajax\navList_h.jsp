<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.hdUtil.hostConf.HdBaseHostConf" %>
<%!

	private String getSkipUrl(String page) throws Exception{
		return "http://o"+ HdBaseHostConf.getTopHost(Web.getEnvMode()) +"/cs/"+page+".jsp";
	}
	

	private String getTopNav(HttpServletRequest request) throws Exception{

		int sid =  fai.web.Session.getSid();
		HdOss  hdOss = (HdOss)Core.getCorpKit(1,Kid.HD_OSS);
		Param staff = hdOss.getStaff(sid);
		if(staff == null || staff.isEmpty()){
			return "";
		}
		int auth = staff.getInt(HdOssStaffDef.Info.AUTH,0);
		//权限校验
		boolean adm = false;
		boolean hdSaleManage = false;
		boolean hdSaleGroupLeader = false;
		boolean hdSale = false;
		boolean scPM = false;
		boolean scDevelop = false;
		boolean scOperation = false;
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.ALL)){//管理员直接返回true
			adm = true;
		}
		//销售管理
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_MANAGE)  ){
			hdSaleManage = true;
		}
		//销售组长
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER) ){
			hdSaleGroupLeader = true;
		}
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE) ){
			hdSale = true;
		}
		// 速创pm
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_PM) ){
			scPM = true;
		}
		// 速创开发
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_DEVELOP) ){
			scDevelop = true;
		}
		// 速创运营
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_OPERATION) ){
			scOperation = true;
		}
		FaiList<Param> itemList = new FaiList<Param>();
		Param item = null;
		item = new Param();
		item.setString("title", "主页");
		item.setString("url", "index");
		itemList.add(item);
		
		item = new Param();
		item.setString("title", "互动销售");
		item.setString("url", "hdSale");
		itemList.add(item);
		if(adm){
			item = new Param();
			item.setString("title", "互动产品");
			item.setString("url", "hdProduct");
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "互动设计");
			item.setString("url", "hdDesign");
			itemList.add(item);

			item = new Param();
			item.setString("title", "权限设置");
			item.setString("url", "hdAuth");
			itemList.add(item);
		}
		item = new Param();
		item.setString("title", "合作方设置");
		item.setString("url", "hdPartner");
		itemList.add(item);
		if(adm || scDevelop || scPM || scOperation){
			item = new Param();
			item.setString("title", "速创");
			item.setString("url", "scPortal");
			itemList.add(item);
		}
		
		return itemList.toJson();
	}

	private String getLeftNav(HttpServletRequest request) throws Exception{
		int sid =  fai.web.Session.getSid();
		HdOss  hdOss = (HdOss)Core.getCorpKit(1,Kid.HD_OSS);
		Param staff = hdOss.getStaff(sid);
		if(staff == null || staff.isEmpty()){
			return "";
		}
		int auth = staff.getInt(HdOssStaffDef.Info.AUTH,0);
		//权限校验
		boolean adm = false;
		boolean hdSaleManage = false;
		boolean hdSaleGroupLeader = false;
		boolean hdSale = false;
		boolean scPM = false;
		boolean scDevelop = false;
		boolean scOperation = false;
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.ALL)){//管理员直接返回true
			adm = true;
		}
		// 互动销售管理
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_MANAGE)  ){
			hdSaleManage = true;
		}
		// 互动销售组长
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER) ){
			hdSaleGroupLeader = true;
		}
		// 互动销售
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE) ){
			hdSale = true;
		}
		// 速创pm
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_PM) ){
			scPM = true;
		}
		// 速创开发
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_DEVELOP) ){
			scDevelop = true;
		}
		// 速创运营
		if(Misc.checkBit(auth,HdOssStaffDef.Auth.SC_OPERATION) ){
			scOperation = true;
		}

		String menu = Parser.parseString(request.getParameter("menu"), "");
		FaiList<Param> itemList = new FaiList<Param>();
		Param item = new Param();
		item.setString("title", "展开/收起");
		item.setString("icon", "el-icon-menu");
		item.setString("url", "showLeftNav");
		itemList.add(item);
		
		if(menu.equals("hdSale")){
			item = new Param();
			item.setString("title", "企业查询");
			item.setString("icon", "icon-acct");
			item.setString("url", "http://oss."+Web.getPortalDomain()+"/module/cs/corp.jsp");
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "客户列表");
			item.setString("icon", "el-icon-service");
			item.setString("url", "/hdSale/list.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "付款记录");
			item.setString("icon", "el-icon-goods");
			item.setString("url", "/hdSale/payRecord.jsp");
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "汇款确认");
			item.setString("icon", "el-icon-document");
			item.setString("url", getSkipUrl("transConfirm"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "开具发票");
			item.setString("icon", "el-icon-edit");
			item.setString("url", getSkipUrl("addClientInvoicing"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "发票记录");
			item.setString("icon", "el-icon-tickets");
			item.setString("url", getSkipUrl("clientInvoiceList"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "开票记录表");
			item.setString("icon", "el-icon-edit-outline");
			item.setString("url", getSkipUrl("invoice"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "合同制作");
			item.setString("icon", "el-icon-message");
			item.setString("url", getSkipUrl("agreementSet2"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "未支付订单");
			item.setString("icon", "el-icon-sold-out");
			item.setString("url", getSkipUrl("orderList2"));
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "销售arpu");
			item.setString("icon", "el-icon-arrow-up");
			item.setString("url", "/hdSale/saleArpu.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "领取审批");
			item.setString("icon", "el-icon-view");
			item.setString("url", "/hdSale/approve.jsp");
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "添加特殊订单");
			item.setString("icon", "el-icon-sort");
		//	item.setString("url", "http://oss."+Web.getPortalDomain()+"/module/cs/orderAddNew.jsp");
			item.setString("url", "http://o."+Web.getPortalDomain()+"/cs/orderAddNew.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "售前领取记录");
			item.setString("icon", "el-icon-news");
			item.setString("url", "/hdSale/logRecord.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "领取数量查询");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdSale/countStat.jsp");
			itemList.add(item);
			
			item=new Param();
			item.setString("title","活动指导");
			item.setString("icon","el-icon-service");
			item.setString("url","/hdSale/activityGuidance.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "互动跨aid的游戏复制");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdGameCopy.jsp?fromHdOss=true");
			itemList.add(item);

			item = new Param();
			item.setString("title", "互动跨aid的游戏复制数据");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdGameCopyData.jsp");
			itemList.add(item);

			if (hdSaleManage) {
				item = new Param();
				item.setString("title", "能力认证");
				item.setString("icon", "el-icon-setting");
				item.setString("url", "/hdSale/saleCertificate.jsp");
				itemList.add(item);
			}

			if(adm || hdSaleManage){
				item = new Param();
				item.setString("title", "分配资源");
				item.setString("icon", "el-icon-sort");
				item.setString("url", "/hdSale/setConf.jsp");
				itemList.add(item);

				item = new Param();
				item.setString("title", "员工分配设置");
				item.setString("icon", "el-icon-setting");
				item.setString("url", "/hdSale/setCountLimit.jsp");
				itemList.add(item);

				item = new Param();
				item.setString("title", "资源分析");
				item.setString("icon", "el-icon-view");
				item.setString("url", "/hdSale/resourceAnalyze.jsp");
				itemList.add(item);
			}
			if(adm){
				item = new Param();
				item.setString("title", "清除离职员工数据");
				item.setString("icon", "el-icon-zoom-in");
				item.setString("url", "/hdSale/cleanup.jsp");
				itemList.add(item);
				
				
				item = new Param();
				item.setString("title", "adm操作");
				item.setString("icon", "el-icon-search");
				item.setString("url", "/hdSale/cleanupAdm.jsp");
				itemList.add(item);
			}
			
		}else if(menu.equals("hdProduct")){
			item = new Param();
			item.setString("title", "活动列表");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/newGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "互动前端错误码");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdErrorCode.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "互动调查问卷记录");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdInvastigate.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "互动红包功能解封");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdRedPackect.jsp");
			itemList.add(item);

			// item = new Param();
			// item.setString("title", "互动跨aid的游戏复制");
			// item.setString("icon", "icon-binded-domian");
			// item.setString("url", "/hdProduct/hdGameCopy.jsp");
			// itemList.add(item);

			/*
			item = new Param();
			item.setString("title", "发现活动列表");
			item.setString("icon", "el-icon-search");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "直销已购活动列表");
			item.setString("icon", "el-icon-setting");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "分销已购活动列表");
			item.setString("icon", "el-icon-view");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "增加摇周边设备");
			item.setString("icon", "el-icon-goods");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "摇周边设备列表");
			item.setString("icon", "el-icon-document");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "节日主题");
			item.setString("icon", "el-icon-edit");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "直销红包数据汇总");
			item.setString("icon", "el-icon-tickets");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "分销红包数据汇总");
			item.setString("icon", "el-icon-edit-outline");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "直销红包充值派发记录");
			item.setString("icon", "el-icon-message");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "分销红包充值派发记录");
			item.setString("icon", "el-icon-sold-out");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "代理商红包充值派发记录");
			item.setString("icon", "el-icon-sort");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "直销红包领取记录");
			item.setString("icon", "el-icon-news");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "分销红包领取记录");
			item.setString("icon", "el-icon-zoom-in");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "注册7天后送100元现金券促销");
			item.setString("icon", "el-icon-rank");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "门店版咨询客户列表");
			item.setString("icon", "el-icon-phone-outline");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "参赛活动列表");
			item.setString("icon", "el-icon-star-off");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "企业白名单");
			item.setString("icon", "el-icon-printer");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "回电脑版意向调查弹窗");
			item.setString("icon", "el-icon-menu");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "第三方礼品采购记录");
			item.setString("icon", "el-icon-location-outline");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "第三方礼品派发记录");
			item.setString("icon", "el-icon-mobile-phone");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "第三方礼品采购记录(分销)");
			item.setString("icon", "el-icon-info");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "第三方礼品派发记录(分销)");
			item.setString("icon", "el-icon-right");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "查询流量/话费充值状态");
			item.setString("icon", "el-icon-date");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "活动用途数据统计");
			item.setString("icon", "el-icon-time");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "创建活动-广告配置");
			item.setString("icon", "el-icon-bell");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "搜索关键词记录");
			item.setString("icon", "el-icon-edit-outline");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "升级铂金券赠送情况");
			item.setString("icon", "el-icon-edit");
			item.setString("url", "/hdProduct/findGameList.jsp");
			itemList.add(item);*/

			
		}else if(menu.equals("hdDesign")){
			item = new Param();
			item.setString("title", "互动模板列表");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdModelList.jsp");
			itemList.add(item);
			
			item = new Param();
			item.setString("title", "添加互动模板");
			item.setString("icon", "icon-binded-domian");
			item.setString("url", "/hdProduct/hdModelAdd.jsp");
			itemList.add(item);
			
		}else if(menu.equals("hdAuth")){
			item = new Param();
			item.setString("title", "员工权限管理");
			item.setString("icon", "el-icon-setting");
			item.setString("url", "/hdAuth/authConf.jsp");
			itemList.add(item);
		}else if(menu.equals("hdPartner")){
			item = new Param();
			item.setString("title", "开通账户");
			item.setString("icon", "");
			item.setString("url", "/hdPartner/openAccount");
			itemList.add(item);

			item = new Param();
			item.setString("title", "合作方账号列表");
			item.setString("icon", "");
			item.setString("url", "/hdPartner/partnerAccountList");
			itemList.add(item);

			item = new Param();
			item.setString("title", "合作方关联B端账号列表");
			item.setString("icon", "");
			item.setString("url", "/hdPartner/partnerAccountAidList");
			itemList.add(item);

			item = new Param();
			item.setString("title", "客户数据（未注册）");
			item.setString("icon", "");
			item.setString("url", "http://o." + Web.getPortalDomain() + "/union/newUnion/clientList.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "客户数据（已注册）");
			item.setString("icon", "");
			item.setString("url", "http://o." + Web.getPortalDomain() + "/union/newUnion/clientList2.jsp");
			itemList.add(item);

			item = new Param();
			item.setString("title", "消费记录");
			item.setString("icon", "");
			item.setString("url", "http://o." + Web.getPortalDomain() + "/union/newUnion/consumeList.jsp");
			itemList.add(item);
		} else if (menu.equals("scPortal")) {
			if (adm || scPM || scDevelop) {
				item = new Param();
				item.setString("title", "原型配置");
				item.setString("icon", "");
				item.setString("url", "/scPortal/scProto");
				itemList.add(item);
			}

			item = new Param();
			item.setString("title", "模板配置");
			item.setString("icon", "");
			item.setString("url", "/scPortal/scTemplate");
			itemList.add(item);

			if (adm || scPM || scDevelop) {
				item = new Param();
				item.setString("title", "素材管理");
				item.setString("icon", "");
				item.setString("url", "/scPortal/scMaterial");
				itemList.add(item);
	
				item = new Param();
				item.setString("title", "分类管理");
				item.setString("icon", "");
				item.setString("url", "/scPortal/scCategory");
				itemList.add(item);
			}
		}
		return itemList.toJson();
	}

%>

<%
	String output = "";
	try{
		String cmd = Parser.parseString(request.getParameter("cmd"), "");
		
		if (cmd.isEmpty()) {
			out.print("没有找到方法");
			return;
		}
		if(cmd.equals("getTopNav")){
			output = getTopNav(request);
		}
		if(cmd.equals("getLeftNav")){
			output = getLeftNav(request);
		}
	}catch (Exception exp){
		Log.logErr(exp);
		output = "";
	}
	out.print(output);
%>