<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page session="false" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="fai.webhdoss.app.*" %>
<%@ page import="fai.webhdoss.*" %>
<%
    boolean authHdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
    String crmDomain = WebHdOss.getCrmDomain();
    boolean isFromCrm = Parser.parseBoolean(request.getParameter("_formCrm"), false);

%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>转入无效库</title>
    <%@ include file="/comm/link.jsp.inc" %>
    <%@ include file="/comm/script.jsp.inc" %>
    <script src="<%=HdOssResDef.getResPath("js_jquery_min")%>" type="text/javascript" charset="utf-8"></script>
</head>
<body class="saleBox">
<div class="reasonBox">
    <fai-iframe-box class="reasonBox" :submit="onSubmit" :close="onClose">
        <el-form slot="fai-iframe-data" :label-position="labelPosition" label-width="120px">
            <div>
                <el-form-item label="入库类型:">
                    <el-select v-model="remarkForm.invalidType" placeholder="请选择">
                        <el-option
                                v-for="(item, index) in invalidTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="remarkForm.invalidType == 4 || remarkForm.invalidType == 7" label="备注:">
                    <el-input type="textarea" v-model="invalidMark" :rows="4"></el-input>
                </el-form-item>
            </div>
        </el-form>
    </fai-iframe-box>
</div>
<script type="text/javascript">
    document.domain = "<%=Web.getPortalDomain()%>";
    var url = location.search; //获取url中"?"符后的字串
    console.info(url);
    var theRequest = new Object();
    if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
        }
    }
    var aid = theRequest.aid;
    var reasonBox = new Vue({
        el: '.reasonBox',
        data: {
            invalidTypeList: [
                {label: "学生", value: 1},
                {label: "U+资源", value: 2},
                {label: "代理商", value: 3},
                {label: "已换账号购买", value: 4},
                {label: "态度恶劣", value: 5},
                {label: "内部账号", value: 6},
                {label: "其他详见备注", value: 7},
            ],
            invalidMark: "",
            labelPosition: 'right',
            remarkForm: {}
        },
        created: function () {

        },
        methods: {
            onSubmit() {
                console.log(this.remarkForm);
                var that = this;
                if (!that.remarkForm.invalidType || that.remarkForm.invalidType == "空") {
                    this.$message({
                        type: 'warning',
                        message: '请选择入库类型'
                    });
                    return;
                }
                var data = {
                    aid: aid,
                    invalidType: this.remarkForm.invalidType
                }
                if(this.remarkForm.invalidType == 4 || this.remarkForm.invalidType == 7){
                    data["invalidMark"] = this.invalidMark;
                }

                $.ajax({
                    type: "get",
                    external: true,
                    url: '<%=crmDomain%>/ajax/customer_h.jsp?cmd=transToInvalid&callBack=jsonpCallBack',
                    data: data,
                    dataType: "jsonp",
                });
            },
            onClose() {
                parent.closeFaiBoxDailog();
            },
            jsonpResult: function (type, msg) {
                this.$message({
                    type: type,
                    message: msg
                });
            }
        }
    });

    function submitCallBack(result) {
        console.log("submitCallBack", result);
        if(result.success){
            reasonBox.jsonpResult("success", result.msg);
            setTimeout(function () {
                parent.closeFaiBoxDailog(true);
            }, 1000)
        }else {
            reasonBox.jsonpResult('warning', result.msg)
        }
    }
</script>
</body>
</html>
