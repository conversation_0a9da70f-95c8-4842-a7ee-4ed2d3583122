﻿<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.*" %>
<%@ page import="java.math.*" %>
<%@ page import="java.util.regex.Matcher" %>
<%@ page import="java.util.regex.Pattern" %>
<%@ page import="fai.cli.PreSaleUtilCli" %>
<%@ page import="fai.cli.BssStatCli" %>
<%@ page import="fai.cli.*" %>

<%!
    private String getShowIframe(HttpServletRequest request) throws Exception {
		int aid = Parser.parseInt(request.getParameter("aid"), 0);
		boolean isFromCrm = Parser.parseBoolean(request.getParameter("_formCrm"), false);
		String referer = Parser.parseString(Request.getReferer(), "");
		boolean isFaiUrl = OptWebApp.isFaiUrl(referer);
        if (!isFaiUrl) {
            Param info = new Param();
            info.setBoolean("success", false);
            info.setString("msg", "非fkw域名下的请求");
            String call = "callBack(" + info.toJson() + ")";
            return call;
        }

        Param defaultParam = new Param();
        defaultParam.setInt("aid", aid);
        defaultParam.setString("iframeId", "fp-yk-1");
        defaultParam.setBoolean("_formCrm", isFromCrm);
        Url.PageUrl pageUrl = new Url.PageUrl("", defaultParam, WebHdOss.getDomainUrl() + "/ykSale/showYkIframe.jsp", true);

        Param data = new Param();
        data.setString("width", "100%");
        data.setString("height", "80px");
        data.setString("url", pageUrl.url);

        Param data1 = new Param(false);
        data1.setParam("fp-yk-1", data);

        Param info = new Param();
        info.setBoolean("success", true);
        info.setParam("data", data1);
        String call = "callBack(" + info.toJson() + ")";
        return call;
    }

%>


<%
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            return;
        } else if (cmd.equals("getShowIframe")) {
            output = getShowIframe(request);
        }

    } catch (Exception exp) {
        PrintUtil.printStackTrace(exp, 0, "kemuying");
        output = WebOss.checkAjaxException(exp);
    }

    out.print(output);

%>
