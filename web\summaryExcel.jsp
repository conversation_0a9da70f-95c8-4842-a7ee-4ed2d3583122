<%@ page import="fai.app.HdGameDef"%>
<%@ page import="fai.app.HdProfDef" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.hdUtil.PrintUtil" %>
<%@ page import="fai.weboss.WebOss" %>
<%@ page import="org.apache.poi.hssf.usermodel.*" %>
<%@ page import="org.apache.poi.hssf.util.HSSFColor" %>
<%@ page import="org.apache.poi.ss.usermodel.CellStyle" %>
<%@ page import="org.apache.poi.ss.usermodel.Font" %>
<%@ page import="org.apache.poi.ss.usermodel.IndexedColors" %>
<%@ page import="java.io.FileOutputStream" %>
<%@ page import="java.io.IOException" %>
<%@ page import="java.util.*" %>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%!

    private static final String A_SUM_KEY = "aNum";
    private static final String B_SUM_KEY = "bNum";

    private static final String A_COMPANY_TOTAL = "aTotalNum";
    private static final String B_COMPANY_TOTAL = "bTotalNum";

    private static final String A_COMPANY_CREATE_TOTAL = "aCompanyCreateTotalNum";
    private static final String B_COMPANY_CREATE_TOTAL = "bCompanyCreateTotalNum";

    private static final String A_COMPANY_PUB_TOTAL = "aCompanyPubTotalNum";
    private static final String B_COMPANY_PUB_TOTAL = "bCompanyPubTotalNum";

    private static final String A_ACTIVITY_CREATE_TOTAL = "aActivityCreateTotalNum";
    private static final String B_ACTIVITY_CREATE_TOTAL = "bActivityCreateTotalNum";

    private static final String A_ACTIVITY_PUB_TOTAL = "aActivityPubTotalNum";
    private static final String B_ACTIVITY_PUB_TOTAL = "bActivityPubTotalNum";

    private static final List<Integer> storeDrainageAids = new ArrayList<Integer>();


    public static void dealBeforeConvert(HttpServletRequest request,
                                         HttpServletResponse response, JspWriter out,
                                         String fileName, HSSFWorkbook workbook) throws IOException {
        out.clear();
        String agent = request.getHeader("User-Agent");
        agent = agent==null ? "" : agent.toUpperCase();
        response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent, fileName) + "\"");
        response.setContentType("application/vnd.ms-excel");
        workbook.write(response.getOutputStream());
        FileOutputStream outputStream = new FileOutputStream("/home/<USER>/tmp/"+fileName);
        workbook.write(outputStream);
        outputStream.close();
    }

    /**
     * @Description 获取汇总数据Excel表
     * @param isNon 是否是抽奖类
     * @throws IOException
     */
    public static String getDataSummaryExcel(HttpServletRequest request,
                                           HttpServletResponse response, JspWriter out, boolean isNon) throws IOException {
        Log.logDbg("cajrLogDebug ------------------ 开始执行 -----------------");
        Param data = new Param();
        Param param = getCompanyOpenHdNum();
        data.setInt(A_COMPANY_TOTAL, param.getInt(A_SUM_KEY));
        data.setInt(B_COMPANY_TOTAL, param.getInt(B_SUM_KEY));

        param = selectCreatedCompanyNum(isNon);
        data.setInt(A_COMPANY_CREATE_TOTAL, param.getInt(A_SUM_KEY));
        data.setInt(B_COMPANY_CREATE_TOTAL, param.getInt(B_SUM_KEY));

        param = selectPubCompanyNum(isNon);
        data.setInt(A_COMPANY_PUB_TOTAL, param.getInt(A_SUM_KEY));
        data.setInt(B_COMPANY_PUB_TOTAL, param.getInt(B_SUM_KEY));

        param = selectCreatedActivityNum(isNon);
        data.setInt(A_ACTIVITY_CREATE_TOTAL, param.getInt(A_SUM_KEY));
        data.setInt(B_ACTIVITY_CREATE_TOTAL, param.getInt(B_SUM_KEY));

        param = selectPublishedActivityNum(isNon);
        data.setInt(A_ACTIVITY_PUB_TOTAL, param.getInt(A_SUM_KEY));
        data.setInt(B_ACTIVITY_PUB_TOTAL, param.getInt(B_SUM_KEY));
        getExcel(request, response, out, data, isNon);
        Log.logDbg("cajrLogDebug ------------------ 执行结束 -----------------");
        return "success";
    }

    /**
     * @Description 获取时间内的企业数
     * @return
     */
    public static Param getCompanyOpenHdNum(){
        long start = System.currentTimeMillis();
        Param param = selectBySql();
        long end = System.currentTimeMillis();
        Log.logDbg("cajrLogDebug getCompanyOpenHdNum耗时 -> "+(end-start));
        return param;
    }

    private static Param selectCreatedCompanyNum(boolean isNon){
        long start = System.currentTimeMillis();
        Param param = selectNum(isNon, false, true);
        long end = System.currentTimeMillis();
        Log.logDbg("cajrLogDebug selectCreatedCompanyNum耗时 -> "+(end-start));
        return param;
    }

    private static Param selectPubCompanyNum(boolean isNon) {
        long start = System.currentTimeMillis();
        Param param = selectNum(isNon, true, true);
        long end = System.currentTimeMillis();
        Log.logDbg("cajrLogDebug selectPubCompanyNum 耗时 -> "+(end-start));
        return param;
    }

    private static Param selectCreatedActivityNum(boolean isNon){
        long start = System.currentTimeMillis();
        Param param = selectNum(isNon, false, false);
        long end = System.currentTimeMillis();
        Log.logDbg("cajrLogDebug selectCreatedActivityNum 耗时 -> "+(end-start));
        return param;
    }

    private static Param selectPublishedActivityNum(boolean isNon){
        long start = System.currentTimeMillis();
        Param param = selectNum(isNon, true, false);
        long end = System.currentTimeMillis();
        Log.logDbg("cajrLogDebug selectPublishedActivityNum 耗时 -> "+(end-start));
        return param;
    }

    private static Param selectBySql(){
        Param resultParam = new Param();
        resultParam.setInt(A_SUM_KEY, 0);
        resultParam.setInt(B_SUM_KEY, 0);
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdProf";
        selectArg.field = "aid";
        SearchArg searchArg = new SearchArg();
        Calendar calendar = Parser.parseCalendar("2020-03-26 00:00:00", "yyyy-MM-dd HH:mm:ss");
        ParamMatcher matcher = new ParamMatcher(HdProfDef.Info.CREATE_TIME, ParamMatcher.GE, calendar);
        calendar = Parser.parseCalendar("2020-04-13 23:59:59", "yyyy-MM-dd HH:mm:ss");
        matcher.and(HdProfDef.Info.CREATE_TIME, ParamMatcher.LE, calendar);
        matcher.and(HdProfDef.Info.AGENT_AID, ParamMatcher.EQ, "0");
        matcher.and(HdProfDef.Info.DIRECTPURPOSE, ParamMatcher.EQ, HdProfDef.Directpurpose.STORE_DRAINAGE);
        searchArg.matcher = matcher;
        selectArg.searchArg = searchArg;
        Dao dao = WebOss.getSlaveHdDao();
        try{
            FaiList<Param> params = dao.select(selectArg);
            if (isEmpty(params)){
                return resultParam;
            }
            for (Param p : params) {
                storeDrainageAids.add(p.getInt(HdProfDef.Info.AID));
                if (p.getInt(HdProfDef.Info.AID) % 2 == 0){
                    resultParam.setInt(A_SUM_KEY, resultParam.getInt(A_SUM_KEY)+1);
                }else {
                    resultParam.setInt(B_SUM_KEY, resultParam.getInt(B_SUM_KEY)+1);
                }
            }
        }finally {
            dao.close();
        }
        return resultParam;
    }

    private static Param selectNum(boolean isNon, boolean isPublished, boolean isDeduplication) {
        Param resultParam = new Param();
        resultParam.setInt(A_SUM_KEY, 0);
        resultParam.setInt(B_SUM_KEY, 0);
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdProf";
        selectArg.field = "aid, createTime";
        SearchArg searchArg = new SearchArg();
        Calendar calendar = Parser.parseCalendar("2020-03-26 00:00:00", "yyyy-MM-dd HH:mm:ss");
        ParamMatcher matcher = new ParamMatcher(HdProfDef.Info.CREATE_TIME, ParamMatcher.GE, calendar);
        calendar = Parser.parseCalendar("2020-04-13 23:59:59", "yyyy-MM-dd HH:mm:ss");
        matcher.and(HdProfDef.Info.CREATE_TIME, ParamMatcher.LE, calendar);
        searchArg.matcher = matcher;
        selectArg.searchArg = searchArg;
        Dao dao = WebOss.getSlaveHdDao();
        try {
            List<Param> params = dao.select(selectArg);
            FaiList<Integer> aids = new FaiList<Integer>();
            for (Param param : params) {
                aids.add(param.getInt(HdGameDef.Info.AID));
            }
            List<Param> publishedGames = selectPublishedGame(dao, aids, isNon, isPublished);
            publishedGames = isPublished?getPublishedGames(publishedGames) : publishedGames;
            if (isDeduplication){
                deduplication(params, publishedGames, resultParam);
            } else {
                unDeduplication(params, publishedGames, resultParam);
            }
        }finally {
            dao.close();
        }
        return resultParam;
    }

    private static List<Param> getPublishedGames(List<Param> publishedGames) {
        List<Param> params = new ArrayList<Param>();
        if (!isEmpty(publishedGames)){
            for (Param p : publishedGames) {
                int flag = p.getInt(HdGameDef.Info.FLAG);
                if ((flag & HdGameDef.Flag.PUBLISH) == HdGameDef.Flag.PUBLISH){
                    params.add(p);
                }
            }
        }
        return params;
    }

    private static void deduplication(List<Param> params, List<Param> publishedGames, Param resultParam){
        Set<Integer> aSet = new HashSet<Integer>();
        Set<Integer> bSet = new HashSet<Integer>();
        for (Param param : params){
            for (Param param1 : publishedGames){
                Calendar calendar = param.getCalendar(HdProfDef.Info.CREATE_TIME);
                calendar.add(Calendar.DATE, 7);
                if (param1.getInt(HdProfDef.Info.AID).equals(param.getInt(HdGameDef.Info.AID)) &&
                        param1.getCalendar(HdGameDef.Info.CREATE_TIME).before(calendar)){
                    if (storeDrainageAids.contains(param.getInt(HdGameDef.Info.AID))){
                        if (param.getInt(HdGameDef.Info.AID)%2 == 0){
                            aSet.add(param.getInt(HdGameDef.Info.AID));
                        } else {
                            bSet.add(param.getInt(HdGameDef.Info.AID));
                        }
                    }
                }
            }
        }
        resultParam.setInt(A_SUM_KEY, aSet.size());
        resultParam.setInt(B_SUM_KEY, bSet.size());
    }

    private static void unDeduplication(List<Param> params, List<Param> publishedGames, Param resultParam){
        for (Param param : params){
            for (Param param1 : publishedGames){
                Calendar calendar = param.getCalendar(HdGameDef.Info.CREATE_TIME);
                calendar.add(Calendar.DATE, 7);
                if (param1.getInt(HdProfDef.Info.AID).equals(param.getInt(HdGameDef.Info.AID)) &&
                        param1.getCalendar(HdProfDef.Info.CREATE_TIME).before(calendar)){
                    if (storeDrainageAids.contains(param.getInt(HdGameDef.Info.AID))){
                        if (param.getInt(HdGameDef.Info.AID)%2 == 0){
                            resultParam.setInt(A_SUM_KEY, resultParam.getInt(A_SUM_KEY)+1);
                        } else {
                            resultParam.setInt(B_SUM_KEY, resultParam.getInt(B_SUM_KEY)+1);
                        }
                    }
                }
            }
        }
    }

    private static List<Param> selectPublishedGame(Dao dao, FaiList<Integer> aids, boolean isNon, boolean isPublished){
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdGame";
        selectArg.field = "aid, createTime, flag";
        SearchArg searchArg = new SearchArg();
        Calendar calendar = Parser.parseCalendar("2020-04-13 23:59:59", "yyyy-MM-dd HH:mm:ss");
        ParamMatcher matcher = new ParamMatcher(HdGameDef.Info.CREATE_TIME, ParamMatcher.LE, calendar);
        matcher.and(HdGameDef.Info.AID, ParamMatcher.IN, aids);
        if (isNon){
            matcher.and(HdGameDef.Info.GAMETYPE, ParamMatcher.EQ, 0);
        } else {
            matcher.and(HdGameDef.Info.GAMETYPE, ParamMatcher.NE, 0);
        }
        searchArg.matcher = matcher;
        selectArg.searchArg = searchArg;
        return dao.select(selectArg);
    }

    /**
     * @Description 生成获取Excel表格
     * @param param 数据参数
     * @param isNon 是否活动抽奖
     * @throws IOException
     */
    public static void getExcel(HttpServletRequest request,
                                HttpServletResponse response, JspWriter out, Param param, boolean isNon) throws IOException {
        String fileName = isNon+"_DataSummary_"+System.currentTimeMillis()+".xlsx";
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        HSSFRow row =sheet.createRow(0);
        HSSFCell cell = row.createCell(0);
        //初始化一张没有数据的Excel表
        initExcel(workbook, sheet, row, cell, isNon);
        // 256*width+184
        sheet.setColumnWidth(0,  256*50+184);
        sheet.setColumnWidth(1,  256*50+184);
        sheet.setColumnWidth(2,  256*50+184);

        //填入数据
        insertData(param, sheet);
        dealBeforeConvert(request, response, out, fileName, workbook);
    }

    private static void initExcel(HSSFWorkbook workbook, HSSFSheet sheet, HSSFRow row, HSSFCell cell, boolean isNon){
        row.setHeightInPoints(20);
        cell.setCellValue("查询条件");
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex());

        row = sheet.createRow(1);
        cell = row.createCell(0);
        cell.setCellValue("互动开通时间：2020/3/26-2020/4/13");
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex());
        row.setHeightInPoints(20);

        row = sheet.createRow(2);
        cell = row.createCell(0);
        cell.setCellValue("活动开通时间：2020/3/26-2020/4/13");
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex());
        row.setHeightInPoints(20);

        row = sheet.createRow(3);
        cell = row.createCell(0);
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex());
        //对
        setRedFontStyle(workbook, cell, isNon);

        row.setHeightInPoints(20);

        row = sheet.createRow(4);
        cell = row.createCell(0);
        cell.setCellValue("限制注册来源组为活动引流");
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex());
        row.setHeightInPoints(20);


        //初始化企业数表
        initCompanyNumTable(workbook, sheet, row, cell);
        //初始化活动数表
        initActivityNumTable(workbook, sheet, row, cell);
    }

    private static void initCompanyNumTable(HSSFWorkbook workbook, HSSFSheet sheet, HSSFRow row, HSSFCell cell){
        row = sheet.createRow(7);
        //第一列
        cell = row.createCell(0);
        cell.setCellValue("企业数");
        createABCol(workbook, row, cell);

        row = sheet.createRow(8);
        cell = row.createCell(0);
        cell.setCellValue("互动开通量");
        create2LemonCol(workbook, row, cell);

        row = sheet.createRow(9);
        cell = row.createCell(0);
        cell.setCellValue("互动开通7日内创建量");
        create2LemonCol(workbook, row, cell);

        row = sheet.createRow(10);
        cell = row.createCell(0);
        cell.setCellValue("互动开通7日内发布量");
        create2LemonCol(workbook, row, cell);
    }

    private static void initActivityNumTable(HSSFWorkbook workbook, HSSFSheet sheet, HSSFRow row, HSSFCell cell){
        row = sheet.createRow(12);
        //第一列
        cell = row.createCell(0);
        cell.setCellValue("活动数");
        createABCol(workbook, row, cell);

        row = sheet.createRow(13);
        cell = row.createCell(0);
        cell.setCellValue("互动开通7日内创建的活动量");
        create2LemonCol(workbook, row, cell);

        row = sheet.createRow(14);
        cell = row.createCell(0);
        cell.setCellValue("互动开通7日内发布的活动量");
        create2LemonCol(workbook, row, cell);
    }

    private static void setStyle(HSSFWorkbook workbook, HSSFCell cell, short bColor){
        setStyle(workbook, cell, bColor, false);
    }

    private static void create2LemonCol(HSSFWorkbook workbook, HSSFRow row, HSSFCell cell){
        setStyle(workbook, cell, IndexedColors.CORNFLOWER_BLUE.getIndex());
        //第二列
        cell = row.createCell(1);
        setStyle(workbook, cell, IndexedColors.LEMON_CHIFFON.getIndex(), true);
        //第三列
        cell = row.createCell(2);
        setStyle(workbook, cell, IndexedColors.LEMON_CHIFFON.getIndex(), true);
        row.setHeightInPoints(20);
    }

    private static void createABCol(HSSFWorkbook workbook, HSSFRow row, HSSFCell cell){
        setStyle(workbook, cell, IndexedColors.WHITE.getIndex(), true);
        //第二列
        cell = row.createCell(1);
        cell.setCellValue("A类");
        setStyle(workbook, cell, IndexedColors.CORNFLOWER_BLUE.getIndex(), true);
        //第三列
        cell = row.createCell(2);
        cell.setCellValue("B类");
        setStyle(workbook, cell, IndexedColors.CORNFLOWER_BLUE.getIndex(), true);
        row.setHeightInPoints(20);
    }

    private static void setStyle(HSSFWorkbook workbook, HSSFCell cell, short backgroundColor, boolean isFontCenter){
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(backgroundColor);
        cellStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
        HSSFFont font = workbook.createFont();
        font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        font.setFontHeightInPoints((short) 12);
        if (isFontCenter) {
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        }
        cellStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN);
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);
        cellStyle.setFont(font);
        cell.setCellStyle(cellStyle);
    }

    private static void setRedFontStyle(HSSFWorkbook workbook, HSSFCell cell, boolean isNon){
        String msg;
        if (!isNon){
            msg = "活动类型：非【游戏抽奖】【抽奖活动】";
        } else {
            msg = "活动类型：【游戏抽奖】【抽奖活动】";
        }
        HSSFRichTextString richTextString = new HSSFRichTextString(msg);
        HSSFFont font = workbook.createFont();
        font.setBoldweight(Font.BOLDWEIGHT_BOLD);
        font.setFontHeightInPoints((short) 12);
        richTextString.applyFont(font);
        if (isNon){
            font = workbook.createFont();
            font.setColor(HSSFColor.RED.index);
            font.setBoldweight(Font.BOLDWEIGHT_BOLD);
            font.setFontHeightInPoints((short) 12);
            richTextString.applyFont(5, 6, font);
        }
        cell.setCellValue(richTextString);
    }

    private static void insertData(Param param, HSSFSheet sheet){
        sheet.getRow(8).getCell(1).setCellValue(param.getInt(A_COMPANY_TOTAL));
        sheet.getRow(8).getCell(2).setCellValue(param.getInt(B_COMPANY_TOTAL));

        sheet.getRow(9).getCell(1).setCellValue(param.getInt(A_COMPANY_CREATE_TOTAL));
        sheet.getRow(9).getCell(2).setCellValue(param.getInt(B_COMPANY_CREATE_TOTAL));

        sheet.getRow(10).getCell(1).setCellValue(param.getInt(A_COMPANY_PUB_TOTAL));
        sheet.getRow(10).getCell(2).setCellValue(param.getInt(B_COMPANY_PUB_TOTAL));

        sheet.getRow(13).getCell(1).setCellValue(param.getInt(A_ACTIVITY_CREATE_TOTAL));
        sheet.getRow(13).getCell(2).setCellValue(param.getInt(B_ACTIVITY_CREATE_TOTAL));

        sheet.getRow(14).getCell(1).setCellValue(param.getInt(A_ACTIVITY_PUB_TOTAL));
        sheet.getRow(14).getCell(2).setCellValue(param.getInt(B_ACTIVITY_PUB_TOTAL));

    }
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
%>

<%
    String cmd = request.getParameter("cmd");
    if (cmd == null) {
        return;
    }
    try {
        if ("true".equals(cmd)){
            out.print(getDataSummaryExcel(request, response, out, true));
            return;
        }
        if ("false".equals(cmd)){
            out.print(getDataSummaryExcel(request, response, out, false));
        }
    }catch (Exception e){
        PrintUtil.printStackTrace(e, 1, "gameDataSummaryExcel");
    }
%>