<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	/*if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}*/
	int investigateId = Parser.parseInt(request.getParameter("investigateId"),0);
	if( investigateId == 0 ){
		out.println("没有找到该问卷!");
		return;
	}
	Param exposeParam = new Param();
	{
		exposeParam.setInt( "coupType",investigateId );
	}
%>

<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>投票详情</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdProduct")%>"/>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_invastiGateInfo")%>"/>
    </head>

    <body>	
		<div id="investigateInfo"
			v-loading="isLoading"
			element-loading-text="拼命加载中"
			element-loading-spinner="el-icon-loading"
			element-loading-background="rgba(0, 0, 0, 0.8)"
		>
			<!--表单-->
			<div class="menuBox">
				<el-form :inline="true" :model="formQuery" ref="formQuery" class="invastigate">
					<div class="otherBlock">
						<el-form-item prop="aid" >
							<span class="menuBox_span">aid</span>
							<el-input type="text" v-model.number="formQuery.aid" placeholder="请输入你要查询的aid"></el-input>
						</el-form-item>
					</div>
					<div class="otherBlock">
						<span class="menuBox_span">提交答卷时间</span>
						<el-date-picker
							v-model="formQuery.startTime"
							type="datetime"
							placeholder="选择开始时间"
							value-format="timestamp">
						</el-date-picker>
						<span>至</span>
						<el-date-picker
							v-model="formQuery.endTime"
							type="datetime"
							placeholder="选择结束时间"
							value-format="timestamp">
						</el-date-picker>
						<el-button type="primary" @click="searchInvasi('formQuery')" icon="el-icon-search" class="searchBtn">查询</el-button>
					</div>
					<div class="otherBlock">
						<span class="menuBox_span">是否互动付费版本</span>
						<template>
							<el-select v-model="formQuery.isPayVersion" placeholder="请选择">
								<el-option
								v-for="item in options"
								:key="item.value"
								:label="item.label"
								:value="item.value">
								</el-option>
							</el-select>
						</template>
						<el-button type="primary" plain @click="downLoadInfo('formQuery')" icon="el-icon-download" class="searchBtn">导出</el-button>
					</div>
				</el-form>
			</div>

			<div class="showTable">
				<template>
					<el-table 
						:data="tableData" 
						border
						style="width: 100%" 
						v-if="type == 3"
						>
						<el-table-column align="center" prop="aid" label="aid" width="80">
						</el-table-column>
						<el-table-column align="center" prop="hostUnit" label="主办单位" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regCity" label="注册城市" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regProvince" label="注册省份" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regArea" label="注册区域" width="200">
						</el-table-column>
						<el-table-column align="center" prop="openCompanyGoal" label="开通用途" width="100">
						</el-table-column>
						<el-table-column align="center" prop="isPayVersion" label="是否互动付费版本" width="60">
						</el-table-column>
						<el-table-column align="center" prop="ta" label="注册来源" width="60">
						</el-table-column>
						<el-table-column align="center" prop="taGroup" label="注册来源组" width="60">
						</el-table-column>
						<el-table-column align="center" prop="regBiz" label="注册产品" width="100">
						</el-table-column>
						<el-table-column align="center" prop="ctime" label="提交答卷时间" width="200">
						</el-table-column>
						<el-table-column align="center" prop="useTime" label="用时" width="60">
						</el-table-column>
						<el-table-column align="center" prop="attenInfo1" label="第一道题目(1)" width="80">
						</el-table-column>
						<el-table-column align="center" prop="attenInfo2" label="第一道题目(2)" width="80">
						</el-table-column>
						<el-table-column align="center" prop="attenInfo3" label="第一道题目(3)" width="80">
						</el-table-column>
						<el-table-column align="center" prop="profession1" label="第二道题目" width="80">
						</el-table-column>
						<el-table-column align="center" prop="sexInfo" label="第三道题目" width="80">
						</el-table-column>
						<el-table-column align="center" prop="ageInfo" label="第四道题目" width="80">
						</el-table-column>
						<el-table-column align="center" prop="studyInfo" label="第五道题目" width="100">
						</el-table-column>
						<el-table-column align="center" prop="accountBelong" label="第六道题目" width="80">
						</el-table-column>
						<el-table-column align="center" prop="assitance" label="第七道题目" width="80">
						</el-table-column>
						<el-table-column align="center" prop="company" label="第八道题目" width="80">
						</el-table-column>
					</el-table>
					<el-table 
						:data="tableData" 
						border
						style="width: 100%" 
						v-if="type == 4 || type == 5 || type == 7 || type == 8 || type == 9">
						<el-table-column align="center" prop="aid" label="aid" width="80">
						</el-table-column>
						<el-table-column align="center" prop="hostUnit" label="主办单位" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regCity" label="注册城市" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regProvince" label="注册省份" width="200">
						</el-table-column>
						<el-table-column align="center" prop="regArea" label="注册区域" width="200">
						</el-table-column>
						<el-table-column align="center" prop="openCompanyGoal" label="开通用途" width="100">
						</el-table-column>
						<el-table-column v-if="type == 4 || type == 5" align="center" prop="isPayVersion" label="是否互动付费版本" width="60">
						</el-table-column>
						<el-table-column align="center" prop="ta" label="注册来源" width="60">
						</el-table-column>
						<el-table-column align="center" prop="taGroup" label="注册来源组" width="60">
						</el-table-column>
						<el-table-column align="center" prop="regBiz" label="注册产品" width="100">
						</el-table-column>
						<el-table-column v-if="type == 4" align="center" prop="registerReason" label="注册原因" width="100">
						</el-table-column>
						<el-table-column v-if="type == 5" align="center" prop="back2PcResearch" label="不回电脑版原因" width="100">
						</el-table-column>

						<el-table-column v-if="type == 7" align="center" prop="ctime" label="点击图标时间" width="160">
						</el-table-column>
						<el-table-column v-if="type == 7" align="center" prop="ip" label="点击时ip" width="130">
						</el-table-column>

						<el-table-column v-if="type == 8" align="center" prop="recommendHd" label="推荐分数" width="160">
						</el-table-column>
						<el-table-column v-if="type == 8" align="center" prop="notRecommendReason" label="原因" width="130">
						</el-table-column>

						<el-table-column v-if="type == 8" align="center" prop="notRecommendReasonType" label="建议类型" width="130">
						</el-table-column>

						<el-table-column v-if="type == 9" align="center" prop="account" label="凡科账号" width="160">
						</el-table-column>
						<el-table-column v-if="type == 9" align="center" prop="phone" label="手机号" width="130">
						</el-table-column>
						<el-table-column v-if="type == 9" align="center" prop="bodyNum" label="主体数量" width="160">
						</el-table-column>
						<el-table-column v-if="type == 9" align="center" prop="mark" label="备注" width="130">
						</el-table-column>
					</el-table>
				</template>
				<div>
					<el-pagination
					@size-change="changeSize"
					@current-change="chagePageInfo"
					:page-sizes="pageInfo.pageArray"
					:page-size="pageInfo.pageSize"
					layout="total, prev, pager, next, sizes"
					:total="pageInfo.total">
					</el-pagination>
				</div>
			</div>
		</div>	
    </body>
	<script type="text/javascript" charset="utf-8">
		var exposeParam = <%=exposeParam%>;
	</script>
	<script src='<%=HdOssResDef.getResPath("js_invastiGateInfo")%>' type="text/javascript" charset="utf-8"></script>
</html>