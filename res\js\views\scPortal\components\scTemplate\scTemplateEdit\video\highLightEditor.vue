<template>
  <div class="border-[1px] border-gray-300 rounded-[4px] p-[10px] w-[1000px]">
    <select @change="onTagChange" class="w-[280px] border-[1px] border-gray-300 rounded-[4px] p-[5px]">
      <option disabled selected>请选择高亮标签</option>
      <option v-for="item in inputFormList" :key="item.variable"  :value="item.variable">{{ item.label }}</option>
    </select>
    <el-button size="small" @click="clearTag">清除高亮标签</el-button>
    <p class="text-[12px] text-[#999]">选中指定文本后，再选择高亮标签，进行高亮设置，或点击清除标签取消高亮设置</p>
    <div class="mt-[10px] highlight-editor-content">
      <editor-content :editor="editor" />
    </div>
  </div>
</template>

<script>
import { Editor, EditorContent } from "@tiptap/vue-2";
import StarterKit from "@tiptap/starter-kit";
import { TagMark } from '@/views/scPortal/extensions/tiptap.js';

/**
 * @see link {https://tiptap.dev/docs/editor|tiptap 富文本编辑器文档}
 */
export default {
  name: "HighLightEditor",
  components: {
    EditorContent,
  },
  props: {
    inputFormList: {
      type: Array,
      default: () => [],
    },
    contentHtml: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      editor: null,
    };
  },
  computed: {
  },
  mounted() {
    this.initEditor()
  },
  beforeDestroy() {
    this.editor.destroy();
  },
  methods: {
    initEditor() {
      const _this = this;
      this.editor = new Editor({
        content: this.contentHtml,
        extensions: [
          StarterKit.configure({
            // 禁用可能影响自定义标签的扩展
            heading: false,
            codeBlock: false,
            blockquote: false,
            horizontalRule: false,
            // 允许所有HTML标签
            htmlAttributes: {
              allow: ['*']
            },
            // 保留空格
            preserveWhitespace: true,
            // 添加以下配置
            whitespace: 'pre-wrap'
          }), 
          TagMark
        ],
        editorProps: {
          attributes: {
            // 添加 white-space 样式
            style: 'white-space: pre-wrap;'
          }
        },
        onUpdate({ editor }) {
          let html = editor.getHTML()
          // 替换多个空格为 &nbsp;（注意保留换行）
          html = html.replace(/  /g, '&nbsp;&nbsp;')
          console.log(html, 'editor-html');
          _this.$emit('update:contentHtml', html)
        },
      });
    },
    /**
     * 高亮标签选择
     */
    onTagChange(event) {
      const tagType = event.target.value;
      if (!tagType) return;

      const labelText = this.inputFormList.find(item => item.variable === tagType).label;
      const { state } = this.editor;
      const { from, to } = state.selection;
      
      if (from === to) return; // 没有选中文本时不执行
      this.editor.commands.setMark('tag', {
        tagType,
        label: labelText,
      })
      event.target.selectedIndex = 0;
      console.log(event.target.value);
    },
    clearTag() {
      this.editor.commands.unsetTag()
    },
    getEditorContent() {
      console.log(this.editor, 'editor-html');
      return this.editor?.getHTML();
    }
  },
};
</script>

<style>
.script-example-tag {
  padding: 2px 4px;
  margin: 0 1px;
  color: #3261FD;
}

/* 添加标签样式 */
.script-example-tag-label {
  display: inline-block;
  border-radius: 4px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  padding: 0 4px;
  line-height: 20px;
  margin-right: 2px;
  user-select: none;
}

/* 添加一个伪元素作为可点击区域 */
.script-example-tag-label::after {
  content: '';
  position: absolute;
  right: -2px;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: text;
}

.highlight-editor-content {
  background: #f3f3f5;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  line-height: 25px;
}

.tag-container {
  display: inline-block;
  position: relative;
}

.tag-cursor-placeholder {
  display: inline-block;
  width: 0;
  height: 0;
  position: relative;
  z-index: 1;
}
</style>
