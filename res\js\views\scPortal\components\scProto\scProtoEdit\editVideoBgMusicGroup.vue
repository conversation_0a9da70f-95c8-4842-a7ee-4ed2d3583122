<template>
  <div class="mt-[40px]">
    <p class="mb-[10px] text-[16px] font-bold">背景音乐配置</p>
    <div class="edit-video-bg-music-group">
      <div class="bgm-tree-container">
        <el-tree ref="bgmTree" :data="bgmTreeData" node-key="id" :default-checked-keys="defaultCheckList" show-checkbox @check-change="handleCheckChange"></el-tree>
      </div>
    </div>
  </div>
</template>

<script>  
import { getBgmTree } from '@/views/scPortal/api/scProto.js';
export default {
  data() {
    return {
      bgmTreeData: [],
    };
  },
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    defaultCheckList() {
      return this.protoInfo.bgmList || [];
    }
  },
  created() {
    this.getBgmTree();
  },
  methods: {
    getBgmTree() {
      getBgmTree().then(res => {
        if (res.success) {
          if (res.data?.length) {
            let bgmTreeList = []
            for (let i = 0; i < res.data.length; i++) {
              let curTree = {
                label: res.data[i].name,
                value: res.data[i].id,
              }
              if (res.data[i]?.bgmList?.length) {
                curTree.children = res.data[i].bgmList.map(item => ({
                  label: item.name,
                  value: item.resId,
                  resId: item.resId,
                }))
              }
              bgmTreeList.push(curTree);
            }
            this.bgmTreeData = bgmTreeList
            console.log( this.bgmTreeData, ' this.bgmTreeData');
          }
          console.log(res.data, 'getBgmTree');
        } else {
          this.$message.error(res.msg || '系统错误，请稍后再试');
        }
      });
    },
  
  }
};
</script> 

<style lang="scss" scoped>
.edit-video-bg-music-group {
  .bgm-tree-container {
    width: 400px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #ffffff;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 树形组件样式优化
  ::v-deep .el-tree {
    .el-tree-node {
      .el-tree-node__content {
        padding: 8px 0;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-tree-node__label {
        font-size: 14px;
        color: #606266;
      }

      .el-checkbox {
        margin-right: 8px;
      }
    }

    // .el-tree-node__children {
    //   padding-left: 20px;
    // }
  }
}
</style>