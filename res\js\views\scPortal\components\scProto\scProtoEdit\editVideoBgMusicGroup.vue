<template>
  <div class="mt-[40px]">
    <p class="mb-[10px] text-[16px] font-bold">背景音乐配置</p>
    <div class="edit-video-bg-music-group">
      <el-tree :data="bgmTreeData" node-key="id" :default-checked-keys="defaultCheckList" :props="defaultProps" show-checkbox @check-change="handleCheckChange"></el-tree>
    </div>
  </div>
</template>

<script>  
import { getBgmTree } from '@/views/scPortal/api/scProto.js';
export default {
  data() {
    return {
      bgmTreeData: [],
    };
  },
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    defaultCheckList() {
      return this.protoInfo.bgmList || [];
    }
  },
  created() {
    this.getBgmTree();
  },
  methods: {
    getBgmTree() {
      getBgmTree().then(res => {
        if (res.success) {
          if (res.data?.length) {
            let bgmTreeList = []
            for (let i = 0; i < res.data.length; i++) {
              let curTree = {
                label: res.data[i].name,
                value: res.data[i].id,
              }
              if (res.data[i]?.bgmList?.length) {
                curTree.children = res.data[i].bgmList.map(item => ({
                  label: item.name,
                  value: item.resId,
                  resId: item.resId,
                }))
              }
              bgmTreeList.push(curTree);
            }
            this.bgmTreeData = bgmTreeList
            console.log( this.bgmTreeData, ' this.bgmTreeData');
          }
          console.log(res.data, 'getBgmTree');
        } else {
          this.$message.error(res.msg || '系统错误，请稍后再试');
        }
      });
    },
    handleCheckChange(data, checked, indeterminate) {
      console.log(data, checked, indeterminate, 'handleCheckChange');
    }
  }
};
</script> 

<style lang="scss" scoped>

</style>






