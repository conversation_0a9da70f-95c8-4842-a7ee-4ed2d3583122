<template>
  <div class="mt-[40px]">
    <p class="mb-[10px] text-[16px] font-bold">背景音乐配置</p>
    <div class="edit-video-bg-music-group">
      <div class="bgm-tree-container">
        <el-tree ref="bgmTree" :data="bgmTreeData" node-key="id" :default-checked-keys="defaultCheckList" show-checkbox></el-tree>
      </div>
    </div>
  </div>
</template>

<script>  
import { getBgmTree } from '@/views/scPortal/api/scProto.js';
export default {
  data() {
    return {
      bgmTreeData: [],
    };
  },
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 默认选中的节点
    defaultCheckList() {
      return this.protoInfo.bgmList || [];
      // return [
      //   "AJwBCAAQNxgAIPbij8UGKPWp5jg",
      //   "AJwBCAAQNxgAIOfRj8UGKM7J3osD",
      //   "AJwBCAAQNxgAIMbf_cEGKPLp7rcG",
      //   "AJwBCAAQNxgAINiyncMGKOjW5Y4D",
      //   "AJwBCAAQNxgAILGq6cIGKM68iMYH",
      //   "AJwBCAAQNxgAIP6p6cIGKJyo0IIH",
      //   "AJwBCAAQNxgAIK-8rsIGKITn47oE",
      //   "AJwBCAAQNxgAILOp6cIGKJbs5ZoG",
      //   "AJwBCAAQNxgAIMSo6cIGKPzppN8B",
      //   "AJwBCAAQNxgAIN3Ir8IGKPrd7zU",
      //   "AJwBCAAQNxgAIOu-qsIGKKCEwcwH",
      //   "AJwBCAAQNxgAINm3qsIGKIb_mrcF",
      //   "AJwBCAAQNxgAINnH5cEGKLDL2-4E",
      //   "AJwBCAAQNxgAIMzIicIGKNDxtTc",
      //   "AJwBCAAQNxgAIKvHicIGKO3phpIE",
      //   "AJwBCAAQNxgAIJDBicIGKLvniqoE",
      //   "AJwBCAAQNxgAILaD_8EGKJPp79kF"
      // ];
    }
  },
  created() {
    this.getBgmTree();
  },
  methods: {
    getBgmTree() {
      getBgmTree().then(res => {
        if (res.success) {
          if (res.data?.length) {
            let bgmTreeList = []
            for (let i = 0; i < res.data.length; i++) {
              // 父节点
              let curTree = {
                label: res.data[i].name,
                id: res.data[i].id,
                isParent: true,
              }
              if (res.data[i]?.bgmList?.length) {
                // 子节点
                curTree.children = res.data[i].bgmList.map(item => ({
                  label: item.name,
                  id: item.resId,
                  resId: item.resId,
                }))
              }
              bgmTreeList.push(curTree);
            }
            this.bgmTreeData = bgmTreeList
            // this.$refs.bgmTree.setCheckedKeys(this.defaultCheckList)
          }
        } else {
          this.$message.error(res.msg || '系统错误，请稍后再试');
        }
      });
    },
    /**
     * 获取选中的背景音乐列表(筛选出子节点)
     * @returns {Array} 选中的背景音乐ID数组
     */
    getSelectedBgmList() {
      if (this.$refs.bgmTree) {
        const checkedNodes = this.$refs.bgmTree.getCheckedNodes();
        // 过滤出叶子节点（实际的音乐文件）
        const selectedBgmList = checkedNodes
          .filter(node => !node.isParent);
        return selectedBgmList;
      }
      return [];
    },
    /**
     * 获取选中的背景音乐ID数组
     * @returns {Array} 选中的背景音乐ID数组
     */
    getSelectedBgmIds() {
      const selectedList = this.getSelectedBgmList();
      return selectedList.map(item => item.resId);
    },
  }
};
</script> 

<style lang="scss" scoped>
.edit-video-bg-music-group {
  .bgm-tree-container {
    width: 400px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #ffffff;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;

    // 滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  // 树形组件样式优化
  ::v-deep .el-tree {
    .el-tree-node {
      .el-tree-node__content {
        padding: 8px 0;

        &:hover {
          background-color: #f5f7fa;
        }
      }

      .el-tree-node__label {
        font-size: 14px;
        color: #606266;
      }

      .el-checkbox {
        margin-right: 8px;
      }
    }

    // .el-tree-node__children {
    //   padding-left: 20px;
    // }
  }
}
</style>