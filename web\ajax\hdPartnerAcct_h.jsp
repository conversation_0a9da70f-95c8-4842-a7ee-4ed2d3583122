<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" session="false" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="java.sql.PreparedStatement" %>
<%@ page import="fai.web.inf.HdOss" %>
<%@ page import="fai.webhdoss.WebHdOss" %>
<%@ page import="fai.web.inf.Kid" %>
<%@ page import="org.junit.internal.runners.statements.Fail" %>
<%@ page import="fai.weboss.WebOss" %>
<%@ page import="fai.cli.HdProfCli"%>
<%@ page import="fai.cli.OpenCli"%>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.SysBssStat" %>
<%@ page import="java.util.Calendar" %>
<%@ page import="fai.web.inf.SysNewUnion" %>
<%@ page import="fai.cli.VipCli" %>
<%@ page import="fai.sdk.fdpdata.operator.GE" %>
<%@ page import="fai.sdk.fdpdata.operator.LE" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.comm.fdpdata.FdpDataParamMatcher" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.IN" %>
<%@ page import="fai.sdk.fdpdata.operator.EQ" %>


<%!
	private String add(HttpServletRequest request) throws Exception{
        HdOss ho = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
        int rt = Errno.OK;

        String acct = request.getParameter("acct");
        String pwd = request.getParameter("pwd");
        int type = Parser.parseInt(request.getParameter("type"), 0);
        String companyName = request.getParameter("companyName");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");
        String address = request.getParameter("address");
        boolean isBigCustomer = Parser.parseBoolean(request.getParameter("isBigCustomer"), false);

        if(Str.isEmpty(acct) || Str.isEmpty(pwd) || type == 0 || Str.isEmpty(companyName) || Str.isEmpty(phone)){
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "args er; acct=%s, pwd=%s, type=%s, companyName=%s, phone=%s", acct, pwd, type, companyName, phone);
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "参数错误").toString();
        }

        int flag = 0;
        if (isBigCustomer) {
            flag = 1;
        }

        Param data = new Param();
        data.setString(HdPartnerAcctDef.Info.ACCT, acct);
        data.setString(HdPartnerAcctDef.Info.PWD, pwd);
        data.setInt(HdPartnerAcctDef.Info.FLAG, flag);
        data.setInt(HdPartnerAcctDef.Info.TYPE, type);
        data.setString(HdPartnerAcctDef.Info.COMPANY_NAME, companyName);
        data.setString(HdPartnerAcctDef.Info.PHONE, phone);
        data.setString(HdPartnerAcctDef.Info.EMAIL, email);
        data.setString(HdPartnerAcctDef.Info.ADDRESS, address);

        rt = ho.addPartnerAcct(data);
        if(rt != Errno.OK){
            Log.logErr(rt, "add partner acc err; data=%s", data);
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }

        return new HdGameDef.ErrorInfo(rt, "success", true).add("msg", "添加成功").toString();
    }
	

    private String set(HttpServletRequest request) throws Exception{
        HdOss ho = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
        int rt = Errno.OK;

        int type = Parser.parseInt(request.getParameter("type"), 0);
        if(type == 0){
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "args err; type=%s", type);
            return HdGameDef.ErrorInfo.getErrInfo(rt, "参数错误");
        }

        String pwd = request.getParameter("pwd");
        String companyName = request.getParameter("companyName");
        String phone = request.getParameter("phone");
        String email = request.getParameter("email");

        Param update = new Param();
        if(!Str.isEmpty(pwd)){
            update.setString(HdPartnerAcctDef.Info.PWD, pwd);
        }
        if(!Str.isEmpty(companyName)){
            update.setString(HdPartnerAcctDef.Info.COMPANY_NAME, companyName);
        }
        if(!Str.isEmpty(phone)){
            update.setString(HdPartnerAcctDef.Info.PHONE, phone);
        }
        if(!Str.isEmpty(email)){
            update.setString(HdPartnerAcctDef.Info.EMAIL, email);
        }

        ParamUpdater updater = new ParamUpdater(update);
        rt = ho.setPartnerAcct(type, updater);
        if(rt != Errno.OK){
            return HdGameDef.ErrorInfo.getErrInfo(rt, "系统错误");
        }
        return HdGameDef.ErrorInfo.getErrInfo(rt, "设置成功");
    }


    private String getPartnerAcctList(HttpServletRequest request) throws Exception{
        HdOss ho = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
        int rt = Errno.OK;

        int limit = Parser.parseInt(request.getParameter("limit"), 10);
        int page = Parser.parseInt(request.getParameter("page"), 0);

        SearchArg searchArg = new SearchArg();
        if(limit != -1 && page != -1){
            searchArg.limit = limit;
            searchArg.start = (page - 1) * limit;
        }
        searchArg.totalSize = new Ref<Integer>();

        FaiList<Param> list = ho.getPartnerAcctList(searchArg);
        if(rt != Errno.OK){
           return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }
        return new HdGameDef.ErrorInfo(rt, "success", true).add("list", list).add("totalSize", searchArg.totalSize.value).toString();
    }

    private String getBindAcctByType(HttpServletRequest request) throws Exception{
	    int rt = Errno.OK;
        OpenCli openCli = new OpenCli(Core.getFlow());
        if(!openCli.init()){
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        Core.setAid(1);
        SysNewUnion snu = (SysNewUnion)Core.getSysKit(Kid.SYS_NEWUNION);
        SysBssStat sysBssStat = (SysBssStat)Core.getSysKit(Kid.SYS_BSS_STAT);

        String submitTimeBegin = request.getParameter("submitTimeBegin");
        String submitTimeEnd = request.getParameter("submitTimeEnd");

        String regTimeBegin = request.getParameter("regTimeBegin");
        String regTimeEnd = request.getParameter("regTimeEnd");

        String showPayTimeBegin = request.getParameter("showPayTimeBegin");
        String showPayTimeEnd = request.getParameter("showPayTimeEnd");

        String acct = request.getParameter("acct");
        int hdVer = Parser.parseInt(request.getParameter("hdVer"), -1);

        int page = Parser.parseInt(request.getParameter("page"), 0);
        int limit = Parser.parseInt(request.getParameter("limit"), 0);

        FaiList<Integer> typeList = new FaiList<Integer>();
        typeList.add(13);
        typeList.add(14);
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(OpenDef.Info.TYPE, ParamMatcher.IN, typeList);
        searchArg.limit = limit;
        searchArg.start = (page - 1) * limit;
        searchArg.totalSize = new Ref<Integer>();

        FaiList<Param> list = new FaiList<Param>();
        rt = openCli.getOpenAcctList(searchArg, list);
        if(rt != Errno.OK){
            Log.logErr(rt, "get list err;searchArg=%s", searchArg.matcher.getSql());
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }

        FaiList<Integer> aidList = new FaiList<Integer>();
        for(Param item : list){
            int aid = item.getInt(OpenDef.Info.AID);
            aidList.add(aid);
        }

        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);
        fieldList.add(BssStatDef.Info.ACCT);
        fieldList.add(BssStatDef.Info.REG_TIME);
        fieldList.add(BssStatDef.Info.HD_VERSION);
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        if(hdVer != -1){
            searchArg.matcher.and(BssStatDef.Info.HD_VERSION, ParamMatcher.EQ, hdVer);
        }
        if(!Str.isEmpty(regTimeBegin) && !Str.isEmpty(regTimeEnd)){
            int intBegTime = (int) (Parser.parseCalendar(regTimeBegin+" 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int intEndTime = (int) (Parser.parseCalendar(regTimeEnd+" 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

            searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.GE, intBegTime);
            searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.LE, intEndTime);
        }
        if(!Str.isEmpty(acct)){
            searchArg.matcher.and(BssStatDef.Info.ACCT, ParamMatcher.EQ, acct);
        }
        //FaiList<Param> corpAllList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

        FaiList<Object> outerExpr = new FaiList<Object>();
        outerExpr.add(IN.contains("aid", aidList));
        outerExpr.add(EQ.of("version_hd", hdVer));
        if(!Str.isEmpty(regTimeBegin) && !Str.isEmpty(regTimeEnd)){
            int intBegTime = (int) (Parser.parseCalendar(regTimeBegin+" 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int intEndTime = (int) (Parser.parseCalendar(regTimeEnd+" 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            outerExpr.add(GE.of("reg_time", intBegTime));
            outerExpr.add(LE.of("reg_time", intEndTime));
        }

        FdpDataParamMatcher matcher = AND.expr(outerExpr);


        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid", "reg_aacct", "reg_time", "version_hd")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> corpAllList = execute.getData();


        //第一次查出来的是所有type符合条件的aidList，第二次查出来的是aidList中符合过滤条件的aidList
        aidList.clear();
        for(Param corpAll : corpAllList){
            int aid = corpAll.getInt(BssStatDef.Info.AID);
            aidList.add(aid);
        }

        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(NewUnionDef.Client.FK_AID, ParamMatcher.IN, aidList);
        if(!Str.isEmpty(submitTimeBegin) && !Str.isEmpty(submitTimeEnd)){
            searchArg.matcher.and(NewUnionDef.Client.SUBMIT_TIME, ParamMatcher.GE, submitTimeBegin + " 00:00:00");
            searchArg.matcher.and(NewUnionDef.Client.SUBMIT_TIME, ParamMatcher.LE, submitTimeEnd + " 23:59:59");
        }
        if(!Str.isEmpty(showPayTimeBegin) && !Str.isEmpty(showPayTimeEnd)){
            searchArg.matcher.and(NewUnionDef.Client.PAY_TIME, ParamMatcher.GE, showPayTimeBegin + " 00:00:00");
            searchArg.matcher.and(NewUnionDef.Client.PAY_TIME, ParamMatcher.LE, showPayTimeEnd + " 23:59:59");
        }
        FaiList<Param> data = snu.searchNewUnionClient(searchArg);
        int count = data.size();

        typeList.clear();
        typeList.add(VipDef.Type.SITE_VIP);
        typeList.add(VipDef.Type.MAIL_VIP);
        typeList.add(VipDef.Type.FLYER_VIP);
        typeList.add(VipDef.Type.HD_VIP);
        FaiList<Integer> productIdList = new FaiList<Integer>();
        productIdList.add(FaiProductDef.Id.HD_PLATINUM);
        productIdList.add(FaiProductDef.Id.HD_SILVER);
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(VipDef.Info.AID, ParamMatcher.IN, aidList);
        searchArg.matcher.and(VipDef.Info.TYPE, ParamMatcher.IN, typeList);
        searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.IN, productIdList);

        VipCli vipCli = new VipCli(Core.getFlow());
        if(!vipCli.init()){
            throw new WebException("vipCli init error");
        }
        FaiList<Param> vipList = new FaiList<Param>();
        rt = vipCli.searchVip(searchArg, vipList);

        for(Param item : data){
            Calendar submitTimeCalendar = item.getCalendar(NewUnionDef.Client.SUBMIT_TIME);
            if (submitTimeCalendar != null) {
                item.setString(NewUnionDef.Client.SUBMIT_TIME, Parser.parseSimpleTime(submitTimeCalendar));
            }

            int aid = item.getInt("fkAid");
            int sid = item.getInt(NewUnionDef.Client.SID);
            faiTradeStationBaseApi.changeAid(aid);
            faiTradeStationBaseApi.changeSiteId(sid);

            Param acctInfo = Misc.getFirst(corpAllList, BssStatDef.Info.AID, aid);
            item.assign(acctInfo, BssStatDef.Info.ACCT);
            // item.assign(acctInfo, BssStatDef.Info.REG_TIME);
            int regTime = acctInfo.getInt("reg_time", 0);
            item.setString(BssStatDef.Info.REG_TIME, Parser.parseDateString(regTime, "yyyy-MM-dd"));
            item.assign(acctInfo, "version_hd");
            String hdName = faiTradeStationBaseApi.getName(acctInfo.getInt("version_hd", 0));
            Log.logDbg("zhongdbg acctInfo=%s", acctInfo);
            hdName = hdName.equals("")?"互动免费版":hdName;

            FaiList<Param> tmpVipList = Misc.getList(vipList, VipDef.Info.AID, aid);
            Param hdVip = Misc.getFirstNullIsEmpty(tmpVipList, VipDef.Info.TYPE, VipDef.Type.HD_VIP);
            String hdRenewTimeStr = Parser.parseSimpleTime(hdVip.getCalendar(VipDef.Info.EXPIRE_TIME));
            String showPayTime = Parser.parseSimpleTime(item.getCalendar(NewUnionDef.Client.PAY_TIME));

            item.setString("hdName", hdName);
            item.setString("hdRenewTimeStr", hdRenewTimeStr);
            item.setString("showPayTime", showPayTime);
        }

        return new HdGameDef.ErrorInfo(rt, "success", true).add("list", data).add("totalSize", count).toString();

    }

    private String getOrderList(HttpServletRequest request) throws Exception {
        int rt = Errno.OK;

        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        int start = Parser.parseInt(request.getParameter("start"), 0);
        int limit = Parser.parseInt(request.getParameter("limit"), 0);

        String acct = request.getParameter("acct");
        String name = request.getParameter("name");
        String person = request.getParameter("person");
        int product = Parser.parseInt(request.getParameter("product"), 0);
        String payTimeBegin = request.getParameter("payTimeBegin");
        String payTimeEnd = request.getParameter("payTimeEnd");

        OpenCli openCli = new OpenCli(Core.getFlow());
        if(!openCli.init()){
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }
        Core.setAid(1);
        SysBssStat sysBssStat = (SysBssStat)Core.getSysKit(Kid.SYS_BSS_STAT);

        FaiList<Integer> typeList = new FaiList<Integer>();
        typeList.add(13);
        typeList.add(14);
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(OpenDef.Info.TYPE, ParamMatcher.IN, typeList);
        searchArg.totalSize = new Ref<Integer>();
        FaiList<Param> list = new FaiList<Param>();
        rt = openCli.getOpenAcctList(searchArg, list);
        if(rt != Errno.OK){
            Log.logErr(rt, "get acct list err");
            return new HdGameDef.ErrorInfo(rt, "success", false).add("msg", "系统错误，请稍后再试").toString();
        }

        FaiList<Integer> aidList = new FaiList<Integer>();
        for(Param p:list){
            int aid = p.getInt(OpenDef.Info.AID, 0);
            aidList.add(aid);
        }

        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);
        fieldList.add(BssStatDef.Info.ACCT);
        fieldList.add(BssStatDef.Info.NAME);
        fieldList.add(BssStatDef.Info.PERSON);
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        if(!Str.isEmpty(acct)){
            searchArg.matcher.and(BssStatDef.Info.ACCT, ParamMatcher.EQ, acct);
        }
        if(!Str.isEmpty(name)){
            searchArg.matcher.and(BssStatDef.Info.NAME, ParamMatcher.EQ, name);
        }
        if(!Str.isEmpty(person)){
            searchArg.matcher.and(BssStatDef.Info.PERSON, ParamMatcher.EQ, person);
        }
        //FaiList<Param> corpAllList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

        FaiList<Object> outerExpr = new FaiList<Object>();
        outerExpr.add(IN.contains("aid", aidList));
        if(!Str.isEmpty(acct)){
            outerExpr.add(EQ.of("reg_aacct", acct));
        }
        if(!Str.isEmpty(name)){
            outerExpr.add(EQ.of("reg_name", name));
        }
        if(!Str.isEmpty(person)){
            outerExpr.add(EQ.of("reg_person", person));
        }

        FdpDataParamMatcher matcher = AND.expr(outerExpr);


        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid", "reg_aacct", "reg_name", "reg_person")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> corpAllList = execute.getData();


        aidList.clear();
        for(Param p:corpAllList){
            int aid = p.getInt(BssStatDef.Info.AID, 0);
            aidList.add(aid);
        }

        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, false);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN, aidList);

        FaiList<Integer> productList = new FaiList<Integer>();
        if(product != 0){
            productList.add(product);
        }else{
            productList.add(123);	//白银版
            productList.add(115);	//白金版
            productList.add(164);	//门店版
            productList.add(228);	//钻石版
        }
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, productList);


        if(!Str.isEmpty(payTimeBegin) && !Str.isEmpty(payTimeEnd)){
            long begDateTime = Parser.parseCalendar(payTimeBegin+" 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis()/1000;
            long endDateTime = Parser.parseCalendar(payTimeEnd+" 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis()/1000;
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GT, begDateTime);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LT, endDateTime);
        }

        searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.PAY_TIME, true);
        searchArg.totalSize = new fai.comm.util.Ref<Integer>();
        searchArg.start = start;
        searchArg.limit = limit;
        FaiList<Param> orderItemList = sysBssStat.getOrderItemList(searchArg);
        int total = searchArg.totalSize.value;

        for (Param order : orderItemList) {
            int aid = order.getInt(BssStatDef.Info.AID, 0);
            faiTradeStationBaseApi.changeAid(aid);
            Param acctInfo = Misc.getFirst(corpAllList, BssStatDef.Info.AID, aid);
            order.assign(acctInfo, "reg_aacct");
            order.assign(acctInfo, "reg_name");
            order.assign(acctInfo, "reg_person");
            int productId = order.getInt(BssStatDef.OrderItemInfo.PRODUCT_ID, 0);
            order.setString("product", faiTradeStationBaseApi.getName(productId));
            long payTime = order.getLong(BssStatDef.OrderItemInfo.PAY_TIME, 0L);
            if (payTime != 0) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(payTime * 1000);
                order.setString("payTime", Parser.parseSimpleTime(calendar));
            }
        }

        return new HdGameDef.ErrorInfo(rt, "success", false).add("list", orderItemList).add("total", total).toString();
    }

%>

<%
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        
        if(cmd.equals("add")){
            output = add(request);
        }else if(cmd.equals("set")) {
            output = set(request);
        }else if(cmd.equals("getPartnerAcctList")){
            output = getPartnerAcctList(request);
        }else if(cmd.equals("getBindAcctByType")){
            output = getBindAcctByType(request);
        }else if(cmd.equals("getOrderList")){
            output = getOrderList(request);
        }

        //TODO
        /*  else if (cmd.equals("getNewAndOldList")) {
            output = getNewAndOldList(request);
        }  */
        else {
            output = "no cmd find";
        }


    } catch (Exception exp) {
        output = WebOss.checkAjaxException(exp);
    }

    out.print(output);


%>