(function(){'use strict';const getDomain = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/getDomain").then((response) => {
      resolve(response.body);
    }, (error) => {
      console.log(error);
      reject(error);
    });
  });
};
const getTemplateConfInfo = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/getConfInfo").then((response) => {
      resolve(response.body);
      console.log(response.body);
    }, (error) => {
      console.log(error);
      reject(error);
    });
  });
};const ScProductType = {
  /**
   * 全部类型
   */
  ALL: -1,
  /**
   * 视频
   */
  VIDEO: 0,
  /**
   * 图文
   */
  IMGTEXT: 1
};const getStaff = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/staff/getInfo").then((response) => {
      resolve(response.body);
    }, (error) => {
      console.log(error);
      reject(error);
    });
  });
};var scPortal = {
  namespaced: true,
  // 建议设置为true，使用命名空间
  state: {
    // 模板配置信息
    templateConfInfo: {
      industryList: [],
      // 行业列表
      sceneList: []
      // 场景列表
    },
    // 域名信息
    domainInfo: {
      scUsrResOssRoot: ""
      // oss资源域名
    },
    curActiveName: String(ScProductType.VIDEO),
    // 当前切换的tab
    staffInfo: {
      all: true,
      hdProductManage: false,
      hdSale: true,
      hdSaleManage: true,
      name: "",
      sacct: "",
      scDevelop: false,
      scOperation: false,
      scPm: false,
      sid: 0
    }
    // 登录账号信息
  },
  mutations: {
    setTemplateConfInfo(state, payload) {
      state.templateConfInfo = payload;
    },
    domainInfo(state, payload) {
      state.domainInfo = payload;
    },
    curActiveName(state, payload) {
      state.curActiveName = payload;
    },
    setStaffInfo(state, payload) {
      state.staffInfo = payload;
    }
  },
  actions: {
    getTemplateConfInfo({ commit, state }) {
      if (state.templateConfInfo.industryList.length > 0) {
        return Promise.resolve();
      }
      return getTemplateConfInfo().then((res) => {
        if (res && res.success) {
          commit("setTemplateConfInfo", res.data);
          return res;
        }
        return Promise.reject(res.message || "获取配置信息失败");
      });
    },
    getDomainInfo({ commit, state }) {
      if (state.domainInfo.scUsrResOssRoot) {
        return Promise.resolve();
      }
      return getDomain().then((res) => {
        if (res && res.success) {
          commit("domainInfo", res.data);
          return res;
        }
        return Promise.reject(res.message || "获取域名信息失败");
      });
    },
    getStaffInfo({ commit, state }) {
      if (state.staffInfo.sid) {
        return Promise.resolve();
      }
      return getStaff().then((res) => {
        if (res && res.success) {
          commit("setStaffInfo", res.data);
          return res;
        }
        return Promise.reject(res.message || "获取账号信息失败");
      });
    }
  }
};var scTemplate = {
  namespaced: true,
  // 建议设置为true，使用命名空间
  state: {
    curActiveName: String(ScProductType.VIDEO)
    // 当前切换的tab
  },
  mutations: {
    curActiveName(state, payload) {
      state.curActiveName = payload;
    }
  }
};const initStore = () => {
  return new Vuex.Store({
    state: {},
    mutations: {},
    actions: {},
    getters: {},
    namespaced: true,
    modules: {
      scPortal,
      scTemplate,
    },
  });
};

window.initStore = initStore;})();