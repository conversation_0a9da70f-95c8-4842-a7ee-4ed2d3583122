<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}

if(!Web.getDebug() && WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
	out.println("请到新销售系统进行操作");
	return;
} 

%>



<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>售前领取记录</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-approve">

		<!--查询条件 start-->
		<div class="fai-approve-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<el-form-item label="申请人">
					<el-select v-model="form.sid" filterable>
						<el-option label="所有" value="0"></el-option>
						<el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sid" :key="preSale.sid"></el-option>
					</el-select>
				</el-form-item>
			    <el-form-item label="申请账号/AID">
					<el-input v-model="form.acct" placeholder="" class="fai-wid-100"></el-input>
				</el-form-item>
			    <el-form-item label="审批结果">
					<el-select v-model="form.status" filterable>
						<el-option label="全部" value="-1"></el-option>
						<el-option label="申请中" value="0"></el-option>
						<el-option label="同意" value="1"></el-option>
						<el-option label="拒绝" value="2"></el-option>
					</el-select>
				</el-form-item>
			    <el-form-item label="申请时间">
			    	<el-checkbox v-model="form.approveDateFlag"></el-checkbox>
					<el-date-picker class="fai-date" v-model="form.approveDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.approveDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
			    <el-form-item label="审批时间">
			    	<el-checkbox v-model="form.applyDateFlag"></el-checkbox>
					<el-date-picker class="fai-date" v-model="form.applyDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.applyDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
			    <el-form-item label="报备类型">
					<el-select v-model="form.style" filterable>
						<el-option label="全部" value="-1"></el-option>
						<el-option label="新旧账号" value="1"></el-option>
						<el-option label="其他" value="0"></el-option>
						<el-option label="大满贯" value="11"></el-option>
						<el-option label="七鱼" value="13"></el-option>
						<el-option label="工单资源" value="2"></el-option>
						<el-option label="客户转介绍" value="3"></el-option>
						<el-option label="自主开发" value="4"></el-option>
						<el-option label="离职销售跟进" value="5"></el-option>
						<el-option label="同账号资源跟进" value="6"></el-option>
						<el-option label="修改领取时间" value="7"></el-option>
						<el-option label="客户主动添加" value="9"></el-option>
						<el-option label="小A" value="10"></el-option>
					</el-select>
				</el-form-item>

				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="fai-approve-list" v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border>
				<el-table-column label="操作" v-if="this.auth.authAdm || this.auth.hdSaleManage || this.auth.hdSaleGroupLeader " type="index" width="230px" fixed>
					<template slot-scope="scope" v-if="scope.row.status == 0">
						<el-button type="" size="mini" @click="set(scope.row.id, 'agree')">同意</el-button>
						<el-button type="" size="mini" @click="set(scope.row.id, 'refuse')">拒绝</el-button>
						<el-button type="" size="mini" @click="set(scope.row.id, 'del')">删除</el-button>
					</template>
				</el-table-column><!--fixed:固定-->
				<el-table-column label="企业账户/AID" width="200px">
					<template slot-scope="scope">
						<a v-bind:href="scope.row.url" target='_blank'>{{scope.row.acctStr}}</a>
					</template>
				</el-table-column>
				<el-table-column label="申请人" prop="staffName" width="100px"></el-table-column>
				<el-table-column label="审批时间" prop="approveTime" width="160px"></el-table-column>
				<el-table-column label="申请时间" prop="applyTime" width="160px"></el-table-column>
				<el-table-column label="备注" prop="mark" width="160px"></el-table-column>
				<el-table-column label="报备类型" prop="styleStr" width="160px"></el-table-column>
				<el-table-column label="审批结果" prop="statusStr" width="160px"></el-table-column>
				<el-table-column label="图片资料" width="160px">
					<template slot-scope="scope" >
						<span v-if="scope.row.coverImgsrc != ''">
							<!-- <a v-bind:href="scope.row.coverImgsrc" target='_blank'>查看</a> -->
							<el-popover trigger="hover" placement="top">
									<img v-bind:src="scope.row.coverImgsrc" ></img>
									<!-- <div slot="reference" class="name-wrapper"> -->
									  <a slot="reference" style="cursor: pointer">查看</a>
									<!-- </div> -->
							</el-popover>
							&nbsp;<a v-bind:href="scope.row.coverImgsrc" target='_blank' download>下载</a>
						</span>
						<span v-else>无</span>
					</template>
				</el-table-column>
			</el-table>
			<div class="block">
    			<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" layout="total, sizes, prev, pager, next, jumper" 
      					:current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total">
    			</el-pagination>
 			</div>
		</div>
		
		<!--数据表格 end-->

	</body>
	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-approve-search',
			data: {
				form: {//这里是为了填充默认值
					sid: "0",
					acct: "",
					status: "-1",
					approveDateFlag: false,
					approveDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					approveDateEnd: Fai.tool.dateFormatter(new Date()),
					applyDateFlag: false,
					applyDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					applyDateEnd: Fai.tool.dateFormatter(new Date()),
					style: "-1",
					currentPage: 1,
					limit: 10
				},
				preSaleList: [],
				editable: false,
				clearable: false,
			},
			created:function(){
				Fai.http.post("hdSale_h.jsp?cmd=getPreSale", "", false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
				getDataList(this.form);
				console.log("id = " + this.$route);

			},
			methods: {
				onSubmit() {
					getDataList(this.form);
				},
				
		    }
		});
		
		var faiDataList = new Vue({
			el: '.fai-approve-list',
			data: {
				tableData: [],
				currentPage: 1,
				limit: 10,
				total: 0,
			},
			created:function(){
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				handleSizeChange(val) {
					faiSearchObj.form.limit = val;
		        	getDataList(faiSearchObj.form);
			    },
			    handleCurrentChange(val) {
			    	faiSearchObj.form.currentPage = val;
			        getDataList(faiSearchObj.form);
			    },
				set(id, type) {// 操作：agree(同意)；refuse(拒绝)；del(删除)；
					var str = "";
 					var cmd = "setApproveStatus";
					if(type == 'agree'){
		 				str = "同意";
		 			}else if(type == 'refuse'){
		 				str = "拒绝";
		 			}else if(type == 'del'){
		 				str = "删除";
		 				cmd = "delApprove";
		 			}else{
		 				alert("参数错误，请刷新重试！");
		 				return;
		 			}
					this.$confirm('确定要 '+str+' 该条申请吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":cmd, "id":id, "type":type}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}else{
								//Fai.http.post已经封装了错误提示
							}
							getDataList(faiSearchObj.form);//操作成功刷新数据表格	
						});
					}).catch(() => { });
				},
				
		    }
		});
        
		// 获取数据
		function getDataList(urlParam){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getApproveList", urlParam, false).then(result => {
				if(result.success){
					faiDataList.tableData = result.dataList;
					faiDataList.total = result.total;
				}
			});
		}

	</script>

</html>



