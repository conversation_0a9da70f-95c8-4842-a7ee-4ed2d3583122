package fai.webhdoss.factory;

import fai.app.HdModelDef;
import fai.comm.util.ParamMatcher;
import fai.webhdoss.model.vo.HdModelListVO;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;

@Configuration
public class BeanFactory {

    @Qualifier(value = "hdModelListSearch")
    @Bean
    public List<BiConsumer<HdModelListVO, ParamMatcher>> getHdModelList(){
        List<BiConsumer<HdModelListVO, ParamMatcher>> list = new ArrayList<>();
        //节日
        list.add((vo,mather)->{
            if(vo.getFestivel()!=-1){
                mather.and(HdModelDef.Info.KEY1, ParamMatcher.EQ, vo.getFestivel());
            }
        });
        //游戏类型
        list.add((vo,mather)->{
            if(vo.getType()!=-1){
                mather.and(HdModelDef.Info.KEY2, ParamMatcher.EQ, vo.getType());
            }
        });
        //设计师
        list.add((vo,mather)->{
            if(vo.getDesigner()!=-1){
                mather.and(HdModelDef.Info.DESIGNER, ParamMatcher.EQ, vo.getDesigner());
            }
        });
        //发布
        list.add((vo,mather)->{
            if(vo.getPub()!=-1){
                mather.and(HdModelDef.Info.PROPERTY, ParamMatcher.EQ, vo.getPub());
            }
        });
        //热点
        list.add((vo,mather)->{
            if(vo.getFestivel()!=-1){
                mather.and(HdModelDef.Info.KEY1, ParamMatcher.EQ, vo.getFestivel());
            }
        });
        //原型
        list.add((vo,mather)->{
            if(vo.getProType()!=-1){
                mather.and(HdModelDef.Info.RSTYLE, ParamMatcher.EQ, vo.getProType());
            }
        });
        //账号
        list.add((vo,mather)->{
            if(vo.getAcct()!=-1){
                mather.and(HdModelDef.Info.ACCT, ParamMatcher.EQ, vo.getAcct());
            }
        });
        //游戏id
        list.add((vo,mather)->{
            if(vo.getGstyle()!=-1){
                mather.and(HdModelDef.Info.GAMEID, ParamMatcher.EQ, vo.getGstyle());
            }
        });
        //样板名称
        list.add((vo,mather)->{
            if(vo.getModelName()!=-1){
                mather.and(HdModelDef.Info.NAME, ParamMatcher.EQ, vo.getModelName());
            }
        });
        return list;
    }
}
