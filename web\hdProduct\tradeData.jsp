<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%
	FaiList<Param> tradeList = HdModelTradeDef.getTradeList();
%>

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title></title>
	<%@ include file="/comm/link.jsp.inc"%>
	<%@ include file="/comm/script.jsp.inc"%>
	<style>
		.container {
			padding: 20px;
		}
	</style>
</head>
<body>
	<div class="container">
		aid：<el-input v-model="aid"></el-input>
		提交时间：<el-date-picker
			v-model="date"
			size="mini"
			type="daterange"
			range-separator="至"
			start-placeholder="开始日期"
			end-placeholder="结束日期"
			format="yyyy年MM月dd日"
			value-format="yyyy-MM-dd"
			:editable="false"
			@change="onDateChange"
		></el-date-picker>
		<el-button type="mini" @click="getTradeRecordList">查询</el-button>
		<el-button type="mini" @click="exportTradeRecordList">导出</el-button>
		<div style="margin: 20px 0">
			已填写：{{totalSize}}
			<div v-for="(item, index) in tradeList" :key="index">
				{{item.name}}：{{item.num || 0}}
			</div>
		</div>
		<div>
			<el-table :data="tradeRecordShowList" stripe border max-height="720">
				<el-table-column v-for="(column, index) in tableColumns" :key="index" :prop="column.prop" :label="column.label"></el-table-column>
			</el-table>
			<el-pagination @size-change="sizeChange" @current-change="currentChanage" layout="total, sizes, prev, pager, next, jumper" :total="totalSize" :page-sizes="[10, 50, 100, 500]" :page-size="limit" :current-page="page" style="margin-top: 30px;"></el-pagination>
		</div>
	</div>
</body>
<script type="text/javascript">
	let now = new Date();
	let last7day = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
	var tradeList = <%=tradeList%>.filter(function(item) {
		return item.id != 3;
	});

	new Vue({
		el: '.container',
		data() {
			return {
				aid: '',
				date: [last7day.getFullYear() + '-' + (last7day.getMonth() + 1) + '-' + last7day.getDate(), now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + now.getDate()],
				tradeList: tradeList,
				tableColumns: [
					{
						prop: 'aid',
						label: 'aid'
					},
					{
						prop: 'tradeName',
						label: '所属一级行业'
					},
					{
						prop: 'trade2Name',
						label: '所属二级行业'
					},
					{
						prop: 'hostName',
						label: '主办单位'
					},
					{
						prop: 'siteSourceName',
						label: '注册来源'
					},
					{
						prop: 'regBizName',
						label: '注册产品'
					}
				],
				tradeRecordList: [],
				tradeRecordShowList: [],
				totalSize: 0,
				limit: 10,
				page: 1
			};
		},
		created() {
			this.getTradeRecordList();
		},
		methods: {
			getTradeRecordList() {
				Fai.http.post('hdProduct_h.jsp?cmd=getTradeRecordList', {
					aid: this.aid,
					startTime: this.date[0],
					endTime: this.date[1]
				}, false).then(res => {
					this.tradeList.forEach(item => { delete item.num });
					this.tradeRecordList = res.dataList;
					this.getTradeRecordShowList();
					this.totalSize = res.totalSize;
					res.dataList.forEach(data => {
						data.trade == 3 && (data.trade = 5);
						this.tradeList.forEach(item => {
							item.id == data.trade && (item.num = item.num ? item.num + 1 : 1);
						});
					});
				});
			},
			exportTradeRecordList() {
				window.location.href = '/ajax/hdProduct_h.jsp?cmd=exportTradeRecord&aid=' + this.aid + '&startTime=' + this.date[0] + '&endTime=' + this.date[1];
			},
			getTradeRecordShowList() {
				this.tradeRecordShowList = this.tradeRecordList.slice((this.page - 1) * this.limit, this.page * this.limit);
			},
			onDateChange() {
				this.getTradeRecordList();
			},
			sizeChange(limit) {
				this.limit = limit;
				this.getTradeRecordShowList();
			},
			currentChanage(page) {
				this.page = page;
				this.getTradeRecordShowList();
			}
		}
	});
</script>
</html>