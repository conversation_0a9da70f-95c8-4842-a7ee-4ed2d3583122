package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoInputFormVO {
    // dify 工作流输入字段名称
    private String variable;

    // dify 工作流输入字段显示名称
    private String label;

    // dify 工作流限制的字段
    private int maxLength;

    // dify 工作流的必填限制
    private boolean required;

    // 字段类型定义：0-文本，1-标签
    private int filedType;
}
