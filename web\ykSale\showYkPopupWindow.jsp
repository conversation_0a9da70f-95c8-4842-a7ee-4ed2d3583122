<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page session="false" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="fai.webhdoss.app.*" %>
<%
    boolean authHdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>不购买原因</title>
    <%@ include file="/comm/link.jsp.inc" %>
    <%@ include file="/comm/script.jsp.inc" %>
</head>
<body class="saleBox">
<div class="reasonBox">
    <fai-iframe-box class="reasonBox" :submit="onSubmit" :close="onClose">
        <el-form slot="fai-iframe-data" :label-position="labelPosition" label-width="120px">
            <el-form-item label="标记:">
                <el-select id="intent" v-model="form.intent">
                    <el-option v-for="intent in intentList" :key="intent.value" :label='intent.text'
                               :value="intent.value"></el-option>
                </el-select>
            </el-form-item>
            
            <el-form-item label="原因:">
                <el-select id="reason" v-model="form.reason" @change="markReasonChange">
                    <el-option v-for="reason in reasonList" v-if="reason.fatherId == 0" :key="reason.reason"
                               :label='reason.reason' :value="reason.reason"></el-option>
                </el-select>
                <a v-if="form.showReasonAdmin" :href="form.reasonHref" target="_blank">选项管理</a>
            </el-form-item>
            <el-form-item label="下次联系时间:">
                <el-date-picker
                        v-model="form.talkNextTime"
                        type="date"
                        placeholder="下次联系时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="当前意向度:">
                <el-radio-group v-model="form.intentLevel" size="small">
                    <el-radio-button :label="1">A</el-radio-button>
                    <el-radio-button :label="2">B</el-radio-button>
                    <el-radio-button :label="3">C</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="备注:">
                <el-input type="textarea" v-model="form.corpMark" :rows="8"></el-input>
            </el-form-item>
        </el-form>
    </fai-iframe-box>
</div>
<script type="text/javascript">
    document.domain = "<%=Web.getPortalDomain()%>";
    var url = location.search; //获取url中"?"符后的字串
    console.info(url);
    var theRequest = new Object();
    if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
        }
    }
    var aid = theRequest.aid;
    var reasonBox = new Vue({
        el: '.reasonBox',
        data: {
            form: {
                aid: aid,
                corpMark: ''
            },
            intentList: [],
            reasonList: [],
            markList: [],
            labelPosition: 'right',
            dialogVisible: false,
            ykIntent: "-"
        },
        created: function () {
            this.getYkInfo(aid);
        },
        mounted: function () {
            var thiz = this;
            Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkInfo", {
                aid: thiz.form.aid,
            }, false).then(result => {
                if (result.success) {
                    var data = result.data;
                    for (var k in data) {
                        thiz.$set(thiz.form, k, data[k]);
                    }
                }
            });

            Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkIntentList", "", false).then(result => {
                if (result.success) {
                    if (result.success) {
                        thiz.intentList = result.data;
                    }
                }
            });

            Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkReasonList", "", false).then(result => {
                if (result.success) {
                    if (result.success) {
                        thiz.reasonList = result.data;
                    }
                }
            });
            // 通知父窗口已经加载完数据
            Fai.iframe.notify(thiz.$el);
        },
        methods: {
            onSubmit() {
				 let mark = this.form.corpMark;
				 if (typeof mark == "undefined" || mark == null || mark == ""){
					this.$message({
						type: 'warning',
						message: '备注不能为空!'
					});
					return;
				}
				
				let intent = this.form.intent;	
				if (typeof intent != "undefined" && intent != null && intent != ""&& intent==5){
					let reason = this.form.reason;
					if (typeof reason == "undefined" || reason == null || reason == ""){
						this.$message({
							type: 'warning',
							message: '原因不能为空'
						});
						return;
					}
				}
                // 将信息set进localStorage里面
                if (window.localStorage) {
                    // 封装成json
                    // 只能传String
                    window.localStorage.setItem("preSale-yk", JSON.stringify(this.form));
                } else {
                    console.log("当前浏览器暂不支持Storage");
                }
                Fai.http.post("ykSale/ykSale_h.jsp?cmd=setYkReason", this.form, false).then(result => {
                    if (result.success) {
                        parent.closeFaiBoxDailog(true);
                    }
                });
            },
            showReasonBox() {
                if (!<%=authHdSale%>) {
                    Fai.alert('没有权限', '错误', {
                        confirmButtonText: '确定',
                        closeOnClickModal: true,
                        type: 'error',
                    });
                    return;
                }
                this.dialogVisible = true;
            },
            getYkInfo(aid) {
                Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkInfo", {aid: aid}, false).then(result => {
                    if (result.success) {
                        if (result.success) {
                            this.ykIntent = result.data.intentName;
                        }
                    }
                });
            },
            onClose() {
                parent.closeFaiBoxDailog();
            },
            markReasonChange(val) {
                markReasonChange(val);
            },
            handleCommand(command) {
                this.form.mark = command;
            },
        }
    });

    // 不购买原因修改事件
    function markReasonChange(val) {
        reasonBox.markList = [];	// 清空下拉列表
        var liList = reasonBox.reasonList || [];

        val = val || '';
        if (val === '') {
            return;
        }

        // 获取父级id
        var fatherId = 0;
        liList.forEach(function (item, index) {
            if (item.reason == val) {
                fatherId = item.id;
                return false;		// break;
            }
        });

        // 初始化二级原因li
        fatherId > 0 && liList.forEach(function (item, index) {
            if (item.fatherId != fatherId) {
                return true;	// continue
            }
            reasonBox.markList.push(item);
        });
    }
</script>
</body>
</html>