export default {
	template: `
		<el-form :model="partnerInfo" :rules="rules" ref="partnerInfo" label-width="130px" size="medium">
			<el-form-item v-for="(value,key) in partnerInfo" :key="key" :label="labels[key]" :prop="key" style="margin-top: 18px;">
				<el-input v-model="partnerInfo[key]" style="width: 250px;margin-bottom: 6px"></el-input>
			</el-form-item>
			<el-form-item label="是否大客户类型：">
				<el-radio v-model="isBigCustomer" :label="true">是</el-radio>
				<el-radio v-model="isBigCustomer" :label="false">否</el-radio>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="submitForm()">提交</el-button>
			</el-form-item>
		</el-form>
	`,
	data(){
		return {
			partnerInfo: {
				acct: '',
				pwd: '',
				type: '',
				companyName: '',
				phone: '',
				address: '',
				email: ''
			},
			labels: {
				acct: '账号：',
				pwd: '密码：',
				type: '合作伙伴id：',
				companyName: '公司名称：',
				phone: '手机：',
				address: '地址：',
				email: '邮箱：'
			},
			rules: {
				acct: [
					{
						required: true,
						message: '请输入账号'
					}
				],
				pwd: [
					{
						required: true,
						message: '请输入密码'
					}
				],
				type: [
					{
						required: true,
						message: '请输入合作伙伴id'
					},
					{
						validator(rule,value,callback){
							callback(/\D/g.test(value) ? new Error('请输入纯数字') : undefined);
						}
					}
				],
				companyName: [
					{
						required: true,
						message: '请输入公司名称'
					}
				],
				phone: [
					{
						required: true,
						validator(rule,value,callback){
							callback(/^1[3456789]\d{9}$/.test(value) ? undefined : new Error('请输入正确的手机号码'));
						}
					}
				]
			},
			isBigCustomer: false
		};
	},
	methods: {
		submitForm(){
			this.$refs.partnerInfo.validate(valid => {
				if(valid){
					Vue.http.post(
						'/ajax/hdPartnerAcct_h.jsp?cmd=add',
						Object.assign({},this.partnerInfo,{
							pwd: Fai.md5(this.partnerInfo.pwd),
							isBigCustomer: this.isBigCustomer
						}),
						{emulateJSON: true}
					).then(({data}) => {
						Fai.alert(data.msg);
					});
				}
			});
		}
	}
} 