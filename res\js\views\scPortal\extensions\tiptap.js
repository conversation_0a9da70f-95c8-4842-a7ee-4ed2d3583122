import { Mark } from '@tiptap/core'

/**
 * @see link {https://tiptap.dev/docs/editor/extensions/custom-extensions/create-new|tiptap 创建新扩展}
 */
export const TagMark = Mark.create({
  name: 'tag',

  addAttributes() {
    return {
      tagType: {
        default: 'highlight',
      },
      label: {
        default: null,
      },
    }
  },

  
  parseHTML() {
    return [
      {
        tag: 'span.script-example-tag',
        getAttrs: dom => {
          const labelElement = dom.parentElement?.querySelector('.script-example-tag-label');
          if (labelElement) {
            labelElement.remove();
          }
          return {
            label: labelElement?.textContent || '',
          };
        },
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const tag = ['span', {
      class: `script-example-tag`,
    }, 0]

    if (HTMLAttributes.label) {
      return ['span', { class: 'tag-container' }, 
        tag,
        ['span', { 
          class: 'script-example-tag-label',
        }, HTMLAttributes.label],
        ['span', { class: 'tag-cursor-placeholder' }, '\u200B']
      ]
    }
    return tag
  },


  addCommands() {
    return {
      setTag: attrs => ({ commands }) => {
        return commands.setMark(this.name, attrs)
      },
      unsetTag: () => ({ commands }) => {
        return commands.unsetMark(this.name)
      },
    }
  },

 
})