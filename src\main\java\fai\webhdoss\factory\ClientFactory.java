package fai.webhdoss.factory;

import fai.cli.*;
import fai.hdUtil.exception.HdAssert;
import fai.hdUtil.exception.constant.SystemExceptionEnum;
import fai.hdUtil.rpc.HdFaiClientProxyFactory;
import fai.web.Core;
import org.springframework.context.annotation.*;
import org.springframework.web.context.WebApplicationContext;

/**
 * client工厂
 * 由于作用域是request的，所以不用担心flow不对应问题
 * 单例bean引用多例bean的话，多例bean一定要配置 proxyMode = ScopedProxyMode.TARGET_CLASS,
 * 否则多例bean也会变单例
 *
 * <AUTHOR>
 */
@Configuration
@Lazy
public class ClientFactory {

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdZhuliCli getHdZhuliCli() {
        HdZhuliCli hdZhuliCli = new HdZhuliCli(Core.getFlow());
        HdAssert.System.judge(!hdZhuliCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return hdZhuliCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdOssAffairCli hdOssAffairCli() {
        HdOssAffairCli ossAffairCli = new HdOssAffairCli(Core.getFlow());
        HdAssert.System.judge(!ossAffairCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return ossAffairCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdProfCli hdProfCli() {
        HdProfCli hdProfCli = new HdProfCli(Core.getFlow());
        HdAssert.System.judge(!hdProfCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return hdProfCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    StaffCli staffCli() {
        StaffCli staffCli = new StaffCli(Core.getFlow());
        HdAssert.System.judge(!staffCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return staffCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdRecordCli hdRecordCli() {
        HdRecordCli hdRecordCli = new HdRecordCli(Core.getFlow());
        HdAssert.System.judge(!hdRecordCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return hdRecordCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdGameCli getHdGameCli() {
        HdGameCli hdGameCli = new HdGameCli(Core.getFlow());
        HdAssert.System.judge(!hdGameCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return hdGameCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    HdBssStatCli getHdBssStatCli() {
        HdBssStatCli hdBssStatCli = new HdBssStatCli(Core.getFlow());
        HdAssert.System.judge(!hdBssStatCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return hdBssStatCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    CorpProfCli getCorpProfCli() {
        CorpProfCli CorpProfCli = new CorpProfCli(Core.getFlow());
        HdAssert.System.judge(!CorpProfCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return CorpProfCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    FaiMarketCli getFaiMarketCli() {
        FaiMarketCli faiMarketCli = new FaiMarketCli(Core.getFlow());
        HdAssert.System.judge(!faiMarketCli.init(), SystemExceptionEnum.CLI_INIT_ERROR);
        return faiMarketCli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    ScProtoCli getScProtoCli() {
        ScProtoCli cli = HdFaiClientProxyFactory.createProxy(ScProtoCli.class);
        HdAssert.System.judge(cli == null, SystemExceptionEnum.CLI_INIT_ERROR);
        return cli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    ScTemplateCli getScTemplateCli() {
        ScTemplateCli cli = HdFaiClientProxyFactory.createProxy(ScTemplateCli.class);
        HdAssert.System.judge(cli == null, SystemExceptionEnum.CLI_INIT_ERROR);
        return cli;
    }

    @Bean
    @Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
    ScResCli getScResCli() {
        ScResCli cli = HdFaiClientProxyFactory.createProxy(ScResCli.class);
        HdAssert.System.judge(cli == null, SystemExceptionEnum.CLI_INIT_ERROR);
        return cli;
    }
}
