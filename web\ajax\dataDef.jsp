<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.util.regex.Matcher"%>
<%@ page import="java.util.regex.Pattern"%>
<%@ page import="fai.cli.PreSaleUtilCli"%>
<%@ page import="fai.webhdoss.*"%>

<%!
	
/************************************************************/
/**存储常量 **/
	
	/******************** 拿短信模板 ********************/
	private static String getMessageModelList() throws Exception{
		Log.logDbg("yansen jjjjj=%s",999);
		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(OssSmsDef.ModelInfo.MODELTYPE, ParamMatcher.EQ, OssSmsDef.BusType.HD); 	
		FaiList<Param> modelList =  WebOss.getModelList(searchArg);

		FaiList<Param> infoList = new FaiList<Param>();
		SysFaiSmsPlatform smsPlatform = (SysFaiSmsPlatform)Core.getSysKit(Kid.SYS_FAI_SMS);	
		for(Param p : modelList){
			int templateId = p.getInt(OssSmsDef.ModelInfo.TEMPLATE_ID, 0);
			Param templateInfo = smsPlatform.getTemplateInfo(Web.getFaiCid(), templateId);
			if(Web.getDebug()){//测试环境
				infoList.add(p);
			}
			if(templateInfo == null || templateInfo.getInt(SungoinDef.Info.STATUS, 0) == SungoinDef.Status.TEMPLATE_ADD_SUCCESS){
				infoList.add(p);
		    	continue;
	   		}
		}
		return "{\"success\":true, \"dataList\":" + modelList + "}";
	}
	/********************拿短信模板 ********************/

	/******************** 拿销售列表 ********************/
	private static FaiList<Param> getSaleList(ParamMatcher matcher) throws Exception{
		SysPreSaleHd sysPreSaleHd = (SysPreSaleHd)Core.getSysKit(Kid.SYS_PRESALE_HD);				// presaleHd 的接口
		SearchArg	saleSearchArg = new SearchArg();
		FaiList<Param> infoList = new FaiList<Param>();
		saleSearchArg.matcher = matcher;
		int rt = sysPreSaleHd.getSalesList(new FaiList<String>(),saleSearchArg,infoList);
		if(rt != Errno.OK && rt != Errno.NOT_FOUND){
			Log.logErr("get saleList err mat=%s",saleSearchArg.matcher);
		}
		return infoList;
	}
	/******************** 拿定义的一些数据 ********************/
	private static String getPageDef(HttpServletRequest request) throws Exception{
		String key = request.getParameter("key");
		String tag = request.getParameter("tag");
		Param info = getPageDefInfo(key,tag);
		if(info == null ){
			return "";
		}
		return info.toJson();
	}
	private static Param getPageDefInfo(String key, String tag) throws Exception{
		Param constant = Web.getConf("constant");
		if(key == null ){
			return new Param();
		}
		Param info = constant.getParam(key,new Param());
		Log.logDbg("yansen getData def info=%s",info);
		if(tag != null ){
			Log.logDbg("yansen tag=%s",info.getParam(tag));
			return info.getParam(tag);
		}
		return info;
	}
	/********************拿定义的一些数据 ********************/

	private static String getSaleArpuTag(HttpServletRequest request) throws Exception{
		Param info = getPageDefInfo("comm",null);
		if(info == null ){
			return "";
		}
		Param result = new Param();
		result.setParam("taList",info.getParam("taNameList"));
		result.setParam("arpuSort",info.getParam("arpuSort"));
		result.setList("saleList",getSaleList(new ParamMatcher(HdSaleDef.Info.SID,ParamMatcher.GE,0)));
		result.setBoolean("success",true);
		return result.toJson();
	}

	private static String getSaleArpuABTag(HttpServletRequest request) throws Exception{
		Param info = getPageDefInfo("comm",null);
		if(info == null ){
			return "";
		}
		Param result = new Param();
		result.setParam("arpuSort",info.getParam("arpuABSort"));
		result.setList("saleList",getSaleList(new ParamMatcher(HdSaleDef.Info.SID,ParamMatcher.GE,0)));
		result.setBoolean("success",true);
		return result.toJson();
	}


	
	
/** end **/
/************************************************************/
%>

<%
	String output = "";
	try{
		String cmd = request.getParameter("cmd");
		if (cmd == null) {
			return;
		}else if(cmd.equals("getMessageModelList")){
			output = getMessageModelList();
		}else if(cmd.equals("getPageDef")){
			output = getPageDef(request);
		}else if(cmd.equals("getSaleArpuTag")){
			output = getSaleArpuTag(request);
		}else if(cmd.equals("getSaleArpuABTag")){
			output = getSaleArpuABTag(request);
		}
	}catch (Exception exp){
		Log.logErr(exp);
		output = WebOss.checkAjaxException(exp);
	}
	out.print(output);
%>