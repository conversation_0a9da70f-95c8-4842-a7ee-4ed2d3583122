<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){
	out.println("没有权限");
	return;
}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-cleanupAdm">

		<!--查询条件 start-->
		<div class="resource" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<span>
					<span class="demonstration">开通时间: </span>
					<el-date-picker class="fai-daterange"  type="date" v-model="form.openHdTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
					<el-date-picker class="fai-daterange" type="date" v-model="form.openHdTime.end" placeholder="选择日期" :editable=false></el-date-picker>
				</span>
				<span>
					<span class="demonstration">领取时间: </span><el-checkbox v-model="form.receiveTime.check"></el-checkbox></span>
					<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
					<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
				</span>
				<span>
					标签
					<el-select v-model="form.tag.value">
						<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<span>
					注册来源
					<el-select v-model="form.ta.value">
						<el-option v-for="select in form.ta.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<span>
					剔除原因
					<el-select v-model="form.removeReason.value">
						<el-option v-for="select in form.removeReason.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<!-- <span>
				       是否回到电脑
				   <el-select v-model="form.flag">
				          <el-option  label="全部" value="-1" selected></el-option>
				          <el-option  label="是" value="0"></el-option>
				          <el-option  label="否" value="1"></el-option>
				   </el-select>
				</span> -->
				
				注：防止查询数据量大，时间跨度只能为1天
					<br>
				<div>
					领取数：<span>{{res.allotCount}}</span>
					活动引流资源数量：<span>{{res.activityCount}}</span>
					排除内部账号、经销商客户等：<span>{{res.removeClientCount}}</span>
					排除ta资源数：<span>{{res.removeTaCount}}</span>
					公共条件排除数：<span>{{res.removeCommCount}}</span>
					<br>
					ta为0：<span>{{res.taZeroCount}}</span>&nbsp;&nbsp;&nbsp;
					未命中活动引流数量：<span>{{res.notHitActivityCount}}</span>&nbsp;&nbsp;&nbsp;
					无剔除原因未命中：<span>{{res.notHitNoResCount}}</span><br>
					未命中总数：<span>{{res.notHitCount}}</span>&nbsp;&nbsp;&nbsp;
					其中： SEO:<span>{{res.notHitSeoCount}}</span>&nbsp;&nbsp;&nbsp;
					            互动推广：<span>{{res.notHitPreHdCount}}</span>&nbsp;&nbsp;&nbsp;
					            建站来源：<span>{{res.notHitPreSiteCount}}</span>&nbsp;&nbsp;&nbsp;
					            未知：<span>{{res.notHitNotInCount}}</span>&nbsp;&nbsp;&nbsp;
					            移动端推广：<span>{{res.notHitPreMobiCount}}</span>&nbsp;&nbsp;&nbsp;
					            移动端放量：<span>{{res.notHitMobiCount}}</span>&nbsp;&nbsp;&nbsp;
					            自来：<span>{{res.notHitPreSelfCount}}</span>&nbsp;&nbsp;&nbsp;
					            有创建游戏：<span>{{res.notHitGameCount}}</span>&nbsp;&nbsp;&nbsp;
					    
				</div>
				<br>
				
				<div>
					<el-form-item>
						<el-button type="primary"  @click="getDataListByArgs('select')">查询</el-button>
						<el-button type="primary"  @click="getDataListByArgs('export')">导出</el-button>
					</el-form-item>
				</div>

				<div>
					<el-table :data="form.dataList" style="width: 1000" max-height="620" stripe>
						<el-table-column  prop="aid"  :show-Overflow-Tooltip=true label="aid" width="150"  align="center">
							<template slot-scope="scope">
								<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>
							</template>
						</el-table-column>
						<el-table-column  prop="mobile" label="注册手机" width="120"  align="center"></el-table-column>
						<el-table-column  prop="openHdTime" label="开通互动时间" width="160"  align="center"></el-table-column>
						<el-table-column  prop="regTime" label="注册时间" width="160" align="center"></el-table-column>
						<el-table-column  prop="taGroupName" label="注册来源" width="120"  align="center"></el-table-column>
						<el-table-column  prop="receiveTime" label="领取时间" width="160"  align="center"></el-table-column>
						<el-table-column  prop="tag" label="命中标签" width="160"  align="center"></el-table-column>
						<el-table-column  prop="firstCreateGameTime" label="首次创建游戏" width="160"  align="center"></el-table-column>
						<el-table-column  prop="removeClient" label="剔除原因1" width="120"  align="center"></el-table-column>
						<el-table-column  prop="removeComm" label="剔除原因2" width="120"  align="center"></el-table-column>
						<el-table-column  prop="removeTa" label="剔除原因3" width="120"  align="center"></el-table-column>
						<el-table-column  prop="flag" label="是否回到电脑" width="120"  align="center"></el-table-column>
					</el-table>
				</div>
				<div class="block">
						<el-pagination  @current-change="getDataListByArgs('select')" :current-page.sync="pageData.currentPage" 
							:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
						</el-pagination>
				</div>
			</el-form>
		</div>
		<!--查询条件 end-->

	</body>


	<script type="text/javascript">
		var localTime = new Date();
		var rerourceAnalyze = new Vue({
			el: '.resource',
			data: {
				pageData:{
					size:10,
					total:0,
					currentPage:1
				},
				res:{
					removeClientCount:0,
					removeCommCount:0,
					removeTaCount:0,
					activityCount:0,
					allotCount:0,
				},
				form:{
					openHdTime:{
						start:localTime,
						end:localTime,
					},
					receiveTime:{
						check:false,
						start:localTime,
						end:localTime,
					},
					dataList:[],
					checkedConditions:[],
					tag:{
						name:"标签",
						value:-1,
						labelList:[{name:"全部",label:"-1"}]
					},
					ta:{
						name:"注册来源",
						value:-1,
						labelList:[{name:"全部",label:"-1"}]
					},
					removeReason:{
						name:"剔除原因",
						value:"全部",
						labelList:[
							{name:"全部",label:"-1"},{name:"网站销售领取/经销商/",label:"1"},{name:"公共条件/个人/学生/内部账号",label:"2"}
						]
					},
					flag:"全部",
					exportFlag:false,/** 判断是否导出 */
				},
				checkAll:false,
				
				conditions: [
					{
						name:"排除常规条件",
						key:"normalCondition"
					},
					{
						name:"创建游戏",
						key:"createGame",
					},
					{
						name:"首次创建游戏",
						key:"firstCreateGame",
					},
				],
			},
			created:function(){
				this.getDataListByArgs('select');
				this.getDefList();
			},
			methods: {
				getDataListByArgs(val) {
					var arg = {
						"cmd":"resourceAnalyze",
						"openHdTimeStart":Fai.tool.getDateTimeStart(this.form.openHdTime.start),
						"openHdTimeEnd":Fai.tool.getDateTimeEnd(this.form.openHdTime.end),
						"receiveTimeStart":Fai.tool.getDateTimeStart(this.form.receiveTime.start),
						"receiveTimeEnd":Fai.tool.getDateTimeEnd(this.form.receiveTime.end),
						"receiveTimeCheck":this.form.receiveTime.check,
						"checkedConditions":JSON.stringify(this.form.checkedConditions),
						"currentPage":this.pageData.currentPage,
						"tag":this.form.tag.value,
						"removeReason":this.form.removeReason.value,
						"ta":this.form.ta.value,
						"exportFlag":false,
						/* "flag":this.form.flag, */
					};
					if("export" === val){
						arg.exportFlag=true;
						console.info(arg);
						window.open("/ajax/hdSale_h.jsp?cmd=resourceAnalyze" + Fai.tool.parseJsonToUrlParam( arg));
					}else{
						Fai.http.post("hdSale_h.jsp?cmd=resourceAnalyze",arg, false).then(result => {
								if(result.success){
									this.form.dataList = result.list;
									this.pageData.total = result.total;
									this.res = result.res;
								}
								
						});
					}
				},
				getDefList(){
					var arg = {
						"cmd":"getPageDef",
						"key":"comm"
					}
					Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
						var hdSaleList = response.data;
						this.form.ta = hdSaleList.taList;
						var noTag = {
							label:999,
							name:"未命中"
						}
						var all = {
							label:-1,
							name:"全部"
						}

						//下面是为了整合顺便和加多一个选项
						var tagList = hdSaleList.tag;
						var tempList = [];
						tempList.push(all);
						tempList.push(noTag);
						for(var i=1;i<tagList.labelList.length;i++){
							tempList.push(tagList.labelList[i]);
						}
						tagList.labelList = tempList;
						this.form.tag = tagList;


					}, response => {
						this.$message({
						type: 'warning',
						message: '系统错误!'
						});
					});
				},
				handleCheckAllChange(val){
					console.info(val);
					this.form.checkedConditions = val ? this.conditions : [];
					console.info(this.form.checkedConditions);
				},
				handleCheckedCitiesChange(value){
					let checkedCount = value.length;
					this.checkAll = checkedCount === this.conditions.length;
					this.isIndeterminate = checkedCount > 0 && checkedCount < this.conditions.length;
				},
				handleCheckAllOther(val){
					console.info(val);
					this.checkedOthers = val ? this.otherInfo : [];
					console.info(this.checkedOthers);
				},
				handleCheckedOtherChange(value){
					let checkedCount = value.length;
					this.checkAll = checkedCount === this.conditions.length;
					this.isIndeterminate = checkedCount > 0 && checkedCount < this.conditions.length;
				},
				
		    }
		});


	</script>

</html>



