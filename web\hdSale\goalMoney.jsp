<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%
    if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
        out.println("没有权限");
        return;
    }%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>录入目标业绩</title>
    <%@ include file="/comm/link.jsp.inc"%>
    <%@ include file="/comm/script.jsp.inc"%>
    <link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
</head>
<body id="hdsale-countStat">

<div class="fai-goalMoney-submit" v-cloak>
    <h2>请输入<span style="color:red;">{{year}}</span>年<span style="color:red;">{{checkMonth}}</span>月的目标业绩</h2>
    <el-form :inline="true" size="mini">
	    <el-form-item label="">
			<el-select v-model="checkMonth" placeholder="请选择">
			    <el-option
			      v-for="item in form.selectMonth"
			      :key="item"
			      :label="item"
			      :value="item">
			    </el-option>
  			</el-select>月
		</el-form-item>
	</el-form>
    <el-table :data="form.saleList" row-key="rowkey" stripe border max-height="680">
        <el-table-column label="销售" width="200px" fixed>
            <template slot-scope="scope">
                <span>{{scope.row.nickName}}</span>
            </template>
        </el-table-column width="200px">
        <el-table-column label="目标业绩" width="300px">
            <template slot-scope="scope">
                <el-input type="text" v-model="scope.row.goalMoney" class="short-input" :label="scope.row.name" :value="scope.row.goalMoney" :key="scope.row.acct"  v-bind:disabled=" checkMonth != (new Date().getMonth())+1"></el-input></div>
            </template>
        </el-table-column>
        <el-table-column label="报备业绩" width="300px">
            <template slot-scope="scope">
                <el-input type="text" v-model="scope.row.approveMoney" class="short-input" :label="scope.row.name" :value="scope.row.approveMoney" :key="scope.row.acct"></el-input></div>
            </template>
        </el-table-column>
        <el-table-column label="报备单数" width="300px">
            <template slot-scope="scope">
                <el-input type="text" v-model="scope.row.approveCount" class="short-input" :label="scope.row.name" :value="scope.row.approveCount" :key="scope.row.acct"></el-input></div>
            </template>
        </el-table-column>

</el-table>

<el-button type="primary"  @click="setGoalMoney">确认设置</el-button>
</div>

</body>


<script type="text/javascript">
    var faiDataList = new Vue({
        el: '.fai-goalMoney-submit',
        data: {
            form: {
                saleList: [],
                selectMonth: [],
                isCurrentMonth: true
            },
            count: 0,
            year: new Date().getFullYear(),
            month: new Date().getMonth()+1,
            checkMonth: new Date().getMonth()+1
        },
        created:function(){
            Fai.http.post("hdSale/setGoalMoney_h.jsp?cmd=getHdSaleAchievement", "", false).then(result => {
                if(result.success){
                    this.form.saleList = result.saleList;
                }
             });
            var now = new Date();
            var day = now.getDate();		//当前日
            if(day <= 10){
            	this.form.selectMonth.push(this.month-1);
            	this.form.selectMonth.push(this.month);
            }else{
            	this.form.selectMonth.push(this.month);
            }
        },
        watch:{
        	'checkMonth'(){
        		var arg ={
        				"month":this.checkMonth
        		};
        		if(this.checkMonth != new Date().getMonth()+1 && this.checkMonth ==12 && new Date().getMonth()+1== 1){
        			this.year = new Date().getFullYear()-1;
        		}
        		Fai.http.post("hdSale/setGoalMoney_h.jsp?cmd=getHdSaleAchievement" , arg, false).then(result => {
                    if(result.success){
                        this.form.saleList = result.saleList;
                    }
                 });
        	}
       	},
        methods: {
            setGoalMoney(){
                var saleList = this.form.saleList;
                var map = {};
                for(let item of saleList) {
                    let goalMoney = item.goalMoney+"";
                    let approveMoney = item.approveMoney+"";
                    let approveCount = item.approveCount+"";


                    if(!/^\d+$/.test(goalMoney) || !/^-?\d+\.?\d*$/.test(approveMoney) || !/^\d+$/.test(approveCount)){
                        this.$message({
                            type: 'warning',
                            message: "输入格式有误"
                        });
                        return;
                    }

                    var saleInfo = {};
                    saleInfo['goalMoney'] = goalMoney;
                    saleInfo['approveMoney'] = approveMoney;
                    saleInfo['approveCount'] = approveCount;
                    map[item.acct] = saleInfo;
                }

                var arg = {
                    "cmd": "setGoalMoney",
                    "goalMoneyMap": JSON.stringify(map),
                    "month": this.checkMonth
                }

                Fai.http.post("hdSale/setGoalMoney_h.jsp?cmd=setGoalMoney", arg, false).then(result => {
                    if(result.success){
                        this.$message({
                            type: 'success',
                            message: result.msg
                        });
                    }
                });
            }
        }

    });



</script>

</html>



