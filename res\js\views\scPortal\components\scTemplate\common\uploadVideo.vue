<template>
  <div>
    <el-upload
      ref="upload"
      accept=".mp4,.mov"
      name="filedata"
      :action="actionUrl"
      :http-request="uploadFile"
      :class="{'hideUploadBtn': hideUploadBtn}"
      :file-list="curFileList"
      :show-file-list="false"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeFileUpload"
      :on-remove="fileRemove"
      :limit="limit"
      :disabled="isUploading"
    >
      <div v-if="!hideUploadBtn" class="upload-btn">
        <div class="upload-btn-content">
          <p class="text-[14px] text-center">{{ title }}</p>
          <el-progress 
            v-if="isUploading" 
            type="circle" 
            :percentage="percentage" 
            :width="30"
            :stroke-width="3"
            class="mt-[5px]">
          </el-progress>
        </div>
      </div>
      <!-- <p class="text-[14px] text-center">{{ title }}</p> -->
    </el-upload>
    <!-- 视频封面列表 -->
    <div class="image-list">
      <div 
        v-for="item in curFileList" 
        :key="item.id" 
        class="image-item"
        @mouseenter="item.showMask = true"
        @mouseleave="item.showMask = false"
      >
        <img :src="item.url" :alt="item.name" class="image-preview">
        
        <!-- 悬停遮罩 -->
        <div v-show="item.showMask" class="image-mask">
          <div class="mask-buttons">
            <el-button 
              type="text" 
              size="mini" 
              @click="handlePictureCardPreview(item)"
              class="mask-btn preview-btn"
            >
              <i class="el-icon-zoom-in"></i>
            </el-button>
            <el-button 
              type="text" 
              size="mini" 
              @click="fileRemove(item)"
              class="mask-btn delete-btn"
            >
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog 
      title="视频预览" 
      :visible.sync="dialogVisible" 
      height="60%" 
      custom-class="video-dialog"
      @close="handleDialogClose"
    >
      <video ref="previewVideo" :src="dialogVideoUrl" style="width: 100%;" controls></video>
    </el-dialog>
  </div>
</template>

<script>
const splitMB = 10 * 1024 * 1024; // 10MB
export default {
  name: 'UploadVideo',
  props: {
    title: {
      type: String,
      default: '上传视频'
    },
    // 默认展示文件列表
    fileList: {
      type: Array,
      default: () => []
    },
    // 限制上传数量
    limit: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      isUploading: false,
      percentage: 0,
      curFileList: [],
      dialogVisible: false,
      dialogVideoUrl: '',
      chunkSize: 2 * 1024 * 1024, // 默认2MB切片大小
      totalChunks: 0, // 总分片数
      uploadedChunks: 0, // 已上传分片数
    }
  },
  computed: {
    hideUploadBtn() {
      return this.curFileList.length >= this.limit;
    },
    actionUrl() {
      return `/api/template/uploadTmpFile`;
    }
  },
  created() {
    // console.log(this.fileList, 'fileList');
    this.curFileList = this.fileList.map(item => ({
      ...item,
      showMask: false
    }));
  },
  methods: {
    // 根据文件大小计算分片大小
    getChunkSize(fileSize) {
      if (fileSize >= 0 && fileSize < 20 << 10 << 10) { // 0-20MB
        return 5 << 10 << 10; // 5MB
      } else if (fileSize >= 20 << 10 << 10 && fileSize < 50 << 10 << 10) { // 20-50MB
        return 5 << 10 << 10; // 5MB
      } else if (fileSize >= 50 << 10 << 10 && fileSize < 100 << 10 << 10) { // 50-100MB
        return 8 << 10 << 10; // 8MB
      } else if (fileSize >= 100 << 10 << 10 && fileSize < 500 << 10 << 10) { // 100-500MB
        return 10 << 10 << 10; // 10MB
      } else { // 500MB以上
        return 20 << 10 << 10; // 20MB
      }
    },
    async beforeFileUpload(file) {
      this.isUploading = true;
      this.percentage = 0;
      this.uploadedChunks = 0;
      
      if (file.size > splitMB) {
        // 大于20MB才需要分片上传
        this.chunkSize = this.getChunkSize(file.size);
        this.totalChunks = Math.ceil(file.size / this.chunkSize);
        console.log(`文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB, 分片大小: ${(this.chunkSize / 1024 / 1024).toFixed(2)}MB, 总分片数: ${this.totalChunks}`);
        // 计算 MD5
        file.fileMd5 = this.calcFileMd5(file);
      } else {
        // 小于等于20MB的文件不需要分片
        this.totalChunks = 1;
        this.chunkSize = file.size;
      }
      console.log(file, 'file信息');
    },
    // 计算文件 MD5
    calcFileMd5(file) {
      const timeStamp = typeof file.lastModifiedDate === 'object' ? file.lastModifiedDate.getTime() : files.lastModifiedDate;
      return Fai.md5(file.name + file.size + file.uid + timeStamp)
    },
    async uploadFile({file}) {
      // 判断文件是否需要分片上传(20MB)
      if (file.size <= splitMB) {
        // 小于等于20MB的文件直接上传
        const formData = new FormData();
        formData.append('filedata', file);
        
        // 使用XMLHttpRequest来监听上传进度
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          
          xhr.upload.onprogress = (e) => {
            if (e.lengthComputable) {
              this.percentage = Math.round((e.loaded / e.total) * 100);
            }
          };
          
          xhr.onload = () => {
            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                  this.fileUploadSuccess(response);
                  resolve();
                } else {
                  this.$message({
                    type: "error",
                    message: response.msg || "上传失败！",
                  });
                  this.resetUploadStatus();
                  reject(new Error(response.msg));
                }
              } catch (error) {
                console.error('解析响应失败:', error);
                this.$message({
                  type: "error",
                  message: "上传失败！",
                });
                this.resetUploadStatus();
                reject(error);
              }
            } else {
              this.$message({
                type: "error",
                message: "上传失败！",
              });
              this.resetUploadStatus();
              reject(new Error('Upload failed'));
            }
          };
          
          xhr.onerror = () => {
            console.error('上传失败');
            this.$message({
              type: "error",
              message: "上传失败！",
            });
            this.resetUploadStatus();
            reject(new Error('Upload failed'));
          };
          
          xhr.onabort = () => {
            console.error('上传被取消');
            this.resetUploadStatus();
            reject(new Error('Upload aborted'));
          };

          xhr.open('POST', '/api/template/uploadTmpFile', true);
          xhr.send(formData);
        });
      }

      // 大于20MB的文件进行分片上传
      let initSize = 0
      console.log(file, '切片信息:file')
      
      while (initSize < file.size) {
        const end = Math.min(initSize + this.chunkSize, file.size)
        const chunk = file.slice(initSize, end)
        
        console.log('切片信息:', {
          chunk,
          file,
          initSize,
          end,
          chunkSize: chunk.size,
          fileName: file.name,
          chunkType: chunk.type,
          chunkName: chunk.name
        })
        
        const formData = new FormData()
        formData.append('filedata', chunk, file.name)
        formData.append('fileMd5', file.fileMd5)
        formData.append('totalSize', file.size)
        formData.append('complete', end >= file.size)
        formData.append('initSize', initSize)
        
        // 调试 FormData 内容
        console.log('FormData 内容:')
        
        try {
          const response = await Vue.http.post('/api/template/advanceUploadFile4Sc', formData)
          console.log('上传成功:', response)
          
          // 更新进度
          this.uploadedChunks++;
          this.percentage = Math.round((this.uploadedChunks / this.totalChunks) * 100);
          console.log(`分片上传进度: ${this.uploadedChunks}/${this.totalChunks} (${this.percentage}%)`);
          
          const responseData = response.body
          if (responseData.success) {
            if (end >= file.size) {
              this.fileUploadSuccess(responseData)
            }
          } else {
            this.$message({
              type: "error",
              message: response.msg || "上传失败！",
            });
            this.resetUploadStatus();
          }
        } catch (error) {
          console.error('上传失败:', error.response?.data || error)
          // 检查错误信息中的具体问题
          if (error.response?.data?.message) {
            console.error('错误详情:', error.response.data.message)
          }
          this.resetUploadStatus();
        }
        
        initSize = end
      }
    },
    fileUploadSuccess(response) {
      this.$message({
        type: "success", 
        message: "文件上传成功！",
      });
      const data = response.data;
      const file = {
        name: data.name,
        url: data.firstImgUrl,   // 封面图用首帧图
        id: data.id,     // 视频id
        type: data.type, // 视频类型
        videoUrl: data.path, // 视频url
        coverId: data.firstImgId, // 视频封面id
        coverUrl: data.firstImgUrl, // 视频封面url
        coverType: data.firstImgType, // 视频封面类型
        showMask: false // 是否显示遮罩
      }
      this.$emit('upload-success', file);
      this.curFileList.push(file);
    
      this.isUploading = false;
      this.percentage = 0;
      this.uploadedChunks = 0;
      this.totalChunks = 0;
    },
    fileUploadProgress(event) {
      // this.percentage = Math.round(event.percent);
    },
    fileUploadError(err) {
      this.$message({
        type: "error",
        message: "系统繁忙，请稍后重试！",
      });
      this.resetUploadStatus();
    },
    // 重置上传状态
    resetUploadStatus() {
      this.isUploading = false;
      this.percentage = 0;
      this.uploadedChunks = 0;
      this.totalChunks = 0;
      // 重置 el-upload 组件
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
    },
    fileRemove(file, fileList) {
      const index = this.curFileList.findIndex(item => item.id === file.id);
      if (index > -1) {
        this.curFileList.splice(index, 1);
      }
      this.$emit('upload-remove');
      console.log(this.curFileList, file, 'fileRemove');
    },
    // 视频预览
    handlePictureCardPreview(file) {
      console.log(file, 'handlePictureCardPreview');
      this.dialogVideoUrl = file.videoUrl;
      this.dialogVisible = true;
    },
    handleDialogClose() {
      const videoElement = this.$refs.previewVideo;
      if (videoElement) {
        videoElement.pause();
        videoElement.currentTime = 0;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 移除外部样式引入,改用scoped
.el-upload {
  width: 100%;
}
.el-progress {
  margin-bottom: 10px;
}
.hideUploadBtn {
  // ::v-deep .el-upload {
    display: none;
  // }
}
::v-deep .video-dialog {
  margin: 100px auto;
  height: 70vh;
  .el-dialog__body {
    height: 100%;
    video {
      height: 100%;
    }
  }
}

.upload-btn {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #409eff;
  }
  
  .upload-btn-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 8px;
  }
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  
  .image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .image-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: space-around;
    align-items: center;
    transition: opacity 0.3s;
    
    .mask-buttons {
      display: flex;
      // gap: 10px;
      
      .mask-btn {
        display: flex;
        justify-content: center;
        width: 20px;
        color: white;
        background: transparent;
        border: none;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
        
        &.preview-btn:hover {
          background: rgba(64, 158, 255, 0.8);
        }
        
        &.delete-btn:hover {
          background: rgba(245, 108, 108, 0.8);
        }
      }
    }
  }
}

::v-deep {
  .el-upload--picture-card {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }
  .el-upload-list--picture-card .el-upload-list__item {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }
}
</style>
