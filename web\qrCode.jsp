<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="java.awt.*"%>
<%@ page import="java.awt.image.*"%>
<%@ page import="javax.imageio.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.nio.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.net.URL" %>
<%@ page import="java.io.ByteArrayInputStream" %>
<%@ page import="fai.hdUtil.HdTimer"%>
<%@ page import="fai.weboss.*"%>
<%!	

	private void printUrlQRCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
		int type =  Parser.parseInt(request.getParameter("type"),0);
		String siteUrl = request.getParameter("siteUrl");
		if(siteUrl == null){
			return;
		}
	
		int imageSize = 400;
		try {
			BufferedImage image;
			if (type == 1) {
				image = QRCode.encodeToBufferedImageForMobi(siteUrl, imageSize, imageSize,null);
				String filename = siteUrl + ".gif";
				response.setHeader("Content-Type", "application/octet-stream");
				response.setHeader("Content-Disposition", "attachment;filename=\"" + filename + "\"");
			} else {
				image = QRCode.encodeToBufferedImage(siteUrl, imageSize, imageSize);
				response.setHeader("Content-Type", "image/gif");
			}
			ImageIO.write(image, "gif", response.getOutputStream());
		} catch(Exception e) {
			App.logErr(e, "fail encode qrcode;siteUrl=%s;", siteUrl);
	    	response.sendError(response.SC_NOT_FOUND);
			return;
		}
		
	}


%>
<%
	String cmd = request.getParameter("cmd");
	if (cmd == null){
		return;
	}
	try{
		out.clear(); 
		if (cmd.equals("qrurl")){
			printUrlQRCode(request, response);
		}
		
	}catch (Exception exp){
		WebOss.checkAjaxException(exp);
	}
%>