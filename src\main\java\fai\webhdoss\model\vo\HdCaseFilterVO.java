package fai.webhdoss.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.codehaus.jackson.annotate.JsonProperty;

import java.util.Date;

@Data
@ApiModel("案例筛选库VO")
public class HdCaseFilterVO {

    @ApiModelProperty("案例筛选库活动的唯一id")
    int id;

    @ApiModelProperty("原账户id")
    int oAid;

    @ApiModelProperty("原活动id")
    int oGameId;

    @ApiModelProperty("原企业名称")
    String oAcctName;

    @ApiModelProperty("复制后活动Id")
    int gameId;

    @ApiModelProperty("复制后的aid")
    int caseAid;

    @ApiModelProperty("活动模板名称")
    String gameName;

    @ApiModelProperty("活动二维码")
    String gameCode;

    @ApiModelProperty("行业")
    String trade;

    @ApiModelProperty("场景")
    String scene;

    @ApiModelProperty("活动链接")
    String gameUrl;

    @ApiModelProperty("活动分类")
    String label;

    @ApiModelProperty("活动亮点")
    String activityBright;

    @ApiModelProperty("活动功能")
    String activeFunc;

    @ApiModelProperty("复制时间")
    String createTime;

    @ApiModelProperty("复制人")
    String createPerson;

    @ApiModelProperty("该案例是否从案例筛选库导入案例库")
    int flag;


    public int getoAid() {
        return oAid;
    }

    @JsonProperty(value = "oAid")
    public void setoAid(int oAid) {
        this.oAid = oAid;
    }

    public int getoGameId() {
        return oGameId;
    }

    public String getoAcctName() {
        return oAcctName;
    }
    @JsonProperty(value = "oAcctName")
    public void setoAcctName(String oAcctName) {
        this.oAcctName = oAcctName;
    }

    @JsonProperty(value = "oGameId")
    public void setoGameId(int oGameId) {
        this.oGameId = oGameId;
    }

}
