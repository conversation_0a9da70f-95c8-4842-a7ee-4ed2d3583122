<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.webhdoss.app.*" %>
<%
    if (!WebHdOss.checkSession(response)) {
        return;
    }
    if (!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)) {
        out.println("没有权限");
        return;
    }
%>


<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>数据可视化中心</title>
    <%@ include file="/comm/link.jsp.inc" %>
    <%@ include file="/comm/script.jsp.inc" %>
    <%@ include file="/comm/echarts.jsp.inc" %>
</head>

<body>
<div class="fai-data-visual" v-cloak>
    <el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
        <el-form-item label="成交时间">
            <el-checkbox v-model="form.dealFlag"></el-checkbox>
            <el-date-picker class="fai-date" v-model="form.dealTimeBeg" type="date" value-format="yyyy-MM-dd"
                            placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
            -
            <el-date-picker class="fai-date" v-model="form.dealTimeEnd" type="date" value-format="yyyy-MM-dd"
                            placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
        </el-form-item>
        <el-form-item label="领取时间">
            <el-checkbox v-model="form.receiveTimeFlag"></el-checkbox>
            <el-date-picker class="fai-date" v-model="form.receiveTimeBeg" type="date" placeholder="开始日期"
                            value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
            -
            <el-date-picker class="fai-date" v-model="form.receiveTimeEnd" type="date" placeholder="结束日期"
                            value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
        </el-form-item>

        <el-form-item label="账号">
            <el-select v-model="form.acct" filterable>
                <el-option label="所有" value="all"></el-option>
                <el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sacct"
                           :key="preSale.sacct"></el-option>
            </el-select>
        </el-form-item>

        <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="onSubmit">查询</el-button>
        </el-form-item>
    </el-form>

    <hr/>


</div>


</body>

<script type="text/javascript">

    var faiSearchObj = new Vue({
        el: '.fai-data-visual',
        data: {
            form: {//这里是为了填充默认值
                dealFlag: true,
                dealTimeBeg: Fai.tool.dateFormatter(new Date().getTime() - 7 * 24 * 3600 * 1000),
                dealTimeEnd: Fai.tool.dateFormatter(new Date()),
                receiveTimeFlag: false,
                receiveTimeBeg: Fai.tool.dateFormatter(new Date().getTime() - 7 * 24 * 3600 * 1000),
                receiveTimeEnd: Fai.tool.dateFormatter(new Date()),
                acct: 'all'
            },
            preSaleList: [],
            editable: false,
            clearable: false,
        },
        created: function () {
            //获取销售列表
            Fai.http.post("hdSale_h.jsp?cmd=getPreSale", "", false).then(result => {
                if (result.success) {
                    this.preSaleList = result.dataList;
                }
            });
        },
        methods: {
            onSubmit() {

            }
        }
    })


</script>

</html>

