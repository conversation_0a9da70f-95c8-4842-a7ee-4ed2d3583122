<template>
  <div class="sc-category-list">
    <el-tabs v-model="activeName">
      <el-tab-pane label="背景音乐" :name="String(ScMaterialType.BG_MUSIC)"></el-tab-pane>
      <el-tab-pane label="配音" :name="String(ScMaterialType.DUBBING)"></el-tab-pane>
    </el-tabs>
    <div class="mb-[20px] sc-list-header">
      <el-button type="primary" @click="handleAdd">添加分类</el-button>
    </div>
    <div class="flex mb-[20px] sc-list-filter">
      <el-input
        size="small"
        class="mr-[30px] sc-list-input !w-[240px]"
        v-model.trim="filterData.name"
        placeholder="请输入搜索名称"
        :clearable="true"
        @keyup.enter.native="searchList"
        @clear="searchList"
      />
      <!-- 分类 -->
      <el-button size="small" type="primary" @click="searchList"
        >搜索</el-button
      >
    </div>
    <el-table :data="dataList">
      <el-table-column label="ID" prop="id" width="150"/>
      <el-table-column label="名称" prop="name" />
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="mt-[20px] flex justify-end"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageConfig.pageNo"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageConfig.pageLimit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pageConfig.total"
    >
    </el-pagination>

    <scCategoryEdit
      v-if="editConfig.dialogVisible"
      :editInfo="editConfig.itemInfo"
      :editType="Number(activeName)"
      @confirm="handleEditConfirm"
      @close="handleEditClose"
    />
  </div>
</template>

<script>
import scCategoryEdit from "../scCategoryEdit/index.vue";
import { ScMaterialType } from '@/views/scPortal/config/index.js';
import { getCategoryList, deleteCategory } from '@/views/scPortal/api/scCategory.js';

export default {
  name: "ScCategoryList",
  components: {
    scCategoryEdit,
  },
  data() {
    return {
      ScMaterialType,
      activeName: String(ScMaterialType.BG_MUSIC),
      filterData: {
        name: '', // 搜索名称
      },
      dataList: [], // 表格数据
      pageConfig: { // 分页
        pageNo: 1,
        pageLimit: 10,
        total: 0,
      },
      editConfig: {
        dialogVisible: false, // 是否显示编辑对话框
        itemInfo: {}, // 编辑信息
      }
    };
  },
  watch: {
    activeName: {
      handler(val) {
        this.resetFilterData();
        this.searchList();
      },
      immediate: true
    },
  },
  created() {
    this.searchList();
  },
  methods: {
    resetFilterData() {
      this.filterData.name = ''
    },
    searchList() {
      this.pageConfig.pageNo = 1;
      this.getDataList();
    },
    getDataList() {
      getCategoryList({
        name: this.filterData.name,
        type: Number(this.activeName),
        pageNo: this.pageConfig.pageNo,
        pageLimit: this.pageConfig.pageLimit,
      }).then(res => {
        if (res.success) {
          this.dataList = res.data;
          this.pageConfig.total = res.totalSize;
        } else {
          this.$message.error(res.msg || '系统错误，请稍后再试');
        }
      });
    },
    handleAdd() {
      this.editConfig.dialogVisible = true
      this.editConfig.itemInfo = {};
    },
    handleEdit(row) {
      this.editConfig.dialogVisible = true;
      this.editConfig.itemInfo = row;
    },
    handleDelete(row) {
      this.$confirm('确定删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        deleteCategory({
          id: row.id,
        }).then((res) => {
          if (res && res.success) {
            this.$message.success('删除成功');
            // 判断是否为最后一页且只有一条数据
            if (this.dataList.length === 1 && this.pageConfig.pageNo > 1) {
              // 如果是最后一页的最后一条数据，删除后返回上一页
              this.pageConfig.pageNo--;
            }
            this.getDataList();
          } else {
            this.$message.error(res.msg || '删除失败，请稍后再试');
          }
        }).catch(err => {
          this.$message.error('删除失败，请稍后再试');
        });
      });
    },
    handleEditClose() {
      this.editConfig.dialogVisible = false;
    },
    handleEditConfirm() {
      this.editConfig.dialogVisible = false;
      this.getDataList();
    },
    handleSizeChange(size) {
      this.pageConfig.pageLimit = size;
      this.getDataList();
    },
    handleCurrentChange(page) {
      this.pageConfig.pageNo = page;
      this.getDataList();
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__item{
  font-size: 16px;
  font-weight: bold;
}
</style>
