package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateVO {
    private int id;

    // 原型id
    @Min(value = 1, message = "protoId不能为空")
    private int protoId;

    // 名称
    @NotBlank(message = "名称不能为空")
    private String name;

    // 原型类型（视频：0，图文：1）
    private int type;

    // 行业
    private int industry;

    // 场景
    private int scene;

    // 系统提示词
    private String sysKeyWords;

    // 封面图
    @NotNull(message = "封面图不能为空")
    private ScTemplateCommResVO cover;

    @NotNull(message = "示例资源不能为空")
    private ScTemplateSettingVO setting;

    // dify工作流—输入表单：基于原型inputForm新增内容
    @NotNull(message = "表单数据不能为空")
    private List<ScTemplateInputFormVO> inputFormList;

    // dify工作流—脚本结构-通用配置
    @NotNull(message = "内容配置数据不能为空")
    private ScTemplateScriptVO script;

    // 原型编辑—资源表单
    @NotNull(message = "素材组配置不能为空")
    private List<ScTemplateResFormVO> resFormList;
}
