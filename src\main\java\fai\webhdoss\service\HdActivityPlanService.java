package fai.webhdoss.service;


import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.comm.util.SearchArg;

public interface HdActivityPlanService {
    /**
     * 创建方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    Integer addActivityPlan(String activityPlan) throws Exception;

    /**
     * 获取方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    Param getHdActivityPlan(int id) throws Exception;

    /**
     * 获取方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    FaiList<Param> getHdActivityPlanList(SearchArg searchArg) throws Exception;

    /**
     * 更新方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    void updateHdActivityPlan(String hdActivityPlan) throws Exception;

    /**
     * 删除方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    void delHdActivityPlan(int planId) throws Exception;

    /**
     * 获取某个行业下或者所有的活动方案数量
     *
     * @param tradeId 行业id 负数时获取所有
     * @return
     * <AUTHOR>
     */
    int getActivityPlanCount(int tradeId, int relationFlyerState) throws Exception;
}
