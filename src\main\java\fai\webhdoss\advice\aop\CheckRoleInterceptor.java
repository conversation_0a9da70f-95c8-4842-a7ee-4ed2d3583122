package fai.webhdoss.advice.aop;

import fai.app.HdOssStaffDef;
import fai.comm.util.Log;
import fai.hdUtil.JsonResult;
import fai.webhdoss.WebHdOss;
import fai.webhdoss.annotation.CheckRole;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025/8/21 16:46
 */
@Component
public class CheckRoleInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        try {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                // 获取Controller类上的CheckRole注解
                CheckRole classCheckRole = handlerMethod.getBeanType().getAnnotation(CheckRole.class);
                // 获取方法上的CheckRole注解
                CheckRole methodCheckRole = handlerMethod.getMethodAnnotation(CheckRole.class);

                if (WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL)) {
                    return true;
                }

                // 如果类和方法都没有注解，直接不允许访问
                if (classCheckRole == null && methodCheckRole == null) {
                    returnJson(response, "暂无操作权限!");
                    return false;
                }

                // 先检查类级别注解
                if (classCheckRole != null) {
                    if (methodCheckRole != null) {
                        boolean hasMethodAuth = WebHdOss.checkHdOssAuth(methodCheckRole.value());
                        if (hasMethodAuth) {
                            return true;
                        }
                    }
                    boolean hasClassAuth = WebHdOss.checkHdOssAuth(classCheckRole.value());
                    if (hasClassAuth) {
                        return true;
                    }
                }

                // 检查方法级别注解（如果存在）
                if (methodCheckRole != null) {
                    boolean hasMethodAuth = WebHdOss.checkHdOssAuth(methodCheckRole.value());
                    if (hasMethodAuth) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Log.logErr(-1, e.getMessage());
        }
        returnJson(response, "权限验证出错!");
        return false;
    }

    private void returnJson(HttpServletResponse response, String message) {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");

        try (PrintWriter writer = response.getWriter()) {
            writer.print(JsonResult.error(message));
        } catch (IOException e) {
            System.out.println(Arrays.toString(e.getStackTrace()));
        }
    }
}