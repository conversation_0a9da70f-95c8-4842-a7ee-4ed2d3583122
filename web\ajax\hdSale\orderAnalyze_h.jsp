<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false"%>

<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.cli.BssStatCli"%>
<%@ page import="fai.cli.*"%>
<%@ page import="java.sql.PreparedStatement"%>
<%@ page import="java.util.Calendar"%>

<%!
	public String resourceOrderAnalyze(HttpServletRequest request,HttpServletResponse response, JspWriter out) throws Exception {
		Param resultInfo = new Param(true);			//用来封装返回前端json数据
		FaiList<Param> data = new FaiList<Param>();//用来封装用来统计计算结果值
		FaiList<Param> exportData=new FaiList<Param>();		//用于导出报表
		FaiList<Param> orderInfos = new FaiList<Param>();	//订单信息
		FaiList<Integer> sidList=new FaiList<Integer>();	//销售人员的sid
		FaiList<String> saleNames = new FaiList<String>();      //销售的名字列表
		FaiList<Param> receiveInfos=new FaiList<Param>();   //领取记录
		FaiList<Param> dealInfos=new FaiList<Param>(); //成交记录
		
		//获取前端传来的数据  
		Calendar receiveStart = Parser.parseCalendar(request.getParameter("receiveTimeStart")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
		Calendar receiveEnd = Parser.parseCalendar(request.getParameter("receiveTimeEnd")+" 23:59:59", "yyyy-MM-dd HH:mm:ss");
		Calendar payStart = Parser.parseCalendar(request.getParameter("payTimeStart")+" 00:00:00", "yyyy-MM-dd HH:mm:ss");
		Calendar payEnd = Parser.parseCalendar(request.getParameter("payTimeEnd")+" 23:59:59", "yyyy-MM-dd HH:mm:ss");
		boolean exportFlag = Parser.parseBoolean(request.getParameter("exportFlag"), false);		
		int teamType = Parser.parseInt(request.getParameter("type"), 0);
		
		FaiList<Param> saleInfoList=getSalerInfo(teamType);
		for(Param p:saleInfoList){
			sidList.add(p.getInt("sid"));
			saleNames.add(p.getString("acct"));
		}
			
		orderInfos=getOrderInfo(sidList,payStart,payEnd);     //获取订单信息 
			
		receiveInfos= getReceiveInfo(saleNames,receiveStart, receiveEnd);    //获取领取记录信息
			
		dealInfos=getAcctPreSaleInfo(saleNames,receiveStart,receiveEnd);   //获取成交库信息
			
		//获取其配置文件
		Param constant = Web.getConf("constant");
		if(constant==null){
			throw new Exception("constant not founding");
		}
		Param comm = constant.getParam("comm",new Param());
		Param tag = comm.getParam("tag",new Param());
		FaiList<Param> labelList = FaiList.parseParamList(tag.getList("labelList").toJson(), new FaiList<Param>());
		labelList.remove(0);
		//遍历配合每个标签分别进行统计计算
		for (Param p : labelList) {
			Param statisticResult=statistics(orderInfos,receiveInfos,dealInfos,saleInfoList,p.getInt("label"));
			exportData.add(statisticResult);
			if(statisticResult.getInt("receiveCount")>0){
				data.add(statisticResult);
			}
		}

		if (exportFlag == true) {// 导出ex
			response.setContentType("application/x-excel");
			// 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
			// 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
			response.setHeader("Content-Disposition","attachment;filename="+ new String("资源成单分析.xls".getBytes("GBK"),"ISO-8859-1"));
			out.clear();// 必须得加
			ServletOutputStream outputStream = response.getOutputStream();
	
			// 创建导出的表头跟需要导出的数据KEY值
			Param cellKey = new Param();
			cellKey.setString("resourceType", "标签类型");
			cellKey.setString("receiveCount", "领取数量");
			cellKey.setString("orderCount", "成单数量");
			cellKey.setString("inversionRate", "转化率");
			cellKey.setString("payPrice", "付款金额");
			cellKey.setString("payArpu", "付款arpu");
			OssPoi.exportExcel(cellKey, exportData, outputStream);
			return "{\"success\":true}";
		}
		resultInfo.setBoolean("success", true);
		resultInfo.setList("dataList", data);
		return resultInfo.toJson();
	}
	

	/**
	writer: zhs
	description：这个方法用来进行每行数据的统计计算
	parameter：orderInfos订单信息（该订单信息是符合指定时间，指定状态的订单信息）
				receiveInfo订单领取记录
				acctPreSaleInfos订单成交库记录
				saleInfoList销售人员的信息
				tag：标签（其为计算每个标签）
	return：Param itemParam每个标签的计算结果（key-value
											key:receiveCount,orderCount,payPrice...）
	**/
	public Param statistics(FaiList<Param> orderInfos,FaiList<Param> receiveInfo,FaiList<Param> acctPreSaleInfos,FaiList<Param> saleInfoList,int tag)throws Exception {	
		FaiList<Integer> dealAidList= new FaiList<Integer>();// 用于存储成交库中的aid
		HashSet<Integer> aidTemp = new HashSet<Integer>();		// 用于去重操作
		Param itemParam = new Param(true);			//用于存储每列计算结果key-value：key：（receiveCount,orderCount,payPrice...）

		int receiveCount = 0;		// 领取数量
		int orderCount = 0;		// 成单数
		double payPrice = 0;		// 付款金额
		double inversionRate = 0;// 转化率
		double arpu = 0;			// arpu
		
		ParamMatcher paramMatcher=new ParamMatcher();
		paramMatcher.and("tag",ParamMatcher.EQ, tag);
		FaiList<Param> receiveIsTagInfo=Misc.getList(receiveInfo,paramMatcher);
		FaiList<Param> acctPreSaleInfo=Misc.getList(acctPreSaleInfos,paramMatcher);
		for (Param p : acctPreSaleInfo) {
			dealAidList.add(p.getInt("aid",0));
		}
		
		receiveCount = receiveIsTagInfo.size();			//计算出其领取数量
		itemParam.setInt("receiveCount", receiveCount);
		aidTemp.clear();     //去重保证，aidTemp无值
		
		//将订单信息的aidList于成交库aidList进行匹配
		ParamMatcher matcher=new ParamMatcher();
	    matcher.and("aid",ParamMatcher.IN,dealAidList); 
		FaiList<Param> statisticOrderInfo=Misc.getList(orderInfos,matcher);

		for(Param p:statisticOrderInfo){
			int aid=p.getInt("aid");
			int salesSid=p.getInt("salesSid",0);
			payPrice+=p.getDouble(BssStatDef.OrderItemInfo.PRICE);
			if(p.getDouble(BssStatDef.OrderItemInfo.PRICE)>=380&&!aidTemp.contains(aid)){
				aidTemp.add(aid);
				orderCount++;
			}
		} 
		
		if (receiveCount != 0) {
			inversionRate = new Double(orderCount)/receiveCount;
		}
		if (receiveCount != 0) {
			arpu = payPrice / receiveCount;
		}
		
		itemParam.setString("payPrice", payPrice+"");
		itemParam.setInt("orderCount", orderCount);
		itemParam.setString("inversionRate",  (int)(Num.round(inversionRate,4)*10000)/100.0+"%");//采用int类型进行百分比转化，因为采用double进行计算会出现数据格式不一致
		itemParam.setString("payArpu", Num.round(arpu,2)+"");
		switch (tag) {
		case PreSaleHdDef.Tag.NON_CORRECTION_STAFF_MONTH:
			itemParam.setString("resourceType", "未转正员工月度分配");
			break;
		case PreSaleHdDef.Tag.NEW_RESOURCE:
			itemParam.setString("resourceType", "新员工入职分配");
			break;
		case PreSaleHdDef.Tag.NEW_OPEN:
			itemParam.setString("resourceType", "当天开通互动资源");
			break;
		case PreSaleHdDef.Tag.HISTORY_YK:
			itemParam.setString("resourceType", "悦客历史资源");
			break;
		case PreSaleHdDef.Tag.GET_GH:
			itemParam.setString("resourceType", "主动领取公海库资源");
			break;
		case PreSaleHdDef.Tag.QB_RESOURCE:
			itemParam.setString("resourceType", "券宝资源");
			break;
		default:
			itemParam.setString("resourceType", PreSaleHdDef.getTagName(tag));
		}
		return itemParam;
	}
	
	/**
	writer：zhs
	description：该方法是用来获取销售人员信息函数，为了保证其销售人员是在职人员
	parameter：
			teamType获取类型的销售人员
			teamType取值：1为：获取真实销售信息
	 					 2为：获取虚拟销售信息
	return：
			saleInfoList：销售人员的信息
	**/
	public FaiList<Param> getSalerInfo(int teamType) throws Exception{
		FaiList<Param> saleInfoList = new FaiList<Param>();     // 销售人员的信息
		FaiList<Integer> vsidList = new FaiList<Integer>();
		
		SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
		SearchArg searchArg = new SearchArg();
		ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);    //必须加条件，否则matcher没值会报错
		if(teamType==1){
			matcher.and("acct", ParamMatcher.NOT_LK, "hdsale");      //模糊查询，其忽略大小写
		}else if(teamType==2){
			matcher.and("acct", ParamMatcher.LK, "hdsale");     //糊匹配
		}
		searchArg.matcher = matcher;
		int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), searchArg, saleInfoList);
		return saleInfoList;
	}
	
	/**
	writer：zhs
	description：获取订单数据
	parameter：sidList销售人员的sid
		payStart~payEnd为支付时间段
	return :orderInfos 订单信息
	**/
	public FaiList<Param> getOrderInfo(FaiList<Integer> sidList,Calendar payStart,Calendar payEnd) throws Exception{
		FaiList<Param> orderInfos = new FaiList<Param>();	//订单信息
		SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);   
		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher();
		searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS,ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
		searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME,ParamMatcher.LE, payEnd.getTimeInMillis() / 1000);
		searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME,ParamMatcher.GE, payStart.getTimeInMillis() / 1000);
		searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID,ParamMatcher.IN,sidList);
		
		FaiList<Integer> productVersions=PreSaleHdDef.getProductList(PreSaleHdDef.PreSaleProduct.ALL);   //获取全部互动，传单，助手，悦客产品版本
		productVersions.removeAll(PreSaleHdDef.getProductList(PreSaleHdDef.PreSaleProduct.YK_PRODUCT));  //排除悦客产品
		searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID,ParamMatcher.IN,productVersions);
		orderInfos = sysBssStat.getOrderItemList(searchArg);
		return orderInfos;
	}
		
	/**
	writer：zhs
	description： 该方法获取符合要求的领取信息
	parameter:
			 saleNames销售人员的名字，即账号
			 start：开始领取时间
			 end：结束领取时间
	return：receiveList领取记录信息
	**/
	public FaiList<Param> getReceiveInfo( FaiList<String> saleNames,Calendar start, Calendar end) throws Exception {
		FaiList<Integer> attrTypes = new FaiList<Integer>();		// 领取方式：自动，手动，公海
		FaiList<Param> receiveList = new FaiList<Param>();			//领取信息
		
		attrTypes.add(HdSaleRecordDef.AttrType.INIT_CLIENT);
		attrTypes.add(HdSaleRecordDef.AttrType.GET_FILTER);
		attrTypes.add(HdSaleRecordDef.AttrType.GET_FILTER_LOGIN_TIME);
		attrTypes.add(HdSaleRecordDef.AttrType.GET_TASK);
		attrTypes.add(HdSaleRecordDef.AttrType.APPROVE);
		attrTypes.add(HdSaleRecordDef.AttrType.APPROVE_COMM);
		attrTypes.add(HdSaleRecordDef.AttrType.CITY_CLIENT);
		attrTypes.add(HdSaleRecordDef.AttrType.PHONE_ALLOT);
		attrTypes.add(HdSaleRecordDef.AttrType.PHONE_PERSON);
		attrTypes.add(HdSaleRecordDef.AttrType.AUTO_CLIENT);
		attrTypes.add(HdSaleRecordDef.AttrType.NON_NOW_ALLOT);
		attrTypes.add(HdSaleRecordDef.AttrType.RENEW_RESOURCE_ALLOT);
		attrTypes.add(HdSaleRecordDef.AttrType.GET_COMM);
		attrTypes.add(HdSaleRecordDef.AttrType.MANAGER_ALLOT);
		
		Dao ossBssDao = WebOss.getOssBsDao();// 获取ossBss 
		try{
			Dao.SelectArg selectArg = new Dao.SelectArg();
			SearchArg searchArg = new SearchArg();
			searchArg.matcher = new ParamMatcher();
			if (start != null && end != null) {
				searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME,ParamMatcher.LE, end);
				searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME,ParamMatcher.GE, start);
			}
			searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN,saleNames);
			searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN,attrTypes);
			FaiList<Integer> bussines = new FaiList<Integer>();
			bussines.add(HdSaleRecordDef.Business.MP);
			bussines.add(HdSaleRecordDef.Business.HD);
			searchArg.matcher.and(HdSaleRecordDef.Info.BUSINESS, ParamMatcher.IN,bussines);
			selectArg.searchArg = searchArg;
			selectArg.table = "hdSaleRecord";
			selectArg.field = "*";
			receiveList = ossBssDao.select(selectArg);
		}finally{
			ossBssDao.close();
		}
		return receiveList;
	}
	 
	
	/**
	writer ：zhs
	desc：  获取成交库中成交订单数据
	parameter:
				saleNames:销售人员的账号
				receiveStart：开始领取时间
				receiveEnd ：领取结束时间
	return ：  acctPreSaleInfo ：领取记录信息
	**/
	public FaiList<Param> getAcctPreSaleInfo(FaiList<String> saleNames,Calendar receiveStart, Calendar receiveEnd) throws Exception{
		SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口		
	    FaiList<Integer> business=new FaiList<Integer>();
	    business.add(HdSaleRecordDef.Business.MP);
	    business.add(HdSaleRecordDef.Business.HD);
	    SearchArg searchArg=new SearchArg();
	    searchArg.matcher = new ParamMatcher();
	    searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.DEAL); 
	    searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME,ParamMatcher.LE,receiveEnd);
	    searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME,ParamMatcher.GE,receiveStart);
	    searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS,ParamMatcher.IN,business);
	    searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT,ParamMatcher.IN,saleNames);
        FaiList<Param> acctPreSaleInfo = sysPreSaleHd.getList(searchArg); 
		return acctPreSaleInfo;
	}
	
%>
<%

    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        Log.logStd("zhs cmd = %s",cmd);
        if(cmd == null){
            output = "no cmd find";
        }else if(cmd.equals("resourceOrderAnalyze")){
            output = resourceOrderAnalyze(request,response,out);
        }

    }catch (Exception e){
        PreSaleHdDef.printErr(e);
        output = WebOss.checkAjaxException(e);
    }
    out.print(output);


%>