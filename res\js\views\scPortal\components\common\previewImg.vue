<template>
  <div class="preview-img" @click="close">
    
    <img :src="imgUrl" alt="">
  </div>
</template>

<script>
export default {
  name: 'previewImg',
  props: {
    imgUrl: {
      type: String,
      default: '',
    },
  },
  methods: {
    close() {
      this.$emit('close');
    },
  },
}
</script>

<style lang="scss" scoped>
.preview-img {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); 
    width: auto;
    height: 70vh;
  }
  
}
</style>
