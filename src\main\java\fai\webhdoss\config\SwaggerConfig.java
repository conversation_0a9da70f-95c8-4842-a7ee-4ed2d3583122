package fai.webhdoss.config;

import fai.web.Web;
import io.swagger.models.Contact;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseMessageBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ResponseMessage;
import springfox.documentation.service.VendorExtension;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.paths.RelativePathProvider;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.servlet.ServletContext;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description swagger配置
 * @date 2021/03/14 11:40
 */
@Configuration
@EnableWebMvc
@EnableSwagger2
public class SwaggerConfig {
    private final boolean IS_DEBUG = Web.getDebug();

    public SwaggerConfig() {
        System.out.println("SwaggerConfig init debug="+ Web.getDebug());
    }

    final List<ResponseMessage> globalResponses = Arrays.asList(
            new ResponseMessageBuilder()
                    .code(200)
                    .message("成功")
                    .responseModel(new ModelRef("Success"))
                    .build(),
            new ResponseMessageBuilder()
                    .code(201)
                    .message("添加或者修改成功")
                    .build(),
            new ResponseMessageBuilder()
                    .code(400)
                    .message("错误请求")
                    .build(),
            new ResponseMessageBuilder()
                    .code(401)
                    .message("未授权")
                    .build(),
            new ResponseMessageBuilder()
                    .code(403)
                    .message("拒绝请求")
                    .build(),
            new ResponseMessageBuilder()
                    .code(404)
                    .message("未找到相关的请求")
                    .build(),
            new ResponseMessageBuilder()
                    .code(500)
                    .message("服务器内部错误")
                    .build());

    /**
     * Swagger 页面显示提示信息配置
     * 创建API应用
     * apiInfo() 增加API相关信息
     * 通过select()函数返回一个ApiSelectorBuilder实例,用来控制哪些接口暴露给Swagger来展现，
     *  本例采用指定扫描的包路径来定义指定要建立API的目录。
     * @return springfox.documentation.spring.web.plugins.Docket
     * <AUTHOR>
     * @date 2020/9/21
     */
    @Bean
    @Lazy
    public Docket createRestApi(ServletContext servletContext) {

        return new Docket(DocumentationType.SWAGGER_2)
                .pathProvider(new RelativePathProvider(servletContext){
                    @Override
                    public String getApplicationBasePath() {
                        return "/api";
                    }
                })
                .apiInfo(getApiInfo())
                // 是否开启Swagger
                .enable(IS_DEBUG)
                .select()
                //指定swagger扫描的包
                .apis(RequestHandlerSelectors.basePackage("fai.webhdoss.controller"))
                .paths(PathSelectors.any())
                .build()
                .useDefaultResponseMessages(false)
                .globalResponseMessage(RequestMethod.GET,globalResponses)
                .globalResponseMessage(RequestMethod.POST,globalResponses)
                .globalResponseMessage(RequestMethod.PUT,globalResponses)
                .globalResponseMessage(RequestMethod.DELETE,globalResponses);
    }

    /**
     * 增加顶级扩展，ListVendorExtension表示以列表形式
     * @return java.util.List<springfox.documentation.service.VendorExtension>
     * <AUTHOR>
     * @date 2020/9/18
     */
    private List<VendorExtension> getExtension(){

        List<VendorExtension> vendorExtensionsList = new ArrayList<VendorExtension>();

        List<String> stringList = new ArrayList<String>();
        stringList.add("http");
        return vendorExtensionsList;
    }


    /**
     * 创建该API的基本信息（这些基本信息会展现在文档页面中）
     * @return springfox.documentation.service.ApiInfo
     * <AUTHOR>
     * @date 2020/9/21
     */
    private ApiInfo getApiInfo() {
        // 定义联系人信息
        Contact contact = new Contact();
        contact.setName("hd");
        contact.setUrl("http://www.hd.oss.aaa.cn");
        return new ApiInfoBuilder()
                // 标题
                .title("互动oss接口管理")
                // 描述信息
                .description("互动oss API信息")
                //版本
                .version("1.0.0")
                .license("Apache 2.0")
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }

}
