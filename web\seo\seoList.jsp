<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.net.URL"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.weboss.*"%>
<%if(!Auth.checkFaiscoAuth("authSeoManage", false)){out.println("没有权限");return;}%>

<%
String searchUrl = Parser.parseString(request.getParameter("url"), "").replaceAll("\\s*", "");
int pageNo = Parser.parseInt(request.getParameter("pageNo"), 1); 		  						 // 当前页数

int limit = 30;  // 一页多少条数据
int start = (pageNo - 1) * limit; // 开始
int total = 0;	// 总数

if(!Str.isEmpty(searchUrl)) {
	URL oUrl = new URL(Url.fixUrl(searchUrl));
	searchUrl = oUrl.getHost() + oUrl.getPath();
}

SysFaiSeo oSeo = (SysFaiSeo)Core.getSysKit(Kid.SYS_FAI_SEO);
SearchArg searchArg = new SearchArg();
searchArg.matcher = new ParamMatcher();
if(!searchUrl.equals("")){
	searchArg.matcher.and(FaiSeoDef.Info.URL, ParamMatcher.LK, searchUrl);
}else{
	//拿所有
	searchArg.matcher.and(FaiSeoDef.Info.URL, ParamMatcher.NE, "");
}

searchArg.cmpor = new ParamComparator(FaiSeoDef.Info.UPDATETIME, true);
searchArg.totalSize = new fai.comm.util.Ref<Integer>();
searchArg.start = start;
searchArg.limit = limit;

FaiList<Param> list = oSeo.searchTDK(searchArg);
total = searchArg.totalSize.value;

String httpStr = Request.getScheme() + "://";
%>

<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title></title>
<link id="popupWindowCss" type="text/css" href="/css/jquery/base/jquery.ui.all.css" rel="stylesheet" />
<script language="javascript" type="text/javascript" src="/js/jquery/jquery-1.4.2.min.js"></script>
<%@ include file="../agent/link.jsp.inc"%>
<%@ include file="/frontend.jsp.inc"%>
<style type="text/css">
	body{
		font-size: 14px;
	}
	.spiderNum{
		cursor: pointer;
		color: red;
	}
	.main{
		margin: 0 auto;
		width: 100%;
	}
	td{
		max-width: 280px;
	    word-wrap: break-word;
	    word-break: break-all;
	}
	.delete{
		min-width: 50px;
	}
	tr{
		height: 50px;
	}
	.pageTitle{
		font-size: 20px;
	}
	.pageTitle, .search, .result{
		margin-top: 20px;
	}
</style>
</head>
<body>
	<div class="pageTitle">官网TDK设置详情：</div>
	<div class="main">
		<!--查询-->
		<div class="search">
			<form id="form" method="POST" action="seoList.jsp">
				url：<input type="text" value="<%=searchUrl%>" name="url" id="url"/>
				<input type="submit" name="search" id="search" value="查询" />&nbsp;&nbsp;&nbsp;
				<input type="button" class="searchAll" onclick="" value="查看所有" />&nbsp;&nbsp;&nbsp;
				<input type="button" class="addNew" onclick="" value="新增" />
			</form>
		</div>
		<div class="result">
			<table width=100% border=1>
				<tr>
					<td>url</td>
					<td>Title</td>
					<td>Keywords</td>
					<td>Description</td>
					<td>设置时间</td>
					<td>操作</td>
				</tr>
				<%
				for (int i=0;i<list.size();i++) {
					Param data = list.get(i);
					String url = data.getString(FaiSeoDef.Info.URL);
					String title = data.getString(FaiSeoDef.Info.TITLE);
					String keywords = data.getString(FaiSeoDef.Info.KEYWORD);
					String description = data.getString(FaiSeoDef.Info.DESCRIPTION);
					Calendar setTime = data.getCalendar(FaiSeoDef.Info.UPDATETIME, null);
					String setTimeStr = Parser.parseString(setTime);
					String herf = httpStr + url;
				%>
					<tr>
						<td><a href="<%=herf%>" target="_blank"><%=url%></a></td>
						<td><%=title%></td>		
						<td><%=keywords%></td>
						<td><%=description%></td>
						<td><%=setTimeStr%></td>
						<td class="delete">
							<a href="javascript:set('<%=url%>')">修改</a>&nbsp;&nbsp;<a href="javascript:del('<%=url%>')">删除</a>
						</td>
					</tr>
				<%}%>
			</table>
		</div>
		<div style="margin: 10px 0 20px 0;">
			<%
				String seq_url="seoList.jsp?url="+searchUrl;
				int totalPage = (total % limit == 0) ? (total / limit) : ((total / limit) + 1);
				out.println("共" + total + "个&nbsp;&nbsp;共" + totalPage + "页&nbsp;&nbsp;当前第" + pageNo + "页&nbsp;&nbsp;");
				if (pageNo > 1) {
					out.println("<a href='" + seq_url + "&pageNo=" + (pageNo - 1)  +"'>上一页</a>&nbsp;&nbsp;");
				}
				if (pageNo < totalPage) {
					out.println("<a href='" + seq_url + "&pageNo=" + (pageNo + 1) + "'>下一页</a>&nbsp;&nbsp;");
				}
			%>
			第<input id="page" type="text" size="5" maxlength="5" />页<a href="javascript:;" onclick="goPage()">跳转</a>
		</div>
	</div>
</body>
</html>
<script language="javascript" type="text/javascript" src="/js/jquery/form/jquery.form.js"></script>
<script type="text/javascript">
	
$(function(){
	$(".searchAll").click(function(event){
	    document.location.href = "seoList.jsp";
	    event.stopPropagation();
	});
	$(".addNew").click(function(event){
	    document.location.href = "seo.jsp";
	    event.stopPropagation();
	});
});
	
function set(setUrl) {
	document.location.href = "seo.jsp?url=" + setUrl;
}

function del(delUrl) {
	var confirmStr = "删除 " + delUrl + " 设置的TDK, 将会使用开发页面时设置的默认TDK\n\n" + "确认要删除吗?";
	if(!confirm(confirmStr)) {
		return;
	}
	$.ajax({
		type: "post",
		url: 'ajax/seo_h.jsp',
		data: "cmd=delTDK&url=" + delUrl,
		error: function(){alert("服务繁忙，请稍候重试");},
		success: function(result){
			result = jQuery.parseJSON(result);
			alert(result.msg);
			if (result.success) {
				document.location.href = "<%=seq_url%>";
			}
		}
	});
}

// 跳转
function goPage(){
	var page = $("#page").val();
    if (page > <%=totalPage%>){
        return;
    }
    if (page > 0) {
    	var url = "<%=seq_url%>";
		document.location.href = url + "&pageNo=" + page;
    }
}

</script>

