<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){out.println("没有权限");return;}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>配置销售 | 分配资源</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>

	<body id="hdsale-setConf">

		<div style="float:left" class="fai-setConf-personal-list" v-cloak>
			<b style="">分配个人库-互动销售</b>
			<br/><br/>
		    <div>
		    	<span>时间范围：</span>
				<el-date-picker class="fai-date" size="mini" v-model="form.dateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable" :picker-options="pickerOptions"></el-date-picker>
				- <el-date-picker class="fai-date" size="mini" v-model="form.dateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable" :picker-options="pickerOptions"></el-date-picker>
		    </div>
			<span style="color: red;font-size: 12px;">注：2016年10月开始采用手机号注册，因此建议选择2016年10月1日后的日期。</span>
			<el-table :data="tableData" row-key="rowkey" stripe border max-height="700" >
				<el-table-column label="销售" prop="sacctStr"></el-table-column>
				<el-table-column label="个人库数量" prop="person"></el-table-column>
				<el-table-column label="操作" width="80px">
					<template slot-scope="scope">
						<el-button type="primary" size="mini" @click="onAllot(scope.row.sacct)">分配</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<div style="float:left" class="fai-setConf-allotRegular">
			<b style="">资源分配规则配置</b>
			<br/><br/>
			<el-table :data="tableData" row-key="rowkey" stripe border max-height="700" >
				<el-table-column label="销售" prop="name" width="150px"></el-table-column>
				<el-table-column label="助手资源分配数量" prop="mpProductPageCount" width="160px">
					<template slot-scope="scope"  >
						<el-popover trigger="click" placement="top" v-model="scope.row.visible">
							<p>分配数量设置：</p>
							<p ><el-input size="mini" v-model="scope.row.mpProductPageCount" ></el-input>
								<el-button plain size="mini" @click="updateAllotCount(scope)">确认</el-button>
							</p>
							<div slot="reference" class="name-wrapper" @click="scope.row.visible = !scope.row.visible">
								{{ scope.row.mpProductPageCount }}
							</div>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="80px">
					<template slot-scope="scope">
						<el-button type="primary" size="mini" @click="onAllot(scope.row.sid)">分配</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>


	</body>


	<script type="text/javascript">

		/* 分配个人库-互动销售 */
        var faiPersonalDataList = new Vue({
			el: '.fai-setConf-personal-list',
			data: {
				tableData: [],
				inputData:[],
				form: {
					dateBeg:"",
					dateEnd:"",
					count: 2,//默认分配 2
					type: "authHDSale",
				},
				editable: false,
				clearable: false,
				pickerOptions: {
					disabledDate(time) {//如果比16/10早的话，设为16/10（改进: 不允许选择）
						return time.getTime() < new Date("2016-10-01 00:00:00").getTime();
					},
				}
			},
			created:function(){
				var date = new Date();
				date.setMonth(date.getMonth() - 6);
				this.form.dateBeg = Fai.tool.dateFormatter(date);
				date = new Date();
				date.setMonth(date.getMonth() - 1)//previous month
				date.setDate(Fai.tool.getMonthDays(date));
				this.form.dateEnd = Fai.tool.dateFormatter(date);//截止时间设为上一个月的月末日
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				onAllot(salesAcct){
					//alert(salesAcct);
					this.$confirm('请确认信息：\n姓名：'+salesAcct+';\n时间范围：'+this.form.dateBeg+'至'+this.form.dateEnd+';\n数量：'+this.form.count, '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						this.$confirm('请再次确认信息：\n姓名：'+salesAcct+';\n时间范围：'+this.form.dateBeg+'至'+this.form.dateEnd, '提示', {
							confirmButtonText: '确定',
							cancelButtonText: '取消',
							type: 'warning'
						}).then(() => {//再次确定
							Fai.http.post("hdSale_h.jsp?cmd=allotPerson&salesAcct=" + salesAcct, this.form, false).then(result => {
								if(result.success){
									this.$message({showClose: true, type: 'success', message: result.msg});
									getDataList();
								}else{
									//Fai.http.post已经封装了错误提示
								}
							});
						}).catch(() => {
						});
					}).catch(() => {//取消
					});
				}
		    }
		});
        
		// 获取数据
		function getDataList(){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getSetConfList", "", false).then(result => {
				if(result.success){
					//faiConfDataList.tableData[0].salesNameStr = result.salesNameStr;
					//faiConfDataList.sidList = result.sidList;
					//faiConfDataList.salesList = result.salesList;
					faiPersonalDataList.tableData = result.personalList;
				}
			});
		}

		getDataList();//进入页面加载一次数据！


        var allotRegular = new Vue({
            el: '.fai-setConf-allotRegular',
            data: {
                tableData: [],
            },
            created:function () {
                this.getAllotRegular();
            },
            methods: {
                getAllotRegular() {
                    var arg = {
                        "cmd":"getAllotRegular"
                    }
                    Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
                        let result = response.data;
                        console.log(result);
                        if(result.success == 'true'){
                            this.tableData = result.data;
                        }
                        this.tableData = result.data;
                    }, response => {
                        this.$message({
                            type: 'warning',
                            message: '系统错误!'
                        });
                    });
                },
                updateAllotCount(scope) {
                    scope.row.visible = !scope.row.visible;
				}
            }
        })


	</script>

</html>



