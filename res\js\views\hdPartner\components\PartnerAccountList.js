export default {
	template: `
		<div style="margin: 15px 0 0 20px;">
			<el-table :data="partnerAcctList" stripe border max-height="720" style="width: 1200px;">
				<el-table-column v-for="(column,index) in tableColumns" :key="index" :prop="column.prop" :label="column.label"></el-table-column>
			</el-table>
			<el-pagination @size-change="sizeChange" @current-change="currentChanage" layout="total,sizes,prev,pager,next,jumper" :total="totalSize" :page-sizes="[10,50,100,500]" :page-size="limit" :current-page="page" style="margin-top: 30px;"></el-pagination>
		</div>
	`,
	data(){
		return {
			tableColumns: [
				{
					prop: 'type',
					label: '合作伙伴id'
				},
				{
					prop: 'acct',
					label: '账号'
				},
				{
					prop: 'companyName',
					label: '公司名称'
				},
				{
					prop: 'phone',
					label: '手机'
				},
				{
					prop: 'address',
					label: '地址'
				},
				{
					prop: 'email',
					label: '邮箱'
				}
			],
			partnerAcctList: [],
			totalSize: 0,
			limit: 10,
			page: 1
		};
	},
	created(){
		this.getPartnerAcctList();
	},
	methods: {
		sizeChange(limit){
			this.limit = limit;
			this.getPartnerAcctList();
		},
		currentChanage(page){
			this.page = page;
			this.getPartnerAcctList();
		},
		getPartnerAcctList(){
			Vue.http.post(
				'/ajax/hdPartnerAcct_h.jsp?cmd=getPartnerAcctList',
				{limit: this.limit, page: this.page},
				{emulateJSON: true}
			).then(({data}) => {
				this.partnerAcctList = data.list;
				this.totalSize = data.totalSize;
			});
		}
	}
} 