<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.webkchome.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webkchome.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="java.util.regex.*"%>
<%@ page import="fai.hdUtil.upload.HdUpLoadUtil" %>
<%@ page import="fai.hdUtil.exception.SystemException" %>
<%@ page import="com.fasterxml.jackson.databind.ObjectMapper" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="fai.app.dto.HdMBTPPlayerDTO" %>
<%@ page import="org.springframework.core.io.ClassPathResource" %>
<%@ page import="org.springframework.core.io.Resource" %>
<%@ page import="java.io.File" %>
<%@ page import="org.springframework.core.io.FileSystemResource" %>
<%@ page import="org.springframework.web.context.ContextLoader" %>
<%@ page import="fai.hdUtil.exception.HdAssert" %>
<%@ page import="fai.hdUtil.exception.ProfessionException" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="fai.hdUtil.graySchemeUtil.GrayScheme" %>
<%@ page import="fai.hdUtil.graySchemeUtil.GraySchemeEnum" %>
<%@ page import="fai.app.dto.credit.HdCreditsSettingDto" %>
<%@ page import="fai.app.hdcredits.HdCreditsDef" %>
<%@ page import="fai.cli.inf.HdCreditsCli" %>
<%@ page import="fai.comm.rpc.client.FaiClientProxyFactory" %>
<%@ page import="fai.hdUtil.exception.constant.SystemExceptionEnum" %>
<%@ page import="fai.comm.jnetkit.server.fai.RemoteStandResult" %>
<%@ page import="fai.entity.credits.HdCreditsSettingEntity" %>

<%!

public boolean strIsNum(String str) throws Exception{
    boolean flag = str.matches("-?[0-9]+.?[0-9]*");
    return flag;
}

/*
功能：跨aid之间的游戏复制（直销分销之间都可以）

说明：sourceID有两种方式：1）游戏链接中的字符串（可以转成aid和gameId）
                        2)aid+"HD"+gameId(HD不区分大小写)

*/
public String copyActive(HttpServletRequest request, HttpServletResponse response) throws Exception {

    int rt = Errno.ERROR;

    boolean isFkCaseCopy = Boolean.parseBoolean(request.getParameter("isMgCaseCopy"));
    //案例中台复制活动的时候不需要校验
    if (!isFkCaseCopy){
        String requestOrigin = request.getHeader("Origin");
        String agentSiteDomain = Web.getAgentSiteDomain();
        String kcHomeDomain = WebKchome.getAgentKcHomeDomain();
        Log.logStd("requestOrigin = %s", requestOrigin);
        if (requestOrigin != null && !Web.isFaiHost(requestOrigin) && !requestOrigin.endsWith(agentSiteDomain) && !requestOrigin.endsWith(kcHomeDomain)) {
            rt = Errno.ERROR;
            Log.logErr(rt, "args  requestOrigin=%s", requestOrigin);
            return HdGameDef.ErrorInfo.getErrInfo(rt, "非法域名");
        }
    }

    response.setHeader("Access-Control-Allow-Credentials", "true");
    response.setHeader("Access-Control-Allow-Origin","*");
	
    int sourceAid = 0;
    int sourceGameId = 0;
    String targetAcct = request.getParameter("targetAcct");//代理商 targetAcct  
    int targetAid = Parser.parseInt(request.getParameter("targetAid"), 0);
    String sourceID = request.getParameter("sourceID");//sourceID
	boolean fromHdOss = Parser.parseBoolean(request.getParameter("fromHdOss") ,false);

    Log.logStd("test test targetAcct=%s ,targetAid=%s, sourceID=%s", targetAcct, targetAid, sourceID);


    if (targetAcct != null) {
        SysAcct oSysAcct = (SysAcct) Core.getSysKit(Kid.SYS_ACCT);
        Param info = oSysAcct.getAcctInfo(AcctDef.Atype.AGENT_CORP, targetAcct);
        targetAid = info.getInt(AcctDef.Info.AID, 0);
        // targetAid = 9862941;
    }
    if (Str.isEmpty(sourceID)) {
        rt = Errno.ERROR;
        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
    }
    String tempSourceId = sourceID;
    tempSourceId = tempSourceId.toLowerCase();
    String[] split = tempSourceId.split("hd");

    //String[] split = sourceID.split("HD");
    Ref<Integer> aidRef = new Ref<Integer>();
    Ref<Integer> gameIdRef = new Ref<Integer>();

    //对sourceID的判断处理
    if (tempSourceId.indexOf("hd") != -1) {//可能是方式二
        if (split.length == 1) {//断定是方式一,因为HD子窜在最前或最后
            rt = HdTool.decryptGameId(sourceID, aidRef, gameIdRef);
            if (rt != Errno.OK) {
                Log.logErr("dktest02");
                return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
            } else {
                sourceAid = aidRef.value;
                sourceGameId = gameIdRef.value;
            }
        } else {//可能是方式二
            if (strIsNum(split[0]) && strIsNum(split[1])) {//都是数字则肯定是方式二
                try {
                    sourceAid = Integer.parseInt(split[0]);
                    sourceGameId = Integer.parseInt(split[1]);
                } catch (Exception e) {
                    Log.logErr("dktest03");
                    return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
                }
            } else {//肯定是方式一
                rt = HdTool.decryptGameId(sourceID, aidRef, gameIdRef);
                if (rt != Errno.OK) {
                    Log.logErr("dktest04");
                    return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
                } else {
                    sourceAid = aidRef.value;
                    sourceGameId = gameIdRef.value;
                }
            }
        }
    } else {//肯定是方式一
        rt = HdTool.decryptGameId(sourceID, aidRef, gameIdRef);
        if (rt != Errno.OK) {
            Log.logErr("dktest04");
            return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
        } else {
            sourceAid = aidRef.value;
            sourceGameId = gameIdRef.value;
        }
    }


    //复制活动链接开放到外部账号，所以注释掉下面判断
    /*//判断targetAid是否内部账号
    Param tAcctInfo = Acct.getAcctInfo(targetAid);
    int tflag = tAcctInfo.getInt(AcctDef.Info.FLAG);
    if(fromHdOss && !Misc.checkBit(tflag, AcctDef.Flag.Corp.INTERNAL)){
        rt = Errno.ERROR;
        return new HdGameDef.ErrorInfo(rt, "msg", "仅支持内部账号复制活动").add("status", 3).toString();
    }*/


    Log.logDbg("greakjack sourceGameId=%d;sourceAid=%d;targetAid=%d;", sourceGameId, sourceAid, targetAid);
    HdProf hdProf = (HdProf) Core.getCorpKit(1, Kid.HD_PROF);//是否加aid
    Log.logDbg("greakjack hdProf=" + hdProf);
    Log.logDbg("greakjack 1");
    //判断sourceAid是否存在
    try {
        Param targetProf = hdProf.getProf(sourceAid);
    } catch (Exception e) {
        rt = Errno.ARGS_ERROR;
        Log.logErr("dktest05");
        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始企业账号。请与客户再次确认").add("status", 2).toString();
    }
    Log.logDbg("greakjack 2");
    //获取目标aid的prof判断ver等信息
    Param prof = null;
    try {
        prof = hdProf.getProf(targetAid);
    } catch (Exception e) {
        rt = Errno.ARGS_ERROR;
        Log.logErr("dktest06");
        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到目标企业账号。请与客户再次确认").add("status", 3).toString();
    }
    Log.logDbg("greakjack 3");
    HdGame hg = (HdGame) Core.getCorpKit(targetAid, Kid.HD_GAME);//是否加aid 加
    HdOss ho = (HdOss) Core.getCorpKit(targetAid, Kid.HD_OSS);
    SysAcct oSysAcct = (SysAcct) Core.getSysKit(Kid.SYS_ACCT);

    //判断sourceAid直分销
    Param sAidInfo = oSysAcct.getAcctInfo(sourceAid);
    int atype = sAidInfo.getInt(AcctDef.Info.ATYPE);
    boolean isOemSource = (atype == AcctDef.Atype.AGENT_CORP) ? true : false;//true则分销，false直销
    //判断targetAid直分销
    Param tAidInfo = oSysAcct.getAcctInfo(targetAid);
    atype = tAidInfo.getInt(AcctDef.Info.ATYPE);
    boolean isOemTarget = (atype == AcctDef.Atype.AGENT_CORP) ? true : false;//true则分销，false直销

    int profFlag = prof.getInt(HdProfDef.Info.FLAG, 0);
    //获取用户版本信息，如果为铂金版可直接复制所有功能
    int ver = prof.getInt(HdProfDef.Info.VERSION, 0);

    //获取源aid的gameInfo
    Param gameInfo = hg.getGameInfo(sourceAid, sourceGameId,0,true);
    if (gameInfo == null || gameInfo.isEmpty()) {
        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
    }

    long recyTime = 0l;
    Calendar cal = gameInfo.getCalendar(HdGameDef.Info.RECYCLE_TIME, null);

    int souFlagB = gameInfo.getInt(HdGameDef.Info.FLAGB, 0);
    int souFlagC = gameInfo.getInt(HdGameDef.Info.FLAGC, 0);
    Log.logDbg("greakjack b=%s; c=%s", Misc.checkBit(souFlagB, HdGameDef.FlagB.ISRECYCLE), Misc.checkBit(souFlagC, HdGameDef.FlagC.IS_CLEAR_RECYCLE));
    if (Misc.checkBit(souFlagB, HdGameDef.FlagB.ISRECYCLE) || Misc.checkBit(souFlagC, HdGameDef.FlagC.IS_CLEAR_RECYCLE)) {
        Log.logErr("dktest08");
        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，找不到原始游戏ID。请与客户再次确认").add("status", 1).toString();
    }

    int style = gameInfo.getInt(HdGameDef.Info.STYLE, 0);
    int tplId = gameInfo.getInt(HdGameDef.Info.TEMPLATE_ID, 0);
//    if (style == HdGameDef.Style.JLCJ && isOemTarget) {
//        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，锦鲤抽奖活动不可复制到分销账号上").add("status", 4).toString();
//    }
//    if (style == HdGameDef.Style.YQLZS && isOemTarget) {
//        return new HdGameDef.ErrorInfo(rt, "msg", "复制失败，一起来种树活动不可复制到分销账号上").add("status", 4).toString();
//    }
    Log.logDbg("greakjack 4");

    Param setting1 = Param.parseParam(gameInfo.getString(HdGameDef.Info.SETTING, ""));
    int modeId = setting1.getInt(HdGameDef.Setting.MODID, 0);

    Param gameDef = HdGameInit.getGameDef(null, sourceAid, isOemSource, style, tplId, modeId, gameInfo, 0);
    Log.logDbg("greakjack 5");
    if (gameDef == null || gameDef.isEmpty()) {
        Log.logErr(rt, "HdGameInit getGameDef err; aid=%d", sourceAid);
        //return HdGameDef.ErrorInfo.getErrInfo(rt, "活动不存在！").add("status", 5);
        return new HdGameDef.ErrorInfo(rt, "msg", "活动不存在！").add("status", 5).toString();
    }

    Param settingDef = gameDef.getParam("_" + HdGameDef.Info.SETTING);

    if (setting1 != null) {
        int modAid = setting1.getInt(HdGameDef.Setting.MOD_AID, 0);
        int modGameId = setting1.getInt(HdGameDef.Setting.MOD_GAMEID, 0);
        if (modeId != 0 || (modAid != 0 && modGameId != 0)) {
            //int cid = WebHdPortal.getCid()>0?WebHdPortal.getCid():1;
            HdOss oPortal = (HdOss) WebHdOss.getCorpKit(1, Kid.HD_OSS);

            Param modelInfo = null;
            if (modeId > 0) {
                modelInfo = oPortal.getHdModelInfo(modeId);//获取模板信息
            } else {
                SearchArg searchArg = new SearchArg();
                searchArg.matcher = new ParamMatcher(HdModelDef.Info.AID, ParamMatcher.EQ, modAid);
                searchArg.matcher.and(HdModelDef.Info.GAMEID, ParamMatcher.EQ, modGameId);
                FaiList<Param> modelList = oPortal.getHdModelList(searchArg);
                if (modelList.size() > 0) {
                    modelInfo = modelList.get(0);
                }
            }
            if (modelInfo != null) {
                int modeFlag = modelInfo.getInt(HdModelDef.Info.PROPERTY, 0);
                if (modeFlag != 1) {
                    rt = Errno.ERROR;
                    //return HdGameDef.ErrorInfo.getErrInfo(rt, "抱歉！该模板已下架，无法复制！").add("status", 6);
                    return new HdGameDef.ErrorInfo(rt, "msg", "抱歉！该模板已下架，无法复制！").add("status", 6).toString();
                }
            }
        }
    }
    Log.logDbg("greakjack 6");

    //这一段逻辑只针对活动列表的复制功能，只有活动列表复制的时候才会传入identify参数
    if (request.getParameter("identify") != null && "1".equals(request.getParameter("identify"))){
        if(targetAid == 9871760 || targetAid == 24265546 || targetAid == 26942413){
            HdOssAffairCli hdOssAffairCli = new HdOssAffairCli(Core.getFlow());
            hdOssAffairCli.init();

            Param param = new Param();
            param.setInt("oAid",sourceAid);
            param.setInt("oGameId",sourceGameId);
            param.setInt("caseAid",targetAid);
            String paramJson = param.toJson();

            Ref<String> ref = new Ref<String>();
            int rt1 = hdOssAffairCli.getGameLinkandCode(paramJson,ref);
            if (rt1 != Errno.OK || "查询失败".equals(ref.value)){
                return new HdGameDef.ErrorInfo(rt,"msg","查询失败").add("status",9).toString();
            }
            String value = ref.value;
            //数据库没有该案例，返回导入成功
            if (!"".equals(value)){

                return new HdGameDef.ErrorInfo(rt,"msg","案例已复制，请勿重复操作").add("status",9).toString();
            }
        }
    }




    Param copyActive = gameInfo.clone();
    int flagC = copyActive.getInt(HdGameDef.Info.FLAGC, 0);
    //需要重置的数据字段
    FaiList<String> resetFieldList = new FaiList<String>();
    resetFieldList.add(HdGameDef.Info.VIEW);
    resetFieldList.add(HdGameDef.Info.PLAYER);
    resetFieldList.add(HdGameDef.Info.SHARE);
    resetFieldList.add(HdGameDef.Info.PROP_0);
    resetFieldList.add(HdGameDef.Info.PROP_1);
    resetFieldList.add(HdGameDef.Info.AREA_ID);
    resetFieldList.add(HdGameDef.Info.STORE_ID);
    resetFieldList.add(HdGameDef.Info.FLAG + "-" + HdGameDef.Flag.PUBLISH);
    resetFieldList.add(HdGameDef.Info.FLAG + "-" + HdGameDef.Flag.CLOSE);
    resetFieldList.add(HdGameDef.Info.FLAGB + "-" + HdGameDef.FlagB.IS_TIP_ILLEGAL);
    resetFieldList.add(HdGameDef.Info.FLAGB + "-" + HdGameDef.FlagB.TECH_SUPPORT_BUY);
    resetFieldList.add(HdGameDef.Info.FLAGB + "-" + HdGameDef.FlagB.BLACKLIST_SHARE);
    resetFieldList.add(HdGameDef.Info.FLAGB + "-" + HdGameDef.FlagB.WHITELIST_SHARE);
    resetFieldList.add(HdGameDef.Info.FLAGF + "-" + HdGameDef.FlagF.OPEN_WXWORK_ADD);
    resetFieldList.add(HdGameDef.Info.FLAGF + "-" + HdGameDef.FlagF.IS_LIMIT_WXWORK_JOIN);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.COMFORT);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.LINK_INFO_TYPE);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.MAX_AWARD_NUM);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.VOTER_NUM);
    resetFieldList.add(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.CONTACT_INFO);

    resetFieldList.add(HdGameDef.Setting.COMFORT);
    resetFieldList.add(HdGameDef.Info.REMAINMONEY);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.CANALLIST);
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.RULE);//中奖规则
    if (sourceAid != targetAid) {
        resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.HOST_LOGO);
        if (isOemTarget) {
            resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.BRAND_EXPOSURE_TEXT);
        }
        resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.MENU_LINK);
    }

    Log.logDbg("greakjack 7");
    //resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.HOST_NAME);//活动主办方   //活动主办方链接地址???
    resetFieldList.add(HdGameDef.Info.SETTING + "-" + HdGameDef.Setting.SMS_TEMPLATE_LIST);//短信list,是一个list,看下怎么获取

    int tmpVer = ver;
    if (ver == HdProfDef.Version.Corp.BY) { // 兼容版本顺序调换了
        tmpVer = HdGameDef.GameFucVer.BY;
    } else if (ver == HdProfDef.Version.Corp.BJ) {
        tmpVer = HdGameDef.GameFucVer.BJ;
    }

    //根据账号版本比较活动功能列表, 判断是否需要重置该字段数据
    for (Map.Entry<String, Integer> entry : HdGameDef.GameFucVer.fucCache.entrySet()) {
        String key = entry.getKey();
        int value = entry.getValue();
        // 直销所有版本购买永久去广告的不重置页脚广告
        if (Misc.checkBit(profFlag, HdProfDef.Flag.HAS_FOREVER_HIDE_SKILLSUP) && !isOemTarget) {
            if (key.equals(HdGameDef.Info.FLAG + "-" + HdGameDef.Flag.SHOW_SKILL_SUP)) {
                continue;
            }
        }
        if (tmpVer < value) {
            resetFieldList.add(key);
        }
    }
    //复制资源文件的资源文件id
    FaiList<String> imgIdList = new FaiList<String>();
    Log.logDbg("greakjack 777");
    resetParamData(targetAid, copyActive, resetFieldList, isOemTarget, imgIdList, ver);
    Log.logDbg("greakjack 7777");
    Param setting = Param.parseParam(copyActive.getString(HdGameDef.Info.SETTING));
    Param verInfo = HdVerDef.getVerInfo(null, prof, isOemTarget);
    ver = HdVerDef.getRealVerByBuy(ver, false, false, false);
    if (ver < HdVerDef.BJ) { // 白银以下的版本需要重置宣传海报的数据
        Param poster = new Param();
        try {
            poster = settingDef.getParam(HdGameDef.Setting.POSTER);
        } catch (Exception e) {
            poster = Param.parseParam(settingDef.getString(HdGameDef.Setting.POSTER));
        }
        setting.setParam(HdGameDef.Setting.POSTER, poster);
        // 白银以下的版本没有渠道功能
        setting.setString(HdGameDef.Setting.CANALLIST, "");
    } else if (ver == HdVerDef.BJ) {
        FaiList<Param> canallist = FaiList.parseParamList(setting.getString(HdGameDef.Setting.CANALLIST), new FaiList<Param>());
        int canalLimit = HdVerDef.getCanalLimit(ver, false);
        //铂金版复制的游戏渠道
        if (canallist.size() > canalLimit) {
            Iterator<Param> iterator = canallist.iterator();
            int i = 1;
            while (iterator.hasNext()) {
                Param p = iterator.next();
                if (i > canalLimit) {
                    iterator.remove();
                }
                if (!"".equals(p.getString("name", ""))) {
                    i++;
                }
            }
        }
        setting.setString(HdGameDef.Setting.CANALLIST, canallist.toJson());
    }
    setting.setString(HdGameDef.Setting.WXWORKCORPID,"");
    if (style == HdGameDef.Style.DSPKJ && !isOemTarget) {
        // 全民砍价游戏, 免费版帮砍人数不得超过5人
        if (verInfo.getInt(HdVerDef.Info.REALVER, HdVerDef.FREE) == HdVerDef.FREE) {
            int maxHelpNum = 5;
            FaiList<Param> awards = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.AWARDLIST, ""), new FaiList<Param>());
            for (Param award : awards) {
                int needHelpNum = award.getInt(HdGameDef.Award.NEEDHEELPPEOPLE, 0);
                if (needHelpNum > maxHelpNum) {
                    award.setInt(HdGameDef.Award.NEEDHEELPPEOPLE, maxHelpNum);
                }
                award.setInt(HdGameDef.Award.USED_AMOUNT, 0);
                award.setInt(HdGameDef.Award.PT_PRE_SELL_AMOUNT, 0);
            }
            copyActive.setString(HdGameDef.Info.AWARDLIST, awards.toJson());
        }
    }
    if (style == HdGameDef.Style.QMKJ_ZF) {
        Param payData = setting1.getParam(HdGameDef.Setting.PAY_DATA, new Param());
        int pt_h = payData.getInt(HdGameDef.Setting.PayData.PT_H, 0);
        int pt_m = payData.getInt(HdGameDef.Setting.PayData.PT_M, 0);
        if (pt_h <= 0 && pt_m <= 4) {
            payData.setInt(HdGameDef.Setting.PayData.PT_H, 1);
            payData.setInt(HdGameDef.Setting.PayData.PT_M, 0);
        }
        setting1.setParam(HdGameDef.Setting.PAY_DATA, payData);
        copyActive.setString(HdGameDef.Info.SETTING, setting1.toJson());
    }
    if (style == HdGameDef.Style.PTCHL_ZF) {
        FaiList<Param> awards = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.AWARDLIST, ""), new FaiList<Param>());
        for (Param award : awards) {
            award.setInt(HdGameDef.Award.USED_AMOUNT, 0);
            award.setInt(HdGameDef.Award.PT_PRE_SELL_AMOUNT, 0);
            award.setInt(HdGameDef.Award.PT_ALL_ORDERS_AMOUNT, 0);
            award.setInt(HdGameDef.Award.PT_GET_MONEY_BUTNOT_SUCCESS, 0);
        }
        copyActive.setString(HdGameDef.Info.AWARDLIST, awards.toJson());
    }

    if (style == HdGameDef.Style.MBTP_NEW) {
        long createTime = Calendar.getInstance().getTimeInMillis();
        createTime = gameInfo.getCalendar(HdGameDef.Info.CREATE_TIME).getTimeInMillis();
        long theCreateTime = Parser.parseLong(HdGameDef.getTheSpecialTime(style, Web.getDebug()), 0l);
        if (createTime < theCreateTime) {
            Param ndjSetting = Param.parseParam(copyActive.getString(HdGameDef.Info.SETTING, ""));
            FaiList<Param> editPropList = FaiList.parseParamList(ndjSetting.getString(HdGameDef.Setting.PROPLIST, ""));
            FaiList<Param> propList = HdWebTemplateDef.getEditPropList(style);
            FaiList<Param> theAddList = new FaiList<Param>();
            for (Param item : editPropList) {
                String name = item.getString("name");
                if (name.equals("joinBtn")) {
                    FaiList<Object> pathList = item.getList("path");
                    if (pathList != null && !pathList.isEmpty()) {
                        FaiList<String> path0 = (FaiList<String>) pathList.get(0);
                        FaiList<String> path1 = (FaiList<String>) pathList.get(1);
                        if (path0.get(0).indexOf("joinBtn.png") != -1) {
                            path0.set(0, "*_resRoot*/image/mbtp_new/joinBtn_new.png");
                        }
                        if (path1.get(0).indexOf("myBtn.png") != -1) {
                            path1.set(0, "*_resRoot*/image/mbtp_new/myBtn_new.png");
                        }
                    }
                    Param joinPos = new Param();
                    joinPos.setString("left", "12.7rem");
                    joinPos.setString("top", "19.5rem");
                    joinPos.setString("forParent", "false");
                    item.setParam("pos", joinPos);
                }
            }
            ndjSetting.setString(HdGameDef.Setting.PROPLIST, editPropList.toJson());
            copyActive.setString(HdGameDef.Info.SETTING, ndjSetting.toJson());
        }
    }





    if (style == HdGameDef.Style.XYNDJ) {
        long createTime = Calendar.getInstance().getTimeInMillis();
        createTime = gameInfo.getCalendar(HdGameDef.Info.CREATE_TIME).getTimeInMillis();
        long theSpecialTime = Parser.parseLong(HdGameDef.getTheSpecialTime(style, Web.getDebug()), 0l);
        //兼容旧数据
        if (createTime < theSpecialTime) {
            Param ndjSetting = Param.parseParam(copyActive.getString(HdGameDef.Info.SETTING, ""));
            FaiList<Param> editPropList = FaiList.parseParamList(ndjSetting.getString(HdGameDef.Setting.PROPLIST, ""));
            FaiList<Param> propList = HdWebTemplateDef.getEditPropList(style);
            FaiList<Param> theAddList = new FaiList<Param>();
            for (Param item : editPropList) {
                String name = item.getString("name");
                if (name.equals("rightEye") || name.equals("leftEye") || name.equals("exit")) {
                    Param theTemplateParam = new Param();
                    String theNewKey = name + "_new";
                    for (Param theTargetItem : propList) {
                        String targetName = theTargetItem.getString("name");
                        if (targetName != null && !targetName.isEmpty()) {
                            if (targetName.equals(theNewKey)) {
                                Param theCloneParam = theTargetItem.clone();
                                theCloneParam.assign(item, "path");
                                theCloneParam.assign(theTargetItem, "size");
                                theCloneParam.assign(theTargetItem, "pos");
                                theAddList.add(theCloneParam);
                            }
                        }
                    }
                    HdTool.extend(false, editPropList, theAddList);
                }
            }
            ndjSetting.setString(HdGameDef.Setting.PROPLIST, editPropList.toJson());
            copyActive.setString(HdGameDef.Info.SETTING, ndjSetting.toJson());
        }
    }

    FaiList<Param> awardList = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.AWARDLIST, ""));
    boolean isOpenAccessKey = Misc.checkBit(flagC, HdGameDef.FlagC.IS_OPEN_ACCESS_KEY);
    flagC = copyActive.getInt(HdGameDef.Info.FLAGC, 0);
    flagC = Misc.setFlag(flagC, HdGameDef.FlagC.IS_OPEN_ACCESS_KEY, isOpenAccessKey);
    flagC = Misc.setFlag(flagC, HdGameDef.FlagC.USED_FAIOPENID, true);
    flagC = Misc.setFlag(flagC, HdGameDef.FlagC.IS_NO_WXCARD, true);
    flagC = Misc.setFlag(flagC, HdGameDef.FlagC.IS_AUTO_SENT_AWARD, false);//清除PTHD的一键派奖相关标志
    flagC = Misc.setFlag(flagC, HdGameDef.FlagC.IS_AUTO_SENDING_AWARD, false);//清除PTHD的一键派奖相关标志
    setting.setInt(HdGameDef.Setting.PTHD_GROUPNUM, 0);
    Param profOther = Param.parseParam(prof.getString(HdProfDef.Info.OTHER, ""), new Param());
    int defaultMoney = 50000;
    //默认超过红包金额超过多少要开高级防刷
    int limit = profOther.getInt(HdProfDef.Other.LIMIT_MONEY, defaultMoney);

    //最低不能低于50000
    if (limit < defaultMoney) {
        limit = defaultMoney;
    }
    setting.setInt(HdGameDef.Setting.HB_LIMIT, limit);

    FaiList<Param> domainList = null;

    if (isOemTarget) {
        domainList = HdWebApp.getOemHdGameDomainList();
    } else {
        domainList = HdWebApp.getHdGameDomainList();
    }

    int size = domainList.size();
    int domainId = targetAid % size;
    setting.setInt(HdGameDef.Setting.DOMAIN_ID, domainId);
    copyActive.setString(HdGameDef.Info.SETTING, setting.toJson());

    if (hg.isCalLevelNew()) {
        flagC = Misc.setFlag(flagC, HdGameDef.FlagC.USED_CAL_LEVEL_NEW, true);
    }
    if (HdAwardCodeDef.isOpenNewCuscode(targetAid) && System.currentTimeMillis() > HdGameDef.CompatibleTime.OPEN_NEW_CUSCODE && style != HdGameDef.Style.PTCHL) {
        flagC = Misc.setFlag(flagC, HdGameDef.FlagC.OPEN_NEW_CUSCODE, true);
    }

    for (Param award : awardList) {
        int awardType = award.getInt(HdGameDef.Award.AWARDTYPE, 0);
        if (awardType == HdGameDef.awardType.WXCARD) {
            flagC = Misc.setFlag(flagC, HdGameDef.FlagC.IS_NO_WXCARD, false);
        }
    }
    copyActive.setInt(HdGameDef.Info.FLAGC, flagC);
    Log.logDbg("greakjack 8");
    copyActive.setInt(HdGameDef.Info.CREATETYPE, HdGameDef.CreateType.PC_CREATE);


    //积分营销 根据积分设置开关处理默认值
    boolean openCreditJoin4Credits = false;
    boolean openCreditAward4Credits = false;

        HdCreditsCli hdCreditsCli = FaiClientProxyFactory.createProxy(HdCreditsCli.class);
        HdAssert.System.judge(hdCreditsCli == null, SystemExceptionEnum.CLI_INIT_ERROR);
        RemoteStandResult rsr = hdCreditsCli.getCreditSetting(Core.getFlow(), targetAid);
        rt = rsr.getRt();
        HdAssert.System.judgeNoFound(rt, "get credit setting error");
        HdCreditsSettingEntity creditsSetting = rsr.getObject(HdCreditsDef.Protocol.Key.INFO, HdCreditsSettingEntity.class);

        if (!Str.isEmpty(creditsSetting)) {
            int flag = Optional.ofNullable(creditsSetting.getFlag()).orElse(0);
            openCreditJoin4Credits = Misc.checkBit(flag, HdCreditsDef.HdCreditsSettingDef.Flag.OPEN_CREDIT_JOIN);
            openCreditAward4Credits = Misc.checkBit(flag, HdCreditsDef.HdCreditsSettingDef.Flag.OPEN_CREDIT_AWARD);
        }

    int flagF = copyActive.getInt(HdGameDef.Info.FLAGF, 0);
    if (Misc.checkBit(flagF, HdGameDef.FlagF.IS_OPEN_CREDIT_FUNCTION_PUBLISH)) { //重置发布标志
        flagF = Misc.setFlag(flagF, HdGameDef.FlagF.IS_OPEN_CREDIT_FUNCTION_PUBLISH, false);
    }
    if (Misc.checkBit(flagF, HdGameDef.FlagF.IS_CREDIT_JOIN_CREDITS) && !openCreditJoin4Credits) { //重置消耗积分参与
        flagF = HdCreditsDef.restoreCreditsJoin(flagF);
    }
    if (Misc.checkBit(flagF, HdGameDef.FlagF.IS_CREDIT_AWARD_CREDITS) && !openCreditAward4Credits) { //重置积分奖项
        flagF = Misc.setFlag(flagF, HdGameDef.FlagF.IS_CREDIT_AWARD_CREDITS, false);
        FaiList<Param> initAwardList = HdGameInit.getInitAwardList(style,8);
        awardList = HdCreditsDef.restoreCreditsAward(awardList, initAwardList);
        copyActive.setString(HdGameDef.Info.AWARDLIST,awardList.toJson());

        //重置安慰奖
        int flag = copyActive.getInt(HdGameDef.Info.FLAG, 0);
        if (Misc.checkBit(flag,HdGameDef.Flag.OPEN_COMFORT)) {
            Param comfort = Param.parseParam(setting.getString(HdGameDef.Setting.COMFORT), new Param());
            if (comfort.getInt(HdGameDef.Setting.Comfort.COMFORT_AWARDTYPE, 0) == HdGameDef.awardType.CREDITS_AWARD) {
                Param initComfort = HdGameDef.setAwardToComfort(initAwardList.get(initAwardList.size()-1));
                setting.setString(HdGameDef.Setting.COMFORT, initComfort.toJson());
                copyActive.setString(HdGameDef.Info.SETTING, setting.toJson());
            }
        }
    }
    //投票活动 投票人数限制
    if (HdGameUtilDef.isVoteGame(style)) {
        int realVer = verInfo.getInt(HdVerDef.Info.REALVER);
        int voteLimit = HdVerAuth.getMaxLimit(HdVerAuthDef.Id.OPEN_VOTENUM_LIMIT, realVer, targetAid, -1,style);
        if (voteLimit <= 0) {
            //二期账号，配额是-1表示不限，但是编辑页默认是限制并且初始配额是50w，所以这里要改一下数据
            voteLimit = 50000;
        }
        setting.setInt(HdGameDef.Setting.LIMIT_VOTE_NUM, voteLimit);

        //非二期投票人数是不限的，从配置文件拿到的是限制,这里进行修正
        if (HdVerAuth.getVerStage(targetAid, -1) != HdProfDef.STAGE.STAGE_TWO) {
            flagF = Misc.setFlag(flagF, HdGameDef.FlagF.IS_LIMIT_VOTE, false);
        }
    }
    copyActive.setInt(HdGameDef.Info.FLAGF, flagF);



    //针对这两个案例账号的复制活动进行特殊处理
    //案例中台的账号"dev":9897375,"online":28196589
    if(targetAid == 24265546 || targetAid == 24447872 || targetAid == 9858037 || targetAid == 9851878 || targetAid == 9897375 || targetAid == 28196589 || targetAid == 9908663){
        String name = copyActive.getString(HdGameDef.Info.NAME, "");
        name += "【示例】";
        copyActive.setString(HdGameDef.Info.NAME, name);

        if(style != HdGameDef.Style.ZQCJ && style != HdGameDef.Style.ZQCJ_H5){
            Calendar endTime = copyActive.getCalendar(HdGameDef.Info.END_TIME);
            endTime.add(Calendar.YEAR, 5);
            copyActive.setCalendar(HdGameDef.Info.END_TIME, endTime);
        }
        if(style == HdGameDef.Style.MBTP_NEW){
            Param special = setting.getParam(HdGameDef.Setting.SPECIAL);
            if(special != null && !special.isEmpty()){
                Calendar endTime = special.getCalendar(HdGameDef.Setting.MbtpNew.REG_END);
                endTime.add(Calendar.YEAR, 5);
                special.setCalendar(HdGameDef.Setting.MbtpNew.REG_END, endTime);
            }
        }

        if(fromHdOss) {
            int flag = copyActive.getInt(HdGameDef.Info.FLAG,0);
            flag = Misc.setFlag(copyActive.getInt(HdGameDef.Info.FLAG,0), HdGameDef.Flag.LIMIT_REG_NUM, false);
            flag = Misc.setFlag(copyActive.getInt(HdGameDef.Info.FLAG,0), HdGameDef.Flag.LIMIT_JOIN_NUM, false);
            flagC = Misc.setFlag(copyActive.getInt(HdGameDef.Info.FLAGC,0), HdGameDef.FlagC.IS_OPEN_ACCESS_KEY, false);
            setting.setInt(HdGameDef.Setting.ACCESS_RULE, HdGameDef.Setting.ACCESSRULE.CLOSE);
            copyActive.setInt(HdGameDef.Info.FLAG, flag);
            copyActive.setInt(HdGameDef.Info.FLAGC, flagC);


//            if(style == HdGameDef.Style.MBTP_NEW) {
//                // 视频如果是通用码， 则改成本地上传
//                if(copyActive.getInt(HdGameUtilDef.Setting.MbtpNew.VIDEO_FORMAT, 0) == 0) {
//                    setting.setInt(HdGameUtilDef.Setting.MbtpNew.VIDEO_FORMAT, 1);
//                }
//            }
            if(style == HdGameDef.Style.QNTPZS) {
                if(copyActive.getInt(HdGameUtilDef.Setting.QNTPNew.WORKS_TYPE, 0) == HdGameUtilDef.Setting.QNTPNew.WorksType.VIDEO_TYPE && copyActive.getInt(HdGameUtilDef.Setting.QNTPNew.VIDEO_FORMAT, 0) == HdGameUtilDef.Setting.QNTPNew.VideoFormat.VIDEO_URL) {
                    setting.setInt(HdGameUtilDef.Setting.QNTPNew.VIDEO_FORMAT, HdGameUtilDef.Setting.QNTPNew.VideoFormat.LOCAL_UPLOAD);
                }
            }
        }

        String hostName = setting.getString(HdGameDef.Setting.HOST_NAME, "");
        hostName += "【示例】";
        setting.setString(HdGameDef.Setting.HOST_NAME, hostName);

        Param comfort = Param.parseParam(setting.getString(HdGameDef.Setting.COMFORT), new Param());
        if(!Str.isEmpty(comfort)){
            String comfortName = comfort.getString(HdGameDef.Setting.Comfort.COMFORT_AWARD);
            comfortName += "【示例】";
            comfort.setString(HdGameDef.Setting.Comfort.COMFORT_AWARD, comfortName);
            comfort.setInt(HdGameDef.Setting.Comfort.COMFORT_NUM, 1000);

            long comcet = comfort.getLong(HdGameDef.Setting.Comfort.COMFORT_CODE_END_TIME);
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(comcet);
            calendar.add(Calendar.YEAR, 10);
            comfort.setLong(HdGameDef.Setting.Comfort.COMFORT_CODE_END_TIME, calendar.getTimeInMillis());

            setting.setString(HdGameDef.Setting.COMFORT, comfort.toJson());
        }

        if(style == HdGameDef.Style.JSMS_ZF || style == HdGameDef.Style.JSMS_ZF_DSP){
            FaiList<Param> msScence = setting.getList(HdGameDef.Setting.MS_SCENES, new FaiList<Param>());
            for(Param item : msScence){
                long sceneEnd = item.getLong(HdGameDef.Setting.MsScene.SCENE_END);
                Calendar calendar = Calendar.getInstance();
                calendar.setTimeInMillis(sceneEnd);
                calendar.add(Calendar.YEAR, 10);
                item.setLong(HdGameDef.Setting.MsScene.SCENE_END, calendar.getTimeInMillis());
            }
            setting.setList(HdGameDef.Setting.MS_SCENES, msScence);
        }
        if(style == HdGameDef.Style.FXB){
            setting.setInt(HdGameSettingDef.FXBInfo.COMMISSION_PER_ORDER, 30);
        }
        if(style == HdGameDef.Style.JCGF){
            setting.setInt(HdGameDef.Setting.AWARD_NUM, 1);
            setting.setInt(HdGameDef.Setting.JCGF.TOTAL_MONEY, 100);
        }

        if (style == HdGameDef.Style.XCYYY) {
            //现场摇一摇发布和重置，清楚轮次状态
            copyActive.setString(HdGameDef.Info.PROP_1,"{\"currentRound\":1}");
        }

        copyActive.setString(HdGameDef.Info.SETTING, setting.toJson());

        awardList = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.AWARDLIST));
        for(Param award : awardList) {
            String awardName = award.getString(HdGameDef.Award.NAME, "");
            awardName += "【示例】";
            award.setString(HdGameDef.Award.NAME, awardName);

            Calendar cet = award.getCalendar(HdGameDef.Award.CODE_END_TIME);
            cet.add(Calendar.YEAR, 10);
            award.setCalendar(HdGameDef.Award.CODE_END_TIME, cet);

            int mainStype = award.getInt(HdGameDef.Award.MAIN_STYPE, 0);
            int awardType = award.getInt(HdGameDef.Award.AWARDTYPE);
            if(awardType == HdGameDef.awardType.PASSWORDRED){
                int amount = award.getInt(HdGameDef.Award.AMOUNT, 0);
                if(amount > 100){
                    award.setInt(HdGameDef.Award.AMOUNT, 100);
                }
                award.setInt(HdGameDef.Award.HB_TYPE, HdGameDef.Award.HbType.FIXED);
                award.setInt(HdGameDef.Award.PASSWORDREDNUM, 30);
                if(style == HdGameDef.Style.JCGF){
                    award.setInt(HdGameDef.Award.PASSWORDREDNUM, 100);
                }
            }else if(mainStype == HdGameDef.Award.MainStyle.SYSTEM_GIFT){
                award.setInt(HdGameDef.Award.MAIN_STYPE,HdGameDef.Award.MainStyle.CUSTOM_TYPE);
                award.setInt(HdGameDef.Award.AWARDTYPE,HdGameDef.awardType.REALGIFT);
                award.setInt(HdGameDef.Award.AMOUNT, 1000);
            }else if(awardType == HdGameDef.awardType.MALL_CREDIT){
                award.setInt(HdGameDef.Award.BIND_WID, 0);
                award.setInt(HdGameDef.Award.AMOUNT, 1000);
            }else if(awardType == HdGameDef.awardType.MALL_COUPON){
                award.setString(HdGameDef.Award.INTERNAL_COUPON_ID, "101");
                award.setInt(HdGameDef.Award.BIND_WID, 0);
                award.setInt(HdGameDef.Award.AMOUNT, 1000);
            }else if(awardType == HdGameDef.awardType.FISSILE_COUPON){
                Param fissileCouponData = award.getParam(HdGameDef.Award.FISSILE_COUPON_DATA);
                name = fissileCouponData.getString(HdGameDef.Award.FissileCouponData.NAME, "");
                name += "【示例】";
                fissileCouponData.setString(HdGameDef.Award.FissileCouponData.NAME, name);
                award.setInt(HdGameDef.Award.AMOUNT, 1000);
            }else{
                if(style == HdGameDef.Style.FXB){
                    if(award.getInt(HdGameDef.Award.AMOUNT) > 100){
                        award.setInt(HdGameDef.Award.AMOUNT, 100);
                    }
                }else{
                    award.setInt(HdGameDef.Award.AMOUNT, 1000);
                }
            }
            int level = award.getInt(HdGameDef.Award.LEVEL);
            if(style == HdGameDef.Style.GFHB && level == 1){
                award.setInt(HdGameDef.Award.PASSWORDREDNUM, 180);
            }
        }
        copyActive.setString(HdGameDef.Info.AWARDLIST, awardList.toJson());

        if (HdGameDef.hasGiftList(style)) {
            FaiList<Param> giftList = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.GIFT_LIST));
            for(Param gift : giftList){
                String awardName = gift.getString(HdGameDef.Award.NAME, "");
                awardName += "【示例】";
                gift.setString(HdGameDef.Award.NAME, awardName);
                gift.setInt(HdGameDef.Award.AMOUNT, 1000);
            }
            copyActive.setString(HdGameDef.Info.GIFT_LIST, giftList.toJson());
        }
    }
    if (isFkCaseCopy) {

        //int sourceAid = Parser.parseInt(request.getParameter("sourceID"), 0);
        AcctCli acctCli = new AcctCli(Core.getFlow());
        if (!acctCli.init()) {
            Log.logErr(" hdProfCli init err sourceAid=%s;", targetAid);
        }
        Param acctInfo = new Param();
        rt = acctCli.getAcctInfo(targetAid, acctInfo);
        if (rt != Errno.OK) {
            Log.logErr(rt, "getAcctInfo error;aid=%d", targetAid);
        }
        int actFlag = acctInfo.getInt(AcctDef.Info.FLAG, 0);
        boolean isInner = Misc.checkBit(actFlag, AcctDef.Flag.Corp.INTERNAL);//是否内部账号
        Log.logStd("是否内部账号 targetAid=%s sourceAid=%s isInner=%s actFlag=%s", targetAid, sourceAid, isInner, actFlag);
        if (isInner) {
            Calendar endTimeCalendar = Calendar.getInstance();
            endTimeCalendar.setTimeInMillis(1980172800000L);//2032-10-01
            copyActive.setCalendar(HdGameDef.Info.END_TIME, endTimeCalendar);
        }
    }

    Ref<Integer> idRef = new Ref<Integer>();
    rt = hg.addHdGame(copyActive, idRef, new FaiList<Param>(), targetAid, false);//权限问题,ok
    Log.logDbg("greakjack 9");
    if (rt != Errno.OK || idRef.value == null) {
        Log.logDbg("greakjack rt=" + rt);
        return new HdGameDef.ErrorInfo(rt, "msg", "系统错误").add("status", 7).toString();

    } else {
        //员工操作日志，复制游戏部分
        HdOperateLog operate = (HdOperateLog) Core.getCorpKit(targetAid, Kid.HD_OPERATE_LOG);
        Param detail = new Param();
        String name = copyActive.getString(HdGameDef.Info.NAME, "");
        int storeId = copyActive.getInt(HdGameDef.Info.STORE_ID, 0);
        int areaId = copyActive.getInt(HdGameDef.Info.AREA_ID, 0);
        detail.setInt(HdOperateLogDef.Detail.GAMEID, idRef.value);
        detail.setInt("oldGameId", sourceGameId);
        detail.setString(HdOperateLogDef.Detail.GAMETITLE, name);
        int logrt = operate.addHdOptlog(HdOperateLogDef.Type.COPY_GAME, detail.toJson(), targetAid, storeId, areaId, HdOperateLogDef.Terminals.WEB, 1, "boss");
        if (logrt != Errno.OK) {
            App.logErr("copyGame logging... err aid=%d, gameid=%d", targetAid, idRef.value);
        }
    }

    int id = idRef.value;
    if (isOpenAccessKey) {
        // 复制通用秘钥
        HdAccessKey hk = (HdAccessKey) Core.getCorpKit(1, Kid.HD_ACCESSKEY);
        Param usualKeyInfo = new Param();
        hk.getUsualKey(sourceAid, sourceGameId, usualKeyInfo);//获取旧游戏的通用码
        if (!usualKeyInfo.getString(HdAccessKeyDef.Info.CUSKEY, "").equals("")) {
            hk.addUsualAccessKey(targetAid, id, usualKeyInfo.getString(HdAccessKeyDef.Info.CUSKEY, ""));//设置新游戏的通用码
        }
    }
    Log.logDbg("greakjack 1");
    HdGameCli cli = new HdGameCli(Core.getFlow());
    if (!cli.init()) {
        throw new WebException("HdGameCli init ersetRegPlayerAwardror");
    }

    //复制api任务信息
    if(Misc.checkBit(flagF, HdGameDef.FlagF.OPEN_API_TSAK)) {
        Ref<Param> apiTaskRef = new Ref<Param>();
        rt = cli.getApiTasks(sourceAid, sourceGameId, apiTaskRef);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "copyGame getApiTasks err");
        }
        Param apiTaskInfo = apiTaskRef.value;
        if (!Str.isEmpty(apiTaskInfo)) {
            String verToken = UUID.randomUUID().toString().replaceAll("-", "");
            Param info = new Param();
            info.setInt(HdApiTasksDef.Info.AID, Core.getAid());
            info.setInt(HdApiTasksDef.Info.GAMEID, id);
            info.setInt(HdApiTasksDef.Info.STATUS, HdApiTasksDef.STATUS.COMMIT);
            info.setString(HdApiTasksDef.Info.VER, verToken);
            info.setString(HdApiTasksDef.Info.TASK_LIST, apiTaskInfo.getString(HdApiTasksDef.Info.TASK_LIST, ""));
            rt = cli.repalceApiTasks(targetAid, id, info);
            if (rt != Errno.OK) {
                Log.logStd(rt, "copyGame repalceApiTasks err");
            }
        }
    }

    //复制资源文件
    Param newActive = hg.getGameInfo(targetAid, id);
    if (newActive == null || newActive.isEmpty()) {
        return new HdGameDef.ErrorInfo(rt, "msg", "系统错误").add("status", 7).toString();
    }
    setting = Param.parseParam(newActive.getString(HdGameDef.Info.SETTING, ""));




    //设置默认红包防刷
    setting.setInt(HdGameDef.Setting.HB_PREVENT_COUTING, HdGameDef.Setting.PreventCount.IMAGE);
    if (HdGameDef.hasGiftList(style)) {
        FaiList<Param> giftList = FaiList.parseParamList(copyActive.getString(HdGameDef.Info.GIFT_LIST, ""));
        rt = hg.copyActiveImgPathWithGiftList(targetAid, id, sourceAid, sourceGameId, awardList, giftList, setting, imgIdList);
    } else {
        rt = hg.copyActiveImgPath(targetAid, id, sourceAid, sourceGameId, awardList, setting, imgIdList, true);//可能有问题,OK
    }

    //文状元题库复制
    if (HdGameDef.Style.CGDT == style || style == HdGameDef.Style.WZYTZS || style == HdGameDef.Style.KTDTYDJ
            || style == HdGameDef.Style.AYNLSSGJ || style == HdGameDef.Style.AYNLSSGJ_NEW || style == HdGameDef.Style.FKCT
            || style == HdGameDef.Style.NLDTZ || style == HdGameDef.Style.JCGF || HdGameDef.isPaymentGame(style)) {
        String question = hg.getQuestions(sourceAid, sourceGameId);
        if (!Str.isEmpty(question)) {
            Param info = new Param();
            if((style == HdGameDef.Style.AYNLSSGJ_NEW || style == HdGameDef.Style.JCGF) && fromHdOss) {
                // 字段相同就不重复写了
                question = createQuestion(targetAid, idRef.value, question);
                Log.logDbg("jonm question=%s",question);
            }
            info.setString(HdQuestionsDef.Info.QUESTION, question);
            rt = cli.addQuestion(targetAid, id, info);
            if (rt != Errno.OK) {
                //添加题库失败直接删游戏，返回错误信息
                hg.delGame(targetAid, id, false);//权限问题***** ok
                Log.logErr(rt, "hdgame addQuestions err;");
                return new HdGameDef.ErrorInfo(rt, "msg", "复制游戏失败").add("status", 8).toString();
            }
        }
    }

    if (rt != Errno.OK) {
        hg.delGame(targetAid, id, false);
        return new HdGameDef.ErrorInfo(rt, "msg", "复制游戏失败").add("status", 8).toString();
    }
    Log.logDbg("greakjack 10");

    if((style == HdGameDef.Style.MBTP_NEW || style == HdGameDef.Style.QNTPZS ) && fromHdOss) {
        // 1. 获取该活动下的全部玩家旧数据
        SearchArg searchArgPlayer = new SearchArg();
        searchArgPlayer.matcher = new ParamMatcher(HdPlayerDef.Info.AID, ParamMatcher.EQ, sourceAid);
        searchArgPlayer.matcher.and(HdPlayerDef.Info.GAMEID, ParamMatcher.EQ, sourceGameId);
        Param args = new Param();
        args.setInt(HdPlayerDef.GetListEvn.webEvn, HdPlayerDef.GetListEvn.hdportal);
        args.setString("fields", HdPlayerDef.Info.ID +","+ HdPlayerDef.Info.NAME +","+ HdPlayerDef.Info.INFO +","+ HdPlayerDef.Info.OPENID  +","+ HdPlayerDef.Info.CANAL_ID + ","+ HdPlayerDef.Info.ACHIEVE+ "," + HdPlayerDef.Info.CURRENT_ACHIEVE +","+ HdPlayerDef.Info.PROP);
        FaiList<Param> oldPlayerList = new FaiList<Param>();
        rt = cli.getPlayerListFromDB(sourceAid, sourceGameId, searchArgPlayer, oldPlayerList, args);
        if(rt != Errno.OK){
            Log.logErr(rt, "getPlayerListFromDB err; oldPlayerList=%s", oldPlayerList.size());
        }

        if(style == HdGameDef.Style.QNTPZS) {
            rt = addWorks(targetAid, idRef.value, oldPlayerList, newActive);
            if(rt != Errno.OK) {
                Log.logErr(rt, "qntp addWorks err;");
                return new HdGameDef.ErrorInfo(rt, "msg", "qntp addWorks err").toString();
            }
        } else if(style == HdGameDef.Style.MBTP_NEW) {
            FaiList<HdMBTPPlayerDTO> playerList = createMBTPPlayerDate(targetAid, idRef.value, oldPlayerList, gameInfo);
            rt = cli.addPlayerList(targetAid, idRef.value, playerList);
            if(rt != Errno.OK){
                Log.logErr(rt, "mbtp addPlayerList err; playerList=%s", playerList.toString());
                return new HdGameDef.ErrorInfo(rt, "msg", "mbtp addPlayerList err").toString();
            }
        }
    }



    //案例中台的账号"dev":9897375,"online":28196589
    if(targetAid == 24265546 || targetAid == 24447872 || targetAid == 9858037 || targetAid == 9851878
            || targetAid == 9871760 || targetAid == 26942413|| targetAid == 9897375 || targetAid == 28196589 || targetAid == 9908663){
        Param staffInfo = Acct.getStaffInfo(1, Session.getSid());
        String name = staffInfo.getString(StaffDef.Info.NAME, "");

        Param record = new Param();
        record.setInt(HdCopyRecordDef.Info.SOURCE_AID, sourceAid);
        record.setInt(HdCopyRecordDef.Info.TARGET_AID, targetAid);
        record.setInt(HdCopyRecordDef.Info.SOURCE_GAMEID, sourceGameId);
        record.setInt(HdCopyRecordDef.Info.TARGET_GAMEID, id);
        record.setString(HdCopyRecordDef.Info.OPERATOR, name);
        record.setCalendar(HdCopyRecordDef.Info.CREATE_TIME, Calendar.getInstance());
        rt = ho.addCopyRecord(record);
        if(rt != Errno.OK){
            Log.logErr(rt, "addCopyRecord err; record=%s", record);
        }

        Param update = new Param();
        update.setString(HdGameDef.Info.AWARDLIST, awardList.toJson());
        ParamUpdater updater = new ParamUpdater(update);
        int flagOr = 0;
        flagOr |= HdGameDef.Flag.PUBLISH;
        updater.add(HdGameDef.Info.FLAG, ParamUpdater.LOR, flagOr);

        rt = hg.setGamePub(id, updater, true);
        if(rt != Errno.OK){
            return new HdGameDef.ErrorInfo(rt,"msg","复制成功").add("msg", "发布失败").add("status",9).toString();
        }

        String httpStr = Web.getDebug() ? "http://" : "https://";
        String portalHost = isOemTarget ? HdWebApp.getOemHdPortalDomain() : HdWebApp.getHdPortalDomain();
        String gameHost = isOemTarget ? HdWebApp.getOemHdGameDomain() : HdWebApp.getHdGameDomain();
        String token = HdTool.encryptGameId(targetAid, id);
        String gameLink = httpStr + gameHost + "/"+ targetAid+ "/"+ token +  "/load.html?style=" + style;
        String gameCode = "//" + portalHost + "/qrCode.jsp?cmd=qrurl&siteUrl=" + Encoder.encodeUrl(gameLink);

        int tarGameId = record.getInt(HdCopyRecordDef.Info.TARGET_GAMEID);



        return new HdGameDef.ErrorInfo(rt,"msg","复制成功").add("msg", "发布成功")
                .add("targetGameId",tarGameId)
                .add("gameLink", gameLink).add("gameCode", gameCode).add("status",9).toString();
    }


    return new HdGameDef.ErrorInfo(rt,"msg","复制成功").add("status",9).toString();
}

    private String createQuestion(int targetAid, int targetGameId, String sourceQuestion) throws Exception {
        FaiList<Param> zcList = FaiList.parseParamList(sourceQuestion);
        HdUpLoadUtil upLoadUtil = new HdUpLoadUtil(targetAid, Core.getFlow(), targetGameId);
        int i = 0;
        try {
            for(Param zc: zcList) {
                FileSystemResource imgResource = new FileSystemResource(ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".png"));
                Param imgFileInfo = upLoadUtil.uploadByFile(imgResource.getFile());
                String ImgPath = imgFileInfo.getString("path", "");
                Log.logStd("jonm ImgPath=%s",ImgPath);
                FaiList<Param> details = zc.getList(HdGameDef.Setting.JCGF.ZcList_New.DetailItems);
                for(Param detail: details) {
                    detail.setString(HdGameDef.Setting.JCGF.ZcList_New.DetailItem.SRC,ImgPath);
                }
                zc.setList(HdGameDef.Setting.JCGF.ZcList_New.DetailItems, details);
                zc.setString(HdGameDef.Setting.JCGF.ZcList_New.COVERSRC, ImgPath);
            }
        } catch (Exception e){
            Log.logErr("JCGF uploadByPath err");
        }
        return zcList.toJson();
    }

private FaiList<HdMBTPPlayerDTO> createMBTPPlayerDate(int targetAid, int targetGameId, FaiList<Param> sourcePlayerList, Param sourceGameInfo) throws Exception {
    Param setting = Param.parseParam(sourceGameInfo.getString(HdGameDef.Info.SETTING, ""));
    int MBtype = setting.getInt(HdGameUtilDef.Setting.MbtpNew.WORKS_TYPE, 0);
    // 图片/视频url上传
    int i=0,n=0;
    HdUpLoadUtil upLoadUtil = new HdUpLoadUtil(targetAid, Core.getFlow(), targetGameId);
    FaiList<HdMBTPPlayerDTO> hdMBTPPlayerDTOS = new FaiList<HdMBTPPlayerDTO>();
    for(Param player: sourcePlayerList) {
        Log.logStd("jonm player=%s",player);
        HdMBTPPlayerDTO hdMBTPPlayerDTO = new HdMBTPPlayerDTO();
        hdMBTPPlayerDTO.setProp1("");
        hdMBTPPlayerDTO.setAid(targetAid);
        hdMBTPPlayerDTO.setGameId(targetGameId);
        hdMBTPPlayerDTO.setName(player.getString(HdPlayerDef.Info.NAME));
        hdMBTPPlayerDTO.setProp("投票素材"+(++n));
        hdMBTPPlayerDTO.setAchievement(player.getString(HdPlayerDef.Info.ACHIEVE));
        hdMBTPPlayerDTO.setCurrentAchievement(player.getString(HdPlayerDef.Info.CURRENT_ACHIEVE));

        Param info = Param.parseParam(player.getString(HdPlayerDef.Info.INFO),new Param());
        FaiList<String> cutpics = info.getList(HdPlayerDef.WxInfo.CUTPICS);
        FaiList<String> newCutpics = new FaiList<String>();
//        for(String url: cutpics) {
//            Param fileInfo = upLoadUtil.uploadByPath(url);
//            String path = fileInfo.getString("path","");
//            Log.logDbg("jonm path=%s", path);
//            if(Str.isEmpty(path)) {
//                Log.logErr("file upload err, path is null");
//            }
//            newCutpics.add(path);
//        }
        // 上传到文件服务器
        for(int j = 0; j < cutpics.size(); ++j) {
            Log.logStd("jonm path1=%s", ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".png"));
            FileSystemResource resource = null;
            if(MBtype != HdPlayerDef.WxInfo.MbType.IMAGE && j==1) {
                resource = new FileSystemResource(ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".mp4"));
            } else {
                resource = new FileSystemResource(ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".png"));
            }
            Log.logStd("jonm length=%s", resource.getFile().length());
            Param fileInfo = new Param();
            try {
                fileInfo = upLoadUtil.uploadByFile(resource.getFile());
            } catch (Exception e){
                Log.logErr("uploadByPath err");
                return new FaiList<HdMBTPPlayerDTO>();
            }

            String path = fileInfo.getString("path","");
            Log.logStd("jonm path=%s", path);
            if(Str.isEmpty(path)) {
                Log.logErr("file upload err, path is null");
            }
            newCutpics.add(path);
        }
        info.setList(HdPlayerDef.WxInfo.CUTPICS, newCutpics);
        // dep不支持显示http
        String headImg = newCutpics.get(0);
        if(Web.isPre()) {
            headImg = newCutpics.get(0).replace(".faidev.cc","");
        }
        info.setString(HdPlayerDef.WxInfo.HEAD_IMG, headImg);
        if(MBtype != HdPlayerDef.WxInfo.MbType.IMAGE) {
            info.setInt(HdPlayerDef.WxInfo.MBTYPE, HdPlayerDef.WxInfo.MbType.VIDEO);
        }
//        if(newCutpics != null && !newCutpics.isEmpty()){
//            int aid = player.getInt(HdPlayerDef.Info.AID,0);
//            FaiList<String> thumbs = new FaiList<String>();
//            thumbs.add(HdGameDef.setImgUrl(aid,newCutpics.get(0),"10rem","10rem"));
//            info.setList(HdPlayerDef.WxInfo.THUMBNAILS,thumbs);
//        }

        JSONObject jsonObject2 = JSONObject.parseObject(info.toJson());
        Log.logDbg("jonm jsonObject2=%s",jsonObject2.toString());
        hdMBTPPlayerDTO.setInfo(jsonObject2.toString());
        hdMBTPPlayerDTOS.add(hdMBTPPlayerDTO);
    }

    return hdMBTPPlayerDTOS;
}

    private int addWorks(int targetAid, int targetGameId, FaiList<Param> sourcePlayerList, Param targetGameInfo) throws Exception {
        //新增的虚拟openId
        int rt = Errno.OK;
        Param setting = Param.parseParam(targetGameInfo.getString(HdGameDef.Info.SETTING, ""));
        int urlType = setting.getInt(HdGameUtilDef.Setting.QNTPNew.WORKS_TYPE, -1);
        HdVoteCli hdVoteCli = new HdVoteCli(Core.getFlow());
        if (!hdVoteCli.init()) {
            throw new WebException("hdVoteCli init err");
        }
        int i = 0;
        int n = 1;
        HdUpLoadUtil upLoadUtil = new HdUpLoadUtil(targetAid, Core.getFlow(), targetGameId);
        for(Param player: sourcePlayerList) {
            Log.logStd("jonm player=%s",player);
            String openId = "hdplayer_qn_" + System.currentTimeMillis();
            // 添加玩家
            Param playerInfo = new Param();
            //组装player信息
            playerInfo = new Param();
            playerInfo.setInt(HdPlayerDef.Info.AID, targetAid);
            playerInfo.setInt(HdPlayerDef.Info.GAMEID, targetGameId);
            playerInfo.setString(HdPlayerDef.Info.NAME, player.getString(HdPlayerDef.Info.NAME,""));
            playerInfo.setString(HdPlayerDef.Info.PROP, player.getString(HdPlayerDef.Info.PROP,""));
            playerInfo.setString(HdPlayerDef.Info.INFO, player.getString(HdPlayerDef.Info.INFO,""));
            playerInfo.setString(HdPlayerDef.Info.OPENID, openId);
            int playerFlag = Misc.setFlag(0, HdPlayerDef.Flag.PLAYER_REG, true);
            playerInfo.setInt(HdPlayerDef.Info.FLAG, playerFlag);
            playerInfo.setInt(HdPlayerDef.Info.CANAL_ID, -1);
            Ref<Param> resultRef = new Ref<Param>();
            HdPlayer hdPlayer = (HdPlayer) Core.getCorpKit(targetAid, Kid.HD_PLAYER);
            Log.logDbg("gameid =%d; targetGameId=%d", targetGameInfo.getInt(HdGameDef.Info.ID), targetGameId);
            rt = hdPlayer.addPlayerNewOnly(targetAid, targetGameInfo, playerInfo, resultRef);
            if (rt != Errno.OK) {
                Log.logErr(rt, "add player err aid = %s;gameId = %s; player = %s", targetAid, targetGameId, playerInfo);
                throw new ProfessionException(rt, "参与失败", Core.getFlow());
            }
            int playerId = resultRef.value.getInt("playerId");

            Param imgFileInfo = new Param();
            Param videoFileInfo = new Param();
            String ImgPath = "";
            String videoPath = "";
            try {
                FileSystemResource imgResource = new FileSystemResource(ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".png"));
                imgFileInfo = upLoadUtil.uploadByFile(imgResource.getFile());
                ImgPath = imgFileInfo.getString("path","");
                Log.logStd("workTypeList=%s",setting.getList(HdGameDef.Setting.QNTPZS.WORKS_TYPE_LIST, new FaiList<Param>()));
                if(HdGameDef.Setting.QNTPZS.WorksType.isSelected(setting.getList(HdGameDef.Setting.QNTPZS.WORKS_TYPE_LIST, new FaiList<Param>()),HdGameDef.Setting.QNTPZS.WorksType.WORKS_TYPE_VIDEO)) {
                    FileSystemResource videoResource = new FileSystemResource(ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("upload/"+((i++%5)+1)+".mp4"));
                    videoFileInfo = upLoadUtil.uploadByFile(videoResource.getFile());
                    videoPath = videoFileInfo.getString("path","");
                }
            } catch (Exception e){
                Log.logErr("uploadByPath err; e=%s",e.toString());
                return Errno.ERROR;
            }
            int flag = 0;
            flag = Misc.setFlag(flag, HdWorksDef.Flag.CUSCREATE, true);
            flag = Misc.setFlag(flag, HdWorksDef.Flag.HASCHECK, true);
            flag = Misc.setFlag(flag, HdWorksDef.Flag.IS_BLACK, false);
            flag = Misc.setFlag(flag, HdWorksDef.Flag.CHECKPASS, true);
            Param insertData = new Param();
            insertData.setInt(HdWorksDef.Info.AID, targetAid);
            insertData.setCalendar(HdWorksDef.Info.CREATE_TIME, Calendar.getInstance());
            Map<String, Object> context = new HashMap<String, Object>();
            Map<String, String> headImg = new HashMap<String, String>();
            headImg.put("cutSrc", ImgPath);
            headImg.put("originSrc", ImgPath);
            context.put(HdWorksDef.Context.HEAD_IMG, headImg);
            context.put(HdWorksDef.Context.VIDEO_CODE, "");
            context.put(HdWorksDef.Context.AUDIO, "");
            context.put(HdWorksDef.Context.VIDEO, videoPath);
            context.put(HdWorksDef.Context.VIDEO_HEAD_IMG, ImgPath);
            FaiList<Map<String, Object>> imgList = new FaiList<Map<String, Object>>();
            Map<String, Object> img = new HashMap<String, Object>();
            img.put(HdWorksDef.Context.ImgListParamDef.CUT_SRC, ImgPath);
            img.put(HdWorksDef.Context.ImgListParamDef.ORIGIN_SRC, ImgPath);
            img.put(HdWorksDef.Context.ImgListParamDef.IS_POSTER, true);
            imgList.add(img);
            context.put(HdWorksDef.Context.IMG_LIST, imgList);
            insertData.setString(HdWorksDef.Info.CONTEXT, JSON.toJSONString(context));
            insertData.setInt(HdWorksDef.Info.FLAG, flag);
            insertData.setInt(HdWorksDef.Info.GAMEID, targetGameId);
            //insertData.setInt(HdWorksDef.Info.GROUP_ID, -1);
            String linkInfo = "{\"ausername\":\"商家\",\"aphone\":\"13122322211\"}";
            insertData.setString(HdWorksDef.Info.LINK_INFO, linkInfo);
            insertData.setInt(HdWorksDef.Info.PLAYER_ID, playerId);
            insertData.setString(HdWorksDef.Info.WORKS_NAME, "投票素材"+n);
            insertData.setString(HdWorksDef.Info.WORKS_EXPLAIN, "");
            insertData.setInt(HdWorksDef.Info.WORKS_NO, n++);
            insertData.setInt(HdWorksDef.Info.AID, targetAid);
            Ref<Integer> refWorksId = new Ref<Integer>();
            insertData.setInt(HdWorksDef.Info.ACHIEVEMENT, 0);
            rt = hdVoteCli.addWorks(targetAid, insertData, refWorksId);
            HdAssert.Biz.judgeNotLog(rt != Errno.OK, "add works err");
        }
        return rt;
    }





private void resetParamData(int aid, Param data, FaiList<String> resetFieldList, boolean isOem, FaiList<String> imgIdList,int ver) throws Exception {
    Calendar cal = Calendar.getInstance();
    long nowTimes = cal.getTimeInMillis();
    String portalResRoot = FileStg.getFaiHdPortalResRoot();
    Calendar createCal = data.getCalendar(HdGameDef.Info.CREATE_TIME, Calendar.getInstance());
    long createTimes = createCal.getTimeInMillis();
    Param setting = Param.parseParam(data.getString(HdGameDef.Info.SETTING, ""));
    Param comfort = Param.parseParam(setting.getString(HdGameDef.Setting.COMFORT,""));
    FaiList<Param> awardList = FaiList.parseParamList(data.getString(HdGameDef.Info.AWARDLIST, ""));
    int style = data.getInt(HdGameDef.Info.STYLE, 0);
    boolean isZhuLi = HdGameDef.isZhuLiGame(style);
    int flagB = data.getInt(HdGameDef.Info.FLAGB, 0);
    int flag = data.getInt(HdGameDef.Info.FLAG, 0);

    int flagC = data.getInt(HdGameDef.Info.FLAGC, 0);
    flagC |= HdGameDef.FlagC.IS_COPY_GAME;

    /**新的活动，复制的时候，setting中的hn和hl要从prof中获取*/
    if(createTimes > HdGameDef.CompatibleTime.NEED_GET_HNHL_FROM_PROF ){

        if( ver!= HdProfDef.Version.Corp.MD){

            HdProf hdProf = (HdProf)Core.getCorpKit(1,Kid.HD_PROF);
            Param prof = hdProf.getProf(aid);
            if(prof != null && !prof.isEmpty()){
                String name = prof.getString(HdProfDef.Info.NAME, "");
                String link = prof.getString(HdProfDef.Info.LINK, "");
                setting.setString(HdGameDef.Setting.HOST_LINK, link);
                setting.setString(HdGameDef.Setting.HOST_NAME, name);
            }else{
                Log.logErr("resetParamData; getProf err ; aid=%d", aid);
            }
        }
    }


    Set<String> flags = new HashSet<String>();
    flags.add("flag");
    flags.add("flagB");
    flags.add("flagC");

    for(String key : resetFieldList){
        if(key.indexOf(HdGameDef.Info.SETTING) > -1){
            if(setting == null || setting.isEmpty()){
                continue;
            }else{
                key = key.replace(HdGameDef.Info.SETTING + "-", "");
                //铂金版--参与人数限制还原
                if(HdGameDef.Setting.LIMIT_JOIN_NUM.equals(key)){
                    int joinLimit = setting.getInt(key, 0);
                    if(isOem && !isZhuLi){
                        if(joinLimit > HdGameDef.gameLimit.PLAYER_LIMIT){
                            setting.setInt(HdGameDef.Setting.LIMIT_JOIN_NUM, HdGameDef.gameLimit.PLAYER_LIMIT);
                        }
                    }else{
                        if(joinLimit > HdGameDef.gameLimit.HD_PLAYER_LIMIT){
                            setting.setInt(HdGameDef.Setting.LIMIT_JOIN_NUM, HdGameDef.gameLimit.HD_PLAYER_LIMIT);
                        }
                    }
                }else if(HdGameDef.Setting.HELP_TYPE.equals(key)){
                    //白银版，好友助力
                    setting.setInt(HdGameDef.Setting.HELP_TYPE, HdGameDef.helpType.CLOSE);
                }else if(HdGameDef.Setting.LINK_INFO_TYPE.equals(key)){
                    //白银版，开启联系方式
                    if(createTimes <= 1478003400000L){
                        setting.setInt(HdGameDef.Setting.LINK_INFO_TYPE, HdGameDef.Setting.LinkInfoType.CLOSE);
                    }
                }else if(HdGameDef.Setting.MAX_AWARD_NUM.equals(key)){
                    setting.setInt(HdGameDef.Setting.MAX_AWARD_NUM, setting.getInt(HdGameDef.Setting.AWARD_NUM,3));
                }else if(HdGameDef.Setting.GAMEUNIT.equals(key)){
                    //白银版，分数单位
                    String unit = HdGameDef.getScoreUnit(style);
                    setting.setString(HdGameDef.Setting.GAMEUNIT, unit);
                }else if(HdGameDef.Setting.GAMETIMETYPE.equals(key)){
                    //白银版，游戏时间
                    setting.setInt(HdGameDef.Setting.GAMETIMETYPE, HdGameDef.gameTimeStyle.DEFAULT);
                }else if(HdGameDef.Setting.VOTER_NUM.equals(key)){
                    setting.setInt(HdGameDef.Setting.VOTER_NUM, 0);
                }else if(HdGameDef.Setting.CANALLIST.equals(key)&&ver<HdVerDef.BJ){
                    setting.setString(HdGameDef.Setting.CANALLIST,"");
                }else if (HdGameDef.Setting.RULE.equals(key)){//重置RULE字段
                    Param rule = Param.parseParam(setting.getString(HdGameDef.Setting.RULE, ""), new Param());
                    Param newRule = new Param();
                    newRule.setInt(HdGameDef.Setting.Rule.CONSUME,rule.getInt(HdGameDef.Setting.Rule.CONSUME,0));
                    newRule.setInt(HdGameDef.Setting.Rule.EXP_PLAYER,rule.getInt(HdGameDef.Setting.Rule.EXP_PLAYER,1));
                    newRule.setInt(HdGameDef.Setting.Rule.EXP_AWARD,rule.getInt(HdGameDef.Setting.Rule.EXP_AWARD,10));
                    setting.setString(HdGameDef.Setting.RULE,newRule.toJson());
				}else if(HdGameDef.Setting.SMS_TEMPLATE_LIST.equals(key)){//短信list，选用默认模板
                    Param msgModel = new Param();
                    msgModel.setInt(HdGameDef.Setting.SmsTemplateList.SMSID, 0);
                    msgModel.setInt(HdGameDef.Setting.SmsTemplateList.CONSTRACT_SMS_ID, 0);
                    msgModel.setInt(HdGameDef.Setting.SmsTemplateList.GIFT_SMS_ID, 0);
                    msgModel.setInt(HdGameDef.Setting.SmsTemplateList.VOTE_SMS_ID, 0);
                    // setting.setString(HdGameDef.Setting.SMS_TEMPLATE_LIST,msgModel.toJson());
                    setting.setParam(HdGameDef.Setting.SMS_TEMPLATE_LIST, msgModel);
                }else if(HdGameDef.Setting.HOST_LOGO.equals(key)){
                    int brandExposure = setting.getInt(HdGameDef.Setting.BRAND_EXPOSURE,0);
                    if(brandExposure==HdGameDef.Setting.BrandExposure.SHOW_LOGO){
                        setting.remove(HdGameDef.Setting.HOST_LOGO);
                    }
                }else if(HdGameDef.Setting.BRAND_EXPOSURE_TEXT.equals(key)){
                    setting.setString(HdGameDef.Setting.BRAND_EXPOSURE_TEXT, "宣传语");
                }else if(HdGameDef.Setting.MENU_LINK.equals(key)){
                    setting.setString(HdGameDef.Setting.MENU_LINK, "");
                }
            }
        }else if(key.indexOf(HdGameDef.Setting.COMFORT) > -1){
            //安慰奖的处理
            if(comfort == null || comfort.isEmpty()){
                continue;
            }
            comfort.setBoolean(HdGameDef.Setting.Comfort.COMFORT_ISCUSCODE, false);
            comfort.setString(HdGameDef.Setting.Comfort.COMFORT_CODE, "");
            comfort.setString(HdGameDef.Setting.Comfort.COMFORT_CODE_CUS, "");
            comfort.setInt(HdGameDef.Setting.Comfort.COMFORT_USED_AOMOUNT, 0);
            comfort.setInt(HdGameDef.Setting.Comfort.COMFORT_NUM, 0);
            comfort.setList(HdGameDef.Setting.Comfort.REPLY_DEFINE,new FaiList<String>());
            if(createTimes <= 1478003400000L){
                comfort.setInt(HdGameDef.Setting.Comfort.COMFORT_CONTACT_INFO, 0);
            }

            setting.setString(HdGameDef.Setting.COMFORT, comfort.toJson());
        }else if(key.indexOf(HdGameDef.Info.FLAGB) > -1){
            //FLAGB处理
            key = key.replace(HdGameDef.Info.FLAGB + "-", "");
            int miscArg = Parser.parseInt(key, 0);
            if(Misc.checkBit(flagB, miscArg)){
                flagB = flagB ^ miscArg;
            }
        }else if(key.indexOf(HdGameDef.Info.FLAG) > -1){

            key = key.replace(HdGameDef.Info.FLAG + "-", "");
            int miscArg = Parser.parseInt(key, 0);
            if(Misc.checkBit(flag, miscArg)){
                if(miscArg != HdGameDef.Flag.SHOW_SKILL_SUP && miscArg != HdGameDef.Flag.LIMIT_REG_NUM){
                    flag = flag ^ miscArg;
                }
            }else{
                if(miscArg == HdGameDef.Flag.SHOW_SKILL_SUP || miscArg == HdGameDef.Flag.LIMIT_REG_NUM){
                    flag = flag | miscArg;
                }
            }

        }else{
            if(data.containsKey(key)){
                Object value = data.getObject(key);
                if(value instanceof String){
                    data.setString(key, "");
                }else if(value instanceof Integer){
                    data.setInt(key, 0);
                }
            }
        }
    }
    int srcId = data.getInt(HdGameDef.Info.SRCID, 0);
    int templateId = data.getInt(HdGameDef.Info.TEMPLATE_ID, 0);
    if(srcId < 0){
        FaiList<String> orderList = HdWebDef.getOssOrder();
        String orderStr = style + (templateId < 0 ? "" : "_" + templateId);
        for(int i = 0; i < orderList.size(); i++){
            if(orderStr.equals(orderList.get(i))){
                data.setInt(HdGameDef.Info.SRCID, i);
                setting.setInt(HdGameDef.Setting.SRCID, i);
                break;
            }
        }
    }

    //旧的排名型活动复制时加上参与次数限制
    int playTimesLimit = setting.getInt(HdGameDef.Setting.PLAY_TIMES_LIMIT,-1);
    if( playTimesLimit == -1 ){
        setting.setInt(HdGameDef.Setting.PLAY_TIMES_LIMIT,5);
    }

    //旧的游戏活动复制时，邀请助力加上邀请次数限制
    int inviteTimeLimit = setting.getInt(HdGameDef.Setting.INVITE_TIME_LIMIT,-1); //每日邀请次数限制
    if( inviteTimeLimit == -1 ){
        setting.setInt(HdGameDef.Setting.INVITE_TIME_LIMIT,10);
    }

    FaiList<Param> editPropList = FaiList.parseParamList(setting.getString(HdGameDef.Setting.PROPLIST));
    FaiList<Param> propList = HdWebTemplateDef.getEditPropList( HdGameDef.Style.DEFAULT, style );
    if( editPropList != null && !editPropList.isEmpty() && propList != null && !propList.isEmpty()){
        for(Param item : editPropList){
            String name = item.getString("name","");
            if( name.startsWith("theGetPricePic") ){
                try{
                    double height = Parser.parseDouble(item.getParam("size",new Param()).getString("height","").replace("rem",""),0.0);
                    double top = Parser.parseDouble(item.getParam("pos",new Param()).getString("top","").replace("rem",""),0.0);
                    if(height + top > 13.5){
                        HdTool.extend(true,item,HdGameDef.getEditProp(propList,name));
                    }
                } catch (Exception e) {
                    Log.logStd("copy pricePic err item=%s",item);
                }
            }
        }
        setting.setString(HdGameDef.Setting.PROPLIST,editPropList.toJson());
    }

    data.setInt(HdGameDef.Info.FLAGB, flagB);
    data.setInt(HdGameDef.Info.FLAG,  flag);
    Param faiOpenConf = Web.getConf("faiopenid");
    if (faiOpenConf == null) {
        faiOpenConf = new Param();
    }
    Log.logStd("faiOpenConf=%s", faiOpenConf);
    long faiOpenIdTimeStamp = faiOpenConf.getInt("timestamp", -1);
    boolean openFaiWxuser = HdGameDef.openFaiOpenId(aid, isOem, faiOpenConf);
    boolean openFaiWxUserThisGame = openFaiWxuser && ((Calendar.getInstance().getTimeInMillis()/1000) > faiOpenIdTimeStamp) ? true: false;
    if( openFaiWxUserThisGame ){
        flagC |= HdGameDef.FlagC.USED_FAIOPENID;
        Log.logDbg("the johnvi openFaiWxUserThisGame=%s, flagC=%d", openFaiWxUserThisGame, flagC);
    }
    data.setInt(HdGameDef.Info.FLAGC,  flagC);
    data.setString(HdGameDef.Info.SETTING, setting.toString());
    //最后对比下flag是否有注册复制功能，没注册的话是不允许复制的
    setFlags(flags, data);
    HdProf hdProf = (HdProf) Core.getCorpKit(1,Kid.HD_PROF);
    for(Param award : awardList){
        getTemplateIdList(aid, award, imgIdList);
        if(aid != 24265546 && aid != 24447872 && aid != 9858037 && aid != 9851878 && aid != 9897375 && aid != 9908663){
            award.setInt(HdGameDef.Award.AMOUNT, 0);        //奖品数量清空
        }
        if(style == HdGameDef.Style.PTCHL){
            award.setInt(HdGameDef.Award.GROUP_AWARD_AMOUNT, 0);
        }
        award.setInt(HdGameDef.Award.CARDID, -1);       //清空cardId
        award.remove(HdGameDef.Award.WXCARDID);         //清空cardId
        award.remove(HdGameDef.Award.EXCELNAME);        //清空上传过的excel文件名
        award.setBoolean(HdGameDef.Award.ISCUSCODE, false);
        long cashBeginTime = award.getLong(HdGameDef.Award.CODE_BEGIN_TIME, 0L);
        long cashEndTime = award.getLong(HdGameDef.Award.CODE_END_TIME, 0L);
        if(resetFieldList.contains(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.GENERATE_WXCARD)){//是否生成微信卡券
            award.setBoolean(HdGameDef.Award.GENERATE_WXCARD, false);
        }
        if(resetFieldList.contains(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.ISCANCELVER)){//自助核销
            award.setBoolean(HdGameDef.Award.ISCANCELVER, false);
        }
        if(resetFieldList.contains(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.NEEDLESSCONSUME)){//无需核销
            award.setBoolean(HdGameDef.Award.NEEDLESSCONSUME, false);
        }
        if(resetFieldList.contains(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.AWARDTYPE)){//奖品类型(微信红包和微信优惠券是白金版本的)
            int aw_type = award.getInt(HdGameDef.Award.AWARDTYPE, HdGameDef.awardType.REALGIFT);
            if( aw_type== HdGameDef.awardType.PASSWORDRED && ver < HdVerDef.BJ){
                award.setInt(HdGameDef.Award.AWARDTYPE, HdGameDef.awardType.REALGIFT);
            }
            if( aw_type== HdGameDef.awardType.WXCARD && ver < HdVerDef.BJ){
                award.setInt(HdGameDef.Award.AWARDTYPE, HdGameDef.awardType.REALGIFT);
            }
        }
        if(resetFieldList.contains(HdGameDef.Info.AWARDLIST + "-" + HdGameDef.Award.CONTACT_INFO)){
            if(createTimes <= 1478003400000L){
                award.setInt(HdGameDef.Award.CONTACT_INFO, 0);
            }
        }
        if(award.getBoolean(HdGameDef.Award.GENERATE_WXCARD,false)){
            if(award.getInt(HdGameDef.Award.AWARDTYPE,2) != HdGameDef.awardType.WXCARD){
                award.setInt(HdGameDef.Award.AWARDTYPE,HdGameDef.awardType.WXCARD);
            }
        }else if(award.getInt(HdGameDef.Award.AWARDTYPE,2) == HdGameDef.awardType.WXCARD){
            award.setInt(HdGameDef.Award.AWARDTYPE,HdGameDef.awardType.REALGIFT);
        }
        if(award.getInt(HdGameDef.Award.PASSWORDREDNUM,-1) == -1){ //旧活动没开启口令红包，新活动加上
            award.setInt(HdGameDef.Award.PASSWORDREDNUM,100);
        }
        if(award.getInt(HdGameDef.Award.CASHTYPE,-1) == -1){ //没有CASHTYPE的活动要加上
            award.setInt(HdGameDef.Award.CASHTYPE,HdGameDef.CashType.CASH_UNDERLINE);
        }
        if((ver == HdProfDef.Version.Corp.FRE || ver == HdProfDef.Version.Corp.BY) && award.getInt(HdGameDef.Award.CASHTYPE,-1) == HdGameDef.CashType.CASH_WX_AUTO){
            award.remove(HdGameDef.Award.REPLY_DEFINE);
        }
        //判断建站优惠券是否存在，不存在就将奖品类型设置为礼品
        if(award.getInt(HdGameDef.Award.AWARDTYPE,-1)==HdGameDef.awardType.JZ_COUPON){
            Param jzData = award.getParam(HdGameDef.Award.JZ_DATA, new Param());
            if (!Str.isEmpty(jzData)) {
                int siteId = jzData.getInt(HdGameDef.Award.JzData.SITE_ID,-1);
                int couponId = jzData.getInt(HdGameDef.Award.JzData.JZ_COUPON_ID,-1);
                int wid = SiteDef.genWid(siteId, 0);
                MallCoupon oCoupon = (MallCoupon) Core.getCorpKit(aid, wid, Kid.MALL_COUPON);
                Param couponInfo = oCoupon.getCouponInfo(couponId);
                if (Str.isEmpty(couponInfo)||hdProf.checkVerForJzCoupon(aid,siteId)!=Errno.OK){//删除优惠券和关闭版本和优惠券功能都会重置奖品
                    award.setInt(HdGameDef.Award.AWARDTYPE,HdGameDef.awardType.REALGIFT);
                }
            }
        }
    }
    getTemplateIdList(aid, setting, imgIdList);
    Param cusImgs = setting.getParam(HdGameDef.Setting.CUS_IMGS, new Param());
    if(!Str.isEmpty(cusImgs)){
        getTemplateIdList(aid, cusImgs, imgIdList);
        Log.logDbg("the johnvi imgIdList=%s", imgIdList);
    }
    data.setCalendar(HdGameDef.Info.CREATE_TIME, cal);
    data.setString(HdGameDef.Info.AWARDLIST, awardList.toString());
    data.setString(HdGameDef.Info.APPID, "");                          //清空游戏绑定的appid
    data.setString(HdGameDef.Info.NICK_NAME, "");                      //清空游戏绑定的公众号名称
}


private void getTemplateIdList (int aid, Param data, FaiList<String> tmpImgList){
    Set<String> keySet = data.keySet();
    for(String key : keySet){
        Object value = data.getObject(key);
        dealValueType(value, aid, tmpImgList);
    }
}


//获取图片id List
private void getTemplateIdListByList(int aid, FaiList<Object> propList, FaiList<String> tmpImgList){
    for(Object value : propList){
        dealValueType(value, aid, tmpImgList);
    }
}

//根据数据类型以不同方法获取imgId
private void dealValueType(Object value, int aid, FaiList<String> tmpImgList){
     if(value instanceof String){
        String str = (String)value;
        Param param = Param.parseParam(str);
        FaiList<Object> list = FaiList.parseList(str, Var.Type.OBJECT);
        if(param != null && !param.isEmpty()){
            getTemplateIdList(aid, param, tmpImgList);
        }else if(list != null && !list.isEmpty()){
            getTemplateIdListByList(aid, list, tmpImgList);
        }else{
            String regexStr = "^http[s]{0,1}://" + aid + "\\.h40\\.[a-zA-Z0-9]{1,10}\\.com(.)*$";
            Pattern pattern = Pattern.compile(regexStr);
            Matcher matcher = pattern.matcher(str);
            if(matcher.matches()){
                String imgId = str.substring(str.lastIndexOf("/") + 1, str.lastIndexOf("."));
                if(imgId != null && !"".equals(imgId.trim()) && !tmpImgList.contains(imgId)){
                    tmpImgList.add(imgId);
                }
            }
        }
    }else if(value instanceof FaiList){
        getTemplateIdListByList(aid, (FaiList<Object>)value, tmpImgList);
    }else if(value instanceof Param){
        getTemplateIdList(aid, (Param)value, tmpImgList);
    }
}

private void setFlags(Set<String> flags, Param data){
    int flagB = data.getInt(HdGameDef.Info.FLAGB, 0);
    int flag = data.getInt(HdGameDef.Info.FLAG, 0);
    int flagC = data.getInt(HdGameDef.Info.FLAGC, 0);
    for(String str : flags){
        int flagNew = 0;
        for(String flagSetTrue : HdGameDef.GameFucVer.flagSetTrueDefault){/*把需要默认置1的先置为1*/
            String[] flagSetTrueSpit = flagSetTrue.split("-");
            if(flagSetTrueSpit!=null && flagSetTrueSpit.length==2){
                if(flagSetTrueSpit[0].equals(str)){
                    int flagNum = Integer.parseInt(flagSetTrueSpit[1]);
                    flagNew = flagNew | flagNum;
                }
            }
        }
        Log.logDbg("flagNew:"+flagNew);
        for(int i = 1; i < 32; i++){
            int flagVal = 1 << i;
            boolean isTrue = false;
            if("flag".equals(str)){
                isTrue = Misc.checkBit(flag, flagVal);
            }else if("flagB".equals(str)){
                isTrue = Misc.checkBit(flagB, flagVal);
            }else if("flagC".equals(str)){
                isTrue = Misc.checkBit(flagC, flagVal);
            }
            if(isTrue){
                String key = str + "-" + flagVal;
                if(HdGameDef.GameFucVer.flagCache.contains(key)){
                    flagNew = Misc.setFlag(flagNew, flagVal, true);
                }
            }
        }
        Log.logDbg("flagNew:"+flagNew);
        data.setInt(str, flagNew);
    }
}


%>

<%
	String output = "";
	try{
        // if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){out.println("没有权限");return;}
		String cmd = request.getParameter("cmd");
		if (cmd == null) {
			return;
		}

		if (cmd.equals("copyActive")) {
			output = copyActive(request, response);
		}

	}catch (Exception exp){
		output = WebOss.checkAjaxException(exp);
		throw exp;
	}
    // out.print(output);
    SensitiveInfoCheckUtil.check(request, out, output);
%>
