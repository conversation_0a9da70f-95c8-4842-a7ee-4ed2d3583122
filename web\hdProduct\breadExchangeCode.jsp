<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.sql.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%if(!Auth.checkFaiscoAuth("authHdManage|authAd", false)){out.println("没有权限");return;}%>
<%
    String jSessionId = Request.getCookie( request, Request.CookieId.SESSION_ID );
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>上传馒头商学院兑换码</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<%-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.4.2/lib/theme-chalk/index.css"> --%>
<link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
</head>
<body>
<div id="app">
<%-- <el-upload
   class='image-uploader'
   :multiple='false'
   :auto-upload='true'
  	accept=".xlsx"
   list-type='text'
   :show-file-list='true'
   :before-upload="beforeUpload"
   :drag='true'
   action='/ajax/advanceUpload.jsp?cmd=uploadMteduVipCodeList&ver=1'
   :limit="1"
   :on-exceed="handleExceed"
>
   <i class="el-icon-upload"></i>
   <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
   <div class="el-upload__tip" slot="tip">一次只能上传一个文件，仅限xls格式，单文件不超过1MB</div>
</el-upload> --%>
<div class="comUploadBox">
	<form id="uploadFileForm" enctype="multipart/form-data">
		<span class="btn">
			<input @change="uploadMteduVipCodeList" ref="fileInput" type="file" class="uploadInput theSpecialDesigan" name="ctrl" accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
		</span>
		<span class="uploadText"></span>
	</form>
</div>
</div>
</body>
<%-- <script src="https://cdn.jsdelivr.net/npm/vue@2.5.15/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vue-resource@1.5.1/dist/vue-resource.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/element-ui@2.4.2/lib/index.js"></script> --%>
<script src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
<script src="<%=HdOssResDef.getResPath("js_vue_resource")%>"></script>
<script src="<%=HdOssResDef.getResPath("js_element")%>"></script>
<script type="text/javascript">
function encodeHtml(html){
    return html && html.replace ? (html.replace(/&/g, "&amp;").replace(/ /g, "&nbsp;").replace(/\b&nbsp;+/g, " ").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\\/g, "&#92;").replace(/\'/g, "&#39;").replace(/\"/g, "&quot;").replace(/\n/g, "<br/>").replace(/\r/g, "")) : html;
}
new Vue({
	el: '#app',
	data: {

	},
	methods: {
		uploadFile: function() {
			// this.$refs.fileInput.click();
		},
		uploadMteduVipCodeList: function(e){
			var file = this.$refs.fileInput.files[0];
			// uploadBtn.text('重新上传');
			if(!file){
				return;
			}
			var fd = new FormData();
			fd.append('filedata', file);
			Vue.http.post(
				'/ajax/advanceUpload.jsp?cmd=uploadMteduVipCodeList&ver=1',
				fd,
				{
					headers: {
						"Content-Type": "multipart/form-data"
					}
				}
			).then(({data}) => {
				console.log(data);
				// _this.isLoading = false;
				// if(data.success) {
				// 	_this.activeList.splice(index, 1);
				// 	_this.$message({
				// 		type: 'success',
				// 		message: '删除成功',
				// 		duration: 500
				// 	});
				// }else{
				// 	showError(data.msg);
				// }
			});
			// $.ajax({
			// 	type: 'post',
			// 	url: '/ajax/advanceUpload.jsp?cmd=uploadMteduVipCodeList&ver=1',
			// 	data: fd,
			// 	dataType: 'json',
			// 	contentType: false,
			// 	processData: false,
			// 	success: function (response) {
			// 		if(response.rt) {
			// 			HdPortal.hdShowMsg('error', response.result || '系统繁忙，请稍后再试', false);
			// 		}else{
			// 			HdPortal.hdShowMsg('right', '上传成功!', false);
			// 		}
			// 	}
			// });
		},
		// 上传文件之前的钩子
		beforeUpload (file) {
			console.log('beforeUpload')
			console.log(file.type)
			const isText = file.type === 'application/vnd.ms-excel'
			const isTextComputer = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			return (isText | isTextComputer)
		},
		// 上传文件个数超过定义的数量
		handleExceed (files, fileList) {
			this.$message.warning(`当前限制选择 1 个文件，请删除后继续上传`)
		},
	}
});
</script>
</html>
