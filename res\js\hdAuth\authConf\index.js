(function(){'use strict';var script = {
  name: "AuthConf",
  data() {
    return {
      tableData: [],
      pageData: {
        size: 10,
        total: 0,
        currentPage: 1
      },
      staff: "",
      newStaff: "",
      loading: false,
      // 权限控制
      auth: {
        hdSaleManage: true,
        authAdm: true
      }
    };
  },
  computed: {
    hasPermission() {
      return this.auth.hdSaleManage && this.auth.authAdm;
    }
  },
  created() {
    this.getDataList();
  },
  methods: {
    /**
     * 设置员工权限
     */
    setAuth(acct, type, updateFlag, sid, checked) {
      console.info("设置权限参数:", { acct, type, updateFlag, sid, checked });
      this.$confirm(`确定要设置该账号 ${acct} 权限吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        Fai.http.post("hdSale_h.jsp", {
          cmd: "setStaffAuth",
          acct,
          type,
          updateFlag,
          sid,
          checked
        }, false).then((result) => {
          this.loading = false;
          if (result.success) {
            this.$message({
              showClose: true,
              type: "success",
              message: result.msg || "权限设置成功"
            });
            this.getDataList();
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: result.msg || "设置权限失败"
            });
          }
        }).catch((error) => {
          this.loading = false;
          console.error("设置权限失败:", error);
          this.$message({
            showClose: true,
            type: "error",
            message: "网络错误，请稍后重试"
          });
        });
      }).catch(() => {
      });
    },
    /**
     * 获取员工列表数据
     */
    getDataList() {
      console.info("获取数据列表，参数:", this.pageData);
      this.loading = true;
      const params = {
        cmd: "getAuthStaffList",
        size: this.pageData.size,
        currentPage: this.pageData.currentPage,
        staff: this.staff.trim()
      };
      Fai.http.post("hdSale_h.jsp?cmd=getAuthStaffList", params, false).then((result) => {
        this.loading = false;
        console.info("获取数据结果:", result);
        if (result.success) {
          this.tableData = result.dataList || [];
          this.pageData.total = result.total || 0;
          if (this.tableData.length === 0) {
            this.$message({
              showClose: true,
              type: "info",
              message: "暂无员工数据"
            });
          }
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: result.msg || "获取数据失败"
          });
        }
      }).catch((error) => {
        this.loading = false;
        console.error("获取数据失败:", error);
        this.$message({
          showClose: true,
          type: "error",
          message: "网络错误，请稍后重试"
        });
      });
    },
    /**
     * 新增员工
     */
    addNewStaff() {
      const staffName = this.newStaff.trim();
      if (!staffName) {
        this.$message({
          showClose: true,
          type: "warning",
          message: "请输入正确的员工账号"
        });
        return;
      }
      this.$confirm(`确定要新增员工 ${staffName} 吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        Fai.http.post("hdSale_h.jsp", {
          cmd: "addHdOssStaff",
          acct: staffName
        }, false).then((result) => {
          this.loading = false;
          if (result.success) {
            this.$message({
              showClose: true,
              type: "success",
              message: result.msg || "新增员工成功"
            });
            this.newStaff = "";
            this.getDataList();
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: result.msg || "新增员工失败"
            });
          }
        }).catch((error) => {
          this.loading = false;
          console.error("新增员工失败:", error);
          this.$message({
            showClose: true,
            type: "error",
            message: "网络错误，请稍后重试"
          });
        });
      }).catch(() => {
      });
    },
    /**
     * 分页改变事件
     */
    handleCurrentChange(page) {
      this.pageData.currentPage = page;
      this.getDataList();
    }
  }
};function normalizeComponent(template, style, script, scopeId, isFunctionalTemplate, moduleIdentifier
/* server only */
, shadowMode, createInjector, createInjectorSSR, createInjectorShadow) {
  if (typeof shadowMode !== 'boolean') {
    createInjectorSSR = createInjector;
    createInjector = shadowMode;
    shadowMode = false;
  } // Vue.extend constructor export interop.


  var options = typeof script === 'function' ? script.options : script; // render functions

  if (template && template.render) {
    options.render = template.render;
    options.staticRenderFns = template.staticRenderFns;
    options._compiled = true; // functional template

    if (isFunctionalTemplate) {
      options.functional = true;
    }
  } // scopedId


  if (scopeId) {
    options._scopeId = scopeId;
  }

  var hook;

  if (moduleIdentifier) {
    // server build
    hook = function hook(context) {
      // 2.3 injection
      context = context || // cached call
      this.$vnode && this.$vnode.ssrContext || // stateful
      this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional
      // 2.2 with runInNewContext: true

      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__;
      } // inject component styles


      if (style) {
        style.call(this, createInjectorSSR(context));
      } // register component module identifier for async chunk inference


      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier);
      }
    }; // used by ssr in case component is cached and beforeCreate
    // never gets called


    options._ssrRegister = hook;
  } else if (style) {
    hook = shadowMode ? function (context) {
      style.call(this, createInjectorShadow(context, this.$root.$options.shadowRoot));
    } : function (context) {
      style.call(this, createInjector(context));
    };
  }

  if (hook) {
    if (options.functional) {
      // register for functional component in vue file
      var originalRender = options.render;

      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context);
        return originalRender(h, context);
      };
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate;
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook];
    }
  }

  return script;
}

var normalizeComponent_1 = normalizeComponent;var isOldIE = typeof navigator !== 'undefined' && /msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());
function createInjector(context) {
  return function (id, style) {
    return addStyle(id, style);
  };
}
var HEAD;
var styles = {};

function addStyle(id, css) {
  var group = isOldIE ? css.media || 'default' : id;
  var style = styles[group] || (styles[group] = {
    ids: new Set(),
    styles: []
  });

  if (!style.ids.has(id)) {
    style.ids.add(id);
    var code = css.source;

    if (css.map) {
      // https://developer.chrome.com/devtools/docs/javascript-debugging
      // this makes source maps inside style tags work properly in Chrome
      code += '\n/*# sourceURL=' + css.map.sources[0] + ' */'; // http://stackoverflow.com/a/26603875

      code += '\n/*# sourceMappingURL=data:application/json;base64,' + btoa(unescape(encodeURIComponent(JSON.stringify(css.map)))) + ' */';
    }

    if (!style.element) {
      style.element = document.createElement('style');
      style.element.type = 'text/css';
      if (css.media) style.element.setAttribute('media', css.media);

      if (HEAD === undefined) {
        HEAD = document.head || document.getElementsByTagName('head')[0];
      }

      HEAD.appendChild(style.element);
    }

    if ('styleSheet' in style.element) {
      style.styles.push(code);
      style.element.styleSheet.cssText = style.styles.filter(Boolean).join('\n');
    } else {
      var index = style.ids.size - 1;
      var textNode = document.createTextNode(code);
      var nodes = style.element.childNodes;
      if (nodes[index]) style.element.removeChild(nodes[index]);
      if (nodes.length) style.element.insertBefore(textNode, nodes[index]);else style.element.appendChild(textNode);
    }
  }
}

var browser = createInjector;/* script */
const __vue_script__ = script;

/* template */
var __vue_render__ = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:"fai-staff-list"},[_c('div',{staticClass:"search-section"},[_c('span',[_vm._v("\n      搜索员工：\n      "),_c('el-input',{staticClass:"short-input",attrs:{"placeholder":"IT系统员工英文名/中文名"},on:{"keyup":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,"enter",13,$event.key,"Enter")){ return null; }return _vm.getDataList.apply(null, arguments)}},model:{value:(_vm.staff),callback:function ($$v) {_vm.staff=$$v;},expression:"staff"}})],1),_vm._v(" "),_c('el-button',{attrs:{"type":"primary","size":"small"},on:{"click":_vm.getDataList}},[_vm._v("\n      查询\n    ")])],1),_vm._v(" "),_c('div',{staticClass:"add-section"},[_vm._v("\n    新增员工：\n    "),_c('el-input',{staticClass:"add-input",attrs:{"placeholder":"IT系统员工英文名"},on:{"keyup":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,"enter",13,$event.key,"Enter")){ return null; }return _vm.addNewStaff.apply(null, arguments)}},model:{value:(_vm.newStaff),callback:function ($$v) {_vm.newStaff=$$v;},expression:"newStaff"}}),_vm._v(" "),_c('el-button',{attrs:{"type":"primary","size":"small"},on:{"click":_vm.addNewStaff}},[_vm._v("\n      新增\n    ")])],1),_vm._v(" "),_c('el-table',{directives:[{name:"loading",rawName:"v-loading",value:(_vm.loading),expression:"loading"}],attrs:{"data":_vm.tableData,"row-key":"rowkey","stripe":"","border":"","element-loading-text":"加载中..."}},[_c('el-table-column',{attrs:{"label":"姓名","prop":"name"}}),_vm._v(" "),_c('el-table-column',{attrs:{"label":"账号","prop":"sacct"}}),_vm._v(" "),(_vm.hasPermission)?_c('el-table-column',{attrs:{"label":"操作","width":"650","fixed":"right"},scopedSlots:_vm._u([{key:"default",fn:function(scope){return [_c('div',{staticClass:"auth-controls",staticStyle:{"margin-bottom":"10px"}},[_c('div',{staticClass:"auth-title"},[_vm._v("全部")]),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("adm")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'all', scope.row.auth, scope.row.sid, scope.row.all)}},model:{value:(scope.row.all),callback:function ($$v) {_vm.$set(scope.row, "all", $$v);},expression:"scope.row.all"}})],1)]),_vm._v(" "),_c('div',{staticClass:"auth-controls",staticStyle:{"margin-bottom":"10px"}},[_c('div',{staticClass:"auth-title"},[_vm._v("互动")]),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("互动销售管理")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'hdSaleManage', scope.row.auth, scope.row.sid, scope.row.hdSaleManage)}},model:{value:(scope.row.hdSaleManage),callback:function ($$v) {_vm.$set(scope.row, "hdSaleManage", $$v);},expression:"scope.row.hdSaleManage"}})],1),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("互动销售组长")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'hdSaleGroupLeader', scope.row.auth, scope.row.sid, scope.row.hdSaleGroupLeader)}},model:{value:(scope.row.hdSaleGroupLeader),callback:function ($$v) {_vm.$set(scope.row, "hdSaleGroupLeader", $$v);},expression:"scope.row.hdSaleGroupLeader"}})],1),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("互动销售")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'hdSale', scope.row.auth, scope.row.sid, scope.row.hdSale)}},model:{value:(scope.row.hdSale),callback:function ($$v) {_vm.$set(scope.row, "hdSale", $$v);},expression:"scope.row.hdSale"}})],1)]),_vm._v(" "),_c('div',{staticClass:"auth-controls"},[_c('div',{staticClass:"auth-title"},[_vm._v("速创")]),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("速创PM")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'scPm', scope.row.auth, scope.row.sid, scope.row.scPm)}},model:{value:(scope.row.scPm),callback:function ($$v) {_vm.$set(scope.row, "scPm", $$v);},expression:"scope.row.scPm"}})],1),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("速创运营")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'scOperation', scope.row.auth, scope.row.sid, scope.row.scOperation)}},model:{value:(scope.row.scOperation),callback:function ($$v) {_vm.$set(scope.row, "scOperation", $$v);},expression:"scope.row.scOperation"}})],1),_vm._v(" "),_c('div',{staticClass:"auth-item"},[_c('el-button',{attrs:{"type":"","size":"mini"}},[_vm._v("速创开发")]),_vm._v(" "),_c('el-checkbox',{on:{"change":function($event){return _vm.setAuth(scope.row.sacct, 'scDevelop', scope.row.auth, scope.row.sid, scope.row.scDevelop)}},model:{value:(scope.row.scDevelop),callback:function ($$v) {_vm.$set(scope.row, "scDevelop", $$v);},expression:"scope.row.scDevelop"}})],1)])]}}],null,false,3059873734)}):_vm._e()],1),_vm._v(" "),_c('div',{staticClass:"pagination-section"},[_c('el-pagination',{attrs:{"current-page":_vm.pageData.currentPage,"page-size":_vm.pageData.size,"layout":"total, prev, pager, next, jumper","total":_vm.pageData.total},on:{"current-change":_vm.handleCurrentChange,"update:currentPage":function($event){return _vm.$set(_vm.pageData, "currentPage", $event)},"update:current-page":function($event){return _vm.$set(_vm.pageData, "currentPage", $event)}}})],1)],1)};
var __vue_staticRenderFns__ = [];

  /* style */
  const __vue_inject_styles__ = function (inject) {
    if (!inject) return
    inject("data-v-37384605_0", { source: "@charset \"UTF-8\";.fai-staff-list[data-v-37384605]{padding:0 20px 20px}.fai-staff-list .add-section[data-v-37384605],.fai-staff-list .search-section[data-v-37384605]{margin:10px 0;padding:10px}.fai-staff-list .add-section .add-input[data-v-37384605],.fai-staff-list .add-section .short-input[data-v-37384605],.fai-staff-list .search-section .add-input[data-v-37384605],.fai-staff-list .search-section .short-input[data-v-37384605]{width:200px;margin:0 10px}.fai-staff-list .auth-controls[data-v-37384605]{display:flex;flex-wrap:wrap;gap:8px;align-items:center}.fai-staff-list .auth-controls .auth-title[data-v-37384605]{margin-right:10px}.fai-staff-list .auth-controls .auth-item[data-v-37384605]{display:flex;align-items:center;gap:4px;margin-bottom:4px}.fai-staff-list .pagination-section[data-v-37384605]{margin-top:20px;text-align:center}[data-v-37384605] .el-table .el-button--mini{margin-right:4px;font-size:12px}[data-v-37384605] .el-table .el-checkbox{margin-right:10px}[data-v-37384605] .el-loading-text{color:#409eff}", map: undefined, media: undefined });

  };
  /* scoped */
  const __vue_scope_id__ = "data-v-37384605";
  /* module identifier */
  const __vue_module_identifier__ = undefined;
  /* functional template */
  const __vue_is_functional_template__ = false;
  /* style inject SSR */
  
  /* style inject shadow dom */
  

  
  const __vue_component__ = /*#__PURE__*/normalizeComponent_1(
    { render: __vue_render__, staticRenderFns: __vue_staticRenderFns__ },
    __vue_inject_styles__,
    __vue_script__,
    __vue_scope_id__,
    __vue_is_functional_template__,
    __vue_module_identifier__,
    false,
    browser,
    undefined,
    undefined
  );// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
  // 检查挂载点是否存在
  const mountElement = document.querySelector('#auth-conf-app');
  
  if (mountElement) {
    new Vue({
      render: h => h(__vue_component__),
    }).$mount(mountElement);
    
    console.log('authConf Vue应用已成功挂载到:', mountElement.id);
  } else {
    console.error('找不到挂载点元素');
  }
});})();