(function( $, undefined ) {
	$.widget("ui.combobox", {
		_create: function() {
			var self = this;
			var selector = this.element;
			var width = selector.width();
			var input = $("<input>")
				.appendTo(selector)
				.autocomplete({
					source: self.options.source,
					delay: 0,
					minLength: 0,
					open: function(event, ui) { 
						//text -> title
						if(self.options.maxlength){
							$(this).autocomplete('widget').width(self.options.maxlength);
						}else{
							var wdgWidth = $(this).autocomplete("widget").width();//menu width
							var slcWidth = $(this).outerWidth()-6;

							if(input.width() != (width - Fai.getCssInt(input, "border-left-width"))){
								slcWidth = $(this).outerWidth();
								if(!self.options.readonly){
									slcWidth = $(this).outerWidth()-6;
								}
							}
							$(this).autocomplete("widget").width(slcWidth);
						}
						var items = $(this).autocomplete("widget").find("li");
						if(items.length == 1 && $(this).attr('readonly') == false ){
							var inputValue = $(this).val();
							var matching = false;
							$(items).each(function(i,obj){
								var text = $.trim( $(obj).text() );
								if( inputValue == text ){
									matching = true;
								}
							})
							if(matching){ $(this).autocomplete("close"); }
						}
					},
					select: function(event, ui) {
						$(self).data('key', ui.item.key);
						if (self.options.select){
							self.options.select(ui.item);
						}
						if (self.options.change){
							self.options.change(ui.item.label);
						}
						if (self.options.finish){
							setTimeout(function(){
								self.options.finish(ui.item);
							}, 0);
						}
					}
				});

			input.change(function(){
				if (self.options.change){
					self.options.change($(this).val());
				}
			});

			input.keyup(function(event){
				if (self.options.change){
					self.options.change($(this).val());
				}
			});
			
			if(self.options.readonly){
				input.click(function(event){
					if (input.autocomplete("widget").is(":visible")) {
						input.autocomplete("close");
						return false;
					}
					input.autocomplete("search", "");
					return false;
				});
			}

			if (self.options.id){
				input.attr("id", self.options.id);
			}
			if(self.options.readonly){
				input.attr('readonly', 'readonly');
			}

			
			input.width(width - Fai.getCssInt(input, "border-left-width"));
			
			selector.width(width + Fai.getCssInt(input, "border-left-width") + Fai.getCssInt(input, "border-right-width"));
			
			if (self.options.name) {
				input.attr("name", self.options.name);
			} else {
				input.attr("name", self.options.id);
			}
			if (typeof self.options.initVal != "undefined"){
				input.val(self.options.initVal);
			}
			if(typeof self.options.initKey != "undefined"){
				$(self).data('key',self.options.initKey)
			} else {
				$(self).data('key','')
			}
			
			/*
			var resRoot = '';
			if (typeof Fai != "undefined" && typeof Fai.top._resRoot != 'undefined') {
				resRoot = Fai.top._resRoot;
			}
			*/
				
			var button = $("<div>&nbsp;</div>")
			.attr("tabIndex", -1)
			.css('outline','none')
			.insertAfter(input)
			.addClass("autoCompleteButton")
			.hover(
				function(){
					$(this).addClass("autoCompleteButtonHover");
				},
				function(){
					$(this).removeClass("autoCompleteButtonHover");
					$(this).removeClass("autoCompleteButtonDown");
				}
			)
			.mouseup(
				function(){
					//$(this).removeClass("autoCompleteButtonDown");
					//$(this).css("background","url(" + resRoot + "/image/comm/jquery/autocomplete/select_click.gif) no-repeat 0 1px");
				}
			)
			.mousedown(
				function(){
					$(this).removeClass("autoCompleteButtonHover");
					$(this).addClass("autoCompleteButtonDown");
					//$(this).css("background","url(" + resRoot + "/image/comm/jquery/autocomplete/select_up.gif) no-repeat 0 1px");
				}
			)
			.click(
				function() {
					// close if already visible
					if (input.autocomplete("widget").is(":visible")) {
						input.autocomplete("close");
						return false;
					}
					// pass empty string as value to search for, displaying all results
					input.autocomplete("search", "");
					input.focus();
					return false;
				}
			);

			var height = input.outerHeight();
			selector.css("position", "relative");
			selector.css("height", height);
			//Fai.logDbg(width + " " + button.width());
			//inut.width()可以代替width - Fai.getCssInt(input, "border-left-width")，但是IE678不兼容啊，没有办法啦，只有这样写
			var left = width - button.width() - (self.options.readonly ? 0 : 1);
			// 容器高度 - 按钮高度
			var posTop = input.position().top + ( height - button.height() ) / 2;	
			button.css("position", "absolute");
			button.css("left", left + "px");
			button.css("top", posTop + "px");

			if(!self.options.readonly){
				input.width(width - Fai.getCssInt(input, "border-left-width") - 20);
				input.css('padding-right',20+'px');
				button.css('background-color', '#ffffff').css('*width',17+'px');
			}

			//button ³õÊ¼»¯Ê±Òþ²Ø , input focusÊ±ÏÔÊ¾
			if(self.options.readonly){
				button.show();	
			}else{
				button.hide();
				input.focus(function(){
					button.show();
				})
			}
		},
		
		addSource: function(nOption){
			// var input = this.element.find(' > input[type=text]');
			var input = this.element.find('input');
			var tempSource = input.autocomplete( 'option', 'source' );
			var _has = false;
			$.each(tempSource, function( i, to ){
				if( to.key == nOption.key ){
					_has = true;
					tempSource.splice( i, 1, nOption );
				}
			});
			if( !_has ){
				tempSource.push( nOption );
			}
			input.autocomplete( 'option', 'source', tempSource );
		},

		setSource: function(source) {
			// var input = this.element.find(' > input[type=text]');
			var input = this.element.find('input');
			input.autocomplete('option','source',source);
		},
		
		getSource: function() {
			var input = this.element.find('input');
			return input.autocomplete( 'option', 'source' );
		},

		setInitVal: function(initVal, initKey) {
			// var input = this.element.find(' > input[type=text]');
			var input = this.element.find('input');
			input.val(initVal);
			$(this).data('key', initKey);
		},

		setInitKey: function(initKey) {
			// var input = this.element.find(' > input[type=text]');
			var input = this.element.find('input');
			var source = input.autocomplete( 'option', 'source');
			var initVal = '';
			$.each(source, function( i, to ){
				if( to.key == initKey ){
					initVal = to.label;
				}
			});
			input.val(initVal);
			$(this).data('key', initKey);
		},

		getVal: function() {
			// var input = this.element.find(' > input[type=text]');
			var input = this.element.find('input');
			return input.val();
		},

		getKey: function() {
			return $(this).data('key');
		},

		setKey: function( val ){
			$(this).data('key',val);
		}
		
	});
		
}(jQuery));