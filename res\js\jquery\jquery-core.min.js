!function(e,t){var n,r,i=e.document,o=e.location,a=e.navigator,s=e.jQ<PERSON>y,l=e.$,u=Array.prototype.push,c=Array.prototype.slice,f=Array.prototype.indexOf,p=Object.prototype.toString,d=Object.prototype.hasOwnProperty,h=String.prototype.trim,g=function e(t,r){return new e.fn.init(t,r,n)},m=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,y=/\S/,v=/\s+/,b=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,x=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,w=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,T=/^[\],:{}\s]*$/,N=/(?:^|:|,)(?:\s*\[)+/g,C=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,E=/"[^"\\\r\n]*"|true|false|null|-?(?:\d\d*\.|)\d+(?:[eE][\-+]?\d+|)/g,k=/^-ms-/,S=/-([\da-z])/gi,A=function(e,t){return(t+"").toUpperCase()},j=function e(){i.addEventListener?(i.removeEventListener("DOMContentLoaded",e,!1),g.ready()):"complete"===i.readyState&&(i.detachEvent("onreadystatechange",e),g.ready())},D={};g.fn=g.prototype={constructor:g,init:function(e,n,r){var o,a,s;if(!e)return this;if(e.nodeType)return this.context=this[0]=e,this.length=1,this;if("string"==typeof e){if(!(o="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:x.exec(e))||!o[1]&&n)return!n||n.jquery?(n||r).find(e):this.constructor(n).find(e);if(o[1])return s=(n=n instanceof g?n[0]:n)&&n.nodeType?n.ownerDocument||n:i,e=g.parseHTML(o[1],s,!0),w.test(o[1])&&g.isPlainObject(n)&&this.attr.call(e,n,!0),g.merge(this,e);if((a=i.getElementById(o[2]))&&a.parentNode){if(a.id!==o[2])return r.find(e);this.length=1,this[0]=a}return this.context=i,this.selector=e,this}return g.isFunction(e)?r.ready(e):(e.selector!==t&&(this.selector=e.selector,this.context=e.context),g.makeArray(e,this))},selector:"",jquery:"1.8.2",length:0,size:function(){return this.length},toArray:function(){return c.call(this)},get:function(e){return null==e?this.toArray():e<0?this[this.length+e]:this[e]},pushStack:function(e,t,n){var r=g.merge(this.constructor(),e);return r.prevObject=this,r.context=this.context,"find"===t?r.selector=this.selector+(this.selector?" ":"")+n:t&&(r.selector=this.selector+"."+t+"("+n+")"),r},each:function(e,t){return g.each(this,e,t)},ready:function(e){return g.ready.promise().done(e),this},eq:function(e){return-1===(e=+e)?this.slice(e):this.slice(e,e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(c.apply(this,arguments),"slice",c.call(arguments).join(","))},map:function(e){return this.pushStack(g.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:u,sort:[].sort,splice:[].splice},g.fn.init.prototype=g.fn,g.extend=g.fn.extend=function(){var e,n,r,i,o,a,s=arguments[0]||{},l=1,u=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[1]||{},l=2),"object"==typeof s||g.isFunction(s)||(s={}),u===l&&(s=this,--l);l<u;l++)if(null!=(e=arguments[l]))for(n in e)r=s[n],s!==(i=e[n])&&(c&&i&&(g.isPlainObject(i)||(o=g.isArray(i)))?(o?(o=!1,a=r&&g.isArray(r)?r:[]):a=r&&g.isPlainObject(r)?r:{},s[n]=g.extend(c,a,i)):i!==t&&(s[n]=i));return s},g.extend({noConflict:function(t){return e.$===g&&(e.$=l),t&&e.jQuery===g&&(e.jQuery=s),g},isReady:!1,readyWait:1,holdReady:function(e){e?g.readyWait++:g.ready(!0)},ready:function(e){if(!0===e?!--g.readyWait:!g.isReady){if(!i.body)return setTimeout(g.ready,1);g.isReady=!0,!0!==e&&--g.readyWait>0||(r.resolveWith(i,[g]),g.fn.trigger&&g(i).trigger("ready").off("ready"))}},isFunction:function(e){return"function"===g.type(e)},isArray:Array.isArray||function(e){return"array"===g.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return null==e?String(e):D[p.call(e)]||"object"},isPlainObject:function(e){if(!e||"object"!==g.type(e)||e.nodeType||g.isWindow(e))return!1;try{if(e.constructor&&!d.call(e,"constructor")&&!d.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(e){return!1}var n;for(n in e);return n===t||d.call(e,n)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw new Error(e)},parseHTML:function(e,t,n){var r;return e&&"string"==typeof e?("boolean"==typeof t&&(n=t,t=0),t=t||i,(r=w.exec(e))?[t.createElement(r[1])]:(r=g.buildFragment([e],t,n?null:[]),g.merge([],(r.cacheable?g.clone(r.fragment):r.fragment).childNodes))):null},parseJSON:function(t){return t&&"string"==typeof t?(t=g.trim(t),e.JSON&&e.JSON.parse?e.JSON.parse(t):T.test(t.replace(C,"@").replace(E,"]").replace(N,""))?new Function("return "+t)():void g.error("Invalid JSON: "+t)):null},parseXML:function(n){var r;if(!n||"string"!=typeof n)return null;try{e.DOMParser?r=(new DOMParser).parseFromString(n,"text/xml"):((r=new ActiveXObject("Microsoft.XMLDOM")).async="false",r.loadXML(n))}catch(e){r=t}return r&&r.documentElement&&!r.getElementsByTagName("parsererror").length||g.error("Invalid XML: "+n),r},noop:function(){},globalEval:function(t){t&&y.test(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(k,"ms-").replace(S,A)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,n,r){var i,o=0,a=e.length,s=a===t||g.isFunction(e);if(r)if(s){for(i in e)if(!1===n.apply(e[i],r))break}else for(;o<a&&!1!==n.apply(e[o++],r););else if(s){for(i in e)if(!1===n.call(e[i],i,e[i]))break}else for(;o<a&&!1!==n.call(e[o],o,e[o++]););return e},forEach:function(e,t){return this.each(e,function(e,n){t.call(this,n,e)})},trim:h&&!h.call("\ufeff ")?function(e){return null==e?"":h.call(e)}:function(e){return null==e?"":(e+"").replace(b,"")},makeArray:function(e,t){var n,r=t||[];return null!=e&&(n=g.type(e),null==e.length||"string"===n||"function"===n||"regexp"===n||g.isWindow(e)?u.call(r,e):g.merge(r,e)),r},inArray:function(e,t,n){var r;if(t){if(f)return f.call(t,e,n);for(r=t.length,n=n?n<0?Math.max(0,r+n):n:0;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,n){var r=n.length,i=e.length,o=0;if("number"==typeof r)for(;o<r;o++)e[i++]=n[o];else for(;n[o]!==t;)e[i++]=n[o++];return e.length=i,e},grep:function(e,t,n){var r=[],i=0,o=e.length;for(n=!!n;i<o;i++)n!==!!t(e[i],i)&&r.push(e[i]);return r},map:function(e,n,r){var i,o,a=[],s=0,l=e.length;if(e instanceof g||l!==t&&"number"==typeof l&&(l>0&&e[0]&&e[l-1]||0===l||g.isArray(e)))for(;s<l;s++)null!=(i=n(e[s],s,r))&&(a[a.length]=i);else for(o in e)null!=(i=n(e[o],o,r))&&(a[a.length]=i);return a.concat.apply([],a)},guid:1,proxy:function(e,n){var r,i,o;return"string"==typeof n&&(r=e[n],n=e,e=r),g.isFunction(e)?(i=c.call(arguments,2),o=function(){return e.apply(n,i.concat(c.call(arguments)))},o.guid=e.guid=e.guid||g.guid++,o):t},access:function(e,n,r,i,o,a,s){var l,u=null==r,c=0,f=e.length;if(r&&"object"==typeof r){for(c in r)g.access(e,n,c,r[c],1,a,i);o=1}else if(i!==t){if(l=s===t&&g.isFunction(i),u&&(l?(l=n,n=function(e,t,n){return l.call(g(e),n)}):(n.call(e,i),n=null)),n)for(;c<f;c++)n(e[c],r,l?i.call(e[c],c,n(e[c],r)):i,s);o=1}return o?e:u?n.call(e):f?n(e[0],r):a},now:function(){return(new Date).getTime()}}),g.ready.promise=function(t){if(!r)if(r=g.Deferred(),"complete"===i.readyState)setTimeout(g.ready,1);else if(i.addEventListener)i.addEventListener("DOMContentLoaded",j,!1),e.addEventListener("load",g.ready,!1);else{i.attachEvent("onreadystatechange",j),e.attachEvent("onload",g.ready);var n=!1;try{n=null==e.frameElement&&i.documentElement}catch(e){}n&&n.doScroll&&function e(){if(!g.isReady){try{n.doScroll("left")}catch(t){return setTimeout(e,50)}g.ready()}}()}return r.promise(t)},g.each("Boolean Number String Function Array Date RegExp Object".split(" "),function(e,t){D["[object "+t+"]"]=t.toLowerCase()}),n=g(i);var L={};g.Callbacks=function(e){e="string"==typeof e?L[e]||function(e){var t=L[e]={};return g.each(e.split(v),function(e,n){t[n]=!0}),t}(e):g.extend({},e);var n,r,i,o,a,s,l=[],u=!e.once&&[],c=function t(c){for(n=e.memory&&c,r=!0,s=o||0,o=0,a=l.length,i=!0;l&&s<a;s++)if(!1===l[s].apply(c[0],c[1])&&e.stopOnFalse){n=!1;break}i=!1,l&&(u?u.length&&t(u.shift()):n?l=[]:f.disable())},f={add:function(){if(l){var t=l.length;!function t(n){g.each(n,function(n,r){var i=g.type(r);"function"!==i||e.unique&&f.has(r)?r&&r.length&&"string"!==i&&t(r):l.push(r)})}(arguments),i?a=l.length:n&&(o=t,c(n))}return this},remove:function(){return l&&g.each(arguments,function(e,t){for(var n;(n=g.inArray(t,l,n))>-1;)l.splice(n,1),i&&(n<=a&&a--,n<=s&&s--)}),this},has:function(e){return g.inArray(e,l)>-1},empty:function(){return l=[],this},disable:function(){return l=u=n=t,this},disabled:function(){return!l},lock:function(){return u=t,n||f.disable(),this},locked:function(){return!u},fireWith:function(e,t){return t=[e,(t=t||[]).slice?t.slice():t],!l||r&&!u||(i?u.push(t):c(t)),this},fire:function(){return f.fireWith(this,arguments),this},fired:function(){return!!r}};return f},g.extend({Deferred:function(e){var t=[["resolve","done",g.Callbacks("once memory"),"resolved"],["reject","fail",g.Callbacks("once memory"),"rejected"],["notify","progress",g.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return g.Deferred(function(n){g.each(t,function(t,r){var o=r[0],a=e[t];i[r[1]](g.isFunction(a)?function(){var e=a.apply(this,arguments);e&&g.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o+"With"](this===i?n:this,[e])}:n[o])}),e=null}).promise()},promise:function(e){return null!=e?g.extend(e,r):r}},i={};if(r.pipe=r.then,g.each(t,function(e,o){var a=o[2],s=o[3];r[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),i[o[0]]=a.fire,i[o[0]+"With"]=a.fireWith}),r.promise(i),"string"==typeof e){if(i[e]){var o=c.call(arguments);i[o.shift()].apply(i,o)}}else e&&e.call(i,i);return i},when:function(e){var t,n,r,i=0,o=c.call(arguments),a=o.length,s=1!==a||e&&g.isFunction(e.promise)?a:0,l=1===s?e:g.Deferred(),u=function(e,n,r){return function(i){n[e]=this,r[e]=arguments.length>1?c.call(arguments):i,r===t?l.notifyWith(n,r):--s||l.resolveWith(n,r)}};if(a>1)for(t=new Array(a),n=new Array(a),r=new Array(a);i<a;i++)o[i]&&g.isFunction(o[i].promise)?o[i].promise().done(u(i,r,o)).fail(l.reject).progress(u(i,n,t)):--s;return s||l.resolveWith(r,o),l.promise()}}),g.support=function(){var t,n,r,o,a,s,l,u,c,f,p,d=i.createElement("div");if(d.setAttribute("className","t"),d.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",n=d.getElementsByTagName("*"),(r=d.getElementsByTagName("a")[0]).style.cssText="top:1px;float:left;opacity:.5",!n||!n.length)return{};a=(o=i.createElement("select")).appendChild(i.createElement("option")),s=d.getElementsByTagName("input")[0],t={leadingWhitespace:3===d.firstChild.nodeType,tbody:!d.getElementsByTagName("tbody").length,htmlSerialize:!!d.getElementsByTagName("link").length,style:/top/.test(r.getAttribute("style")),hrefNormalized:"/a"===r.getAttribute("href"),opacity:/^0.5/.test(r.style.opacity),cssFloat:!!r.style.cssFloat,checkOn:"on"===s.value,optSelected:a.selected,getSetAttribute:"t"!==d.className,enctype:!!i.createElement("form").enctype,html5Clone:"<:nav></:nav>"!==i.createElement("nav").cloneNode(!0).outerHTML,boxModel:"CSS1Compat"===i.compatMode,submitBubbles:!0,changeBubbles:!0,focusinBubbles:!1,deleteExpando:!0,noCloneEvent:!0,inlineBlockNeedsLayout:!1,shrinkWrapBlocks:!1,reliableMarginRight:!0,boxSizingReliable:!0,pixelPosition:!1},s.checked=!0,t.noCloneChecked=s.cloneNode(!0).checked,o.disabled=!0,t.optDisabled=!a.disabled;try{delete d.test}catch(e){t.deleteExpando=!1}if(!d.addEventListener&&d.attachEvent&&d.fireEvent&&(d.attachEvent("onclick",p=function(){t.noCloneEvent=!1}),d.cloneNode(!0).fireEvent("onclick"),d.detachEvent("onclick",p)),(s=i.createElement("input")).value="t",s.setAttribute("type","radio"),t.radioValue="t"===s.value,s.setAttribute("checked","checked"),s.setAttribute("name","t"),d.appendChild(s),(l=i.createDocumentFragment()).appendChild(d.lastChild),t.checkClone=l.cloneNode(!0).cloneNode(!0).lastChild.checked,t.appendChecked=s.checked,l.removeChild(s),l.appendChild(d),d.attachEvent)for(c in{submit:!0,change:!0,focusin:!0})(f=(u="on"+c)in d)||(d.setAttribute(u,"return;"),f="function"==typeof d[u]),t[c+"Bubbles"]=f;return g(function(){var n,r,o,a,s="padding:0;margin:0;border:0;display:block;overflow:hidden;",l=i.getElementsByTagName("body")[0];l&&((n=i.createElement("div")).style.cssText="visibility:hidden;border:0;width:0;height:0;position:static;top:0;margin-top:1px",l.insertBefore(n,l.firstChild),r=i.createElement("div"),n.appendChild(r),r.innerHTML="<table><tr><td></td><td>t</td></tr></table>",(o=r.getElementsByTagName("td"))[0].style.cssText="padding:0;margin:0;border:0;display:none",f=0===o[0].offsetHeight,o[0].style.display="",o[1].style.display="none",t.reliableHiddenOffsets=f&&0===o[0].offsetHeight,r.innerHTML="",r.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;",t.boxSizing=4===r.offsetWidth,t.doesNotIncludeMarginInBodyOffset=1!==l.offsetTop,e.getComputedStyle&&(t.pixelPosition="1%"!==(e.getComputedStyle(r,null)||{}).top,t.boxSizingReliable="4px"===(e.getComputedStyle(r,null)||{width:"4px"}).width,(a=i.createElement("div")).style.cssText=r.style.cssText=s,a.style.marginRight=a.style.width="0",r.style.width="1px",r.appendChild(a),t.reliableMarginRight=!parseFloat((e.getComputedStyle(a,null)||{}).marginRight)),void 0!==r.style.zoom&&(r.innerHTML="",r.style.cssText=s+"width:1px;padding:1px;display:inline;zoom:1",t.inlineBlockNeedsLayout=3===r.offsetWidth,r.style.display="block",r.style.overflow="visible",r.innerHTML="<div></div>",r.firstChild.style.width="5px",t.shrinkWrapBlocks=3!==r.offsetWidth,n.style.zoom=1),l.removeChild(n),n=r=o=a=null)}),l.removeChild(d),n=r=o=a=s=l=d=null,t}();var H=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,F=/([A-Z])/g;function M(e,n,r){if(r===t&&1===e.nodeType){var i="data-"+n.replace(F,"-$1").toLowerCase();if("string"==typeof(r=e.getAttribute(i))){try{r="true"===r||"false"!==r&&("null"===r?null:+r+""===r?+r:H.test(r)?g.parseJSON(r):r)}catch(e){}g.data(e,n,r)}else r=t}return r}function O(e){var t;for(t in e)if(("data"!==t||!g.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}g.extend({cache:{},deletedIds:[],uuid:0,expando:"jQuery"+(g.fn.jquery+Math.random()).replace(/\D/g,""),noData:{embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:!0},hasData:function(e){return!!(e=e.nodeType?g.cache[e[g.expando]]:e[g.expando])&&!O(e)},data:function(e,n,r,i){if(g.acceptData(e)){var o,a,s=g.expando,l="string"==typeof n,u=e.nodeType,c=u?g.cache:e,f=u?e[s]:e[s]&&s;if(f&&c[f]&&(i||c[f].data)||!l||r!==t)return f||(u?e[s]=f=g.deletedIds.pop()||g.guid++:f=s),c[f]||(c[f]={},u||(c[f].toJSON=g.noop)),"object"!=typeof n&&"function"!=typeof n||(i?c[f]=g.extend(c[f],n):c[f].data=g.extend(c[f].data,n)),o=c[f],i||(o.data||(o.data={}),o=o.data),r!==t&&(o[g.camelCase(n)]=r),l?null==(a=o[n])&&(a=o[g.camelCase(n)]):a=o,a}},removeData:function(e,t,n){if(g.acceptData(e)){var r,i,o,a=e.nodeType,s=a?g.cache:e,l=a?e[g.expando]:g.expando;if(s[l]){if(t&&(r=n?s[l]:s[l].data)){g.isArray(t)||(t=t in r?[t]:(t=g.camelCase(t))in r?[t]:t.split(" "));for(i=0,o=t.length;i<o;i++)delete r[t[i]];if(!(n?O:g.isEmptyObject)(r))return}(n||(delete s[l].data,O(s[l])))&&(a?g.cleanData([e],!0):g.support.deleteExpando||s!=s.window?delete s[l]:s[l]=null)}}},_data:function(e,t,n){return g.data(e,t,n,!0)},acceptData:function(e){var t=e.nodeName&&g.noData[e.nodeName.toLowerCase()];return!t||!0!==t&&e.getAttribute("classid")===t}}),g.fn.extend({data:function(e,n){var r,i,o,a,s,l=this[0],u=0,c=null;if(e===t){if(this.length&&(c=g.data(l),1===l.nodeType&&!g._data(l,"parsedAttrs"))){for(s=(o=l.attributes).length;u<s;u++)(a=o[u].name).indexOf("data-")||(a=g.camelCase(a.substring(5)),M(l,a,c[a]));g._data(l,"parsedAttrs",!0)}return c}return"object"==typeof e?this.each(function(){g.data(this,e)}):((r=e.split(".",2))[1]=r[1]?"."+r[1]:"",i=r[1]+"!",g.access(this,function(n){if(n===t)return c=this.triggerHandler("getData"+i,[r[0]]),c===t&&l&&(c=g.data(l,e),c=M(l,e,c)),c===t&&r[1]?this.data(r[0]):c;r[1]=n,this.each(function(){var t=g(this);t.triggerHandler("setData"+i,r),g.data(this,e,n),t.triggerHandler("changeData"+i,r)})},null,n,arguments.length>1,null,!1))},removeData:function(e){return this.each(function(){g.removeData(this,e)})}}),g.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=g._data(e,t),n&&(!r||g.isArray(n)?r=g._data(e,t,g.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=g.queue(e,t),r=n.length,i=n.shift(),o=g._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){g.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return g._data(e,n)||g._data(e,n,{empty:g.Callbacks("once memory").add(function(){g.removeData(e,t+"queue",!0),g.removeData(e,n,!0)})})}}),g.fn.extend({queue:function(e,n){var r=2;return"string"!=typeof e&&(n=e,e="fx",r--),arguments.length<r?g.queue(this[0],e):n===t?this:this.each(function(){var t=g.queue(this,e,n);g._queueHooks(this,e),"fx"===e&&"inprogress"!==t[0]&&g.dequeue(this,e)})},dequeue:function(e){return this.each(function(){g.dequeue(this,e)})},delay:function(e,t){return e=g.fx&&g.fx.speeds[e]||e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,n){var r,i=1,o=g.Deferred(),a=this,s=this.length,l=function(){--i||o.resolveWith(a,[a])};for("string"!=typeof e&&(n=e,e=t),e=e||"fx";s--;)(r=g._data(a[s],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(l));return l(),o.promise(n)}});var _,q,B,W=/[\t\r\n]/g,P=/\r/g,R=/^(?:button|input)$/i,I=/^(?:button|input|object|select|textarea)$/i,z=/^a(?:rea|)$/i,X=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,U=g.support.getSetAttribute;g.fn.extend({attr:function(e,t){return g.access(this,g.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){g.removeAttr(this,e)})},prop:function(e,t){return g.access(this,g.prop,e,t,arguments.length>1)},removeProp:function(e){return e=g.propFix[e]||e,this.each(function(){try{this[e]=t,delete this[e]}catch(e){}})},addClass:function(e){var t,n,r,i,o,a,s;if(g.isFunction(e))return this.each(function(t){g(this).addClass(e.call(this,t,this.className))});if(e&&"string"==typeof e)for(t=e.split(v),n=0,r=this.length;n<r;n++)if(1===(i=this[n]).nodeType)if(i.className||1!==t.length){for(o=" "+i.className+" ",a=0,s=t.length;a<s;a++)o.indexOf(" "+t[a]+" ")<0&&(o+=t[a]+" ");i.className=g.trim(o)}else i.className=e;return this},removeClass:function(e){var n,r,i,o,a,s,l;if(g.isFunction(e))return this.each(function(t){g(this).removeClass(e.call(this,t,this.className))});if(e&&"string"==typeof e||e===t)for(n=(e||"").split(v),s=0,l=this.length;s<l;s++)if(1===(i=this[s]).nodeType&&i.className){for(r=(" "+i.className+" ").replace(W," "),o=0,a=n.length;o<a;o++)for(;r.indexOf(" "+n[o]+" ")>=0;)r=r.replace(" "+n[o]+" "," ");i.className=e?g.trim(r):""}return this},toggleClass:function(e,t){var n=typeof e,r="boolean"==typeof t;return g.isFunction(e)?this.each(function(n){g(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if("string"===n)for(var i,o=0,a=g(this),s=t,l=e.split(v);i=l[o++];)s=r?s:!a.hasClass(i),a[s?"addClass":"removeClass"](i);else"undefined"!==n&&"boolean"!==n||(this.className&&g._data(this,"__className__",this.className),this.className=this.className||!1===e?"":g._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(W," ").indexOf(t)>=0)return!0;return!1},val:function(e){var n,r,i,o=this[0];return arguments.length?(i=g.isFunction(e),this.each(function(r){var o,a=g(this);1===this.nodeType&&(null==(o=i?e.call(this,r,a.val()):e)?o="":"number"==typeof o?o+="":g.isArray(o)&&(o=g.map(o,function(e){return null==e?"":e+""})),(n=g.valHooks[this.type]||g.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&n.set(this,o,"value")!==t||(this.value=o))})):o?(n=g.valHooks[o.type]||g.valHooks[o.nodeName.toLowerCase()])&&"get"in n&&(r=n.get(o,"value"))!==t?r:"string"==typeof(r=o.value)?r.replace(P,""):null==r?"":r:void 0}}),g.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){var t,n,r,i,o=e.selectedIndex,a=[],s=e.options,l="select-one"===e.type;if(o<0)return null;for(n=l?o:0,r=l?o+1:s.length;n<r;n++)if((i=s[n]).selected&&(g.support.optDisabled?!i.disabled:null===i.getAttribute("disabled"))&&(!i.parentNode.disabled||!g.nodeName(i.parentNode,"optgroup"))){if(t=g(i).val(),l)return t;a.push(t)}return l&&!a.length&&s.length?g(s[o]).val():a},set:function(e,t){var n=g.makeArray(t);return g(e).find("option").each(function(){this.selected=g.inArray(g(this).val(),n)>=0}),n.length||(e.selectedIndex=-1),n}}},attrFn:{},attr:function(e,n,r,i){var o,a,s,l=e.nodeType;if(e&&3!==l&&8!==l&&2!==l)return i&&g.isFunction(g.fn[n])?g(e)[n](r):void 0===e.getAttribute?g.prop(e,n,r):((s=1!==l||!g.isXMLDoc(e))&&(n=n.toLowerCase(),a=g.attrHooks[n]||(X.test(n)?q:_)),r!==t?null===r?void g.removeAttr(e,n):a&&"set"in a&&s&&(o=a.set(e,r,n))!==t?o:(e.setAttribute(n,r+""),r):a&&"get"in a&&s&&null!==(o=a.get(e,n))?o:null===(o=e.getAttribute(n))?t:o)},removeAttr:function(e,t){var n,r,i,o,a=0;if(t&&1===e.nodeType)for(r=t.split(v);a<r.length;a++)(i=r[a])&&(n=g.propFix[i]||i,(o=X.test(i))||g.attr(e,i,""),e.removeAttribute(U?i:n),o&&n in e&&(e[n]=!1))},attrHooks:{type:{set:function(e,t){if(R.test(e.nodeName)&&e.parentNode)g.error("type property can't be changed");else if(!g.support.radioValue&&"radio"===t&&g.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}},value:{get:function(e,t){return _&&g.nodeName(e,"button")?_.get(e,t):t in e?e.value:null},set:function(e,t,n){if(_&&g.nodeName(e,"button"))return _.set(e,t,n);e.value=t}}},propFix:{tabindex:"tabIndex",readonly:"readOnly",for:"htmlFor",class:"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(e,n,r){var i,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return(1!==a||!g.isXMLDoc(e))&&(n=g.propFix[n]||n,o=g.propHooks[n]),r!==t?o&&"set"in o&&(i=o.set(e,r,n))!==t?i:e[n]=r:o&&"get"in o&&null!==(i=o.get(e,n))?i:e[n]},propHooks:{tabIndex:{get:function(e){var n=e.getAttributeNode("tabindex");return n&&n.specified?parseInt(n.value,10):I.test(e.nodeName)||z.test(e.nodeName)&&e.href?0:t}}}}),q={get:function(e,n){var r,i=g.prop(e,n);return!0===i||"boolean"!=typeof i&&(r=e.getAttributeNode(n))&&!1!==r.nodeValue?n.toLowerCase():t},set:function(e,t,n){var r;return!1===t?g.removeAttr(e,n):((r=g.propFix[n]||n)in e&&(e[r]=!0),e.setAttribute(n,n.toLowerCase())),n}},U||(B={name:!0,id:!0,coords:!0},_=g.valHooks.button={get:function(e,n){var r;return(r=e.getAttributeNode(n))&&(B[n]?""!==r.value:r.specified)?r.value:t},set:function(e,t,n){var r=e.getAttributeNode(n);return r||(r=i.createAttribute(n),e.setAttributeNode(r)),r.value=t+""}},g.each(["width","height"],function(e,t){g.attrHooks[t]=g.extend(g.attrHooks[t],{set:function(e,n){if(""===n)return e.setAttribute(t,"auto"),n}})}),g.attrHooks.contenteditable={get:_.get,set:function(e,t,n){""===t&&(t="false"),_.set(e,t,n)}}),g.support.hrefNormalized||g.each(["href","src","width","height"],function(e,n){g.attrHooks[n]=g.extend(g.attrHooks[n],{get:function(e){var r=e.getAttribute(n,2);return null===r?t:r}})}),g.support.style||(g.attrHooks.style={get:function(e){return e.style.cssText.toLowerCase()||t},set:function(e,t){return e.style.cssText=t+""}}),g.support.optSelected||(g.propHooks.selected=g.extend(g.propHooks.selected,{get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}})),g.support.enctype||(g.propFix.enctype="encoding"),g.support.checkOn||g.each(["radio","checkbox"],function(){g.valHooks[this]={get:function(e){return null===e.getAttribute("value")?"on":e.value}}}),g.each(["radio","checkbox"],function(){g.valHooks[this]=g.extend(g.valHooks[this],{set:function(e,t){if(g.isArray(t))return e.checked=g.inArray(g(e).val(),t)>=0}})});var Y=/^(?:textarea|input|select)$/i,J=/^([^\.]*|)(?:\.(.+)|)$/,V=/(?:^|\s)hover(\.\S+|)\b/,K=/^key/,G=/^(?:mouse|contextmenu)|click/,Q=/^(?:focusinfocus|focusoutblur)$/,Z=function(e){return g.event.special.hover?e:e.replace(V,"mouseenter$1 mouseleave$1")};function ee(){return!1}function te(){return!0}g.event={add:function(e,n,r,i,o){var a,s,l,u,c,f,p,d,h,m,y;if(3!==e.nodeType&&8!==e.nodeType&&n&&r&&(a=g._data(e))){for(r.handler&&(r=(h=r).handler,o=h.selector),r.guid||(r.guid=g.guid++),(l=a.events)||(a.events=l={}),(s=a.handle)||(a.handle=s=function(e){return void 0===g||e&&g.event.triggered===e.type?t:g.event.dispatch.apply(s.elem,arguments)},s.elem=e),n=g.trim(Z(n)).split(" "),u=0;u<n.length;u++)f=(c=J.exec(n[u])||[])[1],p=(c[2]||"").split(".").sort(),y=g.event.special[f]||{},f=(o?y.delegateType:y.bindType)||f,y=g.event.special[f]||{},d=g.extend({type:f,origType:c[1],data:i,handler:r,guid:r.guid,selector:o,needsContext:o&&g.expr.match.needsContext.test(o),namespace:p.join(".")},h),(m=l[f])||((m=l[f]=[]).delegateCount=0,y.setup&&!1!==y.setup.call(e,i,p,s)||(e.addEventListener?e.addEventListener(f,s,!1):e.attachEvent&&e.attachEvent("on"+f,s))),y.add&&(y.add.call(e,d),d.handler.guid||(d.handler.guid=r.guid)),o?m.splice(m.delegateCount++,0,d):m.push(d),g.event.global[f]=!0;e=null}},global:{},remove:function(e,t,n,r,i){var o,a,s,l,u,c,f,p,d,h,m,y=g.hasData(e)&&g._data(e);if(y&&(p=y.events)){for(t=g.trim(Z(t||"")).split(" "),o=0;o<t.length;o++)if(s=l=(a=J.exec(t[o])||[])[1],u=a[2],s){for(d=g.event.special[s]||{},c=(h=p[s=(r?d.delegateType:d.bindType)||s]||[]).length,u=u?new RegExp("(^|\\.)"+u.split(".").sort().join("\\.(?:.*\\.|)")+"(\\.|$)"):null,f=0;f<h.length;f++)m=h[f],!i&&l!==m.origType||n&&n.guid!==m.guid||u&&!u.test(m.namespace)||r&&r!==m.selector&&("**"!==r||!m.selector)||(h.splice(f--,1),m.selector&&h.delegateCount--,d.remove&&d.remove.call(e,m));0===h.length&&c!==h.length&&(d.teardown&&!1!==d.teardown.call(e,u,y.handle)||g.removeEvent(e,s,y.handle),delete p[s])}else for(s in p)g.event.remove(e,s+t[o],n,r,!0);g.isEmptyObject(p)&&(delete y.handle,g.removeData(e,"events",!0))}},customEvent:{getData:!0,setData:!0,changeData:!0},trigger:function(n,r,o,a){if(!o||3!==o.nodeType&&8!==o.nodeType){var s,l,u,c,f,p,d,h,m,y,v=n.type||n,b=[];if(!Q.test(v+g.event.triggered)&&(v.indexOf("!")>=0&&(v=v.slice(0,-1),l=!0),v.indexOf(".")>=0&&(b=v.split("."),v=b.shift(),b.sort()),o&&!g.event.customEvent[v]||g.event.global[v]))if((n="object"==typeof n?n[g.expando]?n:new g.Event(v,n):new g.Event(v)).type=v,n.isTrigger=!0,n.exclusive=l,n.namespace=b.join("."),n.namespace_re=n.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,p=v.indexOf(":")<0?"on"+v:"",o){if(n.result=t,n.target||(n.target=o),(r=null!=r?g.makeArray(r):[]).unshift(n),!(d=g.event.special[v]||{}).trigger||!1!==d.trigger.apply(o,r)){if(m=[[o,d.bindType||v]],!a&&!d.noBubble&&!g.isWindow(o)){for(y=d.delegateType||v,c=Q.test(y+v)?o:o.parentNode,f=o;c;c=c.parentNode)m.push([c,y]),f=c;f===(o.ownerDocument||i)&&m.push([f.defaultView||f.parentWindow||e,y])}for(u=0;u<m.length&&!n.isPropagationStopped();u++)c=m[u][0],n.type=m[u][1],(h=(g._data(c,"events")||{})[n.type]&&g._data(c,"handle"))&&h.apply(c,r),(h=p&&c[p])&&g.acceptData(c)&&h.apply&&!1===h.apply(c,r)&&n.preventDefault();return n.type=v,a||n.isDefaultPrevented()||d._default&&!1!==d._default.apply(o.ownerDocument,r)||"click"===v&&g.nodeName(o,"a")||!g.acceptData(o)||p&&o[v]&&("focus"!==v&&"blur"!==v||0!==n.target.offsetWidth)&&!g.isWindow(o)&&((f=o[p])&&(o[p]=null),g.event.triggered=v,o[v](),g.event.triggered=t,f&&(o[p]=f)),n.result}}else for(u in s=g.cache)s[u].events&&s[u].events[v]&&g.event.trigger(n,r,s[u].handle.elem,!0)}},dispatch:function(n){n=g.event.fix(n||e.event);var r,i,o,a,s,l,u,f,p,d=(g._data(this,"events")||{})[n.type]||[],h=d.delegateCount,m=c.call(arguments),y=!n.exclusive&&!n.namespace,v=g.event.special[n.type]||{},b=[];if(m[0]=n,n.delegateTarget=this,!v.preDispatch||!1!==v.preDispatch.call(this,n)){if(h&&(!n.button||"click"!==n.type))for(o=n.target;o!=this;o=o.parentNode||this)if(!0!==o.disabled||"click"!==n.type){for(s={},u=[],r=0;r<h;r++)s[p=(f=d[r]).selector]===t&&(s[p]=f.needsContext?g(p,this).index(o)>=0:g.find(p,this,null,[o]).length),s[p]&&u.push(f);u.length&&b.push({elem:o,matches:u})}for(d.length>h&&b.push({elem:this,matches:d.slice(h)}),r=0;r<b.length&&!n.isPropagationStopped();r++)for(l=b[r],n.currentTarget=l.elem,i=0;i<l.matches.length&&!n.isImmediatePropagationStopped();i++)f=l.matches[i],(y||!n.namespace&&!f.namespace||n.namespace_re&&n.namespace_re.test(f.namespace))&&(n.data=f.data,n.handleObj=f,(a=((g.event.special[f.origType]||{}).handle||f.handler).apply(l.elem,m))!==t&&(n.result=a,!1===a&&(n.preventDefault(),n.stopPropagation())));return v.postDispatch&&v.postDispatch.call(this,n),n.result}},props:"attrChange attrName relatedNode srcElement altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,n){var r,o,a,s=n.button,l=n.fromElement;return null==e.pageX&&null!=n.clientX&&(o=(r=e.target.ownerDocument||i).documentElement,a=r.body,e.pageX=n.clientX+(o&&o.scrollLeft||a&&a.scrollLeft||0)-(o&&o.clientLeft||a&&a.clientLeft||0),e.pageY=n.clientY+(o&&o.scrollTop||a&&a.scrollTop||0)-(o&&o.clientTop||a&&a.clientTop||0)),!e.relatedTarget&&l&&(e.relatedTarget=l===e.target?n.toElement:l),e.which||s===t||(e.which=1&s?1:2&s?3:4&s?2:0),e}},fix:function(e){if(e[g.expando])return e;var t,n,r=e,o=g.event.fixHooks[e.type]||{},a=o.props?this.props.concat(o.props):this.props;for(e=g.Event(r),t=a.length;t;)e[n=a[--t]]=r[n];return e.target||(e.target=r.srcElement||i),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,o.filter?o.filter(e,r):e},special:{load:{noBubble:!0},focus:{delegateType:"focusin"},blur:{delegateType:"focusout"},beforeunload:{setup:function(e,t,n){g.isWindow(this)&&(this.onbeforeunload=n)},teardown:function(e,t){this.onbeforeunload===t&&(this.onbeforeunload=null)}}},simulate:function(e,t,n,r){var i=g.extend(new g.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?g.event.trigger(i,null,t):g.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},g.event.handle=g.event.dispatch,g.removeEvent=i.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(void 0===e[r]&&(e[r]=null),e.detachEvent(r,n))},g.Event=function(e,t){if(!(this instanceof g.Event))return new g.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||!1===e.returnValue||e.getPreventDefault&&e.getPreventDefault()?te:ee):this.type=e,t&&g.extend(this,t),this.timeStamp=e&&e.timeStamp||g.now(),this[g.expando]=!0},g.Event.prototype={preventDefault:function(){this.isDefaultPrevented=te;var e=this.originalEvent;e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){this.isPropagationStopped=te;var e=this.originalEvent;e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=te,this.stopPropagation()},isDefaultPrevented:ee,isPropagationStopped:ee,isImmediatePropagationStopped:ee},g.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){g.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;i.selector;return r&&(r===this||g.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}}),g.support.submitBubbles||(g.event.special.submit={setup:function(){if(g.nodeName(this,"form"))return!1;g.event.add(this,"click._submit keypress._submit",function(e){var n=e.target,r=g.nodeName(n,"input")||g.nodeName(n,"button")?n.form:t;r&&!g._data(r,"_submit_attached")&&(g.event.add(r,"submit._submit",function(e){e._submit_bubble=!0}),g._data(r,"_submit_attached",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&g.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(g.nodeName(this,"form"))return!1;g.event.remove(this,"._submit")}}),g.support.changeBubbles||(g.event.special.change={setup:function(){if(Y.test(this.nodeName))return"checkbox"!==this.type&&"radio"!==this.type||(g.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),g.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),g.event.simulate("change",this,e,!0)})),!1;g.event.add(this,"beforeactivate._change",function(e){var t=e.target;Y.test(t.nodeName)&&!g._data(t,"_change_attached")&&(g.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||g.event.simulate("change",this.parentNode,e,!0)}),g._data(t,"_change_attached",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type)return e.handleObj.handler.apply(this,arguments)},teardown:function(){return g.event.remove(this,"._change"),!Y.test(this.nodeName)}}),g.support.focusinBubbles||g.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){g.event.simulate(t,e.target,g.event.fix(e),!0)};g.event.special[t]={setup:function(){0==n++&&i.addEventListener(e,r,!0)},teardown:function(){0==--n&&i.removeEventListener(e,r,!0)}}}),g.fn.extend({on:function(e,n,r,i,o){var a,s;if("object"==typeof e){for(s in"string"!=typeof n&&(r=r||n,n=t),e)this.on(s,n,r,e[s],o);return this}if(null==r&&null==i?(i=n,r=n=t):null==i&&("string"==typeof n?(i=r,r=t):(i=r,r=n,n=t)),!1===i)i=ee;else if(!i)return this;return 1===o&&(a=i,(i=function(e){return g().off(e),a.apply(this,arguments)}).guid=a.guid||(a.guid=g.guid++)),this.each(function(){g.event.add(this,e,i,r,n)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,n,r){var i,o;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,g(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(o in e)this.off(o,n,e[o]);return this}return!1!==n&&"function"!=typeof n||(r=n,n=t),!1===r&&(r=ee),this.each(function(){g.event.remove(this,e,r,n)})},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},live:function(e,t,n){return g(this.context).on(e,this.selector,t,n),this},die:function(e,t){return g(this.context).off(e,this.selector||"**",t),this},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},trigger:function(e,t){return this.each(function(){g.event.trigger(e,t,this)})},triggerHandler:function(e,t){if(this[0])return g.event.trigger(e,t,this[0],!0)},toggle:function(e){var t=arguments,n=e.guid||g.guid++,r=0,i=function(n){var i=(g._data(this,"lastToggle"+e.guid)||0)%r;return g._data(this,"lastToggle"+e.guid,i+1),n.preventDefault(),t[i].apply(this,arguments)||!1};for(i.guid=n;r<t.length;)t[r++].guid=n;return this.click(i)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),g.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){g.fn[t]=function(e,n){return null==n&&(n=e,e=null),arguments.length>0?this.on(t,null,e,n):this.trigger(t)},K.test(t)&&(g.event.fixHooks[t]=g.event.keyHooks),G.test(t)&&(g.event.fixHooks[t]=g.event.mouseHooks)}),function(e,t){var n,r,i,o,a,s,l,u,c,f,p,d,h,m,y,v,b,x,w=("sizcache"+Math.random()).replace(".",""),T=String,N=e.document,C=N.documentElement,E=0,k=0,S=[].pop,A=[].push,j=[].slice,D=[].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},L=function(e,t){return e[w]=null==t||t,e},H=function(){var e={},t=[];return L(function(n,r){return t.push(n)>i.cacheLength&&delete e[t.shift()],e[n]=r},e)},F=H(),M=H(),O=H(),_="[\\x20\\t\\r\\n\\f]",q="(?:\\\\.|[-\\w]|[^\\x00-\\xa0])+",B=q.replace("w","w#"),W="\\["+_+"*("+q+")"+_+"*(?:([*^$|!~]?=)"+_+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+B+")|)|)"+_+"*\\]",P=":("+q+")(?:\\((?:(['\"])((?:\\\\.|[^\\\\])*?)\\2|([^()[\\]]*|(?:(?:"+W+")|[^:]|\\\\.)*|.*))\\)|)",R=":(even|odd|eq|gt|lt|nth|first|last)(?:\\("+_+"*((?:-\\d)?\\d*)"+_+"*\\)|)(?=[^-]|$)",$=new RegExp("^"+_+"+|((?:^|[^\\\\])(?:\\\\.)*)"+_+"+$","g"),I=new RegExp("^"+_+"*,"+_+"*"),z=new RegExp("^"+_+"*([\\x20\\t\\r\\n\\f>+~])"+_+"*"),X=new RegExp(P),U=/^(?:#([\w\-]+)|(\w+)|\.([\w\-]+))$/,Y=/[\x20\t\r\n\f]*[+~]/,J=/h\d/i,V=/input|select|textarea|button/i,K=/\\(?!\\)/g,G={ID:new RegExp("^#("+q+")"),CLASS:new RegExp("^\\.("+q+")"),NAME:new RegExp("^\\[name=['\"]?("+q+")['\"]?\\]"),TAG:new RegExp("^("+q.replace("w","w*")+")"),ATTR:new RegExp("^"+W),PSEUDO:new RegExp("^"+P),POS:new RegExp(R,"i"),CHILD:new RegExp("^:(only|nth|first|last)-child(?:\\("+_+"*(even|odd|(([+-]|)(\\d*)n|)"+_+"*(?:([+-]|)"+_+"*(\\d+)|))"+_+"*\\)|)","i"),needsContext:new RegExp("^"+_+"*[>+~]|"+R,"i")},Q=function(e){var t=N.createElement("div");try{return e(t)}catch(e){return!1}finally{t=null}},Z=Q(function(e){return e.appendChild(N.createComment("")),!e.getElementsByTagName("*").length}),ee=Q(function(e){return e.innerHTML="<a href='#'></a>",e.firstChild&&void 0!==e.firstChild.getAttribute&&"#"===e.firstChild.getAttribute("href")}),te=Q(function(e){e.innerHTML="<select></select>";var t=typeof e.lastChild.getAttribute("multiple");return"boolean"!==t&&"string"!==t}),ne=Q(function(e){return e.innerHTML="<div class='hidden e'></div><div class='hidden'></div>",!(!e.getElementsByClassName||!e.getElementsByClassName("e").length)&&(e.lastChild.className="e",2===e.getElementsByClassName("e").length)}),re=Q(function(e){e.id=w+0,e.innerHTML="<a name='"+w+"'></a><div name='"+w+"'></div>",C.insertBefore(e,C.firstChild);var t=N.getElementsByName&&N.getElementsByName(w).length===2+N.getElementsByName(w+0).length;return r=!N.getElementById(w),C.removeChild(e),t});try{j.call(C.childNodes,0)[0].nodeType}catch(e){j=function(e){for(var t,n=[];t=this[e];e++)n.push(t);return n}}function ie(e,t,n,r){n=n||[];var i,o,l,u,c=(t=t||N).nodeType;if(!e||"string"!=typeof e)return n;if(1!==c&&9!==c)return[];if(!(l=a(t))&&!r&&(i=U.exec(e)))if(u=i[1]){if(9===c){if(!(o=t.getElementById(u))||!o.parentNode)return n;if(o.id===u)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(u))&&s(t,o)&&o.id===u)return n.push(o),n}else{if(i[2])return A.apply(n,j.call(t.getElementsByTagName(e),0)),n;if((u=i[3])&&ne&&t.getElementsByClassName)return A.apply(n,j.call(t.getElementsByClassName(u),0)),n}return ge(e.replace($,"$1"),t,n,r,l)}function oe(e){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===e}}function ae(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function se(e){return L(function(t){return t=+t,L(function(n,r){for(var i,o=e([],n.length,t),a=o.length;a--;)n[i=o[a]]&&(n[i]=!(r[i]=n[i]))})})}function le(e,t,n){if(e===t)return n;for(var r=e.nextSibling;r;){if(r===t)return-1;r=r.nextSibling}return 1}function ue(e,t){var n,r,o,a,s,l,u,c=M[w][e];if(c)return t?0:c.slice(0);for(s=e,l=[],u=i.preFilter;s;){for(a in n&&!(r=I.exec(s))||(r&&(s=s.slice(r[0].length)),l.push(o=[])),n=!1,(r=z.exec(s))&&(o.push(n=new T(r.shift())),s=s.slice(n.length),n.type=r[0].replace($," ")),i.filter)!(r=G[a].exec(s))||u[a]&&!(r=u[a](r,N,!0))||(o.push(n=new T(r.shift())),s=s.slice(n.length),n.type=a,n.matches=r);if(!n)break}return t?s.length:s?ie.error(e):M(e,l).slice(0)}function ce(e,t,r){var i=t.dir,o=r&&"parentNode"===t.dir,a=k++;return t.first?function(t,n,r){for(;t=t[i];)if(o||1===t.nodeType)return e(t,n,r)}:function(t,r,s){if(s){for(;t=t[i];)if((o||1===t.nodeType)&&e(t,r,s))return t}else for(var l,u=E+" "+a+" ",c=u+n;t=t[i];)if(o||1===t.nodeType){if((l=t[w])===c)return t.sizset;if("string"==typeof l&&0===l.indexOf(u)){if(t.sizset)return t}else{if(t[w]=c,e(t,r,s))return t.sizset=!0,t;t.sizset=!1}}}}function fe(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function pe(e,t,n,r,i){for(var o,a=[],s=0,l=e.length,u=null!=t;s<l;s++)(o=e[s])&&(n&&!n(o,r,i)||(a.push(o),u&&t.push(s)));return a}function de(e,t,n,r,i,o){return r&&!r[w]&&(r=de(r)),i&&!i[w]&&(i=de(i,o)),L(function(o,a,s,l){if(!o||!i){var u,c,f,p=[],d=[],h=a.length,g=o||function(e,t,n,r){for(var i=0,o=t.length;i<o;i++)ie(e,t[i],n,r);return n}(t||"*",s.nodeType?[s]:s,[],o),m=!e||!o&&t?g:pe(g,p,e,s,l),y=n?i||(o?e:h||r)?[]:a:m;if(n&&n(m,y,s,l),r)for(f=pe(y,d),r(f,[],s,l),u=f.length;u--;)(c=f[u])&&(y[d[u]]=!(m[d[u]]=c));if(o)for(u=e&&y.length;u--;)(c=y[u])&&(o[p[u]]=!(a[p[u]]=c));else y=pe(y===a?y.splice(h,y.length):y),i?i(null,a,y,l):A.apply(a,y)}})}function he(e){for(var t,n,r,o=e.length,a=i.relative[e[0].type],s=a||i.relative[" "],l=a?1:0,u=ce(function(e){return e===t},s,!0),c=ce(function(e){return D.call(t,e)>-1},s,!0),p=[function(e,n,r){return!a&&(r||n!==f)||((t=n).nodeType?u(e,n,r):c(e,n,r))}];l<o;l++)if(n=i.relative[e[l].type])p=[ce(fe(p),n)];else{if((n=i.filter[e[l].type].apply(null,e[l].matches))[w]){for(r=++l;r<o&&!i.relative[e[r].type];r++);return de(l>1&&fe(p),l>1&&e.slice(0,l-1).join("").replace($,"$1"),n,l<r&&he(e.slice(l,r)),r<o&&he(e=e.slice(r)),r<o&&e.join(""))}p.push(n)}return fe(p)}function ge(e,t,n,r,o){var a,s,u,c,f,p=ue(e);p.length;if(!r&&1===p.length){if((s=p[0]=p[0].slice(0)).length>2&&"ID"===(u=s[0]).type&&9===t.nodeType&&!o&&i.relative[s[1].type]){if(!(t=i.find.ID(u.matches[0].replace(K,""),t,o)[0]))return n;e=e.slice(s.shift().length)}for(a=G.POS.test(e)?-1:s.length-1;a>=0&&(u=s[a],!i.relative[c=u.type]);a--)if((f=i.find[c])&&(r=f(u.matches[0].replace(K,""),Y.test(s[0].type)&&t.parentNode||t,o))){if(s.splice(a,1),!(e=r.length&&s.join("")))return A.apply(n,j.call(r,0)),n;break}}return l(e,p)(r,t,o,n,Y.test(e)),n}function me(){}ie.matches=function(e,t){return ie(e,null,null,t)},ie.matchesSelector=function(e,t){return ie(t,null,null,[e]).length>0},o=ie.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r];r++)n+=o(t);return n},a=ie.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return!!t&&"HTML"!==t.nodeName},s=ie.contains=C.contains?function(e,t){var n=9===e.nodeType?e.documentElement:e,r=t&&t.parentNode;return e===r||!!(r&&1===r.nodeType&&n.contains&&n.contains(r))}:C.compareDocumentPosition?function(e,t){return t&&!!(16&e.compareDocumentPosition(t))}:function(e,t){for(;t=t.parentNode;)if(t===e)return!0;return!1},ie.attr=function(e,t){var n,r=a(e);return r||(t=t.toLowerCase()),(n=i.attrHandle[t])?n(e):r||te?e.getAttribute(t):(n=e.getAttributeNode(t))?"boolean"==typeof e[t]?e[t]?t:null:n.specified?n.value:null:null},i=ie.selectors={cacheLength:50,createPseudo:L,match:G,attrHandle:ee?{}:{href:function(e){return e.getAttribute("href",2)},type:function(e){return e.getAttribute("type")}},find:{ID:r?function(e,t,n){if(void 0!==t.getElementById&&!n){var r=t.getElementById(e);return r&&r.parentNode?[r]:[]}}:function(e,t,n){if(void 0!==t.getElementById&&!n){var r=t.getElementById(e);return r?r.id===e||void 0!==r.getAttributeNode&&r.getAttributeNode("id").value===e?[r]:void 0:[]}},TAG:Z?function(e,t){if(void 0!==t.getElementsByTagName)return t.getElementsByTagName(e)}:function(e,t){var n=t.getElementsByTagName(e);if("*"===e){for(var r,i=[],o=0;r=n[o];o++)1===r.nodeType&&i.push(r);return i}return n},NAME:re&&function(e,t){if(void 0!==t.getElementsByName)return t.getElementsByName(name)},CLASS:ne&&function(e,t,n){if(void 0!==t.getElementsByClassName&&!n)return t.getElementsByClassName(e)}},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(K,""),e[3]=(e[4]||e[5]||"").replace(K,""),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1]?(e[2]||ie.error(e[0]),e[3]=+(e[3]?e[4]+(e[5]||1):2*("even"===e[2]||"odd"===e[2])),e[4]=+(e[6]+e[7]||"odd"===e[2])):e[2]&&ie.error(e[0]),e},PSEUDO:function(e){var t,n;return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[3]:(t=e[4])&&(X.test(t)&&(n=ue(t,!0))&&(n=t.indexOf(")",t.length-n)-t.length)&&(t=t.slice(0,n),e[0]=e[0].slice(0,n)),e[2]=t),e.slice(0,3))}},filter:{ID:r?function(e){return e=e.replace(K,""),function(t){return t.getAttribute("id")===e}}:function(e){return e=e.replace(K,""),function(t){var n=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},TAG:function(e){return"*"===e?function(){return!0}:(e=e.replace(K,"").toLowerCase(),function(t){return t.nodeName&&t.nodeName.toLowerCase()===e})},CLASS:function(e){var t=F[w][e];return t||(t=F(e,new RegExp("(^|"+_+")"+e+"("+_+"|$)"))),function(e){return t.test(e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}},ATTR:function(e,t,n){return function(r,i){var o=ie.attr(r,e);return null==o?"!="===t:!t||(o+="","="===t?o===n:"!="===t?o!==n:"^="===t?n&&0===o.indexOf(n):"*="===t?n&&o.indexOf(n)>-1:"$="===t?n&&o.substr(o.length-n.length)===n:"~="===t?(" "+o+" ").indexOf(n)>-1:"|="===t&&(o===n||o.substr(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r){return"nth"===e?function(e){var t,i,o=e.parentNode;if(1===n&&0===r)return!0;if(o)for(i=0,t=o.firstChild;t&&(1!==t.nodeType||(i++,e!==t));t=t.nextSibling);return(i-=r)===n||i%n==0&&i/n>=0}:function(t){var n=t;switch(e){case"only":case"first":for(;n=n.previousSibling;)if(1===n.nodeType)return!1;if("first"===e)return!0;n=t;case"last":for(;n=n.nextSibling;)if(1===n.nodeType)return!1;return!0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||ie.error("unsupported pseudo: "+e);return r[w]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?L(function(e,n){for(var i,o=r(e,t),a=o.length;a--;)e[i=D.call(e,o[a])]=!(n[i]=o[a])}):function(e){return r(e,0,n)}):r}},pseudos:{not:L(function(e){var t=[],n=[],r=l(e.replace($,"$1"));return r[w]?L(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),!n.pop()}}),has:L(function(e){return function(t){return ie(e,t).length>0}}),contains:L(function(e){return function(t){return(t.textContent||t.innerText||o(t)).indexOf(e)>-1}}),enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},parent:function(e){return!i.pseudos.empty(e)},empty:function(e){var t;for(e=e.firstChild;e;){if(e.nodeName>"@"||3===(t=e.nodeType)||4===t)return!1;e=e.nextSibling}return!0},header:function(e){return J.test(e.nodeName)},text:function(e){var t,n;return"input"===e.nodeName.toLowerCase()&&"text"===(t=e.type)&&(null==(n=e.getAttribute("type"))||n.toLowerCase()===t)},radio:oe("radio"),checkbox:oe("checkbox"),file:oe("file"),password:oe("password"),image:oe("image"),submit:ae("submit"),reset:ae("reset"),button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},input:function(e){return V.test(e.nodeName)},focus:function(e){var t=e.ownerDocument;return e===t.activeElement&&(!t.hasFocus||t.hasFocus())&&!(!e.type&&!e.href)},active:function(e){return e===e.ownerDocument.activeElement},first:se(function(e,t,n){return[0]}),last:se(function(e,t,n){return[t-1]}),eq:se(function(e,t,n){return[n<0?n+t:n]}),even:se(function(e,t,n){for(var r=0;r<t;r+=2)e.push(r);return e}),odd:se(function(e,t,n){for(var r=1;r<t;r+=2)e.push(r);return e}),lt:se(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:se(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},u=C.compareDocumentPosition?function(e,t){return e===t?(c=!0,0):(e.compareDocumentPosition&&t.compareDocumentPosition?4&e.compareDocumentPosition(t):e.compareDocumentPosition)?-1:1}:function(e,t){if(e===t)return c=!0,0;if(e.sourceIndex&&t.sourceIndex)return e.sourceIndex-t.sourceIndex;var n,r,i=[],o=[],a=e.parentNode,s=t.parentNode,l=a;if(a===s)return le(e,t);if(!a)return-1;if(!s)return 1;for(;l;)i.unshift(l),l=l.parentNode;for(l=s;l;)o.unshift(l),l=l.parentNode;n=i.length,r=o.length;for(var u=0;u<n&&u<r;u++)if(i[u]!==o[u])return le(i[u],o[u]);return u===n?le(e,o[u],-1):le(i[u],t,1)},[0,0].sort(u),p=!c,ie.uniqueSort=function(e){var t,n=1;if(c=p,e.sort(u),c)for(;t=e[n];n++)t===e[n-1]&&e.splice(n--,1);return e},ie.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},l=ie.compile=function(e,t){var r,o=[],a=[],s=O[w][e];if(!s){for(t||(t=ue(e)),r=t.length;r--;)(s=he(t[r]))[w]?o.push(s):a.push(s);s=O(e,function(e,t){var r=t.length>0,o=e.length>0,a=function a(s,l,u,c,p){var d,h,g,m=[],y=0,v="0",b=s&&[],x=null!=p,w=f,T=s||o&&i.find.TAG("*",p&&l.parentNode||l),C=E+=null==w?1:Math.E;for(x&&(f=l!==N&&l,n=a.el);null!=(d=T[v]);v++){if(o&&d){for(h=0;g=e[h];h++)if(g(d,l,u)){c.push(d);break}x&&(E=C,n=++a.el)}r&&((d=!g&&d)&&y--,s&&b.push(d))}if(y+=v,r&&v!==y){for(h=0;g=t[h];h++)g(b,m,l,u);if(s){if(y>0)for(;v--;)b[v]||m[v]||(m[v]=S.call(c));m=pe(m)}A.apply(c,m),x&&!s&&m.length>0&&y+t.length>1&&ie.uniqueSort(c)}return x&&(E=C,f=w),b};return a.el=0,r?L(a):a}(a,o))}return s},N.querySelectorAll&&(h=ge,m=/'|\\/g,y=/\=[\x20\t\r\n\f]*([^'"\]]*)[\x20\t\r\n\f]*\]/g,v=[":focus"],b=[":active",":focus"],x=C.matchesSelector||C.mozMatchesSelector||C.webkitMatchesSelector||C.oMatchesSelector||C.msMatchesSelector,Q(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||v.push("\\["+_+"*(?:checked|disabled|ismap|multiple|readonly|selected|value)"),e.querySelectorAll(":checked").length||v.push(":checked")}),Q(function(e){e.innerHTML="<p test=''></p>",e.querySelectorAll("[test^='']").length&&v.push("[*^$]="+_+"*(?:\"\"|'')"),e.innerHTML="<input type='hidden'/>",e.querySelectorAll(":enabled").length||v.push(":enabled",":disabled")}),v=new RegExp(v.join("|")),ge=function(e,t,n,r,i){if(!(r||i||v&&v.test(e))){var o,a,s=!0,l=w,u=t,c=9===t.nodeType&&e;if(1===t.nodeType&&"object"!==t.nodeName.toLowerCase()){for(o=ue(e),(s=t.getAttribute("id"))?l=s.replace(m,"\\$&"):t.setAttribute("id",l),l="[id='"+l+"'] ",a=o.length;a--;)o[a]=l+o[a].join("");u=Y.test(e)&&t.parentNode||t,c=o.join(",")}if(c)try{return A.apply(n,j.call(u.querySelectorAll(c),0)),n}catch(e){}finally{s||t.removeAttribute("id")}}return h(e,t,n,r,i)},x&&(Q(function(e){d=x.call(e,"div");try{x.call(e,"[test!='']:sizzle"),b.push("!=",P)}catch(e){}}),b=new RegExp(b.join("|")),ie.matchesSelector=function(e,t){if(t=t.replace(y,"='$1']"),!(a(e)||b.test(t)||v&&v.test(t)))try{var n=x.call(e,t);if(n||d||e.document&&11!==e.document.nodeType)return n}catch(e){}return ie(t,null,null,[e]).length>0})),i.pseudos.nth=i.pseudos.eq,i.filters=me.prototype=i.pseudos,i.setFilters=new me,ie.attr=g.attr,g.find=ie,g.expr=ie.selectors,g.expr[":"]=g.expr.pseudos,g.unique=ie.uniqueSort,g.text=ie.getText,g.isXMLDoc=ie.isXML,g.contains=ie.contains}(e);var ne=/Until$/,re=/^(?:parents|prev(?:Until|All))/,ie=/^.[^:#\[\.,]*$/,oe=g.expr.match.needsContext,ae={children:!0,contents:!0,next:!0,prev:!0};function se(e){return!e||!e.parentNode||11===e.parentNode.nodeType}function le(e,t){do{e=e[t]}while(e&&1!==e.nodeType);return e}function ue(e,t,n){if(t=t||0,g.isFunction(t))return g.grep(e,function(e,r){return!!t.call(e,r,e)===n});if(t.nodeType)return g.grep(e,function(e,r){return e===t===n});if("string"==typeof t){var r=g.grep(e,function(e){return 1===e.nodeType});if(ie.test(t))return g.filter(t,r,!n);t=g.filter(t,r)}return g.grep(e,function(e,r){return g.inArray(e,t)>=0===n})}function ce(e){var t=de.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}g.fn.extend({find:function(e){var t,n,r,i,o,a,s=this;if("string"!=typeof e)return g(e).filter(function(){for(t=0,n=s.length;t<n;t++)if(g.contains(s[t],this))return!0});for(a=this.pushStack("","find",e),t=0,n=this.length;t<n;t++)if(r=a.length,g.find(e,this[t],a),t>0)for(i=r;i<a.length;i++)for(o=0;o<r;o++)if(a[o]===a[i]){a.splice(i--,1);break}return a},has:function(e){var t,n=g(e,this),r=n.length;return this.filter(function(){for(t=0;t<r;t++)if(g.contains(this,n[t]))return!0})},not:function(e){return this.pushStack(ue(this,e,!1),"not",e)},filter:function(e){return this.pushStack(ue(this,e,!0),"filter",e)},is:function(e){return!!e&&("string"==typeof e?oe.test(e)?g(e,this.context).index(this[0])>=0:g.filter(e,this).length>0:this.filter(e).length>0)},closest:function(e,t){for(var n,r=0,i=this.length,o=[],a=oe.test(e)||"string"!=typeof e?g(e,t||this.context):0;r<i;r++)for(n=this[r];n&&n.ownerDocument&&n!==t&&11!==n.nodeType;){if(a?a.index(n)>-1:g.find.matchesSelector(n,e)){o.push(n);break}n=n.parentNode}return o=o.length>1?g.unique(o):o,this.pushStack(o,"closest",e)},index:function(e){return e?"string"==typeof e?g.inArray(this[0],g(e)):g.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.prevAll().length:-1},add:function(e,t){var n="string"==typeof e?g(e,t):g.makeArray(e&&e.nodeType?[e]:e),r=g.merge(this.get(),n);return this.pushStack(se(n[0])||se(r[0])?r:g.unique(r))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),g.fn.andSelf=g.fn.addBack,g.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return g.dir(e,"parentNode")},parentsUntil:function(e,t,n){return g.dir(e,"parentNode",n)},next:function(e){return le(e,"nextSibling")},prev:function(e){return le(e,"previousSibling")},nextAll:function(e){return g.dir(e,"nextSibling")},prevAll:function(e){return g.dir(e,"previousSibling")},nextUntil:function(e,t,n){return g.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return g.dir(e,"previousSibling",n)},siblings:function(e){return g.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return g.sibling(e.firstChild)},contents:function(e){return g.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:g.merge([],e.childNodes)}},function(e,t){g.fn[e]=function(n,r){var i=g.map(this,t,n);return ne.test(e)||(r=n),r&&"string"==typeof r&&(i=g.filter(r,i)),i=this.length>1&&!ae[e]?g.unique(i):i,this.length>1&&re.test(e)&&(i=i.reverse()),this.pushStack(i,e,c.call(arguments).join(","))}}),g.extend({filter:function(e,t,n){return n&&(e=":not("+e+")"),1===t.length?g.find.matchesSelector(t[0],e)?[t[0]]:[]:g.find.matches(e,t)},dir:function(e,n,r){for(var i=[],o=e[n];o&&9!==o.nodeType&&(r===t||1!==o.nodeType||!g(o).is(r));)1===o.nodeType&&i.push(o),o=o[n];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}});var fe,pe,de="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",he=/ jQuery\d+="(?:null|\d+)"/g,ge=/^\s+/,me=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,ye=/<([\w:]+)/,ve=/<tbody/i,be=/<|&#?\w+;/,xe=/<(?:script|style|link)/i,we=/<(?:script|object|embed|option|style)/i,Te=new RegExp("<(?:"+de+")[\\s/>]","i"),Ne=/^(?:checkbox|radio)$/,Ce=/checked\s*(?:[^=]|=\s*.checked.)/i,Ee=/\/(java|ecma)script/i,ke=/^\s*<!(?:\[CDATA\[|\-\-)|[\]\-]{2}>\s*$/g,Se={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>","</map>"],_default:[0,"",""]},Ae=ce(i),je=Ae.appendChild(i.createElement("div"));function De(e,t){if(1===t.nodeType&&g.hasData(e)){var n,r,i,o=g._data(e),a=g._data(t,o),s=o.events;if(s)for(n in delete a.handle,a.events={},s)for(r=0,i=s[n].length;r<i;r++)g.event.add(t,n,s[n][r]);a.data&&(a.data=g.extend({},a.data))}}function Le(e,t){var n;1===t.nodeType&&(t.clearAttributes&&t.clearAttributes(),t.mergeAttributes&&t.mergeAttributes(e),"object"===(n=t.nodeName.toLowerCase())?(t.parentNode&&(t.outerHTML=e.outerHTML),g.support.html5Clone&&e.innerHTML&&!g.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Ne.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.selected=e.defaultSelected:"input"===n||"textarea"===n?t.defaultValue=e.defaultValue:"script"===n&&t.text!==e.text&&(t.text=e.text),t.removeAttribute(g.expando))}function He(e){return void 0!==e.getElementsByTagName?e.getElementsByTagName("*"):void 0!==e.querySelectorAll?e.querySelectorAll("*"):[]}function Fe(e){Ne.test(e.type)&&(e.defaultChecked=e.checked)}Se.optgroup=Se.option,Se.tbody=Se.tfoot=Se.colgroup=Se.caption=Se.thead,Se.th=Se.td,g.support.htmlSerialize||(Se._default=[1,"X<div>","</div>"]),g.fn.extend({text:function(e){return g.access(this,function(e){return e===t?g.text(this):this.empty().append((this[0]&&this[0].ownerDocument||i).createTextNode(e))},null,e,arguments.length)},wrapAll:function(e){if(g.isFunction(e))return this.each(function(t){g(this).wrapAll(e.call(this,t))});if(this[0]){var t=g(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return g.isFunction(e)?this.each(function(t){g(this).wrapInner(e.call(this,t))}):this.each(function(){var t=g(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=g.isFunction(e);return this.each(function(n){g(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){g.nodeName(this,"body")||g(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,!0,function(e){1!==this.nodeType&&11!==this.nodeType||this.appendChild(e)})},prepend:function(){return this.domManip(arguments,!0,function(e){1!==this.nodeType&&11!==this.nodeType||this.insertBefore(e,this.firstChild)})},before:function(){if(!se(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this)});if(arguments.length){var e=g.clean(arguments);return this.pushStack(g.merge(e,this),"before",this.selector)}},after:function(){if(!se(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this.nextSibling)});if(arguments.length){var e=g.clean(arguments);return this.pushStack(g.merge(this,e),"after",this.selector)}},remove:function(e,t){for(var n,r=0;null!=(n=this[r]);r++)e&&!g.filter(e,[n]).length||(t||1!==n.nodeType||(g.cleanData(n.getElementsByTagName("*")),g.cleanData([n])),n.parentNode&&n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)for(1===e.nodeType&&g.cleanData(e.getElementsByTagName("*"));e.firstChild;)e.removeChild(e.firstChild);return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return g.clone(this,e,t)})},html:function(e){return g.access(this,function(e){var n=this[0]||{},r=0,i=this.length;if(e===t)return 1===n.nodeType?n.innerHTML.replace(he,""):t;if("string"==typeof e&&!xe.test(e)&&(g.support.htmlSerialize||!Te.test(e))&&(g.support.leadingWhitespace||!ge.test(e))&&!Se[(ye.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(me,"<$1></$2>");try{for(;r<i;r++)1===(n=this[r]||{}).nodeType&&(g.cleanData(n.getElementsByTagName("*")),n.innerHTML=e);n=0}catch(e){}}n&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(e){return se(this[0])?this.length?this.pushStack(g(g.isFunction(e)?e():e),"replaceWith",e):this:g.isFunction(e)?this.each(function(t){var n=g(this),r=n.html();n.replaceWith(e.call(this,t,r))}):("string"!=typeof e&&(e=g(e).detach()),this.each(function(){var t=this.nextSibling,n=this.parentNode;g(this).remove(),t?g(t).before(e):g(n).append(e)}))},detach:function(e){return this.remove(e,!0)},domManip:function(e,n,r){var i,o,a,s,l,u,c=0,f=(e=[].concat.apply([],e))[0],p=[],d=this.length;if(!g.support.checkClone&&d>1&&"string"==typeof f&&Ce.test(f))return this.each(function(){g(this).domManip(e,n,r)});if(g.isFunction(f))return this.each(function(i){var o=g(this);e[0]=f.call(this,i,n?o.html():t),o.domManip(e,n,r)});if(this[0]){if(o=(a=(i=g.buildFragment(e,this,p)).fragment).firstChild,1===a.childNodes.length&&(a=o),o)for(n=n&&g.nodeName(o,"tr"),s=i.cacheable||d-1;c<d;c++)r.call(n&&g.nodeName(this[c],"table")?(l=this[c],u="tbody",l.getElementsByTagName(u)[0]||l.appendChild(l.ownerDocument.createElement(u))):this[c],c===s?a:g.clone(a,!0,!0));a=o=null,p.length&&g.each(p,function(e,t){t.src?g.ajax?g.ajax({url:t.src,type:"GET",dataType:"script",async:!1,global:!1,throws:!0}):g.error("no ajax"):g.globalEval((t.text||t.textContent||t.innerHTML||"").replace(ke,"")),t.parentNode&&t.parentNode.removeChild(t)})}return this}}),g.buildFragment=function(e,n,r){var o,a,s,l=e[0];return n=(n=!(n=n||i).nodeType&&n[0]||n).ownerDocument||n,!(1===e.length&&"string"==typeof l&&l.length<512&&n===i&&"<"===l.charAt(0))||we.test(l)||!g.support.checkClone&&Ce.test(l)||!g.support.html5Clone&&Te.test(l)||(a=!0,s=(o=g.fragments[l])!==t),o||(o=n.createDocumentFragment(),g.clean(e,n,o,r),a&&(g.fragments[l]=s&&o)),{fragment:o,cacheable:a}},g.fragments={},g.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){g.fn[e]=function(n){var r,i=0,o=[],a=g(n),s=a.length,l=1===this.length&&this[0].parentNode;if((null==l||l&&11===l.nodeType&&1===l.childNodes.length)&&1===s)return a[t](this[0]),this;for(;i<s;i++)r=(i>0?this.clone(!0):this).get(),g(a[i])[t](r),o=o.concat(r);return this.pushStack(o,e,a.selector)}}),g.extend({clone:function(e,t,n){var r,i,o,a;if(g.support.html5Clone||g.isXMLDoc(e)||!Te.test("<"+e.nodeName+">")?a=e.cloneNode(!0):(je.innerHTML=e.outerHTML,je.removeChild(a=je.firstChild)),!(g.support.noCloneEvent&&g.support.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||g.isXMLDoc(e)))for(Le(e,a),r=He(e),i=He(a),o=0;r[o];++o)i[o]&&Le(r[o],i[o]);if(t&&(De(e,a),n))for(r=He(e),i=He(a),o=0;r[o];++o)De(r[o],i[o]);return r=i=null,a},clean:function(e,t,n,r){var o,a,s,l,u,c,f,p,d,h,m,y=t===i&&Ae,v=[];for(t&&void 0!==t.createDocumentFragment||(t=i),o=0;null!=(s=e[o]);o++)if("number"==typeof s&&(s+=""),s){if("string"==typeof s)if(be.test(s)){for(y=y||ce(t),f=t.createElement("div"),y.appendChild(f),s=s.replace(me,"<$1></$2>"),l=(ye.exec(s)||["",""])[1].toLowerCase(),c=(u=Se[l]||Se._default)[0],f.innerHTML=u[1]+s+u[2];c--;)f=f.lastChild;if(!g.support.tbody)for(p=ve.test(s),a=(d="table"!==l||p?"<table>"!==u[1]||p?[]:f.childNodes:f.firstChild&&f.firstChild.childNodes).length-1;a>=0;--a)g.nodeName(d[a],"tbody")&&!d[a].childNodes.length&&d[a].parentNode.removeChild(d[a]);!g.support.leadingWhitespace&&ge.test(s)&&f.insertBefore(t.createTextNode(ge.exec(s)[0]),f.firstChild),s=f.childNodes,f.parentNode.removeChild(f)}else s=t.createTextNode(s);s.nodeType?v.push(s):g.merge(v,s)}if(f&&(s=f=y=null),!g.support.appendChecked)for(o=0;null!=(s=v[o]);o++)g.nodeName(s,"input")?Fe(s):void 0!==s.getElementsByTagName&&g.grep(s.getElementsByTagName("input"),Fe);if(n)for(h=function(e){if(!e.type||Ee.test(e.type))return r?r.push(e.parentNode?e.parentNode.removeChild(e):e):n.appendChild(e)},o=0;null!=(s=v[o]);o++)g.nodeName(s,"script")&&h(s)||(n.appendChild(s),void 0!==s.getElementsByTagName&&(m=g.grep(g.merge([],s.getElementsByTagName("script")),h),v.splice.apply(v,[o+1,0].concat(m)),o+=m.length));return v},cleanData:function(e,t){for(var n,r,i,o,a=0,s=g.expando,l=g.cache,u=g.support.deleteExpando,c=g.event.special;null!=(i=e[a]);a++)if((t||g.acceptData(i))&&(n=(r=i[s])&&l[r])){if(n.events)for(o in n.events)c[o]?g.event.remove(i,o):g.removeEvent(i,o,n.handle);l[r]&&(delete l[r],u?delete i[s]:i.removeAttribute?i.removeAttribute(s):i[s]=null,g.deletedIds.push(r))}}}),g.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},pe={},(fe=g.uaMatch(a.userAgent)).browser&&(pe[fe.browser]=!0,pe.version=fe.version),pe.chrome?pe.webkit=!0:pe.webkit&&(pe.safari=!0),g.browser=pe,g.sub=function(){function e(t,n){return new e.fn.init(t,n)}g.extend(!0,e,this),e.superclass=this,e.fn=e.prototype=this(),e.fn.constructor=e,e.sub=this.sub,e.fn.init=function(n,r){return r&&r instanceof g&&!(r instanceof e)&&(r=e(r)),g.fn.init.call(this,n,r,t)},e.fn.init.prototype=e.fn;var t=e(i);return e};var Me,Oe,_e,qe=/alpha\([^)]*\)/i,Be=/opacity=([^)]*)/,We=/^(top|right|bottom|left)$/,Pe=/^(none|table(?!-c[ea]).+)/,Re=/^margin/,$e=new RegExp("^("+m+")(.*)$","i"),Ie=new RegExp("^("+m+")(?!px)[a-z%]+$","i"),ze=new RegExp("^([-+])=("+m+")","i"),Xe={},Ue={position:"absolute",visibility:"hidden",display:"block"},Ye={letterSpacing:0,fontWeight:400},Je=["Top","Right","Bottom","Left"],Ve=["Webkit","O","Moz","ms"],Ke=g.fn.toggle;function Ge(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Ve.length;i--;)if((t=Ve[i]+n)in e)return t;return r}function Qe(e,t){return e=t||e,"none"===g.css(e,"display")||!g.contains(e.ownerDocument,e)}function Ze(e,t){for(var n,r,i=[],o=0,a=e.length;o<a;o++)(n=e[o]).style&&(i[o]=g._data(n,"olddisplay"),t?(i[o]||"none"!==n.style.display||(n.style.display=""),""===n.style.display&&Qe(n)&&(i[o]=g._data(n,"olddisplay",rt(n.nodeName)))):(r=Me(n,"display"),i[o]||"none"===r||g._data(n,"olddisplay",r)));for(o=0;o<a;o++)(n=e[o]).style&&(t&&"none"!==n.style.display&&""!==n.style.display||(n.style.display=t?i[o]||"":"none"));return e}function et(e,t,n){var r=$e.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function tt(e,t,n,r){for(var i=n===(r?"border":"content")?4:"width"===t?1:0,o=0;i<4;i+=2)"margin"===n&&(o+=g.css(e,n+Je[i],!0)),r?("content"===n&&(o-=parseFloat(Me(e,"padding"+Je[i]))||0),"margin"!==n&&(o-=parseFloat(Me(e,"border"+Je[i]+"Width"))||0)):(o+=parseFloat(Me(e,"padding"+Je[i]))||0,"padding"!==n&&(o+=parseFloat(Me(e,"border"+Je[i]+"Width"))||0));return o}function nt(e,t,n){var r="width"===t?e.offsetWidth:e.offsetHeight,i=!0,o=g.support.boxSizing&&"border-box"===g.css(e,"boxSizing");if(r<=0||null==r){if(((r=Me(e,t))<0||null==r)&&(r=e.style[t]),Ie.test(r))return r;i=o&&(g.support.boxSizingReliable||r===e.style[t]),r=parseFloat(r)||0}return r+tt(e,t,n||(o?"border":"content"),i)+"px"}function rt(e){if(Xe[e])return Xe[e];var t=g("<"+e+">").appendTo(i.body),n=t.css("display");return t.remove(),"none"!==n&&""!==n||(Oe=i.body.appendChild(Oe||g.extend(i.createElement("iframe"),{frameBorder:0,width:0,height:0})),_e&&Oe.createElement||((_e=(Oe.contentWindow||Oe.contentDocument).document).write("<!doctype html><html><body>"),_e.close()),t=_e.body.appendChild(_e.createElement(e)),n=Me(t,"display"),i.body.removeChild(Oe)),Xe[e]=n,n}g.fn.extend({css:function(e,n){return g.access(this,function(e,n,r){return r!==t?g.style(e,n,r):g.css(e,n)},e,n,arguments.length>1)},show:function(){return Ze(this,!0)},hide:function(){return Ze(this)},toggle:function(e,t){var n="boolean"==typeof e;return g.isFunction(e)&&g.isFunction(t)?Ke.apply(this,arguments):this.each(function(){(n?e:Qe(this))?g(this).show():g(this).hide()})}}),g.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Me(e,"opacity");return""===n?"1":n}}}},cssNumber:{fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:g.support.cssFloat?"cssFloat":"styleFloat"},style:function(e,n,r,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var o,a,s,l=g.camelCase(n),u=e.style;if(n=g.cssProps[l]||(g.cssProps[l]=Ge(u,l)),s=g.cssHooks[n]||g.cssHooks[l],r===t)return s&&"get"in s&&(o=s.get(e,!1,i))!==t?o:u[n];if(!("string"===(a=typeof r)&&(o=ze.exec(r))&&(r=(o[1]+1)*o[2]+parseFloat(g.css(e,n)),a="number"),null==r||"number"===a&&isNaN(r)||("number"!==a||g.cssNumber[l]||(r+="px"),s&&"set"in s&&(r=s.set(e,r,i))===t)))try{u[n]=r}catch(e){}}},css:function(e,n,r,i){var o,a,s,l=g.camelCase(n);return n=g.cssProps[l]||(g.cssProps[l]=Ge(e.style,l)),(s=g.cssHooks[n]||g.cssHooks[l])&&"get"in s&&(o=s.get(e,!0,i)),o===t&&(o=Me(e,n)),"normal"===o&&n in Ye&&(o=Ye[n]),r||i!==t?(a=parseFloat(o),r||g.isNumeric(a)?a||0:o):o},swap:function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r}}),e.getComputedStyle?Me=function(t,n){var r,i,o,a,s=e.getComputedStyle(t,null),l=t.style;return s&&(""!==(r=s[n])||g.contains(t.ownerDocument,t)||(r=g.style(t,n)),Ie.test(r)&&Re.test(n)&&(i=l.width,o=l.minWidth,a=l.maxWidth,l.minWidth=l.maxWidth=l.width=r,r=s.width,l.width=i,l.minWidth=o,l.maxWidth=a)),r}:i.documentElement.currentStyle&&(Me=function(e,t){var n,r,i=e.currentStyle&&e.currentStyle[t],o=e.style;return null==i&&o&&o[t]&&(i=o[t]),Ie.test(i)&&!We.test(t)&&(n=o.left,(r=e.runtimeStyle&&e.runtimeStyle.left)&&(e.runtimeStyle.left=e.currentStyle.left),o.left="fontSize"===t?"1em":i,i=o.pixelLeft+"px",o.left=n,r&&(e.runtimeStyle.left=r)),""===i?"auto":i}),g.each(["height","width"],function(e,t){g.cssHooks[t]={get:function(e,n,r){if(n)return 0===e.offsetWidth&&Pe.test(Me(e,"display"))?g.swap(e,Ue,function(){return nt(e,t,r)}):nt(e,t,r)},set:function(e,n,r){return et(0,n,r?tt(e,t,r,g.support.boxSizing&&"border-box"===g.css(e,"boxSizing")):0)}}}),g.support.opacity||(g.cssHooks.opacity={get:function(e,t){return Be.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=g.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=r&&r.filter||n.filter||"";n.zoom=1,t>=1&&""===g.trim(o.replace(qe,""))&&n.removeAttribute&&(n.removeAttribute("filter"),r&&!r.filter)||(n.filter=qe.test(o)?o.replace(qe,i):o+" "+i)}}),g(function(){g.support.reliableMarginRight||(g.cssHooks.marginRight={get:function(e,t){return g.swap(e,{display:"inline-block"},function(){if(t)return Me(e,"marginRight")})}}),!g.support.pixelPosition&&g.fn.position&&g.each(["top","left"],function(e,t){g.cssHooks[t]={get:function(e,n){if(n){var r=Me(e,t);return Ie.test(r)?g(e).position()[t]+"px":r}}}})}),g.expr&&g.expr.filters&&(g.expr.filters.hidden=function(e){return 0===e.offsetWidth&&0===e.offsetHeight||!g.support.reliableHiddenOffsets&&"none"===(e.style&&e.style.display||Me(e,"display"))},g.expr.filters.visible=function(e){return!g.expr.filters.hidden(e)}),g.each({margin:"",padding:"",border:"Width"},function(e,t){g.cssHooks[e+t]={expand:function(n){var r,i="string"==typeof n?n.split(" "):[n],o={};for(r=0;r<4;r++)o[e+Je[r]+t]=i[r]||i[r-2]||i[0];return o}},Re.test(e)||(g.cssHooks[e+t].set=et)});var it=/%20/g,ot=/\[\]$/,at=/\r?\n/g,st=/^(?:color|date|datetime|datetime-local|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,lt=/^(?:select|textarea)/i;function ut(e,t,n,r){var i;if(g.isArray(t))g.each(t,function(t,i){n||ot.test(e)?r(e,i):ut(e+"["+("object"==typeof i?t:"")+"]",i,n,r)});else if(n||"object"!==g.type(t))r(e,t);else for(i in t)ut(e+"["+i+"]",t[i],n,r)}g.fn.extend({serialize:function(){return g.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?g.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||lt.test(this.nodeName)||st.test(this.type))}).map(function(e,t){var n=g(this).val();return null==n?null:g.isArray(n)?g.map(n,function(e,n){return{name:t.name,value:e.replace(at,"\r\n")}}):{name:t.name,value:n.replace(at,"\r\n")}}).get()}}),g.param=function(e,n){var r,i=[],o=function(e,t){t=g.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(n===t&&(n=g.ajaxSettings&&g.ajaxSettings.traditional),g.isArray(e)||e.jquery&&!g.isPlainObject(e))g.each(e,function(){o(this.name,this.value)});else for(r in e)ut(r,e[r],n,o);return i.join("&").replace(it,"+")};var ct,ft,pt=/#.*$/,dt=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,ht=/^(?:GET|HEAD)$/,gt=/^\/\//,mt=/\?/,yt=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,vt=/([?&])_=[^&]*/,bt=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,xt=g.fn.load,wt={},Tt={},Nt=["*/"]+["*"];try{ft=o.href}catch(e){(ft=i.createElement("a")).href="",ft=ft.href}function Ct(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i,o=t.toLowerCase().split(v),a=0,s=o.length;if(g.isFunction(n))for(;a<s;a++)r=o[a],(i=/^\+/.test(r))&&(r=r.substr(1)||"*"),(e[r]=e[r]||[])[i?"unshift":"push"](n)}}function Et(e,n,r,i,o,a){if(!i.isStop){(a=a||{})[o=o||n.dataTypes[0]]=!0;for(var s,l=e[o],u=0,c=l?l.length:0,f=e===wt;u<c&&(f||!s);u++){if(s=l[u](n,r,i),i.isStop)return;"string"==typeof s&&(!f||a[s]?s=t:(n.dataTypes.unshift(s),s=Et(e,n,r,i,s,a)))}return!f&&s||a["*"]||(s=Et(e,n,r,i,"*",a)),s}}function kt(e,n){var r,i,o=g.ajaxSettings.flatOptions||{};for(r in n)n[r]!==t&&((o[r]?e:i||(i={}))[r]=n[r]);i&&g.extend(!0,e,i)}ct=bt.exec(ft.toLowerCase())||[],g.fn.load=function(e,n,r){if("string"!=typeof e&&xt)return xt.apply(this,arguments);if(!this.length)return this;var i,o,a,s=this,l=e.indexOf(" ");return l>=0&&(i=e.slice(l,e.length),e=e.slice(0,l)),g.isFunction(n)?(r=n,n=t):n&&"object"==typeof n&&(o="POST"),g.ajax({url:e,type:o,dataType:"html",data:n,complete:function(e,t){r&&s.each(r,a||[e.responseText,t,e])}}).done(function(e){a=arguments,s.html(i?g("<div>").append(e.replace(yt,"")).find(i):e)}),this},g.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(e,t){g.fn[t]=function(e){return this.on(t,e)}}),g.each(["get","post"],function(e,n){g[n]=function(e,r,i,o){return g.isFunction(r)&&(o=o||i,i=r,r=t),g.ajax({type:n,url:e,data:r,success:i,dataType:o})}}),g.extend({getScript:function(e,n){return g.get(e,t,n,"script")},getJSON:function(e,t,n){return g.get(e,t,n,"json")},ajaxSetup:function(e,t){return t?kt(e,g.ajaxSettings):(t=e,e=g.ajaxSettings),kt(e,t),e},ajaxSettings:{url:ft,isLocal:/^(?:about|app|app\-storage|.+\-extension|file|res|widget):$/.test(ct[1]),global:!0,type:"GET",contentType:"application/x-www-form-urlencoded; charset=UTF-8",processData:!0,async:!0,accepts:{xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript","*":Nt},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":e.String,"text html":!0,"text json":g.parseJSON,"text xml":g.parseXML},flatOptions:{context:!0,url:!0},errorCall:[]},ajaxPrefilter:Ct(wt),ajaxTransport:Ct(Tt),ajax:function(e,n){"object"==typeof e&&(n=e,e=t),n=n||{};var r,i,o,a,s,l,u,c,f=g.ajaxSetup({},n),p=f.context||f,d=p!==f&&(p.nodeType||p instanceof g)?g(p):g.event,h=g.Deferred(),m=g.Callbacks("once memory"),y=f.statusCode||{},b={},x={},w=0,T="canceled",N={isStop:!1,readyState:0,setRequestHeader:function(e,t){if(!w){var n=e.toLowerCase();e=x[n]=x[n]||e,b[e]=t}return this},getAllResponseHeaders:function(){return 2===w?i:null},getResponseHeader:function(e){var n;if(2===w){if(!o)for(o={};n=dt.exec(i);)o[n[1].toLowerCase()]=n[2];n=o[e.toLowerCase()]}return n===t?null:n},overrideMimeType:function(e){return w||(f.mimeType=e),this},abort:function(e){return e=e||T,a&&a.abort(e),C(0,e),this},stop:function(){return this.isStop=!0,this.remove(),C(0),this}};function C(e,n,o,l){var c,v,b,x,T,C=n;2!==w&&(w=2,s&&clearTimeout(s),a=t,i=l||"",N.readyState=e>0?4:0,o&&(x=function(e,n,r){var i,o,a,s,l=e.contents,u=e.dataTypes,c=e.responseFields;for(o in c)o in r&&(n[c[o]]=r[o]);for(;"*"===u[0];)u.shift(),i===t&&(i=e.mimeType||n.getResponseHeader("content-type"));if(i)for(o in l)if(l[o]&&l[o].test(i)){u.unshift(o);break}if(u[0]in r)a=u[0];else{for(o in r){if(!u[0]||e.converters[o+" "+u[0]]){a=o;break}s||(s=o)}a=a||s}if(a)return a!==u[0]&&u.unshift(a),r[a]}(f,N,o)),e>=200&&e<300||304===e?(f.ifModified&&((T=N.getResponseHeader("Last-Modified"))&&(g.lastModified[r]=T),(T=N.getResponseHeader("Etag"))&&(g.etag[r]=T)),304===e?(C="notmodified",c=!0):(c=function(e,t){var n,r,i,o,a=e.dataTypes.slice(),s=a[0],l={},u=0;e.dataFilter&&(t=e.dataFilter(t,e.dataType));if(a[1])for(n in e.converters)l[n.toLowerCase()]=e.converters[n];for(;i=a[++u];)if("*"!==i){if("*"!==s&&s!==i){if(!(n=l[s+" "+i]||l["* "+i]))for(r in l)if((o=r.split(" "))[1]===i&&(n=l[s+" "+o[0]]||l["* "+o[0]])){!0===n?n=l[r]:!0!==l[r]&&(i=o[0],a.splice(u--,0,i));break}if(!0!==n)if(n&&e.throws)t=n(t);else try{t=n(t)}catch(e){return{state:"parsererror",error:n?e:"No conversion from "+s+" to "+i}}}s=i}return{state:"success",data:t}}(f,x),C=c.state,v=c.data,c=!(b=c.error))):(b=C,C&&!e||(C="error",e<0&&(e=0))),N.status=e,N.statusText=(n||C)+"",c?h.resolveWith(p,[v,C,N]):(g.ajaxSettings.errorCall=g.ajaxSettings.errorCall||[],g.each(g.ajaxSettings.errorCall,function(e,t){"function"==typeof t&&t(p,C)}),h.rejectWith(p,[N,C,b])),N.statusCode(y),y=t,u&&d.trigger("ajax"+(c?"Success":"Error"),[N,f,c?v:b]),m.fireWith(p,[N,C]),u&&(d.trigger("ajaxComplete",[N,f]),--g.active||g.event.trigger("ajaxStop")))}if(h.promise(N),N.success=N.done,N.error=N.fail,N.complete=m.add,N.statusCode=function(e){var t;if(e)if(w<2)for(t in e)y[t]=[y[t],e[t]];else t=e[N.status],N.always(t);return this},f.url=((e||f.url)+"").replace(pt,"").replace(gt,ct[1]+"//"),f.dataTypes=g.trim(f.dataType||"*").toLowerCase().split(v),null==f.crossDomain&&(l=bt.exec(f.url.toLowerCase())||!1,f.crossDomain=l&&l.join(":")+(l[3]?"":"http:"===l[1]?80:443)!==ct.join(":")+(ct[3]?"":"http:"===ct[1]?80:443)),f.data&&f.processData&&"string"!=typeof f.data&&(f.data=g.param(f.data,f.traditional)),Et(wt,f,n,N),2===w)return N;if(u=f.global,f.type=f.type.toUpperCase(),f.hasContent=!ht.test(f.type),u&&0==g.active++&&g.event.trigger("ajaxStart"),!f.hasContent&&(f.data&&(f.url+=(mt.test(f.url)?"&":"?")+f.data,delete f.data),r=f.url,!1===f.cache)){var E=g.now(),k=f.url.replace(vt,"$1_="+E);f.url=k+(k===f.url?(mt.test(f.url)?"&":"?")+"_="+E:"")}for(c in"string"==typeof $("#_TOKEN").attr("value")&&(f.url+=(mt.test(f.url)?"&":"?")+"_TOKEN="+$("#_TOKEN").attr("value")),(f.data&&f.hasContent&&!1!==f.contentType||n.contentType)&&N.setRequestHeader("Content-Type",f.contentType),f.ifModified&&(r=r||f.url,g.lastModified[r]&&N.setRequestHeader("If-Modified-Since",g.lastModified[r]),g.etag[r]&&N.setRequestHeader("If-None-Match",g.etag[r])),N.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Nt+"; q=0.01":""):f.accepts["*"]),f.headers)N.setRequestHeader(c,f.headers[c]);if(f.beforeSend&&(!1===f.beforeSend.call(p,N,f)||2===w))return N.abort();for(c in T="abort",{success:1,error:1,complete:1})N[c](f[c]);if(a=Et(Tt,f,n,N)){N.readyState=1,u&&d.trigger("ajaxSend",[N,f]),f.async&&f.timeout>0&&(s=setTimeout(function(){N.abort("timeout")},f.timeout));try{w=1,a.send(b,C)}catch(e){if(!(w<2))throw e;C(-1,e)}}else C(-1,"No Transport");return N},active:0,lastModified:{},etag:{}});var St=[],At=/\?/,jt=/(=)\?(?=&|$)|\?\?/,Dt=g.now();g.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=St.pop()||g.expando+"_"+Dt++;return this[e]=!0,e}}),g.ajaxPrefilter("json jsonp",function(n,r,i){var o,a,s,l=n.data,u=n.url,c=!1!==n.jsonp,f=c&&jt.test(u),p=c&&!f&&"string"==typeof l&&!(n.contentType||"").indexOf("application/x-www-form-urlencoded")&&jt.test(l);if("jsonp"===n.dataTypes[0]||f||p)return o=n.jsonpCallback=g.isFunction(n.jsonpCallback)?n.jsonpCallback():n.jsonpCallback,a=e[o],f?n.url=u.replace(jt,"$1"+o):p?n.data=l.replace(jt,"$1"+o):c&&(n.url+=(At.test(u)?"&":"?")+n.jsonp+"="+o),n.converters["script json"]=function(){return s||g.error(o+" was not called"),s[0]},n.dataTypes[0]="json",e[o]=function(){s=arguments},i.always(function(){e[o]=a,n[o]&&(n.jsonpCallback=r.jsonpCallback,St.push(o)),s&&g.isFunction(a)&&a(s[0]),s=a=t}),"script"}),g.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/javascript|ecmascript/},converters:{"text script":function(e){return g.globalEval(e),e}}}),g.ajaxPrefilter("script",function(e){e.cache===t&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),g.ajaxTransport("script",function(e){if(e.crossDomain){var n,r=i.head||i.getElementsByTagName("head")[0]||i.documentElement;return{send:function(o,a){(n=i.createElement("script")).async="async",e.scriptCharset&&(n.charset=e.scriptCharset),n.src=e.url,n.onload=n.onreadystatechange=function(e,i){(i||!n.readyState||/loaded|complete/.test(n.readyState))&&(n.onload=n.onreadystatechange=null,r&&n.parentNode&&r.removeChild(n),n=t,i||a(200,"success"))},r.insertBefore(n,r.firstChild)},abort:function(){n&&n.onload(0,1)}}}});var Lt,Ht,Ft=!!e.ActiveXObject&&function(){for(var e in Lt)Lt[e](0,1)},Mt=0;function Ot(){try{return new e.XMLHttpRequest}catch(e){}}g.ajaxSettings.xhr=e.ActiveXObject?function(){return!this.isLocal&&Ot()||function(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}()}:Ot,Ht=g.ajaxSettings.xhr(),g.extend(g.support,{ajax:!!Ht,cors:!!Ht&&"withCredentials"in Ht}),g.support.ajax&&g.ajaxTransport(function(n){var r;if(!n.crossDomain||g.support.cors)return{send:function(i,o){var a,s,l,u=n.xhr();if(n.username?u.open(n.type,n.url,n.async,n.username,n.password):u.open(n.type,n.url,n.async),n.xhrFields)for(s in n.xhrFields)u[s]=n.xhrFields[s];n.mimeType&&u.overrideMimeType&&u.overrideMimeType(n.mimeType),n.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest");try{for(s in i)u.setRequestHeader(s,i[s])}catch(e){}u.send(n.hasContent&&n.data||null),r=function(e,i){var s,c,f,p,d;try{if(r&&(i||4===u.readyState))if(r=t,a&&(u.onreadystatechange=l||g.noop,Ft&&delete Lt[a]),i)4!==u.readyState&&u.abort();else{s=u.status,f=u.getAllResponseHeaders(),p={},(d=u.responseXML)&&d.documentElement&&(p.xml=d);try{p.text=u.responseText}catch(e){}try{c=u.statusText}catch(e){c=""}s||!n.isLocal||n.crossDomain?1223===s&&(s=204):s=p.text?200:404}}catch(e){i||o(-1,e)}p&&o(s,c,p,f)},n.async?4===u.readyState?setTimeout(r,0):(a=++Mt,Ft&&(Lt||(Lt={},g(e).unload(Ft)),Lt[a]=r),l=u.onreadystatechange,u.onreadystatechange=function(){l&&l.apply(this,arguments),r.apply(this,arguments)}):r()},abort:function(){r&&r(0,1)}}});var _t,qt,Bt=/^(?:toggle|show|hide)$/,Wt=new RegExp("^(?:([-+])=|)("+m+")([a-z%]*)$","i"),Pt=/queueHooks$/,Rt=[function(e,t,n){var r,i,o,a,s,l,u,c,f=this,p=e.style,d={},h=[],m=e.nodeType&&Qe(e);n.queue||(null==(u=g._queueHooks(e,"fx")).unqueued&&(u.unqueued=0,c=u.empty.fire,u.empty.fire=function(){u.unqueued||c()}),u.unqueued++,f.always(function(){f.always(function(){u.unqueued--,g.queue(e,"fx").length||u.empty.fire()})}));1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],"inline"===g.css(e,"display")&&"none"===g.css(e,"float")&&(g.support.inlineBlockNeedsLayout&&"inline"!==rt(e.nodeName)?p.zoom=1:p.display="inline-block"));n.overflow&&(p.overflow="hidden",g.support.shrinkWrapBlocks||f.done(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in t)if(o=t[r],Bt.exec(o)){if(delete t[r],o===(m?"hide":"show"))continue;h.push(r)}if(a=h.length)for(s=g._data(e,"fxshow")||g._data(e,"fxshow",{}),m?g(e).show():f.done(function(){g(e).hide()}),f.done(function(){var t;for(t in g.removeData(e,"fxshow",!0),d)g.style(e,t,d[t])}),r=0;r<a;r++)i=h[r],l=f.createTween(i,m?s[i]:0),d[i]=s[i]||g.style(e,i),i in s||(s[i]=l.start,m&&(l.end=l.start,l.start="width"===i||"height"===i?1:0))}],$t={"*":[function(e,t){var n,r,i=this.createTween(e,t),o=Wt.exec(t),a=i.cur(),s=+a||0,l=1,u=20;if(o){if(n=+o[2],"px"!==(r=o[3]||(g.cssNumber[e]?"":"px"))&&s){s=g.css(i.elem,e,!0)||n||1;do{s/=l=l||".5",g.style(i.elem,e,s+r)}while(l!==(l=i.cur()/a)&&1!==l&&--u)}i.unit=r,i.start=s,i.end=o[1]?s+(o[1]+1)*n:n}return i}]};function It(){return setTimeout(function(){_t=t},0),_t=g.now()}function zt(e,t,n){var r,i=0,o=Rt.length,a=g.Deferred().always(function(){delete s.elem}),s=function(){for(var t=_t||It(),n=Math.max(0,l.startTime+l.duration-t),r=1-(n/l.duration||0),i=0,o=l.tweens.length;i<o;i++)l.tweens[i].run(r);return a.notifyWith(e,[l,r,n]),r<1&&o?n:(a.resolveWith(e,[l]),!1)},l=a.promise({elem:e,props:g.extend({},t),opts:g.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:_t||It(),duration:n.duration,tweens:[],createTween:function(t,n,r){var i=g.Tween(e,l.opts,t,n,l.opts.specialEasing[t]||l.opts.easing);return l.tweens.push(i),i},stop:function(t){for(var n=0,r=t?l.tweens.length:0;n<r;n++)l.tweens[n].run(1);return t?a.resolveWith(e,[l,t]):a.rejectWith(e,[l,t]),this}}),u=l.props;for(!function(e,t){var n,r,i,o,a;for(n in e)if(r=g.camelCase(n),i=t[r],o=e[n],g.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(a=g.cssHooks[r])&&"expand"in a)for(n in o=a.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,l.opts.specialEasing);i<o;i++)if(r=Rt[i].call(l,e,u,l.opts))return r;return function(e,t){g.each(t,function(t,n){for(var r=($t[t]||[]).concat($t["*"]),i=0,o=r.length;i<o;i++)if(r[i].call(e,t,n))return})}(l,u),g.isFunction(l.opts.start)&&l.opts.start.call(e,l),g.fx.timer(g.extend(s,{anim:l,queue:l.opts.queue,elem:e})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}function Xt(e,t,n,r,i){return new Xt.prototype.init(e,t,n,r,i)}function Ut(e,t){var n,r={height:e},i=0;for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=Je[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}g.Animation=g.extend(zt,{tweener:function(e,t){g.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,r=0,i=e.length;r<i;r++)n=e[r],$t[n]=$t[n]||[],$t[n].unshift(t)},prefilter:function(e,t){t?Rt.unshift(e):Rt.push(e)}}),g.Tween=Xt,Xt.prototype={constructor:Xt,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(g.cssNumber[n]?"":"px")},cur:function(){var e=Xt.propHooks[this.prop];return e&&e.get?e.get(this):Xt.propHooks._default.get(this)},run:function(e){var t,n=Xt.propHooks[this.prop];return this.options.duration?this.pos=t=g.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Xt.propHooks._default.set(this),this}},Xt.prototype.init.prototype=Xt.prototype,Xt.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=g.css(e.elem,e.prop,!1,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){g.fx.step[e.prop]?g.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[g.cssProps[e.prop]]||g.cssHooks[e.prop])?g.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Xt.propHooks.scrollTop=Xt.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},g.each(["toggle","show","hide"],function(e,t){var n=g.fn[t];g.fn[t]=function(r,i,o){return null==r||"boolean"==typeof r||!e&&g.isFunction(r)&&g.isFunction(i)?n.apply(this,arguments):this.animate(Ut(t,!0),r,i,o)}}),g.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Qe).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=g.isEmptyObject(e),o=g.speed(t,n,r),a=function(){var t=zt(this,g.extend({},e),o);i&&t.stop(!0)};return i||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(e,n,r){var i=function(e){var t=e.stop;delete e.stop,t(r)};return"string"!=typeof e&&(r=n,n=e,e=t),n&&!1!==e&&this.queue(e||"fx",[]),this.each(function(){var t=!0,n=null!=e&&e+"queueHooks",o=g.timers,a=g._data(this);if(n)a[n]&&a[n].stop&&i(a[n]);else for(n in a)a[n]&&a[n].stop&&Pt.test(n)&&i(a[n]);for(n=o.length;n--;)o[n].elem!==this||null!=e&&o[n].queue!==e||(o[n].anim.stop(r),t=!1,o.splice(n,1));!t&&r||g.dequeue(this,e)})}}),g.each({slideDown:Ut("show"),slideUp:Ut("hide"),slideToggle:Ut("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){g.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),g.speed=function(e,t,n){var r=e&&"object"==typeof e?g.extend({},e):{complete:n||!n&&t||g.isFunction(e)&&e,duration:e,easing:n&&t||t&&!g.isFunction(t)&&t};return r.duration=g.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in g.fx.speeds?g.fx.speeds[r.duration]:g.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){g.isFunction(r.old)&&r.old.call(this),r.queue&&g.dequeue(this,r.queue)},r},g.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},g.timers=[],g.fx=Xt.prototype.init,g.fx.tick=function(){for(var e,t=g.timers,n=0;n<t.length;n++)(e=t[n])()||t[n]!==e||t.splice(n--,1);t.length||g.fx.stop()},g.fx.timer=function(e){e()&&g.timers.push(e)&&!qt&&(qt=setInterval(g.fx.tick,g.fx.interval))},g.fx.interval=13,g.fx.stop=function(){clearInterval(qt),qt=null},g.fx.speeds={slow:600,fast:200,_default:400},g.fx.step={},g.expr&&g.expr.filters&&(g.expr.filters.animated=function(e){return g.grep(g.timers,function(t){return e===t.elem}).length});var Yt=/^(?:body|html)$/i;function Jt(e){return g.isWindow(e)?e:9===e.nodeType&&(e.defaultView||e.parentWindow)}g.fn.offset=function(e){if(arguments.length)return e===t?this:this.each(function(t){g.offset.setOffset(this,e,t)});var n,r,i,o,a,s,l,u={top:0,left:0},c=this[0],f=c&&c.ownerDocument;return f?(r=f.body)===c?g.offset.bodyOffset(c):(n=f.documentElement,g.contains(n,c)?(void 0!==c.getBoundingClientRect&&(u=c.getBoundingClientRect()),i=Jt(f),o=n.clientTop||r.clientTop||0,a=n.clientLeft||r.clientLeft||0,s=i.pageYOffset||n.scrollTop,l=i.pageXOffset||n.scrollLeft,{top:u.top+s-o,left:u.left+l-a}):u):void 0},g.offset={bodyOffset:function(e){var t=e.offsetTop,n=e.offsetLeft;return g.support.doesNotIncludeMarginInBodyOffset&&(t+=parseFloat(g.css(e,"marginTop"))||0,n+=parseFloat(g.css(e,"marginLeft"))||0),{top:t,left:n}},setOffset:function(e,t,n){var r=g.css(e,"position");"static"===r&&(e.style.position="relative");var i,o,a=g(e),s=a.offset(),l=g.css(e,"top"),u=g.css(e,"left"),c={},f={};("absolute"===r||"fixed"===r)&&g.inArray("auto",[l,u])>-1?(i=(f=a.position()).top,o=f.left):(i=parseFloat(l)||0,o=parseFloat(u)||0),g.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(c.top=t.top-s.top+i),null!=t.left&&(c.left=t.left-s.left+o),"using"in t?t.using.call(e,c):a.css(c)}},g.fn.extend({position:function(e){if(this[0]){var t=this[0],n=e||this.offsetParent(),r=this.offset(),i=Yt.test(n[0].nodeName)?{top:0,left:0}:n.offset();return e||(r.top-=parseFloat(g.css(t,"marginTop"))||0,r.left-=parseFloat(g.css(t,"marginLeft"))||0),i.top+=parseFloat(g.css(n[0],"borderTopWidth"))||0,i.left+=parseFloat(g.css(n[0],"borderLeftWidth"))||0,{top:r.top-i.top,left:r.left-i.left}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||i.body;e&&!Yt.test(e.nodeName)&&"static"===g.css(e,"position");)e=e.offsetParent;return e||i.body})}}),g.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var r=/Y/.test(n);g.fn[e]=function(i){return g.access(this,function(e,i,o){var a=Jt(e);if(o===t)return a?n in a?a[n]:a.document.documentElement[i]:e[i];a?a.scrollTo(r?g(a).scrollLeft():o,r?o:g(a).scrollTop()):e[i]=o},e,i,arguments.length,null)}}),g.each({Height:"height",Width:"width"},function(e,n){g.each({padding:"inner"+e,content:n,"":"outer"+e},function(r,i){g.fn[i]=function(i,o){var a=arguments.length&&(r||"boolean"!=typeof i),s=r||(!0===i||!0===o?"margin":"border");return g.access(this,function(n,r,i){var o;return g.isWindow(n)?n.document.documentElement["client"+e]:9===n.nodeType?(o=n.documentElement,Math.max(n.body["scroll"+e],o["scroll"+e],n.body["offset"+e],o["offset"+e],o["client"+e])):i===t?g.css(n,r,i,s):g.style(n,r,i,s)},n,a?i:t,a,null)}})}),e.jQuery=e.$=g,"function"==typeof define&&define.amd&&define.amd.jQuery&&define("jquery",[],function(){return g})}(window);