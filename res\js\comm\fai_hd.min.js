try{$.browser.msie&&"6.0"==$.browser.version&&document.execCommand("BackgroundImageCache",!1,!0)}catch(e){}if("undefined"==typeof hdFai){hdFai={};var _top=_top||top;hdFai.top=_top}!function(hdFai){var arr_proto=Array.prototype,arr_slice=arr_proto.slice;hdFai.isNumber=function(e){return!/[^\d]/.test(e)},hdFai.isChinese=function(e){return!(e<"一"||e>"龥")},hdFai.isEmail=function(e){return/^[a-zA-Z0-9_\&\-\.\+]+@[a-zA-Z0-9][a-zA-Z0-9_\-]*\.[a-zA-Z0-9\-][a-zA-Z0-9_\-\.]*[a-zA-Z0-9 ]$/.test(e)},hdFai.isUrl=function(e,t){if(void 0===t&&(t=!0),t&&e.length>=1&&"/"==e.charAt(0))return!0;if(t&&e.length>=1&&"#"==e.charAt(0))return!0;return/^(\w+:).+/.test(e)},hdFai.fixUrl=function(e,t){return hdFai.isUrl(e,t)?e:"http://"+e},hdFai.getUrlParam=function(e,t){var i,n=e.substring(e.indexOf("?")+1,e.indexOf("#")).split("&");return $.each(n,function(e,n){if(decodeURIComponent(n.substring(0,n.indexOf("=")))===t)return i=decodeURIComponent(n.substring(n.indexOf("=")+1,n.length)),!1}),i},hdFai.isLetter=function(e){return!(e<"a"||e>"z")||!(e<"A"||e>"Z")},hdFai.compareObjNew=function(e,t,i,n,r){var o=e[n],a=t[n];if(void 0!==o&&void 0!==a)return"number"===r?hdFai.ber(o,a,i):hdFai.isIE6()||hdFai.isIE7()||hdFai.isIE8()?hdFai.compareObjLocale(e,t,i,n):hdFai.compareStrings(o,a,i)},hdFai.compareStrings=function(e,t,i){"number"!=typeof e&&"boolean"!=typeof e||(e=e.toString()),"number"!=typeof t&&"boolean"!=typeof t||(t=t.toString());for(var n={str1:e,str2:t,len1:e.length,len2:t.length,pos1:0,pos2:0},r=0;0==r&&n.pos1<n.len1&&n.pos2<n.len2;){var o=n.str1.charAt(n.pos1),a=n.str2.charAt(n.pos2);r=hdFai.isDigit(o)?hdFai.isDigit(a)?s(n):-1:hdFai.isChinese(o)?hdFai.isChinese(a)?h(n):1:hdFai.isLetter(o)?hdFai.isLetter(a)||hdFai.isChinese(a)?d(n,!0):1:hdFai.isDigit(a)?1:hdFai.isLetter(a)||hdFai.isChinese(a)?-1:d(n,!1),n.pos1++,n.pos2++}return"asc"==i?0==r?n.len1-n.len2:r:-(0==r?n.len1-n.len2:r);function s(e){for(var t=e.pos1+1;t<e.len1&&hdFai.isDigit(e.str1.charAt(t));)t++;for(var i=t-e.pos1;e.pos1<t&&"0"==e.str1.charAt(e.pos1);)e.pos1++;for(var n=e.pos2+1;n<e.len2&&hdFai.isDigit(e.str2.charAt(n));)n++;for(var r=n-e.pos2;e.pos2<n&&"0"==e.str2.charAt(e.pos2);)e.pos2++;var o=t-e.pos1-(n-e.pos2);if(0!=o)return o;for(;e.pos1<t&&e.pos2<n;)if(0!=(o=e.str1.charCodeAt(e.pos1++)-e.str2.charCodeAt(e.pos2++)))return o;return e.pos1--,e.pos2--,r-i}function d(e,t){var i=e.str1.charAt(e.pos1),n=e.str2.charAt(e.pos2);return i==n?0:(t&&(i=i.toUpperCase())!=(n=n.toUpperCase())&&(i=i.toLowerCase(),n=n.toLowerCase()),i.charCodeAt(0)-n.charCodeAt(0))}function h(e){var t,i,n=e.str1.charAt(e.pos1),r=e.str2.charAt(e.pos2);return n==r?0:"undefined"!=typeof Pinyin?(t=Pinyin.getStringStriped(n,Pinyin.mode.LOWERCASE,!0),i=Pinyin.getStringStriped(r,Pinyin.mode.LOWERCASE,!0),-hdFai.compareStrings(t,i)):(t=n.charCodeAt(0))==(i=r.charCodeAt(0))?0:t-i}},hdFai.popupWindow=function(e){var t={title:"",width:500,height:300,frameSrcUrl:"about:_blank",frameScrolling:"auto",bannerDisplay:!0,framePadding:!0,opacity:"0.3",displayBg:!0,bgClose:!1,closeBtnClass:""};t=$.extend(t,e);var i=parseInt(t.width),n=parseInt(t.height),r=hdFai.top.document.documentElement.clientWidth;$.browser.msie||(r=hdFai.top.document.body.clientWidth);var o=hdFai.top.document.documentElement.clientHeight,a=(r-i)/2;null!=t.leftMar&&(a=parseInt(t.leftMar));var s=80;t.bannerDisplay||(s=0);var d=(o-n-s)/2;null!=t.topMar&&(d=parseInt(t.topMar));var h="",p="",l="";t.bannerDisplay||(p="display:none;",h="background:none;",t.closeBtnClass||(t.closeBtnClass="formX_old")),t.framePadding||(h+="padding:0;",l='allowtransparency="true"');var c=parseInt(1e4*Math.random()),u="<iframe "+l+' id="popupWindowIframe'+c+'" class="popupWindowIframe" src="" frameborder="0" scrolling="'+t.frameScrolling+'" style="width:100%;height:100%;"></iframe>',f=!0;null!=t.divId&&(f=!1,u=$(t.divId).html()),null!=t.divContent&&(f=!1,u=t.divContent),t.displayBg&&hdFai.bg(c,t.opacity);var g="",m=hdFai.top.$("body").scrollTop();0==m&&(m=hdFai.top.$("html").scrollTop());var F="left:"+a+"px; top:"+(d+m)+"px;";(hdFai.isIE6()||hdFai.isIE7())&&(F+="width:"+i+"px;");var v="position:relative;width:"+i+"px;height:"+n+"px;";$.browser.msie;g=['<div id="popupWindow'+c+'" class="formDialog" style="'+F+'">','<div class="formTL" style=\''+p+'\'><div class="formTR"><div class="formTC">'+t.title+"</div></div></div>",'<div class="formBL" style=\''+h+"'>",'<div class="formBR" style=\''+h+"'>",'<div class="formBC" id="formBC'+c+'" style="height:auto;'+h+'">','<div class="formMSG" style="'+v+'">',u,"</div>","<table cellpadding='0' cellspacing='0' class='formBtns'>","<tr><td align='center' style='padding:15px 0px;'></td></tr>","</table>","</div>",'<div id="waitingP'+c+'" class="waitingP" style="height:auto;"></div>',"</div>","</div>",'<a href="javascript:;" class="formX '+t.closeBtnClass+"\" hidefocus='true' onclick='return false;'></a>","</div>"];var w=hdFai.top.$(g.join("")).appendTo("body");if(hdFai.isIE6()||hdFai.isIE7()){var y=w.find(".formBL"),b=hdFai.getCssInt(y,"padding-left")+hdFai.getCssInt(y,"padding-right")+hdFai.getCssInt(y,"border-left-width")+hdFai.getCssInt(y,"border-right-width"),I=w.find(".formBR"),E=hdFai.getCssInt(I,"padding-left")+hdFai.getCssInt(I,"padding-right")+hdFai.getCssInt(I,"border-left-width")+hdFai.getCssInt(I,"border-right-width"),x=w.find(".formBC"),C=hdFai.getCssInt(x,"padding-left")+hdFai.getCssInt(x,"padding-right")+hdFai.getCssInt(x,"border-left-width")+hdFai.getCssInt(x,"border-right-width");w.css("width",i+b+E+C+"px")}var S=40;if(t.bannerDisplay||(S=0),w.height()+S>o-20){var A=w.height()+S-w.find(".formMSG").height();w.find(".formMSG").css("height",o-20-A+"px"),w.css("top",10+m+"px")}return hdFai.isIE6()&&$("#fixFlashIframe"+c).attr("height",w.height()+"px"),f?(hdFai.top.$("#waitingP"+c).height(hdFai.top.$("#formBC"+c).height()),hdFai.top.$("#waitingP"+c).width(hdFai.top.$("#formBC"+c).width())):hdFai.top.$("#waitingP"+c).hide(),null!=t.divInit&&t.divInit(c),hdFai.top.$("#popupWindow"+c).ready(function(){if(f){var e="popupID="+c;hdFai.top.$("#popupWindowIframe"+c).attr("src",hdFai.addUrlParams(t.frameSrcUrl,e)).load(function(){hdFai.top.$("#waitingP"+c).hide()})}w.draggable({start:function(){hdFai.top.$("body").disableSelection(),hdFai.top.$("#colorpanel").remove(),hdFai.top.$(".faiColorPicker").remove()},handle:".formTL",stop:function(){hdFai.top.$("body").enableSelection()}}),w.find(".formX").bind("click",function(){return hdFai.closePopupWindow(c),!1}),w.find(".formTL").disableSelection(),t.bgClose&&hdFai.top.$("#popupBg"+c).bind("click",function(){return hdFai.closePopupWindow(c),!1})}),hdFai.isNull(hdFai.top._popupOptions)&&(hdFai.top._popupOptions={}),hdFai.isNull(hdFai.top._popupOptions["popup"+c])&&(hdFai.top._popupOptions["popup"+c]={}),hdFai.isNull(e.callArgs)||(hdFai.top._popupOptions["popup"+c].callArgs=e.callArgs),hdFai.top._popupOptions["popup"+c].options=e,hdFai.top._popupOptions["popup"+c].change=!1,c},hdFai.setPopupWindowChange=function(e,t){hdFai.isNull(hdFai.top._popupOptions)||hdFai.isNull(hdFai.top._popupOptions["popup"+e])||(hdFai.top._popupOptions["popup"+e].change=t)},hdFai.closePopupWindow=function(e,t){if(e)try{if(hdFai.isNull(hdFai.top._popupOptions["popup"+e]))return;var i=hdFai.top._popupOptions["popup"+e];if(i.change&&!window.confirm("您的修改尚未保存，确定要离开吗？"))return;if(i.refresh)return void hdFai.top.location.reload();hdFai.top.hdFai.removeAllIng(!1);var n=i.options;hdFai.isNull(n.closeFunc)||(t?n.closeFunc(t):n.closeFunc(hdFai.top._popupOptions["popup"+e].closeArgs)),hdFai.top._popupOptions["popup"+e]={};var r=hdFai.top.$("#popupWindow"+e);n.animate&&hdFai.top.hdFai.closePopupWindowAnimate(r,n.animateTarget,n.animateOnClose),hdFai.top.setTimeout("hdFai.closePopupWindow_Internal('"+e+"')")}catch(e){}else hdFai.removeBg(),hdFai.top.$(".formDialog").remove()},hdFai.closePopupWindow_Internal=function(e){if(void 0===e){if($.browser.msie&&10==$.browser.version)if((t=hdFai.top.$(".formDialog").find(".popupWindowIframe")[0])&&(popupWindowIframeWindow=t.contentWindow,popupWindowIframeWindow))try{popupWindowIframeWindow.swfObj&&popupWindowIframeWindow.swfObj.destroy(),popupWindowIframeWindow.editor&&popupWindowIframeWindow.editor.swfObj&&popupWindowIframeWindow.editor.swfObj.destroy()}catch(e){}hdFai.top.$(".popupBg").remove(),hdFai.top.$(".formDialog").remove()}else{var t;if($.browser.msie&&10==$.browser.version)if((t=hdFai.top.document.getElementById("popupWindowIframe"+e))&&(popupWindowIframeWindow=t.contentWindow,popupWindowIframeWindow))try{popupWindowIframeWindow.swfObj&&popupWindowIframeWindow.swfObj.destroy(),popupWindowIframeWindow.editor&&popupWindowIframeWindow.editor.swfObj&&popupWindowIframeWindow.editor.swfObj.destroy()}catch(e){}hdFai.top.hdFai.removeBg(e),hdFai.top.$("#popupWindowIframe"+e).remove(),hdFai.top.$("#popupWindow"+e).remove()}},hdFai.closePopupWindowAnimate=function(e,t,i){var n=$("<div>");hdFai.top.$("body").append(n),n.css({border:"1px solid #ff4400",position:"absolute","z-index":"9999",top:e.offset().top,left:e.offset().left,height:e.height()+"px",width:e.width()+"px"});var r=hdFai.top.$("body").find(t);n.animate({top:r.offset().top+"px",left:r.offset().left+"px",width:r.width()+"px",height:r.height()+"px"},"slow",function(){"function"==typeof i&&i(),n.remove()})},hdFai.addPopupWindowBtn=function(e,t){var i=hdFai.top.$("#popupWindow"+e);i.find(".formBtns").show();var n="popup"+e+t.id,r=i.find(".formBtns td"),o=r.find("#"+n);if(o.length>0&&o.remove(),"help"!=t.click)if(void 0!==t.extClass){var a=t.extClass;hdFai.top.$("<input id='"+n+"' type='button' value='"+t.text+"' class='abutton faiButton' extClass='"+a+"'></input>").appendTo(r)}else hdFai.top.$("<input id='"+n+"' type='button' value='"+t.text+"' class='abutton faiButton'></input>").appendTo(r);"function"==typeof(o=r.find("#"+n)).faiButton&&o.faiButton(),t.callback&&"[object Function]"===Object.prototype.toString.call(t.callback)&&o.click(function(){t.callback(),hdFai.top.hdFai.closePopupWindow(e)}),"close"==t.click?o.click(function(){hdFai.top.hdFai.closePopupWindow(e)}):"help"==t.click?0==i.find("a.formH").length&&i.append("<a class='formH' href='"+t.helpLink+"' target='_blank' title='"+t.text+"'></a>"):o.click(t.click),t.disable&&(o.attr("disabled",!0),o.faiButton("disable"))},hdFai.enablePopupWindowBtn=function(e,t,i){t="popup"+e+t;var n=hdFai.top.$("#popupWindow"+e).find("#"+t);i?(n.removeAttr("disabled"),n.faiButton("enable")):(n.attr("disabled",!0),n.faiButton("disable"))},hdFai.bg=function(e,t){var i,n="";if(t&&(n="filter: alpha(opacity="+100*t+"); opacity:"+t+";"),hdFai.isIE6()){var r=hdFai.top.$("html").scrollTop();hdFai.top.$("html").data("scrollTop",r),hdFai.top.$("html").scrollTop(0);r=hdFai.top.$("body").scrollTop();hdFai.top.$("body").data("scrollTop",r),hdFai.top.$("body").scrollTop(0),hdFai.top.$("html").data("overflow-x",hdFai.top.$("html").css("overflow-x")),hdFai.top.$("html").data("overflow-y",hdFai.top.$("html").css("overflow-y")),hdFai.top.$("html").css("overflow-x","hidden"),hdFai.top.$("html").css("overflow-y","hidden"),hdFai.top.$("body").data("overflow-x",hdFai.top.$("body").css("overflow-x")),hdFai.top.$("body").data("overflow-y",hdFai.top.$("body").css("overflow-y")),hdFai.top.$("body").css("overflow-x","hidden"),hdFai.top.$("body").css("overflow-y","hidden")}(hdFai.isIE6()||hdFai.isIE7()||hdFai.isIE8())&&hdFai.top.$("html").css("filter")&&(hdFai.top.$("html").data("filter",hdFai.top.$("html").css("filter")),hdFai.top.$("html").css("filter","none")),i='<div id="popupBg'+e+'" class="popupBg" style=\''+n+"' >"+($.browser.msie&&6==$.browser.version?'<iframe id="fixSelectIframe'+e+'" wmode="transparent" style="filter: alpha(opacity=0);opacity: 0;" class="popupBg" style="z-index:-111" src="javascript:"></iframe>':"")+"</div>",hdFai.top.$(i).appendTo("body"),hdFai.stopInterval(null)},hdFai.startInterval=function(e){if(!hdFai.isNull(hdFai.intervalFunc))for(var t=0;t<hdFai.intervalFunc.length;++t){var i=hdFai.intervalFunc[t];null!=e&&i.id!=e||(i.timer&&clearInterval(i.timer),1==i.type?i.timer=setInterval(i.func,i.interval):i.timer=setTimeout(i.func,i.interval))}},hdFai.stopInterval=function(e){if(!hdFai.isNull(hdFai.intervalFunc))for(var t=0;t<hdFai.intervalFunc.length;++t){var i=hdFai.intervalFunc[t];null!=e&&i.id!=e||i.timer&&clearInterval(i.timer)}},hdFai.removeBg=function(e){e?hdFai.top.$("#popupBg"+e).remove():hdFai.top.$(".popupBg").remove(),hdFai.isIE6()&&(hdFai.top.$("html").css("overflow-x",hdFai.top.$("html").data("overflow-x")),hdFai.top.$("html").css("overflow-y",hdFai.top.$("html").data("overflow-y")),hdFai.top.$("body").css("overflow-x",hdFai.top.$("body").data("overflow-x")),hdFai.top.$("body").css("overflow-y",hdFai.top.$("body").data("overflow-y")),hdFai.top.$("html").scrollTop(hdFai.top.$("html").data("scrollTop")),hdFai.top.$("body").scrollTop(hdFai.top.$("body").data("scrollTop"))),(hdFai.isIE6()||hdFai.isIE7()||hdFai.isIE8())&&hdFai.top.$("html").data("filter")&&hdFai.top.$("html").css("filter",hdFai.top.$("html").data("filter")),hdFai.startInterval(null)},function(e){e.fn.numeric=function(t,i){"boolean"==typeof t&&(t={decimal:t}),void 0===(t=t||{}).negative&&(t.negative=!0);var n=!1===t.decimal?"":t.decimal||".",r=!0===t.negative;return i="function"==typeof i?i:function(){},this.data("numeric.decimal",n).data("numeric.negative",r).data("numeric.callback",i).keypress(e.fn.numeric.keypress).keyup(e.fn.numeric.keyup).blur(e.fn.numeric.blur)},e.fn.numeric.keypress=function(t){var i=e.data(this,"numeric.decimal"),n=e.data(this,"numeric.negative"),r=t.charCode?t.charCode:t.keyCode?t.keyCode:0;if(13==r&&"input"==this.nodeName.toLowerCase())return!0;if(13==r)return!1;var o=!1;if(t.ctrlKey&&97==r||t.ctrlKey&&65==r)return!0;if(t.ctrlKey&&120==r||t.ctrlKey&&88==r)return!0;if(t.ctrlKey&&99==r||t.ctrlKey&&67==r)return!0;if(t.ctrlKey&&122==r||t.ctrlKey&&90==r)return!0;if(t.ctrlKey&&118==r||t.ctrlKey&&86==r||t.shiftKey&&45==r)return!0;if(r<48||r>57){var a=e(this).val();if(0!==a.indexOf("-")&&n&&45==r&&(0===a.length||0===parseInt(e.fn.getSelectionStart(this),10)))return!0;i&&r==i.charCodeAt(0)&&-1!=a.indexOf(i)&&(o=!1),8!=r&&9!=r&&13!=r&&35!=r&&36!=r&&37!=r&&39!=r&&46!=r?o=!1:void 0!==t.charCode&&(t.keyCode==t.which&&0!==t.which?(o=!0,46==t.which&&(o=!1)):0!==t.keyCode&&0===t.charCode&&0===t.which&&(o=!0)),i&&r==i.charCodeAt(0)&&(o=-1==a.indexOf(i))}else o=!0;return o},e.fn.numeric.keyup=function(t){var i=e(this).val();if(i&&i.length>0){var n=e.fn.getSelectionStart(this),r=e.fn.getSelectionEnd(this),o=e.data(this,"numeric.decimal"),a=e.data(this,"numeric.negative");if(""!==o&&null!==o){var s=i.indexOf(o);0===s&&(this.value="0"+i),1==s&&"-"==i.charAt(0)&&(this.value="-0"+i.substring(1)),i=this.value}for(var d=[0,1,2,3,4,5,6,7,8,9,"-",o],h=i.length,p=h-1;p>=0;p--){var l=i.charAt(p);0!==p&&"-"==l?i=i.substring(0,p)+i.substring(p+1):0!==p||a||"-"!=l||(i=i.substring(1));for(var c=!1,u=0;u<d.length;u++)if(l==d[u]){c=!0;break}c&&" "!=l||(i=i.substring(0,p)+i.substring(p+1))}var f=i.indexOf(o);if(f>0)for(var g=h-1;g>f;g--){i.charAt(g)==o&&(i=i.substring(0,g)+i.substring(g+1))}this.value=i,e.fn.setSelection(this,[n,r])}},e.fn.numeric.blur=function(){var t=e.data(this,"numeric.decimal"),i=e.data(this,"numeric.callback"),n=this.value;""!==n&&(new RegExp("^\\d+$|^\\d*"+t+"\\d+$").exec(n)||i.apply(this))},e.fn.removeNumeric=function(){return this.data("numeric.decimal",null).data("numeric.negative",null).data("numeric.callback",null).unbind("keypress",e.fn.numeric.keypress).unbind("blur",e.fn.numeric.blur)},e.fn.getSelectionStart=function(e){if(e.createTextRange){var t=document.selection.createRange().duplicate();return t.moveEnd("character",e.value.length),""===t.text?e.value.length:e.value.lastIndexOf(t.text)}return e.selectionStart},e.fn.getSelectionEnd=function(e){if(e.createTextRange){var t=document.selection.createRange().duplicate();return t.moveStart("character",-e.value.length),t.text.length}return e.selectionEnd},e.fn.setSelection=function(e,t){if("number"==typeof t&&(t=[t,t]),t&&t.constructor==Array&&2==t.length)if(e.createTextRange){var i=e.createTextRange();i.collapse(!0),i.moveStart("character",t[0]),i.moveEnd("character",t[1]),i.select()}else e.setSelectionRange&&(e.focus(),e.setSelectionRange(t[0],t[1]))}}(jQuery),hdFai.Img={},hdFai.Img={MODE_SCALE_FILL:1,MODE_SCALE_WIDTH:2,MODE_SCALE_HEIGHT:3,MODE_SCALE_DEFLATE_WIDTH:4,MODE_SCALE_DEFLATE_HEIGHT:5,MODE_SCALE_DEFLATE_FILL:6,MODE_SCALE_DEFLATE_MAX:7},hdFai.Img.optimize=function(e,t,i){var n=new Image;n.src=e.src;n.width,n.height;n.onload=function(){var r=n.width,o=n.height,a=hdFai.Img.calcSize(r,o,t.width,t.height,t.mode);e.width=a.width,e.height=a.height,1==t.display?e.style.display="inline":2==t.display?e.style.display="none":e.style.display="block",i&&i(e,{imgWidth:r,imgHeight:o,width:e.width,height:e.height})}},hdFai.Img.calcSize=function(e,t,i,n,r){var o={width:e,height:t};if(r==hdFai.Img.MODE_SCALE_FILL)(a=e/i)>(s=t/n)?(o.width=i,o.height=t/a):(o.width=e/s,o.height=n);else if(r==hdFai.Img.MODE_SCALE_WIDTH){var a=e/i;o.width=i,o.height=t/a}else if(r==hdFai.Img.MODE_SCALE_HEIGHT){var s=t/n;o.width=e/s,o.height=n}else if(r==hdFai.Img.MODE_SCALE_DEFLATE_WIDTH){(a=e/i)>1&&(o.width=i,o.height=t/a)}else if(r==hdFai.Img.MODE_SCALE_DEFLATE_HEIGHT){(s=t/n)>1&&(o.width=e/s,o.height=n)}else if(r==hdFai.Img.MODE_SCALE_DEFLATE_FILL){(a=e/i)>(s=t/n)?a>1&&(o.width=i,o.height=t/a):s>1&&(o.width=e/s,o.height=n)}else if(r==hdFai.Img.MODE_SCALE_DEFLATE_MAX){if(e>i&&t>n)(a=e/i)<(s=t/n)?(o.width=i,o.height=t/a):(o.width=e/s,o.height=n)}return o.width=Math.floor(o.width),o.height=Math.floor(o.height),0==o.width&&(o.width=1),0==o.height&&(o.height=1),o},hdFai.checkBit=function(e,t){var i=!0;if((e>2147483647||e<0||t>2147483647||t<0)&&(i=!1),i)return(e&t)==t;var n=e.toString(2),r=t.toString(2);if(n.length>62||r.length>62)return alert("Does not support more than 62 bit. flagBinary.length="+n.length+",bitFlagBinary.length"+r.length+"."),!1;var o=flagLow=bitFlagHight=bitFlagLow=0;if(n.length>31){var a=n.slice(0,n.length-31),s=n.slice(n.length-31);o=parseInt(a,"2"),flagLow=parseInt(s,"2")}else flagLow=parseInt(n.slice(0,n.length),"2");if(r.length>31){a=r.slice(0,r.length-31),s=r.slice(r.length-31);bitFlagHight=parseInt(a,"2"),bitFlagLow=parseInt(s,"2")}else bitFlagLow=parseInt(r.slice(0,r.length),"2");var d=(flagLow&bitFlagLow)==bitFlagLow;return d&&(d=(o&bitFlagHight)==bitFlagHight),d},hdFai.isNumberKey=function(e,t){return $.browser.msie?!(!t||45!=event.keyCode)||(event.keyCode>47&&event.keyCode<58||8==event.keyCode):!(!t||45!=e.which)||(e.which>47&&e.which<58||8==e.which)},hdFai.isFloatKey=function(e){return $.browser.msie?event.keyCode>47&&event.keyCode<58||8==event.keyCode||46==event.keyCode:e.which>47&&e.which<58||8==e.which||46==e.which},hdFai.isDigit=function(e){return!(e<"0"||e>"9")},hdFai.isNull=function(e){return void 0===e||null==e},hdFai.isIE=function(){return!!$.browser.msie},hdFai.isIE6=function(){return!(!$.browser.msie||"6.0"!=$.browser.version)},hdFai.isIE7=function(){return!(!$.browser.msie||"7.0"!=$.browser.version)},hdFai.isIE8=function(){return!(!$.browser.msie||"8.0"!=$.browser.version)},hdFai.isIE9=function(){return!(!$.browser.msie||"9.0"!=$.browser.version)},hdFai.isIE10=function(){return!(!$.browser.msie||"10.0"!=$.browser.version)},hdFai.isSafari=function(){return!!$.browser.safari},hdFai.isWebkit=function(){return!!$.browser.webkit},hdFai.isChrome=function(){return!!$.browser.chrome},hdFai.isMozilla=function(){return!!$.browser.mozilla},hdFai.isAppleWebKit=function(){return window.navigator.userAgent.indexOf("AppleWebKit")>=0},hdFai.isOpera=function(){return!(!$.browser.opera&&!$.browser.opr)},hdFai.isAndroid=function(){return!!$.browser.android},hdFai.isIpad=function(){return!!$.browser.ipad},hdFai.isIphone=function(){return!!$.browser.iphone},hdFai.BrowserType={UNKNOWN:0,SPIDER:1,CHROME:2,FIREFOX:3,MSIE8:4,MSIE7:5,MSIE6:6,MSIE9:7,SAFARI:8,MSIE10:9,MSIE11:10,OPERA:11,APPLE_WEBKIT:12},hdFai.getBrowserType=function(){return hdFai.isIE6()?hdFai.BrowserType.MSIE6:hdFai.isIE7()?hdFai.BrowserType.MSIE7:hdFai.isIE8()?hdFai.BrowserType.MSIE8:hdFai.isIE9()?hdFai.BrowserType.MSIE9:hdFai.isIE10()?hdFai.BrowserType.MSIE10:hdFai.isIE11()?hdFai.BrowserType.MSIE11:hdFai.isMozilla()?hdFai.BrowserType.FIREFOX:hdFai.isOpera()?hdFai.BrowserType.OPERA:hdFai.isChrome()?hdFai.BrowserType.CHROME:hdFai.isSafari()?hdFai.BrowserType.SAFARI:hdFai.isAppleWebKit()?hdFai.BrowserType.APPLE_WEBKIT:hdFai.BrowserType.UNKNOWN},hdFai.encodeHtml=function(e){return e&&e.replace?e.replace(/&/g,"&amp;").replace(/ /g,"&nbsp;").replace(/\b&nbsp;+/g," ").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\\/g,"&#92;").replace(/\'/g,"&#39;").replace(/\"/g,"&quot;").replace(/\n/g,"<br/>").replace(/\r/g,""):e},hdFai.decodeHtml=function(e){return e&&e.replace?e.replace(/&nbsp;/gi," ").replace(/&lt;/gi,"<").replace(/&gt;/g,">").replace(/&#92;/gi,"\\").replace(/&#39;/gi,"'").replace(/&quot;/gi,'"').replace(/\<br\/\>/gi,"\n").replace(/&amp;/gi,"&"):e},hdFai.encodeHtmlJs=function(e){return e&&e.replace?e.replace(/\\/g,"\\\\").replace(/\'/g,"\\x27").replace(/\"/g,"\\x22").replace(/\n/g,"\\n").replace(/</g,"\\x3c").replace(/>/g,"\\x3e"):e},hdFai.encodeHtmlAttr=function(e){return e&&e.replace?e.replace(/\"/g,"&#x22;").replace(/\'/g,"&#x27;").replace(/</g,"&#x3c;").replace(/>/g,"&#x3e;").replace(/&/g,"&#x26;").replace(/\\/g,"&#5c;"):e},hdFai.encodeUrl=function(e){return void 0===e?"":encodeURIComponent(e)},hdFai.decodeUrl=function(e){return void 0===e?"":decodeURIComponent(e)},hdFai.ing=function(e,t){if(window!==top&&top.$(".bg-mask").is(":visible"))return top.hdFai.ing.apply(top.hdFai,arguments);var i=null==e||""==e?"正在处理...":e;hdFai.top.document.body.clientWidth,hdFai.top.document.body.clientHeight;0==hdFai.top.$("#ing").length&&hdFai.top.$("<div id='ing' style='position:fixed; top:50px; left: 50%; margin:0px auto; width:auto;  height:auto; z-index:9999;transition: opacity ease .6s; -moz-transition: opacity ease .6s; -webkit-transition: opacity ease .6s; -o-transition: opacity ease .6s; opacity: 0; -webkit-opacity: 0; -moz-opacity: 0; -khtml-opacity: 0; filter:alpha(opacity=0);'></div>").appendTo("body");var n=hdFai.top.$("#ing"),r=hdFai.top.$("body").scrollTop();hdFai.isIE()&&0==r&&(r=hdFai.top.$("html").scrollTop()),r>0&&n.css("top",r+50+"px");var o=parseInt(1e4*Math.random()),a="";a+='<div id="'+o+'" class="tips"><div class="msg">'+i+"</div><div class='close' onclick=\"hdFai.top.hdFai.removeIng(false, "+o+');"></div></div>',n.find(".tips").remove(),hdFai.top.$(a).appendTo(n);var s=hdFai.top.$(n).width();hdFai.top.$(n).css("left",(hdFai.top.document.documentElement.clientWidth-s)/2),t&&hdFai.top.hdFai.removeIng(t,o),hdFai.isIE6()||hdFai.isIE7()||hdFai.isIE8()?hdFai.top.$("#ing").animate({opacity:1,filter:"alpha(opacity=100)"},300):hdFai.top.$("#ing").css({opacity:1}),hdFai.top.$("#ing").find(".close").bind("mouseenter",function(){$(this).addClass("close_hover")}).bind("mouseleave",function(){$(this).removeClass("close_hover")})},hdFai.removeAllIng=function(){hdFai.top.$("#ing").remove()},hdFai.removeIng=function(e,t){e?void 0!==t&&hdFai.top.$("#"+t).length>0?(hdFai.top.window.setTimeout(function(){$("#"+t).fadeOut(1e3)},3e3),hdFai.top.window.setTimeout(function(){$("#"+t).remove()},4500)):(hdFai.top.$(".tips").fadeOut(1e3),hdFai.top.window.setTimeout(function(){$("#ing").remove()},3e3)):void 0!==t&&hdFai.top.$("#"+t).length>0?(hdFai.top.$("#"+t).fadeOut(500),hdFai.top.window.setTimeout(function(){$("#"+t).remove()},1e3)):(hdFai.top.$(".tips").fadeOut(500),hdFai.top.window.setTimeout(function(){$("#ing").remove()},1e3)),hdFai.top.$("#ing").css("opacity",0)},hdFai.parseFileSize=function(e){if(void 0!==e&&"number"==typeof e){var t;if(e<1024)t=e+"B";else if(e<1048576){t=(e/1024).toFixed(2)+"KB"}else{t=(e/1048576).toFixed(2)+"MB"}return t}return"-"},function(e){function t(e,i,n){return e instanceof t?e:i instanceof t?i:this instanceof t?(this.noPrimaryKey=!1,$.isArray(e)?(n=i,i=e,e=void 0):"boolean"==$.type(e)&&(n=e,e=void 0),void 0===e&&(this.noPrimaryKey=!0),void 0===i&&(i=[]),this.list=i,void 0!==n&&(this.like=n),void(this.noPrimaryKey||(this.primaryKey=e))):new t(e,i,n)}var i=["concat","push","splice","unshift"];t.fn=t.prototype,$.forEach(["copyWithin","entries","concat","every","fill","filter","find","findIndex","forEach","includes","indexOf","join","keys","lastIndexOf","map","pop","push","reduce","reduceRight","reverse","shift","slice","some","sort","splice","toLocaleString","toString","unshift"],function(e){if(arr_proto[e]){var n=-1!=i.indexOf(e);t.fn[e]=function(){var t,i,r=arr_slice.call(arguments),o=this;return n&&(t=0,i=function(e){for(var i=t;i<e.length;i++)o.contains(e[i])&&(e.splice(i,1),i--)},"concat"==e?$.forEach(r,i):("splice"==e&&(t=2),i(r))),o.list[e].apply(o.list,r)}}}),$.extend(t.fn,{size:function(){return this.list.length},empty:function(){this.list.length=0},get:function(e){return this.list[e]},set:function(e,t){return this.list[e]=t,this},getIndex:function(e,t){void 0===t&&(t=this.like);for(var i,n=this.size(),r=0;r<n;r++)if(i=this.getName(r),t?i==e:i===e)return r;return-1},contains:function(e,t){return-1!=this.getIndex(e,t)},setName:function(e,t){if(this.contains(t))return this;if(this.noPrimaryKey){var i=this.getIndex(e);-1!=i&&this.set(i,t)}else{var n=this.getByName(e);if(!n)return console.warn("HdList setName err name:"+e),this;n[this.primaryKey]=t}return this},getName:function(e){var t=this.get(e);return this.noPrimaryKey?t:t?t[this.primaryKey]:void 0},getByName:function(e){return this.get(this.getIndex(e))},setByName:function(e,t){var i=this.getIndex(e);return-1===i?(console.warn("HdList setByName err name:"+e),this):(this.set(i,t),this)},not:function(e){if(!(arguments.length<2)){for(var i=[],n=0;n<arguments.length;n++)i.push(this.not(arguments[n]));return t(this.primaryKey,i)}var r=this.getIndex(e);if(-1!==r)return this.splice(r,1)[0]},add:function(){return this.push.apply(this,arguments),this},clone:function(){return t(this.primaryKey,[].concat(this.list))}}),$.each(["not","add"],function(e,i){t.fn[i+"List"]=function(e){return e instanceof t&&(e=e.list),this[i].apply(this,e)}}),$.each({replace:[0,1],after:[1,0],before:[0,0]},function(e,i){var n=e+"ByIndex";t.fn[n]=function(e){return arr_proto.splice.call(arguments,0,1,e+i[0],i[1]),this.splice.apply(this,arguments),this},t.fn[e]=function(e){var t=arr_slice.call(arguments);return t[0]=this.getIndex(e),this[n].apply(this,t)}}),t.fn.forEach||(t.fn.forEach=function(e){$.forEach(this.list,e)}),e.HdList=t}(hdFai),function(e){var t={};function i(){var e={};return function t(i,r,o,a){if("function"==typeof r){var s=arr_slice.call(arguments);return s.unshift(r.toString()),t.apply(this,s)}var d=n(!e.hasOwnProperty(i),r,e[i],o,a);return d.hasOwnProperty("last")&&(e[i]=d.last),d.rt}}function n(e,t,i,n,r){var o,a=t;return e||(a=i,o=!1,$.isArray(t)&&$.isArray(a)?$.each(a,function(e,i){if(i!==t[e])return o=!0,!1}):o=t!==a,o)?{rt:n(t,a),last:$.isArray(t)?$.extend([],t):t}:{rt:r&&r(a)}}e.watch=i(),e.watch.$new=i,e.watch.$create=function(){var e=arguments.length>0?arguments[0]:t;return function(i,r,o){var a=n(e===t,i,e,r,o);return a.hasOwnProperty("last")&&(e=a.last),a.rt}}}(hdFai),function(e){function t(e){if(!(this instanceof t))return new t(e);this.callbacks={},this._ones={},this.free=e}var i={register:function(e,t){var i=this;return $.isArray(e)?($.each(e,function(e,t){$.isArray(t)?i.register.apply(i,t):i.register(t)}),i):("string"==typeof e&&void 0===this.callbacks[e]&&(this.callbacks[e]=null,t&&(this._ones[e]=1)),i)},on:function(e,t){var i;if(this.checkFire(e))return t();if(!this.callbacks.hasOwnProperty(e)){if(!this.free)return this;"function"==$.type(this.free)&&this.free(e)&&(this._ones[e]=1)}return(i=this.callbacks[e])||(this.callbacks[e]=i=$.Callbacks("unique stopOnFalse"+(this._ones[e]?" onec":""))),i.add(t),this},one:function(e,t){var i=this;return t.$$oneCallback=function(){t.apply(this,arguments),i.off(e,t)},i.on(e,t.$$oneCallback)},off:function(e,t){var i,n=this;return 0==arguments.length?($.each(this.callbacks,function(e,i){n.off(i,t)}),n):(i=this.callbacks[e])?(1==arguments.length?i.empty():"function"==typeof t&&(t.$$oneCallback?(i.remove(t.$$oneCallback),delete t.$$oneCallback):i.remove(t)),n):n},checkFire:function(e){return 2==this._ones[e]},getApiKeys:function(){return $.map(i,function(e,t){return t})}};$.each(["fire","fireWith"],function(e,t){i[t]=function(){var e=arr_slice.call(arguments),i=e.shift(),n=this.callbacks[i];return this._ones[i]&&(this._ones[i]=2),!n||n[t].apply(n,e)}}),$.extend(t.prototype,i),e.CallBack=t}(hdFai),function(e){function t(t){var i=e.extend({defer:null,sync:null,delay:0,memory:!1},t);if(!i.defer)return function(){};var n={list:[],step:-1},r="animFrame"==i.delay?e.requestAnimFrame:function(e){return setTimeout(e,i.delay)};return function(){var e=arr_slice.call(arguments);if(n.step++,i.sync&&i.sync.call(this,n.step,e),i.memory?n.list.push(e):n.list=e,!(n.step>0)){var t=this;r(function(){i.defer[i.memory?"call":"apply"](t,n.list),n.list=[],n.step=-1})}}}e.requestAnimFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60,(new Date).getTime())},e.throttle=function(i,n,r){return 1==arguments.length&&e.isPlainObject(i)?t(i):t({defer:i,delay:n,memory:r})}}(jQuery),function(e){e.cookie=function(t,i,n){if(arguments.length>1&&(null===i||"object"!=typeof i)){if(n=e.extend({},n),null===i&&(n.expires=-1),"number"==typeof n.expires){var r=n.expires,o=n.expires=new Date;o.setDate(o.getDate()+r)}return document.cookie=[encodeURIComponent(t),"=",n.raw?String(i):encodeURIComponent(String(i)),n.expires?"; expires="+n.expires.toUTCString():"",n.path?"; path="+n.path:"",n.domain?"; domain="+n.domain:"",n.secure?"; secure":""].join("")}var a,s=(n=i||{}).raw?function(e){return e}:decodeURIComponent;return(a=new RegExp("(?:^|; )"+encodeURIComponent(t)+"=([^;]*)").exec(document.cookie))?s(a[1]):null}}(jQuery),function($){"use strict";var escape=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},hasOwn=Object.prototype.hasOwnProperty;$.toJSON=function(e){if(null===e)return"null";var t,i,n,r,o=$.type(e);if("undefined"!==o){if("number"===o||"boolean"===o)return String(e);if("string"===o)return $.quoteString(e);if("function"==typeof e.toJSON)return $.toJSON(e.toJSON());if("date"===o){var a=e.getUTCMonth()+1,s=e.getUTCDate(),d=e.getUTCFullYear(),h=e.getUTCHours(),p=e.getUTCMinutes(),l=e.getUTCSeconds(),c=e.getUTCMilliseconds();return a<10&&(a="0"+a),s<10&&(s="0"+s),h<10&&(h="0"+h),p<10&&(p="0"+p),l<10&&(l="0"+l),c<100&&(c="0"+c),c<10&&(c="0"+c),'"'+d+"-"+a+"-"+s+"T"+h+":"+p+":"+l+"."+c+'Z"'}if(t=[],$.isArray(e)){for(i=0;i<e.length;i++)t.push($.toJSON(e[i])||"null");return"["+t.join(",")+"]"}if("object"==typeof e){for(i in e)if(hasOwn.call(e,i)){if("number"===(o=typeof i))n='"'+i+'"';else{if("string"!==o)continue;n=$.quoteString(i)}"function"!==(o=typeof e[i])&&"undefined"!==o&&(r=$.toJSON(e[i]),t.push(n+":"+r))}return"{"+t.join(",")+"}"}}},$.evalJSON="object"==typeof JSON&&JSON.parse?JSON.parse:function(str){return eval("("+str+")")},$.secureEvalJSON="object"==typeof JSON&&JSON.parse?JSON.parse:function(str){var filtered=str.replace(/\\["\\\/bfnrtu]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"");if(/^[\],:{}\s]*$/.test(filtered))return eval("("+str+")");throw new SyntaxError("Error parsing JSON, source is not valid.")},$.quoteString=function(e){return e.match(escape)?'"'+e.replace(escape,function(e){var t=meta[e];return"string"==typeof t?t:(t=e.charCodeAt(),"\\u00"+Math.floor(t/16).toString(16)+(t%16).toString(16))})+'"':'"'+e+'"'}}(jQuery),function(e){"use strict";e.format=function(){function e(e,t){var i,n="";if((i=(e+="").length)>=t)return e;for(;t--;)n+="0";return(n+e).substr(i)}function t(e){var t=e.years||0,i=e.months||0,n=e.weeks||0,r=e.days||0,o=e.hours||0,a=e.minutes||0,s=e.seconds||0,d=e.milliseconds||0;return this.milliseconds=+d+1e3*s+6e4*a+1e3*o*60*60,this.days=+r+7*n,this.months=+i+12*t,this}function i(e,i,n,r){var o,a,s=function(e,i){var n=e;return"number"==typeof e&&(n={},i?n[i]=e:n.milliseconds=e),new t(n)}(i,n),d=(a=s.months,(o=146097*a/4800+s.days)<0?Math.floor(o):Math.ceil(o)),h=s.milliseconds+864e5*d;return new Date(r*(d+h)+e.getTime())}return{date:function(t,i,n){if(!(t instanceof Date))return t;var r,o,a={"y+":t.getFullYear(),"M+":t.getMonth()+1,"d+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),S:t.getMilliseconds()};for(r in a)i=i.replace(new RegExp("("+r+")"),function(t,i,s,d){return o=a[r],n||(o=e(o,i.length)),o});return i},addDate:function(e,t,n){return i(e,t,n,1)},subDate:function(e,t,n){return i(e,t,n,-1)}}}()}(jQuery),function(e){"use strict";function t(e,t){var i=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(i>>16)<<16|65535&i}function i(e,i,n,r,o,a){return t((s=t(t(i,e),t(r,a)))<<(d=o)|s>>>32-d,n);var s,d}function n(e,t,n,r,o,a,s){return i(t&n|~t&r,e,t,o,a,s)}function r(e,t,n,r,o,a,s){return i(t&r|n&~r,e,t,o,a,s)}function o(e,t,n,r,o,a,s){return i(t^n^r,e,t,o,a,s)}function a(e,t,n,r,o,a,s){return i(n^(t|~r),e,t,o,a,s)}function s(e,i){e[i>>5]|=128<<i%32,e[14+(i+64>>>9<<4)]=i;var s,d,h,p,l,c=1732584193,u=-271733879,f=-1732584194,g=271733878;for(s=0;s<e.length;s+=16)d=c,h=u,p=f,l=g,c=n(c,u,f,g,e[s],7,-680876936),g=n(g,c,u,f,e[s+1],12,-389564586),f=n(f,g,c,u,e[s+2],17,606105819),u=n(u,f,g,c,e[s+3],22,-1044525330),c=n(c,u,f,g,e[s+4],7,-176418897),g=n(g,c,u,f,e[s+5],12,1200080426),f=n(f,g,c,u,e[s+6],17,-1473231341),u=n(u,f,g,c,e[s+7],22,-45705983),c=n(c,u,f,g,e[s+8],7,1770035416),g=n(g,c,u,f,e[s+9],12,-1958414417),f=n(f,g,c,u,e[s+10],17,-42063),u=n(u,f,g,c,e[s+11],22,-1990404162),c=n(c,u,f,g,e[s+12],7,1804603682),g=n(g,c,u,f,e[s+13],12,-40341101),f=n(f,g,c,u,e[s+14],17,-1502002290),c=r(c,u=n(u,f,g,c,e[s+15],22,1236535329),f,g,e[s+1],5,-165796510),g=r(g,c,u,f,e[s+6],9,-1069501632),f=r(f,g,c,u,e[s+11],14,643717713),u=r(u,f,g,c,e[s],20,-373897302),c=r(c,u,f,g,e[s+5],5,-701558691),g=r(g,c,u,f,e[s+10],9,38016083),f=r(f,g,c,u,e[s+15],14,-660478335),u=r(u,f,g,c,e[s+4],20,-405537848),c=r(c,u,f,g,e[s+9],5,568446438),g=r(g,c,u,f,e[s+14],9,-1019803690),f=r(f,g,c,u,e[s+3],14,-187363961),u=r(u,f,g,c,e[s+8],20,1163531501),c=r(c,u,f,g,e[s+13],5,-1444681467),g=r(g,c,u,f,e[s+2],9,-51403784),f=r(f,g,c,u,e[s+7],14,1735328473),c=o(c,u=r(u,f,g,c,e[s+12],20,-1926607734),f,g,e[s+5],4,-378558),g=o(g,c,u,f,e[s+8],11,-2022574463),f=o(f,g,c,u,e[s+11],16,1839030562),u=o(u,f,g,c,e[s+14],23,-35309556),c=o(c,u,f,g,e[s+1],4,-1530992060),g=o(g,c,u,f,e[s+4],11,1272893353),f=o(f,g,c,u,e[s+7],16,-155497632),u=o(u,f,g,c,e[s+10],23,-1094730640),c=o(c,u,f,g,e[s+13],4,681279174),g=o(g,c,u,f,e[s],11,-358537222),f=o(f,g,c,u,e[s+3],16,-722521979),u=o(u,f,g,c,e[s+6],23,76029189),c=o(c,u,f,g,e[s+9],4,-640364487),g=o(g,c,u,f,e[s+12],11,-421815835),f=o(f,g,c,u,e[s+15],16,530742520),c=a(c,u=o(u,f,g,c,e[s+2],23,-995338651),f,g,e[s],6,-198630844),g=a(g,c,u,f,e[s+7],10,1126891415),f=a(f,g,c,u,e[s+14],15,-1416354905),u=a(u,f,g,c,e[s+5],21,-57434055),c=a(c,u,f,g,e[s+12],6,1700485571),g=a(g,c,u,f,e[s+3],10,-1894986606),f=a(f,g,c,u,e[s+10],15,-1051523),u=a(u,f,g,c,e[s+1],21,-2054922799),c=a(c,u,f,g,e[s+8],6,1873313359),g=a(g,c,u,f,e[s+15],10,-30611744),f=a(f,g,c,u,e[s+6],15,-1560198380),u=a(u,f,g,c,e[s+13],21,1309151649),c=a(c,u,f,g,e[s+4],6,-145523070),g=a(g,c,u,f,e[s+11],10,-1120210379),f=a(f,g,c,u,e[s+2],15,718787259),u=a(u,f,g,c,e[s+9],21,-343485551),c=t(c,d),u=t(u,h),f=t(f,p),g=t(g,l);return[c,u,f,g]}function d(e){var t,i="";for(t=0;t<32*e.length;t+=8)i+=String.fromCharCode(e[t>>5]>>>t%32&255);return i}function h(e){var t,i=[];for(i[(e.length>>2)-1]=void 0,t=0;t<i.length;t+=1)i[t]=0;for(t=0;t<8*e.length;t+=8)i[t>>5]|=(255&e.charCodeAt(t/8))<<t%32;return i}function p(e){var t,i,n="";for(i=0;i<e.length;i+=1)t=e.charCodeAt(i),n+="0123456789abcdef".charAt(t>>>4&15)+"0123456789abcdef".charAt(15&t);return n}function l(e){return unescape(encodeURIComponent(e))}function c(e){return function(e){return d(s(h(e),8*e.length))}(l(e))}function u(e,t){return function(e,t){var i,n,r=h(e),o=[],a=[];for(o[15]=a[15]=void 0,r.length>16&&(r=s(r,8*e.length)),i=0;i<16;i+=1)o[i]=909522486^r[i],a[i]=1549556828^r[i];return n=s(o.concat(h(t)),512+8*t.length),d(s(a.concat(n),640))}(l(e),l(t))}e.md5=function(e,t,i){return t?i?u(t,e):p(u(t,e)):i?c(e):p(c(e))}}("function"==typeof jQuery?jQuery:this),jQuery.openURL=function(e,t,i){t?window.open(e,t,i):window.open(e)},jQuery.jumpURL=function(e){window.location.href=e},function(e,t,i){"use strict";var n,r;e.uaMatch=function(e){e=e.toLowerCase();var t=/(opr)[\/]([\w.]+)/.exec(e)||/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[],i=/(ipad)/.exec(e)||/(iphone)/.exec(e)||/(android)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0",platform:i[0]||""}},r={},(n=e.uaMatch(t.navigator.userAgent)).browser&&(r[n.browser]=!0,r.version=n.version),n.platform&&(r[n.platform]=!0),r.chrome||r.opr?r.webkit=!0:r.webkit&&(r.safari=!0),r.rv&&(r.msie=!0),r.opr&&(r.opera=!0),e.browser=r}(jQuery,window)}(hdFai);