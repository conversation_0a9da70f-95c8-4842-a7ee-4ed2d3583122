<template>
  <el-dialog
    class="select-proto-dialog"
    title="选择原型"
    :visible.sync="protoSelectDialogVisible"
    width="700px"
  >
    <div
      class="pl-[20px] pr-[20px] pt-[10px] pb-[10px] box-border proto-select-dialog"
    >
      <div class="mb-[10px]">
        <el-input
          v-model="protoSearchKeyword"
          placeholder="请输入原型名称搜索"
          class="!w-[300px]"
          @input="handleProtoSearch"
        >
          <el-button slot="append" icon="el-icon-search"></el-button>
        </el-input>
      </div>

      <el-table
        :data="protoList"
        height="400"
        class="overflow-y-auto proto-table"
        @current-change="handleProtoSelectionChange"
        highlight-current-row
      >
        <el-table-column width="50">
          <template slot-scope="scope">
            <el-radio v-model="selectedProtoId" :label="scope.row.id">
              <span class="radio-label"></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="原型ID" width="100">
        </el-table-column>
        <el-table-column prop="name" label="原型名称"> </el-table-column>
      </el-table>

      <div class="mt-[10px] text-right">
        <el-pagination
          :pager-count="5"
          :page-size="pageConfig.pageSize"
          :current-page="pageConfig.pageCurrent"
          @size-change="handleProtoSizeChange"
          @current-change="handleProtoCurrentChange"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageConfig.total"
        >
        </el-pagination>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="cancelProtoSelection">取 消</el-button>
      <el-button type="primary" @click="confirmProtoSelection">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { ScProductType } from "@/views/scPortal/config/index.js";
import { getScProtoNameList } from "@/views/scPortal/api/scProto.js";

export default {
  name: "SelectProtoDialog",
  data() {
    return {
      ScProductType,
      protoSearchKeyword: "",
      protoList: [],
      selectedProto: null,
      selectedProtoId: null,
      pageConfig: {
        pageSize: 10,
        pageCurrent: 1,
        total: 0,
      },
    };
  },
  props: {
    /**
     * 是否显示
     */
    visible: {
      type: Boolean,
      default: false,
    },
    /**
     * 编辑类型
     */
    editType: {
      type: Number,
      default: ScProductType.VIDEO,
    },
    /**
     * 默认选中
     */
    // defaultSelectedProto: {
    //   type: Object,
    //   default: null,
    // },
    defaultSelectedProtoId: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.loadProtoList();
          this.$nextTick(() => {
            if (this.defaultSelectedProtoId) {
              this.selectedProtoId = this.defaultSelectedProtoId;
            }
          });
        }
      },
      immediate: true,
    },
  },
  computed: {
    protoSelectDialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        this.$emit("update:visible", value);
      },
    },
  },
  methods: {
    handleProtoSizeChange(size) {
      this.pageConfig.pageSize = size;
      this.loadProtoList();
    },
    handleProtoCurrentChange(page) {
      this.pageConfig.pageCurrent = page;
      this.loadProtoList();
    },
    handleProtoSelectionChange(row) {
      this.selectedProto = row;
      if (row) {
        this.selectedProtoId = row?.id;
      }
    },
    /**
     * 搜索
     */
    handleProtoSearch() {
      this.pageConfig.pageCurrent = 1;
      this.loadProtoList();
    },
    /**
     * 加载原型列表
     */
    loadProtoList() {
      getScProtoNameList({
        type: this.editType,
        keyword: this.protoSearchKeyword,
        pageNo: this.pageConfig.pageCurrent,
        pageLimit: this.pageConfig.pageSize,
      }).then((res) => {
        if (res && res.success) {
          this.protoList = res.data;
          console.log(this.protoList, "loadProtoList");
          this.pageConfig.total = res.totalSize;
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /**
     * 确定
     */
    confirmProtoSelection() {
      console.log(this.selectedProto, "confirmProtoSelection");
      this.$emit("confirmProtoSelection", this.selectedProto);
      this.protoSelectDialogVisible = false;
      this.protoSearchKeyword = "";
    },
    /**
     * 取消
     */
    cancelProtoSelection() {
      this.protoSelectDialogVisible = false;
      this.selectedProtoId = 0;
      this.protoSearchKeyword = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.proto-table {
  ::v-deep .el-table__row {
    cursor: pointer;

    &:hover {
      background-color: #f5f7fa;
    }

    &.current-row {
      background-color: #ecf5ff;
    }
  }

  ::v-deep .el-radio {
    margin-right: 0;

    .el-radio__label {
      display: none;
    }
  }
}

.select-proto-dialog {
  ::v-deep .el-dialog__body {
    width: 100%;
  }
}

</style>
