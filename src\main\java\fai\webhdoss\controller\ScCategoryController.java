package fai.webhdoss.controller;

import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scRes.ScCategoryVO;
import fai.webhdoss.service.ScCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 素材库-分类接口
 *
 * <AUTHOR>
 * @since 25-06-11 14:55
 */
@RestController
@RequestMapping("/category")
public class ScCategoryController {

    @Autowired
    private ScCategoryService scCategoryService;

    @GetMapping("/list")
    public JsonResult list(ScCategoryVO vo) {
        return scCategoryService.getList(vo);
    }

    @PostMapping("/update")
    public JsonResult update(@RequestBody ScCategoryVO vo) {
        return scCategoryService.set(vo);
    }

    @PostMapping("/delete")
    public JsonResult delete(@RequestBody ScCategoryVO vo) {
        return scCategoryService.del(vo);
    }

    @PostMapping("/add")
    public JsonResult add(@RequestBody ScCategoryVO vo) {
        return scCategoryService.add(vo);
    }
}
