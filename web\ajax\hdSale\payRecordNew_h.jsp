<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.inf.Kid" %>
<%@ page import="fai.web.inf.SysPreSaleHd" %>
<%@ page import="fai.weboss.WebOss" %>
<%@ page import="fai.web.inf.SysBssStat" %>
<%@ page import="fai.webhdoss.WebHdOss" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.weboss.OssPoi" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.SysCrmSale" %>
<%@ page import="fai.comm.fdpdata.FdpDataParamMatcher" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.IN" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%--互动销售能力认证--%>
<%!
    private static final class Key {
        public static final String DEPARTMENT = "department";
        public static final String BUSINESS = "business";
        public static final String LIST = "list";
        public static final String CMD = "cmd";
        public static final String LABEL = "label";
        public static final String SID = "sid";
        public static final String SID_LIST = "sidList";
    }
    private static final class Message {
        public static final String NO_AUTH = "没有权限";
        public static final String NO_METHOD = "没有找到方法";
        public static final String SYS_ERR = "系统错误";
        public static final String ARG_ERR_DEPARTMENT = "参数错误，没有指定部门";
        public static final String GET_SALE_ERR = "获取其他部门销售失败";
        public static final String ARG_ERR_SID = "参数错误，销售员工id为0";
        public static final String ARG_ERR_BUSINESS = "参数错误，业务为空";
        public static final String GET_BIZ_ERROR = "获取销售已有业务权限失败";
        public static final String REMOVE_ERR = "删除失败";
        public static final String ARG_ERR_SID_LIST = "参数错误，请选择要添加的销售";
    }
%>
<%!
    private String getPayRecordNewList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        boolean isPreSaleLeader = Auth.checkFaiscoAuth("authHDSaleManage", false);

        boolean isOther = Parser.parseBoolean(request.getParameter("other"), false);

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> sidList = new FaiList<Integer>();                        // 直销互动销售列表
        boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        // 如果是查顾问式销售的成单，则需要拿其他部门的销售列表
        if (!isOther) {
            sidList.clear();
            sidList = getSaleList();
        }
        // 产品列表
        FaiList<Integer> allProductList = new FaiList<Integer>();
        if (isOther) {
            allProductList.addAll(PreSaleDef.getProductIdList(PreSaleDef.PreSaleProduct.ALL));
            allProductList.addAll(PreSaleDjDef.Product.getList(PreSaleDjDef.Product.ALL));
        } else {
            allProductList.addAll(PreSaleHdDef.getProductList(PreSaleHdDef.PreSaleProduct.ALL));
        }

        // 接口
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);

        SearchArg searchArg = null;
        double totalPrice = 0.0;
        int totalClient = 0;
        FaiList<Param> orderItemList = new FaiList<Param>();
        FaiList<Param> preSaleHdList = new FaiList<Param>();
        FaiList<String> fieldList = new FaiList<String>();
        FaiList<Integer> aidList = new FaiList<Integer>();


        /*** 分页参数 ****/
        int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);        // 当前页数
        int limit = Parser.parseInt(request.getParameter("limit"), 10);            // 一页多少条数据
        int start = (pageNo - 1) * limit;                                        // 开始
        int total = 0;                                                            // 总数

        /***** 获取查询条件 start *****/
        boolean regOnTime = Parser.parseBoolean(request.getParameter("regDateFlag"), false);            // 是否查询注册时间
        boolean creOnTime = Parser.parseBoolean(request.getParameter("creDateFlag"), false);            // 是否查询创建时间
        boolean payOnTime = Parser.parseBoolean(request.getParameter("payDateFlag"), false);            // 是否查询支付时间
        boolean refOnTime = Parser.parseBoolean(request.getParameter("refDateFlag"), false);            // 是否查询退款时间
        boolean receiveOnTime = Parser.parseBoolean(request.getParameter("receiveDateFlag"), false);    // 是否查询领取时间
        boolean oldReceiveOnTime = Parser.parseBoolean(request.getParameter("oldReceiveDateFlag"), false);//是否查询旧账号领取时间

        int sea_PayType = Parser.parseInt(request.getParameter("payType"), -1);    // 类型
        int seq_cmpor = Parser.parseInt(request.getParameter("sort"), 2);        // 排序
        int seq_status = Parser.parseInt(request.getParameter("status"), 0);    // 订单状态
        int seq_pay = Parser.parseInt(request.getParameter("pay"), 0);            // 支付情况
        int se_price1 = Parser.parseInt(request.getParameter("minPrice"), -1);        // min金额
        int se_price2 = Parser.parseInt(request.getParameter("maxPrice"), -1);        // max金额
        int product = Parser.parseInt(request.getParameter("productType"), 0);    // 产品类型
        int seq_goal = Parser.parseInt(request.getParameter("goal"), -1);            // 用途
        int se_ta = Parser.parseInt(request.getParameter("ta"), -1);            // 注册来源
        int se_tag = Parser.parseInt(request.getParameter("tag"), -1);            // 标签
        int amount = Parser.parseInt(request.getParameter("amount"), 0);            // 购买数量
        int time_tag = Parser.parseInt(request.getParameter("time_tag"), -1);            // 时间段
        int A_BLib = Parser.parseInt(request.getParameter("A_BLib"), -1);            // 默认A库
        int newA_BLib = Parser.parseInt(request.getParameter("newA_BLib"), -1);
        int staff_sid = Parser.parseInt(request.getParameter("staff_sid"), 0);    // 领取人
        int seq_aid = Parser.parseInt(request.getParameter("aid"), 0);            // aid
        int seq_orderId = Parser.parseInt(request.getParameter("orderId"), 0);    // 订单号
        int business = Parser.parseInt(request.getParameter("Business"), -1);    // 业务
        int abType = Parser.parseInt(request.getParameter("abType"), -1);    // ab类
        int isWeekendResource = Parser.parseInt(request.getParameter("isWeekendResource"), -1);    // 是否周末分配资源
        int allotType = Parser.parseInt(request.getParameter("allotType"), -1);  //资源类型
        int approveStyle = Parser.parseInt(request.getParameter("approveStyle"),-1);   //报备类型
        String staff_group = Parser.parseString(request.getParameter("staff_group"), "all"); // 销售分组
        String cmd = Parser.parseString(request.getParameter("cmd"), "");        // 判断是否点击过查询(cmd为空代表第一次进入页面)

        /***** 日期设置 START *****/
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, -7);//7天前

        //支付时间
        String begDate = Parser.parseString(request.getParameter("payDateBeg"), Parser.parseSimpleTime(c));
        String endDate = Parser.parseString(request.getParameter("payDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        int begDateInt = (int) (Parser.parseCalendar(begDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endDateInt = (int) (Parser.parseCalendar(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        //订单创建时间
        String begDate2 = Parser.parseString(request.getParameter("creDateBeg"), Parser.parseSimpleTime(c));
        String endDate2 = Parser.parseString(request.getParameter("creDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        int begDateInt2 = (int) (Parser.parseCalendar(begDate2 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endDateInt2 = (int) (Parser.parseCalendar(endDate2 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        //注册时间
        String begDate3 = Parser.parseString(request.getParameter("regDateBeg"), Parser.parseSimpleTime(c));
        String endDate3 = Parser.parseString(request.getParameter("regDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        int begDateInt3 = (int) (Parser.parseCalendar(begDate3 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endDateInt3 = (int) (Parser.parseCalendar(endDate3 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        //退款时间
        String begDate4 = Parser.parseString(request.getParameter("refDateBeg"), Parser.parseSimpleTime(c));
        String endDate4 = Parser.parseString(request.getParameter("refDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        int begDateInt4 = (int) (Parser.parseCalendar(begDate4 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endDateInt4 = (int) (Parser.parseCalendar(endDate4 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        //领取时间
        String receiveBegDate = Parser.parseString(request.getParameter("receiveDateBeg"), Parser.parseSimpleTime(c));
        String receiveEndDate = Parser.parseString(request.getParameter("receiveDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));

        //旧账号领取时间
        String oldReceiveDateBeg = Parser.parseString(request.getParameter("oldReceiveDateBeg"), Parser.parseSimpleTime(c));
        String oldReceiveDateEnd = Parser.parseString(request.getParameter("oldReceiveDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));

        FaiList<Integer> approveTypeAidList = new FaiList<Integer>();  //查询报备类型
        int i = 0;
        int totalNum = 1000;  //查询的个数信息,假设初始值为1000条
        if(approveStyle!=-1){
            Dao dao = WebOss.getOssBsDao();			// 获取OssBsDao
            try{
                while(i <= totalNum/1000){
                    Dao.SelectArg selectArg = new Dao.SelectArg();
                    SearchArg approveStyleSearch =new SearchArg();
                    selectArg.searchArg=approveStyleSearch;
                    selectArg.table="hdSaleApprove";
                    selectArg.searchArg.start = i*1000;
                    selectArg.searchArg.limit = 1000;
                    selectArg.searchArg.totalSize = new Ref<Integer>();
                    approveStyleSearch.matcher = new ParamMatcher();
                    approveStyleSearch.matcher.and(PreSaleDef.AcctPreSaleApprove.APPROVE_STYLE,ParamMatcher.EQ,approveStyle);
                    approveStyleSearch.matcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ , HdSaleApproveDef.ApproveStatus.AGREE);
                    FaiList<Param> approveTypeInfo = dao.select(selectArg);   //获取对应报备类型对应的信息
                    totalNum = selectArg.searchArg.totalSize.value;        //注意这里需要再查询后才能拿取value熟悉，否则会报空
                    for(Param p : approveTypeInfo){
                        approveTypeAidList.add(p.getInt("aid",0));
                    }
                    i++;
                }
            }finally{
                dao.close();
            }
        }

        //如果筛选了旧账号领取时间先去acctPreSaleHd表查领取时间在范围内的的账号，再去审批表查这些账号是否旧账号，是得话将新账号作为订单的查询条件
        FaiList<Integer> oldAidList = new FaiList<Integer>();
        FaiList<Integer> newAidList = new FaiList<Integer>();
        if(oldReceiveOnTime){
            fieldList.clear();
            fieldList.add(PreSaleHdDef.Info.AID);

            SearchArg oldArg = new SearchArg();
            ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.RECEIVE_TIME,ParamMatcher.GE,oldReceiveDateBeg + " 00:00:00");
            matcher.and(PreSaleHdDef.Info.RECEIVE_TIME,ParamMatcher.LT,oldReceiveDateEnd + " 23:59:59");
            oldArg.limit = 30000;
            oldArg.matcher = matcher;

            FaiList<Param> tempList = sysPreSaleHd.getList(fieldList, oldArg);
            for(Param p : tempList){
                oldAidList.add(p.getInt("aid",0));
            }

            //查审批表，关联新账号
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "aid,oldAid";
            selectArg.table = "hdSaleApprove";
            matcher = new ParamMatcher(HdSaleApproveDef.Info.OLD_AID,ParamMatcher.IN,oldAidList);
            matcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
            matcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
            selectArg.searchArg.matcher = matcher;
            Dao ossDao = WebOss.getOssBsDao();
            try{
                tempList = ossDao.select(selectArg);
            }finally {
                ossDao.close();
            }

            for(Param p : tempList){
                newAidList.add(p.getInt("aid",0));
            }
        }
        Log.logStd("new aid list size = %d",newAidList.size());


        /******************** 获取关联的订单item数据 ********************/
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, sidList); // 电话销售的销售业绩
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, allProductList);

        if (isOther) {
        } else {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, allProductList);
        }

        if (amount != 0) {//如果要查询购买数量，要区分按月还是按年存，prop2=1时是按月，其他是按年
            ParamMatcher searchArgTemp = null;
            ParamMatcher searchArgProp2 = new ParamMatcher();
            if (amount == 1) {//购买数量为1年
                searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.EQ, 1);//按月计算
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 12);//大于等于1个月，小于等于12个月
                searchArgProp2.and(searchArgTemp);
                searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.NE, 1);//按年计算的
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.EQ, 1); //购买数量是1年
                searchArgProp2.or(searchArgTemp);
                searchArg.matcher.and(searchArgProp2);
            }
            if (amount == 2) {//大于等于2年算多年
                searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.EQ, 1);//按月计算
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 24);//大于等于三年的版本
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 120);//大于等于三年的版本×
                searchArgProp2.and(searchArgTemp);

                searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.NE, 1);//按年计算
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 2);
                searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 10);
                searchArgProp2.or(searchArgTemp);

                //升级产品都是按照月份统计的，需要特殊处理
                FaiList<Integer> upVersionList = new FaiList<Integer>();
                upVersionList.add(FaiProductDef.Id.UPGRADE_TO_HD_PLATINUM);
                upVersionList.add(FaiProductDef.Id.UPGRADE_TO_HD_STORE);
                upVersionList.add(FaiProductDef.Id.UPGRADE_TO_HD_DIAMON);
                upVersionList.add(FaiProductDef.Id.UPGRADE_TO_FLYER_PRIME);
                upVersionList.add(FaiProductDef.Id.UPGRADE_TO_PUBLIC_ASSIST_PRO);

                ParamMatcher upVersion = new ParamMatcher(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, upVersionList);
                upVersion.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 24);
                upVersion.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 120);
                searchArgProp2.or(upVersion);

                searchArg.matcher.and(searchArgProp2);
            }
            Log.logStd("amount=%d sql is [%s]", amount, searchArgProp2);
        }


        // 如果 创建时间or支付时间or注册时间 都没有勾选的话，默认选择支付时间
        boolean crePayTime = false;
        if (creOnTime == false && !payOnTime && !regOnTime && !refOnTime) {

            if (seq_status == 2) {
                creOnTime = true;
            } else {
                payOnTime = true;
            }

            if (!cmd.equals("")) {    // 弹出勾选时间的提示框
                crePayTime = true;
            }
        }

        // 领取时间查询,只拿成交库就好了
        SearchArg searchArgHdTotal = new SearchArg();
        searchArgHdTotal.matcher = new ParamMatcher();
        fieldList.clear();
        fieldList.add(PreSaleHdDef.Info.AID);
        FaiList<Integer> tempAidList = new FaiList<Integer>();
        if (receiveOnTime) {
            //searchArgHdTotal.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.DEAL);
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveBegDate + " 00:00:00");
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveEndDate + " 23:59:59");
        }
        searchArgHdTotal.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.DEAL);//以防数据了太大
        if (business == 0) {   //业务
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG,ParamMatcher.NE,PreSaleHdDef.Tag.OPENMP_RESOURCE); //排除助手
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);

        }else if (business == 1) {
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.YK);
        }else if (business == 2) {
            ParamMatcher mpMatcher=new ParamMatcher(PreSaleHdDef.Info.TAG,ParamMatcher.EQ,PreSaleHdDef.Tag.OPENMP_RESOURCE);
            mpMatcher.or(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
            searchArgHdTotal.matcher.and(mpMatcher);
        }

        if (isWeekendResource == 1) { //周末分配资源
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.IS_WEEKEND_RESOURCE, PreSaleHdDef.Flag.IS_WEEKEND_RESOURCE);
        } else if (isWeekendResource == 0) { //工作日分配资源
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE, PreSaleHdDef.Flag.IS_WEEKEND_RESOURCE, PreSaleHdDef.Flag.IS_WEEKEND_RESOURCE);
        }

        //资源分配类型  writting zhs
        FaiList<Integer> hd_mp_business=new FaiList<Integer>();
        FaiList<Integer> receiveAidList = new FaiList<Integer>();
        switch(allotType){
            case 0: //平均分配资源
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.AVERAGE_ALLOT, PreSaleHdDef.Flag.AVERAGE_ALLOT);
                hd_mp_business.add(PreSaleHdDef.Business.HD);    //互动
                hd_mp_business.add(PreSaleHdDef.Business.MP);	//助手
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
                break;

            case 1:  //奖励资源
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.IS_AWARD, PreSaleHdDef.Flag.IS_AWARD);
                hd_mp_business.add(PreSaleHdDef.Business.HD);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
                break;

            case 2:   //续费资源
                hd_mp_business.add(PreSaleHdDef.Business.HD);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG,ParamMatcher.EQ,PreSaleHdDef.Tag.RENEW_RESOURCE);
                break;

            case 3:   //公海库领取资源
                hd_mp_business.add(PreSaleHdDef.Business.HD);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG,ParamMatcher.EQ,PreSaleHdDef.Tag.GET_GH);
                break;
            case 4:   //领取审批资源
                hd_mp_business.add(PreSaleHdDef.Business.HD);    //互动
                hd_mp_business.add(PreSaleHdDef.Business.MP);	//助手
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.IN,hd_mp_business);
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TYPE,ParamMatcher.EQ,PreSaleHdDef.Type.APPROVE);
        }

        // 标签
        if (se_tag > -1 ) {
            if(se_tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
                FaiList<Integer> tagTmpList = new FaiList<Integer>();
                tagTmpList.add(72);//排除活动引流回到电脑标签
                tagTmpList.add(52);//排除公众号注册分配
                tagTmpList.add(59);//排除历史资源
                tagTmpList.add(65);//排除周末分配周末资源
                tagTmpList.add(0); //排除手动分配
                tagTmpList.add(57);//排除互动-非当月无创建游戏当天有登录资源
                tagTmpList.add(56);//排除每半小时分配创建游戏
                tagTmpList.add(50);//排除互动-创建游戏时间分配
                tagTmpList.add(68);//排除当天开通互动
                tagTmpList.add(55);//排除互动-非当月有创建游戏当天有登录资源
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.NOT_IN, tagTmpList);
            }else{
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.EQ, se_tag);
            }

        }
        if (time_tag > -1) {
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, 0x1, time_tag); //TODO:这里用PreSaleHdDef.Flag.RECEIVE_AFTER_1645就报错,先用0x1代替
        }
        //2019.1.30 	筛选A/B库
        /* if(A_BLib==0){//默认A库
        	searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.UN_CONTACT_30DAY,PreSaleHdDef.Flag.UN_CONTACT_30DAY);

        }else if */
       /*  if (A_BLib == 0) { //li旧的A、B都算业绩了..
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE, PreSaleHdDef.Flag.UN_CONTACT_30DAY, PreSaleHdDef.Flag.UN_CONTACT_30DAY);
            //Log.logStd("after A_BLib");
        } else if (A_BLib == 1) {
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.UN_CONTACT_30DAY, PreSaleHdDef.Flag.UN_CONTACT_30DAY);
        } */
        //ab类
        if(abType==1){  //b类
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.TYPE_B, PreSaleHdDef.Flag.TYPE_B);
        }else if(abType==0){ //A类
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.TYPE_A, PreSaleHdDef.Flag.TYPE_A);
        }

        //当月，新A，新B库
        if(newA_BLib==0){
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE, PreSaleHdDef.Flag.STATUS_HISTORIAN,  PreSaleHdDef.Flag.STATUS_HISTORIAN);
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE, PreSaleHdDef.Flag.STATUS_INVALID,  PreSaleHdDef.Flag.STATUS_INVALID);
        }else if(newA_BLib==1){
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.STATUS_HISTORIAN,  PreSaleHdDef.Flag.STATUS_HISTORIAN);
        }else if(newA_BLib==2) {
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.STATUS_INVALID, PreSaleHdDef.Flag.STATUS_INVALID);
        }else if(newA_BLib==3){
            //当月库+A库
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE, PreSaleHdDef.Flag.STATUS_INVALID,  PreSaleHdDef.Flag.STATUS_INVALID);
        }


        Log.logStd("after A_BLib searchArgHdTotal.matcher=%s", searchArgHdTotal.matcher);
        if (!searchArgHdTotal.matcher.isEmpty()) {
            FaiList<Param> tempList = sysPreSaleHd.getList(fieldList, searchArgHdTotal);
            for (Param p : tempList) {
                tempAidList.add(p.getInt(PreSaleHdDef.Info.AID, 0));
            }
            //if(tempList!=null && tempList.size()>0){
            // Log.logStd("after A_BLib list=%s",tempList.size());

            searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN, tempAidList);
            Log.logStd("liqin tempAidList list=%s",tempAidList.size());
        }

       /*  if (amount == 2) {//干扰了前面购买数量的条件
            searchArg.matcher.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 3);//大于等于三年的版本
        } else if (amount == 1) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.EQ, 1);//一年版本
        } */
        //zhs writting 10-16
        if(approveStyle!=-1){
            searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN,approveTypeAidList);
        }
        if(oldReceiveOnTime){
            searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN, newAidList);
        }

        if (seq_aid > 0) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.EQ, seq_aid);
        }

        if (seq_orderId > 0) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.ORDER_ID, ParamMatcher.EQ, seq_orderId);
        }


        // 注册时间
        if (regOnTime) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.REG_TIME, ParamMatcher.GE, begDateInt3);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.REG_TIME, ParamMatcher.LE, endDateInt3);
        }
        // 支付时间
        if (payOnTime) {
            if (seq_status != 2) {
                searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, begDateInt);
                searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, endDateInt);
            }
        }
        //退款时间
        if (refOnTime) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.REFUND_TIME, ParamMatcher.GE, begDateInt4);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.REFUND_TIME, ParamMatcher.LE, endDateInt4);
        }
        // 下单时间（订单创建时间）
        if (creOnTime) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.CREATE_TIME, ParamMatcher.GE, begDateInt2);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.CREATE_TIME, ParamMatcher.LE, endDateInt2);
        }
        //已退款
        if (seq_status == 3) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.DEL);
        }
        // 处理完成的订单
        if (seq_status == 1) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.NE, BssStatDef.DelStatus.DEL);
        }
        // 待支付的订单
        if (seq_status == 2) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, FaiOrderDef.Status.FIN_SETTLE);
        }

        // 销售分组-权限组
        if (!staff_group.equals("all")) {
            FaiList<Integer> tmpSidList = WebOss.getAuthSidList(staff_group);
            searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, tmpSidList);
        }
        // 某个销售
        if (staff_sid != 0) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.EQ, staff_sid);
        }

        // 有支付
        if (seq_pay == 1) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GT, 0);
        }
        // 无需支付
        if (seq_pay == 2) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.EQ, 0);
        }

        // 注册用途
        if (seq_goal != -1) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.COMPANY_GOAL, ParamMatcher.EQ, seq_goal);
        }

        // 注册（注册来源）
        FaiList<Param> taList = getTaList();                                            // taList
        if (se_ta != -1 || se_tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC) {
            FaiList<Integer> trackList = PreSaleHdDef.getTaList(taList, se_ta);
            if (se_ta != PreSaleHdDef.PreSaleTa.NOT_IN) {
                if(se_tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
                    trackList = PreSaleHdDef.getTaList(taList, 11);
                }
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.IN, trackList);
            } else {
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, trackList);
            }
        }

        // 类型（支付类型）
        if (sea_PayType != -1) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TYPE, ParamMatcher.EQ, sea_PayType);
        }

        // 区间金额
        if (se_price1 >= 0) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GE, Parser.parseDouble(se_price1 + "", 0.0));
        }
        if (se_price2 >= 0) {
            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.LE, Parser.parseDouble(se_price2 + "", 0.0));
        }

        // 排序
        if (seq_cmpor == 0) {
            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.ID, true);
        } else if (seq_cmpor == 1) {
            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.REG_TIME, true);
        } else if (seq_cmpor == 2) {
            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.PAY_TIME, true);
        }

        // 分页
        if (!excel) {
            searchArg.start = start;
            searchArg.limit = limit;
        }

        searchArg.totalSize = new Ref<Integer>();
        /**调用**/

        Log.logDbg("unique-test aid = " + seq_aid);
        Log.logDbg("unique-test ser = searchArg" + searchArg.matcher.toJson());


        orderItemList = sysBssStat.getOrderItemList(searchArg);

        Log.logDbg("unique-test order = " + orderItemList);

        total = searchArg.totalSize.value;

        /******************** 拿到领取时间 ********************/
        fieldList.clear();
        fieldList.add(PreSaleHdDef.Info.AID);
        fieldList.add(PreSaleHdDef.Info.RECEIVE_TIME);
        fieldList.add(PreSaleHdDef.Info.TAG);

        aidList.clear();
        for (Param p : orderItemList) {
            aidList.add(p.getInt(BssStatDef.OrderItemInfo.AID, 0));
        }

        SearchArg searchArgHd = new SearchArg();
        searchArgHd.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
        preSaleHdList = sysPreSaleHd.getList(fieldList, searchArgHd);
        Param preSaleHdParam = new Param(true);//存储个人库信息 key:aid value:param
        for (Param item : preSaleHdList) {
            preSaleHdParam.setParam(String.valueOf(item.getInt("aid")), item);
        }

        /******************** 关联旧账号 ********************/

        Dao.SelectArg approveArg = new Dao.SelectArg();
        approveArg.field = "aid,oldAid";
        approveArg.table = "hdSaleApprove";
        ParamMatcher approveMatcher = new ParamMatcher(HdSaleApproveDef.Info.AID, ParamMatcher.IN, aidList);
        approveMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
        approveMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
        approveArg.searchArg.matcher = approveMatcher;
        Dao ossDao = WebOss.getOssBsDao();
        FaiList<Param> approveList = new FaiList<Param>();
        try {
            approveList = ossDao.select(approveArg);
            Log.logStd("approve list size = %d",approveList.size());
        } finally {
            ossDao.close();
        }

        HashMap<Integer, Param> approveMap = new HashMap<Integer, Param>();
        oldAidList = new FaiList<Integer>();
        for (Param p : approveList) {
            int aid = p.getInt("aid", 0);
            int oldAid = p.getInt("oldAid", 0);
            oldAidList.add(oldAid);
            approveMap.put(oldAid, p);
        }

        searchArgHd = new SearchArg();
        searchArgHd.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, oldAidList);
        FaiList<Param> oldAidReceiveList = sysPreSaleHd.getList(fieldList, searchArgHd);
        for (Param p : oldAidReceiveList) {
            int oldAid = p.getInt("aid", 0);
            Calendar receiveTime = p.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);
            Param item = approveMap.get(oldAid);
            item.setCalendar("receiveTime", receiveTime);
            int aid = item.getInt("aid", 0);
            approveMap.put(aid, item);
        }

        /******************** 统计客户数量以及金额 ********************/
        searchArg.start = 0;
        searchArg.limit = -1;
        searchArg.totalSize = null;
        searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);


        fieldList.clear();
        fieldList.add(BssStatDef.CountInfo.COUNT_AID);
        fieldList.add(BssStatDef.CountInfo.SUM_PRICE);

        FaiList<Param> countList = sysBssStat.getOrderItemCount(fieldList, null, searchArg);
        if (countList.size() > 0) {
            Param countInfo = countList.get(0);
            totalPrice = countInfo.getInt(BssStatDef.CountInfo.SUM_PRICE, 0);
            totalClient = countInfo.getInt(BssStatDef.CountInfo.COUNT_AID, 0);
        }
        String domain = WebHdOss.getOssDomainUrl();
        /******************** 数据翻译 ********************/
        FaiList<Integer> aidIntList = new FaiList<Integer>();
        Map<Integer, String> tagMap = getTagMap();
        for (Param order : orderItemList) {
            //for (int i = 0; i < orderItemList.size(); i++) {//li
            // Param order = orderItemList.get(i);//li
            int aid = order.getInt(BssStatDef.OrderItemInfo.AID, 0);
            int sid = order.getInt(BssStatDef.OrderItemInfo.S_ID, 0);
            faiTradeStationBaseApi.changeAid(aid);
            faiTradeStationBaseApi.changeSiteId(sid);
            aidIntList.add(aid);
            int id = order.getInt(BssStatDef.OrderItemInfo.ORDER_ID, 0);
            String regTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.REG_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            String createTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.CREATE_TIME, 0), "yyyy-MM-dd");
            String payTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.PAY_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            String refundTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.REFUND_TIME, 0), "yyyy-MM-dd");
            double price = order.getDouble(BssStatDef.OrderItemInfo.PRICE, 0.0);

            String status = FaiOrderDef.getStatusName(order.getInt(BssStatDef.OrderItemInfo.STATUS, 0));
            String typeStr = FaiOrderDef.getPayTypeName(order.getInt(BssStatDef.OrderItemInfo.PAY_TYPE, 0));
            String productStr = faiTradeStationBaseApi.getName(order.getInt(BssStatDef.OrderItemInfo.PRODUCT_ID, 0));

            // 退款显示已退款
            int isDel = order.getInt(BssStatDef.OrderItemInfo.DEL, 0);

            if (isDel == 1) {
                status = "已退款";
            }


            // 销售
            String preSaleSalesAcct = "";
            int salesSid = order.getInt(BssStatDef.OrderItemInfo.SALES_SID, 0);
            if (salesSid > 0) {
                preSaleSalesAcct = WebOss.getStaffName(salesSid);
            }

            int goal = order.getInt(BssStatDef.OrderItemInfo.COMPANY_GOAL);
            String corpGoalName = CorpProfDef.getCorpGoal2Name(goal);

            int ta = order.getInt(BssStatDef.OrderItemInfo.TA);
            String taName = PreSaleHdDef.getTaName(taList, ta);

            // 领取时间
            Param preSaleHdInfo = preSaleHdParam.getParam(String.valueOf(aid), new Param());
            Calendar receiveTimeCal = preSaleHdInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);
            String receiveTime = "";
            if (receiveTimeCal != null) {
                receiveTime = Parser.parseString(receiveTimeCal);
            }
            int tag = preSaleHdInfo.getInt(PreSaleHdDef.Info.TAG, 0);
            String tagName = tagMap.get(tag);

            //旧账号
            Param oldParam = approveMap.get(aid);
            int oldAid = 0;
            String oldReceiveTime = "";
            if(oldParam != null){
                oldAid = oldParam.getInt("oldAid",0);
                oldReceiveTime = Parser.parseString(oldParam.getCalendar("receiveTime"));
            }

            order.clear();
            order.setInt("aid", aid);
            order.setString("sales", preSaleSalesAcct);//销售
            order.setDouble("price", price);//价格
            order.setInt("orderId", id);
            order.setString("orderUrl", domain + "/cs/orderDetail.jsp?aid=" + aid + "&id=" + id);
            order.setString("aidUrl", domain + "/index.jsp?t=hdSale&u=/cs/corp.jsp?aid=" + aid);
            order.setString("regTimeStr", regTime);
            order.setString("createTimeStr", createTime);
            order.setString("payTimeStr", payTime);
            order.setString("refundTimeStr", refundTime);
            order.setString("receiveTime", receiveTime);//领取时间
            order.setString("payCorp", Acct.getCorpLabel(aid));//支付企业
            order.setString("payCorpSite", Web.getSiteHost(Acct.getCacct(aid)));//企业站点
            order.setString("type", typeStr);//类型
            order.setString("statusStr", status);//状态
            order.setString("productStr", productStr);//产品类型
            order.setString("corpGoalName", corpGoalName);//用途
            order.setString("ta", taName);//注册来源

            if(oldAid == 0){
                order.setString("oldAid","无");
            }else{
                order.setInt("oldAid",oldAid);
                order.setString("oldReceiveTime",oldReceiveTime);
            }

            if (tagName == null) {
                tagName = "";
            }
            order.setString("tagName", tagName);//标签
        }


        SearchArg searchArgBss = new SearchArg();
        searchArgBss.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidIntList);
        FaiList<String> fieldListBss = new FaiList<String>();
        fieldListBss.add(BssStatDef.Info.AID);
        fieldListBss.add(BssStatDef.Info.REG_MOBILE);
        //FaiList<Param> acctList = sysBssStat.getAllAcctInfoList(fieldListBss, searchArgBss);

        FaiList<Object> outerExpr = new FaiList<Object>();
        outerExpr.add(IN.contains("aid", aidIntList));
        FdpDataParamMatcher matcher = AND.expr(outerExpr);

        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid",  "reg_mobile")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> acctList = execute.getData();

        Param acctParam = new Param(true);
        for (Param acct : acctList) {
            int aid = acct.getInt(BssStatDef.OrderItemInfo.AID, 0);
            acctParam.setParam(String.valueOf(aid), acct);
        }
        for (Param order : orderItemList) {
            int aid = order.getInt(BssStatDef.OrderItemInfo.AID, 0);
            Param acct = acctParam.getParam(String.valueOf(aid), new Param());
            order.setString(BssStatDef.Info.REG_MOBILE, acct.getString(BssStatDef.Info.REG_MOBILE, ""));
        }
        // 导出到excel
        if (excel && isPreSaleLeader) {
            response.setContentType("application/x-excel");
            //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("付款记录.xls".getBytes("GBK"), "ISO-8859-1"));
            out.clear();//必须得加
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString("aid", "aid");
            cellKey.setString("regTimeStr", "注册时间");
            cellKey.setString("orderId", "订单号");
            cellKey.setString("createTimeStr", "创建时间");
            cellKey.setString("payTimeStr", "支付时间");
            cellKey.setString("refundTimeStr", "退款时间");
            cellKey.setString("payCorp", "支付企业");
            cellKey.setString("sales", "售前销售");
            cellKey.setString("price", "金额");
            cellKey.setString("type", "类型");
            cellKey.setString("statusStr", "状态");
            cellKey.setString("productStr", "产品类型");
            cellKey.setString("corpGoalName", "用途");
            cellKey.setString("receiveTime", "领取时间");
//            cellKey.setString("tagName","标签");
//            cellKey.setString("ta", "注册来源");
//            cellKey.setString("oldAid", "旧账号");
//            cellKey.setString("oldReceiveTime", "旧账号领取时间");

            OssPoi.exportExcel(cellKey, orderItemList, outputStream);

            return "{\"success\":true}";
        }
        //Log.logStd("amount=%d sql is [%s]",amount,searchArg.matcher);
        //Log.logStd("li test orderItemList=%s", orderItemList);

        Param returnParam = new Param();
        returnParam.setBoolean("success", true);
        returnParam.setList("dataList", orderItemList);
        returnParam.setInt("total", total);
        returnParam.setDouble("totalPrice", totalPrice);
        returnParam.setInt("totalClient", totalClient);
        returnParam.setObject("searchArg", searchArg.matcher);
        return returnParam.toJson();
    }

    // 获取已认证的销售列表(排除原部门)
    private FaiList<Integer> getSaleList() throws Exception {
        FaiList<Param> saleList = new FaiList<Param>();

        FaiList<Integer> allDepartMentList = new FaiList<Integer>();
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.MH);
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.YK);
        allDepartMentList.add(SalesSystemDef.SaleInfo.SaleDepartment.FA);

        FaiList<Integer> bizList = new FaiList<Integer>();
        bizList.add(SalesSystemDef.SaleInfo.InchargeBiz.HD);
        bizList.add(SalesSystemDef.SaleInfo.InchargeBiz.FLYER);
        bizList.add(SalesSystemDef.SaleInfo.InchargeBiz.WXAST);

        SearchArg searchArg_sale = new SearchArg();
        searchArg_sale.matcher = new ParamMatcher();
        searchArg_sale.matcher.and(SalesSystemDef.SaleInfo.DEPARTMENT, ParamMatcher.IN, allDepartMentList);

        SysCrmSale sysCrmSale = Core.getSysKit(SysCrmSale.class); // 接口

        int rt = sysCrmSale.getSaleInfoListBySearch(searchArg_sale, saleList);
        if (rt != Errno.OK) {
            Log.logErr("getSaleList::getSaleInfoListBySearch err;rt=%s;", rt);
            return new FaiList<Integer>();
        }

        FaiList<Integer> resultList = new FaiList<Integer>();
        for (Param saleInfo : saleList) {
            int sid = saleInfo.getInt(SalesSystemDef.SaleInfo.SID, 0);

            // 循环查各销售业务权限
            FaiList<Param> saleBizList = new FaiList<Param>();
            rt = sysCrmSale.getSaleInchargeBizBySid(sid, saleBizList);
            if (rt != Errno.OK) {
                Log.logErr("getSaleList::getSaleInchargeBizBySid err;rt=%s;sid=%d;", rt,sid);
                continue;
            }

            for (Param saleBizInfo : saleBizList) {
                int saleBiz = saleBizInfo.getInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.BIZ, -1);
                if (bizList.contains(saleBiz)) {
                    resultList.add(saleInfo.getInt(SalesSystemDef.SaleInfo.SaleInchargeBiz.SID));   // 如果拥有权限，则说明经过了认证
                    break;
                }
            }
        }

        return resultList;
    }

    private FaiList<Param> getTaList() throws Exception {
        FaiList<Param> taList = (FaiList<Param>) Core.getTmpData("_taList");
        if (taList == null) {
            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口
            SearchArg searchArg = new SearchArg();
            taList = sysBssStat.getTaList(searchArg);
            Core.setTmpData("_taList", taList);
        }
        return taList;
    }

    private Map getTagMap() throws Exception {
        Param constant = getPageDefInfo("comm", "tag");
        Map<Integer, String> tagMap = new HashMap<Integer, String>();
        if (constant != null) {
            FaiList<Param> tagList = constant.getList("labelList", new FaiList<Param>());
            for (Param item : tagList) {
                int tagItem = item.getInt("label", 0);
                String name = item.getString("name", "");
                tagMap.put(tagItem, name);
            }
        }
        return tagMap;
    }

    private Param getPageDefInfo(String key, String tag) throws Exception {
        Param constant = Web.getConf("constant");
        if (key == null) {
            return new Param();
        }

        Param info = constant.getParam(key, new Param()).clone();
        Param comm = constant.getParam("comm", new Param());
        if ("hdSaleList".equals(key) || "hdPayRecord".equals(key)) {//客户列表还需要tag标签
            info.setParam("tag", comm.getParam("tag"));
        }
        if (tag != null) {
            return info.getParam(tag);
        }
        return info;
    }

    // 获取销售列表（下拉框）
    private String getPreSaleList(HttpServletRequest request) throws Exception {

//        FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");                // 互动销售列表

        boolean isOther = Parser.parseBoolean(request.getParameter("other"), false);
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> sidList = new FaiList<Integer>();                        // 直销互动销售列表
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        if (!isOther) {
            sidList.clear();
            sidList = getSaleList();
        }

        // 获取员工信息
        FaiList<Param> staffList = new FaiList<Param>();
/*        for (int sid : sidList) {
            Param staff = Acct.getStaffInfo(Web.getFaiCid(), sid);
            if (staff == null) {
                continue;
            }
            staffList.add(staff);
        }*/
        for (Integer tmpSid : sidList) {
            Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), tmpSid);
            Param param = new Param();
            param.setInt("sid", tmpSid);
            param.setString("sacct", staffInfo.getString(StaffDef.Info.SACCT, ""));
            param.setString("name", staffInfo.getHtmlString(StaffDef.Info.NAME));
            staffList.add(param);
        }
        Param result = new Param();
        result.setBoolean("success", true);
        result.setList("dataList", staffList);
        return result.toJson();
    }
%>


<%
    String output = "";
    try {
        String cmd = Parser.parseString(request.getParameter("cmd"), "");
        if (Str.isEmpty(cmd)) {
            output = new Result(Errno.ARGS_ERROR, false, Message.NO_METHOD).toJson();
        }

        else if ("getPayRecordNewList".equals(cmd)) {
            output = getPayRecordNewList(request, response, out);
        }

        else if ("getPreSale".equals(cmd)) {
            output = getPreSaleList(request);
        }

    } catch (Exception e) {
        output = new Result(Errno.ERROR, false, Message.SYS_ERR).toJson();
    }

    out.print(output);

%>