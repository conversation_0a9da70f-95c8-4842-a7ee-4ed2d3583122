package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Calendar;

/**
 *  列表查询vo
 * <AUTHOR> 2025/4/18 11:35
 * @Update HLS 2025/4/18 11:35
**/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoListVO {
    // 类型
    private int type = -1;

    // 原型名称 / id / apiKey
    private String key = "";

    // 原型名称 / id / apiKey 值
    private String keyword = "";

    // 状态
    private int status = -1;

    // 创建时间
    private Calendar createTimeStart;

    private Calendar createTimeEnd;

    // 分页
    private int pageNo = 1;

    private int pageLimit = 20;

    public Integer getOffset() {
        return (pageNo - 1) * pageLimit;
    }

}
