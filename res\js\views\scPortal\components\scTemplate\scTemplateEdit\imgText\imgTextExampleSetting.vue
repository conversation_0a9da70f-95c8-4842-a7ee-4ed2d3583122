<template>
  <div class="mt-[30px]">
    <p class="text-[16px] font-bold">模板示例配置</p>
    <p class="example-title-required mr-[10px] ml-[10px] mt-[20px]">示例贴文</p>
    <div class="ml-[20px]">
      <el-form-item prop="script.title">
        <p>标题</p>
        <el-input
          v-model="templateInfo.script.title"
          class="!w-[500px]"
          type="textarea"
          :rows="2"
          :maxlength="100"
          show-word-limit
          :autosize="{ minRows: 2, maxRows: 4 }"
        />
      </el-form-item>

      <el-form-item prop="script.content">
        <p>作品描述</p>
        <el-input
          class="!w-[500px]"
          v-model="templateInfo.script.content"
          type="textarea"
          :rows="4"
          :maxlength="500"
          show-word-limit
          :autosize="{ minRows: 4, maxRows: 8 }"
        />
      </el-form-item>
    </div>
    <p class="example-title-required mr-[10px] ml-[10px] mt-[20px]">示例图片</p>
    <div class="ml-[20px]">
      <el-form-item
        prop="cover"
        :rules="[{ validator: validateCover, message: '请上传封面图' }]"
      >
        <p class="text-[14px] mt-[10px] mb-[10px]">封面图</p>
        <UploadImg
          title="上传封面图"
          :file-list="templateInfo._coverFileList"
          :limit="1"
          @upload-success="uploadCoverSuccess"
          @upload-remove="uploadCoverRemove"
        />
      </el-form-item>

      <el-form-item
        prop="setting"
        :rules="[{ validator: validateSubImgResId, trigger: 'submit' }]"
      >
        <p class="text-[14px] mt-[10px] mb-[10px]">次图</p>
        <UploadImg
          title="上传次图"
          :file-list="templateInfo._imgFileList"
          :limit="50"
          @upload-success="uploadSubImgSuccess"
          @upload-remove="uploadSubImgRemove"
        />
      </el-form-item>
    </div>
  </div>
</template>

<script>
import UploadImg from "@/views/scPortal/components/scTemplate/common/uploadImg.vue";
export default {
  name: "ImgTextExampleSetting",
  components: {
    UploadImg,
  },
  props: {
    templateInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      testFileList: [
        {
          name: "food.jpeg",
          url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
        },
        {
          name: "food2.jpeg",
          url: "https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100",
        },
      ],
    };
  },
  methods: {
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    /**
     * 上传封面图成功
     * @param response 上传响应
     * @param row 当前行
     */
    uploadCoverSuccess(response) {
      this.templateInfo.cover = {
        resId: response.id, // 图片id
        resType: response.type, // 图片类型
      };
    },
    /**
     * 删除封面图
     * @param response 上传响应
     * @param row 当前行
     */
    uploadCoverRemove(response) {
      this.templateInfo.cover = {};
    },
    /**
     * 上传次图成功
     * @param response 上传响应
     * @param row 当前行
     */
    uploadSubImgSuccess(response) {
      if (!this.templateInfo?.setting?.imgList) {
        this.templateInfo.setting = {
          imgList: [],
        };
      }
      this.templateInfo.setting.imgList.push({
        resId: response.id, // 图片id
        resType: response.type, // 图片类型
      });
    },
    /**
     * 删除次图
     * @param response 上传响应
     * @param row 当前行
     */
    uploadSubImgRemove(response) {
      this.templateInfo.setting.imgList =
        this.templateInfo.setting.imgList.filter(
          (item) => item.resId !== response.id
        );
    },
    /**
     * 验证是否有传次图
     */
    validateSubImgResId(rule, value, callback) {
      if (!value?.imgList || value?.imgList?.length === 0) {
        callback(new Error("请上传次图"));
        return;
      }
      callback();
    },
    validateCover(rule, value, callback) {
      if (!value?.resId) {
        callback(new Error("请上传封面图"));
        return;
      }
      callback();
    },
  },
};
</script>

<style lang="scss" scoped>
.tag {
  padding: 2px 4px;
  margin: 0 1px;
  color: #3261fd;
}

/* 添加标签样式 */
.tag-label {
  display: inline-block;
  border-radius: 4px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  padding: 0 4px;
  line-height: 20px;
}

.example-title-required {
  &::before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
}
</style>
