/**
 * 同步原型
 * 编辑时传id
 * 创建时传type和apiKey
 * 修改apikey时，传id和apiKey
 * @param {*} data 
 * @param {string} data.type  类型：0-视频、1-图文
 * @param {string} data.apiKey  apiKey（同步或者编辑同步时需要传）
 * @param {string} data.id 原型id（获取和编辑同步时需要传）
 * @returns 
 */
export const syncProto = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/proto/syncProto", {params: data}).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 获取素材列表
 * @param {*} data 
 * @param {number} data.type 类型：0-视频、1-图文
 * @param {string} data.keyword 关键字
 * @param {string} data.createTimeStart 创建时间开始
 * @param {string} data.createTimeEnd 创建时间结束
 * @param {number} data.pageNo 页码
 * @param {number} data.pageLimit 每页条数
 * @returns 
 */
export const getScProtoList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/proto/getScProtoList", data, {
      headers: {
        'Content-Type': 'application/json'
      }
    }).then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  });
};

/**
 * 添加修改原型接口
 * @param {*} data 
 * @returns 
 */
export const setScProtoInfo = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/proto/setScProtoInfo", data, {
      headers: {
        'Content-Type': 'application/json'
      },
      emulateJSON: true,
    }).then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  });
};

/**
 * 修改状态
 * @param {*} data 
 * @returns 
 */
export const setScProtoStatus = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/proto/setScProtoStatus", {params: data}).then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  });
};

/**
 * 获取原型名称列表
 * @param {*} data  
 * @returns 
 */
export const getScProtoNameList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/proto/getScProtoNameList", data, {emulateJSON: true}).then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  });
};

/**
 * 获取配置信息
 * @param {*} data 
 * @returns 
 */
export const getConfInfo = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/proto/getConfInfo", {params: data}).then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  });
};

/**
 * 获取背景音乐列表
 * @returns 
 */
export const getBgmTree = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/proto/getBgmTree").then(response => {
      resolve(response.body);
    }, error => {
      reject(error);
    });
  })
}

