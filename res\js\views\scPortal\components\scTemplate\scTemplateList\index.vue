<template>
  <div class="sc-list">
    <el-tabs v-model="activeName">
      <el-tab-pane
        label="视频"
        :name="String(ScProductType.VIDEO)"
      ></el-tab-pane>
      <el-tab-pane
        label="图文"
        :name="String(ScProductType.IMGTEXT)"
      ></el-tab-pane>
    </el-tabs>
    <div class="mb-[20px] sc-list-header">
      <el-button
        type="primary"
        @click="addTemplate(ScProductType.VIDEO)"
        v-if="activeName === String(ScProductType.VIDEO)"
        >添加视频模板</el-button
      >
      <el-button
        type="primary"
        @click="addTemplate(ScProductType.IMGTEXT)"
        v-else
        >添加图文模板</el-button
      >
    </div>
    <div class="flex mb-[20px] sc-list-filter">
      <!-- 关键词搜索 -->
      <div>
        <el-input
          size="small"
          :placeholder="curPlaceholder"
          :clearable="true"
          v-model.trim="filterData.keyword"
          class="!w-[400px] mr-[30px] multiple-input"
          @keyup.enter.native="searchList"
          @clear="searchList"
        >
          <template slot="prepend">
            <el-select v-model="filterData.key" placeholder="请选择">
              <el-option
                v-for="item in filterTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-input>
      </div>
      <!-- 上架状态 -->
      <div class="mr-[30px]">
        <span class="mr-[5px] text-[14px]">状态:</span>
        <el-select v-model="filterData.status" placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>

      <div class="mr-[30px]">
        <span class="mr-[5px] text-[14px]">创建时间:</span>
        <el-date-picker
          class="!w-[240px]"
          :pickerOptions="pickerOptions"
          size="small"
          v-model="filterData.timeString"
          type="daterange"
        ></el-date-picker>
      </div>
      <el-button size="small" type="primary" @click="searchList"
        >搜索</el-button
      >
    </div>
    <div class="sc-list-table">
      <el-table :data="tableData" row-class-name="sc-list-table-row">
        <el-table-column prop="id" label="模板ID" width="100" />
        <el-table-column prop="industry" label="所属行业" width="100" />
        <el-table-column prop="scene" label="所属场景" width="100" />
        <el-table-column prop="statusName" label="上架状态" width="100" />
        <el-table-column prop="name" label="模板名称" width="300" />
        <el-table-column prop="updateTime" label="最近编辑时间" width="200" />
        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="protoId" label="关联原型" width="100" />
        <el-table-column fixed="right" label="操作栏" min-width="200">
          <template slot-scope="scope">
            <el-button type="text" @click="editTemplate(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" @click="copyTemplate(scope.row)"
              >复制</el-button
            >
              <!-- 使用动态按钮列表 -->
            <el-button
              v-for="btn in getStatusButtons(scope.row)"
              :key="btn.key"
              type="text"
              @click="btn.action"
            >{{ btn.text }}</el-button>
            <el-tooltip
              content="模板已上架，如需删除模板先下架模板"
              v-if="scope.row.status != TemplateStatus.OFF_SHELF"
            >
              <el-button type="text" :disabled="true">删除</el-button>
            </el-tooltip>
            <el-button
              type="text"
              @click="deleteTemplate(scope.row, TemplateStatus.DELETE)"
              v-else
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt-[20px] flex justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageConfig.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageConfig.pageLimit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageConfig.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  ScProductType,
  TemplateStatus,
} from "@/views/scPortal/config/index.js";
import {
  getScTemplateList,
  copyScTemplate,
  setScTemplateStatus,
} from "@/views/scPortal/api/scTemplate.js";
import { checkPassOperateFunc } from "@/views/scPortal/utils/index.js";

export default {
  name: "ScTemplateList",
  props: {
    isReloadList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ScProductType,
      TemplateStatus,
      activeName: String(ScProductType.VIDEO),
      filterData: {
        status: -1, // 上架状态
        key: "all", // 搜索类型
        keyword: "", // 搜索关键字
        timeString: "", // 时间
      },
      // 上架状态
      statusOptions: [
        {
          value: -1,
          label: "全部",
        },
        {
          value: TemplateStatus.NORMAL,
          label: "已上架",
        },
        {
          value: TemplateStatus.OFF_SHELF,
          label: "未上架",
        },
        {
          value: TemplateStatus.DRAFT,
          label: "内部上架中",
        },
      ],
      // 搜索类型
      filterTypeOptions: [
        {
          value: "all",
          label: "全部",
          placeholder: "请输入模板名称/模板ID/关联原型id"
        },
        {
          value: "name",
          label: "模板名称",
          placeholder: "请输入模板名称",
        },
        {
          value: "id",
          label: "模板ID",
          placeholder: "请输入模板ID",
        },
        {
          value: "protoId",
          label: "关联原型ID",
          placeholder: "请输入关联原型id",
        },
      ],
      pageConfig: {
        pageNo: 1,
        pageLimit: 10,
        total: 0,
      },
      selectOptions: [
        {
          value: ScProductType.ALL,
          label: "全部类型",
        },
        {
          value: ScProductType.VIDEO,
          label: "视频",
        },
        {
          value: ScProductType.IMGTEXT,
          label: "图文",
        },
      ],
      tableData: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近七天",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近一个月",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created() {
    const storeActiveName = this.$store.state.scTemplate.curActiveName;
    if (storeActiveName) {
      /**
       * 特殊处理：
       * 第一次切换组件后再切换回来，keep-alive没用缓存组件，
       * 而是新建了组件（应该是vue内部机制判断问题），
       * 所以特殊处理，用于第一次切换的特殊情况，保持切换到相应tab
       * 第二次就正常用缓存了
       */
      this.activeName = storeActiveName;
    }

    this.getTemplateList();
  },
  activated() {
    if (this.isReloadList) {
      this.getTemplateList();
    }
  },
  watch: {
    activeName(val) {
      // 当 activeName 变化时，更新 Vuex 中的值
      this.$store.commit('scTemplate/curActiveName', val);
      this.pageConfig.pageNo = 1;
      this.getTemplateList();
    },
  },
  computed: {
    ...Vuex.mapState('scPortal', ['staffInfo']),
    curPlaceholder() {
      return this.filterTypeOptions.find(
        (item) => item.value === this.filterData.key
      ).placeholder;
    },
  },
  methods: {
    /**
     * 后端取日期零点，需要加上一天
     */
    addOneDay(date) {
      if (date) {
        var dateTime = new Date(date);
        dateTime = dateTime.setDate(dateTime.getDate() + 1);
        return new Date(dateTime).getTime();
      }
      return 0;
    },
    searchList() {
      this.pageConfig.pageNo = 1;
      this.getTemplateList();
    },
    /**
     * 编辑模板
     * @param row
     */
    editTemplate(row) {
      const url = `${window.location.origin}/#/scPortal/scTemplate?id=${row.id}&protoId=${row.protoId}`;
      window.open(url, "_blank");
    },
    /**
     * 复制模板
     * @param row
     */
    copyTemplate(row) {
      copyScTemplate({
        id: row.id,
      }).then((res) => {
        if (res && res.success) {
          this.$message.success("复制成功");
          this.getTemplateList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /**
     * 下架模板
     * @param row
     * @param status
     */
    offSaleTemplate(row, status) {
      if (!checkPassOperateFunc()) {
        this.$message({
          showClose: true,
          type: 'error',
          message: '当前账号无下架模板权限'
        });
        return;
      }
      this.$confirm("确定下架该模板吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.setStatus(row, status);
      });
    },
    /**
     * 删除模板
     * @param row
     * @param status
     */
    deleteTemplate(row, status) {
      if (!checkPassOperateFunc()) {
        this.$message({
          showClose: true,
          type: 'error',
          message: `当前账号无删除模板权限`
        });
        return;
      }
      this.$confirm("确定删除该模板吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.setStatus(row, status);
      });
    },
    /**
     * 设置状态
     * @param row
     * @param status
     */
    setStatus(row, status) {
      if ([TemplateStatus.NORMAL].includes(status)) {
        if (!checkPassOperateFunc()) {
          let operateMsg = ''
          switch (status) {
            case TemplateStatus.NORMAL:
              operateMsg = '上架'
              break;
          }
          this.$message({
            showClose: true,
            type: 'error',
            message: `当前账号无${operateMsg}模板权限`
          });
          return;
        }
      }
      setScTemplateStatus({
        id: row.id,
        status: status,
      }).then((res) => {
        if (res && res.success) {
          let msg = "";
          switch (status) {
            case TemplateStatus.NORMAL:
              msg = "上架成功";
              break;
            case TemplateStatus.OFF_SHELF:
              msg = "下架成功";
              break;
            case TemplateStatus.DRAFT:
              msg = "内部上架成功";
              break;
            case TemplateStatus.DELETE:
              msg = "删除成功";
              break;
          }
          this.$message.success(msg);
          this.getTemplateList();
        } else {
          this.$message.error(res.message);
        }
      });
    },
    /**
     * 添加模板
     * @param type
     */
    addTemplate(type) {
      this.$emit("changeComponent", "ScTemplateEdit", {
        createType: type,
      });
    },
    getTemplateList() {
      let newParams = {
        type: Number(this.activeName), // 产品类型
        key: this.filterData.key, // 搜索类型
        keyword: this.filterData.keyword, // 搜索关键字
        status: this.filterData.status, // 状态
        pageNo: this.pageConfig.pageNo, // 当前页
        pageLimit: this.pageConfig.pageLimit, // 每页条数
      };
      if (this.filterData.timeString && this.filterData.timeString.length) {
        // 筛选时间
        let [startDate, endDate] = this.filterData.timeString;
        console.log(startDate, endDate, "startDate, endDate");
        Object.assign(newParams, {
          createTimeStart: this.formatDate(new Date(startDate)),
          createTimeEnd: this.formatDate(new Date(this.addOneDay(endDate))),
        });
      }
      getScTemplateList(newParams)
        .then((res) => {
          if (res && res.success) {
            this.tableData = res.data;
            this.pageConfig.total = res.totalSize;
            console.log(this.tableData, "getScTemplateList");
          } else {
            this.tableData = [];
          }
        })
        .catch((error) => {
          console.error("获取列表失败:", error);
          this.tableData = [];
        });
    },
    handleSizeChange(size) {
      this.pageConfig.pageLimit = size;
      this.getTemplateList();
    },
    handleCurrentChange(page) {
      this.pageConfig.pageNo = page;
      this.getTemplateList();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    /**
     * 根据模板状态获取可显示的状态操作按钮
     * @param {Object} row 当前行数据
     * @returns {Array} 按钮配置数组
     */
    getStatusButtons(row) {
      const buttons = [];
      
      // 上架按钮：下架状态或草稿状态时显示
      if ([this.TemplateStatus.OFF_SHELF, this.TemplateStatus.DRAFT].includes(row.status)) {
        buttons.push({
          key: 'normal',
          text: '上架',
          action: () => this.setStatus(row, this.TemplateStatus.NORMAL)
        });
      }
      
      // 内部上架按钮：只有下架状态时显示
      if (row.status === this.TemplateStatus.OFF_SHELF) {
        buttons.push({
          key: 'draft',
          text: '内部上架',
          action: () => this.setStatus(row, this.TemplateStatus.DRAFT)
        });
      }
      
      // 下架按钮：只有正常状态时显示
      if (row.status === this.TemplateStatus.NORMAL) {
        buttons.push({
          key: 'off',
          text: '下架',
          action: () => this.offSaleTemplate(row, this.TemplateStatus.OFF_SHELF)
        });
      }
      
      return buttons;
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "../../styles/tailwind.css";
.sc-list-input {
  ::v-deep input {
    height: 32px;
  }
}

.sc-list-select {
  ::v-deep .el-input__icon {
    line-height: 30px;
  }
}

.multiple-input {
  ::v-deep {
    .el-input-group__prepend {
      background: #fff;
    }
    .el-input__inner {
      color: #606266 !important;
    }
  }
}

::v-deep .el-tabs__item {
  font-size: 16px;
  font-weight: bold;
}

.el-table {
  ::v-deep .cell {
    word-break: break-word;
    white-space: pre-wrap;
  }
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px; /* 统一按钮间距 */
}

.button-group .el-button {
  margin: 0 !important; /* 清除默认margin */
}
</style>
