package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 原型VO
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/17 16:45
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoVO {
    private int id;

    // dify工作流—api_key
    @NotBlank(message = "apiKey不能为空")
    private String apiKey;

    // 原型类型（视频：0，图文：1）
    private int type;

    // dify工作流—名称 ：来源dify名称，可修改
    @NotBlank(message = "名称不能为空")
    private String name;

    // dify工作流—输入表单
    @NotNull(message = "原型表单数据不能为空")
    private List<ScProtoInputFormVO> inputFormList;

    // dify工作流—脚本结构-通用配置
    @NotNull(message = "配置数据不能为空")
    private List<ScProtoScriptCommVO> commList;

    // dify工作流—脚本结构-封面
    private ScProtoScriptGraphicVO cover;

    // dify工作流—脚本结构-次级
    private ScProtoScriptGraphicVO secondary;

    // 原型编辑—资源表单
    @NotNull(message = "素材组配置不能为空")
    private List<ScProtoResFormVO> resFormList;
}
