import ElVirtualSelect from '../components/ElVirtualSelect.vue'
Vue.component('el-virtual-select', ElVirtualSelect);


window.qqq = new Vue({
  el: '#app',
  data: function data() {
    var isRules = true; // 是否开启验证

    return {
      rules: {
        // name: isRules ? [{required: true, message: '请输入专题名称', trigger: 'blur'}] : [],
        model: isRules ? [{ required: true, message: '请选择活动样板', trigger: 'change' }] : [],
      },
      form: [{
        scene: '10',
        title: "热门模板",
        num: {
          min: 6,
          max: 50
        },
      }, {
        scene: '11',
        title: "活动专题一",
        name: "",
      }, {
        scene: '12',
        title: "活动专题二",
        name: "",
      }, {
        scene: '13',
        title: "活动专题三",
        name: "",
      }, {
        scene: '14',
        title: "美团专属热门模板",
        name: "",
        num: {
          min: 6,
          max: 50
        }
      }],
      activeIndex: '10',
      options: []
    };
  },
  created: function () {
    this.initData();
    this.getModelListByCond();
    this.handleSelect(this.activeIndex);
  },
  methods: {
    initData: function (index) {
      this.form.forEach(item => {
        this.$set(item, 'total', 6);
        this.$set(item, 'models', new Array(6).fill(''));
      });
    },

    submitForm: function (index) {
      var _this = this;

      this.$refs.form[index].validate(function (valid) {
        if (valid) {
          var _this$form$index = _this.form[index],
            name = _this$form$index.name,
            models = [];

          for (let i = 0; i < _this$form$index.total; i++) {
            models.push(_this$form$index.models[i]);
          }

          var data = {
            displayType: 4,
            category: _this.activeIndex,
            modelList: JSON.stringify(models)
          };
          name && (data.name = name);
          debugger
          $.ajax({
            type: 'post',
            url: '/api/caseMode/setCategoryDisplay',
            data: data,
            dataType: "json"
          }).then(function (res) {
            var success = res.success,
              msg = res.msg;

            if (success) {
              _this.$message({
                message: '保存成功',
                type: 'success',
                duration: 1500,
              });
            } else {
              _this.$message({
                message: msg,
                type: 'error',
                duration: 1500,
              });
            }
          });
        } else {
          _this.$message({
            message: '请填写完整活动样板',
            type: 'error',
            duration: 1500,
          });

          return false;
        }
      });
    },
    handleSelect: function (index) {
      this.activeIndex = index;
      var item = this.form.find(function (item) {
        return item.scene === index;
      });
      if (item.models.findIndex(function (item) {
        return item;
      }) >= 0) return;
      $.ajax({
        type: 'post',
        url: '/api/caseMode/getCategoryDisplay',
        data: {
          displayType: 4,
          category: index
        },
        dataType: "json"
      }).then(function (res) {
        var data = res.data,
          success = res.success;

        if (!success) return;

        if (typeof data.modelList != 'string') {
          item.total = data.modelList.length;
          item.models = data.modelList.map(i => i.id);
        }

        'name' in item && (item.name = data.name);
      });
    },
    getModelListByCond: function () {
      var _this = this;

      $.ajax({
        type: 'post',
        url: '/api/caseMode/getModelListByCond',
        data: {
          displayType: 4,
          scene: -1
        },
        dataType: "json"
      }).then(function (res) {
        var success = res.success,
          data = res.data,
          msg = res.msg;
        if (!success) return _this.$message({
          message: msg,
          type: 'error',
          duration: 1500,
        });
        _this.options = Object.keys(data).map(key => data[key]);
      });
    }
  }
});