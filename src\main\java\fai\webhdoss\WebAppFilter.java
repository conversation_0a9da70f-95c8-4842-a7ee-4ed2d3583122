package fai.webhdoss;


import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import fai.comm.util.*;
import fai.web.App;
import fai.web.Auth;
import fai.web.Core;
import fai.web.HdJumper;
import fai.web.Request;
import fai.web.Response;
import fai.web.Web;
import fai.web.WebException;
import fai.web.inf.Kid;
import fai.web.inf.SysSession;

public class WebAppFilter implements Filter {
	
	private static String GE_DEPLOY_LOG_MARK = "GE_DEPLOY";
	
	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest requestTmp, ServletResponse responseTmp, FilterChain chain) throws ServletException, IOException {

		HttpServletResponse resp = (HttpServletResponse) responseTmp;
		HttpServletRequest req = (HttpServletRequest) requestTmp;
		resp.setHeader("Access-Control-Allow-Origin", "*");
		resp.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
		resp.setHeader("Access-Control-Max-Age", "3600");
		resp.setHeader("Access-Control-Allow-Credentials", "true");
		resp.setHeader("Access-Control-Allow-Headers", "*");

		if (requestTmp == null || responseTmp == null) {
			return;
		}

		if (requestTmp instanceof HttpServletRequest && responseTmp instanceof HttpServletResponse) {
			HttpServletRequest request = (HttpServletRequest) requestTmp;
			
			HttpServletResponse response = (HttpServletResponse) responseTmp;
			boolean hasSession = false;
			// 清掉已经无效的cookie，避免cookie已经无效后，还多次验证sessionid
			SysSession session = (SysSession) Core.getSysKit(Kid.SYS_SESSION);
			String sessionId = session.getId();
			if (sessionId != null && !sessionId.isEmpty()) {
				try {
					if (session.checkValid(Core.getWid())) {
						hasSession = true;
					}else{
						Response.addCookie(response, Request.CookieId.SESSION_ID, "");
						return;
					}
				} catch (Exception exp) {
					Log.logErr(exp);
				}
			}
			
			// 设置唯一的访客id
			if (Request.getBrowserType(request) != Request.BrowserType.SPIDER) {
				String cliId = Request.getCookie(request, Request.CookieId.CLI_ID);
				if (cliId == null || cliId.isEmpty()) {
					cliId = Core.getCliId();
					Response.addCookie(response, Request.CookieId.CLI_ID, cliId, Request.CookieMaxAge.CLI_ID);
				}	
			}
			
			

			String uri = request.getRequestURI();
			boolean hello = uri.indexOf("helo.jsp")>0;
			boolean agentCopy = uri.indexOf("hdGameCopy_h.jsp")>0;
			//Log.logDbg("greakjack uri="+uri);
			//Log.logDbg("greakjack agentCopy="+agentCopy);
			if( !hasSession  && !hello && !agentCopy){
				try{
					//sso单点登录模式
					if (WebHdOss.useFaiSession()) {
						String ssoUrl = WebHdOss.getAccoutLoginUrl();
						String backUrl = "http://"+Request.getHostName();
						// 没有ticket,重定向到sso登录中心
						String ticket = request.getParameter("serviceTicket");
						Log.logStd("backUrl============"+backUrl);
						if (Str.isEmpty(ticket)) {
							Log.logStd("%s not session", GE_DEPLOY_LOG_MARK);
							String finalUrl = ssoUrl + backUrl;
							Log.logStd("finalUrl============"+finalUrl);
							HdJumper.sendRedirect(response, finalUrl);
							return;
						}

						// 有ticket,校验ticket
						Ref<String> sessionIdRef = new Ref<String>();
						int rt = Auth.loginFaiCorp(response, ticket, sessionIdRef);
						if (rt == Errno.OK) {
							App.logDog(100004, 1, Web.getFaiCid()); // 统计登录
							Log.logStd("%s check ticket ok; session",GE_DEPLOY_LOG_MARK);
							HdJumper.sendRedirect(response, backUrl);
							return;
						}
						// 校验不成功
						Log.logErr("%s check session error;rt=%d", GE_DEPLOY_LOG_MARK, rt);
						HdJumper.sendRedirect(response, ssoUrl + backUrl);
					}else if(!WebHdOss.checkSession(response)){
						Log.logStd("%s yansen hdoss no login", GE_DEPLOY_LOG_MARK);
						return;
					}
				}catch(Exception e){
					Log.logErr(e);
				}
			}
			
			
			//客服栏目下的所有ajax操作要验证 token
			boolean checkToken = uri.indexOf("ajax/") >= 0 && uri.indexOf("_h.jsp") > 0  &&uri.indexOf("navList_h") <0 && uri.indexOf("hdSale_h") <0  && uri.indexOf("ykSale_h") <0 && uri.indexOf("hdProduct_h")<0 && uri.indexOf("hdSale")<0  && uri.indexOf("ykSale")<0  && uri.indexOf("corp_h")<0 && uri.indexOf("hdPartnerAcct_h")<0;
			if( hasSession && checkToken ){
				String token = request.getParameter( Request.CookieId.TOKEN );
				boolean tokenerr = false;
				if( token == null ){
					String referer = request.getHeader("referer");
					Log.logStd("hdoss token error. token is null.session=%s;referer=%s;uri=%s;parameter=%s;",sessionId,referer,uri,Str.cut(Request.getParameterList( request ).toJson(), 200));
					tokenerr = true;
				}else if( !token.equals( Encryptor.md5(sessionId+Request.TOKEN_TAIL) ) ){
					String referer = request.getHeader("referer");
					Log.logStd("hdoss token error. token is error.session=%s;md5Token=%s;referer=%s;token=%s;uri=%s;parameter=%s;",sessionId,Encryptor.md5(sessionId+Request.TOKEN_TAIL),referer,token,uri,Str.cut(Request.getParameterList( request ).toJson(), 200));
					tokenerr = true;
				}
				if(tokenerr && Web.getConf("site").getBoolean("antCsrf",false)){
					String json = null;
					try {
						json = WebHdOss.checkAjaxException(new WebException( Errno.WEB_TOKEN_ERR, 1 ));
					} catch (Exception e) {
						Log.logErr( e );
						json = "{\"success\":false,\"msg\":\"系统错误\"}";
					}
					response.getOutputStream().print( json );
					return;
				}
			}

			//dep2 qps监控
			if (Web.isPre()) {
				Fdp.bssMonitor(10651);
			}

		}
		chain.doFilter(requestTmp, responseTmp);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
	}
}