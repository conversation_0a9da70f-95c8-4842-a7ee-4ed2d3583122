package fai.webhdoss.controller;


import fai.app.*;
import fai.cli.HdBssStatCli;
import fai.cli.HdGameCli;
import fai.cli.HdOssAffairCli;
import fai.cli.HdStatCli;
import fai.cli.cliUtil.HdTradeUtil;
import fai.comm.util.*;
import fai.hdUtil.CollectionUtil;
import fai.hdUtil.JsonResult;
import fai.web.*;
import fai.web.inf.*;
import fai.webhdoss.model.vo.HdActivityListVO;
import fai.webhdoss.model.vo.HdStyleVo;
import fai.weboss.OssCorp;
import fai.weboss.OssHd;
import fai.weboss.WebOss;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * o系统活动列表相关
 * @date 2021-09-03
 * <AUTHOR>
 */
@RestController
@Api(value = "o系统活动列表相关")
@RequestMapping("/activityList")
public class HdActivityListController {

    @Autowired
    private HdBssStatCli bssStatCli;

    @Autowired
    private HdOssAffairCli ossAffairCli;

    @Autowired
    private HdGameCli hdGameCli;
    /**
     * o系统活动列表查询
     * <AUTHOR>
     * @date 2021-09-23
     */
    @CrossOrigin("*")
    @PostMapping("/getGameListByArgs")
    @ApiOperation(value = "o系统活动列表查询", httpMethod = "POST")
    public JsonResult getGameListByArgs(@Valid @RequestBody HdActivityListVO hdActivityVo) {
        try {
            //获取gameList
            Param result = getOssGameList(hdActivityVo);
            FaiList<Param> resultList = result.getList("list",new FaiList<>());
            Log.logDbg("list.size:"+resultList.size());
            Param data = result.getParam("data", new Param());
            //获取aid集合
            Set<Integer> aidSet = resultList.stream().map(game -> game.getInt(HdGameDef.Info.AID, 0)).collect(Collectors.toSet());

            FaiList<Param> acctList = OssCorp.getAcctList(new FaiList<>(aidSet));
            Map<Integer, Param> acctMap = acctList.stream().collect(Collectors.toMap(i -> i.getInt("aid"), i -> i));

            //获取所有模板信息
            Map<Integer, Param> modelMap = getModelMap();

            //查询条件中如果有行业分类，活动分类，场景分类的条件就移除相应的活动
            boolean checkCate = getCheckCate(hdActivityVo);
            if (checkCate){
                resultList.removeIf(item-> isDelete(item, hdActivityVo, modelMap) );
            }

            //查询条件中如果有客户行业条件就移除相应的活动
            if (hdActivityVo.getAcctTrade()>0){
                FaiList<Param> acctTradeList = getAcctTradeList(aidSet, hdActivityVo.getAcctTrade());
                resultList.removeIf(p -> !acctTradeList.contains(p));
            }

            // 补充浏览人数
            addViewCnt(resultList);

            //统计resultList中活动的浏览人数，玩家人数，分享人数  结果放在data中
            countData(resultList, data);

            //并填充活动的具体数据
            fillData(resultList, acctMap);

            //当前页面中aid数量
            int acctCount = resultList.stream().map(game -> game.getInt(HdGameDef.Info.AID, 0)).collect(Collectors.toSet()).size();
            data.setInt("accCount", acctCount);
            data.setInt("gameCount", resultList.size());

            return JsonResult.success(result);
        } catch (Exception e) {
            return JsonResult.error(e.getMessage());
        }
    }

    private Param getOssGameList(HdActivityListVO hdActivityVo) {
        Param result = new Param();

        Param matcherParam = new Param().setInt("aid", hdActivityVo.getAid())
                .setInt("agentAid", hdActivityVo.getAgentAid())
                .setInt("pageNo", hdActivityVo.getPageNo())
                .setInt("pageLimit", hdActivityVo.getPageLimit())
                .setInt("style", hdActivityVo.getGstyle())
                .setInt("gameStatus", hdActivityVo.getGstatus())
                .setInt("openPurpose", hdActivityVo.getOpenPurpose())
                .setInt("key1", hdActivityVo.getKey1())
                .setInt("key2", hdActivityVo.getKey2())
                .setInt("key3", hdActivityVo.getKey3())
                .setInt("gbuy", hdActivityVo.getGbuy())
                .setInt("gplatform", hdActivityVo.getGplatform())
                .setInt("share", hdActivityVo.getShare())
                .setInt("view", hdActivityVo.getView())
                .setInt("player", hdActivityVo.getPlay())
                .setInt("player1", hdActivityVo.getPlay1())
                .setInt("gameId", hdActivityVo.getGameId())
                .setInt("timetype", hdActivityVo.getTimetype())
                .setInt("report", hdActivityVo.getInform())
                .setInt("indexReplaceNum", hdActivityVo.getIndexReplaceNum())
                .setInt("otherReplaceNum", hdActivityVo.getOtherReplaceNum())
                .setInt("saleCover", hdActivityVo.getSaleCover())
                .setString("keyword", hdActivityVo.getSearchWord())
                .setCalendar("beginTime", Parser.parseCalendar(hdActivityVo.getBegDate() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"))
                .setCalendar("endTime", Parser.parseCalendar(hdActivityVo.getEndDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"))
                .setCalendar("startPayTime", Parser.parseCalendar(hdActivityVo.getBegPay() + " 00:00:00", "yyyy-MM-dd HH:mm:ss"))
                .setCalendar("endPayTime", Parser.parseCalendar(hdActivityVo.getEndPay() + " 23:59:59", "yyyy-MM-dd HH:mm:ss"))
                .setInt("createType", hdActivityVo.getCreateType());
        Log.logErr("dkBUG matcherParam=%s", matcherParam);


        SearchArg searchArg = new SearchArg();
        searchArg.start = (hdActivityVo.getPageNo() - 1) * hdActivityVo.getPageLimit();
        searchArg.limit = hdActivityVo.getPageLimit();
        searchArg.totalSize = new Ref<>();
        int rt = bssStatCli.ossGetGameList(matcherParam, searchArg, result);
        Log.logDbg("totalSize = %s", searchArg.totalSize);
        Param data = result.getParam("data",new Param());
        int pageLimit = hdActivityVo.getPageLimit();
        long totalGameCount = 0;
        if(searchArg.totalSize != null && searchArg.totalSize.value !=null){
            totalGameCount = searchArg.totalSize.value;
        }
        if(totalGameCount==0){
            result.setString("msg","没有数据！");
        }
        data.setLong("gameCount",totalGameCount);
        long pageCount = (totalGameCount%pageLimit)==0?totalGameCount/pageLimit:(totalGameCount/pageLimit)+1;
        data.setLong("pageCount",pageCount);
        return result;
    }

    private Map<Integer, Param> getModelMap(){
        Dao ossHdDao = null;
        try {
            ossHdDao = WebOss.getSlaveHdOssAffairDao();
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdModel";
            sltArg.searchArg.matcher = new ParamMatcher();
            return ossHdDao.select(sltArg).stream().collect(Collectors.toMap(i->i.getInt("id"), i->i));
        } finally {
            if (null != ossHdDao) {
                ossHdDao.close();
            }
        }
    }

    private Param getGameFromDb(int aid,int gameId){
        Param game = new Param();
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdGameDef.Info.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(HdGameDef.Info.ID, ParamMatcher.EQ, gameId);
        // searchArg.file
        FaiList<Param> list = new FaiList<Param>();
        hdGameCli.getDBGameList(aid,searchArg,list);
        if(list.size() == 1){
            game = list.get(0);
        }
        return game;
    }

    /**
     * 查询条件中是否有行业分类，活动分类，场景分类
     * @param hdActivityVo
     * @return
     */
    private Boolean getCheckCate(HdActivityListVO hdActivityVo){
        boolean checkCate = false;
        if(hdActivityVo.getTrade() != 0 || hdActivityVo.getTrade2() !=0 || hdActivityVo.getCategory1() !=0 || hdActivityVo.getCategory2() !=0){
            checkCate = true;
        }
        return checkCate;
    }

    private void addViewCnt(FaiList<Param> resultList) {
        HdStatCli cli = FaiCliFactory.createCli(HdStatCli.class, Core.getFlow());

        int rt;
        FaiList<Param> statList;
        ParamMatcher matcher;
        for (Param p : resultList) {
            int p_aid = p.getInt(HdGameDef.Info.AID, 0);
            int p_gameId = p.getInt(HdGameDef.Info.ID, 0);
            int p_view = p.getInt(HdGameDef.Info.VIEW, 0);

            statList = new FaiList<Param>();
            matcher = new ParamMatcher(HdStatDef.StatLogs.AID, ParamMatcher.EQ, p_aid);
            matcher.and(HdStatDef.StatLogs.GAME_ID, ParamMatcher.EQ, p_gameId);
            matcher.and(HdStatDef.StatLogs.STAT_TYPE, ParamMatcher.EQ, HdStatDef.StatLogs.StatType.NUM_PV);
            rt = cli.getStatList(p_aid, statList, matcher);
            if (rt != Errno.OK || CollectionUtil.isEmpty(statList)) {
                Log.logErr(rt, "===getStatList aid=%s; gameId=%s;", rt, p_aid, p_gameId);
            }

            p.setInt(HdGameDef.Info.VIEW, statList.get(0).getInt("count", p_view));
        }
    }


    /**
     * 查询aidset中是指定acctTrade(客户行业)的数据
     * @param aidset
     * @param acctTrade 客户行业
     * @return
     */
    private FaiList<Param> getAcctTradeList(Set<Integer> aidset, int acctTrade){
        SearchArg tSearchArg = new SearchArg();
        tSearchArg.matcher = new ParamMatcher("aid",ParamMatcher.IN,aidset);
        tSearchArg.matcher.and("trade",ParamMatcher.EQ,acctTrade);
        FaiList<Param> acctTradeList = new FaiList<Param>();
        ossAffairCli.getHdTradeRecordListFromDB(new Param(),tSearchArg,acctTradeList);
        return acctTradeList;
    }

    /**
     * 查询条件中如果有行业分类，活动分类，场景分类 判断是否被移除
     * @param item
     * @param hdActivityVo
     * @param modelMap
     * @return
     */
    private boolean isDelete(Param item, HdActivityListVO hdActivityVo, Map<Integer, Param> modelMap){
        int trade = hdActivityVo.getTrade();
        int trade2 = hdActivityVo.getTrade2();
        int category1 = hdActivityVo.getCategory1();
        int category2 = hdActivityVo.getCategory2();

        int gameAid = item.getInt("aid");
        int tempGameId = item.getInt("id");
        Param thisGame = getGameFromDb(gameAid,tempGameId);
        Param settingTemp = Param.parseParam(thisGame.getString(HdGameDef.Info.SETTING),new Param());
        if(settingTemp!=null) {
            int modId = settingTemp.getInt("modId", 0);
            Param thisModel = modelMap.get(modId);
            int thisTrade = thisModel.getInt("trade", 0);
            int thisTrade2 = thisModel.getInt("trade2", 0);
            int thisCategory1 = thisModel.getInt("category1", 0);
            int thisCategory2 = thisModel.getInt("category2", 0);
            Log.logDbg("thisTrade = %s;trade = %s;", thisTrade, trade);
            if (trade > 0 && thisTrade != trade) return true;
            if (trade2 > 0 && thisTrade2 != trade2) return true;
            if (category1 > 0 && thisCategory1 != category1) return true;
            if (category2 > 0 && thisCategory2 != category2) return true;
            return false;
        }
        return true;
    }

    /**
     * 填充resultList中数据
     * @param resultList
     * @param acctMap
     */
    private void fillData(FaiList<Param> resultList, Map<Integer, Param> acctMap){
        for(Param item:resultList){
            Param acctInfo = acctMap.get(item.getInt("aid"));
            boolean isOem = false;
            if(acctInfo != null){
                item.setString("companyName",acctInfo.getString("name",""));
                item.setString("acct",acctInfo.getString(AcctDef.Info.AACCT));
                int atype = acctInfo.getInt( AcctDef.Info.ATYPE );
                isOem = (AcctDef.isOem(atype));
            }

            Param settting = Param.parseParam(item.getString(HdGameDef.Info.SETTING),new Param());
            Param other =  Param.parseParam(settting.getString("other"),new Param());
            item.setString("gameStyleName",other.getString("gameStyleName"));
            // Log.logDbg("unique-test gameStyleName = "+ item);
            item.setString("key1",other.getString("key1"));
            item.setString("key2",other.getString("key2"));
            item.setString("key3",other.getString("key3"));
            item.setString(HdGameDef.Info.STATUS,other.getString(HdGameDef.Info.STATUS));

            long uriTokenTimeStamp = 1499821200L;
            String host =isOem?Web.getOemHdGameDomain():Web.getHdGameDomain();
            int tempStyle = item.getInt(HdGameDef.Info.STYLE,0);
            String httpStr = Web.getDebug()?"http://":"https://";
            int gameAid = item.getInt("aid");
            int tempGameId = item.getInt("id");
            String link = httpStr + host+ "/"+ gameAid+ "/"+ tempGameId+ "/load.html?style=" + tempStyle;
            long createTime = item.getCalendar(HdGameDef.Info.CREATE_TIME).getTimeInMillis();
            if(createTime/1000 > uriTokenTimeStamp){
                String urlToken = fai.hdUtil.HdTool.encryptGameId(gameAid,tempGameId);
                link = httpStr +  host+ "/"+ gameAid+ "/"+ urlToken  + "/load.html?style=" + tempStyle;
            }
            item.setString("link",link+"&test="+HdGameDef.SecretKey.TEST);
        }
    }

    /**
     * 统计resultList中活动的浏览人数，玩家人数，分享人数  结果放在data中
     * @param resultList
     * @param data
     */
    private void countData(FaiList<Param> resultList, Param data){
        int totalPlayer = 0;
        int totalView = 0;
        int totalShare = 0;

        for(Param item:resultList) {
            //统计数据
            totalPlayer += item.getInt(HdGameDef.Info.PLAYER);
            totalView += item.getInt(HdGameDef.Info.VIEW);
            totalShare += item.getInt(HdGameDef.Info.SHARE);
        }
        data.setInt("totalPlayer",totalPlayer);
        data.setInt("totalView",totalView);
        data.setInt("totalShare",totalShare);
    }


    @GetMapping("/getAllDiscoveryGame")
    public JsonResult getAllDiscoveryGame(String typeStr) throws Exception {
        int rt = Errno.ERROR;
        int type = Parser.parseInt(typeStr, HdDiscoveryGameDef.Type.CARD_BAG);
        HdDiscoveryGame hdDiscoveryGame = Core.getCorpKit(1, HdDiscoveryGame.class);
        FaiList<String> aidGameIdList = new FaiList<String>();
        rt = hdDiscoveryGame.getAllDiscoveryGameIdForOss(type, aidGameIdList);
        if (rt != Errno.OK){
            Log.logErr(rt,"getAllForOss err;type=%s;",type);
            return JsonResult.error();
        }
        return JsonResult.success(aidGameIdList);
    }



    /**
     * 关闭游戏(内容违规)/恢复
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/closeGame")
    @ApiOperation(value = "关闭游戏(内容违规)", httpMethod = "POST")
    public JsonResult closeGame( @RequestParam @NotNull Integer aid,  @RequestParam@NotNull Integer gameId, boolean close,
                                 @RequestParam@NotNull Integer closeType, @RequestParam("reason") String _reason) throws Exception {

        Log.logDbg("dkLog:aid:"+aid);
        Log.logDbg("dkLog:gameId:"+gameId);
        Log.logDbg("dkLog:close:"+close);
        Log.logDbg("dkLog:closeType:"+closeType);
        Log.logDbg("dkLog:_reason:"+_reason);
        ParamUpdater updater = new ParamUpdater();
        int flagOne = HdGameDef.Flag.CLOSE;
        String flagTwo = HdGameDef.Info.FLAG;
        if( close ){
            switch(closeType){
                case 2:
                    flagOne = HdGameDef.FlagB.CLOSE_FOR_PUSH;
                    flagTwo =  HdGameDef.Info.FLAGB;
                    break;
            }
            int flagOr = 0;
            flagOr |= flagOne;
            updater.add(flagTwo, ParamUpdater.LOR, flagOr);
        }else{
            switch(closeType){
                case 2:
                    flagOne = HdGameDef.FlagB.CLOSE_FOR_PUSH;
                    flagTwo =  HdGameDef.Info.FLAGB;
                    break;
            }
            int flagAnd = ~0;
            flagAnd &= ~flagOne;
            updater.add(flagTwo, ParamUpdater.LAND, flagAnd);
            int flagOr = 0;
            flagOr |= HdGameDef.FlagB.REPORT_RELEASE;
            updater.add(HdGameDef.Info.FLAGB, ParamUpdater.LOR, flagOr);
        }

        OssHd ossHd = new OssHd();
        int rt = ossHd.setGame(aid,gameId, updater);
        if( rt != Errno.OK ){
            return JsonResult.error("设置失败");
        }
        try {
            Param reason = new Param();
            SearchArg _searchArg = new SearchArg();
            _searchArg.totalSize = new fai.comm.util.Ref<Integer>(0);
            _searchArg.matcher = new ParamMatcher();
            if (aid != 0) {
                _searchArg.matcher.and(HdGameDef.Info.AID, ParamMatcher.EQ, aid);
            }
            if (gameId != 0) {
                _searchArg.matcher.and(HdGameDef.Info.ID, ParamMatcher.EQ, gameId);
            }
            FaiList<Param> gameList = ossHd.getDBGameList(_searchArg);
            boolean open = true;
            if (!close) {
                String reasonStr = "开启互动游戏 aid:" + aid + " gameId:" + gameId;
                if (closeType == 2) {
                    reasonStr += ";恢复(推送域名)";
                }
                if (closeType == 1) {
                    reasonStr += ";恢复(内容违规)";
                }
                reason.setString("reason", reasonStr);
            } else {
                open = false;
                String reasonStr = "";
                if (Str.isEmpty(_reason)) {
                    reasonStr += "关闭互动游戏 aid:" + aid + " gameId:" + gameId;
                } else {
                    reasonStr += _reason + ";关闭互动游戏 aid:" + aid + " gameId:" + gameId;
                }
                if (closeType == 2) {
                    reasonStr += ";关闭(推送域名)";
                }
                if (closeType == 1) {
                    reasonStr += ";关闭(内容违规)";
                }
                reason.setString("reason", reasonStr);
            }
            rt = addCloseLog(aid,"hdGame",reason, new Param(), gameId, open);
            if (rt == Errno.OK) {
                Log.logStd("add close hd add gfw close log is success aid=%s;gameId=%s;", aid, gameId);
            } else {
                Log.logErr("add close hd add gfw close log is fail rt=%s;aid=%s;gameId=%s;", rt, aid, gameId);
            }
        } catch (Exception e) {
            Log.logErr("add close hd add gfw close log is err msg=%s;",e);
        }
        return JsonResult.success("设置成功");
    }

    /**
     * 加入白名单
     * @param aid
     * @param gameId
     * @param whitelist
     * @return
     * @throws Exception
     */
    @PostMapping("/whitelistShare")
    @ApiOperation(value = "白名单", httpMethod = "POST")
    public JsonResult whitelistShare(int aid, int gameId, boolean whitelist) throws Exception {
        Auth.checkFaiscoAuth("authCs|authHdManage", true);

        if(aid == 0){
            return JsonResult.error("aid参数错误");
        }
        if(gameId == 0){
            return JsonResult.error("gameId参数错误");
        }

        ParamUpdater updater = new ParamUpdater();
        if( whitelist ){
            int flagOr = 0;
            flagOr |= HdGameDef.FlagB.WHITELIST_SHARE;
            updater.add(HdGameDef.Info.FLAGB, ParamUpdater.LOR, flagOr);
        }else{
            int flagAnd = ~0;
            flagAnd &= ~HdGameDef.FlagB.WHITELIST_SHARE;
            updater.add(HdGameDef.Info.FLAGB, ParamUpdater.LAND, flagAnd);
        }

        OssHd ossHd = new OssHd();
        int rt = ossHd.setGame(aid,gameId, updater);
        if( rt != Errno.OK ){
            return JsonResult.error("设置失败");
        }
        //关闭日志记录模块
        try {
            String reason = "移出互动游戏白名单";
            boolean open = false;
            if (whitelist) {
                open = true;
                reason = "加入互动游戏白名单";
            }
            //取不到grade
            Param reasonParam = new Param();
            String bss = "hdGame";
            reasonParam.setString("reason",reason + " aid:" + aid + " gameId:" + gameId);
            addCloseLog(aid,bss,reasonParam, new Param(), gameId, open);
        } catch (Exception e) {
            Log.logErr("add close log is err msg=%s;",e);
        }
        return JsonResult.success("设置成功");
    }

    private int addCloseLog(int aid, String bss, Param reason, Param detail, int i, boolean open) {
        int rt = Errno.ERROR;
        //关闭日志记录模块
        try {
            Staff staff = (Staff) Core.getCorpKit(Session.getCid(), Kid.STAFF);
            Param staffInfo = staff.getStaffInfo(Session.getSid());
            String sacct = staffInfo.getString(StaffDef.Info.SACCT);
            String name = staffInfo.getString(StaffDef.Info.NAME);
            //进行操作日志记录
            Param closeLogInfo = new Param();

            closeLogInfo.setInt(GfwDef.CloseLogInfo.AID, aid);
            closeLogInfo.setString(GfwDef.CloseLogInfo.BSS, bss);
            closeLogInfo.setLong(GfwDef.CloseLogInfo.TIME, System.currentTimeMillis()/1000);
            closeLogInfo.setInt(GfwDef.CloseLogInfo.STATUS, GfwDef.CloseLogInfo.Status.PEOPLE);
            closeLogInfo.setString(GfwDef.CloseLogInfo.SACCT, sacct);
            closeLogInfo.setString(GfwDef.CloseLogInfo.NAME, name);
            closeLogInfo.setString(GfwDef.CloseLogInfo.DETAIL, detail.toJson());
            closeLogInfo.setInt(GfwDef.CloseLogInfo.I, i);
            closeLogInfo.setString(GfwDef.CloseLogInfo.REASON, reason.toString());
            closeLogInfo.setInt(GfwDef.CloseLogInfo.OPEN, (open ? 1 : 0));

            SysGfw gfw =(SysGfw)Core.getSysKit(Kid.SYS_GFW);
            rt = gfw.addCloseLogInfo(closeLogInfo);
            if (rt == Errno.OK) {
                Log.logStd("add close log is success");
            } else {
                Log.logErr("add close log is fail rt=%s;",rt);
            }
        } catch (Exception e) {
            Log.logErr("add close log is err msg=%s;",e);
        }
        return rt;
    }

    /**
     * 提示违规
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/setTipIllage")
    @ApiOperation(value = "提示违规", httpMethod = "POST")
    public JsonResult setTipIllage(int aid, int gameId, String  operation) throws Exception {
        if(gameId == 0){
            return JsonResult.error("aid参数错误");
        }
        if(aid == 0){
            return JsonResult.error("aid参数错误");
        }

        //操作提示违规,解除违规
        ParamUpdater updater = new ParamUpdater();
        if(operation != null && operation.indexOf("illegal") > -1){			//提示违规操作
            int flagOr = 0;
            flagOr |= HdGameDef.FlagB.IS_TIP_ILLEGAL | HdGameDef.FlagB.BLACKLIST_SHARE;						//提示违规同时拉黑操作
            //flagOr |= HdGameDef.FlagB.BLACKLIST_SHARE;
            updater.add(HdGameDef.Info.FLAGB, ParamUpdater.LOR, flagOr);
        }else{
            int flagAnd = ~0;
            flagAnd &= ~HdGameDef.FlagB.IS_TIP_ILLEGAL;
            updater.add(HdGameDef.Info.FLAGB, ParamUpdater.LAND, flagAnd);
        }
        OssHd ossHd = new OssHd();
        int rt = ossHd.setGame(aid,gameId, updater);
        if( rt != Errno.OK ){
            return JsonResult.error("设置失败");
        }
        //关闭日志记录模块
        try {
            boolean isillegal = (operation.indexOf("illegal") > -1);
            String reason = "关闭提示违规";
            boolean open = true;
            if (isillegal) {
                open = false;
                reason = "提示违规";
            }
            //取不到grade
            Param reasonParam = new Param();
            String bss = "hdGame";
            reasonParam.setString("reason", reason + " aid:" + aid + " gameId:" + gameId);
            addCloseLog(aid, bss, reasonParam, new Param(), gameId, open);
        } catch (Exception e) {
            Log.logErr("add close log is err msg=%s;", e);
        }
        return JsonResult.success("设置成功");
    }

    /**
     * 禁止展示
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/closeFx")
    @ApiOperation(value = "禁止展示", httpMethod = "POST")
    public JsonResult closeFx(int aid, int gameId, boolean  close) throws Exception {
        Auth.checkFaiscoAuth("authCs|authHdManage", true);
        return JsonResult.success("功能已经废弃");
    }

    /**
     * 移入劵宝
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/addToDiscoveryGameList")
    @ApiOperation(value = "移入劵宝", httpMethod = "POST")
    public JsonResult addToDiscoveryGameList(int aid, int gameId, int  type) throws Exception {
        int rt = Errno.ERROR;
        if (aid < 1 || gameId < 1) {
            Log.logErr(Errno.ARGS_ERROR, "args err;aid=%s;gameId=%s;", aid, gameId);
            return JsonResult.error(Errno.ARGS_ERROR);
        }
        HdGame hdGame = (HdGame) WebOss.getCorpKit(Kid.HD_GAME);
        Param gameInfo = hdGame.getGameInfo(aid, gameId);
        if (Str.isEmpty(gameInfo)) {
            Log.logErr("the game not found;aid=%s;gameId=%s;", aid, gameId);
            return JsonResult.error(Errno.NOT_FOUND);
        }
        int flag = gameInfo.getInt(HdGameDef.Info.FLAG, 0);
        boolean isPublish = Misc.checkBit(flag, HdGameDef.Flag.PUBLISH);
        if (!isPublish) {//未发布的游戏不能添加到券宝
            Log.logErr("the game not publish ;aid=%s;gameId=%s;", aid, gameId);
            return JsonResult.error(Errno.STATUS_ERROR, "活动尚未发布！");
        }
        Param setting = Param.parseParam(gameInfo.getString(HdGameDef.Info.SETTING, ""), new Param());
        long publishTime = setting.getCalendar(HdGameDef.Setting.PUBLISH_TIME, Calendar.getInstance()).getTimeInMillis();
        HdDiscoveryGame hdDiscoveryGame = Core.getCorpKit(1, HdDiscoveryGame.class);
        Ref<Param> infoRef = new Ref<Param>();
        rt = hdDiscoveryGame.getInfo(aid, gameId, type, infoRef);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "hdDiscoveryGame getInfo err;type=%s;aid=%s;gameId=%s;", type, aid, gameId);
            return JsonResult.error(rt);
        }
        //如果之前没有添加过到券宝，就直接添加
        if (Str.isEmpty(infoRef.value)) {
            rt = hdDiscoveryGame.add(aid, gameId, publishTime, type);
            if (rt != Errno.OK) {
                Log.logErr(rt, "hdDiscoveryGame add err;type=%s;aid=%s;gameId=%s;", type, aid, gameId);
                return JsonResult.error(rt);
            }
        } else {//添加过到券宝就修改flag
            int flagFromDiscoveryGame = infoRef.value.getInt(HdDiscoveryGameDef.Info.FLAG, 0);
            rt = setDiscoveryGameDeleteFlag(aid, gameId, type, hdDiscoveryGame, flagFromDiscoveryGame, false);
            if (rt != Errno.OK) {
                Log.logErr(rt, "setDiscoveryGameDeleteFlag err;aid=%s;gameId=%s;type=%s;flagFromDiscoveryGame=%s;isDelete", aid, gameId, type, flagFromDiscoveryGame, false);
                return JsonResult.error(rt);
            }
        }
        return JsonResult.success("操作成功");
    }


    /**
     * 移出劵宝
     */
    @PostMapping("/delFromDiscoveryGameList")
    @ApiOperation(value = "移出劵宝", httpMethod = "POST")
    public JsonResult delFromDiscoveryGameList(int aid, int gameId, int  type) throws Exception {
        int rt = Errno.ERROR;
        if (aid < 1 || gameId < 1) {
            Log.logErr(Errno.ARGS_ERROR, "args err;aid=%s;gameId=%s;", aid, gameId);
            return JsonResult.error(Errno.ARGS_ERROR);
        }
        HdGame hdGame = (HdGame) WebOss.getCorpKit(Kid.HD_GAME);
        Param gameInfo = hdGame.getGameInfo(aid, gameId);
        if (Str.isEmpty(gameInfo)) {
            Log.logErr("the game not found;aid=%s;gameId=%s;", aid, gameId);
            return JsonResult.error(Errno.NOT_FOUND);
        }
        HdDiscoveryGame hdDiscoveryGame = Core.getCorpKit(1, HdDiscoveryGame.class);
        Ref<Param> infoRef = new Ref<Param>();
        rt = hdDiscoveryGame.getInfo(aid, gameId, type, infoRef);
        if (rt != Errno.OK) {
            Log.logErr("the discovery game not found;aid=%s;gameId=%s;type=%s;", aid, gameId, type);
            return JsonResult.error(Errno.NOT_FOUND);
        }
        int flag = infoRef.value.getInt(HdDiscoveryGameDef.Info.FLAG, 0);
        rt = setDiscoveryGameDeleteFlag(aid, gameId, type, hdDiscoveryGame, flag, true);
        if (rt != Errno.OK) {
            Log.logErr(rt, "setDiscoveryGameDeleteFlag err;aid=%s;gameId=%s;type=%s;flag=%s;isDelete", aid, gameId, type, flag, true);
            return JsonResult.error(Errno.NOT_FOUND);
        }

        if(type==HdDiscoveryGameDef.Type.CARD_BAG_SELF){
            //设置status
            int status = HdDiscoveryGameDef.Status.PASS_END;
            Param upInfo = new Param();
            upInfo.setInt(HdDiscoveryGameDef.Info.STATUS, status);
            ParamUpdater updater = new ParamUpdater(upInfo);
            rt = hdDiscoveryGame.update(aid, gameId, type, updater);
            if (rt != Errno.OK) {
                Log.logErr(rt, "hdDiscoveryGame update err;type=%s;toStatus=%s;aid=%s;gameId=%s;", type, status, aid, gameId);
                return JsonResult.error(Errno.NOT_FOUND);
            }
        }
        return JsonResult.success("操作成功");

    }

    private int setDiscoveryGameDeleteFlag(int aid, int gameId, int type, HdDiscoveryGame hdDiscoveryGame, int flag, boolean isDelete) throws Exception {
        int rt = Errno.ERROR;
        flag = Misc.setFlag(flag, HdDiscoveryGameDef.Flag.DELETE, isDelete);
        Log.logDbg("setDiscoveryGameDeleteFlag flag = %s;", flag);
        Param upInfo = new Param();
        upInfo.setInt(HdDiscoveryGameDef.Info.FLAG, flag);
        if (isDelete){
            upInfo.setInt(HdDiscoveryGameDef.Info.STATUS, HdDiscoveryGameDef.Status.UNTREATED);
        }
        ParamUpdater updater = new ParamUpdater(upInfo);
        rt = hdDiscoveryGame.update(aid, gameId, type, updater);
        if (rt != Errno.OK) {
            Log.logErr(rt, "hdDiscoveryGame update err;type=%s;aid=%s;gameId=%s;", type, aid, gameId);
            return rt;
        }
        return rt;
    }

    /**
     * 举报内容
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/getGameInformCount")
    @ApiOperation(value = "举报内容", httpMethod = "POST")
    public JsonResult getGameInformCount(@NotNull int aid, @NotNull int gameId) throws Exception {

        HdOss ho = (HdOss)Core.getCorpKit(1, Kid.HD_OSS);

        FaiList<Param> list = new FaiList<Param>();
        Param result = new Param();
        int rt = ho.getGameInformCount(aid, gameId, list);
        if(rt != Errno.OK){
            return JsonResult.error(rt, "系统错误，请稍后再试");
        }

        result.setInt("rt", rt);
        result.setList("list", list);
        return JsonResult.success(result);
    }

    /**
     * 举报内容
     * @param aid
     * @param gameId
     * @return
     * @throws Exception
     */
    @PostMapping("/getGameInformList")
    @ApiOperation(value = "举报内容", httpMethod = "POST")
    public JsonResult getGameInformList(@NotNull int aid, @NotNull int gameId, @NotNull int  page, @NotNull int limit) throws Exception {
        HdOss ho = (HdOss)Core.getCorpKit(1, Kid.HD_OSS);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdGameInformDef.Info.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(HdGameInformDef.Info.GAME_ID, ParamMatcher.EQ, gameId);
        searchArg.start = (page - 1) * limit;
        searchArg.limit = limit;
        searchArg.totalSize = new Ref<Integer>();

        FaiList<Param> list = new FaiList<Param>();
        Param result = new Param();
        int rt = ho.getGameInformList(searchArg, list);
        if(rt != Errno.OK){
            result.setInt("rt", rt);
            result.setString("msg", "系统错误，请稍后再试");
            return JsonResult.error(rt, "系统错误，请稍后再试");
        }

        result.setInt("rt", rt);
        result.setList("list", list);
        result.setInt("count", searchArg.totalSize.value);
        return JsonResult.success(result);
    }

    /**
     * 获取游戏类型
     */
    @GetMapping("/getAllGameTypeList")
    @ApiOperation(value = "获取游戏类型", httpMethod = "GET")
    public JsonResult getAllGameTypeList() throws Exception {
        HdOss hdoss = (HdOss)Core.getCorpKit(1,Kid.HD_OSS);//获取模板的新接口，旧接口SysFaiPortal的方法已废弃
        SearchArg modSearchArg = new SearchArg();
        ParamMatcher modMatcher = new ParamMatcher();
        modMatcher.and(HdModelDef.Info.PROPERTY,ParamMatcher.EQ,HdModelDef.Property.ISPUB);
        modSearchArg.matcher = modMatcher;
        FaiList<Param> modGameList =  hdoss.getHdModelList(modSearchArg);
        FaiList<Param> resultList = new FaiList<>();
        for (Param param : modGameList) {
            resultList.add(new Param().
                    setInt("id",param.getInt("id")).
                    setString("name", param.getString("name")).
                    setInt("rstyle", param.getInt("rstyle"))
            );
        }
        return JsonResult.success(resultList);
    }

    /**
     * 获取游戏原型
     */
    @GetMapping("/getAllGameStyleList")
    @ApiOperation(value = "获取游戏原型", httpMethod = "GET")
    public JsonResult getAllGameStyleList() throws Exception {
        FaiList<HdStyleVo> styleList = new FaiList<>();

        for (Field field : HdGameDef.Style.class.getFields()) {
            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
                    field.getType() == int.class && field.getName().equals(field.getName().toUpperCase())) {
                try {
                    int value = field.getInt(null);
                    String styleName = HdGameDef.Style.getNameByStyle(value);

                    HdStyleVo styleInfo = new HdStyleVo(value, styleName);
                    styleList.add(styleInfo);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
        }
        return JsonResult.success(styleList);
    }

    /**
     * 销售成交客户
     */
    @GetMapping("/getSale")
    @ApiOperation(value = "销售成交客户", httpMethod = "GET")
    public JsonResult getSale() throws Exception {
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd)Core.getSysKit(Kid.SYS_PRESALE_HD);				// presaleHd 的接口
        SearchArg	saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher =new ParamMatcher(HdSaleDef.Info.LIMIT_COUNT,ParamMatcher.GE,0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(),saleSearchArg,infoList);
        if(rt != Errno.OK){
            Log.logErr("get saleList err rt= %s",rt);
        }
        FaiList<Param> resultList = new FaiList<Param>();
        for (Param param : infoList) {
            resultList.add(new Param()
                    .setString("name", param.getString(HdSaleDef.Info.NICK_NAME))
                    .setInt("id", param.getInt(HdSaleDef.Info.SID)));
        }
        return JsonResult.success(resultList);
    }

    /**
     * 节日
     */
    @GetMapping("/getFestival")
    @ApiOperation(value = "节日", httpMethod = "GET")
    public JsonResult getFestival() throws Exception {
        FaiList<Param> newGroup = HdTradeDef.getNewGroup(HdTradeDef.Id.FESTIVAL_FOR_TWO, false);
        FaiList<Param> resultList = new FaiList<>();
        for (Param param : newGroup) {
            resultList.add(new Param().setInt("id", param.getInt("id")).setString("name", param.getString("name")));
        }
        return JsonResult.success(resultList);
    }

    /**
     * 类型
     */
    @GetMapping("/getType")
    @ApiOperation(value = "节日", httpMethod = "GET")
    public JsonResult getType() throws Exception {
        FaiList<Param> newGroup = HdTradeDef.getNewGroup(HdTradeDef.Id.TYPE_FOR_TWO,true);
        FaiList<Param> resultList = new FaiList<>();
        for (Param param : newGroup) {
            resultList.add(new Param().setInt("id", param.getInt("id")).setString("name", param.getString("name")));
        }
        return JsonResult.success(resultList);
    }


    /**
     * 游戏营销
     */
    @GetMapping("/getGameMarket")
    @ApiOperation(value = "游戏营销", httpMethod = "GET")
    public JsonResult getGameMarket() throws Exception {
        FaiList<Param> newGroup = HdTradeDef.getNewGroup(HdTradeDef.Id.PLAY_FOR_TWO,true);
        FaiList<Param> resultList = new FaiList<>();
        for (Param param : newGroup) {
            resultList.add(new Param().setInt("id", param.getInt("id")).setString("name", param.getString("name")));
        }
        return JsonResult.success(resultList);
    }


    /**
     * 客户行业
     */
    @GetMapping("/getAcctTrade")
    @ApiOperation(value = "行业", httpMethod = "GET")
    public JsonResult getAcctTrade() throws Exception {
        FaiList<Param> tradeList = HdModelTradeDef.getTradeList();
        FaiList<Param> resultList = new FaiList<>();
        for (Param param : tradeList) {
            resultList.add(new Param().
                    setInt("id", param.getInt("id")).
                    setString("name", param.getString("name")).
                    setList("tradeList2", param.getList("trade2List")));
        }
        return JsonResult.success(resultList);
    }

    /**
     * 客户行业
     */
    @GetMapping("/testTrade")
    @ApiOperation(value = "行业", httpMethod = "GET")
    public JsonResult testTrade(int aid) throws Exception {

        return JsonResult.success(HdTradeUtil.getTradeByAid(aid));
    }

}
