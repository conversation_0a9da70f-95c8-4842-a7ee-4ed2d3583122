<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.webhdportal.app.*"%>
<%@ page import="fai.webhdportal.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="java.util.regex.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%
	Boolean fromHdOss = "true".equals(request.getParameter("fromHdOss")); //是否来自hdoss
	Boolean fromOem = "true".equals(request.getParameter("fromOem")); //是否来自代理商
	Boolean isDev = Web.isDev();
	String account = isDev ? "9897375" : "********";
	String OemAccount = isDev ? "9908663" : "********";
	if (!Auth.checkFaiscoAuth("authCloneHd|authCloneAll", false)) {
		out.println("没有权限");
		return;
	}
%>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title></title>
<%@ include file="/comm/link.jsp.inc"%>
<%@ include file="/comm/script.jsp.inc"%>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<style>
	body{
		padding: 0;
		margin: 0;
	}
	
	input{
		width : 350px;
	}
	.previewBox {
		padding: 20px;
		text-align: center;
	}
	.previewBox .content {
		display: inline-block;
	}
	.previewBox .linkPicBox {
		width: 200px;
	}
	.previewBox .linkPicBox .linkPic img {
		width: 200px;
		height: 200px;
	}
	.previewBox .linkPicBox .downCode {
		width: 100%;
		color: #4381FD;
		/*color: #74b7e6;*/
		margin-left: 28px;
		cursor: pointer;
		text-decoration: underline;
	}
	.previewBox .linkPicBox .showLink {
		text-align: center;
	}
	.previewBox .linkPicBox .showLink .linkSite {
    	height: 24px;
    	word-break: break-all;
		overflow: hidden;
	}
	.previewBox .linkPicBox .showLink .el-button {
		width: 100%;
		margin-top: 10px;
	}
	.previewBox .linkPicBox .showLink .el-button span {
		width: 100%;
	}
	input[type=radio]{
		width: 16px;
	}
</style>
</head>
<body>
	
	<div id="container">
		<h2>方式一： 活动链接复制</h1>
		<form action="#" method="post" id="searchInfo">
			<p>
			原活动链接：
			<input type="text" name="sourceUrl" id="sourceUrl" placeholder="">
			<!-- &nbsp;&nbsp;<a target="_blank" href="https://adm.webportal.top/sys/view.jsp?id=977">如何获取某个游戏的ID?</a> -->
			</p>
			<p>目标企业账号aid：<input type="text" name="targetAid" id="targetAid" placeholder="" @input="targetInput()">
				<% if(!fromOem){ %><input type="radio" name="aidtype" value=<%=account%> id="fktype" @change="changeType($event)" /><label for="fktype">直销案例账号aid</label><%}%>
				<input type="radio" name="aidtype" value="<%=OemAccount%> " id="oemtype" @change="changeType($event)" /><label for="oemtype">分销案例账号aid</label>
			</p>
		</form>	
		<h4 style="color:red;">注：直销复制至分销时需注意检查活动内是否有“凡科”字样</h4>
		<button @click="sumbitToSearch()">复制</button>

		<h2 style="margin-top: 50px;">方式二： 游戏ID复制</h1>
		<form action="#" method="post" id="searchInfo">
			<p>
			原始游戏ID：
			<input type="text" name="sourceID" id="sourceID" placeholder="">
			&nbsp;&nbsp;<a target="_blank" href="https://adm.webportal.top/sys/view.jsp?id=977">如何获取某个游戏的ID?</a>
			</p>
			<p>目标企业账号aid：<input type="text" name="targetAid1" id="targetAid1" placeholder="" @input="targetInput(true)">
				<% if(!fromOem){ %><input type="radio" name="aidtype" value=<%=account%>  id="fktype1" @change="changeType($event,true)" /><label for="fktype1">直销案例账号aid</label><%}%>
				<input type="radio" name="aidtype" value=<%=OemAccount%> id="oemtype1" @change="changeType($event,true)" /><label for="oemtype1">分销案例账号aid</label>
			</p>
		</form>
		<h4 style="color:red;">注：直销复制至分销时需注意检查活动内是否有“凡科”字样</h4>
		<button @click="sumbitToSearch(true)">复制</button>

		<el-dialog
			title="复制后"
			:visible.sync="showPoup"
			width="30%">
			<div class="previewBox">
				<div class="item">
					<div class="content linkPicBox">
						<div class="linkPic">
							<img class="qrcodeImage" :src="gameCode">
							<div class="wxScan">
								<span class="scan">微信扫一扫</span>
								<span class="downCode" @click="downCode">下载二维码</span>
								<span class="showLink">
									<div id="code" class="linkSite">{{gameLink}}</div>
									<el-button
										ref="copy"
										class="copy-btn pointer"
										data-clipboard-action="copy"
										data-clipboard-target="#code"
										@click="copyCode"
									>
										复制链接
									</el-button>
								</span>
							</div>
							<div>案例活动复制成功，可扫码体验</div>
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
		
	</div>
</div>
</body>

<script type="text/javascript" src="<%= HdOssResDef.getResPath("js_clipboard") %>"></script>
<script type="text/javascript">
	var container = new Vue({
		el: "#container",
		data: {
			showPoup: false,
			gameCode: '',
			gameLink: '',
			copyBtn: null
		},
		updated: function() {
			var _this = this;
			this.$nextTick(function() {
				_this.copyBtn = new ClipboardJS(_this.$refs.copy.$el);
			});
		},
		methods: {
			sumbitToSearch: function(isID){
				var targetAid = !isID ? parseInt(document.getElementById("targetAid").value) : parseInt(document.getElementById("targetAid1").value),
					_this = this;
				if(_this.isTargetAid(targetAid)) {
					this.$confirm('活动将复制到特定的案例aid：' + targetAid + '，复制后部分设置将有所改动，且活动将会设置为发布状态，以作为案例展示').then(function(type) {
						type == 'confirm' && _this.gotoCopy(targetAid,isID);
					});
					return;
				}
				_this.gotoCopy(targetAid,isID);
			},
			gotoCopy: function(targetAid,isID){
				if(!isID){
					this.getGameId(targetAid);

				}else{
					var sourceID = document.getElementById("sourceID").value;
					this.copyActive(sourceID, targetAid);
				}
			},
			getGameId: function(targetAid){
				var sourceUrl = document.getElementById("sourceUrl").value,
					_this = this;
				Vue.http.post("/ajax/hdProduct_h.jsp?cmd=getGameIdByUrl&url="+sourceUrl, {emulateJSON: true}).then(response => {
					let data = response.data;
					if(data.success) {
						var sourceID = data.data.aid + "HD" + data.data.gameId;
						_this.copyActive(sourceID, targetAid);
					} else{
						alert(data.msg);
					}
				}, response => {

				});
			},
			copyActive: function(sourceID, targetAid) {
				var _this = this;
				Vue.http.post("/hdProduct/hdGameCopy_h.jsp?cmd=copyActive&sourceID="+sourceID+"&targetAid="+targetAid + "&fromHdOss=<%=fromHdOss%>", {emulateJSON: true}).then(response => {
					let data = response.data;
					if(data.success && _this.isTargetAid(targetAid)) {
						_this.showPoup = true;
						_this.gameCode = data.gameCode;
						_this.gameLink = data.gameLink;
					}
					alert(data.msg);
				}, response => {
					
				}); 
			},
			isTargetAid: function(targetAid) {
				return [24265546, ********, 9858037, 9851878, 9897375, 9908663].indexOf(targetAid) > -1;
			},
			downCode: function() {
				window.location.href = '//i.hd.fff.com/qrCode.jsp?type=1&cmd=qrurl&siteUrl=' + encodeURI(this.gameLink + '&fromQrcode=true&editQrcode=true&_source=1');
			},
			copyCode: function() {
				var clipboard = this.copyBtn,
					_this = this;
				clipboard.on('success', function() {
					_this.$message({
						message: '复制成功！',
						type: 'success'
					});
				});
				clipboard.on('error', function() {
					_this.$message({
						message: '复制失败，请手动选择复制！',
						type: 'warning'
					});
				});
			},
			targetInput: function(isID){
				if(!isID){
					document.getElementById("oemtype").checked = false;
					<% if(!fromOem){ %> document.getElementById("fktype").checked = false; <% } %>
				}else{
					document.getElementById("oemtype1").checked = false;
					<% if(!fromOem){ %> document.getElementById("fktype1").checked = false; <% } %>
				}
			},
			changeType: function(event,isID){
				if(!isID){
					document.getElementById("targetAid").value = event.target.value;
				}else{
					document.getElementById("targetAid1").value = event.target.value;
				}
			},
		}
	});

</script>

</html>

