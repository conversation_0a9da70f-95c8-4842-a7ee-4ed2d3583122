package fai.webhdoss.service;

import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scProto.ScProtoListVO;
import fai.webhdoss.model.vo.scProto.ScProtoVO;

public interface ScProtoService {
    JsonResult syncProto(int type, String apiKey, int id) throws Exception;

    JsonResult setScProtoInfo(ScProtoVO vo);

    JsonResult getScProtoList(ScProtoListVO vo);

    JsonResult setScProtoStatus(int id, int status);

    JsonResult getScProtoNameList(int type, String keyword, int pageNo, int pageLimit);

    JsonResult getConfInfo();
}
