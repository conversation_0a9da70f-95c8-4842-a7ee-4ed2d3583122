package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description 原型中script-comm字段VO
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/17 16:52
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoScriptCommVO {
    // 标题
    private String label;
    // 描述：只有图文才有
    private String desc;
    // 关联资源集合
    private List<Integer> resList;
}