/**
 * author: wuyanting
 * 互动案例库 20200930
 */
var formData = {
	page: 1,
	limit: 10,
	trade: '',
	scene: '',
	lable: '',
	activeFunc: '',
	keyWork: '',
	// startTime: new Date().getTime() - 30 * 24 * 60 * 60 * 1000,
	startTime: 1601395200000,
	endTime: new Date().getTime(),
	isCollect: false, // 是否是收藏页面
	isPreViewCase: false // 是否是预览窗案例页面
};


/**
 * author: pai
 * 互动案例筛选库 20210419
 */
var caseFilterFormData = {
	page: 1,      // 第几页
	limit: 10,        // 每一页多少条数据
	oAid: "",              // 原账号Aid
	oGameId: "",       // 原活动账号id
	gameId: "",       // 案例活动账号id
	createPerson: "",       // 操作人
	createTimeStart: "",// 复制开始时间
	createTimeEnd: "" // 复制结束时间
}

new Vue({
	el: '#app',
	data: {
		tabs: [
			{
				dataName: 'caseData',
				name: '案例库',
			},
			{
				dataName: 'collectCaseData',
				name: '案例收藏夹',
			}
		],
		authMktDepartment: authMktDepartment,
		authSalesCenterAdm: authSalesCenterAdm,
		curkey: "caseData"
	},
	computed: {
		formData: function () {
			var formDataString = JSON.stringify(formData);
			var caseFilterFormDataString = JSON.stringify(caseFilterFormData);
			var data = {
				caseData: JSON.parse(formDataString),
				collectCaseData: JSON.parse(formDataString),
				recordData: JSON.parse(formDataString),
				previewCaseData: JSON.parse(formDataString),
				caseFilterData: JSON.parse(caseFilterFormDataString)
			};
			data.collectCaseData.isCollect = true;
			data.previewCaseData.isPreViewCase = true;
			return data;
		}
	},
	components: {
		'swiper-slide-form': components.swiperSlideForm
	},
	created: function () {
		this.authMktDepartment && this.authSalesCenterAdm && this.tabs.push({
			dataName: 'recordData',
			name: '导入记录'
		});
		this.tabs.push({
			dataName: 'previewCaseData',
			name: '预览窗案例',
		});
		this.getAuthority(); 
	},
	methods: {
		changeActiveKey: function (key) {
			if (key !== this.curkey) {
				this.curkey = key;
			}
		},
		showError: function (msg) {
			this.$message.error(msg || '系统繁忙，请稍候再试');
		},
		showSucces: function (msg) {
			this.$message.success(msg);
		},
		getAuthority: function () {
			// 获取当前用户是否有 案例筛选库 权限
			var _this = this;
			$.ajax({
				type: 'get',
				url: '/api/caseFilter/getAuthority',
				dataType: 'json',
				error: function (res) {
					_this.showError(res.msg);
				},
				success: function (res) {
					if (res.success && res.data) {
						_this.tabs.push({
							dataName: 'caseFilterData',
							name: '案例筛选库',
						});
					}
				}
			});
		}
	},
});