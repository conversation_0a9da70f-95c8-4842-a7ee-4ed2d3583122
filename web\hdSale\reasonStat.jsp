<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>未购买原因统计表</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>

	<body>
	<div id="hdsale-reasonStat" v-cloak>

		<!--查询条件 start-->
		<div class="fai-reasonStat-search">
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
			    <el-form-item label="注册时间">
					<el-date-picker class="fai-date" v-model="form.regDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.regDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
				<el-form-item label="销售">
					<el-select v-model="form.staff_sacct" filterable>
						<el-option label="所有人" value="all"></el-option>
						<el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sacct" :key="preSale.sacct"></el-option>
					</el-select>
				</el-form-item>
				
				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<div class="fai-reasonStat-mark-list">
			<b style="">对进行标记的客户统计情况：</b>
			<el-button icon="el-icon-download" type="primary" size="mini" @click="exportExcel">导出</el-button>
			<br/>
			<el-table :data="markTableData" row-key="rowkey" stripe border show-summary>
				<el-table-column label="客户数量" prop="salesNameStr"></el-table-column>
				<el-table-column label="比例(%)" width="80px"></el-table-column>
				<el-table-column label="免费版" width="80px"></el-table-column>
				<el-table-column label="白银版" width="80px"></el-table-column>
				<el-table-column label="铂金版" width="80px"></el-table-column>
			</el-table>
		</div>
		<div class="fai-reasonStat-nobuy-list">
			<b style="">对填写未购买原因的客户统计情况：</b>
			<el-button icon="el-icon-download" type="primary" size="mini" @click="exportExcel">导出</el-button>
			<br/>
			<el-table :data="nobuyTableData" row-key="rowkey" stripe border show-summary>
				<el-table-column label="客户数量" prop="salesNameStr"></el-table-column>
				<el-table-column label="比例(%)" width="80px"></el-table-column>
				<el-table-column label="免费版" width="80px"></el-table-column>
				<el-table-column label="白银版" width="80px"></el-table-column>
				<el-table-column label="铂金版" width="80px"></el-table-column>
			</el-table>
		</div>

	</div>
	</body>

	<script type="text/javascript">

		var reasonStatVM = new Vue({
			el: '#hdsale-reasonStat',
			data: {
				form: {
					regDateBeg: Fai.tool.dateFormatter(new Date()),
					regDateEnd: Fai.tool.dateFormatter(new Date()),
					staff_sacct: "all",
				},
				markTableData: [],
				nobuyTableData: [],
				preSaleList: [],
				editable: false,
				clearable: false,
			},
			created:function(){
				Fai.http.post("hdSale_h.jsp?cmd=getPreSale", "", false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				onSubmit(){
					alert(1);
				},
				exportExcel(){

				}
				
		    }
		});
        
		// 获取数据
		function getDataList(){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getSetConfList", "", false).then(result => {
				if(result.success){
					faiConfDataList.tableData[0].salesNameStr = result.salesNameStr;
					faiConfDataList.sidList = result.sidList;
					faiConfDataList.salesList = result.salesList;
					faiPersonalDataList.tableData = result.personalList;
				}
			});
		}

		//getDataList();//进入页面加载一次数据！


	</script>

</html>



