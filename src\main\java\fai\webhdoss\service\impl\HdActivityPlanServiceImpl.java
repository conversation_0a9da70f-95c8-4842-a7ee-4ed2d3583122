package fai.webhdoss.service.impl;

import fai.cli.HdOssAffairCli;
import fai.comm.util.*;
import fai.hdUtil.exception.HdAssert;
import fai.webhdoss.service.HdActivityPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HdActivityPlanServiceImpl implements HdActivityPlanService {

    @Autowired
    private HdOssAffairCli hdOssAffairCli;

    /**
     * 创建方案
     *
     * @param activityPlan
     * @param idRef
     * @return
     * <AUTHOR>
     */
    @Override
    public Integer addActivityPlan(String activityPlan) throws Exception {
        Ref<Integer> idRef = new Ref<>();
        int rt = hdOssAffairCli.addHdActivityPlan(activityPlan, idRef);
        HdAssert.judgeNotLog(rt, "addHdActivityPlan err");
        HdAssert.judgeNotLog(HdAssert.isEmpty(idRef), "addHdActivityPlan err");
        return idRef.value;

    }

    /**
     * 获取方案
     *
     * @param id
     * @param info
     * @return
     * <AUTHOR>
     */
    @Override
    public Param getHdActivityPlan(int id) throws Exception {
        Param info = new Param();
        int rt = hdOssAffairCli.getHdActivityPlan(id, info);
        HdAssert.judgeNotLog(rt, "getHdActivityPlan err id = " + id);
        return HdAssert.isEmpty(info) ? new Param() : info;
    }

    /**
     * 获取方案列表
     *
     * @param searchArg
     * @param list
     * @return
     * <AUTHOR>
     */
    @Override
    public FaiList<Param> getHdActivityPlanList(SearchArg searchArg) throws Exception {
        FaiList<Param> list = new FaiList<>();
        int rt = hdOssAffairCli.getHdActivityPlanList(searchArg, list);
        HdAssert.judgeNotLog(rt, "getHdActivityPlanList err ");
        if (HdAssert.isEmpty(list)) {
            return new FaiList<>();
        }
        return HdAssert.isEmpty(list) ? new FaiList<>() : list;
    }

    /**
     * 删除方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @Override
    public void delHdActivityPlan(int planId) throws Exception {
        int rt = hdOssAffairCli.delHdActivityPlan(planId);
        HdAssert.judgeNotLog(rt, "delHdActivityPlan err ");
    }

    /**
     * 更新方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @Override
    public void updateHdActivityPlan(String hdActivityPlan) throws Exception {
        int rt = hdOssAffairCli.setHdActivityPlan(hdActivityPlan);
        HdAssert.judgeNotLog(rt, "setHdActivityPlan err ");
    }

    /**
     * 获取某个行业下或者所有的活动方案数量
     *
     * @param tradeId 行业id 负数时获取所有
     * @return
     * <AUTHOR>
     */
    @Override
    public int getActivityPlanCount(int tradeId, int relationFlyerState) throws Exception {
        Ref<Integer> ref = new Ref<>();
        int rt = hdOssAffairCli.getActivityPlanCount(tradeId, relationFlyerState, false, ref);
        HdAssert.judgeNotLog(rt, "getActivityPlanCount err");
        HdAssert.judgeNotLog(HdAssert.isEmpty(ref), "getActivityPlanCount err");

        return ref.value;
    }
}
