package fai.webhdoss.service.impl;

import fai.MgFilesInfSvr.interfaces.cli.MgFilesInfCli;
import fai.MgFilesInfSvr.interfaces.entity.FilesGroupEntity;
import fai.MgFilesInfSvr.interfaces.utils.MgFilesArg;
import fai.app.ScResDef;
import fai.cli.ScResCli;
import fai.comm.jnetkit.server.fai.RemoteStandResult;
import fai.comm.middleground.FaiValObj;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.hdUtil.collector.FaiListCollector;
import fai.hdUtil.exception.HdAssert;
import fai.web.Core;
import fai.web.Web;
import fai.webhdoss.model.vo.scRes.ScCategoryVO;
import fai.webhdoss.service.ScCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 25-05-28 17:54
 */
@Service
public class ScCategoryServiceImpl implements ScCategoryService {

    @Autowired
    private ScResCli scResCli;

    private List<Param> getMgFilesGroupList(int aid, SearchArg searchArg) {
        final int tid = FaiValObj.TermId.SC;
        MgFilesInfCli mgCli = new MgFilesInfCli(Core.getFlow());
        mgCli.init();
        MgFilesArg mgFilesArg = new MgFilesArg.Builder(aid, tid, 0, 0, 0)
                .setSearchArg(searchArg)
                .build();
        FaiList<Param> groupList = new FaiList<>();
        int rt = mgCli.getFilesGroupList(mgFilesArg, groupList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "get groupList error;aid=" + aid);
            return new FaiList<>();
        }
        return groupList.stream().map(this::convertFolder).collect(Collectors.toList());
    }
    private Param convertFolder(Param groupInfo) {
        Param folder = new Param();
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.AID);
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.PARENT_ID);
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.RL_GROUP_ID);
        folder.setString("name", groupInfo.getString(FilesGroupEntity.GroupInfo.GROUP_NAME, ""));
        folder.setInt("id", groupInfo.getInt(FilesGroupEntity.GroupInfo.RL_GROUP_ID, 0));
        folder.setCalendar("createTime", groupInfo.getCalendar(FilesGroupEntity.GroupInfo.CREATE_TIME));
        folder.setCalendar("updateTime", groupInfo.getCalendar(FilesGroupEntity.GroupInfo.UPDATE_TIME));
        return folder;
    }

    @Override
    public JsonResult getList(ScCategoryVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        HdAssert.Biz.judgeNotLog(vo.getType() <= 0, Errno.ARGS_ERROR, "类型不能为空");

        int flag = vo.getType() == ScResDef.Type.OPT_BGM ? ScResDef.RlFlag.BGM : ScResDef.RlFlag.VOICE;

        SearchArg searchArg = new SearchArg();
        searchArg.start = vo.getOffset();
        searchArg.limit = vo.getPageLimit();
        searchArg.totalSize = new Ref<>(0);

        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.PARENT_ID, ParamMatcher.NE, 0);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.RL_FLAG, ParamMatcher.LAND, flag, flag);
        if (!Str.isEmpty(vo.getName())) {
            searchArg.matcher.and(FilesGroupEntity.GroupInfo.GROUP_NAME, ParamMatcher.LK, vo.getName());
        }

        List<Param> list = getMgFilesGroupList(aid, searchArg);
        if (list == null) list = new FaiList<>();
        int totalSize = searchArg.totalSize.value;
        JsonResult result = JsonResult.success(list);
        result.setTotalSize(totalSize);
        return result;
    }

    @Override
    public JsonResult add(ScCategoryVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");

        int type = vo.getType();
        HdAssert.Biz.judgeNotLog(type <= 0, Errno.ARGS_ERROR, "类型不能为空");

        checkArg(vo, "add"); // 参数检查

        HdAssert.Biz.judgeNotLog(Str.isEmpty(vo.getName()), Errno.ARGS_ERROR, "名称不能为空");
        HdAssert.Biz.judgeNotLog(vo.getParentId() <= 0, Errno.ARGS_ERROR, "文件夹不能为空");

        int flag = vo.getType() == ScResDef.Type.OPT_BGM ? ScResDef.RlFlag.BGM : ScResDef.RlFlag.VOICE;
        final int tid = FaiValObj.TermId.SC;
        Param groupInfo = new Param();
        groupInfo.setString(FilesGroupEntity.GroupInfo.GROUP_NAME, vo.getName());
        groupInfo.setInt(FilesGroupEntity.GroupInfo.PARENT_ID, vo.getParentId());
        groupInfo.setInt(FilesGroupEntity.GroupInfo.AID, aid);
        groupInfo.setInt(FilesGroupEntity.GroupInfo.SOURCE_TID, tid);
        groupInfo.setInt(FilesGroupEntity.GroupInfo.RL_FLAG, flag);
        Ref<Integer> rlGroupIdRef = new Ref<>();

        MgFilesInfCli mgCli = new MgFilesInfCli(176866111);
        mgCli.init();
        MgFilesArg mgFilesArg = new MgFilesArg.Builder(aid, tid, 0, 0, 0)
                .setAddInfo(groupInfo)
                .build();
        int rt = mgCli.addFilesGroup(mgFilesArg, rlGroupIdRef);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "add group error;aid=" + aid);
            return JsonResult.error("添加失败");
        }

        groupInfo.setInt("id", rlGroupIdRef.value);
        JsonResult result = JsonResult.success("添加成功");
        result.setData(groupInfo);
        return result;
    }

    @Override
    public JsonResult set(ScCategoryVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");

        checkArg(vo, "set"); // 参数检查

        String name = vo.getName();
        HdAssert.Biz.judgeNotLog(Str.isEmpty(name), Errno.ARGS_ERROR, "名称不能为空");
        HdAssert.Biz.judgeNotLog(vo.getId() <= 0, Errno.ARGS_ERROR, "id不能为空");

        int flag = vo.getType() == ScResDef.Type.OPT_BGM ? ScResDef.RlFlag.BGM : ScResDef.RlFlag.VOICE;
        final int tid = FaiValObj.TermId.SC;
        MgFilesInfCli mgCli = new MgFilesInfCli(Core.getFlow());
        mgCli.init();

        ParamUpdater paramUpdater = new ParamUpdater(new Param()
                .setInt(FilesGroupEntity.GroupInfo.RL_GROUP_ID, vo.getId())
                .setString(FilesGroupEntity.GroupInfo.GROUP_NAME, name));
        paramUpdater.add(FilesGroupEntity.GroupInfo.RL_FLAG, flag, true);
        FaiList<ParamUpdater> updaters = new FaiList<>();
        updaters.add(paramUpdater);

        MgFilesArg mgFilesArg = new MgFilesArg.Builder(aid, tid, 0, 0, 0)
                .setUpdaterList(updaters)
                .build();
        int rt = mgCli.setFilesGroupList(mgFilesArg);
        HdAssert.Biz.judge(rt != Errno.OK, rt, "更新分类失败")
                .log("update ScFolder error! id=%s;name=%s", vo.getId(), name);

        return JsonResult.success("修改成功");
    }

    @Override
    public JsonResult del(ScCategoryVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        checkArg(vo, "del"); // 参数检查

        HdAssert.Biz.judgeNotLog(vo.getId() <= 0, Errno.ARGS_ERROR, "id不能为空");

        List<Integer> rlIds = new FaiList<>();
        rlIds.add(vo.getId());
        final int tid = FaiValObj.TermId.SC;
        MgFilesInfCli mgCli = new MgFilesInfCli(Core.getFlow());
        mgCli.init();
        MgFilesArg mgFilesArg = new MgFilesArg.Builder(aid, tid, 0, 0, 0)
                .setRlGroupIds(new FaiList<>(rlIds))
                .build();
        int rt = mgCli.delFilesGroupList(mgFilesArg);
        HdAssert.Biz.judge(rt != Errno.OK, rt, "删除文件夹失败！")
                .log("delete ScFolder error! aid=%s; id=%s", aid, vo.getId());

        return JsonResult.success("删除成功");
    }

    @Override
    public FaiList<Param> getCategoryList(ScCategoryVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        HdAssert.Biz.judgeNotLog(vo.getType() <= 0, Errno.ARGS_ERROR, "类型不能为空");

        int flag = vo.getType() == ScResDef.Type.OPT_BGM ? ScResDef.RlFlag.BGM : ScResDef.RlFlag.VOICE;

        SearchArg searchArg = new SearchArg();
        searchArg.start = vo.getOffset();
        searchArg.limit = vo.getPageLimit();

        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.PARENT_ID, ParamMatcher.NE, 0);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.RL_FLAG, ParamMatcher.LAND, flag, flag);
        if (!Str.isEmpty(vo.getName())) {
            searchArg.matcher.and(FilesGroupEntity.GroupInfo.GROUP_NAME, ParamMatcher.LK, vo.getName());
        }
        return getMgFilesGroupList(aid, searchArg).stream().collect(new FaiListCollector<>());
    }

    private Integer getParentId(ScCategoryVO vo) {
        final int sysAid = Web.getFaiCid();

        String name = vo.getType() == ScResDef.Type.OPT_BGM ? "背景音乐" : "配音";
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.AID, ParamMatcher.EQ, sysAid);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.GROUP_NAME, ParamMatcher.EQ, name);

        List<Param> groupList = getMgFilesGroupList(sysAid, searchArg);
        if (groupList.isEmpty()) return -1;

        Param parentFolderEntity = groupList.get(0);
        return parentFolderEntity.getInt("id", -1);
    }

    private void checkArg(ScCategoryVO vo, String method) {
        if ("add".equals(method) || "set".equals(method)) {
            Integer parentId = getParentId(vo);
            HdAssert.Biz.judgeNotLog(parentId == null || parentId <= 0, Errno.ARGS_ERROR, "文件夹不能为空");
            vo.setParentId(parentId);
        }
        if ("del".equals(method)) {
            // 需要判断当前分类下是否有资源，有资源不能删除
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher();
            searchArg.matcher.and(ScResDef.Info.AID, ParamMatcher.EQ, Core.getAid());
            searchArg.matcher.and(ScResDef.Info.FOLDER_ID, ParamMatcher.EQ, vo.getId());
            searchArg.matcher.and(ScResDef.Info.STATUS, ParamMatcher.EQ, ScResDef.Status.NORMAL);

            RemoteStandResult resCount = scResCli.getResCount(searchArg, "");
            int rt = resCount.getRt();
            HdAssert.Biz.judgeNotLog(rt != Errno.OK && rt != Errno.NOT_FOUND, Errno.ARGS_ERROR, "检查素材失败");

            int count = resCount.getObject(ScResDef.Protocol.Key.TOTAL_SIZE, Integer.class);
            HdAssert.Biz.judgeNotLog(count > 0, Errno.ARGS_ERROR, "当前分类下有资源，不能删除");
        }
    }

}
