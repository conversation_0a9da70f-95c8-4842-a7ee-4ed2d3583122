<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<!DOCTYPE html>
<html>
<head>
    <title>PDF文件上传配置</title>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_faiHd")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<%=FrontEndDef.getPackageCss("fa-component", "~1.1.0")%>
    <%=FrontEndDef.getPackageJs("fa-component", "~1.1.0")%>
    <style>
        body {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div id="uploadPdfContainer">
        <fa-spin tip="上传中..." :spinning="isLoading">
            <h2>上传pdf转链接</h2>
            <fa-upload
                accept=".pdf"
                name="filedata"
                action="/ajax/advanceUpload.jsp?cmd=upload"
                :data="uploadData"
                :show-upload-list="false"
                :before-upload="beforeUploadPdf"
                @change="uploadPdfChange"
            >
                <fa-button> 上传pdf </fa-button>
            </fa-upload>
            <div v-show="pdfUrl" style="margin-top: 10px;">pdf路径: {{pdfUrl}}</div>
        </fa-spin>
    </div>
    <script>
        var uploadPdfContainer = new Vue({
            el: '#uploadPdfContainer',
            data: function () {
                return {
                    pdfUrl: '',
                    uploadData: {},
                    isLoading: false
                }
            },
            methods: {
                beforeUploadPdf: function(file) {
                    var fileType = file.type ? file.type.split('/')[1] : file.name.split('.')[1];
                    fileType = fileType.toUpperCase();
                    if(['PDF'].indexOf(fileType) == -1) {
                        this.$message.error('文件' + file.name + '类型不允许');
                        return false;
                    }
                    // if (file.size > 10 * 1024 * 1024) {
                    //     this.$message.error('上传文件需小于10M');
                    //     return false;
                    // }
                    //在safari浏览器下 lastModifiedDate 是一个时间戳,其他浏览器下是一个date()对象。
                    var timeStamp = typeof file.lastModifiedDate === 'object' ? file.lastModifiedDate.getTime() : file.lastModifiedDate;
                    this.uploadData = {
                        complete: true,
                        initSize: 0,
                        totalSize: file.size,
                        fileMd5: $.md5(file.name + file.size + file.type + timeStamp)
                    };
                    this.isLoading = true;
                },

                uploadPdfChange: function(obj) {
                    var file = obj.file,
                        response = file.response;
                    if(file.status == 'done' && response) {
                        if(!response.success) {
                            return this.$message.error(response.msg);
                        }
                        this.isLoading = false;
                        this.pdfUrl = response.path;
                    }
                },
            }
        });
    </script>
</body>
</html>