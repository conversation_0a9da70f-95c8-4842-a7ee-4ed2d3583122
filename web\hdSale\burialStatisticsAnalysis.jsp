<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}%>
<!DOCTYPE html>
<html>
    <head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-resourceOrderAnalyze">
		<!--查询条件 start-->
		<div class="resource" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<span class="demonstration">埋点时间: 
					<el-form-item label="">
						<el-select v-model="month.form.year" filterable>
							<el-option v-for="year in month.yearArr" :label="year.value" :value="year.value" :key="year.id"></el-option>
						</el-select>年
					</el-form-item>
					<el-form-item label="">
						<el-select v-model="month.form.month" filterable>
							<el-option v-for="month in month.monthArr" :label="month.value" :value="month.value" :key="month.id"></el-option>
						</el-select>月
					</el-form-item>
				</span>
				<span class="demonstration">所在库: 
					<el-select v-model="form.module.value">
						<el-option v-for="select in form.module.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<span class="demonstration">类型: 
					<el-select v-model="form.type.value">
						<el-option v-for="select in form.type.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<el-form-item>
						<el-button type="primary"  @click="getDataListByArgs('select')">查询</el-button>
						<el-button type="primary"  @click="getDataListByArgs('export')">导出</el-button>
				</el-form-item>

				<div>
					<el-table :data="form.dataList" style="width: 1000" max-height="620" stripe>
						<el-table-column  prop="moduleId" label="所在库" width="120"  align="center"></el-table-column>
						<el-table-column  prop="element" label="元素名" width="160"  align="center"></el-table-column>
						<el-table-column  prop="type" label="类型" width="160" align="center"></el-table-column>
						<el-table-column  prop="actionNum" label="点击次数" width="120"  align="center"></el-table-column>
						<el-table-column  prop="createTime" label="时间" width="160"  align="center"></el-table-column>
					</el-table>
				</div>
			</el-form>
		</div>
		<!--查询条件 end-->
	</body>
	
	<script type="text/javascript">
		var burialStatistics = new Vue({
			el: '.resource',
			data: {
				month: {
					form: {//这里是为了填充默认值
						year:"",
						month:""
					},
					yearArr:[],
					monthArr:[],
					tableData: [],
					oldAndNewUrl:'',

				},
				form : {
				   dataList:[],
				   exportFlag:false,
				   type:{
						value:0,
						name:"全部",
						labelList:[
							{name:"全部",label:0},
							{name:"按钮类型",label:1},
							{name:"输入框类型",label:2},
							{name:"日期框",label:3},
							{name:"下拉选项",label:4}
						]
					},
					module:{
						value:0,
						name:"全部",
						labelList:[
							{name:"全部",label:0},
							{name:"当月库",label:1},
							{name:"成交库",label:4},
							{name:"A库",label:2},
							{name:"B库",label:3},
							{name:"付款记录",label:5},
						]
					}
				},
				yearArr:[],
				monthArr:[],
		        sortBy:"0"
			},
			created:function(){
				this.getDataListByArgs('select');
				var nowDate = new Date();
				for (var year = 2016; year <= nowDate.getFullYear(); year++) {
					this.month.yearArr.push({"value":year});
				}
				for(var month = 1; month <= 12; month ++){
					this.month.monthArr.push({"value":month});
				}
				this.month.form.year = nowDate.getFullYear();
				this.month.form.month = nowDate.getMonth() + 1;
			},
			methods: {
				getDataListByArgs(val) {
					var excel=val==='export'
					var arg = {
							"cmd":"selectBurialStatisticInfo",
							"year":this.month.form.year,
							"month":this.month.form.month,
							"type":this.form.type.value,
							"module":this.form.module.value,
							"exportFlag":excel,
					}
					if(val === 'export'){
						window.location.href = '/ajax/hdSale_h.jsp'+Fai.tool.parseJsonToUrlParam(arg,true);
					}else{
						Fai.http.post("hdSale_h.jsp",arg, false).then(result => {
							if(result.success){
								this.form.dataList = result.dataList;
							}
					   });
					}
					
				},
				
			}
		});


	</script>
	
</html>