<template>
  <div class="mt-[30px]">
    <p class="mb-[10px] text-[16px] font-bold">视频示例配置</p>
    <el-form-item prop="script.content" :rules="[{ validator: validateScriptContent }]">
    <!-- <el-form-item prop="script.content"> -->
      <div class="flex">
        <p class="example-title-required mr-[10px]">示例脚本</p>
        <p class="text-gray-400">
          (用户在创建模板后，在项目编辑器中可查看的示例脚本)
        </p>
        <ViewScriptExample />
      </div>
      <!-- {{ templateInfo.script.content }} -->
      <HighLightEditor :contentHtml.sync="templateInfo.script.content" :inputFormList="templateInfo.inputFormList" />
    </el-form-item>

      <div class="flex text-[14px] mt-[25px]">
        <p class="example-title-required mr-[10px]">模板展示视频</p>
        <p class="text-gray-400">
          (用户在模板中心预览模板时，在预览弹窗中可播放的视频)
        </p>
      </div>
      <p class="text-gray-400 text-[14px]">提示：当封面图未上传时，b端默认选取视频首帧作为封面图</p>
      <div class="flex p-[20px]">
      <el-form-item :prop="'setting'" :rules="[{ validator: validateResId }]">
        <div class="mr-[40px]">
          <p class="mb-[10px]">视频</p>
          <UploadVideo title="上传视频" :file-list="templateInfo._videoFileList" @upload-success="uploadVideoSuccess" @upload-remove="uploadVideoRemove" @on-preview="onVideoPreview" />
        </div>
      </el-form-item>
      <el-form-item>
        <div>
          <p class="mb-[10px]">封面图</p>
          <UploadImg title="上传封面图" :file-list="templateInfo._coverFileList" @upload-success="uploadCoverSuccess" @upload-remove="uploadCoverRemove" />
        </div>
      </el-form-item>
    </div>
  </div>
</template>

<script>
import HighLightEditor from "./highLightEditor.vue";
import ViewScriptExample from "./viewScriptExample.vue";
import UploadImg from "@/views/scPortal/components/scTemplate/common/uploadImg.vue";
import UploadVideo from "../../common/uploadVideo.vue";
export default {
  name: "VideoExampleSetting",
  inject: ['templateFormRef'],
  components: {
    HighLightEditor,
    ViewScriptExample,
    UploadImg,
    UploadVideo,
  },
  props: {
    templateInfo: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  },
  watch: {
    'templateInfo.script.content': {
      handler(newVal) {
        // const form = this.templateFormRef();
        // this.$nextTick(() => {
        //   form.validateField('script.content');
        // });
        this.valitdateFormContent('script.content');
      },
      deep: true,
    },
  },
  data() {
    return {};
  },
  methods: {
    /**
     * 验证表单内容
     * @param formStr 表单字段
     */
    valitdateFormContent(formStr) {
      const form = this.templateFormRef();
      this.$nextTick(() => {
        form.validateField(formStr);
      });
    },
    /**
     * 上传视频成功
     * @param response 
     */
    uploadVideoSuccess(response) {
      if (!this.templateInfo?.setting?.video) {
        this.templateInfo.setting = {
          video: {}
        }
      } 
      this.templateInfo.setting.video = {
        videoId: response.id, // 视频id
        videoType: response.type, // 视频类型
        coverId: response.coverId, // 视频封面id
        coverType: response.coverType, // 视频封面类型
      }
      this.setCoverImg();
      this.valitdateFormContent('setting');
    },
    /**
     * 当封面图未上传时，b端默认选取视频首帧作为封面图
     */
    setCoverImg() { 
      if (!this.templateInfo?.cover?.resId && this.templateInfo?.setting?.video?.coverId) {
        this.templateInfo.cover = {
          resId: this.templateInfo.setting.video.coverId, // 图片id
          resType: this.templateInfo.setting.video.coverType, // 图片类型
        };
      }
    },
    /**
     * 删除封面图
     */
    removeCoverImg() {
      this.templateInfo.cover = undefined;
    },
    /**
     * 删除视频
     */
    uploadVideoRemove(response) {
      this.templateInfo.setting.video = {};
    },
    /**
     * 上传封面图成功
     * @param response 
     */
    uploadCoverSuccess(response) {
      this.templateInfo.cover = {
        resId: response.id, // 图片id
        resType: response.type, // 图片类型
      };
      console.log(response, 'uploadCoverSuccess');
    },
    /**
     * 删除封面图
     */
    uploadCoverRemove() {
      this.templateInfo.cover = {};
      this.removeCoverImg();
    },
    /**
     * 验证示例脚本
     */
    validateScriptContent(rule, value, callback) {
      if (!value || value === '<p></p>') {
        callback(new Error('示例脚本不能为空'));
        return;
      }
      callback();
    },
    /**
     * 验证是否上传了视频
     */
    validateResId(rule, value, callback) {
      if (!value?.video?.videoId) {
        callback(new Error('请上传示例视频'));
        return;
      }
      callback();
    },
    /**
     * 视频预览
     */
    onVideoPreview() {
      console.log('onVideoPreview');
    }
  },
};
</script>

<style lang="scss" scoped>
.tag {
  padding: 2px 4px;
  margin: 0 1px;
  color: #3261FD;
}

/* 添加标签样式 */
.tag-label {
  display: inline-block;
  border-radius: 4px;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  padding: 0 4px;
  line-height: 20px;
}

.example-title-required {
  &::before {
    content: "*";
    color: #f56c6c;
    margin-right: 4px;
  }
}
</style>
