<template>
  <div class="mt-[40px]">
    <p class="mb-[10px] text-[16px] font-bold">剪辑逻辑配置</p>
    <el-table :data="protoInfo.commList" v-if="protoInfo.commList && protoInfo.commList.length > 0">
      <el-table-column prop="label" label="脚本模块" width="180"></el-table-column>
      <el-table-column prop="resList" label="素材组" width="700">
        <template slot-scope="scope">
          <el-form-item :prop="'commList.' + scope.$index + '.resList'" :rules="[{ validator: validateResList }]">
            <el-select v-model="scope.row.resList" multiple placeholder="请选择素材组" class="!w-[680px]">
              <el-option v-for="item in protoInfo.resFormList" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'EditVideoMaterialGroup',
  props: {
    protoInfo: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    /**
     * 素材组数量变化
     */
    'protoInfo.resFormList.length': {
      handler(newVal, oldVal) {
        if (newVal < oldVal) {
          // 如果素材组数量减少，假如减少的是选中的素材组，则需要去掉对应的素材组
          const resIds = this.protoInfo.resFormList.map(item => item.id); // 新素材组ID列表
          this.protoInfo.commList.forEach(item => {
            if (item.resList.some(id => !resIds.includes(id))) {
              item.resList = item.resList.filter(id => resIds.includes(id));
            }
          });
        }
      },
      deep: true,
    }
  },
  methods: {
    /**
     * 验证素材组
     */
    validateResList(rule, value, callback) {
      if (value.length === 0) {
        callback(new Error('素材组不能为空'));
        return;
      }
      
      const hasRequired = value.some(selectedId => 
        this.protoInfo.resFormList.some(item => item.id === selectedId && item.required)
      );
      
      if (!hasRequired) {
        callback(new Error('素材组必须选择一个必传的素材组'));
        return;
      }
      callback();
    },
    // changeMaterialNum() {
    //   this.protoInfo.script = this.protoInfo.script.slice(0, this.materialNum);
    // },
    // initData() {
    //   this.materialNum = this.protoInfo.script.length;
    // }
  },
}
</script>

<style lang="scss" scoped>
</style>
