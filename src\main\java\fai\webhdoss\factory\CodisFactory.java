package fai.webhdoss.factory;

import fai.app.HdOrderDef;
import fai.comm.cache.redis.RedisCacheManager;
import fai.comm.cache.redis.config.RedisClientConfig;
import fai.comm.cache.redis.pool.JedisPool;
import fai.comm.cache.redis.pool.JedisPoolFactory;
import fai.comm.util.Log;
import fai.comm.util.Param;
import fai.web.Web;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.stereotype.Component;

/**
 * codis bean
 * <AUTHOR>
 */
@Component("codis")
public class CodisFactory implements FactoryBean<RedisCacheManager> {

    @Override
    public RedisCacheManager getObject() throws Exception {
        Param cacheOption = HdOrderDef.getRedisCacheParamByEnvirment(Web.getEnvMode());
        try {
            RedisClientConfig config = new RedisClientConfig(cacheOption);
            JedisPool jedisPool = JedisPoolFactory.createJedisPool(config);
            return new RedisCacheManager(jedisPool, config.getExpire(), config.getExpireRandom());
        } catch (Exception e) {
            Log.logErr("codis init err");
            throw e;
        }
    }

    @Override
    public Class<?> getObjectType() {
        return RedisCacheManager.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
