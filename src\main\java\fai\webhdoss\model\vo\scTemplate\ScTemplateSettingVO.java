package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateSettingVO {

    // 图片资源 集合
    private List<ScTemplateCommResVO> imgList;

    // 视频资源
    private ScTemplateVideoVO video;
}
