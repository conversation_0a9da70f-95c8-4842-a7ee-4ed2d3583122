package fai.webhdoss.controller;

import com.google.common.collect.Maps;
import fai.app.HdModelCategoryDisplayDef;
import fai.app.HdModelDef;
import fai.cli.HdOssAffairCli;
import fai.comm.cache.redis.RedisCacheManager;
import fai.comm.util.*;
import fai.web.App;
import fai.web.Core;
import fai.web.inf.HdOss;
import fai.web.inf.Kid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Api(value = "场景分类")
@RestController
@RequestMapping("caseMode")
public class HdCaseModeController {

    @Autowired
    private HdOssAffairCli hdOssAffairCli;
    @Autowired
    private RedisCacheManager codis;


    @CrossOrigin(methods = {RequestMethod.POST,RequestMethod.GET,RequestMethod.OPTIONS})
    @RequestMapping("/getCategoryDisplay")
    @ApiOperation(value = "获取场景模板", httpMethod = "GET")
    private String getCategoryDisplay(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        Param result = new Param();
        int aid = 1;
        int displayType = Parser.parseInt(request.getParameter("displayType"), 0); /** {@link fai.app.HdModelCategoryDisplayDef.DisplayType} */
        int category = Parser.parseInt(request.getParameter("category"), 0); /** {@link fai.app.HdModelCategoryDisplayDef.CategoryDisplay} */
        if (displayType <= 0 || category < 0) {
            result.setString("msg","displayType <= 0 || category < 0");
            result.setBoolean("success",false);
            result.setInt("rt",Errno.ARGS_ERROR);
            return result.toJson();
        }
        HdOss hdoss = (HdOss) Core.getCorpKit(aid, Kid.HD_OSS);
        ParamMatcher matcher = new ParamMatcher(HdModelCategoryDisplayDef.Info.DISPLAY_TYPE, ParamMatcher.EQ, displayType);
        matcher.and(HdModelCategoryDisplayDef.Info.CATEGORY, ParamMatcher.EQ, category);
        Ref<Param> res = new Ref<Param>();
        rt = hdoss.getCategoryDisplay(matcher, res);
        Param categoryDisplay = res.value;
        FaiList<Integer> modelIdList = FaiList.parseIntList(categoryDisplay.getString(HdModelCategoryDisplayDef.Info.MODEL_LIST), new FaiList<Integer>());
        if (rt != Errno.OK || Str.isEmpty(categoryDisplay)) {
            App.logErr(rt, "getCategoryDisplay err matcher=%s", matcher.toJson());
            result.setString("msg","获取失败");
            result.setBoolean("success",false);
            result.setInt("rt",rt);
            return result.toJson();
        } else {
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(HdModelDef.Info.ID, ParamMatcher.IN, modelIdList);
            FaiList<Param> modelList = null;
            if (!modelIdList.isEmpty()) {
                modelList = hdoss.getHdModelList(searchArg);
            }
            Map<Integer, Param> modelMap = Maps.newHashMap();
            if (null != modelList) {
                for (Param model : modelList) {
                    int id = model.getInt(HdModelDef.Info.ID, -1);
                    if (id < 0) continue;
                    modelMap.put(id, model);
                }
            }
            FaiList<Param> data = new FaiList<Param>();
            if (!modelMap.isEmpty()) {
                for (int id : modelIdList) {
                    Param model = modelMap.get(id);
                    if (null == model) continue;
                    data.add(model);
                }
                categoryDisplay.remove(HdModelCategoryDisplayDef.Info.MODEL_LIST);
                categoryDisplay.setList(HdModelCategoryDisplayDef.Info.MODEL_LIST, data);
            }
            result.setString("msg","success");
            result.setBoolean("success",true);
            result.setInt("rt",rt);
            result.setParam("data", categoryDisplay);
            return result.toJson();
        }
    }

    @CrossOrigin(methods = {RequestMethod.POST,RequestMethod.GET,RequestMethod.OPTIONS})
    @RequestMapping("/setCategoryDisplay")
    @ApiOperation(value = "设置场景模板", httpMethod = "GET")
    public String setCategoryDisplay(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        Param result = new Param();
        int aid = 1;
        int displayType = Parser.parseInt(request.getParameter("displayType"), 0); /** {@link fai.app.HdModelCategoryDisplayDef.DisplayType} */
        int category = Parser.parseInt(request.getParameter("category"), 0); /** {@link fai.app.HdModelCategoryDisplayDef.CategoryDisplay} */
        String name = Parser.parseString(request.getParameter("name"), ""); /** {@link fai.app.HdModelCategoryDisplayDef.Info#NAME} */
        String img = Parser.parseString(request.getParameter("img"), ""); /** {@link fai.app.HdModelCategoryDisplayDef.Info#IMG} */
        String modelListStr = Parser.parseString(request.getParameter("modelList"), ""); /** {@link fai.app.HdModelCategoryDisplayDef.Info#MODEL_LIST} */
        if (displayType <= 0 || category < 0) {
            result.setString("msg","displayType <= 0 || category < 0");
            result.setBoolean("success",false);
            result.setInt("rt",Errno.ARGS_ERROR);
            return result.toJson();
        }
        if (displayType != HdModelCategoryDisplayDef.DisplayType.CATEGORY_DISPLAY) {
            category = 0; // 除了4有category，其他卡片暂不设category，保留为0;
        }
        HdOss hdoss = (HdOss) Core.getCorpKit(aid, Kid.HD_OSS);
        Ref<Param> res = new Ref<Param>();
        ParamMatcher matcher = new ParamMatcher(HdModelCategoryDisplayDef.Info.DISPLAY_TYPE, ParamMatcher.EQ, displayType);
        matcher.and(HdModelCategoryDisplayDef.Info.CATEGORY, ParamMatcher.EQ, category);
        rt = hdoss.getCategoryDisplay(matcher, res);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            App.logErr("getCategoryDisplay err;displayType=%d;category=%d;", displayType, category);
            result.setString("msg","修改失败");
            result.setBoolean("success",false);
            result.setInt("rt",rt);
            return result.toJson();
        }
        Param categoryDisplayParam = res.value;
        if (rt == Errno.NOT_FOUND || Str.isEmpty(categoryDisplayParam)) {
            categoryDisplayParam = new Param();
            categoryDisplayParam.setInt(HdModelCategoryDisplayDef.Info.DISPLAY_TYPE, displayType);
            categoryDisplayParam.setInt(HdModelCategoryDisplayDef.Info.CATEGORY, category);
        } else {
            categoryDisplayParam.remove(HdModelCategoryDisplayDef.Info.ID);
            categoryDisplayParam.remove(HdModelCategoryDisplayDef.Info.DISPLAY_TYPE);
            categoryDisplayParam.remove(HdModelCategoryDisplayDef.Info.CATEGORY);
        }
        if (!Str.isEmpty(name)) {
            categoryDisplayParam.setString(HdModelCategoryDisplayDef.Info.NAME, name);
        }
        if (!Str.isEmpty(img)) {
            categoryDisplayParam.setString(HdModelCategoryDisplayDef.Info.IMG, img);
        }
        FaiList<Integer> modelIdList = FaiList.parseIntList(modelListStr, new FaiList<Integer>());
        categoryDisplayParam.setString(HdModelCategoryDisplayDef.Info.MODEL_LIST, modelIdList.toJson());
        rt = hdoss.setCategoryDisplay(categoryDisplayParam, matcher);
        if (rt != Errno.OK) {
            App.logErr(rt, "setCategoryDisplay err matcher=%s", matcher.toJson());
            result.setString("msg","修改失败");
            result.setBoolean("success",false);
            result.setInt("rt",rt);
            return result.toJson();
        } else {
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(HdModelDef.Info.ID, ParamMatcher.IN, modelIdList);
            FaiList<Param> modelList = hdoss.getHdModelList(searchArg);
            Map<Integer, Param> modelMap = Maps.newHashMap();
            if (null != modelList) {
                for (Param model : modelList) {
                    int id = model.getInt(HdModelDef.Info.ID, -1);
                    if (id < 0) continue;
                    modelMap.put(id, model);
                }
            }
            FaiList<Param> data = new FaiList<Param>();
            if (!modelMap.isEmpty()) {
                for (int id : modelIdList) {
                    Param model = modelMap.get(id);
                    if (null == model) continue;
                    data.add(model);
                }
            }
            result.setString("msg","success");
            result.setBoolean("success",true);
            result.setInt("rt",rt);
            result.setList("data", data);
            return result.toJson();
        }
    }

    /**
     * 获取所有设计师
     * <AUTHOR>
     * @return String
     */
    @CrossOrigin(methods = {RequestMethod.POST,RequestMethod.GET,RequestMethod.OPTIONS})
    @RequestMapping("/getModelListByCond")
    @ApiOperation(value = "根据模板场景获取", httpMethod = "GET")
    public String getModelListByCond(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        Param result = new Param(true);
        int aid = 1;
        int displayType = Parser.parseInt(request.getParameter("displayType"), 4); /** {@link fai.app.HdModelCategoryDisplayDef.DisplayType} */
        int scene = Parser.parseInt(request.getParameter("scene"), -2); /** {@link fai.app.HdModelCategoryDisplayDef.CategoryDisplay} */
        if (displayType <= 0 || scene < -2) {
            result.setString("msg","displayType <= 0 || scene < 0");
            result.setBoolean("success",false);
            result.setInt("rt",Errno.ARGS_ERROR);
            return result.toJson();
        }
        HdOss hdoss = (HdOss) Core.getCorpKit(aid, Kid.HD_OSS);
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdModelDef.Info.PROPERTY, ParamMatcher.EQ, HdModelDef.Property.ISPUB);
        switch (displayType) {
            case 2: break;
            case 3:
                searchArg.cmpor = new ParamComparator(HdModelDef.Info.HOT_SCORE, true);
                searchArg.cmpor.addKey(HdModelDef.Info.RECOM_SCORE, true);
                break;
            case 4:
                if (scene == -1) {
                    Log.logStd("获取全部");
                } else {
                    scene = 1 << (scene - 2);
                    searchArg.matcher.and(HdModelDef.Info.CATEGORY2, ParamMatcher.LAND, scene, scene);
                    searchArg.matcher.and(HdModelDef.Info.CATEGORY2, ParamMatcher.NE, 0);
                }
                searchArg.cmpor = new ParamComparator(HdModelDef.Info.PUBDATE, true);
                break;
            default:
                break;
        }
        Log.logStd("searchArg.matcher=%s", searchArg.matcher);
        FaiList<Param> modelList = hdoss.getHdModelList(searchArg);
        if (null != modelList) {
            rt = Errno.OK;
        }
        if (rt != Errno.OK) {
            App.logErr(rt, "getHdModelList err scene=%d", scene);
            result.setString("msg","获取失败");
            result.setBoolean("success",false);
            result.setInt("rt",rt);
            return result.toJson();
        } else {
            Param modelMap = new Param();
            for (Param model : modelList) {
                int id = model.getInt(HdModelDef.Info.ID, -1);
                if (id < 0) continue;
                modelMap.setParam(String.valueOf(id), model);
            }
            result.setString("msg","success");
            result.setBoolean("success",true);
            result.setInt("rt",rt);
            result.setParam("data", modelMap);
            return result.toJson();
        }
    }


}
