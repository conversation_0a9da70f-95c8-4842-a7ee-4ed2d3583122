package fai.webhdoss.service;

import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.HdCasePreViewVO;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 案例业务逻辑封装接口
 * @date 2021/3/14 11:38
 */
public interface HdCaseService {

    /**
     * 设置预览窗数据
     * @param casePreViewVO 互动案例预览窗封装
     * @return int
     * <AUTHOR>
     * @date 2021/3/14
     */
    int settingPreview(HdCasePreViewVO casePreViewVO);

    /**
     * 投放预览窗
     * @param caseId 案例id
     * @param isPut 是否投放
     * @return int
     * <AUTHOR>
     * @date 2021/3/14
     */
    JsonResult putPreview(Integer caseId, Integer oAid, Integer oGameId, boolean isPut);

    /**
     * 更新预览窗上下架状态
     * @param caseId 案例id
     * @param caseId isPutOn 是否上架
     * @return int 案例id
     * <AUTHOR>
     * @date 2021/3/14
     */
    JsonResult updateStatusPreview(Integer caseId, Integer oAid, Integer oGameId, boolean isPutOnShelf);

    /**
     * 获取案例库列表
     * <AUTHOR>
     * @date 2021/9/6
     */
    JsonResult getHdCaseList(int aid);

    /**
     * 获取案例库列表
     * <AUTHOR>
     * @date 2021/9/6
     */
    JsonResult getCaseListFromHd(String secretKey, FaiList<Param> caseList)throws Exception;
}
