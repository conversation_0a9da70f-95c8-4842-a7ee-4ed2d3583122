import { FILE_EXTENSIONS } from '@/views/scPortal/config/fileType.js';

/**
 * 获取资源URL
 * @param {string} resType 资源类型
 * @param {string} resId 资源ID
 * @returns {string} 资源URL
 */
export const getResUrl = (resType, resId) => {
  const domain = faiIfram?.$store?.state?.scPortal?.domainInfo?.scUsrResOssRoot;
  return `${domain}/${resType}/${resId}.${
      FILE_EXTENSIONS[resType]
    }`;
}

/**
 * 判断是否有上架/下架/删除模板权限，废弃原型权限
 * 管理员、速创产品、速创运营有这些权限（速创开发无这些权限）
 */
export const checkPassOperateFunc = () => {
  const staffInfo = faiIfram?.$store?.state?.scPortal?.staffInfo
  if (staffInfo) {
    return staffInfo.all || staffInfo.scPm || staffInfo.scOperation
  }
}