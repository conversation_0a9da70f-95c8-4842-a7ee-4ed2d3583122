package fai.webhdoss.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;


@Data
@Accessors(chain = true)
@ApiModel("活动方案vo")
public class HdActivityPlanVO {

    @ApiModelProperty("方案编号")
    Integer id;

    @ApiModelProperty("企业账号")
    String accountId;

    @ApiModelProperty("活动原型")
    String prototype;

    @NotNull(message = "aid不能为空")
    @Min(value = 1, message = "aid不能为空")
    @ApiModelProperty("aid")
    Integer aid;

    @NotNull(message = "活动编号不能为空")
    @Min(value = 1, message = "活动编号不能为空")
    @ApiModelProperty("活动编号")
    Integer gameId;

    @ApiModelProperty("传单模板标识码")
    String flyerModId;

    @Length(min = 1, max= 20, message = "方案名称长度为1到20个字符")
    @ApiModelProperty("方案名称")
    String planName;

    @NotNull(message = "行业不能为空")
    @Min(value = 1, message = "行业不能为空")
    @ApiModelProperty("行业id")
    Integer tradeId;

    @Length(min = 1, max = 40, message = "活动目标长度为1到40个字符")
    @ApiModelProperty("活动目标")
    String target;

    @Length(min = 1, max = 40, message = "方案概述长度为1到40个字符")
    @ApiModelProperty("方案概述")
    String summary;

    @Length(min = 1, max = 300, message = "方案亮点长度为1到300个字符")
    @ApiModelProperty("方案亮点")
    String shinePoint;

    @Min(value = 1, message = "推荐分数不能为空")
    @ApiModelProperty("推荐分数")
    Integer score;

    @Min(value = 0, message = "模板id不能为空, 不能为负")
    @ApiModelProperty("模板id")
    Integer modId;

    @ApiModelProperty("属性：是否发布")
    Boolean published;

    @ApiModelProperty("发布时间")
    Long publishedTime;

}
