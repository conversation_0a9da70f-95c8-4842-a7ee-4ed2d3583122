var topNavObj = new Vue({
  el:'.fai-header-nav',
  data: {
      navList: [],
      // navList: [{title:"主页",url:"index"},{title:"互动销售",url:"hdSale"}],
      active:"index",
  },
  created: function(){
    // 刷新的时候回到对应的导航页面
    var result = Fai.url.getNavFlag();
    console.info(result);
    if(result!=""){
      this.active = result[1];
    }
    
    // 获取头部导航数据
    getTopNav();
    // 更新左部导航数据
    getLeftNav(this.active);
    console.info(this.active);
  },
  methods: {
    // 头部导航选择的时候改变左侧对应的导航栏目
    handleSelect(index, event) {
      var url = document.location.protocol+"//"+document.location.host;
      url +="#/"+index;
      document.location.href = url;
      console.info(9999999);
      console.info(url);
      getLeftNav(index);
    }
  }
})

// 获取头部导航
function getTopNav(){
  var urlParam = {
      cmd:'getTopNav',
      token:'54381ee5ee4cd4bffb1f69b2ea663f3b',
    }
  
  Vue.http.post("/ajax/navList_h.jsp", urlParam, {emulateJSON:true}).then(response => {
    // success
    topNavObj.$data.navList = response.data;
  }, response => {
    console.info("getTopNav err");
  });
}

// 获取左侧导航
function getLeftNav(index){
  var urlParam = {
      cmd:'getLeftNav',
      menu:index,
    }
  index == 'hdPartner' && (typeof leftNavObj != 'undefined') && (leftNavObj.$data.navList = []);
  
  Vue.http.post("/ajax/navList_h.jsp", urlParam, {emulateJSON:true}).then(response => {
    // success
    leftNavObj.$data.navList = response.data
    leftNavObj.$data.active = "showLeftNav";
    console.info("getleftNav succ");
  }, response => {
    // error 
  });
}