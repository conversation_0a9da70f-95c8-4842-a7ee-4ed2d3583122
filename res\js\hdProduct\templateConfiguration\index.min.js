function _defineProperty(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _createForOfIteratorHelperLoose(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var i=0;return function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=new Array(t);r<t;r++)i[r]=e[r];return i}function _extends(){return(_extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(this,arguments)}!function(){"use strict";var e=void 0;function t(){t.init||(t.init=!0,e=-1!==function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var r=e.indexOf("rv:");return parseInt(e.substring(r+3,e.indexOf(".",r)),10)}var i=e.indexOf("Edge/");return i>0?parseInt(e.substring(i+5,e.indexOf(".",i)),10):-1}())}var r={render:function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})},staticRenderFns:[],_scopeId:"data-v-b329ee4c",name:"resize-observer",methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify"))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!e&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),delete this._resizeObject.onload)}},mounted:function(){var r=this;t(),this.$nextTick(function(){r._w=r.$el.offsetWidth,r._h=r.$el.offsetHeight});var i=document.createElement("object");this._resizeObject=i,i.setAttribute("aria-hidden","true"),i.setAttribute("tabindex",-1),i.onload=this.addResizeHandlers,i.type="text/html",e&&this.$el.appendChild(i),i.data="about:blank",e||this.$el.appendChild(i)},beforeDestroy:function(){this.removeResizeHandlers()}};var i={version:"0.4.5",install:function(e){e.component("resize-observer",r),e.component("ResizeObserver",r)}},n=null;function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e){return function(e){if(Array.isArray(e)){for(var t=0,r=new Array(e.length);t<e.length;t++)r[t]=e[t];return r}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}"undefined"!=typeof window?n=window.Vue:"undefined"!=typeof global&&(n=global.Vue),n&&n.use(i);var l=function(){function e(t,r,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.el=t,this.observer=null,this.frozen=!1,this.createObserver(r,i)}var t,r,i;return t=e,(r=[{key:"createObserver",value:function(e,t){var r=this;if(this.observer&&this.destroyObserver(),!this.frozen){var i;if(this.options="function"==typeof(i=e)?{callback:i}:i,this.callback=function(e,t){r.options.callback(e,t),e&&r.options.once&&(r.frozen=!0,r.destroyObserver())},this.callback&&this.options.throttle){var n=(this.options.throttleOptions||{}).leading;this.callback=function(e,t){var r,i,n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=function(s){for(var l=arguments.length,c=new Array(l>1?l-1:0),u=1;u<l;u++)c[u-1]=arguments[u];if(n=c,!r||s!==i){var d=o.leading;"function"==typeof d&&(d=d(s,i)),r&&s===i||!d||e.apply(void 0,[s].concat(a(n))),i=s,clearTimeout(r),r=setTimeout(function(){e.apply(void 0,[s].concat(a(n))),r=0},t)}};return s._clear=function(){clearTimeout(r),r=null},s}(this.callback,this.options.throttle,{leading:function(e){return"both"===n||"visible"===n&&e||"hidden"===n&&!e}})}this.oldResult=void 0,this.observer=new IntersectionObserver(function(e){var t=e[0];if(e.length>1){var i=e.find(function(e){return e.isIntersecting});i&&(t=i)}if(r.callback){var n=t.isIntersecting&&t.intersectionRatio>=r.threshold;if(n===r.oldResult)return;r.oldResult=n,r.callback(n,t)}},this.options.intersection),t.context.$nextTick(function(){r.observer&&r.observer.observe(r.el)})}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&this.options.intersection.threshold||0}}])&&s(t.prototype,r),i&&s(t,i),e}();function c(e,t,r){var i=t.value;if(i)if("undefined"==typeof IntersectionObserver)console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var n=new l(e,i,r);e._vue_visibilityState=n}}function u(e){var t=e._vue_visibilityState;t&&(t.destroyObserver(),delete e._vue_visibilityState)}var d={bind:c,update:function(e,t,r){var i=t.value;if(!function e(t,r){if(t===r)return!0;if("object"===o(t)){for(var i in t)if(!e(t[i],r[i]))return!1;return!0}return!1}(i,t.oldValue)){var n=e._vue_visibilityState;i?n?n.createObserver(i,r):c(e,{value:i},r):u(e)}},unbind:u};var f={version:"0.4.6",install:function(e){e.directive("observe-visibility",d)}},p=null;"undefined"!=typeof window?p=window.Vue:"undefined"!=typeof global&&(p=global.Vue),p&&p.use(f);var h="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function v(e,t,r){return e(r={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&r.path)}},r.exports),r.exports}var m=v(function(e){var t,r;t=h,r=function(){function e(e){var t=getComputedStyle(e,null).getPropertyValue("overflow");return t.indexOf("scroll")>-1||t.indexOf("auto")>-1}return function(t){if(t instanceof HTMLElement||t instanceof SVGElement){for(var r=t.parentNode;r.parentNode;){if(e(r))return r;r=r.parentNode}return document.scrollingElement||document.documentElement}}},e.exports?e.exports=r():t.Scrollparent=r()}),y={itemsLimit:1e3},b={items:{type:Array,required:!0},keyField:{type:String,default:"id"},direction:{type:String,default:"vertical",validator:function(e){return["vertical","horizontal"].includes(e)}},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"}};function _(){return this.items.length&&"object"!=typeof this.items[0]}var g=!1;if("undefined"!=typeof window){g=!1;try{var w=Object.defineProperty({},"passive",{get:function(){g=!0}});window.addEventListener("test",null,w)}catch(e){}}var S=0;function z(e,t,r,i,n,o,s,a,l,c){"boolean"!=typeof s&&(l=a,a=s,s=!1);var u,d="function"==typeof r?r.options:r;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,n&&(d.functional=!0)),i&&(d._scopeId=i),o?(u=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},d._ssrRegister=u):t&&(u=s?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,a(e))}),u)if(d.functional){var f=d.render;d.render=function(e,t){return u.call(t),f(e,t)}}else{var p=d.beforeCreate;d.beforeCreate=p?[].concat(p,u):[u]}return r}var O={name:"RecycleScroller",components:{ResizeObserver:r},directives:{ObserveVisibility:d},props:_extends({},b,{itemSize:{type:Number,default:null},gridItems:{type:Number,default:void 0},itemSecondarySize:{type:Number,default:void 0},minItemSize:{type:[Number,String],default:null},sizeField:{type:String,default:"size"},typeField:{type:String,default:"type"},buffer:{type:Number,default:200},pageMode:{type:Boolean,default:!1},prerender:{type:Number,default:0},emitUpdate:{type:Boolean,default:!1},skipHover:{type:Boolean,default:!1},listTag:{type:String,default:"div"},itemTag:{type:String,default:"div"},listClass:{type:[String,Object,Array],default:""},itemClass:{type:[String,Object,Array],default:""}}),data:function(){return{pool:[],totalSize:0,ready:!1,hoverKey:null}},computed:{sizes:function(){if(null===this.itemSize){for(var e,t={"-1":{accumulator:0}},r=this.items,i=this.sizeField,n=this.minItemSize,o=1e4,s=0,a=0,l=r.length;a<l;a++)(e=r[a][i]||n)<o&&(o=e),s+=e,t[a]={accumulator:s,size:e};return this.$_computedMinItemSize=o,t}return[]},simpleArray:_},watch:{items:function(){this.updateVisibleItems(!0)},pageMode:function(){this.applyPageMode(),this.updateVisibleItems(!1)},sizes:{handler:function(){this.updateVisibleItems(!1)},deep:!0},gridItems:function(){this.updateVisibleItems(!0)},itemSecondarySize:function(){this.updateVisibleItems(!0)}},created:function(){this.$_startIndex=0,this.$_endIndex=0,this.$_views=new Map,this.$_unusedViews=new Map,this.$_scrollDirty=!1,this.$_lastUpdateScrollPosition=0,this.prerender&&(this.$_prerender=!0,this.updateVisibleItems(!1)),this.gridItems&&!this.itemSize&&console.error("[vue-recycle-scroller] You must provide an itemSize when using gridItems")},mounted:function(){var e=this;this.applyPageMode(),this.$nextTick(function(){e.$_prerender=!1,e.updateVisibleItems(!0),e.ready=!0})},activated:function(){var e=this,t=this.$_lastUpdateScrollPosition;"number"==typeof t&&this.$nextTick(function(){e.scrollToPosition(t)})},beforeDestroy:function(){this.removeListeners()},methods:{addView:function(e,t,r,i,n){var o={item:r,position:0},s={id:S++,index:t,used:!0,key:i,type:n};return Object.defineProperty(o,"nr",{configurable:!1,value:s}),e.push(o),o},unuseView:function(e,t){void 0===t&&(t=!1);var r=this.$_unusedViews,i=e.nr.type,n=r.get(i);n||(n=[],r.set(i,n)),n.push(e),t||(e.nr.used=!1,e.position=-9999,this.$_views.delete(e.nr.key))},handleResize:function(){this.$emit("resize"),this.ready&&this.updateVisibleItems(!1)},handleScroll:function(e){var t=this;this.$_scrollDirty||(this.$_scrollDirty=!0,requestAnimationFrame(function(){t.$_scrollDirty=!1,t.updateVisibleItems(!1,!0).continuous||(clearTimeout(t.$_refreshTimout),t.$_refreshTimout=setTimeout(t.handleScroll,100))}))},handleVisibilityChange:function(e,t){var r=this;this.ready&&(e||0!==t.boundingClientRect.width||0!==t.boundingClientRect.height?(this.$emit("visible"),requestAnimationFrame(function(){r.updateVisibleItems(!1)})):this.$emit("hidden"))},updateVisibleItems:function(e,t){void 0===t&&(t=!1);var r,i,n,o,s,a,l=this.itemSize,c=this.gridItems||1,u=this.itemSecondarySize||l,d=this.$_computedMinItemSize,f=this.typeField,p=this.simpleArray?null:this.keyField,h=this.items,v=h.length,m=this.sizes,b=this.$_views,_=this.$_unusedViews,g=this.pool;if(v)if(this.$_prerender)r=o=0,i=s=Math.min(this.prerender,h.length),n=null;else{var w=this.getScroll();if(t){var S=w.start-this.$_lastUpdateScrollPosition;if(S<0&&(S=-S),null===l&&S<d||S<l)return{continuous:!0}}this.$_lastUpdateScrollPosition=w.start;var $=this.buffer;w.start-=$,w.end+=$;var z=0;if(this.$refs.before&&(z=this.$refs.before.scrollHeight,w.start-=z),this.$refs.after){var O=this.$refs.after.scrollHeight;w.end+=O}if(null===l){var j,x=0,T=v-1,A=~~(v/2);do{j=A,m[A].accumulator<w.start?x=A:A<v-1&&m[A+1].accumulator>w.start&&(T=A),A=~~((x+T)/2)}while(A!==j);for(A<0&&(A=0),r=A,n=m[v-1].accumulator,i=A;i<v&&m[i].accumulator<w.end;i++);for(-1===i?i=h.length-1:++i>v&&(i=v),o=r;o<v&&z+m[o].accumulator<w.start;o++);for(s=o;s<v&&z+m[s].accumulator<w.end;s++);}else{r=~~(w.start/l*c),(r-=r%c)<0&&(r=0),(i=Math.ceil(w.end/l*c))>v&&(i=v),(o=Math.max(0,Math.floor((w.start-z)/l*c)))<0&&(o=0),(s=Math.floor((w.end-z)/l*c))>v&&(s=v),n=Math.ceil(v/c)*l}}else r=i=o=s=n=0;i-r>y.itemsLimit&&this.itemsLimitError(),this.totalSize=n;var I=r<=this.$_endIndex&&i>=this.$_startIndex;if(this.$_continuous!==I){if(I){b.clear(),_.clear();for(var V=0,k=g.length;V<k;V++)a=g[V],this.unuseView(a)}this.$_continuous=I}else if(I)for(var R=0,C=g.length;R<C;R++)(a=g[R]).nr.used&&(e&&(a.nr.index=h.indexOf(a.item)),(-1===a.nr.index||a.nr.index<r||a.nr.index>=i)&&this.unuseView(a));for(var D,M,P,E,L=I?null:new Map,F=r;F<i;F++){D=h[F];var N=p?D[p]:D;if(null==N)throw new Error("Key is ".concat(N," on item (keyField is '").concat(p,"')"));a=b.get(N),l||m[F].size?(a?(a.nr.used=!0,a.item=D):(F===h.length-1&&this.$emit("scroll-end"),0===F&&this.$emit("scroll-start"),M=D[f],P=_.get(M),I?P&&P.length?((a=P.pop()).item=D,a.nr.used=!0,a.nr.index=F,a.nr.key=N,a.nr.type=M):a=this.addView(g,F,D,N,M):(E=L.get(M)||0,(!P||E>=P.length)&&(a=this.addView(g,F,D,N,M),this.unuseView(a,!0),P=_.get(M)),(a=P[E]).item=D,a.nr.used=!0,a.nr.index=F,a.nr.key=N,a.nr.type=M,L.set(M,E+1),E++),b.set(N,a)),null===l?(a.position=m[F-1].accumulator,a.offset=0):(a.position=Math.floor(F/c)*l,a.offset=F%c*u)):a&&this.unuseView(a)}return this.$_startIndex=r,this.$_endIndex=i,this.emitUpdate&&this.$emit("update",r,i,o,s),clearTimeout(this.$_sortTimer),this.$_sortTimer=setTimeout(this.sortViews,300),{continuous:I}},getListenerTarget:function(){var e=m(this.$el);return!window.document||e!==window.document.documentElement&&e!==window.document.body||(e=window),e},getScroll:function(){var e,t=this.$el,r="vertical"===this.direction;if(this.pageMode){var i=t.getBoundingClientRect(),n=r?i.height:i.width,o=-(r?i.top:i.left),s=r?window.innerHeight:window.innerWidth;o<0&&(s+=o,o=0),o+s>n&&(s=n-o),e={start:o,end:o+s}}else e=r?{start:t.scrollTop,end:t.scrollTop+t.clientHeight}:{start:t.scrollLeft,end:t.scrollLeft+t.clientWidth};return e},applyPageMode:function(){this.pageMode?this.addListeners():this.removeListeners()},addListeners:function(){this.listenerTarget=this.getListenerTarget(),this.listenerTarget.addEventListener("scroll",this.handleScroll,!!g&&{passive:!0}),this.listenerTarget.addEventListener("resize",this.handleResize)},removeListeners:function(){this.listenerTarget&&(this.listenerTarget.removeEventListener("scroll",this.handleScroll),this.listenerTarget.removeEventListener("resize",this.handleResize),this.listenerTarget=null)},scrollToItem:function(e){var t;t=null===this.itemSize?e>0?this.sizes[e-1].accumulator:0:Math.floor(e/this.gridItems)*this.itemSize,this.scrollToPosition(t)},scrollToPosition:function(e){var t,r,i,n="vertical"===this.direction?{scroll:"scrollTop",start:"top"}:{scroll:"scrollLeft",start:"left"};if(this.pageMode){var o=m(this.$el),s="HTML"===o.tagName?0:o[n.scroll],a=o.getBoundingClientRect(),l=this.$el.getBoundingClientRect()[n.start]-a[n.start];t=o,r=n.scroll,i=e+s+l}else t=this.$el,r=n.scroll,i=e;t[r]=i},itemsLimitError:function(){var e=this;throw setTimeout(function(){e.$el}),new Error("Rendered items limit reached")},sortViews:function(){this.pool.sort(function(e,t){return e.nr.index-t.nr.index})}}},j=function(){var e,t,r=this,i=r.$createElement,n=r._self._c||i;return n("div",{directives:[{name:"observe-visibility",rawName:"v-observe-visibility",value:r.handleVisibilityChange,expression:"handleVisibilityChange"}],staticClass:"vue-recycle-scroller",class:(e={ready:r.ready,"page-mode":r.pageMode},e["direction-"+r.direction]=!0,e),on:{"&scroll":function(e){return r.handleScroll.apply(null,arguments)}}},[r.$slots.before?n("div",{ref:"before",staticClass:"vue-recycle-scroller__slot"},[r._t("before")],2):r._e(),r._v(" "),n(r.listTag,{ref:"wrapper",tag:"component",staticClass:"vue-recycle-scroller__item-wrapper",class:r.listClass,style:(t={},t["vertical"===r.direction?"minHeight":"minWidth"]=r.totalSize+"px",t)},[r._l(r.pool,function(e){return n(r.itemTag,r._g({key:e.nr.id,tag:"component",staticClass:"vue-recycle-scroller__item-view",class:[r.itemClass,{hover:!r.skipHover&&r.hoverKey===e.nr.key}],style:r.ready?{transform:"translate"+("vertical"===r.direction?"Y":"X")+"("+e.position+"px) translate"+("vertical"===r.direction?"X":"Y")+"("+e.offset+"px)",width:r.gridItems?("vertical"===r.direction&&r.itemSecondarySize||r.itemSize)+"px":void 0,height:r.gridItems?("horizontal"===r.direction&&r.itemSecondarySize||r.itemSize)+"px":void 0}:null},r.skipHover?{}:{mouseenter:function(){r.hoverKey=e.nr.key},mouseleave:function(){r.hoverKey=null}}),[r._t("default",null,{item:e.item,index:e.nr.index,active:e.nr.used})],2)}),r._v(" "),r._t("empty")],2),r._v(" "),r.$slots.after?n("div",{ref:"after",staticClass:"vue-recycle-scroller__slot"},[r._t("after")],2):r._e(),r._v(" "),n("ResizeObserver",{on:{notify:r.handleResize}})],1)};j._withStripped=!0;var x=z({render:j,staticRenderFns:[]},void 0,O,void 0,!1,void 0,!1,void 0,void 0,void 0),T={name:"DynamicScroller",components:{RecycleScroller:x},provide:function(){return"undefined"!=typeof ResizeObserver&&(this.$_resizeObserver=new ResizeObserver(function(e){requestAnimationFrame(function(){if(Array.isArray(e))for(var t,r=_createForOfIteratorHelperLoose(e);!(t=r()).done;){var i=t.value;if(i.target){var n=new CustomEvent("resize",{detail:{contentRect:i.contentRect}});i.target.dispatchEvent(n)}}})})),{vscrollData:this.vscrollData,vscrollParent:this,vscrollResizeObserver:this.$_resizeObserver}},inheritAttrs:!1,props:_extends({},b,{minItemSize:{type:[Number,String],required:!0}}),data:function(){return{vscrollData:{active:!0,sizes:{},validSizes:{},keyField:this.keyField,simpleArray:!1}}},computed:{simpleArray:_,itemsWithSize:function(){for(var e=[],t=this.items,r=this.keyField,i=this.simpleArray,n=this.vscrollData.sizes,o=t.length,s=0;s<o;s++){var a=t[s],l=i?s:a[r],c=n[l];void 0!==c||this.$_undefinedMap[l]||(c=0),e.push({item:a,id:l,size:c})}return e},listeners:function(){var e={};for(var t in this.$listeners)"resize"!==t&&"visible"!==t&&(e[t]=this.$listeners[t]);return e}},watch:{items:function(){this.forceUpdate(!1)},simpleArray:{handler:function(e){this.vscrollData.simpleArray=e},immediate:!0},direction:function(e){this.forceUpdate(!0)},itemsWithSize:function(e,t){for(var r=this.$el.scrollTop,i=0,n=0,o=Math.min(e.length,t.length),s=0;s<o&&!(i>=r);s++)i+=t[s].size||this.minItemSize,n+=e[s].size||this.minItemSize;var a=n-i;0!==a&&(this.$el.scrollTop+=a)}},beforeCreate:function(){this.$_updates=[],this.$_undefinedSizes=0,this.$_undefinedMap={}},activated:function(){this.vscrollData.active=!0},deactivated:function(){this.vscrollData.active=!1},methods:{onScrollerResize:function(){this.$refs.scroller&&this.forceUpdate(),this.$emit("resize")},onScrollerVisible:function(){this.$emit("vscroll:update",{force:!1}),this.$emit("visible")},forceUpdate:function(e){void 0===e&&(e=!0),(e||this.simpleArray)&&(this.vscrollData.validSizes={}),this.$emit("vscroll:update",{force:!0})},scrollToItem:function(e){var t=this.$refs.scroller;t&&t.scrollToItem(e)},getItemSize:function(e,t){void 0===t&&(t=void 0);var r=this.simpleArray?null!=t?t:this.items.indexOf(e):e[this.keyField];return this.vscrollData.sizes[r]||0},scrollToBottom:function(){var e=this;if(!this.$_scrollingToBottom){this.$_scrollingToBottom=!0;var t=this.$el;this.$nextTick(function(){t.scrollTop=t.scrollHeight+5e3;requestAnimationFrame(function r(){t.scrollTop=t.scrollHeight+5e3,requestAnimationFrame(function(){t.scrollTop=t.scrollHeight+5e3,0===e.$_undefinedSizes?e.$_scrollingToBottom=!1:requestAnimationFrame(r)})})})}}}},A=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("RecycleScroller",e._g(e._b({ref:"scroller",attrs:{items:e.itemsWithSize,"min-item-size":e.minItemSize,direction:e.direction,"key-field":"id","list-tag":e.listTag,"item-tag":e.itemTag},on:{resize:e.onScrollerResize,visible:e.onScrollerVisible},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.item,i=t.index,n=t.active;return[e._t("default",null,null,{item:r.item,index:i,active:n,itemWithSize:r})]}}],null,!0)},"RecycleScroller",e.$attrs,!1),e.listeners),[e._v(" "),r("template",{slot:"before"},[e._t("before")],2),e._v(" "),r("template",{slot:"after"},[e._t("after")],2),e._v(" "),r("template",{slot:"empty"},[e._t("empty")],2)],2)};A._withStripped=!0;var I=z({render:A,staticRenderFns:[]},void 0,T,void 0,!1,void 0,!1,void 0,void 0,void 0),V=z({},void 0,{name:"DynamicScrollerItem",inject:["vscrollData","vscrollParent","vscrollResizeObserver"],props:{item:{required:!0},watchData:{type:Boolean,default:!1},active:{type:Boolean,required:!0},index:{type:Number,default:void 0},sizeDependencies:{type:[Array,Object],default:null},emitResize:{type:Boolean,default:!1},tag:{type:String,default:"div"}},computed:{id:function(){if(this.vscrollData.simpleArray)return this.index;if(this.item.hasOwnProperty(this.vscrollData.keyField))return this.item[this.vscrollData.keyField];throw new Error("keyField '".concat(this.vscrollData.keyField,"' not found in your item. You should set a valid keyField prop on your Scroller"))},size:function(){return this.vscrollData.validSizes[this.id]&&this.vscrollData.sizes[this.id]||0},finalActive:function(){return this.active&&this.vscrollData.active}},watch:{watchData:"updateWatchData",id:function(){this.size||this.onDataUpdate()},finalActive:function(e){this.size||(e?this.vscrollParent.$_undefinedMap[this.id]||(this.vscrollParent.$_undefinedSizes++,this.vscrollParent.$_undefinedMap[this.id]=!0):this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=!1)),this.vscrollResizeObserver?e?this.observeSize():this.unobserveSize():e&&this.$_pendingVScrollUpdate===this.id&&this.updateSize()}},created:function(){var e=this;if(!this.$isServer&&(this.$_forceNextVScrollUpdate=null,this.updateWatchData(),!this.vscrollResizeObserver)){var t=function(t){e.$watch(function(){return e.sizeDependencies[t]},e.onDataUpdate)};for(var r in this.sizeDependencies)t(r);this.vscrollParent.$on("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$on("vscroll:update-size",this.onVscrollUpdateSize)}},mounted:function(){this.vscrollData.active&&(this.updateSize(),this.observeSize())},beforeDestroy:function(){this.vscrollParent.$off("vscroll:update",this.onVscrollUpdate),this.vscrollParent.$off("vscroll:update-size",this.onVscrollUpdateSize),this.unobserveSize()},methods:{updateSize:function(){this.finalActive?this.$_pendingSizeUpdate!==this.id&&(this.$_pendingSizeUpdate=this.id,this.$_forceNextVScrollUpdate=null,this.$_pendingVScrollUpdate=null,this.computeSize(this.id)):this.$_forceNextVScrollUpdate=this.id},updateWatchData:function(){var e=this;this.watchData&&!this.vscrollResizeObserver?this.$_watchData=this.$watch("item",function(){e.onDataUpdate()},{deep:!0}):this.$_watchData&&(this.$_watchData(),this.$_watchData=null)},onVscrollUpdate:function(e){var t=e.force;!this.finalActive&&t&&(this.$_pendingVScrollUpdate=this.id),this.$_forceNextVScrollUpdate!==this.id&&!t&&this.size||this.updateSize()},onDataUpdate:function(){this.updateSize()},computeSize:function(e){var t=this;this.$nextTick(function(){if(t.id===e){var r=t.$el.offsetWidth,i=t.$el.offsetHeight;t.applySize(r,i)}t.$_pendingSizeUpdate=null})},applySize:function(e,t){var r=~~("vertical"===this.vscrollParent.direction?t:e);r&&this.size!==r&&(this.vscrollParent.$_undefinedMap[this.id]&&(this.vscrollParent.$_undefinedSizes--,this.vscrollParent.$_undefinedMap[this.id]=void 0),this.$set(this.vscrollData.sizes,this.id,r),this.$set(this.vscrollData.validSizes,this.id,!0),this.emitResize&&this.$emit("resize",this.id))},observeSize:function(){this.vscrollResizeObserver&&this.$el.parentNode&&(this.vscrollResizeObserver.observe(this.$el.parentNode),this.$el.parentNode.addEventListener("resize",this.onResize))},unobserveSize:function(){this.vscrollResizeObserver&&(this.vscrollResizeObserver.unobserve(this.$el.parentNode),this.$el.parentNode.removeEventListener("resize",this.onResize))},onResize:function(e){var t=e.detail.contentRect,r=t.width,i=t.height;this.applySize(r,i)}},render:function(e){return e(this.tag,this.$slots.default)}},void 0,void 0,void 0,!1,void 0,void 0,void 0);var k={version:"1.1.2",install:function(e,t){var r=Object.assign({},{installComponents:!0,componentsPrefix:""},t);for(var i in r)void 0!==r[i]&&(y[i]=r[i]);r.installComponents&&function(e,t){e.component("".concat(t,"recycle-scroller"),x),e.component("".concat(t,"RecycleScroller"),x),e.component("".concat(t,"dynamic-scroller"),I),e.component("".concat(t,"DynamicScroller"),I),e.component("".concat(t,"dynamic-scroller-item"),V),e.component("".concat(t,"DynamicScrollerItem"),V)}(e,r.componentsPrefix)}},R=null;"undefined"!=typeof window?R=window.Vue:"undefined"!=typeof global&&(R=global.Vue),R&&R.use(k);var C=function(){this.__data__=[],this.size=0};var D=function(e,t){return e===t||e!=e&&t!=t};var M=function(e,t){for(var r=e.length;r--;)if(D(e[r][0],t))return r;return-1},P=Array.prototype.splice;var E=function(e){var t=this.__data__,r=M(t,e);return!(r<0||(r==t.length-1?t.pop():P.call(t,r,1),--this.size,0))};var L=function(e){var t=this.__data__,r=M(t,e);return r<0?void 0:t[r][1]};var F=function(e){return M(this.__data__,e)>-1};var N=function(e,t){var r=this.__data__,i=M(r,e);return i<0?(++this.size,r.push([e,t])):r[i][1]=t,this};function U(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}U.prototype.clear=C,U.prototype.delete=E,U.prototype.get=L,U.prototype.has=F,U.prototype.set=N;var B=U;var W=function(){this.__data__=new B,this.size=0};var H=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r};var K=function(e){return this.__data__.get(e)};var q=function(e){return this.__data__.has(e)},G="object"==typeof h&&h&&h.Object===Object&&h,X="object"==typeof self&&self&&self.Object===Object&&self,Y=G||X||Function("return this")(),J=Y.Symbol,Q=Object.prototype,Z=Q.hasOwnProperty,ee=Q.toString,te=J?J.toStringTag:void 0;var re=function(e){var t=Z.call(e,te),r=e[te];try{e[te]=void 0;var i=!0}catch(e){}var n=ee.call(e);return i&&(t?e[te]=r:delete e[te]),n},ie=Object.prototype.toString;var ne=function(e){return ie.call(e)},oe="[object Null]",se="[object Undefined]",ae=J?J.toStringTag:void 0;var le=function(e){return null==e?void 0===e?se:oe:ae&&ae in Object(e)?re(e):ne(e)};var ce=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},ue="[object AsyncFunction]",de="[object Function]",fe="[object GeneratorFunction]",pe="[object Proxy]";var he=function(e){if(!ce(e))return!1;var t=le(e);return t==de||t==fe||t==ue||t==pe},ve=Y["__core-js_shared__"],me=function(){var e=/[^.]+$/.exec(ve&&ve.keys&&ve.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var ye=function(e){return!!me&&me in e},be=Function.prototype.toString;var _e=function(e){if(null!=e){try{return be.call(e)}catch(e){}try{return e+""}catch(e){}}return""},ge=/^\[object .+?Constructor\]$/,we=Function.prototype,Se=Object.prototype,$e=we.toString,ze=Se.hasOwnProperty,Oe=RegExp("^"+$e.call(ze).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var je=function(e){return!(!ce(e)||ye(e))&&(he(e)?Oe:ge).test(_e(e))};var xe=function(e,t){return null==e?void 0:e[t]};var Te=function(e,t){var r=xe(e,t);return je(r)?r:void 0},Ae=Te(Y,"Map"),Ie=Te(Object,"create");var Ve=function(){this.__data__=Ie?Ie(null):{},this.size=0};var ke=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Re="__lodash_hash_undefined__",Ce=Object.prototype.hasOwnProperty;var De=function(e){var t=this.__data__;if(Ie){var r=t[e];return r===Re?void 0:r}return Ce.call(t,e)?t[e]:void 0},Me=Object.prototype.hasOwnProperty;var Pe=function(e){var t=this.__data__;return Ie?void 0!==t[e]:Me.call(t,e)},Ee="__lodash_hash_undefined__";var Le=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Ie&&void 0===t?Ee:t,this};function Fe(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}Fe.prototype.clear=Ve,Fe.prototype.delete=ke,Fe.prototype.get=De,Fe.prototype.has=Pe,Fe.prototype.set=Le;var Ne=Fe;var Ue=function(){this.size=0,this.__data__={hash:new Ne,map:new(Ae||B),string:new Ne}};var Be=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var We=function(e,t){var r=e.__data__;return Be(t)?r["string"==typeof t?"string":"hash"]:r.map};var He=function(e){var t=We(this,e).delete(e);return this.size-=t?1:0,t};var Ke=function(e){return We(this,e).get(e)};var qe=function(e){return We(this,e).has(e)};var Ge=function(e,t){var r=We(this,e),i=r.size;return r.set(e,t),this.size+=r.size==i?0:1,this};function Xe(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var i=e[t];this.set(i[0],i[1])}}Xe.prototype.clear=Ue,Xe.prototype.delete=He,Xe.prototype.get=Ke,Xe.prototype.has=qe,Xe.prototype.set=Ge;var Ye=Xe,Je=200;var Qe=function(e,t){var r=this.__data__;if(r instanceof B){var i=r.__data__;if(!Ae||i.length<Je-1)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new Ye(i)}return r.set(e,t),this.size=r.size,this};function Ze(e){var t=this.__data__=new B(e);this.size=t.size}Ze.prototype.clear=W,Ze.prototype.delete=H,Ze.prototype.get=K,Ze.prototype.has=q,Ze.prototype.set=Qe;var et=Ze,tt="__lodash_hash_undefined__";var rt=function(e){return this.__data__.set(e,tt),this};var it=function(e){return this.__data__.has(e)};function nt(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new Ye;++t<r;)this.add(e[t])}nt.prototype.add=nt.prototype.push=rt,nt.prototype.has=it;var ot=nt;var st=function(e,t){for(var r=-1,i=null==e?0:e.length;++r<i;)if(t(e[r],r,e))return!0;return!1};var at=function(e,t){return e.has(t)},lt=1,ct=2;var ut=function(e,t,r,i,n,o){var s=r&lt,a=e.length,l=t.length;if(a!=l&&!(s&&l>a))return!1;var c=o.get(e),u=o.get(t);if(c&&u)return c==t&&u==e;var d=-1,f=!0,p=r&ct?new ot:void 0;for(o.set(e,t),o.set(t,e);++d<a;){var h=e[d],v=t[d];if(i)var m=s?i(v,h,d,t,e,o):i(h,v,d,e,t,o);if(void 0!==m){if(m)continue;f=!1;break}if(p){if(!st(t,function(e,t){if(!at(p,t)&&(h===e||n(h,e,r,i,o)))return p.push(t)})){f=!1;break}}else if(h!==v&&!n(h,v,r,i,o)){f=!1;break}}return o.delete(e),o.delete(t),f},dt=Y.Uint8Array;var ft=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e,i){r[++t]=[i,e]}),r};var pt=function(e){var t=-1,r=Array(e.size);return e.forEach(function(e){r[++t]=e}),r},ht=1,vt=2,mt="[object Boolean]",yt="[object Date]",bt="[object Error]",_t="[object Map]",gt="[object Number]",wt="[object RegExp]",St="[object Set]",$t="[object String]",zt="[object Symbol]",Ot="[object ArrayBuffer]",jt="[object DataView]",xt=J?J.prototype:void 0,Tt=xt?xt.valueOf:void 0;var At=function(e,t,r,i,n,o,s){switch(r){case jt:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Ot:return!(e.byteLength!=t.byteLength||!o(new dt(e),new dt(t)));case mt:case yt:case gt:return D(+e,+t);case bt:return e.name==t.name&&e.message==t.message;case wt:case $t:return e==t+"";case _t:var a=ft;case St:var l=i&ht;if(a||(a=pt),e.size!=t.size&&!l)return!1;var c=s.get(e);if(c)return c==t;i|=vt,s.set(e,t);var u=ut(a(e),a(t),i,n,o,s);return s.delete(e),u;case zt:if(Tt)return Tt.call(e)==Tt.call(t)}return!1};var It=function(e,t){for(var r=-1,i=t.length,n=e.length;++r<i;)e[n+r]=t[r];return e},Vt=Array.isArray;var kt=function(e,t,r){var i=t(e);return Vt(e)?i:It(i,r(e))};var Rt=function(e,t){for(var r=-1,i=null==e?0:e.length,n=0,o=[];++r<i;){var s=e[r];t(s,r,e)&&(o[n++]=s)}return o};var Ct=function(){return[]},Dt=Object.prototype.propertyIsEnumerable,Mt=Object.getOwnPropertySymbols,Pt=Mt?function(e){return null==e?[]:(e=Object(e),Rt(Mt(e),function(t){return Dt.call(e,t)}))}:Ct;var Et=function(e,t){for(var r=-1,i=Array(e);++r<e;)i[r]=t(r);return i};var Lt=function(e){return null!=e&&"object"==typeof e},Ft="[object Arguments]";var Nt=function(e){return Lt(e)&&le(e)==Ft},Ut=Object.prototype,Bt=Ut.hasOwnProperty,Wt=Ut.propertyIsEnumerable,Ht=Nt(function(){return arguments}())?Nt:function(e){return Lt(e)&&Bt.call(e,"callee")&&!Wt.call(e,"callee")};var Kt=function(){return!1},qt=v(function(e,t){var r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,n=i&&i.exports===r?Y.Buffer:void 0,o=(n?n.isBuffer:void 0)||Kt;e.exports=o}),Gt=9007199254740991,Xt=/^(?:0|[1-9]\d*)$/;var Yt=function(e,t){var r=typeof e;return!!(t=null==t?Gt:t)&&("number"==r||"symbol"!=r&&Xt.test(e))&&e>-1&&e%1==0&&e<t},Jt=9007199254740991;var Qt=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=Jt},Zt={};Zt["[object Float32Array]"]=Zt["[object Float64Array]"]=Zt["[object Int8Array]"]=Zt["[object Int16Array]"]=Zt["[object Int32Array]"]=Zt["[object Uint8Array]"]=Zt["[object Uint8ClampedArray]"]=Zt["[object Uint16Array]"]=Zt["[object Uint32Array]"]=!0,Zt["[object Arguments]"]=Zt["[object Array]"]=Zt["[object ArrayBuffer]"]=Zt["[object Boolean]"]=Zt["[object DataView]"]=Zt["[object Date]"]=Zt["[object Error]"]=Zt["[object Function]"]=Zt["[object Map]"]=Zt["[object Number]"]=Zt["[object Object]"]=Zt["[object RegExp]"]=Zt["[object Set]"]=Zt["[object String]"]=Zt["[object WeakMap]"]=!1;var er=function(e){return Lt(e)&&Qt(e.length)&&!!Zt[le(e)]};var tr=function(e){return function(t){return e(t)}},rr=v(function(e,t){var r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,n=i&&i.exports===r&&G.process,o=function(){try{var e=i&&i.require&&i.require("util").types;return e||n&&n.binding&&n.binding("util")}catch(e){}}();e.exports=o}),ir=rr&&rr.isTypedArray,nr=ir?tr(ir):er,or=Object.prototype.hasOwnProperty;var sr=function(e,t){var r=Vt(e),i=!r&&Ht(e),n=!r&&!i&&qt(e),o=!r&&!i&&!n&&nr(e),s=r||i||n||o,a=s?Et(e.length,String):[],l=a.length;for(var c in e)!t&&!or.call(e,c)||s&&("length"==c||n&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Yt(c,l))||a.push(c);return a},ar=Object.prototype;var lr=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||ar)};var cr=function(e,t){return function(r){return e(t(r))}}(Object.keys,Object),ur=Object.prototype.hasOwnProperty;var dr=function(e){if(!lr(e))return cr(e);var t=[];for(var r in Object(e))ur.call(e,r)&&"constructor"!=r&&t.push(r);return t};var fr=function(e){return null!=e&&Qt(e.length)&&!he(e)};var pr=function(e){return fr(e)?sr(e):dr(e)};var hr=function(e){return kt(e,pr,Pt)},vr=1,mr=Object.prototype.hasOwnProperty;var yr=function(e,t,r,i,n,o){var s=r&vr,a=hr(e),l=a.length;if(l!=hr(t).length&&!s)return!1;for(var c=l;c--;){var u=a[c];if(!(s?u in t:mr.call(t,u)))return!1}var d=o.get(e),f=o.get(t);if(d&&f)return d==t&&f==e;var p=!0;o.set(e,t),o.set(t,e);for(var h=s;++c<l;){var v=e[u=a[c]],m=t[u];if(i)var y=s?i(m,v,u,t,e,o):i(v,m,u,e,t,o);if(!(void 0===y?v===m||n(v,m,r,i,o):y)){p=!1;break}h||(h="constructor"==u)}if(p&&!h){var b=e.constructor,_=t.constructor;b!=_&&"constructor"in e&&"constructor"in t&&!("function"==typeof b&&b instanceof b&&"function"==typeof _&&_ instanceof _)&&(p=!1)}return o.delete(e),o.delete(t),p},br=Te(Y,"DataView"),_r=Te(Y,"Promise"),gr=Te(Y,"Set"),wr=Te(Y,"WeakMap"),Sr=_e(br),$r=_e(Ae),zr=_e(_r),Or=_e(gr),jr=_e(wr),xr=le;(br&&"[object DataView]"!=xr(new br(new ArrayBuffer(1)))||Ae&&"[object Map]"!=xr(new Ae)||_r&&"[object Promise]"!=xr(_r.resolve())||gr&&"[object Set]"!=xr(new gr)||wr&&"[object WeakMap]"!=xr(new wr))&&(xr=function(e){var t=le(e),r="[object Object]"==t?e.constructor:void 0,i=r?_e(r):"";if(i)switch(i){case Sr:return"[object DataView]";case $r:return"[object Map]";case zr:return"[object Promise]";case Or:return"[object Set]";case jr:return"[object WeakMap]"}return t});var Tr=xr,Ar=1,Ir="[object Arguments]",Vr="[object Array]",kr="[object Object]",Rr=Object.prototype.hasOwnProperty;var Cr=function(e,t,r,i,n,o){var s=Vt(e),a=Vt(t),l=s?Vr:Tr(e),c=a?Vr:Tr(t),u=(l=l==Ir?kr:l)==kr,d=(c=c==Ir?kr:c)==kr,f=l==c;if(f&&qt(e)){if(!qt(t))return!1;s=!0,u=!1}if(f&&!u)return o||(o=new et),s||nr(e)?ut(e,t,r,i,n,o):At(e,t,l,r,i,n,o);if(!(r&Ar)){var p=u&&Rr.call(e,"__wrapped__"),h=d&&Rr.call(t,"__wrapped__");if(p||h){var v=p?e.value():e,m=h?t.value():t;return o||(o=new et),n(v,m,r,i,o)}}return!!f&&(o||(o=new et),yr(e,t,r,i,n,o))};var Dr=function e(t,r,i,n,o){return t===r||(null==t||null==r||!Lt(t)&&!Lt(r)?t!=t&&r!=r:Cr(t,r,i,n,e,o))};for(var Mr,Pr=function(e,t){return Dr(e,t)},Er=256,Lr=[];Er--;)Lr[Er]=(Er+256).toString(16).substring(1);function Fr(){var e,t=0,r="";if(!Mr||Er+16>256){for(Mr=Array(t=256);t--;)Mr[t]=256*Math.random()|0;t=Er=0}for(;t<16;t++)e=Mr[Er+t],r+=6==t?Lr[15&e|64]:8==t?Lr[63&e|128]:Lr[e],1&t&&t>1&&t<11&&(r+="-");return Er++,r}var Nr=Object.defineProperty,Ur=Object.defineProperties,Br=Object.getOwnPropertyDescriptors,Wr=Object.getOwnPropertySymbols,Hr=Object.prototype.hasOwnProperty,Kr=Object.prototype.propertyIsEnumerable,qr=function(e,t,r){return t in e?Nr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},Gr=function(e,t){for(var r in t||(t={}))Hr.call(t,r)&&qr(e,r,t[r]);if(Wr)for(var i,n=_createForOfIteratorHelperLoose(Wr(t));!(i=n()).done;){r=i.value;Kr.call(t,r)&&qr(e,r,t[r])}return e},Xr=function(e,t){return Ur(e,Br(t))},Yr={name:"ElSelectV2",components:{RecycleScroller:x},props:{value:{type:[Array,String,Number,Boolean,Object],default:void 0},options:{type:Array,default:function(){return[]}},valueKey:{type:String,default:"value"},labelKey:{type:String,default:"label"},autocomplete:{type:String,default:"off"},automaticDropdown:Boolean,size:String,disabled:Boolean,clearable:Boolean,filterable:Boolean,allowCreate:Boolean,loading:Boolean,popperClass:String,remote:Boolean,loadingText:String,noMatchText:String,noDataText:String,remoteMethod:Function,filterMethod:Function,multiple:Boolean,multipleLimit:{type:Number,default:0},placeholder:{type:String,required:!1},defaultFirstOption:Boolean,reserveKeyword:{type:Boolean,default:!0},collapseTags:Boolean,popperAppendToBody:{type:Boolean,default:!0},minItemSize:{type:Number,default:34},fitInputWidth:{type:Boolean,default:!0}},data:function(){return{localValue:"",localOptions:[],dropdownWidth:""}},mounted:function(){var e=this;this.updateSelectedLabel(),this.$refs.select&&this.$watch(function(){return e.$refs.select.visible},function(t){t&&e.updateOptions()})},methods:{updateSelectedLabel:function(){var e=this;if(this.$refs.select){var t=this.$refs.select,r=t.setSelected,i=t.cachedOptions,n=this.multiple?this.localValue:[this.localValue];this.flattedOptions.filter(function(t){return null==n?void 0:n.includes(t[e.valueKey])}).map(function(t){return{value:t[e.valueKey],currentLabel:t[e.labelKey]}}).forEach(function(e){var t=i.find(function(t){return t.value===e.value});t?i.splice(i.indexOf(t),1,e):i.push(e)}),r()}},handleScrollerVisible:function(){var e,t=this,r=this.multiple?null==(e=this.localValue)?void 0:e[0]:this.localValue,i=this.localOptions.findIndex(function(e){return e[t.valueKey]===r});this.$refs.scroller.scrollToItem(i)},localFilterMethod:function(e){var t=this,r=this.flattedOptions.filter(function(r){var i;return!r._isGroup&&(null==(i=r[t.labelKey])?void 0:i.toLowerCase().includes(e.toLowerCase()))}).map(function(e){return e._groupName});this.localOptions=this.flattedOptions.filter(function(i){var n;return i._isGroup?r.some(function(e){return i._groupName===e}):null==(n=i[t.labelKey])?void 0:n.toLowerCase().includes(e.toLowerCase())})},updateOptions:function(){this.localOptions=this.flattedOptions},updateDropdownWidth:function(){return e=this,t=null,r=function*(){var e,t=this;if((null==(e=this.$refs.select)?void 0:e.$refs.popper)&&!this.fitInputWidth){var r=this.$refs.select.inputWidth,i=document.createElement("canvas").getContext("2d");yield this.$nextTick();var n=this.$refs.select.$refs.popper.$el.querySelector(".el-select-dropdown__item");if(n){var o=getComputedStyle(n),s=parseFloat(o.paddingLeft)+parseFloat(o.paddingRight);i.font="bold ".concat(o.font);var a=0;this.localOptions.forEach(function(e){var r=i.measureText(e[t.labelKey]);a=Math.max(r.width,a)}),this.dropdownWidth=Math.max(a+s+6,r-2)}}},new Promise(function(i,n){var o=function(e){try{a(r.next(e))}catch(e){n(e)}},s=function(e){try{a(r.throw(e))}catch(e){n(e)}},a=function(e){return e.done?i(e.value):Promise.resolve(e.value).then(o,s)};a((r=r.apply(e,t)).next())});var e,t,r},focus:function(){this.$refs.select.focus()},blur:function(){this.$refs.select.blur()}},computed:{flattedOptions:function(){var e=this;if(!Array.isArray(this.options))return[];var t=[];return this.options.forEach(function(r){var i=Fr();Array.isArray(r.options)?(t.push(Xr(Gr({},r),_defineProperty({_isGroup:!0,_groupName:i},e.valueKey,Fr()))),t.push.apply(t,r.options.map(function(e){return Xr(Gr({},e),{_groupName:i})}))):t.push(r)}),t},scrollerStyle:function(){return{width:this.dropdownWidth?"".concat(this.dropdownWidth,"px"):""}}},watch:{value:{handler:function(){Pr(this.value,this.localValue)||(this.localValue=this.value,this.updateSelectedLabel())},deep:!0,immediate:!0},options:{handler:function(){this.updateOptions();var e=this.$el.querySelectorAll("input");-1===[].indexOf.call(e,document.activeElement)&&this.updateSelectedLabel()},deep:!0},localOptions:function(){this.updateDropdownWidth()}}};var Jr,Qr=function(e,t,r,i,n,o,s,a,l,c){"boolean"!=typeof s&&(l=a,a=s,s=!1);var u,d="function"==typeof r?r.options:r;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,n&&(d.functional=!0)),i&&(d._scopeId=i),o?(u=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},d._ssrRegister=u):t&&(u=s?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,a(e))}),u)if(d.functional){var f=d.render;d.render=function(e,t){return u.call(t),f(e,t)}}else{var p=d.beforeCreate;d.beforeCreate=p?[].concat(p,u):[u]}return r},Zr="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());var ei={};var ti=Qr({render:function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-select",e._g(e._b({ref:"select",staticClass:"el-select-v2",attrs:{autocomplete:e.autocomplete,"automatic-dropdown":e.automaticDropdown,size:e.size,disabled:e.disabled,clearable:e.clearable,filterable:e.filterable,"allow-create":e.allowCreate,loading:e.loading,"popper-class":"el-select-v2__popper "+(e.popperClass||""),remote:e.remote,"loading-text":e.loadingText,"no-match-text":e.noMatchText,"no-data-text":e.noDataText,"remote-method":e.remoteMethod,"filter-method":e.filterMethod||e.localFilterMethod,multiple:e.multiple,"multiple-limit":e.multipleLimit,placeholder:e.placeholder,"default-first-option":e.defaultFirstOption,"reserve-keyword":e.reserveKeyword,"collapse-tags":e.collapseTags,"popper-append-to-body":e.popperAppendToBody},model:{value:e.localValue,callback:function(t){e.localValue=t},expression:"localValue"}},"el-select",e.$attrs,!1),e.$listeners),[e.$slots.header?r("div",{staticClass:"el-select-dropdown__header"},[e._t("header")],2):e._e(),e._v(" "),e.localOptions.length?r("RecycleScroller",{ref:"scroller",staticClass:"scroller",style:e.scrollerStyle,attrs:{items:e.localOptions,"min-item-size":e.minItemSize,"key-field":e.valueKey},on:{visible:e.handleScrollerVisible},scopedSlots:e._u([{key:"default",fn:function(t){var i=t.item;return[i._isGroup?r("li",{staticClass:"el-select-group__title"},[e._v(e._s(i[e.labelKey]))]):r("el-option",{key:i[e.valueKey],attrs:{value:i[e.valueKey],label:i[e.labelKey],disabled:i.disabled}},[e._t("default",null,{item:i})],2)]}}],null,!1,2983873336)}):e._e(),e._v(" "),e.$slots.footer?r("div",{staticClass:"el-select-dropdown__footer"},[e._t("footer")],2):e._e(),e._v(" "),e.$slots.prefix?r("template",{slot:"prefix"},[e._t("prefix")],2):e._e(),e._v(" "),e.$slots.empty?r("template",{slot:"empty"},[e._t("empty")],2):e._e()],2)},staticRenderFns:[]},function(e){e&&e("data-v-1f6ab7bb_0",{source:".el-select-dropdown__wrap{max-width:350px}.scroller{height:450px}.el-select-v2__popper .el-select-dropdown__wrap{max-height:unset}.el-select-v2__popper .el-select-dropdown__wrap .el-select-dropdown__list{padding:0}.el-select-v2__popper .el-select-dropdown__wrap .el-select-dropdown__list .el-select-dropdown__header{padding:10px;border-bottom:1px solid #e4e7ed}.el-select-v2__popper .el-select-dropdown__wrap .el-select-dropdown__list .el-select-dropdown__footer{padding:10px;border-top:1px solid #e4e7ed}.el-select-v2__popper .scroller{padding:6px 0;max-height:238px}.el-select-v2__popper .scroller::-webkit-scrollbar{width:6px;height:6px;background-color:transparent}.el-select-v2__popper .scroller::-webkit-scrollbar-thumb{border-radius:4px;background-color:rgba(144,147,153,.3)}.el-select-v2__popper .scroller::-webkit-scrollbar-thumb:hover{background-color:rgba(144,147,153,.5)}.el-select-v2__popper .el-scrollbar__bar{display:none}",map:void 0,media:void 0})},Yr,void 0,!1,void 0,!1,function(e){return function(e,t){return function(e,t){var r=Zr?t.media||"default":e,i=ei[r]||(ei[r]={ids:new Set,styles:[]});if(!i.ids.has(e)){i.ids.add(e);var n=t.source;if(t.map&&(n+="\n/*# sourceURL="+t.map.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t.map))))+" */"),i.element||(i.element=document.createElement("style"),i.element.type="text/css",t.media&&i.element.setAttribute("media",t.media),void 0===Jr&&(Jr=document.head||document.getElementsByTagName("head")[0]),Jr.appendChild(i.element)),"styleSheet"in i.element)i.styles.push(n),i.element.styleSheet.cssText=i.styles.filter(Boolean).join("\n");else{var o=i.ids.size-1,s=document.createTextNode(n),a=i.element.childNodes;a[o]&&i.element.removeChild(a[o]),a.length?i.element.insertBefore(s,a[o]):i.element.appendChild(s)}}}(e,t)}},void 0,void 0);Vue.component("el-virtual-select",ti),window.qqq=new Vue({el:"#app",data:function(){return{rules:{model:[{required:!0,message:"请选择活动样板",trigger:"change"}]},form:[{scene:"10",title:"热门模板",num:{min:6,max:50}},{scene:"11",title:"活动专题一",name:""},{scene:"12",title:"活动专题二",name:""},{scene:"13",title:"活动专题三",name:""},{scene:"14",title:"美团专属热门模板",name:"",num:{min:6,max:50}}],activeIndex:"10",options:[]}},created:function(){this.initData(),this.getModelListByCond(),this.handleSelect(this.activeIndex)},methods:{initData:function(e){var t=this;this.form.forEach(function(e){t.$set(e,"total",6),t.$set(e,"models",new Array(6).fill(""))})},submitForm:function(e){var t=this;this.$refs.form[e].validate(function(r){if(!r)return t.$message({message:"请填写完整活动样板",type:"error",duration:1500}),!1;for(var i=t.form[e],n=i.name,o=[],s=0;s<i.total;s++)o.push(i.models[s]);var a={displayType:4,category:t.activeIndex,modelList:JSON.stringify(o)};n&&(a.name=n),$.ajax({type:"post",url:"/api/caseMode/setCategoryDisplay",data:a,dataType:"json"}).then(function(e){var r=e.success,i=e.msg;r?t.$message({message:"保存成功",type:"success",duration:1500}):t.$message({message:i,type:"error",duration:1500})})})},handleSelect:function(e){this.activeIndex=e;var t=this.form.find(function(t){return t.scene===e});t.models.findIndex(function(e){return e})>=0||$.ajax({type:"post",url:"/api/caseMode/getCategoryDisplay",data:{displayType:4,category:e},dataType:"json"}).then(function(e){var r=e.data;e.success&&("string"!=typeof r.modelList&&(t.total=r.modelList.length,t.models=r.modelList.map(function(e){return e.id})),"name"in t&&(t.name=r.name))})},getModelListByCond:function(){var e=this;$.ajax({type:"post",url:"/api/caseMode/getModelListByCond",data:{displayType:4,scene:-1},dataType:"json"}).then(function(t){var r=t.success,i=t.data,n=t.msg;if(!r)return e.$message({message:n,type:"error",duration:1500});e.options=Object.keys(i).map(function(e){return i[e]})})}}})}();