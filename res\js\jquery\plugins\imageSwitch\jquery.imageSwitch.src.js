// 修正IE6每次从服务器读取背景图片的BUG
try {
	document.execCommand('BackgroundImageCache', false, true);
}catch(e){

}

(function($){

jQuery.fn.extend({
	
	imageSwitch:function(setting){
		if (typeof Fai == "undefined"){
			alert("must import fai.js");
			return;
		}
		var config = $.extend({
			title:true,						// 是否有标题
			desc:false,						// 是否有描述
			btn:true,						// 是否显示按钮
			repeat:'no-repeat',				// 重复规则 'no-repeat' 'repeat-x' 'repeat-y' 'repeat'
			position:'50% 50%',				// 背景位置
			
			titleSize:14,					// 标题文字大小
			titleFont:'Verdana,宋体',		// 标题文本字体
			titleColor:'#FFF',				// 标题文本颜色
			titleTop:4,						// 标题上边距
			titleLeft:4,					// 标题左边距
			
			descSize:12,					// 描述文字大小
			descFont:'Verdana,宋体',			// 描述文本字体
			descColor:'#FFF',				// 描述文本颜色
			descTop:2,						// 描述上边距
			descLeft:4,						// 描述左边距
			
			btnWidth:15,					// 按钮宽
			btnHeight:15,					// 按钮高
			btnMargin:4,					// 按钮间距
			
			playTime:4000,					// 轮换间隔时间,单位(毫秒)
			animateTime:1500,				// 轮换动画时间,单位(毫秒)
			animateStyle:'o',				// 动画效果:'o':渐显 'x':横向滚动 'y':纵向滚动 'show':原地收缩伸展 'show-x':横向收缩伸展 'show-y':纵向收缩伸展' none':无动画
			//width:300,						// 宽, 不设定则从DOM读取
			//height:200						// 高, 不设定则从DOM读取
			index: 0,
			from: 'module'
			
		},setting);
		
		return $(this).each(function(){
			var _this = $(this);
			var _w = config.width || _this.width();			// 宽
			var _h = config.height || _this.height();		// 高
			var _n = config.data.length;					// 数目
			var _i = config.index;							// 当前显示的item序号
			
			_this.css('overflow','hidden')
			_this.height(_h);
			if (config.width && config.from == "module") {
				_this.width(config.width);
			}
			
			// 按钮区
			var btnDisplay = "none";
			if(config.data.length > 1)
			{
				btnDisplay = config.btn?'block':'none';
			}
			var _btnArea = 
			$('<div class="imageSwitchBtnArea"/>').appendTo(_this)
						.css('position','absolute')
						.css('zIndex',3)
						.css('display',btnDisplay);
									
			// 插入空div修正IE的绝对定位BUG
			$('<div />').appendTo(_this);
			
			var nameDisplay = $('<div class="imageSwitchShowName" />').appendTo(_this)
								  .css('position','absolute')
								  .css('display','none')
								  .css('zIndex',2)
								  .css('width',$.browser.msie && $.browser.version ==6.0 ? $(_this).parent().parent().width()+'px' : $(_this).parent().width()+'px')
								  .css('height', 30+'px')
								  .css('line-height',30+'px')
								  .css('background-color','#131414')
								  .css('filter', 'alpha(opacity=50)')
								  .css('opacity',0.5);

			
			if(config.showImageName){
				nameDisplay.css('display','block');
			}

			// 图片区
			var _imgArea = 
			$('<div class="switchGroup" />').appendTo(_this)
						.css("width", ( config.width <= _this.width() ) ? config.width : "100%")
						.css("position", "relative")
						.height('y,show-y'.indexOf(config.animateStyle)!=-1?_h*_n:_h);			
	
			// 初始化图片 文字 按钮
			var btnAreaWidth = 0;
			$.each(config.data,function(i,n){
				var onclick = "";
				if (!n.href){
					onclick = "onclick='return false;'";
				}
				if(n.width && n.width > 0) {
					//传了宽度且宽度大于0，则以img的方式，否则以背景的方式显示
					$('<a hidefocus="true" style="outline:none;" ' + onclick + '/>').appendTo(_imgArea)
						  .css("width", "100%")
						  .css("height", _h + "px")
						  .attr('href',n.href?n.href:'javascript:;')
						  .attr('target',n.target?n.target:'')
						  .attr('title',n.tip?n.tip:'')
						  .css('cursor',n.href?'pointer':'default')
						  .css('background-position',config.position)
						  .css('background-repeat',config.repeat)
						  .css('overflow', 'hidden')
						  .css('display','block')
						  .css('float','x,show-x'.indexOf(config.animateStyle)!=-1?'left':'').html("<img src='"+ n.src +"' width='"+ n.width +"' height='"+ n.height +"'>");
				}else{
					$('<a hidefocus="true" style="outline:none;" ' + onclick + '/>').appendTo(_imgArea)
						  .css("width", "100%")
						  .css("height", _h + "px")
						  .attr('href',n.href?n.href:'javascript:;')
						  .attr('target',n.target?n.target:'')
						  .attr('title',n.tip?n.tip:'')
						  .css('cursor',n.href?'pointer':'default')
						  .css('background-image','url('+n.src+')')
						  .css('background-position',config.position)
						  .css('background-repeat',config.repeat)
						  .css('overflow', 'hidden')
						  .css('display','block')
						  .css('float','x,show-x'.indexOf(config.animateStyle)!=-1?'left':'');
				}
								
				var _btn = $('<a class="imageSwitchBtn" />').appendTo(_btnArea)
						  .html('<span>' + (i + 1) + '</span>')
				btnAreaWidth += Fai.getDivWidth(_btn);

				var showImageNameTextSpan = $('<span class="spanHiddenName"/>').appendTo(nameDisplay).css('margin-left',10+'px').text(n.tip);

				if (i == _i) {
					_btn.addClass('imageSwitchBtnSel');
					showImageNameTextSpan.addClass('spanShowName');
				}
				
			});
			
			// 再把btnArea插到switchGroup里面。为了适应特别短的情况
			// 针对site的是banner的
			if (_btnArea.parents("#banner").length > 0) {
				_btnArea.appendTo(_imgArea);
			}

			_btnArea.width(btnAreaWidth);
			var parent = _this.parent();
			var parentWidth = parent.width();
			if($.browser.msie && $.browser.version ==6.0){
				parentWidth = parent.parent().width();
			}
			var parentHeight = parent.height();
			if(config.from == "module")
			{
				//图片从左上角开始
				if(parentWidth > config.width){parentWidth = config.width;}
			}else{
				//banner居中
				if(parentWidth > config.width){parentWidth = config.width + (parentWidth - config.width)/2;}
				if(parentWidth > _this.width()){ parentWidth = _this.width();}
			}
			if(parentHeight > _h){parentHeight = _h;}
			_btnArea.css('top', (parentHeight - _btnArea.height()) + "px");
			//_btnArea.css('left', (parentWidth - btnAreaWidth) + 'px');
			
			// 针对site的是banner的，就默认在最右边
			if (_btnArea.parents("#banner").length > 0) {
				// 这里之前是由总宽度-去btn的宽度，再由左边定位。这里改成右边定位
				_btnArea.css('right', '0px');
			} else {
				_btnArea.css('left', (parentWidth - btnAreaWidth) + 'px');
			}
			
			// 保存所有元素集合的引用,方便在事件中使用
			var _bs = _btnArea.children('a');
			var _is = _imgArea.children('a');
			var spanText = nameDisplay.children('span');
			var titleShowWid = parentWidth - btnAreaWidth;
			nameDisplay.css('top', (parentHeight - _btnArea.height()-4) + "px").css('left', 0+'px');
			var anthorWidth = $.browser.msie && $.browser.version ==6.0 ? parseInt(compareTileWidth()+30) : parseInt(compareTileWidth()+20);
			if(titleShowWid > anthorWidth){
				_btnArea.css('top', (parentHeight - _btnArea.height()) + "px");
			}else{
				_btnArea.css('top', (parentHeight - _btnArea.height()-30) + "px");
			}

			function compareTileWidth(){
				if(spanText.length>0){
					var firstSpanWid = $(spanText[0]).width();
					for(var i=1; i<spanText.length; i++){
						if(firstSpanWid < $(spanText[i]).width()){
							firstSpanWid = $(spanText[i]).width();
						}
					}
					return firstSpanWid;
				}
				return 0;
			}

			// 针对不同的动画效果的附加设置, 主要是block的问题, 若在初始化时设置block:none会造成之后无block效果
			if('o,show,none'.indexOf(config.animateStyle)!=-1){
				_is.each(function(i, n){
					if (_i != i) {
						$(this).hide();
					}
					$(this).css('position','absolute');
					$(this).css('left','0');
					$(this).css('top','0');
				});
			}
			
			// 添加按钮事件
			_bs.click(function(e){
                e.stopPropagation();
				var ii = _bs.index(this);
				if(ii==_i){return;}
				
				_bs.eq(_i).removeClass('imageSwitchBtnSel');
				_bs.eq(ii).addClass('imageSwitchBtnSel');
				
				spanText.eq(_i).removeClass('spanShowName');
				spanText.eq(ii).addClass('spanShowName');
				
				switch(config.animateStyle){
				case 'o' :
					_is.eq(_i).fadeOut(config.animateTime, "failinear");
					_is.eq(ii).fadeIn(config.animateTime, "failinear");
					break;
				case 'x' :
					_imgArea.animate({marginLeft:-ii*_w},config.animateTime);
					break;
				case 'y' :
					_imgArea.animate({marginTop:-ii*_h},config.animateTime);
					break;
				case 'show' :
				case 'show-x' :
				case 'show-y' :
					_is.eq(_i).hide(config.animateTime);
					_is.eq(ii).show(config.animateTime);
					break;				
				case 'none' :
					_is.eq(_i).hide();
					_is.eq(ii).show();
					break;				
				}
				_i = ii;
			});

			// faisco:使用fai的方式，以便弹出窗口是能够停止动画
			var intervalId = 'imageSwitch' + Math.random();
			function play(){
				_bs.eq((_i+1)%_n).click()
			}					
			Fai.addInterval(intervalId, play, config.playTime);
			Fai.startInterval(intervalId);
			_this.mouseover(function(){
				Fai.stopInterval(intervalId);
			});
			_this.mouseout(function(){
				Fai.startInterval(intervalId);
			});

		});
	}
});












})(jQuery);