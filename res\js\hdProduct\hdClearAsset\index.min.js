!function(){"use strict";var e={name:"hdClearAsset",data:function(){return{form:{clearAsset:{aid:"",giftType:""},clearPartAsset:{aid:"",giftType:"",clearPartNum:0}},giftList:[],tableData:[]}},created:function(){this.getGiftTypeList(),this.getDeleteFlowList()},methods:{handleClearAssetClick:function(){var e=this.form.clearAsset,t=e.aid,a=e.giftType;if(!t||!a)return this.$message.error("参数错误");this.clearAsset("/api/gift/clearAsset?aid=".concat(t,"&isClearPart=false&giftType=").concat(a))},handleClearPartAssetClick:function(){var e=this.form.clearPartAsset,t=e.aid,a=e.giftType,r=e.clearPartNum;return t&&a?r<=0?this.$message.error("清空数量需要大于0"):void this.clearAsset("/api/gift/clearAsset?aid=".concat(t,"&giftType=").concat(a,"&isClearPart=true&clearPartNum=").concat(r)):this.$message.error("参数错误")},request:function(e){var t=this;return new Promise(function(t,a){$.ajax(e).then(function(e){try{e=JSON.parse(e)}catch(e){}var r=e,s=r.success,i=r.msg;if(!s)return a(i);t(e)}).fail(function(){a()})}).catch(function(e){return void 0===e&&(e="服务器繁忙，请稍后重试"),t.$message.error(e),Promise.reject(e)})},getGiftTypeList:function(){var e=this;this.request({type:"get",url:"/api/gift/getGiftTypeList"}).then(function(t){var a=t.data,r=a.giftListBak,s=a.jdgiftListBak;e.giftList=[].concat(r.map(function(e){var t=+Object.keys(e)[0];return{type:t,name:e[t]}}),s.map(function(e){var t=+Object.keys(e)[0];return{type:t,name:e[t]}}))})},clearAsset:function(e){var t=this;this.$confirm("确定要清空资产？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){t.request({type:"post",url:e}).then(function(){t.getDeleteFlowList(),t.$message({message:"操作成功",type:"success"})})})},getDeleteFlowList:function(){var e=this;this.request({type:"get",url:"/api/gift/getDeleteFlowList"}).then(function(t){var a=t.list;e.tableData=a.map(function(e){var t=e.aid,a=e.createTime,r=e.isAll,s=e.num,i=e.staffLabel,l=e.typeBname;return{action:"清空".concat(r?"全部":"部分","礼品：aid").concat(t),details:"".concat(i," 清空礼品：").concat(l),createTime:moment(a).format("YYYY年MM月DD日 HH:mm:ss"),specifics:"清空数量：".concat(s)}})})}}};var t,a=function(e,t,a,r,s,i,l,n,o,c){"boolean"!=typeof l&&(o=n,n=l,l=!1);var m,d="function"==typeof a?a.options:a;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,s&&(d.functional=!0)),r&&(d._scopeId=r),i?(m=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,o(e)),e&&e._registeredComponents&&e._registeredComponents.add(i)},d._ssrRegister=m):t&&(m=l?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,n(e))}),m)if(d.functional){var f=d.render;d.render=function(e,t){return m.call(t),f(e,t)}}else{var u=d.beforeCreate;d.beforeCreate=u?[].concat(u,m):[m]}return a},r="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());var s={};var i=a({render:function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"hdClearAsset",attrs:{id:"hdClearAsset"}},[a("h3",[e._v("清空第三方礼品资产（话费和京东e卡）")]),e._v(" "),a("el-form",{attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"aid："}},[a("el-input",{attrs:{placeholder:"填入aid",size:"small"},model:{value:e.form.clearAsset.aid,callback:function(t){e.$set(e.form.clearAsset,"aid",t)},expression:"form.clearAsset.aid"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"礼品类型："}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"选择礼品类型",size:"small"},model:{value:e.form.clearAsset.giftType,callback:function(t){e.$set(e.form.clearAsset,"giftType",t)},expression:"form.clearAsset.giftType"}},e._l(e.giftList,function(e){return a("el-option",{key:e.type,attrs:{label:e.name,value:e.type}})}),1)],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleClearAssetClick}},[e._v("清空")])],1)],1),e._v(" "),a("el-divider",{staticClass:"hr"}),e._v(" "),a("h3",[e._v("清空部分第三方礼品资产（话费和京东e卡以及实物礼品件）")]),e._v(" "),a("el-form",{attrs:{inline:!0,model:e.form}},[a("el-form-item",{attrs:{label:"aid："}},[a("el-input",{attrs:{placeholder:"填入aid",size:"small"},model:{value:e.form.clearPartAsset.aid,callback:function(t){e.$set(e.form.clearPartAsset,"aid",t)},expression:"form.clearPartAsset.aid"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"礼品类型："}},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"选择礼品类型",size:"small"},model:{value:e.form.clearPartAsset.giftType,callback:function(t){e.$set(e.form.clearPartAsset,"giftType",t)},expression:"form.clearPartAsset.giftType"}},e._l(e.giftList,function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.type}})}),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"扣除数量："}},[a("el-input-number",{attrs:{placeholder:"扣除数量",size:"small",min:0},model:{value:e.form.clearPartAsset.clearPartNum,callback:function(t){e.$set(e.form.clearPartAsset,"clearPartNum",t)},expression:"form.clearPartAsset.clearPartNum"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleClearPartAssetClick}},[e._v("清空")])],1)],1),e._v(" "),a("el-divider",{staticClass:"hr"}),e._v(" "),a("h3",[e._v("礼品清空日志")]),e._v(" "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,stripe:"",height:e.tableData.length?500:"auto"}},[a("el-table-column",{attrs:{prop:"action","min-width":200,label:"操作行为"}}),e._v(" "),a("el-table-column",{attrs:{prop:"details","min-width":370,label:"操作详情"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createTime","min-width":200,label:"操作时间"}}),e._v(" "),a("el-table-column",{attrs:{prop:"specifics","min-width":150,label:"明细"}})],1)],1)},staticRenderFns:[]},function(e){e&&e("data-v-3cc4a6ac_0",{source:".hdClearAsset[data-v-3cc4a6ac]{margin:20px}.hdClearAsset h3[data-v-3cc4a6ac]{color:#535353}.hdClearAsset .hr[data-v-3cc4a6ac]{margin-top:0}",map:void 0,media:void 0})},e,"data-v-3cc4a6ac",!1,void 0,!1,function(e){return function(e,a){return function(e,a){var i=r?a.media||"default":e,l=s[i]||(s[i]={ids:new Set,styles:[]});if(!l.ids.has(e)){l.ids.add(e);var n=a.source;if(a.map&&(n+="\n/*# sourceURL="+a.map.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a.map))))+" */"),l.element||(l.element=document.createElement("style"),l.element.type="text/css",a.media&&l.element.setAttribute("media",a.media),void 0===t&&(t=document.head||document.getElementsByTagName("head")[0]),t.appendChild(l.element)),"styleSheet"in l.element)l.styles.push(n),l.element.styleSheet.cssText=l.styles.filter(Boolean).join("\n");else{var o=l.ids.size-1,c=document.createTextNode(n),m=l.element.childNodes;m[o]&&l.element.removeChild(m[o]),m.length?l.element.insertBefore(c,m[o]):l.element.appendChild(c)}}}(e,a)}},void 0,void 0);new Vue({render:function(e){return e(i)}}).$mount("#app")}();