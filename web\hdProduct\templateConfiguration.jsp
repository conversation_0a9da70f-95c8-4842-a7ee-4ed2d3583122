<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.hdUtil.hostConf.HdBaseHostConf" %>

<%
  if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
    out.println("没有权限");
    return;
  }

  int envMode =  Web.getEnvMode();
  Boolean $isPre = envMode == Web.EnvMode.PRE;
  Boolean $debug = Web.getDebug();

  String ossUrl = "http://o." + HdBaseHostConf.getTopHost(Web.getEnvMode());
  String hdUrl = "https://" + HdBaseHostConf.getHdPortalHost(Web.getEnvMode());
%>

<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <title>活动市场模板配置</title>
  <%=Web.getToken()%>
  <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
  <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_templateConfiguration")%>">

  <style>
    [v-cloak] {
      display: none;
    }

    .container {
      width: 500px;
      padding-left: 20px;
    }

    .pt20 {
      padding-top: 20px;
    }

    #app .el-button {
      margin-top: 20px;
    }

    .el-button span {
      padding: 0 20px;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <el-menu
      :default-active="activeIndex"
      mode="horizontal"
      @select="handleSelect"
    >
      <el-menu-item
        v-for="(item, index) in form"
        :key="'title' + index"
        :index="item.scene + ''"
      >{{ item.title }}</el-menu-item>
    </el-menu>
    <div
      v-show="activeIndex == item.scene"
      v-for="(item, index) in form"
      :key="index"
      class="container"
    >
      <el-form
        ref="form"
        :model="item"
        label-width="100px"
        label-position="left"
      >
        <el-form-item
          v-if="'name' in item"
          prop="name"
          label="专题名称："
          class="pt20"
          :rules="rules.name"
        >
          <el-input v-model="item.name"></el-input>
        </el-form-item>

        <el-form-item
          v-if="item.num"
          label="活动数量："
          class="pt20"
        >
          <el-select
            v-model="item.total"
            value-key="id"
            placeholder="请选择"
            filterable
            remote
          >
            <el-option
              v-for="num in (item.num.max - item.num.min + 1)"
              :key="'num' + num"
              :label="num + item.num.min - 1"
              :value="num + item.num.min - 1"
            ></el-option>
          </el-select>
        </el-form-item>

        <div v-for="(model, index) in item.total" :key="'model' + index">
          <h4>活动{{ index + 1 }}</h4>
          <el-form-item
            :prop="'models[' + index + ']'"
            label="活动样板："
            :rules="rules.model"
          >
            <el-virtual-select
              v-model="item.models[index]"
              value-key="id"
              placeholder="请选择"
              filterable
              :options="options"
              label-key="name"
            >
            </el-virtual-select>
          </el-form-item>
        </div>
      </el-form>
      <el-button type="primary" round size="small" @click="submitForm(index)">
        保存
      </el-button>
    </div>
  </div>
</body>

<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>

<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_templateConfiguration")%>"></script>

</html>