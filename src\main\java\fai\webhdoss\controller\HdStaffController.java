package fai.webhdoss.controller;

import fai.app.HdOssStaffDef;
import fai.comm.util.Misc;
import fai.comm.util.Param;
import fai.hdUtil.JsonResult;
import fai.web.Core;
import fai.web.inf.HdOss;
import fai.web.inf.Kid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025/8/21 18:02
 */
@RestController
@RequestMapping("/staff")
public class HdStaffController {

    @GetMapping("/getInfo")
    public JsonResult getInfo() throws Exception {
        int sid = fai.web.Session.getSid();
        HdOss hdOss = (HdOss) Core.getCorpKit(1, Kid.HD_OSS);
        Param staff = hdOss.getStaff(sid);
        if (staff == null || staff.isEmpty()) {
            return JsonResult.error("不存在");
        }
        int authFlag = staff.getInt(HdOssStaffDef.Info.AUTH, 0);
        staff.setBoolean("all", Misc.checkBit(authFlag, HdOssStaffDef.Auth.ALL));
        staff.setBoolean("hdSaleManage", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_SALE_MANAGE));
        staff.setBoolean("hdProductManage", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_PRODUCT_MANAGE));
        staff.setBoolean("hdSale", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_SALE));
        staff.setBoolean("scPm", Misc.checkBit(authFlag, HdOssStaffDef.Auth.SC_PM));
        staff.setBoolean("scDevelop", Misc.checkBit(authFlag, HdOssStaffDef.Auth.SC_DEVELOP));
        staff.setBoolean("scOperation", Misc.checkBit(authFlag, HdOssStaffDef.Auth.SC_OPERATION));
        return JsonResult.success(staff);
    }
}
