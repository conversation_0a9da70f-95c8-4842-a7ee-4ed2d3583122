<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%
    if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
        out.println("没有权限--请让管理员设置新销售系统权限");
        return;
    }%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>领取数量查询</title>
    <%@ include file="/comm/link.jsp.inc"%>
    <%@ include file="/comm/script.jsp.inc"%>
    <link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
</head>
<body id="hdsale-saleArpu">
<!--页面顶部标题-->
<!-- <div style="text-align: center;margin-bottom: 10px;">
    <b style="font-size: 20px;">互动销售领取情况</b>
</div> -->

<!--查询条件 start-->
<div class="saleArpuDiv" v-cloak>
    <el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
        <el-form-item label="arpu日期">
            <el-date-picker class="fai-date" v-model="form.receiveDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="false"></el-date-picker>
            - <el-date-picker class="fai-date" v-model="form.receiveDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="false" ></el-date-picker>
        </el-form-item>
        <el-form-item label="领取日期">
            <el-date-picker class="fai-date" v-model="form.receiveBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="false"></el-date-picker>
            - <el-date-picker class="fai-date" v-model="form.receiveEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="false" ></el-date-picker>
        </el-form-item>
        <el-form-item label="销售">
            <el-select v-model="form.se_sacct" filterable>
                <el-option label="所有" value="all"></el-option>
                <el-option v-for="sale in preSaleList" :label="sale.nickName" :value="sale.acct" :key="sale.acct"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="排序">
            <el-select v-model="form.sort" filterable>
                <el-option v-for="info in arpuSort.labelList" :label="info.name" :value="info.label" :key="info.label"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
            <el-button icon="el-icon-download" type="export"  @click="exportExcel">导出</el-button>
        </el-form-item>
    </el-form>
</div>
<!--查询条件 end-->

<!--数据表格 start-->
<div class="saleArpuList"  v-cloak>
    <el-table :data="tableData" row-key="rowkey" stripe border show-summary class="arpuTable" height="750" style="width: 90%" >
        <el-table-column width="70px" label="人员" prop="nickName" ></el-table-column>
        <el-table-column width="70px" label="账号" prop="acct" ></el-table-column>
        <el-table-column width="100px" label="获取资源数(arpu日期)" prop="receiveCount" ></el-table-column>
        <el-table-column width="70px" label="成单数" prop="payCount" ></el-table-column>
        <el-table-column width="70px" label="付费率" prop="payRate" ></el-table-column>
        <el-table-column width="100px" label="付费金额" prop="price" ></el-table-column>
        <el-table-column width="70px" label="arpu" prop="arpu" ></el-table-column>
        <el-table-column width="70px" label="A类资源arpu" prop="aArpu" ></el-table-column>
        <el-table-column width="70px" label="B类资源arpu" prop="bArpu" ></el-table-column>
        <el-table-column width="70px" label="AB资源arpu" prop="abArpu" ></el-table-column>
        <el-table-column width="100px" label="arpu提升" prop="upArpu" ></el-table-column>
        <el-table-column width="80px" label="A类权重数量" prop="typeA" ></el-table-column>
        <el-table-column width="80px" label="B类权重数量" prop="typeB" ></el-table-column>
        <el-table-column width="80px" label="A类领取数量" prop="allotACount"></el-table-column>
        <el-table-column width="80px" label="B类领取数量" prop="allotBCount"></el-table-column>
        <el-table-column width="90px" label="奖励领取数量" prop="awardCount"></el-table-column>
        <el-table-column width="80px" label="平均分领取(不含周末)" prop="averageCount"></el-table-column>
        <el-table-column width="80px" label="周末领取数" prop="weekendCount"></el-table-column>
        <el-table-column width="90px" label="领取总数量" prop="allotCount"></el-table-column>
    </el-table>
</div>

</body>


<script type="text/javascript">
    var faiSearchObj = new Vue({
        el: '.saleArpuDiv',
        data: {
            form: {//这里是为了填充默认值
                receiveDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 15*24*3600*1000),
                receiveDateEnd: Fai.tool.dateFormatter(new Date()),
                receiveBeg: Fai.tool.dateFormatter(new Date().getTime() - 24*3600*1000),
                receiveEnd: Fai.tool.dateFormatter(new Date()),
                se_sacct: "all",
                sort: "upArpu",
                export:false
            },
            preSaleList: [],
            arpuSort: [],
            taList: [],
        },
        created:function(){
            Fai.http.post("dataDef.jsp?cmd=getSaleArpuABTag", "", false).then(result => {
                if(result.success){
                    this.arpuSort = result.arpuSort;
                    this.preSaleList = result.saleList;
                }
            });
            getDataList(this.form);
        },
        methods: {
            onSubmit() {
                this.form.export = false;
                getDataList(this.form);
            },
            exportExcel(){
                this.form.export = true;
                window.open("/ajax/hdSale_h.jsp?cmd=getSaleArpuAB" + Fai.tool.parseJsonToUrlParam(this.form));
            },
        }
    });

    var faiDataList = new Vue({
        el: '.saleArpuList',
        data: {
            tableData: [],
        },
        created:function(){
        },
        updated:function(){
        },
        methods: {
        }
    });

    function getDataList(urlParam){
        // 查询数据
        Fai.http.post("hdSale_h.jsp?cmd=getSaleArpuAB", urlParam, false).then(result => {
            if(result.success){
                console.log(result.dataList);
                faiDataList.tableData = result.dataList;
            }
        });
    }

</script>

</html>



