<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
    if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
        out.println("没有权限");
        return;
    }
    String hdOssUrl = WebHdOss.getDomainUrl();
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>京东礼品列表</title>
    <%=Web.getToken()%>
    <%=FrontEndDef.getJSsdkComponentScript(9000)%>
	<%=FrontEndDef.getPackageCss("fa-component", "~1.1.0")%>
    <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
    <style>
        body {
			margin: 0;
			font-size: 14px;
			line-height: 1.2;
		}
        #app {
            padding: 10px;
        }
        [v-cloak]{
			display: none;
		}
        .topTab .amount {
            margin-right: 50px;
        }
        .giftImg {
            width: 190px;
            height: 190px;
        }
        .giftImg img {
            width: 100% !important;
            height: 100% !important;
        }
        .giftList, .pagination {
            margin: 10px 0;
        }
        .el-form-item {
            /* margin-bottom: 10px; */
        }
        .el-input {
            width: 300px;
        }
        .skuInput {
            margin-right: 20px;
        }
        .textLink {
            color: #1890ff;
            cursor: pointer;
        }
        .textLink:hover {
            color: #40a9ff;
        }
        .fa-popover {
            z-index: 4999;
        }
        .tip {
            color: #f56c6c;
        }
        .loading-animate {
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            position: absolute;
            top: 0;
            left: 0;
            z-index: 5000;
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <h2>京东礼品列表</h2>
        <div class="topTab">
            <span class="amount">已上架：{{giftForm.onSellAmount}}</span>
            <span class="amount">未上架：{{giftForm.downSellAmount}}</span>
            <el-button type="primary" size="small" @click="editGift('新增')">新增礼品</el-button>
        </div>
        <div class="giftList">
            <el-table :data="giftList" height="660" border>
                <el-table-column fixed="left" prop="fkName" label="礼品名称" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="typeB" label="typeB" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="flag" label="状态" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ getGiftState(scope.row.flag, '0x2') }}
                    </template>
                </el-table-column>
                <el-table-column prop="sku" label="商品编号" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="originalPrice" label="市场价" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.originalPrice | formatMoney }}
                    </template>
                </el-table-column>
                <el-table-column prop="price" label="实际售价" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.price | formatMoney }}
                    </template>
                </el-table-column>
                <el-table-column prop="jdPrice" label="京东采购价" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.jdPrice | formatMoney }}
                    </template>
                </el-table-column>
                <el-table-column prop="productId" label="关联凡科订单ID" sortable min-width="180" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="realPrice" label="关联凡科订单价格" sortable min-width="180" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.realPrice | formatMoney }}
                    </template>
                </el-table-column>
                <el-table-column prop="jdName" label="商品京东名称" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="imagePath" label="主图链接" min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        <fa-popover placement="top">
                            <template slot="content">
                                <div class="giftImg">
                                    <img :src="'//img13.360buyimg.com/n1/' + scope.row.imagePath" />
                                </div>
                            </template>
                            <span class="textLink" @click="jumpToImg('//img13.360buyimg.com/n1/' + scope.row.imagePath)">点击查看</span>
                        </fa-popover>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.createTime | formatDate }}
                    </template>
                </el-table-column>
                <el-table-column prop="sellTime" label="上架时间" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.sellTime | formatDate }}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="180" :resizable="false" align="center">
                    <template slot-scope="scope">
                        <el-button @click="editGift('编辑', scope.row.typeB)" type="primary" size="small">编辑</el-button>
                        <fa-popconfirm
                            placement="topRight"
                            title="确定删除该礼品吗？"
                            @confirm="deleteGift(scope.row.typeB)"
                            @cancel="deletePopovervisible = false"
                        >
                            <template slot="okText">继续</template>
                            <template slot="cancelText">取消</template>
                            <el-button type="danger" size="small">删除</el-button>
                        </fa-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background
                @size-change="giftListSizeChange"
                @current-change="giftListPageChange"
                :current-page="giftForm.page"
                :page-sizes="[5, 10, 20, 30, 50, 100]"
                :page-size="giftForm.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="giftForm.total"
                class="pagination">
            </el-pagination>
        </div>
        <el-dialog :title="getDialogTitle()" :visible.sync="editGiftDialog" :fullscreen="fullscreen">
            <el-form :model="giftDialogForm" :rules="rules" ref="giftDialogForm">
                <el-form-item label="商品编号：" :label-width="formLabelWidth">
                    <el-input v-model="giftDialogForm.sku" @keyup.native="giftDialogForm.sku = onInputId(giftDialogForm.sku)" :disabled="giftDialogForm.dialogCmd === '编辑'" class="skuInput" clearable></el-input>
                    <el-button v-show="giftDialogForm.dialogCmd === '新增'" @click="searchJdGiftBySku" type="primary">查询</el-button>
                </el-form-item>
                <div v-show="isShowDialogDetail">
                    <h3>京东参数</h3>
                    <el-form-item label="商品京东名称：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.jdName }}</span>
                    </el-form-item>
                    <el-form-item label="商品主图：" :label-width="formLabelWidth">
                        <fa-popover placement="right">
                            <template slot="content">
                                <div class="giftImg">
                                    <img :src="'//img13.360buyimg.com/n1/' + giftDialogForm.imagePath" />
                                </div>
                            </template>
                            <span class="textLink" @click="jumpToImg('//img13.360buyimg.com/n1/' + giftDialogForm.imagePath)">点击查看</span>
                        </fa-popover>
                    </el-form-item>
                    <el-form-item label="售卖单位：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.saleUnit }}</span>
                    </el-form-item>
                    <el-form-item label="主站上下架状态：" :label-width="formLabelWidth">
                        <span>{{ getGiftState(giftDialogForm.flag, '0x1') }}</span>
                    </el-form-item>
                    <el-form-item label="品牌名称：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.brandName }}</span>
                    </el-form-item>
                    <el-form-item label="商品详情页大字段：" :label-width="formLabelWidth">
                        <span class="textLink" @click="jumpToGiftDetail('introduction')">点击查看</span>
                    </el-form-item>
                    <el-form-item label="移动端商品详情页大字段：" :label-width="formLabelWidth">
                        <span class="textLink" @click="jumpToGiftDetail('nappintroduction')">点击查看</span>
                    </el-form-item>
                    <el-form-item label="PC端商品详情页大字段：" :label-width="formLabelWidth">
                        <span class="textLink" @click="jumpToGiftDetail('nintroduction')">点击查看</span>
                    </el-form-item>
                    <el-form-item label="微信小程序端商品详情页大字段：" :label-width="formLabelWidth">
                        <span class="textLink" @click="jumpToGiftDetail('wxintroduction')">点击查看</span>
                    </el-form-item>
                    <el-form-item label="售后：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.shouHou }}</span>
                    </el-form-item>
                    <el-form-item label="是否京东配送：" :label-width="formLabelWidth">
                        <span>{{ getGiftState(giftDialogForm.flag, '0x4') }}</span>
                    </el-form-item>
                    <el-form-item label="是否厂直商品：" :label-width="formLabelWidth">
                        <span>{{ getGiftState(giftDialogForm.flag, '0x8') }}</span>
                    </el-form-item>
                    <el-form-item label="采购价：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.jdPrice | formatMoney }}</span>
                    </el-form-item>
                    <h3>配置参数</h3>
                    <el-form-item label="typeB：" prop="typeB" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.typeB" @keyup.native="giftDialogForm.typeB = onInputId(giftDialogForm.typeB)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="礼品名称：" prop="fkName" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.fkName" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="市场价：" prop="originalPrice" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.originalPrice" @keyup.native="giftDialogForm.originalPrice = onInputPrice(giftDialogForm.originalPrice, 2)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="实际售价：" prop="price" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.price" @keyup.native="giftDialogForm.price = onInputPrice(giftDialogForm.price, 2)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="关联凡科订单ID：" prop="productId" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.productId" @keyup.native="giftDialogForm.productId = onInputId(giftDialogForm.productId)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="关联凡科订单售价：" :label-width="formLabelWidth">
                        <span>{{ giftDialogForm.realPrice | formatMoney }}</span>
                    </el-form-item>
                    <el-form-item label="关联分销订单ID：" prop="oemProductId" :label-width="formLabelWidth">
                        <el-input v-model="giftDialogForm.oemProductId" @keyup.native="giftDialogForm.oemProductId = onInputId(giftDialogForm.oemProductId)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="商品状态：" :label-width="formLabelWidth">
                        <el-select v-model="giftDialogForm.fkState" placeholder="请选择">
                            <el-option
                                v-for="item in fkStateOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <div class="tip">注：改为上架状态时必选填写且检查关联凡科订单ID和分销订单ID是否有误，上架需谨慎</div>
                    </el-form-item>
                </div>
            </el-form>
            <div v-show="isShowDialogDetail" slot="footer" class="dialog-footer">
                <el-button @click="editGiftDialog = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('giftDialogForm')">提 交</el-button>
            </div>
        </el-dialog>
        <%-- <div v-if="loading" class="loading-animate">
            <fa-spin size="large" />
        </div> --%>
    </div>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_faiHd")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
    <%=FrontEndDef.getPackageJs("fa-component", "~1.1.0")%>
    <script>
        var checkTypeB = function (rule, value, callback) {
            if (!value) {
                return callback(new Error('typeB值不能为空'));
            }
            setTimeout(function() {
                if (value < 10000 || value >= 20000) {
                    callback(new Error('typeB值应大于等于10000且小于20000'));
                } else {
                    callback();
                }
            }, 200);
        }
        var app = new Vue({
            el: "#app",
            data: {
                hdOssUrl: '<%=hdOssUrl%>',
                editGiftDialog: false,
                deletePopovervisible: false,
                loading: false,
                isShowDialogDetail: false,
                fullscreen: true,
                formLabelWidth: '400px',
                cacheTypeB: '',
                giftList: [],
                giftForm: {
                    page: 1,
                    limit: 10,
                    total: 0,
                    onSellAmount: 0,
                    downSellAmount: 0
                },
                giftDialogForm: {
                    dialogCmd: '编辑',
                    sku: '',
                    typeB: '',
                    fkName: '',
                    originalPrice: '',
                    realPrice: '',
                    productId: '',
                    price: '',
                    oemProductId: '',
                    fkState: 0
                },
                rules: {
                    typeB: [
                        { required: true, message: 'typeB值不能为空', trigger: ['blur', 'change'] },
                        { validator: checkTypeB, trigger: ['blur', 'change'] }
                    ],
                    fkName: [
                        { required: true, message: '礼品名称不能为空', trigger: ['blur', 'change'] },
                        { max: 24, message: '最长不可超过24个字符', trigger: ['blur', 'change'] }
                    ],
                    originalPrice: [
                        { required: true, message: '市场价不能为空', trigger: ['blur', 'change'] }
                    ],
                    price: [
                        { required: true, message: '实际售价不能为空', trigger: ['blur', 'change'] }
                    ]
                },
                fkStateOptions: [
                    { value: 1, label: '上架' },
                    { value: 0, label: '下架' }
                ]
            },
            created: function (){
                this.getGiftList();
            },
            methods: {
                getGiftState: function(flag, bit){
                    var sellState = ['0x1', '0x2'].indexOf(bit) !== -1;
                    return hdFai.checkBit(flag, bit) ? (sellState ? '已上架' : '是') : (sellState ? '未上架' : '否');
                },
                getDialogTitle: function(){
                    return this.giftDialogForm.dialogCmd + '京东礼品';
                },
                getGiftList: function(){
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/ajax/hdJdGift_h.jsp?cmd=getJdGiftList',
                        data: {
                            page: _this.giftForm.page,
                            limit: _this.giftForm.limit
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                _this.giftList = result.list;
                                _this.giftForm.total = result.total;
                                _this.giftForm.onSellAmount = result.onSellAmount;
                                _this.giftForm.downSellAmount = result.total - result.onSellAmount;
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                giftListSizeChange: function(val){
                    this.giftForm.limit = val;
                    this.giftForm.page = 1;
                    this.getGiftList();
                },
                giftListPageChange: function(val){
                    this.giftForm.page = val;
                    this.getGiftList();
                },
                editGift: function(cmd, typeB){
                    this.giftDialogForm.dialogCmd = cmd;
                    var isEditGift = cmd === '编辑';
                    if(isEditGift){
                        this.cacheTypeB = typeB;
                        this.giftDialogForm.typeB = typeB;
                        this.searchGiftByTypeB();
                    }else{
                        this.giftDialogForm.typeB = '';
                        this.giftDialogForm.sku = '';
                        this.isShowDialogDetail = false;
                        this.editGiftDialog = true;
                    }
                },
                deleteGift: function(typeB){
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/ajax/hdJdGift_h.jsp?cmd=delJdGift',
                        data: {
                            typeB: typeB
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                _this.alertSuccessMsg('删除成功');
                                _this.deletePopovervisible = false;
                                _this.getGiftList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                // 编辑京东礼品弹窗初始化调用
                searchGiftByTypeB: function(){
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/ajax/hdJdGift_h.jsp?cmd=getJdGift',
                        data: {
                            typeB: _this.giftDialogForm.typeB
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            console.log('getJdGift', result);
                            if(result.success){
                                _this.giftDialogForm = $.extend(true, _this.giftDialogForm, result.info, { fkState: Number(hdFai.checkBit(result.info.flag, '0x2')) });
                                _this.isShowDialogDetail = true;
                                _this.editGiftDialog = true;
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                // 新增京东礼品查询调用
                searchJdGiftBySku: function(){
                    this.isShowDialogDetail = false;
                    if(this.checkEmpty(this.giftDialogForm.sku)){
                        this.alertWarningMsg('请检查商品编号');
                        return;
                    }
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/ajax/hdJdGift_h.jsp?cmd=getJdGiftByJd',
                        data: {
                            sku: _this.giftDialogForm.sku
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            console.log('getJdGiftByJd', result);
                            if(result.success){
                                var defGiftDialogForm = {
                                    typeB: '',
                                    fkName: '',
                                    originalPrice: '',
                                    realPrice: '',
                                    productId: '',
                                    price: '',
                                    oemProductId: '',
                                    fkState: 0
                                };
                                _this.giftDialogForm = $.extend(true, _this.giftDialogForm, defGiftDialogForm, result.info);
                                _this.isShowDialogDetail = true;
                            }else{
                                _this.alertErrorMsg('商品参数错误');
                            }
						}
                    });
                },
                jumpToImg: function(imgPath){
                    window.open(imgPath);
                },
                jumpToGiftDetail: function(key){
                    window.open(this.hdOssUrl + '/hdProduct/showImg.jsp?sku=' + this.giftDialogForm.sku + '&key=' + key);
                },
                submitForm: function(formName){
                    if(this.checkEmpty(this.giftDialogForm.sku)){
                        this.alertWarningMsg('请检查商品编号');
                        return;
                    }
                    var _this = this;
                    this.$refs[formName].validate(function (valid) {
                        if (valid) {
                            if(_this.giftDialogForm.fkState == 1 && (_this.checkEmpty(_this.giftDialogForm.productId) || _this.checkEmpty(_this.giftDialogForm.oemProductId))){
                                _this.alertWarningMsg('请检查凡科订单ID和分享订单ID');
                                return false;
                            }
                            var giftDialogForm = $.extend(true, {}, _this.giftDialogForm);
                            giftDialogForm.flag = _this.setFlag(giftDialogForm.flag, 0x2, giftDialogForm.fkState);
                            for(var key in  giftDialogForm){
                                if($.inArray(key, ['typeB', 'originalPrice', 'price', 'productId', 'oemProductId']) !== -1){
                                    giftDialogForm[key] = Number(giftDialogForm[key] || 0);
                                }
                            }
                            if(_this.giftDialogForm.dialogCmd === '新增'){
                                _this.addGift(giftDialogForm);
                            }else{
                                _this.updateGift(giftDialogForm);
                            }
                        } else {
                            _this.alertWarningMsg('请检查typeB、礼品名称、市场价和实际售价');
                            return false;
                        }
                    });
                },
                addGift: function(giftDialogForm){
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/ajax/hdJdGift_h.jsp?cmd=addJdGift',
                        data: {
                            info: $.toJSON(giftDialogForm)
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                _this.alertSuccessMsg('提交成功');
                                _this.editGiftDialog = false;
                                _this.getGiftList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                updateGift: function(giftDialogForm){
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/ajax/hdJdGift_h.jsp?cmd=setJdGift',
                        data: {
                            typeB: Number(_this.cacheTypeB || 0),
                            info: $.toJSON(giftDialogForm)
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                _this.alertSuccessMsg('提交成功');
                                _this.editGiftDialog = false;
                                _this.getGiftList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                checkEmpty: function(val){
                    return $.trim(val).length === 0 || !hdFai.isNumber(val);
                },
                onInputId: function(val){
                    return val.replace(/[^\d.]/g, '');
                },
                onInputPrice: function(num, limit){
                    var str = num;
                    var len1 = str.substr(0, 1);
                    var len2 = str.substr(1, 1);
                    //如果第一位是0，第二位不是点，就用数字把点替换掉
                    if (str.length > 1 && len1 == 0 && len2 != ".") {
                        str = str.substr(1, 1);
                    }
                    //第一位不能是.
                    if (len1 == ".") {
                        str = "";
                    }
                    //限制只能输入一个小数点
                    if (str.indexOf(".") != -1) {
                        var str_ = str.substr(str.indexOf(".") + 1);
                        if (str_.indexOf(".") != -1) {
                            str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
                        }
                    }
                    //正则替换
                    str = str.replace(/[^\d^\.]+/g, ''); // 保留数字和小数点
                    if (limit / 1 === 1) {
                        str = str.replace(/^\D*([0-9]\d*\.?\d{0,1})?.*$/,'$1'); // 小数点后只能输 1 位
                    } else {
                        str = str.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1'); // 小数点后只能输 2 位
                    }
                    return str;
                },
                /**
                 * 功能说明: 设置flag操作
                 * @param {Number} key 需要设置的整数
                 * @param {Object|Number} checkFlag 需要设置的位数
                 * @param {Boolean} value 需要设置的布尔值
                 * @return {Number}
                 */
                setFlag: function(key, checkFlag, value){
                    if(this.getType(checkFlag) == 'object') {
                        for(var i = 0; i < 31; i++) {
                            var check = 0x1 << i;
                            var val = checkFlag[check + ''];
                            if(typeof val == 'boolean') {
                                key = this.setFlag(key, check, val);
                            }
                        }
                    }else{
                        if(value) {
                            key |= checkFlag;
                        }else{
                            key &= ~checkFlag;
                        }
                    }
                    return key;
                },
                /**
                 * 功能说明: 获取变量类型
                 * @param {any} obj 需要校验的变量
                 * @return {String} 变量的类型
                 */
                getType: function(obj){
                    return Object.prototype.toString.call(obj).match(/\[object\s(\w+)]/)[1].toLowerCase();
                },
                alertErrorMsg: function(msg){
                    ELEMENT.Message({
                        type: 'error',
                        message: msg || '系统繁忙，请稍后重试'
                    });
                },
                alertSuccessMsg: function(msg){
                    ELEMENT.Message({
                        type: 'success',
                        message: msg
                    });
                },
                alertWarningMsg: function(msg){
                    ELEMENT.Message({
                        type: 'warning',
                        message: msg
                    });
                }
            },
            filters: {
                // 自定义时间格式化过滤器，默认返回yyyy-MM-dd HH:mm
                formatDate(timestamp, format){
                    return timestamp === 852048000000 ? '-' : $.format.date(new Date(timestamp), format || 'yyyy-MM-dd HH:mm');
                },
                // 金额格式化过滤器
                formatMoney(value, unit){
                    unit = unit || '￥';
                    if(!value) return unit + '0.00';
                    value = value.toFixed(2);
                    var intPart = parseInt(value); // 获取整数部分
                    var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'); // 将整数部分逢三一断
                    var floatPart = '.00'; // 预定义小数部分
                    var value2Array = value.split('.');
                    // 长度等于2表示数据有小数位
                    if(value2Array.length === 2) {
                        floatPart = value2Array[1].toString(); // 拿到小数部分
                        if(floatPart.length === 1) { // 补0,实际上用不着
                            return unit + intPartFormat + '.' + floatPart + '0';
                        } else {
                            return unit + intPartFormat + '.' + floatPart;
                        }
                    } else {
                        return unit + intPartFormat + floatPart;
                    }
                }
            }
        });
    </script>
</body>
</html>
