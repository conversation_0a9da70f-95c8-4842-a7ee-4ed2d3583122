<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="fai.cli.*"%>
<%@ page import="fai.webkchome.WebKchome" %>
<%@ page import="com.alibaba.fastjson.JSONArray" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.EQ" %>


<%--互动产品--%>

<%!

	//生成唯一错误码
	private String genErrorCode(HttpServletRequest request) throws Exception{
		
		String description = request.getParameter("description");
		DaoPool dp = WebHdOss.getDaoPool("hdOssAffair");
		Param result = new Param();
		if(dp==null){
			result.setInt("rt",-1);
			result.setString("msg","数据库连接为空");
			result.setBoolean("success",false);
			return result.toJson();
		}
		
		Dao d1 = dp.getDao();	
		Param p = new Param();
 		Ref<Integer> id = new Ref<Integer>();
		p.setString("description",description);int rt;
		try{
			rt = d1.insertReturnAutoId("hdErrorCode",p,id);
		}finally{
			d1.close();
		}
		if(rt != Errno.OK){
			result.setInt("rt",rt);
			result.setString("msg","申请失败");
			result.setBoolean("success",false);
			return result.toJson();
		}	
		result.setInt("rt",rt);
		result.setInt("msg",id.value);
		result.setBoolean("success",true);
		return result.toJson();	 
	}

	//获取错误码列表
	private String getErrorCodeList(HttpServletRequest request) throws Exception{
		int page = Parser.parseInt(request.getParameter("page"),1);
		int limit = Parser.parseInt(request.getParameter("limit"),10);
		int start = ( page - 1 ) * limit;
		DaoPool dp = WebHdOss.getDaoPool("hdOssAffair");
		Param result = new Param();
		if(dp==null){
			result.setInt("rt",-1);
			result.setString("msg","数据库连接为空");
			result.setBoolean("success",false);
			return result.toJson();
		}		
		Dao d1 = dp.getDao();	
		String sql  = "SELECT *FROM hdErrorCode ORDER BY code DESC limit "+ (limit-10) +","+limit;
		
		Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdErrorCode";
        selectArg.field = "code,description";
		selectArg.searchArg.cmpor = new ParamComparator("code",true);
		selectArg.searchArg.totalSize = new fai.comm.util.Ref<Integer>();
		selectArg.searchArg.limit = limit;
		selectArg.searchArg.start = start;

		FaiList<Param> codeList = new FaiList<Param>();		
		try{
			codeList = d1.select(selectArg);
		}finally{
			d1.close();
		}
		for(Param p : codeList){
			String code = "互动错误码-"+p.getInt("code");
			p.setString("code",code);
		}
		result.setInt("total",selectArg.searchArg.totalSize.value);
		result.setList("codeList",codeList);
		return result.toJson();
		
	}

	//删除错误码
	private String deleteErrorCode(HttpServletRequest request) throws Exception{
		int code = Parser.parseInt(request.getParameter("code").replace("互动错误码-",""),0);
		Log.logDbg("unique-test code = %s",code);
		DaoPool dp = WebHdOss.getDaoPool("hdOssAffair");
		Param result = new Param();
		if(dp==null){
			result.setString("msg","数据库连接为空");
			result.setBoolean("success",false);
			return result.toJson();
		}
		Dao d1 = dp.getDao();
		ParamMatcher matcher = new ParamMatcher("code",ParamMatcher.EQ,code);
		int rt = Errno.ERROR;
		try{
			rt = d1.delete("hdErrorCode",matcher);
		}finally{
			d1.close();
		}
		if(rt != Errno.OK){
			result.setString("msg","删除失败");
			result.setBoolean("success",false);
			return result.toJson();
		}
		result.setString("msg","删除成功");
		result.setBoolean("success",true);
		return result.toJson();	
	}

	//查询错误码
	private String searchErrorCode(HttpServletRequest request)throws Exception{
		int code = Parser.parseInt(request.getParameter("code").replace("互动错误码-",""),0);
		Log.logDbg("unique-test code = %s", code);
		DaoPool dp = WebHdOss.getDaoPool("hdOssAffair");
		Param result = new Param();
		if(dp==null){
			result.setString("msg","数据库连接为空");
			result.setBoolean("success",false);
			return result.toJson();
		}
		Dao d1 = dp.getDao();
		String sql = "SELECT *FROM hdErrorCode WHERE code = " + code;
		FaiList<Param> codeList = new FaiList<Param>();
		try{
			codeList = d1.executeQuery(sql);
		}finally{
			d1.close();
		}
		if(codeList==null || codeList.size() < 1){
			result.setString("msg","无此错误码");
			result.setBoolean("success",false);
			return result.toJson();
		}
		return codeList.toJson();

	}
	
	//Oss获取调查问卷列表
	private String getResearchQuestionsList4Oss(HttpServletRequest request) throws Exception {
        int rt = Errno.OK;
		
		String invasitiNum = request.getParameter("invastiNum");
        int id = Parser.parseInt(invasitiNum,0);
        String title = Parser.parseString(request.getParameter("invastiName"), "");
        String remark = Parser.parseString(request.getParameter("invastiRemark"), "");
        int page = Parser.parseInt(request.getParameter("page"), 0);
        int pageSize = Parser.parseInt(request.getParameter("pageSize"), 0);

		if(invasitiNum != null && id < 0){
			rt = Errno.ARGS_ERROR;
            return "{\"success\":false,\"rt\":"+rt+", \"msg\":问卷编码无效}";
		}

        if(page <= 0){
            rt = Errno.ARGS_ERROR;
            return "{\"success\":false,\"rt\":"+rt+", \"msg\":page无效}";
        }
        if(pageSize <= 0){
            rt = Errno.ARGS_ERROR;
            return "{\"success\":false,\"rt\":"+rt+", \"msg\":pageSize无效}";
        }

        HdResearchQuestions hdResearchQuestions =  WebHdOss.getCorpKit(HdResearchQuestions.class);
        FaiList<Param> list = new FaiList<Param>();
        Ref<Integer> totalRef = new Ref<Integer>();

        rt = hdResearchQuestions.getListForOss(id, title, remark, page, pageSize, list, totalRef);

        if(rt == Errno.OK){
            return "{\"success\":true,\"list\":"+list+"}";
        }else{
            return "{\"success\":false,\"rt\"\":"+rt+"}";
        }
    }

	//Oss根据问卷type获取问卷答案列表
    private String getResearchQuestions4Oss(HttpServletRequest request) throws Exception {
        int rt = Errno.OK;

		int aid = Parser.parseInt(request.getParameter("aid"), 0);
		long bt = Parser.parseLong(request.getParameter("startTime"), 0L);
		long et = Parser.parseLong(request.getParameter("endTime"), 0L);

		int isPayVersionInt = Parser.parseInt(request.getParameter("isPayVersion"), 0);
		Calendar beginTime = bt == 0L ? null : Parser.parseCalendar(new Date(bt));
		Calendar endTime = et == 0L ? null : Parser.parseCalendar(new Date(et));

        int type = Parser.parseInt(request.getParameter("type"), 0);
        int page = Parser.parseInt(request.getParameter("page"), 0);
        int pageSize = Parser.parseInt(request.getParameter("pageSize"), 0);
		Param result = new Param();
        if(type<=0){
            rt = Errno.ARGS_ERROR;
			result.setBoolean("success", false);
			result.setInt("rt", rt);
			result.setString("msg", "type无效");
            return result.toJson();
        }

        if(page <= 0){
            rt = Errno.ARGS_ERROR;
			result.setBoolean("success", false);
			result.setInt("rt", rt);
			result.setString("msg", "page无效");
            return result.toJson();
        }
        if(pageSize <= 0){
            rt = Errno.ARGS_ERROR;
			result.setBoolean("success", false);
			result.setInt("rt", rt);
			result.setString("msg", "pageSize无效");
            return result.toJson();
        }
		Boolean isPayVersion = null;
        if(isPayVersionInt == 1){
			isPayVersion = true;
		}else if(isPayVersionInt == 2){
            isPayVersion = false;
		}

        HdResearch hdResearch = (HdResearch) WebHdOss.getCorpKit(Kid.HD_RESEARCH);

        Ref<FaiList<Param>> listRef = new Ref<FaiList<Param>>();
        Ref<Integer> totalRef = new Ref<Integer>();
        rt = hdResearch.getList4OssByType(type, page, pageSize, aid, beginTime, endTime, isPayVersion, listRef, totalRef);
        if(rt != Errno.OK){
			result.setBoolean("success", false);
			result.setInt("rt", rt);
			result.setString("msg", "获取问卷记录失败");
            return result.toJson();
        }

		result.setBoolean("success", true);
		result.setString("data", listRef.value.toJson());
		result.setInt("total", totalRef.value);
		return result.toJson();
    }

	//Oss根据type获取问卷详情
	private String getResearchQuestionsByType4Oss(HttpServletRequest request) throws Exception {
        int rt = Errno.OK;

        int type = Parser.parseInt(request.getParameter("type"), 0);
        if(type<=0){
            rt = Errno.ARGS_ERROR;
            return "{\"success\":false,\"rt\":"+rt+", \"msg\":type无效}";
        }
        HdResearchQuestions hdResearchQuestions = WebHdOss.getCorpKit(HdResearchQuestions.class);
        Ref<FaiList<Param>> listRef = new Ref<FaiList<Param>>();

        rt = hdResearchQuestions.getQuestionsByTypeForHD(type, listRef);
		Param result = new Param();
        if(rt != Errno.OK){
			result.setBoolean("success", false);
			result.setInt("rt", rt);
			result.setString("msg", "获取问卷信息失败");
            return result.toJson();
        }
        FaiList<Param> list = listRef.value;

		result.setBoolean("success", true);
		result.setString("data", list.toJson());
        return result.toJson();
    }

	private String exportResearch(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception{
		HdResearch hdResearch = (HdResearch) WebHdOss.getCorpKit(Kid.HD_RESEARCH);
		int aid = Parser.parseInt(request.getParameter("aid"), 0);
		long bt = Parser.parseLong(request.getParameter("startTime"), 0L);
		long et = Parser.parseLong(request.getParameter("endTime"), 0L);

		int isPayVersionInt = Parser.parseInt(request.getParameter("isPayVersion"), 0);
		Calendar beginTime = bt == 0L ? null : Parser.parseCalendar(new Date(bt));
		Calendar endTime = et == 0L ? null : Parser.parseCalendar(new Date(et));

		int type = Parser.parseInt(request.getParameter("type"), 0);

		 if(type<=0){
			return "type无效";
        }

		Boolean isPayVersion = null;
        if(isPayVersionInt == 1){
			isPayVersion = true;
		}else if(isPayVersionInt == 2){
            isPayVersion = false;
		}

		Ref<FaiList<Param>> faiListRef = new Ref<FaiList<Param>>();
		int rt = hdResearch.getList4Export(type, aid, beginTime, endTime, isPayVersion, faiListRef);
		FaiList<Param> options = faiListRef.value;

		String name = "调查问卷";
		String fileName =  name+ Parser.parseDateString( Calendar.getInstance() ) + ".xls";
		dealBeforeConvert( request,response,out,fileName,options );
		return "suc";
	}

	private String getMsgRecordList(HttpServletRequest request) throws Exception{
		int taskId = Parser.parseInt(request.getParameter("taskId"), 0);
		int page = Parser.parseInt(request.getParameter("page"), 1);
		int limit = Parser.parseInt(request.getParameter("limit"), 20);
		HdOss hdOss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdTemplateMsgRecordDef.Info.TASK_ID, ParamMatcher.EQ, taskId);
		searchArg.start = (page - 1) * limit;
		searchArg.limit = limit;
		searchArg.totalSize = new Ref<Integer>();

		FaiList<Param> list = new FaiList<Param>();
		int rt = hdOss.getTemplateMsgRecordList(taskId, searchArg, list);
		if(rt != Errno.OK || searchArg.totalSize == null){
		    if(rt == Errno.NOT_FOUND){
				return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "查找不到数据");
			}
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		return new HdGameDef.ErrorInfo(rt, "list", list).add("total", searchArg.totalSize.value).toString();
	}

	private String exportMsgRecordList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception{
		int taskId = Parser.parseInt(request.getParameter("taskId"), 0);
		HdOss hdOss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdTemplateMsgRecordDef.Info.TASK_ID, ParamMatcher.EQ, taskId);
		searchArg.totalSize = new Ref<Integer>();

		FaiList<Param> list = new FaiList<Param>();
		int rt = hdOss.getTemplateMsgRecordList(taskId, searchArg, list);
		if(rt != Errno.OK && rt != Errno.NOT_FOUND || searchArg.totalSize == null){
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}

		FaiList<Param> colList = new FaiList<Param>();
		{
			Param k = new Param();
			k.setString( MSOfficeConverter.Col.KEY, "aid");
			k.setInt( MSOfficeConverter.Col.WIDTH,20 );
			colList.add( k );
		}
		{
			Param k = new Param();
			k.setString( MSOfficeConverter.Col.KEY, "openId");
			k.setInt( MSOfficeConverter.Col.WIDTH,40 );
			colList.add( k );
		}
		{
			Param k = new Param();
			k.setString( MSOfficeConverter.Col.KEY, "unsubscribe");
			k.setInt( MSOfficeConverter.Col.WIDTH,20 );
			colList.add( k );
		}
		{
			Param k = new Param();
			k.setString( MSOfficeConverter.Col.KEY, "unsubscribeTime");
			k.setInt( MSOfficeConverter.Col.WIDTH,20 );
			colList.add( k );
		}

		FaiList<Param> rowList = new FaiList<Param>();
		Param p = new Param();
		p.setString("aid", "aid");
		p.setString("openId", "openId");
		p.setString("unsubscribe", "是否取消关注");
		p.setString("unsubscribeTime", "取消关注时间");
		rowList.add(p);
		rowList.addAll(list);

		FaiList<Param> optionList = new FaiList<Param>();
		int rowCount = rowList.size();
		Param option = null;
		if (rowCount <= 65535) {
			option = new Param();
			option.setString(MSOfficeConverter.Option.SHEET_NAME, "取关统计");
			option.setList(MSOfficeConverter.Option.COL_LIST, colList);
			option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
			optionList.add(option);
		} else {
			for (int i = 1; i <= rowCount; i = i + 65535) {
				option = new Param();
				int indexCount = (i + 65535) >= rowCount ? rowCount : i + 65535;
				option.setString(MSOfficeConverter.Option.SHEET_NAME, "取关统计" + i + "~" + (indexCount - 1));
				option.setList(MSOfficeConverter.Option.COL_LIST, colList);
				FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
				sheetData.add(0, p);
				option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
				optionList.add(option);
			}
		}

		String fileName = "取关统计"+".xls";
		dealBeforeConvert( request,response,out,fileName,optionList );
		return "suc";
	}

	private static void dealBeforeConvert ( HttpServletRequest request, HttpServletResponse response, JspWriter out , String fileName , FaiList<Param> optionList )throws Exception{
		out.clear();
		String agent = request.getHeader("User-Agent");
		agent = agent == null ? "" : agent.toUpperCase();
		response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent,fileName) + "\""); 
		response.setContentType("application/vnd.ms-excel");
		MSOfficeConverter.excelConverter(response.getOutputStream(),optionList);
		//out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
	}

	private String addQuestions(HttpServletRequest request) throws Exception{
		int id = Parser.parseInt(request.getParameter("id"), 0);
		int type = Parser.parseInt(request.getParameter("type"), 0);
		String title = request.getParameter("title");
		String remark = request.getParameter("remark");
		String question = request.getParameter("questions");

		HdResearchQuestions hdResearchQuestions = WebHdOss.getCorpKit(HdResearchQuestions.class);


		int rt = hdResearchQuestions.addQuestions(id, type, title, question, remark);
		if(rt != Errno.OK){
		    Log.logDbg(rt, "addQuestions err; type=%s, title=%s, question=%s, remaek=%s", type, title, question, remark);
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		return new HdGameDef.ErrorInfo(rt, "ok").toString();
	}

	private String addNextGame(HttpServletRequest request) throws Exception{
		String name = request.getParameter("name");
		String detail =  request.getParameter("detail");
		String img = request.getParameter("img");
		int sortBy = Parser.parseInt(request.getParameter("sortBy"), 0);
		int rt = Errno.OK;

		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdNextGameDef.Info.FLAG, ParamMatcher.LAND_NE, HdNextGameDef.Flag.IS_DEL, HdNextGameDef.Flag.IS_DEL);
		searchArg.cmpor = new ParamComparator(HdNextGameDef.Info.ID, true);

		FaiList<Param> list = new FaiList<Param>();
		rt = hdOss.getNextGameList(searchArg, list);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
			Log.logErr(rt, "get list err");
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		if(list.size() >= 3){
		    rt = Errno.ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "已经有3个预约上线活动了");
		}


		Param data = new Param();
		data.setString(HdNextGameDef.Info.NAME, name);
		data.setString(HdNextGameDef.Info.DETAIL, detail);
		data.setString(HdNextGameDef.Info.IMG, img);
		data.setInt(HdNextGameDef.Info.SORT_BY, sortBy);
		data.setCalendar(HdNextGameDef.Info.ONLINE_TIME, Calendar.getInstance());
		Ref<Integer> idRef = new Ref<Integer>();
		rt = hdOss.addNextGame(data, idRef);
		if(rt != Errno.OK){
			Log.logErr(rt, "addNextGame err; data=%s", data);
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}

		return new HdGameDef.ErrorInfo(rt, "ok").add("id", idRef.value).toString();
	}

	private String setNextGame(HttpServletRequest request) throws Exception{
		int id = Parser.parseInt(request.getParameter("id"), 0);
		String name = request.getParameter("name");
		String detail =  request.getParameter("detail");
		String img = request.getParameter("img");
		int rt = Errno.OK;


		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		Param update = new Param();
		update.setString(HdNextGameDef.Info.NAME, name);
		update.setString(HdNextGameDef.Info.DETAIL, detail);
		update.setString(HdNextGameDef.Info.IMG, img);
		ParamUpdater updater = new ParamUpdater(update);

		rt = hdOss.setNextGame(id, updater);
		if (rt != Errno.OK) {
			Log.logErr(rt, "setNextGame err; id=%s, updater=%s", id, updater.getData());
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		return new HdGameDef.ErrorInfo(rt, "ok").toString();
	}

	private String delNextGame(HttpServletRequest request) throws Exception{
		int id = Parser.parseInt(request.getParameter("id"), 0);

		int rt = Errno.OK;
		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		Param info = new Param();
		rt = hdOss.getNextGame(id, info);
		if(rt != Errno.OK){
			Log.logErr(rt, "get nextGame err; replaceId=%s", id);
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		int flag = info.getInt(HdNextGameDef.Info.FLAG, 0);

		Param update = new Param();
		flag = Misc.setFlag(flag, HdNextGameDef.Flag.IS_DEL, true);
		update.setInt(HdNextGameDef.Info.FLAG, flag);
		update.setCalendar(HdNextGameDef.Info.OFFLINE_TIME, Calendar.getInstance());
		ParamUpdater updater = new ParamUpdater(update);

		rt = hdOss.setNextGame(id, updater);
		if (rt != Errno.OK) {
			Log.logErr(rt, "setNextGame err; replaceId=%s, updater=%s", id, updater.getData());
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		return new HdGameDef.ErrorInfo(rt, "ok").toString();
	}

	private String getNextGameList(HttpServletRequest request) throws Exception{
		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdNextGameDef.Info.FLAG, ParamMatcher.LAND_NE, HdNextGameDef.Flag.IS_DEL, HdNextGameDef.Flag.IS_DEL);
		searchArg.cmpor = new ParamComparator(HdNextGameDef.Info.ID, true);

		FaiList<Param> list = new FaiList<Param>();
		int rt = hdOss.getNextGameList(searchArg, list);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
		    Log.logErr(rt, "get list err");
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}
		rt = Errno.OK;
		return new HdGameDef.ErrorInfo(rt, "list", list).toString();
	}

	private String getHistoryNextGameList(HttpServletRequest request) throws Exception{
	    String name = request.getParameter("name");
	    int page = Parser.parseInt(request.getParameter("page"), 0);
	    int limit = Parser.parseInt(request.getParameter("limit"), 0);

		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher();
		searchArg.matcher = new ParamMatcher();
        if(!Str.isEmpty(name)){
            searchArg.matcher.and(HdNextGameDef.Info.NAME, ParamMatcher.EQ, name);
        }
        searchArg.totalSize = new Ref<Integer>();
        if(page != -1 && limit != -1){
			searchArg.start = (page - 1) * limit;
			searchArg.limit = limit;
		}
		searchArg.cmpor = new ParamComparator(HdNextGameDef.Info.ID, true);

		FaiList<Param> list = new FaiList<Param>();
		int rt = hdOss.getNextGameList(searchArg, list);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
			Log.logErr(rt, "get list err");
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}

		FaiList<Integer> idList = new FaiList<Integer>();
		for(Param item : list){
		    int id = item.getInt(HdNextGameDef.Info.ID, 0);
		    idList.add(id);
        }

        FaiList<Param> voteNumList = new FaiList<Param>();
		rt = hdOss.getNextGameVoteNum(idList, voteNumList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "get voteNumList err");
            return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
        }

        Map<String, Integer> voteNumMap = new HashMap<String, Integer>();
        for(Param item : voteNumList){
            int nextGameId = item.getInt(HdNextGameVoteDef.Info.NEXT_GAME_ID);
            int type = item.getInt(HdNextGameVoteDef.Info.TYPE);
            int num = item.getInt(HdNextGameVoteDef.Info.NUM);

            String key = nextGameId+"_"+type;
            voteNumMap.put(key, num);
        }
        for(Param item : list){
            int id = item.getInt(HdNextGameDef.Info.ID);
            String agreeKey = id+"_0";
            String negativeKey = id+"_1";

            int agreeNum = 0;
            int negativeNum = 0;
            if(voteNumMap.containsKey(agreeKey)){
                agreeNum = voteNumMap.get(agreeKey);
            }
            if(voteNumMap.containsKey(negativeKey)){
                negativeNum = voteNumMap.get(negativeKey);
            }
            item.setInt("agreeNum", agreeNum);
            item.setInt("negativeNum", negativeNum);
        }

		rt = Errno.OK;
		return new HdGameDef.ErrorInfo(rt, "list", list).add("totalSize", searchArg.totalSize.value).toString();
	}

	private String exportNextGameVote(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception{
	    int id = Parser.parseInt(request.getParameter("id"), 0);

		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdNextGameVoteDef.Info.NEXT_GAME_ID, ParamMatcher.EQ, id);

		FaiList<Param> list = new FaiList<Param>();
		int rt = hdOss.getNextGameVoteListFromDB(searchArg, list);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
			Log.logErr(rt, "get voteNumList err");
			return HdGameDef.ErrorInfo.getErrInfo(rt, "msg", "系统错误");
		}

		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		FaiList<Param> voteList = new FaiList<Param>();
		for(Param item : list){
		    Param vote = new Param();
		    vote.assign(item, HdNextGameVoteDef.Info.AID);
		    int type = item.getInt(HdNextGameVoteDef.Info.TYPE);
			if (type == HdNextGameVoteDef.Type.AGREE) {
				vote.setString("vote", "点赞");
			}else{
				vote.setString("vote", "点踩");
			}
			Calendar calendar = item.getCalendar(HdNextGameVoteDef.Info.CREATE_TIME);
			vote.setString("voteTime", simpleDateFormat.format(calendar.getTime()));
			voteList.add(vote);
		}

		FaiList<Param> colList = new FaiList<Param>();
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "aid");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "vote");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "voteTime");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
		Param p = new Param();
		p.setString("aid", "aid");
		p.setString("vote", "投票");
		p.setString("voteTime", "投票时间");
		rowList.add(p);
		rowList.addAll(voteList);
		FaiList<Param> optionList = new FaiList<Param>();
		Param option = null;
		int outputCount = rowList.size();
		if (outputCount <= 65535) {                                                                                        //65536是office2003 sheet的行上限
			option = new Param();
			option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet");
			option.setList(MSOfficeConverter.Option.COL_LIST, colList);
			option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
			optionList.add(option);
		} else {
			//xls一张sheet只能存储65536条数据，因此超出分成多张sheet
			for (int i = 1; i <= outputCount; i = i + 65535) {
				option = new Param();
				int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
				option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet" + i + "~" + (indexCount - 1));
				option.setList(MSOfficeConverter.Option.COL_LIST, colList);
				FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
				sheetData.add(0, p);                                                                                       //在每个工作表首行加上列名字段
				option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
				optionList.add(option);
			}
		}


		String name = "预约活动投票记录-"+id;
		String fileName =  name+ Parser.parseDateString( Calendar.getInstance() ) + ".xls";
		dealBeforeConvert( request,response,out,fileName, optionList );

		return "success";
	}

	private String setHdBannerNew(HttpServletRequest request, HttpServletResponse response) throws Exception{		
		HdOss hdoss = (HdOss)WebOss.getCorpKit(Kid.HD_OSS);
		Param info = new Param();
		int id = Parser.parseInt(request.getParameter("id"), 0);//id代表设置的广告类型
		int openABTest = Parser.parseInt(request.getParameter("openABTest"), 0);
		String data = Parser.parseString(request.getParameter("data"), "{ \"dataList\": { \"A\": [], \"B\": [] }, \"ANumber\": 0, \"BNumber\": 0 }");
		
		if(id==0){
			Log.logErr("args err; id=%d", id);
			return "{\"success\":false,\"msg\":\"参数错误\"}";
		}
		info.setInt(HdBannerDef.Info.ID, id);
		info.setInt(HdBannerDef.Info.OPEN_AB_TEST, openABTest);
		info.setString(HdBannerDef.Info.DATA, data);
		int rt = hdoss.setHdBanner(info);
		Param result = new Param();
		if (rt != Errno.OK) {
			Log.logErr("set HdBanner err");
			result.setString("msg", "设置失败！");
			result.setBoolean("success", false);
		} else {
			result.setString("msg", "设置成功！");
			result.setBoolean("success", true);
		}
		result.setInt("rt", rt);
		return result.toJson();
	}

	private String getHdBannerNew(HttpServletRequest request, HttpServletResponse response) throws Exception{		
		int id = Parser.parseInt(request.getParameter("id"), 0);//id代表设置的广告类型
		if(id==0){
			Log.logErr("args err; id=%d", id);
			return "{\"success\":false,\"msg\":\"参数错误\"}";
		}
		HdOss hdoss = (HdOss)WebOss.getCorpKit(Kid.HD_OSS);
		Log.logDbg("greakjackZWJ hdoss="+hdoss);
		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdBannerDef.Info.ID, ParamMatcher.EQ, id);
		searchArg.limit = 1;
		FaiList<Param> infoList = new FaiList<Param>();
		int rt = hdoss.getHdBanner(searchArg, infoList);
		Log.logDbg("greakjack rt="+rt);
		Param result = new Param();
		if (rt == Errno.OK && infoList.size() > 0) {
			result.setBoolean("success", true);
			result.setParam("data", infoList.get(0));
			result.setInt("rt", rt);
		}else if(rt == Errno.NOT_FOUND){
			result.setBoolean("success", true);
			result.setParam("data", new Param());
			result.setInt("rt", Errno.OK);
		}else {
			result.setBoolean("success", false);
			result.setParam("data", new Param());
			result.setInt("rt", rt);
		}
		
		
		return result.toJson();
	}

	private String getCopyRecordList(HttpServletRequest request) throws Exception{
		int sourceAid = Parser.parseInt(request.getParameter("sourceAid"), -1);
		int targetAid = Parser.parseInt(request.getParameter("targetAid"), -1);
		String operator = request.getParameter("operator");
		String start = request.getParameter("startTime");
		String end =request.getParameter("endTime");

		int page = Parser.parseInt(request.getParameter("currentPage"), 1);
		int limit = Parser.parseInt(request.getParameter("limit"), 10);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher();
		searchArg.totalSize = new Ref<Integer>();
		searchArg.start = (page - 1) * limit;
		searchArg.limit = limit;
		searchArg.cmpor = new ParamComparator(HdCopyRecordDef.Info.ID, true);
		if (sourceAid != -1) {
		    searchArg.matcher.and(HdCopyRecordDef.Info.SOURCE_AID, ParamMatcher.EQ, sourceAid);
		}
		if (targetAid != -1) {
		    searchArg.matcher.and(HdCopyRecordDef.Info.TARGET_AID, ParamMatcher.EQ, targetAid);
		}
		if(!Str.isEmpty(operator)){
		    searchArg.matcher.and(HdCopyRecordDef.Info.OPERATOR, ParamMatcher.EQ, operator);
		}
		if(!Str.isEmpty(start) && !Str.isEmpty(end)){
		    Calendar startTime = Parser.parseCalendar(start + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
			Calendar endTime = Parser.parseCalendar(end+ " 23:59:59", "yyyy-MM-dd HH:mm:ss");

		    searchArg.matcher.and(HdCopyRecordDef.Info.CREATE_TIME, ParamMatcher.GE, startTime);
		    searchArg.matcher.and(HdCopyRecordDef.Info.CREATE_TIME, ParamMatcher.LE, endTime);
		}

		FaiList<Param> list = new FaiList<Param>();
		HdOss hdoss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
		int rt = hdoss.getCopyRecordList(searchArg, list);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
			return "{\"success\":false,\"msg\":\"系统错误\"}";
		}
		return new HdGameDef.ErrorInfo(rt, "list", list).add("totalSize", searchArg.totalSize.value).toString();
	}

	private FaiList<Param> getTradeRecordList(HttpServletRequest request, SearchArg searchArg) throws Exception{
		int rt = Errno.OK;
		int aid = Parser.parseInt(request.getParameter("aid"), 0);
		String startTime = request.getParameter("startTime");
		String endTime = request.getParameter("endTime");

		searchArg.matcher = new ParamMatcher();
		searchArg.totalSize = new Ref<Integer>();
		searchArg.cmpor = new ParamComparator(HdCopyRecordDef.Info.CREATE_TIME, true);

		if (aid > 0) {
			searchArg.matcher.and(HdTradeRecordDef.Info.AID, ParamMatcher.EQ, aid);
		}
		if (!Str.isEmpty(startTime) && !Str.isEmpty(endTime)) {
			searchArg.matcher.and(HdTradeRecordDef.Info.CREATE_TIME, ParamMatcher.GE, Parser.parseCalendar(startTime + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));
			searchArg.matcher.and(HdTradeRecordDef.Info.CREATE_TIME, ParamMatcher.LE, Parser.parseCalendar(endTime + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
		}

		HdOss ho = (HdOss)Core.getCorpKit(1, Kid.HD_OSS);
		FaiList<Param> dataList = new FaiList<Param>();
		rt = ho.getHdTradeRecordListFromDB(new Param(), searchArg, dataList);
		if (rt != Errno.OK) {
			App.logErr(rt, "getHdTradeRecordListFromDB err aid = %s, searchArg.matcher = %s;", aid, searchArg.matcher);
			return dataList;
		}
		HdProf hdPorf = (HdProf)Core.getCorpKit(1, Kid.HD_PROF);
		CorpProfCli cli = new CorpProfCli(Core.getFlow());
		if (!cli.init()) {
			throw new WebException("CorpProfCli init error");
		}
		FaiList<Param> tradeList = HdModelTradeDef.getTradeList();
		SysBssStat sysBssStat = (SysBssStat)Core.getSysKit(Kid.SYS_BSS_STAT);
		for(Param data : dataList){
			int trade = data.getInt(HdTradeRecordDef.Info.TRADE, 0);
			int trade2 = data.getInt(HdTradeRecordDef.Info.TRADE2, 0);
			String tradeName = "无";
			String trade2Name = "无";
			for (int i = 0; i < tradeList.size(); i++) {
				boolean hasFound = false;
				Param item = tradeList.get(i);
				if (item.getInt("id", 0) == trade) {
					hasFound = true;
					tradeName = item.getString("name", "无");
					FaiList<Param> trade2List = item.getList("trade2List", new FaiList<Param>());
					for (int j = 0; j < trade2List.size(); j++) {
						Param item2 = trade2List.get(j);
						if (item2.getInt("id", 0) == trade2) {
							trade2Name = item2.getString("name", "无");
							break;
						}
					}
					if (hasFound) {
						break;
					}
				}
			}
			data.setString("tradeName", tradeName);
			data.setString("trade2Name", trade2Name);
			Param corpProf = new Param();
			int tmpAid = data.getInt(HdTradeRecordDef.Info.AID, 0);
			cli.getProfInfo(tmpAid, corpProf);
			int ta = corpProf.getInt(CorpProfDef.Info.REG_SOURCE);
			SearchArg searchArg1 = new SearchArg();
			searchArg1.matcher = new ParamMatcher(BssStatDef.TaInfo.TA, ParamMatcher.EQ, ta);

			FaiList<Param> taList = sysBssStat.getTaList(searchArg1);
			if(taList.size()>0){
				String siteSourceName = taList.get(0).getString("taName");
				siteSourceName += "("+ta+")";
				data.setString("siteSourceName", siteSourceName);
				
			}
//			Param bssAcctInfo = sysBssStat.getAcctStatusInfo(tmpAid);
//			int regBiz = bssAcctInfo.getInt(BssStatDef.Info.REG_BIZ_2, 0);

			//迁移到新的查询方式
			ResultSet execute = FdpDataSDK.newOLTPQuery()
					.select("*")
					.from("fdpData", "dws_fkw_acct_info")
					.where(AND.expr(EQ.of("aid", aid)))
					.execute(Core.getFlow());
			Log.logDbg("execute:"+execute);
			Param bssAcctInfo = execute.getData().get(0);
			int regBiz = bssAcctInfo.getInt(BssStatDef.Info.REG_BIZ_2, 0);

			String regBizName = BssStatDef.RegBiz2.getRegBiz2Name(regBiz);
			data.setString("regBizName", regBizName);
			Param prof = hdPorf.getProf(tmpAid);
			Param hostInfo = Param.parseParam(prof.getString(HdProfDef.Info.HOSTINFO), new Param());
			data.setString("hostName", Encoder.decodeHtml(hostInfo.getString(HdProfDef.HostInfo.HOST_NAME)));
		}

		return dataList;
	}

	private String getTradeRecordList(HttpServletRequest request) throws Exception{
		SearchArg searchArg = new SearchArg();

		FaiList<Param> dataList = getTradeRecordList(request, searchArg);

		return new HdGameDef.ErrorInfo(Errno.OK, "dataList", dataList).add("totalSize", searchArg.totalSize.value).toString();
	}

	private String exportTradeRecord(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception{
		SearchArg searchArg = new SearchArg();

		FaiList<Param> dataList = getTradeRecordList(request, searchArg);

		FaiList<Param> colList = new FaiList<Param>();
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "aid");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "tradeName");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "trade2Name");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "hostName");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "siteSourceName");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}
		{
			Param k = new Param();
			k.setString(MSOfficeConverter.Col.KEY, "regBizName");
			k.setInt(MSOfficeConverter.Col.WIDTH, 20);
			colList.add(k);
		}

		FaiList<Param> rowList = new FaiList<Param>();
		Param p = new Param();
		p.setString("aid", "aid");
		p.setString("tradeName", "所属一级行业");
		p.setString("trade2Name", "所属二级行业");
		p.setString("hostName", "主办单位");
		p.setString("siteSourceName", "注册来源");
		p.setString("regBizName", "注册产品");
		rowList.add(p);
		rowList.addAll(dataList);

		FaiList<Param> optionList = new FaiList<Param>();
		Param option = null;
		int outputCount = rowList.size();
		if (outputCount <= 65535) {
			option = new Param();
			option.setString(MSOfficeConverter.Option.SHEET_NAME, "行业信息记录");
			option.setList(MSOfficeConverter.Option.COL_LIST, colList);
			option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
			optionList.add(option);
		} else {
			for (int i = 1; i <= outputCount; i = i + 65535) {
				option = new Param();
				int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
				option.setString(MSOfficeConverter.Option.SHEET_NAME, "行业信息记录" + i + "~" + (indexCount - 1));
				option.setList(MSOfficeConverter.Option.COL_LIST, colList);
				FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
				sheetData.add(0, p);
				option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
				optionList.add(option);
			}
		}

		String fileName =  "行业信息记录-" + Parser.parseDateString(Calendar.getInstance()) + ".xls";
		dealBeforeConvert(request, response, out, fileName, optionList);

		return "success";
	}
	
	private String getHdCaseSelectData(HttpServletRequest request) throws Exception{
		int rt = 0;
		String tradeStr = "综合电商,商超便利,日用百货,户外用品,纺织辅料,乐器,服装,美容护肤,珠宝饰品,鞋帽箱包,母婴专区,玩具礼品,餐饮服务,生活服务,旅游,酒店,摄影,宠物,工商法务,房地产,图书教材,技能培训,互联网,幼教早教" +
		",生鲜蔬果,农产品,鲜花植物,手机数码,家装家居,家电办公,家居家纺,数码配件,电脑,灯光照明,食品饮料,茶叶酒类,建材物料,五金,机械设备,仪器器材,安防监控" + 
		",汽车汽配,汽车服务,医药保健品,中医理疗,印刷包装,园林园艺,展览设计,文化传媒";
		String sceneStr = "品牌传播,获客拉新,付费转化,留存活跃";
		String lableStr = "活动抽奖,裂变引流,商业促销,长期活动,投票活动,答题活动,H5活动,现场活动";
//		String lableStr = "游戏抽奖,裂变引流,商业促销,长期活动,投票活动,答题活动,H5活动,粉丝专属,联系信息,红包奖品,活动密码,参与地区限制,视频介绍,支付后兑奖,组队参与,周期抽奖";
		FaiList<String> tradeList = new FaiList<String>();
		tradeList.addAll(Arrays.asList(tradeStr.split(",")));
		FaiList<String> sceneList = new FaiList<String>();
		sceneList.addAll(Arrays.asList(sceneStr.split(",")));
		FaiList<String> lableList = new FaiList<String>();
		lableList.addAll(Arrays.asList(lableStr.split(",")));
		FaiList<String> prototypeList = new FaiList<String>();
		prototypeList.addAll(BssHdPuBUseTypeDef.DealUtil.initGameStyle().values());
		Param data = new Param();
		data.setList("tradeList", tradeList);
		data.setList("sceneList", sceneList);
		data.setList("lableList", lableList);
		data.setList("prototypeList", prototypeList);
		return HdGameDef.ErrorInfo.getErrInfo(rt, data);
	}
	
	private String getHdCaseList(HttpServletRequest request) throws Exception {
		int sid = Session.getSid();
		int rt = 0;
		if (sid <= 0) {
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "未登陆");
		}
		int page = Parser.parseInt(request.getParameter("page"), 1);
		int limit = Parser.parseInt(request.getParameter("limit"), 10);
		int start = (page - 1) * limit;
		String trade = request.getParameter("trade");
		String scene = request.getParameter("scene");
		String label = request.getParameter("label");
		String activeFunc = request.getParameter("activeFunc");
		String keyWork = request.getParameter("keyWork");
		long bt = Parser.parseLong(request.getParameter("startTime"), 0L);
		long et = Parser.parseLong(request.getParameter("endTime"), 0L);
		boolean isCollect = Parser.parseBoolean(request.getParameter("isCollect"), false);
		boolean isPreViewCase = Parser.parseBoolean(request.getParameter("isPreViewCase"), false);
		String prototype = request.getParameter("prototypeName");
		Calendar beginTime = Parser.parseCalendar(new Date(bt));
		Calendar endTime = Parser.parseCalendar(new Date(et));
		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);
		SearchArg searchCollectArg = new SearchArg();
		searchCollectArg.matcher = new ParamMatcher(HdCaseDef.Info4Collect.SID, ParamMatcher.EQ, sid);
		FaiList<Param> collectList = new FaiList<Param>();
		hdOss.getHdCase4CollectListFromDB(new Param(), searchCollectArg, collectList);
		Param collectInfo = new Param();
		if (collectList.size() > 0) {
			collectInfo = collectList.get(0);
		}
		FaiList<Integer> hasCollectList = FaiList.parseIntList(collectInfo.getString(HdCaseDef.Info4Collect.COLLECTLIST), new FaiList<Integer>());

		FaiList<Param> list = new FaiList<Param>();
		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdCaseDef.Info.CREATE_TIME, ParamMatcher.GE, beginTime);
		searchArg.matcher.and(HdCaseDef.Info.CREATE_TIME, ParamMatcher.LE, endTime);
		if (isCollect) {
			searchArg.matcher.and(HdCaseDef.Info.ID, ParamMatcher.IN, hasCollectList);
		}
		if (!Str.isEmpty(trade)) {
			searchArg.matcher.and(HdCaseDef.Info.TRADE, ParamMatcher.LK, trade);
		}
		if (!Str.isEmpty(scene)) {
			searchArg.matcher.and(HdCaseDef.Info.SCENE, ParamMatcher.LK, scene);
		}
		if (!Str.isEmpty(activeFunc)) {
			searchArg.matcher.and(HdCaseDef.Info.ACTIVITY_FUNC, ParamMatcher.LK, activeFunc);
		}
		if (!Str.isEmpty(label)) {
			ParamMatcher orMatcher = new ParamMatcher();
			for (String str : label.split(",")) {
				orMatcher.or(HdCaseDef.Info.LABEL, ParamMatcher.LK, str);
			}
			searchArg.matcher.and(orMatcher);
		}
		if (!Str.isEmpty(keyWork)) {
			ParamMatcher orMatcher = new ParamMatcher(HdCaseDef.Info.O_ACCT_NAME, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.GAME_NAME, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.TRADE, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.SCENE, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.LABEL, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.ACTIVITY_FUNC, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.ACTIVITY_BRIGHT, ParamMatcher.LK, keyWork);
			orMatcher.or(HdCaseDef.Info.ACTIVITY_EFFECT, ParamMatcher.LK, keyWork);
			searchArg.matcher.and(orMatcher);
		}
		if(isPreViewCase){
			searchArg.matcher.and(HdCaseDef.Info.FLAG, ParamMatcher.LAND, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW);
		}
		if (!Str.isEmpty(prototype) && isPreViewCase) {
			searchArg.matcher.and(HdCaseDef.Info.PROTOTYPE_ID, ParamMatcher.EQ, BssHdPuBUseTypeDef.DealUtil.getStyleByName(prototype));
		}
		searchArg.totalSize = new Ref<Integer>();
		searchArg.limit = limit;
		searchArg.start = start;
		searchArg.cmpor = new ParamComparator(HdCaseDef.Info.CREATE_TIME, true);
		Log.logDbg("liqin aaa=%s", searchArg.matcher);
		hdOss.getHdCaseListFromDB(new Param(), searchArg, list);
		if (CollectionUtil.isEmpty(list)) {
			list = new FaiList<Param>();
		}

		// 默认统计类别
		String commonStatType = HdStatDef.StatLogs.StatType.NUM_PV + " ".concat(HdStatDef.StatLogs.StatType.NUM_PTCP + " ")
				.concat(HdStatDef.StatLogs.StatType.NUM_SHARE + " ");
		HdStat oHdStat = (HdStat) WebHdOss.getCorpKit(Kid.HD_STAT);
		HdGame hdGame = (HdGame) WebHdOss.getCorpKit(Kid.HD_GAME);
		FaiList<String> fields = new FaiList<String>();
		fields.add(HdGameDef.Info.STYLE);

		Param selectOption = new Param();
		// 默认时间拿现在的前后十年
		selectOption.setInt(HdStatDef.SelectOption.START_TIME, (int) (System.currentTimeMillis() / 1000) - (60 * 60 * 24 * 3650));
		selectOption.setInt(HdStatDef.SelectOption.END_TIME, (int) (System.currentTimeMillis() / 1000) + (60 * 60 * 24 * 3650));
		selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, commonStatType);
		selectOption.setInt(HdStatDef.SelectOption.TIME_TYPE, HdStatDef.SelectOption.TimeType.DAY);
		selectOption.setInt(HdStatDef.SelectOption.CANAL, -1);
		for (Param info : list) {
			int oAid = info.getInt(HdCaseDef.Info.O_AID, 0);
			int oGameId = info.getInt(HdCaseDef.Info.O_GAME_ID, 0);
			int gameId = info.getInt(HdCaseDef.Info.GAME_ID,0);
			int id = info.getInt(HdCaseDef.Info.ID);
			int flag = info.getInt(HdCaseDef.Info.FLAG, 0);
			info.setBoolean("hasCollect", hasCollectList.contains(id));
			info.setBoolean("isPut", Misc.checkBit(flag, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW));
			info.setBoolean("isPutOnShelf", Misc.checkBit(flag, HdCaseDef.Flag.IS_PUT_ON_SHELF));
			info.setString("prototypeName", BssHdPuBUseTypeDef.DealUtil.getStyleName(info.getInt(HdCaseDef.Info.PROTOTYPE_ID)));

			//统计原活动数据
			Param oGameInfo = hdGame.getGameInfoByField(oAid, oGameId, fields);
			selectOption.setInt(HdStatDef.SelectOption.AID, oAid);
			selectOption.setInt(HdStatDef.SelectOption.GAME_ID, oGameId);
			// 拿传播数据里面的参与人数，分享人数，浏览人数, 不从hdgame拿是为了保持跟传播数据一致，不然必来工单。
			FaiList<Param> selectResultList = new FaiList<Param>();
			rt = oHdStat.selectData(selectOption, selectResultList);
			if (rt != Errno.OK){
				Log.logErr("get hdStat info err aid=%s,gameid=%s",oAid,oGameId);
			}
			countGameRecord(oGameInfo,selectResultList,info,selectOption,false);

			//统计案例活动数据
			int caseAid = 24265546;
			Param gameInfo = hdGame.getGameInfoByField(caseAid, gameId, fields);
			selectOption.setInt(HdStatDef.SelectOption.AID,caseAid);
			selectOption.setInt(HdStatDef.SelectOption.GAME_ID, gameId);
			selectResultList.clear();
			rt = oHdStat.selectData(selectOption, selectResultList);
			if (rt != Errno.OK){
				Log.logErr("get hdStat info err aid=%s,gameid=%s",caseAid,gameId);
			}
			countGameRecord(gameInfo,selectResultList,info,selectOption,true);


			info.setString(HdCaseDef.Info.O_GAME_ID, "HD" + info.getInt(HdCaseDef.Info.O_GAME_ID));
			info.setString(HdCaseDef.Info.GAME_ID, "HD" + info.getInt(HdCaseDef.Info.GAME_ID));
		}
		return HdGameDef.ErrorInfo.getErrInfo(rt, searchArg.totalSize.value, list);
	}

	/**
	 * 统计活动的参与人数，分享人数，浏览人数
	 * @param gameInfo
	 * @param selectResultList
	 * @param info
	 * @param selectOption 需要重置statType
	 * @param isCaseGame 是否案例游戏
	 */
	private void countGameRecord(Param gameInfo,FaiList<Param> selectResultList,Param info,Param selectOption,boolean isCaseGame){
		if (!Str.isEmpty(gameInfo)) {
			int style = gameInfo.getInt(HdGameDef.Info.STYLE);
			// 关注红包只有关注人数和领取人数，产品只要关注人数
			if (style == HdGameDef.Style.GZHB) {
				selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, HdStatDef.StatLogs.StatType.NUM_SUBSCRIBE + " ");
			}
		}
		for (Param result : selectResultList) {
			int statType = result.getInt(HdStatDef.SelectResult.STAT_TYPE, -1);
			String sdl = result.getString(HdStatDef.SelectResult.SELECT_DATA_LIST, "");
			int num = 0;
			try {
				@SuppressWarnings("rawtypes")
				List<FaiList> faiLists = JSONArray.parseArray(sdl, FaiList.class);
				Log.logDbg("SELECT_DATA_LIST : %s", faiLists);
				for (@SuppressWarnings("rawtypes") FaiList faiList : faiLists) {
					num += (Integer) faiList.get(1);
				}
			} catch (ClassCastException cce) {
				num = 0;
			}
			if (isCaseGame) {
				switch (statType) {
					case HdStatDef.StatLogs.StatType.NUM_PV:
						info.setInt(HdCaseDef.Other.CASE_VIEW, num);
						break;
					case HdStatDef.StatLogs.StatType.NUM_SUBSCRIBE:
						// 重置statType
						selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, "1 2 4");
					case HdStatDef.StatLogs.StatType.NUM_PTCP:
						info.setInt(HdCaseDef.Other.CASE_PLAYER, num);
						break;
					case HdStatDef.StatLogs.StatType.NUM_SHARE:
						info.setInt(HdCaseDef.Other.CASE_SHARE, num);
				}
			}else {
				switch (statType) {
					case HdStatDef.StatLogs.StatType.NUM_PV:
						info.setInt(HdGameDef.Info.VIEW, num);
						break;
					case HdStatDef.StatLogs.StatType.NUM_SUBSCRIBE:
						// 重置statType
						selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, "1 2 4");
					case HdStatDef.StatLogs.StatType.NUM_PTCP:
						info.setInt(HdGameDef.Info.PLAYER, num);
						break;
					case HdStatDef.StatLogs.StatType.NUM_SHARE:
						info.setInt(HdGameDef.Info.SHARE, num);
				}
			}
		}
	}

	private String getHdCaseInputRecordList(HttpServletRequest request) throws Exception{
		int sid = Session.getSid();
		int rt = 0;
		if(sid <= 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "未登陆");
		}
		int page = Parser.parseInt(request.getParameter("page"),1);
		int limit = Parser.parseInt(request.getParameter("limit"),10);
		int start = ( page - 1 ) * limit;
		long bt = Parser.parseLong(request.getParameter("startTime"), 0L);
		long et = Parser.parseLong(request.getParameter("endTime"), 0L);
		Calendar beginTime = Parser.parseCalendar(new Date(bt));
		Calendar endTime = Parser.parseCalendar(new Date(et));

		HdOss hdOss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
		FaiList<Param> list = new FaiList<Param>();
		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdCaseDef.Info4Record.CREATE_TIME, ParamMatcher.GE, beginTime);
		searchArg.matcher.and(HdCaseDef.Info4Record.CREATE_TIME, ParamMatcher.LE, endTime);
		searchArg.totalSize = new Ref<Integer>();
		searchArg.limit = limit;
		searchArg.start = start;
		searchArg.cmpor = new ParamComparator(HdCaseDef.Info4Record.CREATE_TIME, true);
		hdOss.getHdCase4RecordListFromDB(new Param(), searchArg, list);
		if(list == null){
			list = new FaiList<Param>();
		}
		for(Param info : list){
			int tempSid = info.getInt(HdCaseDef.Info4Record.SID);
			String statusView = "总案例数" + info.getInt(HdCaseDef.Info4Record.TOTAL) + " 成功数" + info.getInt(HdCaseDef.Info4Record.SUCCESSNUM) + " 失败数" + info.getInt(HdCaseDef.Info4Record.FAILNUM);
			info.setString("statusView", statusView);
			Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), tempSid);
			info.setString("staffName", staffInfo.getString(StaffDef.Info.NAME));
		}
		return HdGameDef.ErrorInfo.getErrInfo(rt, searchArg.totalSize.value, list);
	}
	
	private String setHdCaseCollect(HttpServletRequest request) throws Exception{
		int sid = Session.getSid();
		int rt = 0;
		if(sid <= 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "未登陆");
		}
		int id = Parser.parseInt(request.getParameter("id"), -1);
		if(id < 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "没选中要收藏的");
		}
		HdOss hdOss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);
		SearchArg searchCollectArg = new SearchArg();
		
		searchCollectArg.matcher = new ParamMatcher(HdCaseDef.Info4Collect.SID, ParamMatcher.EQ, sid);
		FaiList<Param> collectlist = new FaiList<Param>();
		hdOss.getHdCase4CollectListFromDB(new Param(), searchCollectArg, collectlist);
		Param collectInfo = new Param();
		if(collectlist.size() > 0){
			collectInfo = collectlist.get(0);
		}
		FaiList<Integer> hasCollectlist = FaiList.parseIntList(collectInfo.getString(HdCaseDef.Info4Collect.COLLECTLIST), new FaiList<Integer>());
		if(hasCollectlist.contains(id)){
			hasCollectlist.remove(new Integer(id));
		}else{
			hasCollectlist.add(new Integer(id));
		}
		Param info = new Param();
		info.setInt(HdCaseDef.Info4Collect.SID, sid);
		info.setString(HdCaseDef.Info4Collect.COLLECTLIST, hasCollectlist.toJson());
		rt = hdOss.addHdCase4Collect(info);
		return HdGameDef.ErrorInfo.getErrInfo(rt);
	}
	private String getGameIdByUrl(HttpServletRequest request, HttpServletResponse response) throws Exception{
		Fdp.bssMonitor(10108);
		int rt = Errno.ERROR;

		// 处理agenthome的跨域
		String requestOrigin = request.getHeader("Origin");
		String agentSiteDomain = Web.getAgentSiteDomain();
		String kcHomeDomain = WebKchome.getAgentKcHomeDomain();

		Log.logStd("requestOrigin = %s", requestOrigin);
		if (!Web.isFaiHost(requestOrigin) && !requestOrigin.endsWith(agentSiteDomain) && !requestOrigin.endsWith(kcHomeDomain)) {
			Log.logErr(rt, "args  requestOrigin=%s", requestOrigin);
			return HdGameDef.ErrorInfo.getErrInfo(rt, "非法域名");
		}
		response.setHeader("Access-Control-Allow-Credentials", "true");

		String url = request.getParameter("url");
		rt = Errno.ARGS_ERROR;
		if(Str.isEmpty(url)){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "参数错误");
		}
		String[] arr = url.split("/");
		if(arr.length < 5){
		    // 小程序的
            if (url.contains("scene")) {
                String[] param = url.split("[?]");
                Log.logDbg("param : %s", Arrays.toString(param));
                String[] arrSplitEqual = param[1].split("[=]");
                Log.logDbg("arrSplitEqual : %s", Arrays.toString(arrSplitEqual));
                String[] scene = arrSplitEqual[1].split("[-]");
                Log.logDbg("scene : %s", Arrays.toString(scene));
                String aid = scene[0];
                String gameId = scene[1];

                Param data = new Param();
                data.setInt("aid", Integer.valueOf(aid));
                data.setInt("gameId", Integer.valueOf(gameId));
                rt = Errno.OK;
                return HdGameDef.ErrorInfo.getErrInfo(rt, data);
            }
			return HdGameDef.ErrorInfo.getErrInfo(rt, "复制失败, 找不到原活动链接, 请再次确认");
		}
		String urlToken = arr[4];
		Log.logDbg("urlToken = %s",urlToken);
		Ref<Integer> aidRef = new Ref<Integer>();
		Ref<Integer> gameIdRef = new Ref<Integer>();
		rt = HdTool.decryptGameId(urlToken, aidRef, gameIdRef);
		if(rt != Errno.OK){
			return HdGameDef.ErrorInfo.getErrInfo(rt, "复制失败, 找不到原活动链接, 请再次确认2");
		}

		Param data = new Param();
		data.setInt("aid",aidRef.value);
		data.setInt("gameId",gameIdRef.value);
		return HdGameDef.ErrorInfo.getErrInfo(rt, data);
	}
	private String deleteHdCase(HttpServletRequest request) throws Exception{
		int sid = Session.getSid();
		int rt = 0;
		if(sid <= 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "未登陆");
		}
		int aid = Parser.parseInt(request.getParameter("aid"), 0);
		int gameId = Parser.parseInt(request.getParameter("gameId"), 0);

		if(aid <= 0 || gameId <= 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "参数错误");
		}
		HdOss hdOss = (HdOss)WebHdOss.getCorpKit(Kid.HD_OSS);

		rt = hdOss.delHdCase(aid, gameId);
		return HdGameDef.ErrorInfo.getErrInfo(rt);
	}
%>


<%
    String output = "";
    try{
		/*if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_OSS_LOGIN)){
			out.print("{\"success\":false, \"msg\":\"没有权限_cmd\"}");
			return;
		}*/

        String cmd = request.getParameter("cmd");    
		if (cmd == null) {
			return;
		}else if (cmd.equals("genErrorCode")){
			output = genErrorCode(request);
		}else if (cmd.equals("getErrorCodeList")){
			output = getErrorCodeList(request);
		}else if (cmd.equals("deleteErrorCode")){
			output = deleteErrorCode(request);
		}else if (cmd.equals("searchErrorCode")){
			output = searchErrorCode(request);
		}else if(cmd.equals("getResearchQuestionsList4Oss")){
            output = getResearchQuestionsList4Oss(request);
        }else if(cmd.equals("getResearchQuestions4Oss")){
            output = getResearchQuestions4Oss(request);
        }else if(cmd.equals("getResearchQuestionsByType4Oss")){
            output = getResearchQuestionsByType4Oss(request);
        }else if(cmd.equals("exportResearch")){
            output = exportResearch(request, response, out);
        }else if(cmd.equals("getMsgRecordList")){
            output = getMsgRecordList(request);
        }else if(cmd.equals("exportMsgRecordList")){
            output = exportMsgRecordList(request, response, out);
        }else if(cmd.equals("addQuestions")){
			output = addQuestions(request);
		}else if(cmd.equals("addNextGame")){
			output = addNextGame(request);
		}else if(cmd.equals("getNextGameList")){
			output = getNextGameList(request);
		}else if(cmd.equals("setNextGame")){
			output = setNextGame(request);
		}else if(cmd.equals("delNextGame")){
			output = delNextGame(request);
		}else if(cmd.equals("getHistoryNextGameList")) {
			output = getHistoryNextGameList(request);
		}else if(cmd.equals("exportNextGameVote")){
			output = exportNextGameVote(request, response, out);
		}else if(cmd.equals("setHdBannerNew")){
			output = setHdBannerNew(request,response);
		}else if(cmd.equals("getHdBannerNew")){
			output = getHdBannerNew(request,response);
		}else if(cmd.equals("getCopyRecordList")){
			output = getCopyRecordList(request);
		}else if(cmd.equals("getTradeRecordList")){
			output = getTradeRecordList(request);
		}else if(cmd.equals("exportTradeRecord")){
			output = exportTradeRecord(request, response, out);
		}else if(cmd.equals("getHdCaseSelectData")){
			output = getHdCaseSelectData(request);
		}else if(cmd.equals("getHdCaseList")){
			output = getHdCaseList(request);
		}else if(cmd.equals("getHdCaseInputRecordList")){
			output = getHdCaseInputRecordList(request);
		}else if(cmd.equals("setHdCaseCollect")){
			output = setHdCaseCollect(request);
		}else if(cmd.equals("deleteHdCase")){
			output = deleteHdCase(request);
		}else if(cmd.equals("getGameIdByUrl")){
			output = getGameIdByUrl(request,response);
		}
				
	}catch (Exception exp){
		PrintUtil.printStackTrace(exp,0,"markin");
		output = WebOss.checkAjaxException(exp);
	}

	out.print(output);
%>