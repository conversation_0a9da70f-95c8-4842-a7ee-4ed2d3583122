<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page session="false" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="fai.webhdoss.app.*" %>
<%@ page import="fai.webhdoss.*" %>
<%
    boolean authHdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
    String crmDomain = WebHdOss.getCrmDomain();
    boolean isFromCrm = Parser.parseBoolean(request.getParameter("_formCrm"), false);

%>
<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>不购买原因</title>
    <%@ include file="/comm/link.jsp.inc" %>
    <%@ include file="/comm/script.jsp.inc" %>
    <%--<script src="<%=HdOssResDef.getResPath("js_jquery_core")%>" type="text/javascript" charset="utf-8"></script>
    <script src="<%=HdOssResDef.getResPath("js_jquery_ui")%>" type="text/javascript" charset="utf-8"></script>--%>
    <script src="<%=HdOssResDef.getResPath("js_jquery_min")%>" type="text/javascript" charset="utf-8"></script>
</head>
<body class="saleBox">
<div class="reasonBox">
    <fai-iframe-box class="reasonBox" :submit="onSubmit" :close="onClose">
        <el-form slot="fai-iframe-data" :label-position="labelPosition" label-width="120px">
            <div v-if="isFromCrm">
                <el-form-item label="意向产品:">
                    <el-radio-group v-model="remarkForm.intentProduct" size="small">
                        <el-radio-button v-for="(item, index) in intentProductList" :key="index" :label="item.key" style="margin-top: 4px;">{{item.name}}</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="推荐版本:">
                    <el-select v-model="remarkForm.recommendVersion">
                        <el-option v-for="intent in recommendVersionList" :key="intent.value" :label='intent.name' :value="intent.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="添加情况:">
                    <el-select v-model="remarkForm.addSituation">
                        <el-option v-for="intent in addSituationList" :key="intent.value" :label='intent.label' :value="intent.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="客户意向:">
                    <el-radio-group v-model="remarkForm.sign" size="small">
                        <el-radio-button v-for="(item, index) in signList" :key="index" :label="item.sign" style="margin-top: 4px;">{{item.sign}}</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="意向度:">
                    <el-radio-group v-model="remarkForm.intentLevel" size="small">
                        <el-radio-button v-for="(item, index) in intentLevelList" :key="index" :label="item.value" style="margin-top: 4px;">{{item.label}}</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="下次联系时间:">
                    <el-date-picker
                            v-model="remarkForm.qwRemindTime"
                            type="datetime"
                            placeholder="下次联系时间"
                            value-format="yyyy-MM-dd HH:mm:ss">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="提醒内容:">
                    <el-select v-model="remarkForm.qwRemindContent">
                        <el-option v-for="intent in qwRemindContentList" :key="intent.value" :label='intent.label' :value="intent.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="快捷备注:">
                    <el-radio-group v-model="remarkForm.firstRemark" size="small" @change="firstRemarkChange">
                        <el-radio-button v-for="(item, index) in firstRemarkList" :key="index" :label="item.id" style="margin-top: 4px;">{{item.remark}}</el-radio-button>
                    </el-radio-group>
                    <template v-if="secondRemarkList.length > 0">
                        <div style="width: 100%;height: 1px;border-top: 1px solid #dcdfe6;margin: 10px 0px 6px 0px;"></div>
                        <el-radio-group v-model="remarkForm.secondRemark" size="small" @change="secondRemarkChange">
                            <el-radio-button v-for="(item, index) in secondRemarkList" :key="index" :label="item.id" style="margin-top: 4px;">{{item.remark}}</el-radio-button>
                        </el-radio-group>
                    </template>
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input type="textarea" v-model="remarkForm.remark" :rows="8"></el-input>
                </el-form-item>
            </div>

            <div v-else>
                <el-form-item label="标记:">
                    <el-select id="intent" v-model="form.intent">
                        <el-option v-for="intent in intentList" :key="intent.value" :label='intent.text'
                                   :value="intent.value"></el-option>
                    </el-select>
                </el-form-item>
                <!-- 2019.04.01 隐藏原因 -->
                <!-- 2019.04.28 说不隐藏了。。。 -->
                <el-form-item label="原因:">
                    <el-select id="reason" v-model="form.reason" @change="markReasonChange">
                        <el-option v-for="reason in reasonList" v-if="reason.fatherId == 0" :key="reason.reason"
                                   :label='reason.reason' :value="reason.reason"></el-option>
                    </el-select>
                    <a v-if="form.showReasonAdmin" :href="form.reasonHref" target="_blank">选项管理</a>
                </el-form-item>
                <el-form-item label="下次联系时间:">
                    <el-date-picker
                            v-model="form.talkNextTime"
                            type="date"
                            placeholder="下次联系时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="当前意向度:">
                    <el-radio-group v-model="form.intentLevel" size="small">
                        <el-radio-button :label="1">A</el-radio-button>
                        <el-radio-button :label="2">B</el-radio-button>
                        <el-radio-button :label="3">C</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="备注:">
                    <el-input type="textarea" v-model="form.corpMark" :rows="8"></el-input>
                </el-form-item>
            </div>
        </el-form>
    </fai-iframe-box>
</div>
<script type="text/javascript">
    document.domain = "<%=Web.getPortalDomain()%>";
    var url = location.search; //获取url中"?"符后的字串
    console.info(url);
    var theRequest = new Object();
    if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
            theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
        }
    }
    var aid = theRequest.aid;
    var reasonBox = new Vue({
        el: '.reasonBox',
        data: {
            form: {
                aid: aid,
                corpMark: ''
            },
            intentList: [],
            reasonList: [],
            markList: [],
            labelPosition: 'right',
            dialogVisible: false,
            hdIntent: "-",
            isFromCrm: <%=isFromCrm%>,
            signList: [],
            allRemarkList: [],
            intentProductList: [],
            otherIntentOptions: [],
            addSituationList: [
                {value: 1, label: "微信未通过"},
                {value: 2, label: "微信已通过"},
                {value: 3, label: "微信主动添加"},
                {value: 4, label: "QQ未通过"},
                {value: 5, label: "QQ已通过"},
                {value: 6, label: "QQ主动添加"},
                /*{value: 7, label: "未通过"},
                {value: 8, label: "仅通过，无其他添加情况"}*/
            ],
            intentLevelList: [
                {value: 1, label: "A"},
                {value: 2, label: "B"},
                {value: 3, label: "C"},
            ],
            qwRemindContentList: [
                {value: 0, label: "现在不方便，等会儿联系。"},
                {value: 1, label: "意向购买需要回访。"},
                {value: 2, label: "下了订单，联系不上。"},
                {value: 3, label: "其他，看销售系统备注。"},
            ],
            recommendVersionList: [],
            firstRemarkList: [],
            secondRemarkList: [],
            remarkForm: {}
        },
        created: function () {
            if(this.isFromCrm){
                this.getCusRemarkConfigList();
            }else {
                this.getHdInfo(aid);
            }
        },
        mounted: function () {
            var thiz = this;
            Fai.http.post("hdSale_h.jsp?cmd=getHdInfo", {
                aid: thiz.form.aid,
            }, false).then(result => {
                if (result.success) {
                    var data = result.data;
                    for (var k in data) {
                        thiz.$set(thiz.form, k, data[k]);
                    }
                }
            });

            Fai.http.post("hdSale_h.jsp?cmd=getHdIntentList", "", false).then(result => {
                if (result.success) {
                    if (result.success) {
                        thiz.intentList = result.data;
                    }
                }
            });

            Fai.http.post("hdSale_h.jsp?cmd=getHdReasonList", "", false).then(result => {
                if (result.success) {
                    if (result.success) {
                        thiz.reasonList = result.data;
                    }
                }
            });
            // 通知父窗口已经加载完数据
            Fai.iframe.notify(thiz.$el);
        },
        watch: {
            remarkForm: {
                handler: function (valOld, valNew) {
                    var that = this;
                    // console.log(valNew.intentProduct);

                    // 意向产品
                    $.each(that.intentProductList, function (index, item) {
                        if (valNew.intentProduct == item.key) {
                            that.recommendVersionList = item.value || [];
                        }
                    });

                    // 客户意向
                    var currentSignName = valNew.sign || '';
                    $.each(that.signList, function (index, item) {
                        if (currentSignName && currentSignName == item.sign) {
                            that.firstRemarkList = item.children || [];
                        }
                    });
                },
                deep: true,
                // immediate: true
            }
        },
        methods: {
            firstRemarkChange: function(){
                this.secondRemarkList = [];
                this.remarkForm.remark = "";
                this.$set(this.remarkForm, "secondRemark", undefined)
                // 一级备注
                var firstRemarkId = this.remarkForm.firstRemark;
                var that = this;
                $.each(that.firstRemarkList, function (index, item) {
                    if (firstRemarkId && firstRemarkId == item.id) {
                        that.secondRemarkList = item.children || [];
                        that.$set(that.remarkForm, "remark", item.remark)
                    }
                });
            },
            secondRemarkChange: function(){
                var secondRemarkId = this.remarkForm.secondRemark;
                var that = this;
                var remark = that.remarkForm.remark;
                $.each(that.secondRemarkList, function (index, item) {
                    if (secondRemarkId && secondRemarkId == item.id) {
                        remark += ("," + item.remark);
                        that.remarkForm.remark = undefined;
                    }
                });
                this.$set(that.remarkForm, "remark", remark)
            },
            onSubmit() {
                if(this.isFromCrm){
                    this.cRmSubmit();
                }else {
                    let mark = this.form.corpMark;
                    if (typeof mark == "undefined" || mark == null || mark == ""){
                        this.$message({
                            type: 'warning',
                            message: '备注不能为空!'
                        });
                        return;
                    }

                    let intent = this.form.intent;
                    if (typeof intent != "undefined" && intent != null && intent != ""&& intent==5){
                        let reason = this.form.reason;
                        if (typeof reason == "undefined" || reason == null || reason == ""){
                            this.$message({
                                type: 'warning',
                                message: '原因不能为空'
                            });
                            return;
                        }
                    }
                    // 将信息set进localStorage里面
                    if (window.localStorage) {
                        // 封装成json
                        // 只能传String
                        window.localStorage.setItem("preSale-hd", JSON.stringify(this.form));
                    } else {
                        console.log("当前浏览器暂不支持Storage");
                    }
                    Fai.http.post("hdSale_h.jsp?cmd=setHDReason", this.form, false).then(result => {
                        if (result.success) {
                            parent.closeFaiBoxDailog(true);
                        }
                    });
                }
            },
            cRmSubmit: function(){
                console.log(this.remarkForm);
                var that = this;
                if (!that.remarkForm.sign || that.remarkForm.sign == "空") {
                    this.$message({
                        type: 'warning',
                        message: '请选择客户意向!'
                    });
                    return;
                }
                if (!that.remarkForm.remark) {
                    this.$message({
                        type: 'warning',
                        message: '备注不能为空!'
                    });
                    return;
                }
                // 将信息set进localStorage里面
                if (window.localStorage) {
                    // 封装成json
                    // 只能传String
                    window.localStorage.setItem("preSale-hd", JSON.stringify(that.remarkForm));
                } else {
                    console.log("当前浏览器暂不支持Storage");
                }

                var remark = '【' + that.remarkForm.sign + '】' + that.remarkForm.remark;
                $.ajax({
                    type: "get",
                    external: true,
                    url: '<%=crmDomain%>/ajax/customer_h.jsp?cmd=setInfo&callBack=jsonpCallBack',
                    data: {
                        business: 0,
                        /*id: that.selectRowData.id,*/
                        aid: aid,
                        addSituation: that.remarkForm.addSituation || -1,
                        sign: that.remarkForm.sign || -1,
                        firstRemark: that.remarkForm.firstRemark || -1,
                        intentLevel: that.remarkForm.intentLevel || -1,
                        intentProduct: that.remarkForm.intentProduct || -1,
                        recommendVersion: that.remarkForm.recommendVersion || -1,
                        remark: remark || '',
                        QWRemindTime: that.remarkForm.qwRemindTime || '',
                        QWRemindContent: that.remarkForm.qwRemindContent,
                    },
                    dataType: "jsonp",
                });
            },
            showReasonBox() {
                if (!<%=authHdSale%>) {
                    Fai.alert('没有权限', '错误', {
                        confirmButtonText: '确定',
                        closeOnClickModal: true,
                        type: 'error',
                    });
                    return;
                }
                this.dialogVisible = true;
            },
            getHdInfo(aid) {
                Fai.http.post("hdSale_h.jsp?cmd=getHdInfo", {aid: aid}, false).then(result => {
                    if (result.success) {
                        if (result.success) {
                            this.hdIntent = result.data.intentName;
                        }
                    }
                });
            },
            onClose() {
                parent.closeFaiBoxDailog();
            },
            markReasonChange(val) {
                markReasonChange(val);
            },
            handleCommand(command) {
                this.form.mark = command;
            },
            getCusRemarkConfigList: function () {
            	$.ajax({
                    type: "get",
                    external: true,
                    url: "<%=crmDomain%>/ajax/customer_h.jsp?cmd=getCusRemarkConfigList&callBack=jsonpCallBack",
                    data: {
                        business: 0,
                    },
                    dataType: "jsonp",
                })
            },
            jsonpResult: function (type, msg) {
                this.$message({
                    type: type,
                    message: msg
                });
            }
        }
    });

    function jsonpCallBack(result) {
        console.log("jsoncallback", result);
        if(result.success){
            reasonBox.signList = result.data.signList;
            reasonBox.allRemarkList = result.data.allRemarkList;
            reasonBox.intentProductList = result.data.intentProductList;
            reasonBox.otherIntentOptions = result.data.otherIntentOptions;
        }
    }

    function submitCallBack(result) {
        console.log("submitCallBack", result);
        if(result.success){
            reasonBox.jsonpResult("success", result.msg);
            parent.closeFaiBoxDailog(true);
        }else {
            reasonBox.jsonpResult('warning', result.msg)
        }
    }

    // 不购买原因修改事件
    function markReasonChange(val) {
        reasonBox.markList = [];	// 清空下拉列表
        var liList = reasonBox.reasonList || [];

        val = val || '';
        if (val === '') {
            return;
        }

        // 获取父级id
        var fatherId = 0;
        liList.forEach(function (item, index) {
            if (item.reason == val) {
                fatherId = item.id;
                return false;		// break;
            }
        });

        // 初始化二级原因li
        fatherId > 0 && liList.forEach(function (item, index) {
            if (item.fatherId != fatherId) {
                return true;	// continue
            }
            reasonBox.markList.push(item);
        });
    }
</script>
</body>
</html>
