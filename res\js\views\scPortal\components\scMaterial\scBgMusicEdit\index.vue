<template>
  <div class="pt-[30px]">
    <el-breadcrumb separator="/" class="mb-[30px]">
      <el-breadcrumb-item
        class="text-[18px] font-bold cursor-pointer"
        @click.native="goBack"
        ><a class="text-[18px] font-bold !cursor-pointer" @click.native="goBack"
          >素材管理</a
        ></el-breadcrumb-item
      >
      <el-breadcrumb-item class="text-[18px] font-bold">{{
        curTitle
      }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-form
      :model="musicInfo"
      :rules="rules"
      class="w-[320px]"
      ref="musicForm"
    >
      <!-- <div class="text-[18px] font-bold">{{ curTitle }}</div> -->
      <el-form-item label="名称：" prop="name">
        <el-input
          placeholder="请输入名称"
          v-model="musicInfo.name"
          class="!w-[250px]"
        />
      </el-form-item>
      <el-form-item label="分类：" prop="categoryId">
        <div class="flex items-center">
          <el-select
            size="small"
            v-model="musicInfo.categoryId"
            placeholder="请选择分类"
            class="!w-[250px]"
          >
            <el-option
              v-for="item in musicCategory"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-tooltip
            width="100"
            content="管理分类"
            placement="top"
          >
            <div
              class="ml-[10px] border-[1px] border-gray-300 rounded-[4px] w-[32px] leading-[30px] text-center cursor-pointer"
              @click="handleManageCategory"
            >
              <el-icon class="el-icon-plus"></el-icon>
            </div>
          </el-tooltip>
          <el-tooltip
            width="100"
            content="刷新分类"
            placement="top"
          >
            <div
              class="ml-[10px] border-[1px] border-gray-300 rounded-[4px] w-[32px] leading-[30px] text-center cursor-pointer"
              @click="getCategoryList"
            >
              <el-icon class="el-icon-refresh-left"></el-icon>
            </div>
          </el-tooltip>
        </div>
      </el-form-item>
      <el-form-item
        label="音频："
        prop="resId"
        :rules="{ required: true, message: '请上传音频' }"
      >
        <div>
          <UploadMusic
            :file-list="musicInfo._musicList"
            @upload-success="uploadMusicSuccess"
            @upload-remove="uploadMusicRemove"
          />
        </div>
      </el-form-item>
      <el-form-item label="封面：" prop="cover">
        <UploadImg
          actionUrl="/api/resource/uploadBgm"
          :file-list="musicInfo._coverList"
          @upload-success="uploadCoverSuccess"
          @upload-remove="uploadCoverRemove"
        />
      </el-form-item>

      <div class="flex justify-center mt-[100px]">
        <el-button
          type="primary"
          class="!w-[100px] sc-edit-save-btn"
          @click="saveConfig"
          >保存</el-button
        >
      </div>
    </el-form>
  </div>
</template>

<script>
import UploadMusic from "@/views/scPortal/components/scTemplate/common/uploadMusic.vue";
import UploadImg from "@/views/scPortal/components/scTemplate/common/uploadImg.vue";
import {
  addMaterial,
  updateMaterial,
  getCategoryList,
} from "@/views/scPortal/api/scMaterial";
import { ScMaterialType } from "@/views/scPortal/config/index.js";
import { getResUrl } from "@/views/scPortal/utils/index.js";

export default {
  name: "ScBgMusicEdit",
  components: {
    UploadMusic,
    UploadImg,
  },
  props: {
    /**
     * 编辑信息
     */
    editInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      musicInfo: {
        type: ScMaterialType.BG_MUSIC,
        name: "", // 名称
        categoryId: null, // 分类id
        resId: "", // 音频id
        fileType: "", // 音频类型
        fileSize: "", // 音频大小
        cover: "", // 封面id
        coverType: "", // 封面类型
        duration: 0, // 音乐时长
      },
      rules: {
        name: [{ required: true, message: "请输入名称" }],
        categoryId: [{ required: true, message: "请选择分类" }],
        resId: [{ required: true, message: "请上传音频" }],
        cover: [{ required: true, message: "请上传封面" }],
      },
      musicCategory: [], // 分类列表
    };
  },
  computed: {
    isEdit() {
      return Object.keys(this.editInfo).length > 0;
    },
    curTitle() {
      return this.isEdit ? "编辑背景音乐" : "添加背景音乐";
    },
  },
  watch: {
    editInfo: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.musicInfo = val;
          if (val.resId) {
            this.musicInfo._musicList = [
              {
                id: val.resId,
                name: val.name,
                type: val.fileType,
                size: val.fileSize,
                url: getResUrl(val.fileType, val.resId),
              },
            ];
          }
          if (val.cover) {
            this.musicInfo._coverList = [
              {
                id: val.cover,
                name: val.name,
                type: val.coverType,
                url: getResUrl(val.coverType, val.cover),
              },
            ];
          }
        } else {
          this.musicInfo = {
            type: ScMaterialType.BG_MUSIC,
            name: "", // 名称
            categoryId: null, // 分类id
            resId: "", // 音频id
            fileType: "", // 音频类型
            fileSize: "", // 音频大小
            cover: "", // 封面id
            coverType: "", // 封面类型
          };
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getCategoryList();
  },
  methods: {
    handleSuccess(response, file, fileList) {
      console.log(response, file, fileList);
    },
    goBack() {
      this.$emit("changeComponent", "ScMaterialList", {
        isReloadList: true,
      });
    },
    /**
     * 获取分类列表
     */
    getCategoryList() {
      getCategoryList({
        type: Number(ScMaterialType.BG_MUSIC),
      }).then((res) => {
        if (res.success) {
          this.musicCategory = res.data.filter(item => item.id !== 0); // 去掉【全部】
        } else {
          this.$message.error(res.msg || "系统错误，请稍后再试");
        }
      });
    },
    /**
     * 上传音频成功
     */
    uploadMusicSuccess(file) {
      this.musicInfo.resId = file.id;
      this.musicInfo.fileType = file.type;
      this.musicInfo.fileSize = file.size;
      this.musicInfo.duration = file.duration;
    },
    /**
     * 删除音频
     */
    uploadMusicRemove(file) {
      this.musicInfo.resId = "";
      this.musicInfo.fileType = "";
      this.musicInfo.fileSize = "";
      this.musicInfo.duration = null;
    },
    /**
     * 上传封面成功
     */
    uploadCoverSuccess(file) {
      this.$set(this.musicInfo, "cover", file.id);
      this.$set(this.musicInfo, "coverType", file.type);
      // this.musicInfo.cover = file.id;
      // this.musicInfo.coverType = file.type;
      // 手动触发表单项的验证
      this.$nextTick(() => {
        this.$refs.musicForm.validateField("cover");
      });
    },
    /**
     * 删除封面
     */
    uploadCoverRemove(file) {
      this.$set(this.musicInfo, "cover", "");
      this.$set(this.musicInfo, "coverType", "");
    },
    /**
     * 管理分类
     */
    handleManageCategory() {
      const url = `${window.location.origin}/#/scPortal/scCategory`;
      window.open(url);
    },
    /**
     * 保存
     */
    saveConfig() {
      this.$refs.musicForm.validate((valid) => {
        if (valid) {
          console.log(this.musicInfo);
          if (this.isEdit) {
            updateMaterial(this.musicInfo).then((res) => {
              if (res.success) {
                this.$message.success("保存成功");
                this.$emit("changeComponent", "ScMaterialList", {
                  isReloadList: true,
                });
              } else {
                this.$message.error(res.msg || "系统错误，请稍后再试");
              }
            });
          } else {
            addMaterial(this.musicInfo).then((res) => {
              if (res.success) {
                this.$message.success("保存成功");
                this.$emit("changeComponent", "ScMaterialList", {
                  isReloadList: true,
                });
              } else {
                this.$message.error(res.msg || "系统错误，请稍后再试");
              }
            });
          }
        } else {
          this.$message.error("请检查输入内容");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.sc-edit-save-btn {
  ::v-deep span {
    float: none;
  }
}
</style>
