<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%
//if(!Auth.checkFaiscoAuth("authHdManage|authAd", false)){out.println("没有权限");return;}%>
<%
/*获得所有已经发布的活动样板 */
SysFaiPortal oPortal = (SysFaiPortal)Core.getSysKit(Kid.SYS_FAI_PORTAL);
SearchArg searchArg = new SearchArg();
ParamMatcher matcher = new ParamMatcher(HdModelDef.Info.PROPERTY,ParamMatcher.EQ,HdModelDef.Property.ISPUB);
searchArg.matcher = matcher;

//FaiList<Param> list = oPortal.getHdModelList(searchArg);

HdOss hdOss = (HdOss)WebOss.getCorpKit(Kid.HD_OSS);
Log.logDbg("unique-test hdoss = %s",hdOss);
FaiList<Param> list = hdOss.getHdModelList(searchArg);
String listJson = list.toJson();
%>

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>创建活动-广告配置</title>
	<%@ page import="fai.web.inf.*"%>
	<%@ page import="fai.comm.util.*"%>
	<link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
	<%=Web.getToken()%>
	<%=FrontEndDef.getJSsdkComponentScript(9000)%>
	<%=FrontEndDef.getPackageCss("fa-component", "~1.1.0")%>

	<%
		int test = Parser.parseInt(request.getParameter("test"), 0);
		Boolean _debug = test == 3;
	%>
	<style>
		[v-cloak] {
			display: none;
		}
		.flex {
			display: flex;
		}
		.container {
			display: inline-block;
			width: 100%;
			height: 100%;
		}
		.menuTab {
			display: inline-block;
		}
		#ossConfig {
			height: 100%;
		}
		.fa-menu-inline-collapsed > .fa-menu-submenu > .fa-menu-submenu-title {
			padding: 0 10px !important;
		}
	</style>
</head>
<body>
	<div id="ossConfig" class="flex" ref="container" v-cloak>
		<!-- 路由入口 -->
		<div class="menuTab">
			<fa-button type="primary" @click="toggleCollapsed" style="margin-bottom: 16px">
				<fa-icon :type="collapsed ? 'menu-unfold' : 'menu-fold'" />
			</fa-button>
			<fa-menu
				:default-selected-keys="['sub21']"
				:default-open-keys="['sub1', 'sub2']"
				mode="inline"
				theme="dark"
				:inline-collapsed="collapsed"
				v-cloak
			>
				<fa-sub-menu key="sub1">
					<span slot="title">广告配置</span>
					<fa-sub-menu
						v-for="tabItem in bannerTabs"
						:key="tabItem.key"
						:title="tabItem.name"
					>
						<fa-menu-item
							v-for="routeItem in bannerRoutes[tabItem.route]"
							:key="routeItem.key"
						>
							<div @click="handleClickBannerRouter('manageBanner', routeItem.bannerId, routeItem.type)">
								{{routeItem.name}}
							</div>
						</fa-menu-item>
					</fa-sub-menu>
				</fa-sub-menu>
			</fa-menu>
		</div>
		<!-- 路由出口 -->
		<keep-alive>
			<router-view class="container"></router-view>
		</keep-alive>
	</div>

	

	<script type="text/x-template" id="wrapIframe">
		<div>
        	<iframe :src="iframeSrc" frameborder="0" scrolling="auto" style="width:100%;height:100%;"></iframe>
		</div>
	</script>

	<script type="text/x-template" id="hdhomeProduct">
		<div>价格页banner</div>
	</script>

	<script type="text/x-template" id="bannerA">
		<div>轮播A位</div>
	</script>

	<script type="text/x-template" id="bannerB">
		<div>B1位</div>
	</script>
	


	<% if (_debug) { %>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_polyfill")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_axios")%>"></script>

	<% } else { %>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_polyfill")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_axios")%>"></script>
	<% } %>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue_router")%>"></script>
	  <%=FrontEndDef.getPackageJs("fa-component", "~1.1.0")%>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_ossConfig")%>"></script>


</body>
</html>
