<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.cli.PhoneCli" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.hdUtil.HdImgUtil" %>
<%@ page import="fai.hdUtil.HdTool" %>
<%@ page import="fai.hdUtil.graySchemeUtil.GrayScheme" %>
<%@ page import="fai.hdUtil.graySchemeUtil.GraySchemeEnum" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.web.jz.WebJZ" %>
<%@ page import="fai.webhdgame.WebHdGame" %>
<%@ page import="fai.webhdgame.WebHdMobi" %>
<%@ page import="fai.webhdgame.app.WxJsSign" %>
<%@ page import="javax.servlet.http.Cookie" %>
<%@ page import="java.net.HttpURLConnection" %>
<%@ page import="java.net.InetSocketAddress" %>
<%@ page import="java.net.Proxy" %>
<%@ page import="java.nio.ByteBuffer" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.hdUtil.SensitiveInfoCheckUtil" %>
<%@ page import="com.sun.org.apache.bcel.internal.generic.NEW" %>
<%@ page import="java.nio.charset.Charset" %>
<%@ page import="java.nio.charset.CharsetDecoder" %>
<%@ page import="java.nio.CharBuffer" %>

<%!
    private static final String APPID = "wx093bdfbf5ae82427";
%>

<%!
    private String login(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String code = request.getParameter("code");
        HdWxAuth hdWxAuth = (HdWxAuth) WebHdGame.getCorpKit(1, Kid.HD_WX_AUTH);
        return hdWxAuth.getWxAppOpenidByCode(APPID, code).getString("unionid", "");

    }
%>

<%
    String output = "";
    try {
        String cmd = request.getParameter("cmd");

        if (cmd == null) {
            return;
        } else if (cmd.equals("login")) {
            login(request, response);
        }
    } catch (Exception exp) {
        output = WebHdGame.checkAjaxException(exp);
    }
    out.print(output);
%>
