package fai.webhdoss.util;

import fai.app.FileStgDef;
import fai.app.TsImgResDef;
import fai.cli.FileStgCli;
import fai.comm.util.*;

public class ScFileUtil {

    /**
     * 临时文件转正
     *
     * <AUTHOR> 2025/5/12 10:36
     * @Update HLS 2025/5/12 10:36
     **/
    public static int copyScFileFromTmp(int flow, int aid, String resId, Param info) {
        int rt = Errno.ERROR;

        // 校验数据
        if (resId.isEmpty() || aid < 0) {
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "copyScFileFromTmp err; aid=%s, resId=%s", aid, resId);
            return rt;
        }

        // 获取图片信息（aid，type，图片详细信息）
        Param fileInfo = FileStgDef.parseFileId(resId);
        if (fileInfo == null) {
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "fileId error; aid=%s; resId=%s;", aid, resId);
            return rt;
        }

        // 是否是正式文件
        boolean isTmp = fileInfo.getBoolean(FileStgDef.IdDef.TMP, false);
        if (!isTmp) {
            if (info != null) {
                info.setString(TsImgResDef.Info.IMG_ID, resId);
            }
            Log.logStd("resId notTmp; resId=%s", resId);
            return Errno.OK;
        }

        // 资源id，架构定义
        int app = fileInfo.getInt(FileStgDef.IdDef.APP, 0);
        int type = fileInfo.getInt(FileStgDef.IdDef.TYPE, 0);
        int width = fileInfo.getInt(FileStgDef.IdDef.Img.WIDTH, 0);
        int height = fileInfo.getInt(FileStgDef.IdDef.Img.HEIGHT, 0);

        String fileId = FileStgDef.genImgId(app, type, false, width, height);
        for (int i = 0; i < 3; i++) {
            rt = copyScFileFromTmp(flow, aid, resId, fileId);
            if (rt == Errno.OK) {
                if (info != null) {
                    info.setString(TsImgResDef.Info.IMG_ID, fileId);
                }
                Log.logStd("copyTsFileFromTmp success; aid=%s, resId=%s, fileIdRef=%s", aid, resId, fileId);
                return Errno.OK;
            }
        }

        Log.logErr(rt, "resRegular error; aid=%s; resId=%s; fileId=%s", aid, resId, fileId);
        return rt;
    }

    /**
     * 临时文件转正
     *
     * <AUTHOR> 2025/5/12 10:29
     * @Update HLS 2025/5/12 10:29
     **/
    private static int copyScFileFromTmp(int flow, int aid, String tmpFileId, String fileId) {
        FileStgCli stgCli = FaiCliFactory.createCli(FileStgCli.class, flow);

        // 从临时目录转正到正式目录
        int rt = stgCli.copyFileByName(FileStgDef.App.TS_FILE, aid, tmpFileId, fileId, true);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "add ts file error;aid=%d;id=%s;", aid, fileId);
            return rt;
        }

        return rt;
    }
}
