package fai.webhdoss.controller;

import fai.app.HdOssStaffDef;
import fai.app.ScResDef;
import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.comm.util.Str;
import fai.hdUtil.CollectionUtil;
import fai.hdUtil.JsonResult;
import fai.webhdoss.annotation.CheckRole;
import fai.webhdoss.model.vo.scProto.ScProtoListVO;
import fai.webhdoss.model.vo.scProto.ScProtoVO;
import fai.webhdoss.model.vo.scRes.ScCategoryVO;
import fai.webhdoss.model.vo.scRes.ScResListVO;
import fai.webhdoss.service.ScCategoryService;
import fai.webhdoss.service.ScProtoService;
import fai.webhdoss.service.ScResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 原型
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/18 10:21
 */
@RestController
@ResponseBody
@RequestMapping("/proto")
@CheckRole({HdOssStaffDef.Auth.SC_PM, HdOssStaffDef.Auth.SC_DEVELOP})
public class ScProtoController {

    @Autowired
    private ScProtoService scProtoService;

    @Autowired
    private ScCategoryService scCategoryService;

    @Autowired
    private ScResService scResService;

    /**
     * 同步原型接口
     *
     * <AUTHOR> 2025/4/17 16:12
     * @Update HLS 2025/4/17 16:12
     **/
    @RequestMapping("/syncProto")
    public JsonResult syncProto(@RequestParam(defaultValue = "0") int type
            , @RequestParam(defaultValue = "") String apiKey, @RequestParam(defaultValue = "0") int id) throws Exception {
        if (id < 1 && Str.isEmpty(apiKey)) {
            return JsonResult.error("apiKey 不能为空呀");
        }

        return scProtoService.syncProto(type, apiKey, id);
    }

    /**
     *  保存接口
     * <AUTHOR> 2025/4/18 10:47
     * @Update HLS 2025/4/18 10:47
    **/
    @PostMapping("/setScProtoInfo")
    public JsonResult setScProtoInfo(@RequestBody @Valid ScProtoVO vo) {
        if (vo == null) {
            return JsonResult.error("保存信息不能为空呀");
        }

        return scProtoService.setScProtoInfo(vo);
    }

    /**
     *  获取列表
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
    **/
    @PostMapping("/getScProtoList")
    public JsonResult getScProtoList(@RequestBody ScProtoListVO vo) {
               if (vo == null) {
            return JsonResult.error("参数 不能为空呀");
        }

        return scProtoService.getScProtoList(vo);
    }

    /**
     *  更新状态
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
    **/
    @RequestMapping("/setScProtoStatus")
    @CheckRole({HdOssStaffDef.Auth.SC_PM})
    public JsonResult setScProtoStatus(@RequestParam(defaultValue = "0") int id, @RequestParam(defaultValue = "0") int status) {
        if (id < 1) {
            return JsonResult.error("id 不能为空呀");
        }

        return scProtoService.setScProtoStatus(id, status);
    }

    /**
     *  获取原型名称列表
     * <AUTHOR> 2025/4/18 15:37
     * @Update HLS 2025/4/18 15:37
    **/
    @PostMapping("/getScProtoNameList")
    public JsonResult getScProtoNameList(@RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "") String keyword
            , @RequestParam(defaultValue = "1") int pageNo
            , @RequestParam(defaultValue = "20") int pageLimit) {
        return scProtoService.getScProtoNameList(type, keyword, pageNo, pageLimit);
    }

    /**
     * 获取原型配置信息
     *
     * <AUTHOR> 2025/4/18 16:33
     * @Update HLS 2025/4/18 16:33
     **/
    @RequestMapping("/getConfInfo")
    public JsonResult getConfInfo() {
        return scProtoService.getConfInfo();
    }


    @GetMapping("/getBgmTree")
    public JsonResult getBgmListTree() {

        // 处理 category
        ScCategoryVO scCategoryVO = new ScCategoryVO();
        scCategoryVO.setPageLimit(-1);
        FaiList<Param> categories = scCategoryService.getCategoryList(scCategoryVO);

        // 处理 bgm
        ScResListVO resListVO = new ScResListVO();
        resListVO.setType(ScResDef.Type.OPT_BGM);
        resListVO.setPageLimit(-1);
        FaiList<Param> bgmList = scResService.getScResListNew(resListVO);

        // 分组
        Map<Integer, FaiList<Param>> groupedBGM = new HashMap<>();
        if (!CollectionUtil.isEmpty(bgmList)) {
            for (Param bgm : bgmList) {
                int categoryId = bgm.getInt("categoryId", 0);
                if (!groupedBGM.containsKey(categoryId)) {
                    groupedBGM.put(categoryId, new FaiList<>());
                }
                groupedBGM.get(categoryId).add(bgm);
            }
            for (Param category : categories) {
                int categoryId = category.getInt("id", 0);
                if (groupedBGM.containsKey(categoryId)) {
                    category.setList("bgmList", groupedBGM.get(categoryId));
                }
            }
        }

        return JsonResult.success(categories);
    }
}
