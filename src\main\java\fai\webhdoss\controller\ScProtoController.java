package fai.webhdoss.controller;

import fai.comm.util.Str;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.webhdoss.model.vo.scProto.ScProtoListVO;
import fai.webhdoss.model.vo.scProto.ScProtoVO;
import fai.webhdoss.service.ScProtoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @Description 原型
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/18 10:21
 */
@RestController
@ResponseBody
@RequestMapping("/proto")
public class ScProtoController {

    @Autowired
    private ScProtoService scProtoService;

    /**
     * 同步原型接口
     *
     * <AUTHOR> 2025/4/17 16:12
     * @Update HLS 2025/4/17 16:12
     **/
    @RequestMapping("/syncProto")
    public JsonResult syncProto(@RequestParam(defaultValue = "0") int type
            , @RequestParam(defaultValue = "") String apiKey, @RequestParam(defaultValue = "0") int id) throws Exception {
        if (id < 1 && Str.isEmpty(apiKey)) {
            return JsonResult.error("apiKey 不能为空呀");
        }

        return scProtoService.syncProto(type, apiKey, id);
    }

    /**
     *  保存接口
     * <AUTHOR> 2025/4/18 10:47
     * @Update HLS 2025/4/18 10:47
    **/
    @PostMapping("/setScProtoInfo")
    public JsonResult setScProtoInfo(@RequestBody @Valid ScProtoVO vo) {
        if (vo == null) {
            return JsonResult.error("保存信息不能为空呀");
        }

        return scProtoService.setScProtoInfo(vo);
    }

    /**
     *  获取列表
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
    **/
    @PostMapping("/getScProtoList")
    public JsonResult getScProtoList(@RequestBody ScProtoListVO vo) {
               if (vo == null) {
            return JsonResult.error("参数 不能为空呀");
        }

        return scProtoService.getScProtoList(vo);
    }

    /**
     *  更新状态
     * <AUTHOR> 2025/4/18 11:44
     * @Update HLS 2025/4/18 11:44
    **/
    @RequestMapping("/setScProtoStatus")
    public JsonResult setScProtoStatus(@RequestParam(defaultValue = "0") int id, @RequestParam(defaultValue = "0") int status) {
        if (id < 1) {
            return JsonResult.error("id 不能为空呀");
        }

        return scProtoService.setScProtoStatus(id, status);
    }

    /**
     *  获取原型名称列表
     * <AUTHOR> 2025/4/18 15:37
     * @Update HLS 2025/4/18 15:37
    **/
    @PostMapping("/getScProtoNameList")
    public JsonResult getScProtoNameList(@RequestParam(defaultValue = "0") int type, @RequestParam(defaultValue = "") String keyword
            , @RequestParam(defaultValue = "1") int pageNo
            , @RequestParam(defaultValue = "20") int pageLimit) {
        return scProtoService.getScProtoNameList(type, keyword, pageNo, pageLimit);
    }

    /**
     *  获取原型配置信息
     * <AUTHOR> 2025/4/18 16:33
     * @Update HLS 2025/4/18 16:33
    **/
    @RequestMapping("/getConfInfo")
    public JsonResult getConfInfo() {
        return scProtoService.getConfInfo();
    }
}
