<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){out.println("没有权限");return;}%>

<!DOCTYPE html>
<html lang="en">
<head>
        <meta charset="UTF-8">
        <title>热门专题配置</title>
        <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
        <style>
                body{
                        margin: 0;
                }
                #app{
                        padding: 10px
                }
                [v-cloak]{
                        display: none;
                }
                .title{
                        font-size: 16px;
                        font-weight: bold;
                        padding-left: 17px;
                        margin-bottom: 18px;
                }
                .subTitle{
                        font-size: 14px;
                        color: #606266;
                        padding-left: 17px;
                        margin-bottom: 18px;
                }
                .el-form{
                        width: 400px;
                }
                .el-upload{
                        width: 400px;
                        text-align: left;
                }
                .imgBox{
                        width: 304px;
                        margin-bottom: 10px;
                }
                .imgBox img{
                        display: block;
                        width: 100%;
                }
                .el-icon-circle-close{
                        font-size: 18px;
                        color: #4381fd;
                        vertical-align: middle;
                        cursor: pointer;
                }
                .button{
                        display: block;
                        margin: 18px auto 0;
                }
        </style>
</head>
<body>
        <div id="app" v-cloak>
                <el-form label-width="100px" size="mini">
                        <div class="title">热门专题配置</div>
                        <div class="subTitle">专题卡片3</div>
                        <el-form-item label="专题名称：">
                                <el-input v-model="hotTopic.name"></el-input>
                        </el-form-item>
                        <el-form-item label="专题图片：">
                                <el-upload
                                        action="/ajax/advanceUpload.jsp?cmd=upload&maxWidth=10000&maxHeight=10000&imgMode=2"
                                        accept="image/*"
                                        name="filedata"
                                        :show-file-list="false"
                                        :before-upload="beforeFileUpload"
                                        :on-success="fileUploadSuccess"
                                        :on-progress="fileUploadProgress"
                                        :on-error="fileUploadError"
                                >
                                        <div class="imgBox">
                                                <img :src="hotTopic.img" />
                                        </div>
                                        <el-progress v-show="isUploading" :percentage="percentage"></el-progress>
                                        <el-button size="mini" type="primary">点击上传</el-button><span style="color: #aaa;">图片尺寸建议为480*180（或等比例大小）</span>
                                </el-upload>
                        </el-form-item>
                        <div class="subTitle">专题活动：</div>
                        <el-form-item
                                v-for="(model, index) in hotTopic.modelList"
                                :key="index"
                                :label="'活动样板' + (index + 1) + '：'"
                        >
                                <el-select
                                        v-model="model.name"
                                        value-key="id"
                                        placeholder="请选择"
                                        filterable
                                >
                                        <el-option
                                                v-for="hdModel in hdModelList"
                                                :key="hdModel.id"
                                                :label="hdModel.name"
                                                :value="hdModel"
                                        ></el-option>
                                </el-select>
                                <i class="el-icon-circle-close" @click="deleteModel(index)"></i>
                        </el-form-item>
                        <el-button
                                class="button"
                                type="primary"
                                size="mini"
                                @click="addModel"
                        >添加活动样板</el-button>
                        <el-button
                                class="button"
                                type="primary"
                                size="mini"
                                :loading="isLoading"
                                @click="save"
                        >保存</el-button>
                </el-form>
        </div>
        <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
        <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue_resource")%>"></script>
        <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
        <script>
                new Vue({
                        el: '#app',
                        data: {
                                isLoading: false,
                                isUploading: false,
                                percentage: 0,
                                hotTopic: {
                                        name: '',
                                        img: '',
                                        modelList: [{}, {}, {}, {}]
                                },
                                hdModelList: []
                        },
                        created() {
                                this.getCategoryDisplay();
                                this.getModelListByCond();
                        },
                        methods: {
                                beforeFileUpload(file) {
                                        if (file.size > 5 * 1024 * 1024) {
                                                return this.$message({
                                                        type: 'error',
                                                        message: '单个文件超过5MB！'
                                                });
                                        }
                                        this.isUploading = true;
                                        this.percentage = 0;
                                },
                                fileUploadSuccess(response) {
                                        if (response.type >= 1 && response.type <= 6) {
                                                this.$message({
                                                        type: 'success',
                                                        message: '文件 ' + response.name + ' 上传成功！'
                                                });
                                                this.hotTopic.img = response.path;
                                        } else {
                                                this.$message({
                                                        type: 'error',
                                                        message: '文件 ' + response.name + ' 类型不允许！'
                                                });
                                        }
                                        this.isUploading = false;
                                },
                                fileUploadProgress(event) {
                                        this.percentage = Math.round(event.percent);
                                },
                                fileUploadError(err) {
                                        this.$message({
                                                type: 'error',
                                                message: '系统繁忙，请稍后重试！'
                                        });
                                        this.isUploading = false;
                                },
                                getCategoryDisplay() {
                                        Vue.http.post(
                                                '/api/caseMode/getCategoryDisplay',
                                                {
                                                        displayType: 3
                                                },
                                                { emulateJSON: true }
                                        ).then(({data}) => {
                                                if (data.success && Object.keys(data.data).length > 0) {
                                                        let modelList = typeof data.data.modelList == 'string' ? JSON.parse(data.data.modelList) : data.data.modelList;
                                                        this.hotTopic.name = data.data.name;
                                                        this.hotTopic.img = data.data.img;
                                                        modelList.length > 0 && (this.hotTopic.modelList = modelList);
                                                }
                                        });
                                },
                                getModelListByCond() {
                                        Vue.http.post(
                                                '/api/caseMode/getModelListByCond',
                                                {
                                                        displayType: 3
                                                },
                                                { emulateJSON: true }
                                        ).then(({data}) => {
                                                this.hdModelList = data.data;
                                        });
                                },
                                addModel() {
                                        this.hotTopic.modelList.push({});
                                },
                                deleteModel(index) {
                                        this.hotTopic.modelList.splice(index, 1);
                                },
                                save() {
                                        this.isLoading = true;
                                        let modelList = [];
                                        this.hotTopic.modelList.forEach((model) => {
                                                Object.keys(model).length > 0 && modelList.push(typeof model.name == 'object' ? model.name.id : model.id);
                                        });
                                        Vue.http.post(
                                                '/api/caseMode/setCategoryDisplay',
                                                Object.assign({}, this.hotTopic, {
                                                        displayType: 3,
                                                        modelList: JSON.stringify(modelList)
                                                }),
                                                { emulateJSON: true }
                                        ).then(({data}) => {
                                                this.isLoading = false;
                                                this.$message({
                                                        type: data.success ? 'success' : 'error',
                                                        message: data.success ? '修改成功' : '修改失败'
                                                });
                                        });
                                }
                        }
                })
        </script>
</body>
</html>