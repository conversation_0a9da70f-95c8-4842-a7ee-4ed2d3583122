<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" session="false" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.*" %>
<%@ page import="java.math.*" %>
<%@ page import="java.util.regex.Matcher" %>
<%@ page import="java.util.regex.Pattern" %>
<%@ page import="fai.cli.PreSaleUtilCli" %>
<%@ page import="fai.cli.BssStatCli" %>
<%@ page import="fai.cli.*" %>
<%@ page import="java.sql.PreparedStatement" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.EQ" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.comm.fdpdata.FdpDataParamMatcher" %>
<%@ page import="fai.sdk.fdpdata.operator.*" %>
<%@ page import="fai.sdk.fdpdata.enumerate.OrderBy" %>

<%!
	
	//时间转换 Long -> yyyy-MM-dd HH:mm:ss
    public static String parseLongDateToString(long date) {
        if (date < 1) {
            return "";
        }
        Date date1 = new Date();
        date1.setTime(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return simpleDateFormat.format(date1);
    }

    /**
     * 判断员工权限(常用于判断是否已离职)
     * staff: 员工info
     * authType: 要检查的权限类型
     */
    private static boolean getChecked(Param staff, int authType){
        if (staff == null || staff.isEmpty()) {
            return false;
        }

        int authBit = staff.getInt(StaffDef.Info.AUTH_BIT, 0);
        return Misc.checkBit(authBit, authType);
    }

    /**
     * 判断是否是某个权限组里面的sid
     */
    private boolean isAuth(String salesAcct, String auth) throws Exception {
        if (salesAcct == null || salesAcct.isEmpty() || auth == null || auth.isEmpty()) {
            return false;
        }

        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), salesAcct);
        int sid = staffInfo.getInt(StaffDef.Info.SID, 0);

        return isAuth(sid, auth);
    }

    /**
     * 判断是否是某个权限组里面的sid
     */
    private boolean isAuth(int sid, String auth) throws Exception {
        if (sid == 0) {
            return false;
        }

        // adm管理员权限直接过
        FaiList<Integer> admList = WebOss.getAuthSidList("authAdm");
        if (admList.contains(sid)) {
            return true;
        }

        FaiList<Integer> sidList = WebOss.getAuthSidList(auth);                        // 权限组列表

        // 存进map里，避免直接用list的contains
        Map<Integer, Integer> mapList = new HashMap<Integer, Integer>();
        for (Integer p_sid : sidList) {
            mapList.put(p_sid, p_sid);
        }

        return mapList.get(sid) != null;
    }

    /**
     * 直销售前-记录领取记录
     */
    private int setRecord(int aid, String salesAcct, int attrType, String action, Calendar optTime, Calendar receiveTime) throws Exception {
        return setRecord(aid, salesAcct, attrType, action, optTime, receiveTime, null);
    }

    /**
     * 直销售前-记录领取记录
     */
    private int setRecord(int aid, String salesAcct, int attrType, String action, Calendar optTime, Calendar receiveTime, Param preSaleInfo) throws Exception {
        int rt = Errno.ERROR;
        //SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);    // presale 的接口
		//CHANGE
        Param recordInfo = new Param();
        recordInfo.setInt(HdSaleRecordDef.Info.AID, aid);
        recordInfo.setString(HdSaleRecordDef.Info.SACCT, salesAcct);
        recordInfo.setInt(HdSaleRecordDef.Info.ATTR_TYPE, attrType);
        recordInfo.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
        recordInfo.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, Calendar.getInstance());
        recordInfo.setString(HdSaleRecordDef.Info.ACTION, action);
        recordInfo.setInt(HdSaleRecordDef.Info.TAG, 0);

        Dao bssDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctStatus";
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.EQ, aid);
            Param bssData = bssDao.selectFirst(sltArg);
            FaiList<Param> taList = bssDao.executeQuery("select * from ta");
            Param taParam = new Param(true);
            for (Param item : taList) {
                taParam.setInt(item.getInt("ta", 0) + "", item.getInt("groupId", 0));
            }
            int ta = bssData.getInt("ta", 0);
            int groupId = taParam.getInt(ta + "", 0);
            recordInfo.setString(HdSaleRecordDef.Info.TA_GROUP_NAME, PreSaleHdDef.getTaNameByTaAndGroupId(groupId, ta));
            recordInfo.setInt(HdSaleRecordDef.Info.TA, ta);
        } finally {
            bssDao.close();
        }
        if (receiveTime != null) {
            recordInfo.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, receiveTime);
        }
        Dao ykDao = WebHdOss.getYkOssDaoMaster();
        try {
            rt = ykDao.insert("ykSaleRecord", recordInfo);
        } finally {
        	ykDao.close();
        }
        //Ref<Integer> idRef = new Ref<Integer>();
        //rt = sysPreSaleUtil.addPreSaleLog(recordInfo, idRef);

        if (rt != Errno.OK) {
            Log.logErr(rt, "error:Oss-PreSale addActionLog set err;info=%s;", recordInfo.toJson());
        }
        return rt;
    }

    /**
     *	将某个客户转换为成交库
     */
    private int changeDeal(Param preSale) throws Exception {
        int rt = Errno.ERROR;
   		
        //SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口
		//CHANGE
        int aid = preSale.getInt(PreSaleHdDef.Info.AID, 0);
        int status = preSale.getInt(PreSaleHdDef.Info.STATUS, 0);
        Calendar receiveTime = preSale.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);

        if (receiveTime == null) {
            rt = Errno.ARGS_ERROR;
            return rt;
        }

        // 成单条件
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(FaiOrderDef.Item.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(FaiOrderDef.Item.PRODUCT_ID, ParamMatcher.IN, PreSaleHdDef.getProductList(PreSaleHdDef.PreSaleProduct.ALL));
        searchArg.matcher.and(FaiOrderDef.Item.STATUS, ParamMatcher.EQ, FaiOrderDef.Status.FIN_PROCESS);        // 完成支付的的订单
        searchArg.matcher.and(FaiOrderDef.Item.PRICE, ParamMatcher.GT, 0);
        searchArg.matcher.and(FaiOrderDef.Item.DEL, ParamMatcher.EQ, false);

        // 查订单
        OssFaiOrder oOrder = new OssFaiOrder();
        FaiList<Param> orderList = oOrder.getAllOrderItemList(searchArg);

        // 判断该客户是否符合成交客户的条件,成交一次就直接return行了
        for (Param p : orderList) {
            Calendar p_payTime = p.getCalendar(FaiOrderDef.Item.PAY_TIME);
            if (p_payTime == null) {
                continue;
            }

            if (receiveTime.after(p_payTime)) {
                continue;
            }

            Param upInfo = new Param();
            upInfo.setInt(PreSaleHdDef.Info.STATUS, PreSaleHdDef.Status.DEAL);
            upInfo.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
			
            //rt = sysPreSaleHd.update(aid, new ParamUpdater(upInfo));
            //TOCHANGE
            if (rt != Errno.OK) {
                Log.logErr(rt, "error:update acctPreSale status=2 err; aid=%d;", aid);
                return rt;
            }

            Log.logStd("update to Deal;aid=%d;", aid);
            return Errno.OK;
        }

        return Errno.NOT_FOUND;
    }


    /**
     *	
     *	释放成交库,直接删除acctPreSaleYk表数据,然后在record表增加一条记录
     *	释放成交库后还需要释放订单saleid
     *	
     *  修改sid获取方式，避免销售离职找不到sid
     */
    public String releasePreSaleDeal(HttpServletRequest request) throws Exception {
        //管理员才能释放成交库
        if (!Auth.checkFaiscoAuth("authYkSaleLeader", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }
        //获取要释放的aid
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if (aid == 0) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }
        
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        // oldInfo
        Param oldInfo = sysPreSaleYk.getInfo(aid);
        if (oldInfo == null || oldInfo.isEmpty()) {
            return "{\"success\":false, \"msg\":\"数据错误\"}";
        }
        String oldSalesAcct = oldInfo.getString(PreSaleHdDef.Info.SALES_ACCT, "");
        int status = oldInfo.getInt(PreSaleHdDef.Info.STATUS, 0);
        //该方法只用于释放成交库
        if (status != PreSaleHdDef.Status.DEAL) {
            return "{\"success\":false, \"msg\":\"库状态错误\"}";
        }
        int rt = Errno.ERROR;

        //订单表销售字段SALES_SID
        Integer sid = getSaleSid(oldSalesAcct);
        Log.logStd("release deal aid = %d , sid = %d", aid, sid);

        if (sid == null || sid == 0) {
        	Log.logErr("get sid err");
        	return "{\"success\":true, \"msg\":\"找不到该销售\"}";
        }
           
        //已知aid,sid获得orderId
        BssStatCli bssCli = new BssStatCli(Core.getFlow());
        if (!bssCli.init()) {
            Log.logErr("BssStatCli init error");
            return "{\"success\":false, \"msg\":\"释放订单失败\"}";
        }
        FaiList<Param> orderList = new FaiList<Param>(); //订单列表
        FaiList<Integer> orderIdList = new FaiList<Integer>(); //订单id列表
        SearchArg orderSearchArg = new SearchArg();
        orderSearchArg.matcher = new ParamMatcher(BssStatDef.OrderItemInfo.AID, ParamMatcher.EQ, aid);
        orderSearchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.EQ, sid);
        orderSearchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
        rt = bssCli.getOrderItemList(orderSearchArg, orderList);
        if (rt != Errno.OK) {
            Log.logErr("getOrderList err");
            return "{\"success\":false, \"msg\":\"获取订单列表失败\"}";
        }

        for (Param tempParam : orderList) {
            orderIdList.add(tempParam.getInt(BssStatDef.OrderItemInfo.ID));
        }

        if (orderIdList.isEmpty()) {
            Log.logErr("orderIdList is null/empty err");
            return "{\"success\":false, \"msg\":\"找不到订单\"}";
        }
        
        Dao ykDao = WebHdOss.getYkOssDaoMaster();
        try{
        	ykDao.setAutoCommit(false);
        	 //正式开始释放
            rt = ykDao.delete("acctPreSaleYk", new ParamMatcher("aid", ParamMatcher.EQ, aid));
            if (rt != Errno.OK) {
            	ykDao.rollback();
                Log.logErr(rt, "error:release preSale err; aid=%d;", aid);
                return "{\"success\":false, \"msg\":\"释放成交库失败\"}";
            }
            
            // 记录系统痕迹
            String action = "主动-释放客户：" + aid + ";销售组 释放 成交库";
            rt = setRecord(aid, oldSalesAcct, HdSaleRecordDef.AttrType.DROP_DEAL, action, Calendar.getInstance(), oldInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
            if(rt != Errno.OK){
            	ykDao.rollback();
            	Log.logErr(rt, "error:insert record err; aid=%d;", aid);
                return "{\"success\":false, \"msg\":\"释放成交库失败\"}";
            }
            ykDao.commit();
        }catch(Exception e){
        	ykDao.rollback();
        	Log.logStd("release transaction fail: %s", e);
        	return "{\"success\":false, \"msg\":\"释放成交库失败\"}";
        }finally{
        	ykDao.close();
        }
        
        //更新参数字段SALES_SID
        Param up = new Param();
        up.setInt(BssStatDef.OrderItemInfo.SALES_SID, 0);
        ParamUpdater pd = new ParamUpdater(up);

        //执行更新
        rt = bssCli.setOrderItemInfo(orderIdList, pd);
        if (rt != Errno.OK) {
            Log.logErr("setOrderItemInfo err updater = %s", up);
            return "{\"success\":false, \"msg\":\"释放订单失败\"}";
        }
        Log.logStd("releasePreSaleDeal aid=%d;operate sid=%d", aid, Session.getSid());

        return "{\"success\":true, \"msg\":\"释放成功\"}";
    }


    /**
     * 批量释放客户
     */
    public String batchRelease(HttpServletRequest request) throws Exception {
    	int rt = Errno.ERROR;
    	if (!Auth.checkFaiscoAuth("authYKSale1|authYkSaleLeader", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }
    	
    	int status = Parser.parseInt(request.getParameter("status"), -1);
    	if(status>1){
     		 return "{\"success\":false, \"msg\":\"库状态错误\"}";
     	}
    	
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);
        boolean saleManage = Auth.checkFaiscoAuth("authYkSaleManage", false);

        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
      	String aidListStr = request.getParameter("aidList");
      	FaiList<Integer> aidList = FaiList.parseIntList(aidListStr, new FaiList<Integer>());
     	SearchArg searchArg = new SearchArg();
     	searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
     	
     	if(!adm && !saleManage){
     		searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, WebOss.getSacct());	
     	}
     	
     	//Log.logStd("searchArg.matcher : %s", searchArg.matcher);
        FaiList<Param> acctList = sysPreSaleYk.getAllInfoList(searchArg);
        //Log.logStd("acctList size = %s", acctList.size());
     	// 除了adm和销售管理，其他人要校验领取人跟登录人是否一致
        if ( !adm && !Auth.checkFaiscoAuth("authYkSaleLeader", false) && acctList.size() != aidList.size()) {
            return "{\"success\":false, \"msg\":\"销售账号错误，不能释放其他销售资源\"}";
        }
     	
        int delRt = Errno.ERROR;
        int insertRt = Errno.ERROR;
        Dao ykDao = WebHdOss.getYkOssDaoMaster();
        try{
        	ykDao.setAutoCommit(false);
        	for(Param p : acctList){
            	Dao.SelectArg sltArg = new Dao.SelectArg();
        		ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.EQ, p.getInt(PreSaleHdDef.Info.AID, 0)); 
            	sltArg.searchArg.matcher = matcher;
            	sltArg.table="acctPreSaleYk";

            	FaiList<Param> infoList = ykDao.select(sltArg);
            	if (infoList == null || infoList.isEmpty()) {
            		ykDao.rollback();
                    return "{\"success\":false, \"msg\":\"参数错误。\"}";
                }
				Log.logStd("here");
            	delRt = ykDao.delete("acctPreSaleYk", matcher);
            	
            	if(delRt != Errno.OK){
            		ykDao.rollback();
                	return "{\"success\":false, \"msg\":\"批量释放失败！\"}";
            	}
            	
	          	String action = "主动释放"+(status==1?"个人库":(status==2?"A库":"B库"));
	          	int attrType = status==1?HdSaleRecordDef.AttrType.DROP_PERSON:(status==2?HdSaleRecordDef.AttrType.DROP_A:HdSaleRecordDef.AttrType.DROP_B);
	          	insertRt = setRecord(p.getInt(PreSaleHdDef.Info.AID,0),WebOss.getSacct(),attrType,action, Calendar.getInstance(), p.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
	            if(delRt != Errno.OK){
	            	ykDao.rollback();
                	return "{\"success\":false, \"msg\":\"批量释放失败，插入记录失败！\"}";
	            }
	            ykDao.commit();
	          	Log.logStd("batchRelease aid=%d;sid=%d", p.getInt(PreSaleHdDef.Info.AID,0), Session.getSid());
        	}
        }catch(Exception e){
        	ykDao.rollback();
			Log.logStd("del and insert err: %s", e);
			return "{\"success\":false, \"msg\":\"批量释放失败！\"}";
        }finally{
        	ykDao.close();
        }
     	
    	return "{\"success\":true, \"msg\":\"批量释放成功！\"}";

    }

    private int admRelease() throws Exception {
        int rt = Errno.ERROR;
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        Dao ossDao = WebOss.getOssBsDao();
        Dao bssMainDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            String table = "acctPreSaleHd";//ossbs库
            sltArg.table = table;
            FaiList<String> staffList = new FaiList<String>();
            staffList.add("mark");
            staffList.add("akira");
            staffList.add("liangyanhong");
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, staffList);
            sltArg.searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.PERSON);
            FaiList<Param> list = ossDao.select(sltArg);
            FaiList<Integer> aidIntList = new FaiList<Integer>();
            for (Param item : list) {
                aidIntList.add(item.getInt("aid"));
            }
            FaiList<Integer> taList = new FaiList<Integer>();
            taList.add(470);
            taList.add(471);
            taList.add(472);
            taList.add(474);
            taList.add(475);
            taList.add(476);
            taList.add(477);
            taList.add(478);
            taList.add(479);
            taList.add(495);
            taList.add(496);
            sltArg.table = "acctStatus";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            sltArg.searchArg.matcher.and("ta", ParamMatcher.IN, taList);
            FaiList<Param> inTaList = bssMainDao.select(sltArg);
            for (Param item : inTaList) {
                int aid = item.getInt("aid");
                sltArg.table = "acctPreSaleHd";
                sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.EQ, aid);
                sltArg.searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.PERSON);
                sltArg.searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, staffList);
                FaiList<Param> dataList = ossDao.select(sltArg);

                if (dataList != null && !dataList.isEmpty()) {
                    Param data = dataList.get(0);
                    rt = sysPreSaleHd.del(aid);
                    if (rt != Errno.OK) {
                        Log.logErr(rt, "error:release preSale err; aid=%d;", aid);
                        return rt;
                    } else {
                        Log.logStd(rt, "success:release data=%s", data);
                    }
                    // 记录系统痕迹
                    String action = "释放客户：" + aid + ";销售组 释放 活动引流 个人库";
                    setRecord(aid, data.getString(PreSaleHdDef.Info.SALES_ACCT, ""), HdSaleRecordDef.AttrType.RELEASE_PERSON, action, Calendar.getInstance(), data.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
                    Log.logStd("releasePreSale aid=%d;sid=%d", aid, Session.getSid());
                } else {
                    Log.logErr("del not found aid=%s", aid);
                }
            }
        } finally {
            ossDao.close();
            bssMainDao.close();
        }
        return 0;
    }

    /**
     *	互动销售-修改备注或标记等信息
     */
    public String setInfo(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authPreSale|authYKSale1|authYkSaleLeader", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        String mark = Parser.parseString(request.getParameter("mark"), "");
        String reason = Parser.parseString(request.getParameter("reason"), "");
        int intent = Parser.parseInt(request.getParameter("intent"), 0);
        String type = Parser.parseString(request.getParameter("type"), "");

        if (aid == 0 || type.equals("")) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }

        Param info = new Param();
        info.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
        info.setCalendar(PreSaleHdDef.Info.OPT_TIME, Calendar.getInstance());
        if (type.equals("intent")) {
            info.setInt(PreSaleHdDef.Info.INTENT, intent);
        }
        if (type.equals("mark")) {
            info.setString(PreSaleHdDef.Info.MARK, mark);
            //info.setString(PreSaleHdDef.Info.DIS_BUY_REASON, reason);
        }

        rt = sysPreSaleYk.setPreSale(aid, new ParamUpdater(info));
        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"修改失败\"}";
        }

        return "{\"success\":true, \"msg\":\"修改成功\"}";
    }

    /**
     *	直销售前-修改领取时间(组长才有的权限)
     */
    public String setReceiveTime(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authHDSaleManage", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口

        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        Calendar receiveTime = Parser.parseCalendar(request.getParameter("receiveTime"), "yyyy-MM-dd hh:mm:ss");
        String salesAcct = Parser.parseString(request.getParameter("salesAcct"), "");

        if (aid == 0 || salesAcct.isEmpty()) {
            return "{\"success\":false, \"msg\":\"参数错误。\"}";
        }

        // 获取旧数据
        Param oldInfo = sysPreSaleHd.getInfo(aid);
        if (oldInfo.isEmpty()) {
            return "{\"success\":false, \"msg\":\"数据错误\"}";
        }

        // 判断领取时间的修改是否符合规矩
        Calendar oldTime = oldInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);
        if (oldTime == null) {
            return "{\"success\":false, \"msg\":\"数据错误\"}";
        }

        // 时间戳
        long oldStamp = oldTime.getTimeInMillis();
        long newStamp = receiveTime.getTimeInMillis();
        long nowStamp = Calendar.getInstance().getTimeInMillis();

        if (newStamp > oldStamp) {
            return "{\"success\":false, \"msg\":\"领取时间不能大于当前领取时间！\"}";
        }
        if ((nowStamp - newStamp) > 30 * 24 * 60 * 60 * 1000) {
            return "{\"success\":false, \"msg\":\"领取时间不能超前30天！\"}";
        }

        // 更新领取时间
        Param upInfo = new Param();
        upInfo.setCalendar(PreSaleHdDef.Info.RECEIVE_TIME, receiveTime);
        upInfo.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());

        rt = sysPreSaleHd.update(aid, new ParamUpdater(upInfo));
        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"修改失败\"}";
        }

        // 记录系统痕迹
        String oldReceiveTime = Parser.parseString(oldInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
        salesAcct = oldInfo.getHtmlString(PreSaleHdDef.Info.SALES_ACCT);
        Param salesAcctInfo = Acct.getStaffInfo(Web.getFaiCid(), salesAcct);
        String salesName = salesAcctInfo.getHtmlString(StaffDef.Info.NAME);

        String optSacct = WebOss.getSacct();
        String action = salesName + "(" + salesAcct + ") 的客户：" + aid + " 的领取时间从(" + oldReceiveTime + ")修改为(" + Parser.parseString(receiveTime) + ")。";
        setRecord(aid, optSacct, HdSaleRecordDef.AttrType.CREATETIME_CLIENT, action, receiveTime, oldInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));

        return "{\"success\":true, \"msg\":\"修改成功\"}";
    }

    /**
     * 配置互动销售
     */
    public String setHDSales(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authHDSaleManage", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);

        String sids = Parser.parseString(request.getParameter("sids"), "").trim();
        // 分割sid
        String[] sidArrary = sids.split("\\|");
        FaiList<Integer> sidList = new FaiList<Integer>();

        for (String sid : sidArrary) {
            sidList.add(Parser.parseInt(sid, 0));
        }

        // 检查权限
        for (Integer sid : sidList) {
            if (!isAuth(sid, "authHDSale")) {
                return "{\"success\":false, \"msg\":\"配置的账号里有非互动销售存在\"}";
            }
        }

        // 更新的信息
        Param info = new Param();
        info.setInt(PreSaleUtilDef.Conf.TYPE, PreSaleUtilDef.ConfType.HD_ALLOT);
        info.setString(PreSaleUtilDef.Conf.HdAllot.SIDS, sidList.toJson());
        info.setCalendar(PreSaleUtilDef.Conf.UPDATE_TIME, Calendar.getInstance());

        // 查旧数据
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.HD_ALLOT);

        // 执行更新
        FaiList<Param> oldList = sysPreSaleUtil.searchPreSaleConf(searchArg);
        if (oldList.size() > 0) {
            int id = oldList.get(0).getInt(PreSaleUtilDef.Conf.ID, 0);
            rt = sysPreSaleUtil.setPreSaleConf(id, new ParamUpdater(info));
        } else {
            info.setCalendar(PreSaleUtilDef.Conf.CREATE_TIME, Calendar.getInstance());
            rt = sysPreSaleUtil.addPreSaleConf(info);
        }


        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"配置失败。\"}";
        }

        return "{\"success\":true, \"msg\":\"配置成功\"}";
    }

    /**
     * 随机出aidList
     * 1.各个list的限制大小问题
     * 2.跳出条件，防止死循环的问题		ps 查不到数据就跳出，所以不会发生死循环的情况
     * 3.sql语句长度的问题
     *
     * 实现思路：
     * 1.根据选定时间均分出12个时间段
     * 2.每个时间段获取6000个资源，然后从这些资源里面随机获取600个资源用于分配，并过滤掉已被领取的资源
     * 3.执行一次获取不够6000资源的时候，继续递归，直至获取够6000或者查无资源则跳出
     */
    public void getAllotAidList(int allotNum, int startTime, int endTime, HashSet<Integer> notInAidSet, FaiList<String> notInCityList, HashSet<Integer> aidSet, FaiList<Integer> notInTaList) throws Exception {
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);            // bss 的接口
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // 互动销售 的接口
        SysBrand sysBrand = (SysBrand) Core.getSysKit(Kid.SYS_BRAND);                    // brand 的接口
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);// presaleUTIL 的接口

        FaiList<Integer> notInAidList = new FaiList<Integer>(notInAidSet);
        FaiList<Integer> acctAidList = new FaiList<Integer>();
        if (allotNum <= 0) {
            return;
        }

        // 从startTime - endTime 中平均拿出12个时间段来进行分配
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);

        int range = (endTime - startTime) / 12;

        // 登录时间条件(2个月内的数据不用管登录时间，按60天算)
        ParamMatcher loginMatcher = new ParamMatcher(BssStatDef.Info.LOGIN_TIME, ParamMatcher.NE, new Null(0));
        loginMatcher.or(BssStatDef.Info.REG_TIME, ParamMatcher.GE, (endTime - 2 * 30 * 24 * 60 * 60));

        // 具体值参考 oss/operate/def.jsp.inc
        FaiList<Integer> goalList = new FaiList<Integer>();
        goalList.add(102);    // 电商引流
        goalList.add(103);    // 现场活动
        goalList.add(101);    // 门店引流
        goalList.add(104);    // 品牌传播
        goalList.add(100);    // 微信吸粉

        // 平均24个时间出来各取一点资源
        for (int i = 0; i < 12; i++) {
            int time = startTime + (range * i);
            int time2 = time + range;

            // 有手机号的建站客户；最近登录时间不为空的
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.NOT_IN, notInAidList);    // 排除掉品牌代理商报备aid
            searchArg.matcher.and(BssStatDef.Info.REG_BIZ, ParamMatcher.EQ, BssStatDef.RegBiz.REG_HD);                        // 注册产品类型
            searchArg.matcher.and(BssStatDef.Info.REG_CITY, ParamMatcher.NOT_IN, notInCityList);            // 排除掉品牌代理商所在城市
            searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.GE, time);
            searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.LE, time2);
            searchArg.matcher.and(loginMatcher);
            searchArg.matcher.and(BssStatDef.Info.REG_MOBILE, ParamMatcher.NE, new Null(0));
            searchArg.matcher.and(BssStatDef.Info.REG_MOBILE, ParamMatcher.NE, "");
            searchArg.matcher.and(BssStatDef.Info.COMPANYGOAL, ParamMatcher.IN, goalList);
            searchArg.matcher.and(BssStatDef.Info.HD_VERSION, ParamMatcher.EQ, 0);
            if (notInTaList != null && !notInTaList.isEmpty()) {
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, notInTaList);
            }
            searchArg.start = 0;
            searchArg.limit = allotNum * 2;
           // FaiList<Param> tmpList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

            FaiList<Object> outerExpr = new FaiList<Object>();
            outerExpr.add(NOT_IN.contains("aid", notInAidList));
            outerExpr.add(EQ.of("reg_biz", 1));
            outerExpr.add(NOT_IN.contains("reg_city", notInCityList));
            outerExpr.add(GE.of("reg_time", time));
            outerExpr.add(LE.of("reg_time", time2));
            outerExpr.add(NE.of("reg_mobile", 0));
            outerExpr.add(NE.of("reg_mobile", ""));
            outerExpr.add(IN.contains("reg_company_goal", goalList));
            outerExpr.add(EQ.of("version_hd", 0));
            if (notInTaList != null && !notInTaList.isEmpty()) {
                outerExpr.add(NOT_IN.contains("reg_ta", notInTaList));
            }
            outerExpr.add(NOT_IN.contains("reg_city", notInCityList));
            FdpDataParamMatcher matcher1 = AND.expr(outerExpr);
            matcher1.or("reg_time", ParamMatcher.GE, (endTime - 2 * 30 * 24 * 60 * 60));
            matcher1.and("last_login_time", ParamMatcher.NE, new Null(0));

            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("aid")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(matcher1)
                    .page(0, allotNum * 2)
                    .execute(Core.getFlow());
            FaiList<Param> tmpList = execute.getData();




            Collections.shuffle(tmpList);    // 打乱aid顺序
            int num = allotNum / 20;        // 乱序取出600资源
            num = num == 0 ? allotNum : num;

            for (int j = 0; j < num && j < tmpList.size(); j++) {
                Param p = tmpList.get(j);
                Integer aid = p.getInt(BssStatDef.Info.AID, 0);

                notInAidSet.add(aid);        // 分不够需要继续分多次的时候才需要用到,下一次继续分的时候跳过这批aid
                acctAidList.add(aid);
                aidSet.add(aid);            // 存入可用的aid
            }
        }

        // 排除掉已在互动销售表里的数据
        fieldList.clear();
        fieldList.add(PreSaleHdDef.Info.AID);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, acctAidList);
        FaiList<Param> tmpList = sysPreSaleHd.getList(fieldList, searchArg);

        for (Param p : tmpList) {
            Integer aid = p.getInt(PreSaleHdDef.Info.AID);
            aidSet.remove(aid);
            acctAidList.remove(aid);
        }

        // 排除品牌商aid
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BrandDef.BrandClient.AID, ParamMatcher.IN, acctAidList);
        tmpList = sysBrand.searchBrandClient(searchArg);

        for (Param p : tmpList) {
            Integer aid = p.getInt(BrandDef.BrandClient.AID);
            aidSet.remove(aid);
            acctAidList.remove(aid);
        }

        // remove曾领取过的客户
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.AID, ParamMatcher.IN, acctAidList);//查一下销售释放记录，有被销售释放过的资源不再分配
        //tmpList = sysPreSaleUtil.getPreSaleLogList(searchArg);

        Dao ossDao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "*";
            selectArg.table = "hdSaleRecord";
            selectArg.searchArg = searchArg;
            tmpList = ossDao.select(selectArg);
        } finally {
            ossDao.close();
        }
        for (Param p : tmpList) {
            Integer aid = p.getInt(HdSaleRecordDef.Info.AID, 0);
            aidSet.remove(aid);
            acctAidList.remove(aid);
        }

        // 如果查不到数据就跳出吧，避免一直循环
        if (acctAidList.size() <= 0) {
            return;
        }

        // 分不够，继续循环
        if (aidSet.size() < allotNum) {
            getAllotAidList(allotNum, startTime, endTime, notInAidSet, notInCityList, aidSet, notInTaList);
        }
    }

    /**
     * 给某个销售分配资源到个人库，将个人库数量填充到6000
     */
    public String allotPerson(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authAdm", false) && Session.getSid() != 753) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        // 个人库最大数量;
        int maxNum = 6000;
        if (Web.getDebug()) {
            maxNum = 9;
        }

        int rt = Errno.ERROR;
        int personCnt = 0;                                                                // 个人库数量
        FaiList<Integer> aidList = new FaiList<Integer>();                                // aidList
        FaiList<String> fieldList = new FaiList<String>();                                // 字段list

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);    // presaleUTIL 的接口
        SysNewUnion sysNewUnion = (SysNewUnion) Core.getSysKit(Kid.SYS_NEWUNION);                    // newUnion 的接口
        SysBrand sysBrand = (SysBrand) Core.getSysKit(Kid.SYS_BRAND);                                // brand 的接口

        int allotNum = 6000;                                                            // 分配数量，以前是提供填写然后传过来的，后来写死6000了
        String salesAcct = Parser.parseString(request.getParameter("salesAcct"), "");//销售账号
        String begDate = Parser.parseString(request.getParameter("dateBeg"), "");
        String endDate = Parser.parseString(request.getParameter("dateEnd"), "");
        // 检查权限

        FaiList<Param> hdSaleList = new FaiList<Param>();
        SearchArg searchArgSale = new SearchArg();
        searchArgSale.matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, salesAcct);

        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        if (!cli.init()) {
            throw new Exception("PreSaleHdCli init error");
        }*/
        FaiList<String> fieldListSale = new FaiList<String>();
        fieldListSale.add("*");
        /*rt = cli.getSalesList(fieldListSale, searchArgSale, hdSaleList);
        if (rt != Errno.OK) {
            Log.logErr(rt, "get hdSale err");
        }*/
        if (hdSaleList == null || hdSaleList.size() == 0) {
            return "{\"success\":false, \"msg\":\"账号错误。该销售不在销售库\"}";
        } else {
            int saleSid = hdSaleList.get(0).getInt("sid", 0);
            HdOss hdOss = (HdOss) Core.getCorpKit(1, Kid.HD_OSS);
            Param sale = hdOss.getStaff(saleSid);
            Log.logDbg("yansen sale=%s", sale);
            int saleFlag = sale.getInt(HdOssStaffDef.Info.AUTH, 0);
            if (!Misc.checkBit(saleFlag, HdOssStaffDef.Auth.HD_SALE)) {
                return "{\"success\":false, \"msg\":\"账号错误，请先设置权限。\"}";
            }
        }

        // 设置时间
        int startTime = (int) (Parser.parseCalendar(begDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endTime = (int) (Parser.parseCalendar(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        /***************************** 1.查询该销售个人库的总量以及计算分配量 **********************************/
        fieldList.clear();
        fieldList.add(PreSaleHdDef.Info.AID);

        SearchArg searchArg = new SearchArg();

        searchArg.limit = 1;
        searchArg.totalSize = new Ref<Integer>();
        searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
        searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.PERSON);

        FaiList<Param> tmpList = sysPreSaleHd.getList(fieldList, searchArg);
        personCnt = searchArg.totalSize.value;

        // 计算分配量
        if (maxNum - personCnt <= 0) {
            return "{\"success\":false, \"msg\":\"个人库已达到标准。\"}";
        }

        // 填充到6000
        if (allotNum > maxNum - personCnt) {
            allotNum = maxNum - personCnt;
        }

        /************************* 2.筛选可被领取的资源 **************************************/
        HashSet<Integer> notInAidSet = new HashSet<Integer>();

        // 排除已被品牌商报备的部分
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BrandDef.BrandReport.DEL, ParamMatcher.EQ, false);
        searchArg.matcher.and(BrandDef.BrandReport.STATUS, ParamMatcher.NE, BrandDef.ReportStatus.REFUSE);
        searchArg.matcher.and(BrandDef.BrandReport.STATUS, ParamMatcher.NE, BrandDef.ReportStatus.RELEASE);

        tmpList = sysBrand.searchBrandReport(searchArg);
        for (Param p : tmpList) {
            Integer aid = p.getInt(BrandDef.BrandReport.AID, 0);
            notInAidSet.add(aid);
        }

        // 品牌代理商(授权没到期)城市部分
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.SALE_CITY);
        tmpList = sysPreSaleUtil.searchPreSaleConf(searchArg);

        FaiList<String> notInCityList = new FaiList<String>();
        for (Param p : tmpList) {
            String city = p.getHtmlString(PreSaleUtilDef.Conf.SaleCity.CITY);

            if (!city.isEmpty()) {
                notInCityList.add(city);

                // 如果城市不带市字，则加上市字
                if (!city.contains("市")) {
                    city += "市";
                    notInCityList.add(city);
                }
            }
        }
        //判断是否未转正员工，未转正员工不派发活动引流资源。

        FaiList<Integer> notInTaList = new FaiList<Integer>();
        Dao dao = WebOss.getBssMainDaoMaster();
        try {
            FaiList<Param> nonCorrectionList = getNonCorrectionStaff();
            boolean non = true;
			/*for(Param item :nonCorrectionList){
				if(salesAcct.equals(item.getString("sacct"))){
					non = true;
					break;
				}
			}*/
            if (non) {
                String sql = "select ta from ta where groupId =32";
                FaiList<Param> taList = new FaiList<Param>();
                taList = dao.executeQuery(sql);
                if (taList != null) {
                    for (Param item : taList) {
                        notInTaList.add(item.getInt("ta"));
                    }
                }
            }
        } finally {
            dao.close();
        }
        // 获取可分配的资源
        HashSet<Integer> aidSet = new HashSet<Integer>();
        getAllotAidList(allotNum, startTime, endTime, notInAidSet, notInCityList, aidSet, notInTaList);

        aidList = new FaiList<Integer>(aidSet);
        // remove 掉这批aid （因为销售覆盖字段是晚上凌晨才同步的，这时候就会有今天领取的资源没有置起该字段）
        for (Param p : tmpList) {
            Integer aid = p.getInt(PreSaleHdDef.Info.AID);
            aidList.remove(aid);
        }

        /************************************ 5.随机出分配的资源 ***********************/

        Collections.shuffle(aidList);                    // 打乱aid顺序
        FaiList<Param> dataList1 = new FaiList<Param>();
        FaiList<Param> dataList2 = new FaiList<Param>();

        FaiList<Integer> realAidList = new FaiList<Integer>();
        for (int i = 0; i < aidList.size() && i < allotNum; i++) {
            int aid = aidList.get(i);
            realAidList.add(aid);

            Param preSale = new Param();
            preSale.setInt(PreSaleHdDef.Info.AID, aid);
            preSale.setInt(PreSaleHdDef.Info.DEL, 0);
            preSale.setString(PreSaleHdDef.Info.SALES_ACCT, salesAcct);
            preSale.setInt(PreSaleHdDef.Info.TYPE, PreSaleHdDef.Type.AFTER);
            preSale.setInt(PreSaleHdDef.Info.STATUS, PreSaleHdDef.Status.PERSON);
            preSale.setCalendar(PreSaleHdDef.Info.RECEIVE_TIME, Calendar.getInstance());
            preSale.setCalendar(PreSaleHdDef.Info.CREATE_TIME, Calendar.getInstance());
            preSale.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());

            dataList1.add(preSale);
        }

        /***************************** 6.存客户 **********************************/
        int cnt = dataList1.size();
        rt = sysPreSaleHd.batchInsert(dataList1);
        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"分配失败\"}";
        }


        /***************************** 存系统痕迹记录 **********************************/
        for (int aid : realAidList) {
            // 领取记录表
            Param preSaleLog = new Param();
            String action = "系统自动分配，客户： " + aid;
            preSaleLog.setInt(HdSaleRecordDef.Info.AID, aid);
            preSaleLog.setString(HdSaleRecordDef.Info.SACCT, salesAcct);
            preSaleLog.setInt(HdSaleRecordDef.Info.ATTR_TYPE, HdSaleRecordDef.AttrType.PHONE_PERSON);
            preSaleLog.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
            preSaleLog.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, Calendar.getInstance());
            preSaleLog.setString(HdSaleRecordDef.Info.ACTION, action);

            dataList2.add(preSaleLog);
        }

        /***************************** 8.存领取痕迹 **********************************/
        //FaiList<Integer> queueIdList = new FaiList<Integer>();
        //rt = sysPreSaleUtil.batchAddPreSaleLogList(dataList2, queueIdList);


        Dao ossDao = WebOss.getOssBsDao();
        try {
            rt = ossDao.batchInsert("hdSaleRecord", dataList2);
        } finally {
            ossDao.close();
        }

        if (rt != Errno.OK) {
            Log.logErr(rt, "error:allotPerson batchInsert acctPreLog err;");
            return "{\"success\":true, \"msg\":\"分配成功。存取日志失败：" + cnt + ";\"}";
        }

        return "{\"success\":true, \"msg\":\"分配成功。分配数量：" + cnt + ";\"}";
    }

    /*查询未转正员工*/
    private FaiList<Param> getNonCorrectionStaff() throws Exception {
        FaiList<Param> acctList = null;
        Dao dao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdSale";
            sltArg.searchArg.matcher = new ParamMatcher("sid", ParamMatcher.NE, 0);
            sltArg.searchArg.matcher.and(HdSaleDef.Info.STATUS, ParamMatcher.EQ, 0);
            acctList = dao.select(sltArg);
        } finally {
            dao.close();
        }
        return acctList;
    }

    /**
     * 查询是否符合审批领取的条件
     */
    public String isApprove(int aid) throws Exception {
    	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass); 
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);    // 销售工具 的接口
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);                    // bss 的接口

        // 避免存入脏数据
        //Param bssAcctInfo = sysBssStat.getAcctStatusInfo(aid);

        //迁移到新的查询方式
        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("*")
                .from("fdpData", "dws_fkw_acct_info")
                .where(AND.expr(EQ.of("aid", aid)))
                .execute(Core.getFlow());
        Log.logDbg("execute:"+execute);
        Param bssAcctInfo = execute.getData().get(0);

        if (bssAcctInfo.isEmpty()) {
            Log.logStd("acctStatus empty; optSid=%d; aid=%d; ", Session.getSid(), aid);
        }
        
     	// 排查该客户是否已被悦客销售领取
        Param salesInfo = sysPreSaleYk.getInfo(aid);
        String salesAcct = salesInfo.getString(PreSaleHdDef.Info.SALES_ACCT, "");
        if (!salesAcct.isEmpty()) {
            return "错误，该客户已被领取";
        }

        // 判断该客户是否是区域品牌代理商所在的城市
        String regCity = bssAcctInfo.getString("reg_city", "");
        if (!regCity.isEmpty()) {

            // 加个保险，品牌代理商那里存的不知道有没有带市字
            String cityOr = regCity;
            if (!cityOr.contains("市")) {
                cityOr += "市";
            } else if (cityOr.contains("市")) {
                cityOr = Str.replace(cityOr, "市", "");
            }

            FaiList<String> cityList = new FaiList<String>();
            cityList.add(regCity);
            cityList.add(cityOr);

            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.SALE_CITY);
            searchArg.matcher.and(PreSaleUtilDef.Conf.SaleCity.CITY, ParamMatcher.IN, cityList);

            FaiList<Param> list = sysPreSaleUtil.searchPreSaleConf(searchArg);
            if (list.size() > 0) {
                return "错误，该客户属于品牌代理商所在城市";
            }
        }

        return "OK";
    }

    /**
     * 申请审批领取
     */
    public String addPreSaleApprove(HttpServletRequest request) throws Exception {
    	 if (!Auth.checkFaiscoAuth("authAdm", false) && !Auth.checkFaiscoAuth("authYkSaleLeader", false) && Session.getSid() != 1535) {
             return "{\"success\":false, \"msg\":\"没有权限\"}";
         }

        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);            // presale 的接口
        int sid = Session.getSid();

        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int oldAid = Parser.parseInt(request.getParameter("oldAid"), 0);
        int approveStyle = Parser.parseInt(request.getParameter("approveStyle"), -1);    // 报备类型
        String mark = Parser.parseString(request.getParameter("mark"), "");
        String fileId = Parser.parseString(request.getParameter("fileId"), "");            // 文件id
        String newReceiveTime = request.getParameter("receiveTime");
        Calendar receiveTime = Calendar.getInstance();
        if (newReceiveTime != null) {
            receiveTime = Parser.parseCalendar(request.getParameter("receiveTime"), "yyyy-MM-dd HH:mm:ss");
        }
        if (aid == 0 || mark.equals("") && approveStyle != HdSaleApproveDef.ApproveStyle.EDIT_RECEIVETIME || approveStyle == -1) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }
        // 检查该aid客户是否符合申请的条件
        if (approveStyle != HdSaleApproveDef.ApproveStyle.EDIT_RECEIVETIME) {
            String msg = isApprove(aid);
            if (!"OK".equals(msg)) {
                return "{\"success\":false, \"msg\":\"" + msg + "\"}";
            }
        }

        Param data = new Param();
        data.setInt(HdSaleApproveDef.Info.AID, aid);
        data.setInt(HdSaleApproveDef.Info.OLD_AID, oldAid);
        data.setInt(HdSaleApproveDef.Info.SID, sid);
        data.setInt(HdSaleApproveDef.Info.APPROVE_STYLE, approveStyle);
        data.setInt(HdSaleApproveDef.Info.STATUS, HdSaleApproveDef.ApproveStatus.INIT);
        data.setString(HdSaleApproveDef.Info.MARK, mark);
        data.setString(HdSaleApproveDef.Info.FILEID, fileId);
        data.setCalendar(HdSaleApproveDef.Info.CREATE_TIME, Calendar.getInstance());

        if (newReceiveTime != null) {
            data.setCalendar("newReceiveTime", receiveTime);
        }
        Dao ykDao = WebHdOss.getYkOssDaoMaster();
        try {
            int rt = ykDao.insert("ykSaleApprove", data);
            if (rt != Errno.OK) {
                return "{\"success\":false, \"msg\":\"申请失败。\"}";
            }
        } finally {
        	ykDao.close();
        }
        return "{\"success\":true, \"msg\":\"申请成功\"}";
    }

    /**
     * 修改申请状态，同意或拒绝
     */
    public String setApproveStatus(HttpServletRequest request) throws Exception {
        int _sid = Session.getSid();

        if (!Auth.checkFaiscoAuth("authYkSaleLeader", false) && _sid != 1535 && _sid != 987) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        int id = Parser.parseInt(request.getParameter("id"), 0);
        String type = Parser.parseString(request.getParameter("type"), "");
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);

        if (id == 0 || (!type.equals("agree") && !type.equals("refuse"))) {
            return "{\"success\":false, \"msg\":\"参数错误1。\"}";
        }

        // oldInfo
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Approve.ID, ParamMatcher.EQ, id);
        FaiList<Param> infoList = new FaiList<Param>();
        Dao ykDao = WebHdOss.getYkOssDaoSlave();
        try {
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "*";
            selectArg.table = "ykSaleApprove";
            selectArg.searchArg = searchArg;
            infoList = ykDao.select(selectArg);
        } finally {
        	ykDao.close();
        }
        if (infoList.isEmpty()) {
            return "{\"success\":false, \"msg\":\"参数错误2。\"}";
        }
        Param oldInfo = infoList.get(0);//审批信息

        // 检查申请人是否离职
        int sid = oldInfo.getInt(HdSaleApproveDef.Info.SID, 0);
        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), sid);
        if (!getChecked(staffInfo, StaffDef.AuthBit.LOGIN)) {
            return "{\"success\":false, \"msg\":\"申请人已离职，请删除该条记录。\"}";
        }

        // 客户aid
        int aid = oldInfo.getInt(HdSaleApproveDef.Info.AID, 0);
        String salesAcct = staffInfo.getString(StaffDef.Info.SACCT, "");

        // 销售类型
        int approveStyle = oldInfo.getInt(HdSaleApproveDef.Info.APPROVE_STYLE, 0);
        int oldAid = oldInfo.getInt(HdSaleApproveDef.Info.OLD_AID, 0);

        // 同意的话要先查该客户是否已被领取
        if (type.equals("agree")) {
            if (approveStyle == HdSaleApproveDef.ApproveStyle.EDIT_RECEIVETIME) {//修改领取时间。直接修改
                Param preSale = new Param();
                preSale.setCalendar(PreSaleHdDef.Info.RECEIVE_TIME, oldInfo.getCalendar(HdSaleApproveDef.Info.NEW_RECEIVE_TIME, Calendar.getInstance()));
                rt = sysPreSaleYk.setPreSale(aid, new ParamUpdater(preSale));
                if (rt != Errno.OK) {
                    return "{\"success\":false, \"msg\":\"存入销售个人库失败。\"}";
                }
                //存领取记录
                String action = "审批分配，修改领取时间，客户： " + aid;
                setRecord(aid, salesAcct, HdSaleRecordDef.AttrType.APPROVE, action, Calendar.getInstance(), oldInfo.getCalendar(HdSaleApproveDef.Info.NEW_RECEIVE_TIME, Calendar.getInstance()));
            }else {
                // 检查该aid客户是否符合申请的条件
                String msg = isApprove(aid);
                if (!"OK".equals(msg)) {
                    return "{\"success\":false, \"msg\":\"" + msg + "\"}";
                }

                // 将该客户分配到销售的个人库
                Param salesInfo = sysPreSaleYk.getInfo(aid);

                Param preSale = new Param();
                preSale.setInt(PreSaleHdDef.Info.DEL, 0);
                preSale.setString(PreSaleHdDef.Info.SALES_ACCT, salesAcct);
                preSale.setInt(PreSaleHdDef.Info.TYPE, PreSaleHdDef.Type.APPROVE);
                preSale.setInt(PreSaleHdDef.Info.STATUS, PreSaleHdDef.Status.PERSON);
                preSale.setCalendar(PreSaleHdDef.Info.RECEIVE_TIME, Calendar.getInstance());
                preSale.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
                if (salesInfo.isEmpty()) {
                    preSale.setInt(PreSaleHdDef.Info.AID, aid);
                    preSale.setCalendar(PreSaleHdDef.Info.CREATE_TIME, Calendar.getInstance());
                    rt = sysPreSaleYk.addPreSale(preSale);
                } else {
                    rt = sysPreSaleYk.setPreSale(aid, new ParamUpdater(preSale));
                }
                if (rt != Errno.OK) {
                    return "{\"success\":false, \"msg\":\"存入销售个人库失败。\"}";
                }

                // 存领取记录
                String action = "审批分配，客户： " + aid;
                setRecord(aid, salesAcct, HdSaleRecordDef.AttrType.APPROVE, action, Calendar.getInstance(), Calendar.getInstance(), preSale);
            }

        }

        // 执行更新
        Param upInfo = new Param();
        int upStatus = type.equals("agree") ? PreSaleUtilDef.ApproveStatus.AGREE : PreSaleUtilDef.ApproveStatus.REFUSE;

        upInfo.setInt(PreSaleUtilDef.Approve.STATUS, upStatus);
        upInfo.setCalendar(PreSaleUtilDef.Approve.APPROVE_TIME, Calendar.getInstance());
        ykDao = WebHdOss.getYkOssDaoMaster();
        try {
            rt = ykDao.update("ykSaleApprove", new ParamUpdater(upInfo), new ParamMatcher(HdSaleApproveDef.Info.ID, ParamMatcher.EQ, id));
            if (rt != Errno.OK) {
                return "{\"success\":false, \"msg\":\"操作失败。\"}";
            }
        } finally {
        	ykDao.close();
        }

        // log一下操作信息
        Log.logStd("approve setApprove; opt_sid=%d; id=%d; aid=%d; str=%s;", _sid, id, aid, type);

        return "{\"success\":true, \"msg\":\"操作成功。\"}";
    }

    /**
     * 删除该条申请
     */
    public String delApprove(HttpServletRequest request) throws Exception {
        int _sid = Session.getSid();
        if (_sid != 1535 && _sid != 987 && _sid != 112) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int id = Parser.parseInt(request.getParameter("id"), 0);
        String type = Parser.parseString(request.getParameter("type"), "");
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);

        if (id == 0 || !type.equals("del")) {
            return "{\"success\":false, \"msg\":\"参数错误1。\"}";
        }

        // check oldInfo
        FaiList<Param> infoList = null;
        Param oldInfo = null;
        Dao ykDao =  WebHdOss.getYkOssDaoMaster();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            String table = "ykSaleApprove";
            sltArg.table = table;
            ParamMatcher matcher = new ParamMatcher(HdSaleApproveDef.Info.ID, ParamMatcher.EQ, id);
            sltArg.searchArg.matcher = matcher;
            infoList = ykDao.select(sltArg);
            if (infoList == null || infoList.isEmpty()) {
                return "{\"success\":false, \"msg\":\"参数错误2。\"}";
            }

            oldInfo = infoList.get(0);
            // 只有申请中的记录才允许被删除
            int status = oldInfo.getInt(PreSaleUtilDef.Approve.STATUS, -1);
            if (status != PreSaleUtilDef.ApproveStatus.INIT) {
                return "{\"success\":false, \"msg\":\"参数错误3。\"}";
            }

            int rt = ykDao.delete(table, matcher);
            if (rt != Errno.OK) {
                return "{\"success\":false, \"msg\":\"删除失败。\"}";
            }
        } finally {
        	ykDao.close();
        }
        // log一下操作信息
        Log.logStd("approve delApprove; opt_sid=%d; id=%d; aid=%d; str=%s;", _sid, id, oldInfo.getInt(PreSaleUtilDef.Approve.AID), type);
        return "{\"success\":true, \"msg\":\"删除成功。\"}";
    }

    /**
     * 上传图片
     */
    private String upLoadFile(HttpServletRequest request) throws Exception {
        int cid = Session.getCid();
        Param info = new Param();
        int rt = FileUpload.uploadImg(getServletContext(), request, cid, FileStgDef.App.OSS_CFG, false, 500, 500, Img.Mode.SCALE_DEFLATE_FILL, null, info);
        switch (rt) {
            case Errno.OK:
                String id = info.getJsonString(FileUpload.Info.ID);
                info.setBoolean("success", true);
                info.setString("fileId", id);
                return info.toJson();
            case Errno.SVR_READONLY:
                return "{\"success\":false,\"msg\":\"系统维护中，暂时不能保存，请稍后重试。\"}";
            case Errno.ARGS_ERROR:
                return "{\"success\":false,\"msg\":\"参数错误\"}";
            case FileUpload.ErrnoUpload.FILE_TYPE_INVALID:
                return "{\"success\":false,\"msg\":\"文件格式错误，请重新选择文件。\"}";
            default:
                return "{\"success\":false,\"msg\":\"系统错误 " + rt + "\"}";
        }
    }

    /**
     * 清除离职员工的数据；转换成缓存库
     */
    public String cleanLeave(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authHDSaleManage", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        String msg = "";
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口

        String salesAcct = Parser.parseString(request.getParameter("salesAcct"), "");
        if (salesAcct.isEmpty()) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }

        // 确认销售已经离职
        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), salesAcct);
        if (getChecked(staffInfo, StaffDef.AuthBit.LOGIN)) {
            return "{\"success\":false, \"msg\":\"错误：" + salesAcct + " 还没离职。如确认已经离职，请联系运营去除该账号的登录权限\"}";
        }

        /************* 1.客户数据部分  *************/
        // 拿到该销售的所有领取数据

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
        FaiList<Param> preSaleList = sysPreSaleHd.getList(searchArg);

        Log.logStd("cleanLeave acctPreSaleHd start optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        Log.logStd("cleanLeave acctPreSaleHd delInfo=%s;", preSaleList.toJson());

        // 互动销售执行删除
        ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
        rt = sysPreSaleHd.delAll(salesAcct, matcher);
        if (rt != Errno.OK) {
            msg += "客户释放失败；";
            Log.logStd("cleanLeave acctPreSale false; optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        } else {
            msg += "客户释放成功；";
            Log.logStd("cleanLeave acctPreSale success; optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        }
        /************* 1.客户数据部分 END  *************/

        /************* 3.领取记录部分  *************/
        for (Param p : preSaleList) {
            int aid = p.getInt(PreSaleHdDef.Info.AID, 0);
            int status = p.getInt(PreSaleHdDef.Info.STATUS, 0);

            // 记录系统痕迹
            String action = "释放客户：" + aid + "; 清除离职员工数据。";
            int attrType = HdSaleRecordDef.AttrType.RELEASE_DEAL;
            if (status == PreSaleHdDef.Status.PERSON) {
                attrType = HdSaleRecordDef.AttrType.RELEASE_PERSON;
            }

            setRecord(aid, salesAcct, attrType, action, Calendar.getInstance(), p.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
        }

        return "{\"success\":true, \"msg\":\"" + msg + "\"}";
    }

    /**
     * 清除员工领取数据  用于销售提的一些特殊需求。。
     */
    public String cleanSaleAdm(HttpServletRequest request) throws Exception {
        if (!Auth.checkFaiscoAuth("authHDSaleManage", false)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        int rt = Errno.ERROR;
        String msg = "";
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口

        String salesAcct = Parser.parseString(request.getParameter("salesAcct"), "");
        salesAcct = "hdsale2";
        if (salesAcct.isEmpty()) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }


        /************* 1.客户数据部分  *************/
        // 拿到该销售的所有领取数据

        SearchArg searchArg = new SearchArg();
        ParamMatcher saleMatcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
        saleMatcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, "2017-09-01 00:00:00");
        saleMatcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, "2017-10-31 23:59:59");
        searchArg.matcher = saleMatcher;
        FaiList<Param> preSaleList = sysPreSaleHd.getList(searchArg);
        Log.logStd("cleanLeave acctPreSaleHd start optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        ;
        // 互动销售执行删除
        ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
        rt = sysPreSaleHd.delAll(salesAcct, matcher);
        if (rt != Errno.OK) {
            msg += "客户释放失败；";
            Log.logStd("cleanLeave acctPreSale false; optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        } else {
            msg += "客户释放成功；";
            Log.logStd("cleanLeave acctPreSale success; optSid=%d; salesAcct=%s;", Session.getSid(), salesAcct);
        }
        /************* 1.客户数据部分 END  *************/

        /************* 3.领取记录部分  *************/
        for (Param p : preSaleList) {
            int aid = p.getInt(PreSaleHdDef.Info.AID, 0);
            int status = p.getInt(PreSaleHdDef.Info.STATUS, 0);

            // 记录系统痕迹
            String action = "释放客户：" + aid + "; 清除离职员工数据。";
            int attrType = HdSaleRecordDef.AttrType.RELEASE_DEAL;
            if (status == PreSaleHdDef.Status.PERSON) {
                attrType = HdSaleRecordDef.AttrType.RELEASE_PERSON;
            }

            setRecord(aid, salesAcct, attrType, action, Calendar.getInstance(), p.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME));
        }
        return "{\"success\":true, \"msg\":\"" + msg + "\"}";
    }

    /**
     * 互动销售-填写不购买原因以及标记
     */
    private String setYkReason(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);

        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int intent = Parser.parseInt(request.getParameter("intent"), 0);
        String reason = Parser.parseString(request.getParameter("reason"), "");        // 原因
        String mark = Parser.parseString(request.getParameter("mark"), "");            // 描述
        String corpMark = Parser.parseString(request.getParameter("corpMark"), "");    // 备注
        String talkNextTime = request.getParameter("talkNextTime");    // 下次联系时间
        Calendar talkNextTimeCal = Parser.parseCalendar(talkNextTime, "yyyy-MM-dd");
        int intentLevel = Parser.parseInt(request.getParameter("reasonVersionInputValue"), 0);//意向度
        // 校验
        if (aid <= 0) {
            return "{\"success\":false, \"msg\":\"网络错误，请刷新重试\"}";
        }
        if (corpMark.equals("") || corpMark.equals("undefined")) {
            return "{\"success\":false, \"msg\":\"备注不能为空！\"}";
        }

        // 填了无意向，则必须填不购买原因。2019.04.01 不购买原因隐藏
       /*  if (intent == PreSaleHdDef.Intent.NO_INTENT && reason.isEmpty()) {
            return "{\"success\":false, \"msg\":\"请选择不购买原因\"}";
        } */

        // 检查销售，只能改自己的客户
        Param oldInfo = sysPreSaleYk.getInfo(aid);
        String salesAcct = oldInfo.getString(PreSaleYkDef.Info.SALES_ACCT, "");
        if (!Web.isDev() && Session.getSid() != 1224) {
            if (!salesAcct.equals(WebOss.getSacct())) {
                return "{\"success\":false, \"msg\":\"错误：不是你的客户\"}";
            }
        }

        // 更新
        Param upInfo = new Param();
        upInfo.setInt(PreSaleYkDef.Info.INTENT, intent);
        upInfo.setString(PreSaleYkDef.Info.DIS_BUY_REASON, reason);
        //acctPreSaleYk表没有mark字段
        //upInfo.setString(PreSaleYkDef.Info.MARK, mark);
        upInfo.setCalendar(PreSaleYkDef.Info.UPDATE_TIME, Calendar.getInstance());
        upInfo.setCalendar(PreSaleYkDef.Info.OPT_TIME, Calendar.getInstance());
        upInfo.setCalendar(PreSaleYkDef.Info.CONTACT_TIME, Calendar.getInstance());//联系时间
        upInfo.setInt(PreSaleYkDef.Info.INTENT_LEVEL, intentLevel);
        if (talkNextTimeCal != null) {
            upInfo.setCalendar(PreSaleYkDef.Info.TALK_NEXT_TIME, talkNextTimeCal);
        }
        rt = sysPreSaleYk.setPreSale(aid, new ParamUpdater(upInfo));
        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }

        // 企业备注
        String newMark = "【" + WebOss.getStaffName(Session.getSid()) + " —— " + Parser.parseString(Calendar.getInstance()) + "】";

        // 标记
        String intentStr = "【" + PreSaleYkDef.getIntentName(intent) + "】";

        // 不够买原因
        String reasonStr = "";
        if (!reason.isEmpty()) {
            reasonStr = reason;

            if (!mark.isEmpty()) {
                reasonStr += "，" + mark;
            }

            reasonStr += "。";
        } 

        // 添加到企业备注
        newMark += intentStr + reasonStr + corpMark;
        WebOss.addCorpMark(aid, newMark);
        //如果提交备注成功。如果该资源是B库，并且不在成交库中，则把该资源从B库转入A库
        /* if (rt == Errno.OK) {
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(PreSaleYkDef.Info.AID, ParamMatcher.EQ, aid);
            searchArg.matcher.and(PreSaleYkDef.Info.FLAG, ParamMatcher.LAND, PreSaleYkDef.Flag.UN_CONTACT_30DAY, PreSaleYkDef.Flag.UN_CONTACT_30DAY);
            searchArg.matcher.and(PreSaleYkDef.Info.STATUS, ParamMatcher.NE, PreSaleYkDef.Status.DEAL);
            FaiList<Param> tmpList = PreSaleYkDef.getList(searchArg);
            FaiList<Param> hdRecordList = new FaiList<Param>();
            //Log.logStd("li test setInfoMark tmpList=%s",tmpList);

            //转入A库，修改acctPreSaleHd表
            //hdSaleRecord插入一条记录
            if (tmpList != null && tmpList.size() > 0) {
                int flag = tmpList.get(0).getInt(PreSaleYkDef.Info.FLAG, 0);
                flag = Misc.setFlag(flag, PreSaleYkDef.Flag.UN_CONTACT_30DAY, false);
                Param info = new Param();
                info.setInt(PreSaleYkDef.Info.FLAG, flag);
                info.setCalendar(PreSaleYkDef.Info.UPDATE_TIME, Calendar.getInstance());
                ParamUpdater updater = new ParamUpdater(info);
                rt = sysPreSaleYk.setPreSale(aid, updater);
                if (rt != Errno.OK) {
                    Log.logErr(rt, "setInfoMark update acctPreSaleHd error aid=%s", aid);
                    return "{\"success\":false, \"msg\":\"B库转入失败，请联系管理员！ \"}";
                }
                Dao ossDao = WebOss.getOssBsDao();
                try {
                    searchArg = new SearchArg();
                    searchArg.matcher = new ParamMatcher(YkSaleRecordDef.Info.AID, ParamMatcher.EQ, aid);
                    Dao.SelectArg selectArg = new Dao.SelectArg();
                    selectArg.field = "aid,sacct,receiveTime,tag,flag,taGroupName,ta";
                    selectArg.table = "hdSaleRecord";
                    selectArg.searchArg = searchArg;
                    hdRecordList = ossDao.select(selectArg);
                    if (hdRecordList != null && hdRecordList.size() > 0) {
                        searchArg = new SearchArg();
                        searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.AID, ParamMatcher.EQ, aid);
                        //根据领取时间排序
                        searchArg.cmpor = new ParamComparator("receiveTime", true);
                        Searcher searcher = new Searcher(searchArg);
                        hdRecordList = searcher.getParamList(hdRecordList);
                        //拿到最近领取的记录
                        Param record = hdRecordList.get(0);
                        record.setInt(HdSaleRecordDef.Info.ATTR_TYPE, HdSaleRecordDef.AttrType.BLIB_ENTER);
                        record.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
                        record.setString(HdSaleRecordDef.Info.ACTION, "B库转入");
                        rt = ossDao.insert("hdSaleRecord", record);
                        if (rt != Errno.OK) {
                            Log.logErr("setInfoMark insert hdSaleRecord err record=%s", record.toJson());
                            return "{\"success\":false, \"msg\":\"增加领取记录失败，请联系管理员! \"}";
                        }
                    } else {
                        return "{\"success\":false, \"msg\":\"参数有误，请联系管理员! \"}";
                    }

                } finally {
                    ossDao.close();
                }
            }
        } */


        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }

    /**
     * 设置每天最大分配量
     */
    private String setAwardCount(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        String staff = request.getParameter("staff");
        int awardCount = Parser.parseInt(request.getParameter("awardCount"), 0);
        Dao bssMainDao = null;
        Param result = new Param();

        FaiList<Integer> sidList = new FaiList<Integer>();//分配员工列表
        SearchArg searchArg = new SearchArg();
        Staff staffImpl = (Staff) Core.getCorpKit(Session.getCid(), Kid.STAFF);
        Param staffInfo = new Param();
        staffInfo = staffImpl.getStaffInfo(staff);
        if (staffInfo == null || staffInfo.isEmpty()) {
            Log.logErr(rt, "error:get staff err;acct=%s", staff);
            result.setInt("rt", 0);
            result.setString("msg", "该员工不存在！");
            return result.toJson();
        }
        int sid = staffInfo.getInt(StaffDef.Info.SID, 0);
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add("*");
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, staff);
        FaiList<Param> list = new FaiList<Param>();
        rt = sysPreSaleHd.getSalesList(fieldList, searchArg, list);
        if (list == null || list.size() == 0) {
            result.setString("msg", "员工不存在！");
            result.setBoolean("success", false);
            result.setInt("rt", -1);
            return result.toJson();
        } else {
            Param oldInfo = list.get(0);
            if (oldInfo.getInt(HdSaleDef.Info.SID, 0) == 0) {
                Param tempInfo = Acct.getStaffInfo(Web.getFaiCid(), staff);
                int tempSid = tempInfo.getInt(StaffDef.Info.SID, 0);
                oldInfo.setInt(HdSaleDef.Info.SID, tempSid);
            }
            oldInfo.setInt(HdSaleDef.Info.AWARD_COUNT, awardCount);
            ParamUpdater updater = new ParamUpdater(oldInfo);
            ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, staff);
            rt = sysPreSaleHd.setPreSaleInfo(matcher, updater);
        }
        if (rt != Errno.OK) {
            Log.logErr("staff=%s,rt=%s,count=%s", staff, rt, awardCount);
            result.setString("msg", "操作失败！");
            result.setBoolean("success", false);
            result.setInt("rt", -1);
            return result.toJson();
        }
        result.setInt("rt", rt);
        result.setString("msg", "操作成功！");
        result.setBoolean("success", true);
        return result.toJson();
    }

    /**
     * 设置每天最大分配量
     */
    private String setTotalLimitCount(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        int totalLimit = Parser.parseInt(request.getParameter("totalLimit"), 0);
        Dao dao = WebOss.getOssBsDao();
        try {
            ParamMatcher mat = new ParamMatcher("id", ParamMatcher.EQ, 1);
            Param info = new Param();
            info.setInt("totalLimitCount", totalLimit);
            ParamUpdater updater = new ParamUpdater(info);
            rt = dao.update("hdTotalLimitCount", updater, mat);
        } catch (Exception e) {
            Log.logErr(e);
        } finally {
            dao.close();
        }
        Param result = new Param();
        if (rt != Errno.OK) {
            Log.logErr("rt=%s,count=%s", rt, totalLimit);
            result.setString("msg", "操作失败！");
            result.setBoolean("success", false);
            result.setInt("rt", -1);
            return result.toJson();
        }
        result.setInt("rt", rt);
        result.setString("msg", "操作成功！");
        result.setBoolean("success", true);
        return result.toJson();
    }

    /**
     * 设置每天最大分配量
     */
    private String setCountLimit(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        String staff = request.getParameter("staff");
        int count = Parser.parseInt(request.getParameter("count"), 0);
        Dao bssMainDao = null;
        Param result = new Param();

        FaiList<Integer> sidList = new FaiList<Integer>();//分配员工列表
        SearchArg searchArg = new SearchArg();
        Staff staffImpl = (Staff) Core.getCorpKit(Session.getCid(), Kid.STAFF);
        Param staffInfo = new Param();
        staffInfo = staffImpl.getStaffInfo(staff);
        if (staffInfo == null || staffInfo.isEmpty()) {
            Log.logErr(rt, "error:get staff err;acct=%s", staff);
            result.setInt("rt", 0);
            result.setString("msg", "该员工不存在！");
            return result.toJson();
        }
        int sid = staffInfo.getInt(StaffDef.Info.SID, 0);
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add("*");
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, staff);
        FaiList<Param> list = new FaiList<Param>();
        rt = sysPreSaleHd.getSalesList(fieldList, searchArg, list);
        if (list == null || list.size() == 0) {
            Param data = new Param();
            data.setString(HdSaleDef.Info.ACCT, staff);
            data.setInt("limitCount", count);
            Param tempInfo = Acct.getStaffInfo(Web.getFaiCid(), staff);
            int tempSid = tempInfo.getInt(StaffDef.Info.SID, 0);
            data.setInt(HdSaleDef.Info.SID, tempSid);
            rt = sysPreSaleHd.addSale(data);
        } else {
            Param oldInfo = list.get(0);
            if (oldInfo.getInt(HdSaleDef.Info.SID, 0) == 0) {
                Param tempInfo = Acct.getStaffInfo(Web.getFaiCid(), staff);
                int tempSid = tempInfo.getInt(StaffDef.Info.SID, 0);
                oldInfo.setInt(HdSaleDef.Info.SID, tempSid);
            }
            oldInfo.setInt("limitCount", count);
            ParamUpdater updater = new ParamUpdater(oldInfo);
            ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, staff);
            rt = sysPreSaleHd.setPreSaleInfo(matcher, updater);
        }
        if (rt != Errno.OK) {
            Log.logErr("staff=%s,rt=%s,count=%s", staff, rt, count);
            result.setString("msg", "操作失败！");
            return result.toJson();
        }
        result.setInt("rt", rt);
        result.setString("msg", "操作成功！");
        result.setBoolean("success", true);
        return result.toJson();
    }

    private String recoverData(HttpServletRequest request) throws Exception {
        String sql = "select * from directSaleActionLog where type = 4 and optTime like '2018-04-03 14:3%' and attrType=5";
        Dao dao = WebOss.getOssBsDao();
        int rt = 0;
        try {
            FaiList<Param> list = dao.executeQuery(sql);
            for (Param item : list) {
                Param data = new Param();
                data.setString("salesAcct", item.getString("sacct"));
                data.setInt("aid", item.getInt("aid"));
                data.setInt("status", 1);
                data.setCalendar("receiveTime", item.getCalendar("receiveTime"));
                rt = dao.insert("acctPreSaleHd", data);
            }
        } finally {
            dao.close();
        }
        return "" + rt;
    }

    private int updateReceiveTime() throws Exception {
        String sql = "select * from acctPreSaleHd where receiveTime is null limit 1; ";
        Dao dao = WebOss.getOssBsDao();
        int rt = 0;
        try {
            FaiList<Param> list = dao.executeQuery(sql);
            ParamUpdater updater = null;
            for (Param item : list) {
                int aid = item.getInt("aid");
                String salesAcct = item.getString("salesAcct");
                sql = " select max(receiveTime) from directSaleActionLog where receiveTime is not null and type = 4 and aid =" + aid + " and sacct=" + salesAcct;
                FaiList<Param> itemList = dao.executeQuery(sql);
                Calendar receiveTime = null;
                if (itemList != null && itemList.size() > 0) {
                    receiveTime = itemList.get(0).getCalendar("receiveTime");
                } else {
                    continue;
                }
                if (receiveTime != null) {
                    Param data = new Param();
                    data.setCalendar("receiveTime", receiveTime);
                    data.setCalendar("createTime", receiveTime);
                    updater = new ParamUpdater(data);
                    ParamMatcher matcher = new ParamMatcher("aid", ParamMatcher.EQ, aid);
                    rt = dao.update("acctPreSaleHd", updater, matcher);
                }

            }
        } finally {
            dao.close();
        }
        return rt;
    }

    //用于销售要求的一些特殊的释放逻辑
    private String admOpt(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        if (Session.getSid() != 753 && !Web.getDebug()) {
            return "err";
        }
        String mainTable = request.getParameter("mainTable");//主表
        String mainSql = request.getParameter("mainSql");
        String type = request.getParameter("type");
        FaiList<String> tables = FaiList.parseStringList(request.getParameter("tables"), new FaiList<String>());
        FaiList<String> others = FaiList.parseStringList(request.getParameter("others"), new FaiList<String>());
        if (mainSql == null || mainTable == null) {
            return "arg err";
        }
        String[] arr = mainTable.split("-");
        mainTable = arr[1];
        String mainDataBase = arr[0];
        FaiList<Param> mainDataList = new FaiList<Param>();

        //先去主库拿数据
        if ("hd".equals(mainDataBase)) {
            Dao hdDao = WebOss.getSlaveHdDao();
            try {
                mainDataList = hdDao.executeQuery(mainSql);
            } catch (Exception e) {
                Log.logErr("e=%s", e);
            } finally {
                hdDao.close();
            }
        } else if ("bssMain".equals(mainDataBase)) {
            Dao bssDao = WebOss.getBssMainDaoMaster();
            try {
                mainDataList = bssDao.executeQuery(mainSql);
            } catch (Exception e) {
                Log.logErr("e=%s", e);
            } finally {
                bssDao.close();
            }
        }
        Set<Integer> aidSet = new HashSet<Integer>();
        if (mainDataList != null && mainDataList.size() > 0) {
            for (Param item : mainDataList) {
                aidSet.add(item.getInt("aid"));
            }
        } else {
            return "no data";
        }
        FaiList<Integer> aidIntList = new FaiList<Integer>(aidSet);
        //再查其他表的数据
        Param otherTableData = new Param();
        ParamMatcher matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
        for (String key : tables) {
            String[] keys = key.split("-");
            String otherTable = keys[1];
            String db = keys[0];
            String field = "*";
            if ("hdProf".equals(otherTable)) {
                field = "aid,name";
            } else if ("acctStatus".equals(otherTable)) {
                field = "aid,acct";
            }
            FaiList<Param> otherList = getDataList(field, db, otherTable, matcher);
            if (otherList != null) {
                for (Param item : otherList) {
                    String aidKey = String.valueOf(item.getInt("aid"));
                    String pKey = key + "-" + aidKey;
                    otherTableData.setParam(pKey, item);//表名加aid组成一个key
                }
            } else {
                Log.logErr("yansen otherList null err,db=%s,otherTable=%s,matcher=%s", db, otherTable, matcher);
            }
        }
        Param taParam = new Param(true);
        Param taGroupParam = new Param(true);
        Param pubGameParam = new Param(true);
        Param createGameParam = new Param(true);
        Param loginParam = new Param(true);
        for (String tempKey : others) {
            if ("ta".equals(tempKey)) {
                Dao bssDao = WebOss.getBssMainDaoMaster();
                try {
                    FaiList<Param> taList = bssDao.executeQuery("select ta,groupId,taName from ta");
                    FaiList<Param> taGroupList = bssDao.executeQuery("select * from taGroup");
                    for (Param item : taList) {
                        taParam.setParam(item.getInt("ta", 0) + "", item);
                    }
                    for (Param item : taGroupList) {
                        taGroupParam.setString(item.getInt("groupId", 0) + "", item.getString("groupName"));
                    }
                } catch (Exception e) {
                    Log.logErr("e=%s", e);
                } finally {
                    bssDao.close();
                }
            } else if ("loginTime".equals(tempKey)) {
                FaiList<Param> loginList = null;
                Dao bssDao = WebOss.getBssMainDaoMaster();
                try {
                    Dao.SelectArg sltArg = new Dao.SelectArg();
                    sltArg.table = "bssLogin";
                    sltArg.field = "max(time) as time,aid";
                    sltArg.searchArg = new SearchArg();
                    sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
                    sltArg.group = "aid";
                    loginList = bssDao.select(sltArg);
                } catch (Exception e) {
                    Log.logErr("e=%s", e);
                } finally {
                    bssDao.close();
                }
                for (Param item : loginList) {
                    int time = item.getInt("time", 0);
                    int aid = item.getInt("aid", 0);
                    loginParam.setString(aid + "", Parser.parseDateString(time, "yyyy-MM-dd HH:mm:ss"));
                }
            } else if ("publishGame".equals(tempKey)) {
                FaiList<Param> pubGameList = null;
                Dao bssDao = WebOss.getBssNativeDao();
                try {
                    Dao.SelectArg sltArg = new Dao.SelectArg();
                    sltArg.table = "hdPubGame";
                    sltArg.field = "aid";
                    sltArg.searchArg = new SearchArg();
                    sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
                    sltArg.group = "aid";
                    pubGameList = bssDao.select(sltArg);
                } catch (Exception e) {
                    Log.logErr("e=%s", e);
                } finally {
                    bssDao.close();
                }
                for (Param item : pubGameList) {
                    int aid = item.getInt("aid", 0);
                    pubGameParam.setInt("" + aid, 1);
                }
            } else if ("createGame".equals(tempKey)) {
                FaiList<Param> createGameList = null;
                Dao bssDao = WebOss.getBssMainDaoMaster();
                try {
                    Dao.SelectArg sltArg = new Dao.SelectArg();
                    sltArg.table = "hdTemplateGame";
                    sltArg.field = "aid";
                    sltArg.searchArg = new SearchArg();
                    sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
                    sltArg.group = "aid";
                    createGameList = bssDao.select(sltArg);
                } catch (Exception e) {
                    Log.logErr("e=%s", e);
                } finally {
                    bssDao.close();
                }
                for (Param item : createGameList) {
                    int aid = item.getInt("aid", 0);
                    createGameParam.setInt("" + aid, 1);
                }
            }

        }
        Param data0 = new Param();
        if (mainDataList != null && mainDataList.size() > 0) {
            data0 = mainDataList.get(0);
        }
        boolean hasRegBiz = data0.containsKey("regBiz");
        boolean hasFirstOpenBiz = data0.containsKey("firstOpenBiz");
        for (Param item : mainDataList) {
            int aid = item.getInt("aid", 0);
            if (aid == 0) {
                Log.logErr("err aid  err game=%s", item);
                continue;
            }
            for (String key : tables) {
                Param info = otherTableData.getParam(key + "-" + aid);
                if (info != null) {
                    item.assign(info);//组装每个表的数据
                }
                if (key.equals("hd-hdGame")) {
                    int flag = item.getInt("flag", 0);
                    if (!Misc.checkBit(flag, HdGameDef.Flag.PUBLISH)) {
                        item.setString("gameStatus", "未发布");
                    } else {
                        Calendar startTime = item.getCalendar("startTime");
                        Calendar endTime = item.getCalendar("endTime");
                        if (startTime == null || endTime == null) {
                            Log.logErr("err game Time err game=%s", item);
                            continue;
                        } else {
                            long endTimeLong = endTime.getTimeInMillis();
                            long startTimeLong = startTime.getTimeInMillis();
                            long nowTime = System.currentTimeMillis();
                            if (endTimeLong < nowTime) {
                                item.setString("gameStatus", "已结束");
                            } else if (startTimeLong > nowTime) {
                                item.setString("gameStatus", "进行中");
                            } else if (startTimeLong < nowTime) {
                                item.setString("gameStatus", "未开始");
                            }
                        }
                    }
                }
            }
            if (!taParam.isEmpty() || !taGroupParam.isEmpty()) {
                int ta = item.getInt("ta", 0);
                Param taInfo = taParam.getParam(ta + "", new Param());
                int groupId = taInfo.getInt("groupId", 0);
                item.setString("ta", taInfo.getString("taName", ""));
                Log.logDbg("yansen ------groupId=%s taGroupParam=%s", groupId, taGroupParam);
                item.setString("groupId", taGroupParam.getString(groupId + "", ""));
            }
            String aidKey = String.valueOf(aid);
            if (!loginParam.isEmpty()) {
                item.setString("loginTime", loginParam.getString(aidKey, ""));
            }
            if (!pubGameParam.isEmpty()) {
                item.setString("publishGame", pubGameParam.getInt(aidKey, 0) > 0 ? "有" : "无");
            }
            if (!createGameParam.isEmpty()) {
                item.setString("createGame", createGameParam.getInt(aidKey, 0) > 0 ? "有" : "无");
            }
            if (hasRegBiz) {
                int regBiz = item.getInt("regBiz", 0);
                if (regBiz == 0) {
                    item.setString("regBiz", "建站");
                } else if (regBiz == 1) {
                    item.setString("regBiz", "互动");
                } else if (regBiz == 5) {
                    item.setString("regBiz", "凡科");
                } else {
                    item.setString("regBiz", "未知");
                }
            }
            if (hasFirstOpenBiz) {
                int firstOpenBiz = item.getInt("firstOpenBiz", 0);
                if (firstOpenBiz == 0) {
                    item.setString("firstOpenBiz", "建站");
                } else if (firstOpenBiz == 1) {
                    item.setString("firstOpenBiz", "互动");
                } else if (firstOpenBiz == 2) {
                    item.setString("firstOpenBiz", "传单");
                } else if (firstOpenBiz == 3) {
                    item.setString("firstOpenBiz", "公众号");
                } else if (firstOpenBiz == 4) {
                    item.setString("firstOpenBiz", "轻站");
                } else if (firstOpenBiz == 5) {
                    item.setString("firstOpenBiz", "凡科网");
                } else if (firstOpenBiz == 6) {
                    item.setString("firstOpenBiz", "快图");
                }
            }

        }
        if ("export".equals(type)) {
            response.setContentType("application/x-excel");
            // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("游戏.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            if (mainDataList != null && mainDataList.size() > 0) {
                Set<String> keySet = mainDataList.get(0).keySet();
                Param cellKey = new Param();
                for (String key : keySet) {
                    cellKey.setString(key, key);
                }
			/*	cellKey.setString("aid","aid");
				cellKey.setString("agentAid","agentAid");
				cellKey.setString("acct","账号");
				cellKey.setString("name","游戏名称");
				cellKey.setString("gameStatus","游戏状态");
				cellKey.setString("view","浏览人数");
				cellKey.setString("player","玩家人数");
				cellKey.setString("share","分享人数");
				cellKey.setString("startTime","开始时间");*/
                OssPoi.exportExcel(cellKey, mainDataList, outputStream);
                return "{\"success\":true}";
            }

        }
        Param result = new Param();
        result.setList("dataList", mainDataList);
        result.setBoolean("success", true);
        result.setString("msg", "获取成功");
        return result.toJson();
    }

    //用于销售要求的一些特殊的释放逻辑
    private String jasenExport(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        if (Session.getSid() != 753 && !Web.getDebug()) {
            return "err";
        }

        Dao dao = WebOss.getOssBsDao();
        Dao bssDao = WebOss.getBssMainDaoMaster();
        FaiList<Param> dataList = null;
        try {
            dataList = dao.executeQuery("select aid,createTime from agentConsultingIntention where createTime>'2018-09-01 00:00:00' and createTime<'2018:12:01 00:00:00'");
            Set<Integer> aidSet = new HashSet<Integer>();
            if (dataList != null) {
                for (Param item : dataList) {
                    aidSet.add(item.getInt("aid"));
                }
            }
            FaiList<Integer> aidIntList = new FaiList<Integer>(aidSet);
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            sltArg.field = "max(receiveTime) as receiveTime,aid,sacct";
            sltArg.group = "aid";
            FaiList<Param> receiveList = dao.select(sltArg);

            sltArg = new Dao.SelectArg();
            sltArg.table = "acctOrderItem";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            FaiList<Integer> hdProductList = new FaiList<Integer>();
            hdProductList.add(115);//铂金
            hdProductList.add(123);//白银
            hdProductList.add(124);//铂金
            hdProductList.add(164);//门店
            hdProductList.add(228);//钻石
            sltArg.searchArg.matcher.and("productId", ParamMatcher.IN, hdProductList);
            sltArg.searchArg.matcher.and("status", ParamMatcher.EQ, 10);
            sltArg.field = "max(payTime) as payTime,aid,productId";
            sltArg.group = "aid";
            FaiList<Param> payHdList = bssDao.select(sltArg);
            Log.logDbg("yansen jese pay =%s", payHdList.size());
            if (payHdList == null) {
                Log.logErr("yansen payHdList=null err");
            }
            sltArg = new Dao.SelectArg();
            sltArg.table = "acctOrderItem";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            FaiList<Integer> cdProductList = new FaiList<Integer>();
            cdProductList.add(125);//微传单专享版
            cdProductList.add(154);//微传单付费模板
            sltArg.searchArg.matcher.and("productId", ParamMatcher.IN, cdProductList);
            sltArg.searchArg.matcher.and("status", ParamMatcher.EQ, 10);
            sltArg.field = "max(payTime) as payTime,aid,productId";
            sltArg.group = "aid";
            FaiList<Param> payCdList = bssDao.select(sltArg);

            Param hdParam = new Param(true);
            Param cdParam = new Param(true);
            Param receiveParam = new Param(true);
            for (Param item : payHdList) {
                hdParam.setParam(item.getInt("aid") + "", item);
            }
            for (Param item : payCdList) {
                cdParam.setParam(item.getInt("aid") + "", item);
            }
            for (Param item : receiveList) {
                receiveParam.setParam(item.getInt("aid") + "", item);
            }
            if (dataList != null) {
                for (Param item : dataList) {
                    String key = String.valueOf(item.getInt("aid"));
                    Param cdPay = cdParam.getParam(key);
                    if (cdPay != null) {
                        int productId = cdPay.getInt("productId", 0);
                        int payTime = cdPay.getInt("payTime", 0);
                        String name = productId == 125 ? "微传单专享版" : "微传单付费模板";
                        item.setString("付费微传单产品", name);
                        item.setString("微传单最近成交时间", Parser.parseDateString(payTime, "yyyy-MM-dd HH:mm:ss"));
                        item.setInt("payTime", payTime);
                    }

                    Param receiveP = receiveParam.getParam(key);
                    if (receiveP != null) {
                        Calendar receiveTime = receiveP.getCalendar("receiveTime");
                        if (receiveTime != null) {
                            item.setString("最近领取时间", Parser.parseDateString(receiveTime, "yyyy-MM-dd HH:mm:ss"));
                        }
                        item.setString("互动销售", receiveP.getString("sacct", ""));
                    }
                    Calendar createTime = item.getCalendar("createTime");
                    item.setString("createTime", Parser.parseDateString(createTime, "yyyy-MM-dd HH:mm:ss"));

                    Param hdPay = hdParam.getParam(key);
                    if (hdPay != null) {
                        int productId = hdPay.getInt("productId", 0);
                        int payTime = hdPay.getInt("payTime", 0);
                        String name = "";
                        if (productId == 115 || productId == 124) {
                            name = "铂金版";
                        } else if (productId == 123) {
                            name = "白银版";
                        } else if (productId == 164) {
                            name = "门店版";
                        } else if (productId == 228) {
                            name = "钻石版";
                        } else {
                            name = hdPay.toJson();
                        }
                        item.setString("互动付费产品", name);
                        item.setInt("payTime", payTime);
                        item.setString("互动游戏最近成交时间", Parser.parseDateString(payTime, "yyyy-MM-dd HH:mm:ss"));
                    }
                }
            }

            Log.logDbg("yansen aidList=%s", dataList);
        } finally {
            dao.close();
            bssDao.close();
        }

    /*
		Dao bssMainDao = WebOss.getBssMainDaoMaster();
		FaiList<Param> dataList = new FaiList<Param>();
		try{
			dataList = bssMainDao.executeQuery("select * from hdData where type = 10");
		}finally{
			bssMainDao.close();
		}
		for(Param item:dataList){
			Param other = Param.parseParam(item.getString("other",""),new Param());
			item.assign(other);
		}*/

        response.setContentType("application/x-excel");
        // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
        // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
        out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("游戏.xls".getBytes("GBK"), "ISO-8859-1"));
        ServletOutputStream outputStream = response.getOutputStream();
        // 创建导出的表头跟需要导出的数据KEY值
        Param cellKey = new Param();
        cellKey.setString("aid", "aid");
        cellKey.setString("互动游戏最近成交时间", "互动游戏最近成交时间");
        cellKey.setString("互动付费产品", "互动付费产品");
        cellKey.setString("微传单最近成交时间", "微传单最近成交时间");
        cellKey.setString("付费微传单产品", "付费微传单产品");
        cellKey.setString("最近领取时间", "最近领取时间");
        cellKey.setString("互动销售", "互动销售");
        cellKey.setString("createTime", "createTime");
        OssPoi.exportExcel(cellKey, dataList, outputStream);
        return "{\"success\":true}";
    }

    //用于销售要求的一些特殊的释放逻辑
    private String admOptExportByOneSql(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        if (Session.getSid() != 753 && !Web.getDebug()) {
            return "err";
        }
        String mainTable = request.getParameter("oneTable");//主表
        String mainSql = request.getParameter("oneSql");
        mainSql = " select areaId,count(1) as gameCount,sum(player) as playerCount from hdGame where aid = 12530474 group by areaId; ";
        String otherSql = "select name,id,aid from hdStoreGroup where aid = 12530474";
        String type = request.getParameter("type");
        String cellKeyStr = request.getParameter("cellKey");
        FaiList<String> tables = FaiList.parseStringList(request.getParameter("tables"), new FaiList<String>());
        if (mainSql == null || mainTable == null) {
            return "arg err";
        }
        String[] arr = mainTable.split("-");
        mainTable = arr[1];
        String mainDataBase = arr[0];
        FaiList<Param> mainDataList = new FaiList<Param>();
        FaiList<Param> otherList = new FaiList<Param>();

        //先去主库拿数据
        if ("hd".equals(mainDataBase)) {
            Dao hdDao = WebOss.getSlaveHdDao();
            try {
                mainDataList = hdDao.executeQuery(mainSql);
                if (!otherSql.isEmpty()) {
                    otherList = hdDao.executeQuery(otherSql);
                }
            } catch (Exception e) {
                Log.logErr("e=%s", e);
            } finally {
                hdDao.close();
            }
        }
        Param other = new Param(true);
        for (Param item : otherList) {
            other.setString(item.getInt("id") + "", item.getString("name"));
        }
        for (Param item : mainDataList) {
            //	int storeId = item.getInt("storeId",0);
            int areaId = item.getInt("areaId", 0);
            //	item.setString("name",other.getString(storeId+"",""));
            item.setString("name", other.getString(areaId + "", ""));
        }
        if ("export".equals(type) && cellKeyStr != null) {
            response.setContentType("application/x-excel");
            // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("游戏.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();
            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            String[] keys = cellKeyStr.split(",");
            for (int i = 0; i < keys.length; i++) {
                String key = keys[i];
                cellKey.setString(key, key);
            }
            OssPoi.exportExcel(cellKey, mainDataList, outputStream);
            return "{\"success\":true}";
        }
        Param result = new Param();
        result.setList("dataList", mainDataList);
        result.setBoolean("success", true);
        result.setString("msg", "获取成功");
        return result.toJson();
    }

    private FaiList<Param> getDataList(String field, String db, String table, ParamMatcher matcher) {
        FaiList<Param> dataList = null;
        if (db.equals("hd")) {
            Dao hdDao = WebOss.getSlaveHdDao();
            try {
                Dao.SelectArg sltArg = new Dao.SelectArg();
                sltArg.table = table;
                sltArg.field = field;
                sltArg.searchArg = new SearchArg();
                sltArg.searchArg.matcher = matcher;
                dataList = hdDao.select(sltArg);
                return dataList;
            } catch (Exception e) {
                Log.logErr("yansen e=%s", e);
            } finally {
                hdDao.close();
            }
        } else if (db.equals("bssMain")) {
            Dao bssMainDao = WebOss.getBssMainDaoMaster();
            try {
                Dao.SelectArg sltArg = new Dao.SelectArg();
                sltArg.table = table;
                sltArg.field = field;
                sltArg.searchArg = new SearchArg();
                sltArg.searchArg.matcher = matcher;
                dataList = bssMainDao.select(sltArg);
                return dataList;
            } catch (Exception e) {
                Log.logErr("yansen e=%s", e);
            } finally {
                bssMainDao.close();
            }
        }
        return dataList;
    }

    private String rollDel(HttpServletRequest request) throws Exception {
        String sql = request.getParameter("rollRecord");
        int rt = 0;
        Dao dao = WebOss.getOssBsDao();
        try {
            dao.setAutoCommit(false);
            FaiList<Param> dataList = dao.executeQuery(sql);
            FaiList<Integer> aidList = new FaiList<Integer>();
            for (Param item : dataList) {
                aidList.add(item.getInt("aid"));
            }
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
            sltArg.searchArg.matcher.and("createTime", ParamMatcher.LE, "2018-09-01 00:00:00");
            sltArg.group = "aid";
            sltArg.searchArg.cmpor = new ParamComparator("createTime", true);
            FaiList<Param> receiveList = dao.select(sltArg);
            FaiList<Param> finalList = new FaiList<Param>();
            for (Param item : receiveList) {
                Param info = new Param();
                info.setString(PreSaleHdDef.Info.SALES_ACCT, item.getString("sacct"));
                info.setInt(PreSaleHdDef.Info.AID, item.getInt("aid"));
                info.setCalendar("receiveTime", item.getCalendar("receiveTime"));
                info.setCalendar("createTime", item.getCalendar("createTime"));
                info.setInt(PreSaleHdDef.Info.TYPE, 2);
                info.setInt("status", 1);
                info.setInt("tag", item.getInt("tag"));
                info.setInt("flag", item.getInt("flag"));
                info.setString("taGroupName", item.getString("taGroupName", ""));
                finalList.add(info);
            }
            rt = dao.batchInsert("acctPreSaleHd", finalList);
            if (rt != Errno.OK) {
                Log.logErr("yansen insert err");
                return "err";
            }
            for (Param item : receiveList) {
                PreSaleHdDef.BSS.afterReceive(Core.getFlow(), item.getInt("aid"), item.getString("sacct"));//销售覆盖
            }
        } catch (Exception e) {
            rt = -1;
            PreSaleHdDef.printErr(e);
        } finally {
            if (rt == 0) {
                dao.commit();
            } else {
                dao.rollback();
            }
            dao.close();
        }
        return "success rt=" + rt;
    }

    // 获取注册时间内最大最小aid
    private Param getRegTimeAid(int intBegTime, int intEndTime) throws Exception {
        Param param = new Param();
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);

        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);

        // 注册时间内最大aid
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.REG_TIME, ParamMatcher.GE, intBegTime);
        searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.LE, intEndTime);

        searchArg.limit = 1;
        searchArg.cmpor = new ParamComparator(BssStatDef.Info.REG_TIME, true);

        //FaiList<Param> list = sysBssStat.getAllAcctInfoList(fieldList, searchArg);


        FdpDataParamMatcher matcher1 = new FdpDataParamMatcher();
        matcher1.and("reg_time", ParamMatcher.GE, intBegTime);
        matcher1.and("reg_time", ParamMatcher.LE, intEndTime);

        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher1)
                .orderBy(OrderBy.DESC, "reg_time")
                .execute(Core.getFlow());
        FaiList<Param> list = execute.getData();




        if (list.size() > 0) {
            int maxAid = list.get(0).getInt(BssStatDef.Info.AID, 0);
            param.setInt("maxAid", maxAid);
        }

        // 注册时间内最小aid
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.REG_TIME, ParamMatcher.GE, intBegTime);
        searchArg.matcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.LE, intEndTime);

        searchArg.limit = 1;
        searchArg.cmpor = new ParamComparator(BssStatDef.Info.REG_TIME, false);

        //list = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

        FdpDataParamMatcher matcher2 = new FdpDataParamMatcher();
        matcher2.and("reg_time", ParamMatcher.GE, intBegTime);
        matcher2.and("reg_time", ParamMatcher.LE, intEndTime);

        ResultSet execute1 = FdpDataSDK.newOLTPQuery()
                .select("aid")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher2)
                .orderBy(OrderBy.DESC, "reg_time")
                .execute(Core.getFlow());
        list = execute1.getData();


        if (list.size() > 0) {
            int minAid = list.get(0).getInt(BssStatDef.Info.AID, 0);
            param.setInt("minAid", minAid);
        }

        return param;
    }


    //TODO
    private String getYkSaleList2(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
    	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        if(!Auth.checkFaiscoAuth("authYKSale1|authYkSaleLeader", false)){
            return "没有权限";
        }

        boolean authAdm = Auth.checkFaiscoAuth("authAdm", false);
        //销售组长
        boolean groupLearder = Auth.checkFaiscoAuth("authYkSaleLeader", false);
        boolean excel = Parser.parseBoolean(request.getParameter("excel"),false);

        Param data = new Param();
        //返回前端的acctList
		FaiList<Param> showAllSale = new FaiList<Param>();
      	Param info = new Param();
		info.setString("acct", "all");
        info.setString("staffName", "全部");
        info.setString("nickName", "全部");
        showAllSale.add(info);
        
      	//悦客所有销售
        FaiList<Param> allSaleList = new FaiList<Param>();
        SearchArg saleSearchArg = new SearchArg();
        saleSearchArg.matcher = new ParamMatcher(YkSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleYk.getYkSaleList(saleSearchArg, allSaleList);
		if(rt != Errno.OK){
			Log.logStd("getYkSaleList err");
			return "";
		}
		showAllSale.addAll(allSaleList);
		
		Param saleParam = new Param(true);
        for (Param item : allSaleList) {
            saleParam.setString(item.getString(YkSaleDef.Info.ACCT), item.getString(YkSaleDef.Info.NICK_NAME));
        }
		
        //aid
        int aid = Parser.parseInt(request.getParameter("aid"), 0);        
        //所属库
        int labelIndex = Parser.parseInt(request.getParameter("labelIndex"), 1);
     	          
        //最终显示的客户信息
		FaiList<Param> finalAidInfoList = new FaiList<Param>();  
		FaiList<Param> excelData = new FaiList<Param>();
        //以aid为第一筛选优先，有aid条件其他条件忽略
   		if(aid != 0){
   			// aid查询需要判断是在哪个库
	        int status = 1;
	        if (labelIndex == 1) { //个人库
	        	status = PreSaleHdDef.Status.PERSON;
	        } else if(labelIndex == 2){
	        	status = PreSaleHdDef.Status.DEAL;//成交库
	        }
	        
	        SearchArg searchArg = new SearchArg();
   			searchArg.matcher = new ParamMatcher(PreSaleYkDef.Info.AID, ParamMatcher.EQ, aid);
   			searchArg.matcher.and(PreSaleYkDef.Info.STATUS, ParamMatcher.EQ, status);
   			
   			FaiList<Param> infoList = sysPreSaleYk.getAllInfoList(searchArg);
   			
   			if(infoList != null && infoList.size() > 0){
   				finalAidInfoList.addAll(infoList);
   			}
   			
   			data.setInt("total", finalAidInfoList.size());
   		}else{
   			//查acctPreSaleYk表
   			FaiList<Param> firstAidInfoList = selectFromAcctPreSaleYk(request, allSaleList);
   			if(firstAidInfoList == null){
   				Log.logStd("selectFromAcctPreSaleYk err: firstAidInfoList = null");
   				return "";
   			}
   			Log.logStd("after selectFromAcctPreSaleYk firstAidInfoList size = %s", firstAidInfoList.size());
   			
   			FaiList<Integer> aidList = new FaiList<Integer>();
   			for(Param p : firstAidInfoList){
   				aidList.add(p.getInt(PreSaleYkDef.Info.AID, 0));
   			}
   			//查acctStatus表
   			if(aidList.size() > 0){
   				aidList = selectFromAcctStatus(request, aidList); 
   				if(aidList == null){
   					Log.logStd("selectFromAcctStatus err： aidList = null");
   					return "";
   				}
   				Log.logStd("after selectFromAcctStatus aidList size = %s", aidList.size());
   			}
   			//查ossSms表
   			if(aidList.size() > 0){
   				aidList = selectFromOssSms(request, aidList);
   				if(aidList == null){
   					Log.logStd("selectFromOssSms err： aidList = null");
   					return "";
   				}
   				Log.logStd("after selectFromOssSms aidList size = %s", aidList.size());
   			}
   			//查corpMark表
   			if(aidList.size() > 0){
   				aidList = selectFromCorpMark(request, aidList);
   				if(aidList == null){
   					Log.logStd("selectFromCorpMark err： aidList = null");
   					return "";
   				}
   				Log.logStd("after selectFromCorpMark aidList size = %s", aidList.size());
   			}
   			//查vip表
   			if(aidList.size() > 0){
   				aidList = selectFromVip(request, aidList);
   				if(aidList == null){
   					Log.logStd("selectFromVip err： aidList = null");
   					return "";
   				}
   				Log.logStd("after selectFromVip aidList size = %s", aidList.size());
   			}
   			
   			//查yk_prof表
   			Param openYkMap = new Param(true);
   			if(aidList.size() > 0){
   				aidList = selectFromYkProf(request, aidList, openYkMap);
   				if(aidList == null){
   					Log.logStd("selectFromYkProf err： aidList = null");
   					return "";
   				}
   				Log.logStd("after selectFromYkProf aidList size = %s", aidList.size());
   			}
   			
   			FaiList<Param> ykStatusList = new FaiList<Param>();
   			Param ykStatusMap = new Param(true);
   			//查ykStatus表
   			if(aidList.size() > 0){
   				ykStatusList = selectFromYkStatus(request, aidList);
   				if(ykStatusList == null){
   					Log.logStd("selectFromYkStatus err： ykStatusList = null");
   					return "";
   				}
   				Log.logStd("after selectFromYkStatus ykStatusList size = %s", ykStatusList.size());
   			}

   			//因为ykStatus有些数据还没有，不完整，所以不做排除
   			//aidList.clear();
   			for(Param p : ykStatusList){
   				int aidTemp = p.getInt("aid", 0);
   				//aidList.add(aidTemp);
   				ykStatusMap.setParam(aidTemp+"", p);
   			}
   			//筛选出剩下的aid后，还需要看是否排序，以及分页数据
   			FaiList<Param> lastList = Misc.getList(firstAidInfoList, new ParamMatcher("aid", ParamMatcher.IN, aidList));
   			if(lastList == null){
   				Log.logStd("lastList is null");
   				return "";
   			}
   			
   			//将所有排序字段补充，需要补充的是openTime, memberCount
   			for(Param p : lastList){
   				int aidTemp = p.getInt("aid", 0);
   				Param ykStatusParam = ykStatusMap.getParam(aidTemp+"", new Param());
   				
   				p.setLong("openYkTime", openYkMap.getLong(aidTemp+""));
   				
   				p.setInt("memberCount", ykStatusParam.getInt("memberCount", 0));
   				p.setInt("loginOneWeekCount", ykStatusParam.getInt("loginOneWeekCount", 0));
   				p.setInt("optOneWeekCount", ykStatusParam.getInt("optOneWeekCount", 0));
   			}
   			data.setInt("total", lastList.size());
   			excelData = bindCustomerData(lastList, saleParam);
   			finalAidInfoList = finalList2order(request, lastList);
   		}

        //绑定数据
        finalAidInfoList = bindCustomerData(finalAidInfoList, saleParam);

        data.setString("msg", "success");
    	data.setList("dataList", finalAidInfoList);
    	data.setList("saleList", showAllSale);
    	if(excel){
	    	if(authAdm || groupLearder){
                response.setContentType("application/x-excel");
                // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
                // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
                out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
                ServletOutputStream outputStream = response.getOutputStream();
                Param cellKey = new Param(true);
	    	    if(labelIndex==1){
                    response.setHeader("Content-Disposition", "attachment;filename=" + new String("个人库数据.xls".getBytes("GBK"), "ISO-8859-1"));
                    cellKey.setString("aid","aid");
                    cellKey.setString("mark","备注");
                    cellKey.setString("name","企业名");
                    cellKey.setString("mobile","注册手机");
                    cellKey.setString("regTime","注册时间");
                    cellKey.setString("ta","注册来源");
                    cellKey.setString("tp","关键字");
                    cellKey.setString("receiveTime","领取时间");
                    cellKey.setString("salesAcct","销售");
                    cellKey.setString("loginTime","最后登入时间");
                    cellKey.setString("sendTime","上次发送短信时间");
                    cellKey.setString("loginOneWeekCount","一周登入次数");
                    cellKey.setString("optOneWeekCount","一周操作次数");
                    cellKey.setString("tag","标签");
                    cellKey.setString("lastUpdateTime","最后更新时间");
	    	    }else if(labelIndex==2){
                    response.setHeader("Content-Disposition", "attachment;filename=" + new String("成交库数据.xls".getBytes("GBK"), "ISO-8859-1"));
                    cellKey.setString("aid","aid");
                    cellKey.setString("name","企业名");
                    cellKey.setString("mobile","注册手机");
                    cellKey.setString("regTime","注册时间");
                    cellKey.setString("ta","注册来源");
                    cellKey.setString("tp","关键字");
                    cellKey.setString("receiveTime","领取时间");
                    cellKey.setString("salesAcct","销售");
                    cellKey.setString("loginTime","最后登入时间");
                    cellKey.setString("loginOneWeekCount","一周登入次数");
                    cellKey.setString("optOneWeekCount","一周操作次数");
                    cellKey.setString("tag","标签");
                    cellKey.setString("mark","备注");
	    	    }
	    	    OssPoi.exportExcel(cellKey,excelData, outputStream);
	    	    return "{\"success\":true}";
	    	}else{
	    	    return "{\"success\":false, \"msg\":" +"没有权限"+ "}";
	    	}
	    	
        } 
    	return data.toJson();
    }
    
    private FaiList<Param> selectFromAcctPreSaleYk(HttpServletRequest request, FaiList<Param> allSaleList) throws Exception{
    	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
    	
    	//查找先查有索引的
    	boolean receiveTimeCheck = "true".equals(request.getParameter("receiveTimeCheck"));     
    	boolean lastUpdateTimeCheck = "true".equals(request.getParameter("lastUpdateTimeCheck"));    
    	boolean loginTimeCheck = "true".equals(request.getParameter("loginTimeCheck"));
    	
    	boolean nextTalkTimeCheck = "true".equals(request.getParameter("nextTalkTimeCheck"));
    	
    	//注册来源、标记、标签、意向度
        int ta = Parser.parseInt(request.getParameter("ta"), -1);                        
        int intent = Parser.parseInt(request.getParameter("intent"), -1);                
        int tag = Parser.parseInt(request.getParameter("tag"), -1);                    
        int intentLevel = Parser.parseInt(request.getParameter("intentLevel"), -1);
      	
      	//组长权限：查询领取人、 查询销售分组
        String receiveSale = Parser.parseString(request.getParameter("receiveSale"), "all");        
        String saleGroup = Parser.parseString(request.getParameter("saleGroup"), "all");
		
        //库
        int labelIndex = Parser.parseInt(request.getParameter("labelIndex"), 1);
        int status = 1;
        if (labelIndex == 1) { //个人库
        	status = PreSaleHdDef.Status.PERSON;
        } else if(labelIndex == 2){
        	status = PreSaleHdDef.Status.DEAL;//成交库
        }
        
        
      	//参数，表示重点关注，10天未联系，20天未联系，28天未联系，禁止发送短信列表
        String args = request.getParameter("args"); 
        
       	SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleYkDef.Info.STATUS, ParamMatcher.EQ, status);
        
      	//重点关注列表
        if ("follow".equals(args)) {
            searchArg.matcher.and(PreSaleYkDef.Info.FLAG, ParamMatcher.LAND, PreSaleYkDef.Flag.SPECIAL_FOCUS, PreSaleYkDef.Flag.SPECIAL_FOCUS);
        }

        //10天未联系
        if ("noContact10Day".equals(args)) {
            searchArg.matcher.and(PreSaleYkDef.Info.RECEIVE_TIME, ParamMatcher.LE, getCalendar(-10, 0, 0, 0));
            searchArg.matcher.and(PreSaleYkDef.Info.CONTACT_TIME, ParamMatcher.LE, getCalendar(-10, 0, 0, 0));
        }
        //20天未联系
        if ("noContact20Day".equals(args)) {
            searchArg.matcher.and(PreSaleYkDef.Info.RECEIVE_TIME, ParamMatcher.LE, getCalendar(-20, 0, 0, 0));
            searchArg.matcher.and(PreSaleYkDef.Info.CONTACT_TIME, ParamMatcher.LE, getCalendar(-20, 0, 0, 0));
        }
        //28天未联系
        if ("noContact28Day".equals(args)) {
            searchArg.matcher.and(PreSaleYkDef.Info.RECEIVE_TIME, ParamMatcher.LE, getCalendar(-28, 0, 0, 0));
            searchArg.matcher.and(PreSaleYkDef.Info.CONTACT_TIME, ParamMatcher.LE, getCalendar(-28, 0, 0, 0));
        }
        //禁止发送短信列表
        if ("banSendMsgAid".equals(args)) {
            searchArg.matcher.and(PreSaleYkDef.Info.FLAG, ParamMatcher.LAND, PreSaleYkDef.Flag.BAN_SEND_MSG, PreSaleYkDef.Flag.BAN_SEND_MSG);
        }
        
        //领取时间
       	if(receiveTimeCheck){
        	String receiveTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("receiveTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String receiveTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("receiveTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            searchArg.matcher.and(PreSaleYkDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveTimeStart);
            searchArg.matcher.and(PreSaleYkDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveTimeEnd);
        }
        //最新更新时间
        if (lastUpdateTimeCheck) {
            String lastUpdateTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("lastUpdateTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String lastUpdateTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("lastUpdateTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            searchArg.matcher.and(PreSaleYkDef.Info.UPDATE_TIME, ParamMatcher.GE, lastUpdateTimeStart);
            searchArg.matcher.and(PreSaleYkDef.Info.UPDATE_TIME, ParamMatcher.LE, lastUpdateTimeEnd);
        }
        //登录时间
        if (loginTimeCheck) {
            String loginTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("loginTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String loginTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("loginTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            searchArg.matcher.and(PreSaleYkDef.Info.LOGIN_TIME, ParamMatcher.GE, loginTimeStart);
            searchArg.matcher.and(PreSaleYkDef.Info.LOGIN_TIME, ParamMatcher.LE, loginTimeEnd);
        }
        //下次联系时间
        if(nextTalkTimeCheck){
        	String nextTalkTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("nextTalkTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String nextTalkTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("nextTalkTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            searchArg.matcher.and(PreSaleYkDef.Info.TALK_NEXT_TIME, ParamMatcher.GE, nextTalkTimeStart);
            searchArg.matcher.and(PreSaleYkDef.Info.TALK_NEXT_TIME, ParamMatcher.LE, nextTalkTimeEnd);
        }
        
     	//来源ta
        if (ta != -1) {
        	FaiList<Param> taList = getTaList();
        	
            FaiList<Integer> trackList = PreSaleYkDef.getTaList(taList,ta);
            if (ta != PreSaleYkDef.PreSaleTa.NOT_IN) {
            	searchArg.matcher.and(PreSaleYkDef.Info.TA, ParamMatcher.IN, trackList);
            } else {
                //未知查询，剔除掉移动放量的ta。
                trackList.add(491);
                trackList.add(493);
                trackList.add(2344);
                searchArg.matcher.and(PreSaleYkDef.Info.TA, ParamMatcher.NOT_IN, trackList);
            }
        }
     	//标记
        if (intent != -1) {
            searchArg.matcher.and(PreSaleYkDef.Info.INTENT, ParamMatcher.EQ, intent);
        }
     	//标签
        if (tag > -1) {
        	searchArg.matcher.and(PreSaleYkDef.Info.TAG, ParamMatcher.EQ, tag);
        }	
     	//意向度等级
     	if (intentLevel > 0) {
            searchArg.matcher.and(PreSaleYkDef.Info.INTENT_LEVEL, ParamMatcher.EQ, intentLevel);
        }
     	
     	FaiList<String> saleAcct = new FaiList<String>(); 
     	saleAcct = getSaleListByCondition(allSaleList, receiveSale, saleGroup);
     	if(saleAcct == null){
     		Log.logStd("getSaleListByCondition err : saleAcct is null");
     		return null;
     	}
	    
     	//Log.logStd("saleAcct  = %s", saleAcct);
     	searchArg.matcher.and(PreSaleYkDef.Info.SALES_ACCT, ParamMatcher.IN, saleAcct);

     	//Log.logStd("matcher sql = %s", searchArg.matcher.toString());
     	
	    FaiList<Param> aidInfoList = sysPreSaleYk.getAllInfoList(searchArg);
     	return aidInfoList;
    }
    
    
    private FaiList<Integer> selectFromAcctStatus(HttpServletRequest request,FaiList<Integer> aidList) throws Exception{
    	//注册时间有索引
    	boolean regTimeCheck = "true".equals(request.getParameter("regTimeCheck"));
    	
    	//省份城市
        String province = Parser.parseString(request.getParameter("province"), "");    
        String city = Parser.parseString(request.getParameter("city"), "");  
    	
        //是否开通悦客
        int hasOpenYk = Parser.parseInt(request.getParameter("hasOpenYk"), -1);
        
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        if(regTimeCheck || !province.equals("") || !city.equals("") || hasOpenYk != -1){
	    	//注册时间查询，acctStatus里面的regTime有索引，通过acctStatus查最大最小aid会比直接查acctPreSale的regTime快。
	    	if (regTimeCheck) {
	    		int regTimeStart = Parser.parseInt(request.getParameter("regTimeStart"), 0);
	            int regTimeEnd = Parser.parseInt(request.getParameter("regTimeEnd"), 0);
	            Param regAidParam = getRegTimeAid(regTimeStart, regTimeEnd);
	            int maxAid = regAidParam.getInt("maxAid", 0);
	            int minAid = regAidParam.getInt("minAid", 0);
	            if (maxAid > 0) {
	                searchArg.matcher.and(BssStatDef.Info.AID, ParamMatcher.LE, maxAid);
	            }
	            if (minAid > 0) {
	                searchArg.matcher.and(BssStatDef.Info.AID, ParamMatcher.GE, minAid);
	            }
	        }
	    	
	    	// 注册省份
	        if (!province.equals("")) {
	        	searchArg.matcher.and(BssStatDef.Info.REG_PROVINCE, ParamMatcher.EQ, province);
	        }
	        // 注册城市
	        if (!city.equals("")) {
	        	searchArg.matcher.and(BssStatDef.Info.REG_CITY, ParamMatcher.EQ, city);
	        }
	        //是否开通悦客
	        if (hasOpenYk != -1) {
	        	if (hasOpenYk == 1) {
	        		searchArg.matcher.and(BssStatDef.Info.OPEN_YK_TIME, ParamMatcher.GT, 0);
	        	}else{
	        		searchArg.matcher.and(BssStatDef.Info.OPEN_YK_TIME, ParamMatcher.LE, 0);
	        	}
	        }
	        
	        FaiList<String> fieldList = new FaiList<String>();
	        
	        //查询字段
	        fieldList.add(BssStatDef.Info.AID);
	        
	        //Log.logStd("matcher sql = %s", searchArg.matcher.toString());
	        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);
	        //FaiList<Param> infoList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

            FaiList<Object> nestExpr = new FaiList<Object>();
            nestExpr.add(IN.contains("aid", aidList));
            FdpDataParamMatcher matcher = AND.expr(nestExpr);
            if(regTimeCheck || !province.equals("") || !city.equals("") || hasOpenYk != -1) {
                //注册时间查询，acctStatus里面的regTime有索引，通过acctStatus查最大最小aid会比直接查acctPreSale的regTime快。
                if (regTimeCheck) {
                    int regTimeStart = Parser.parseInt(request.getParameter("regTimeStart"), 0);
                    int regTimeEnd = Parser.parseInt(request.getParameter("regTimeEnd"), 0);
                    Param regAidParam = getRegTimeAid(regTimeStart, regTimeEnd);
                    int maxAid = regAidParam.getInt("maxAid", 0);
                    int minAid = regAidParam.getInt("minAid", 0);
                    if (maxAid > 0) {
                        matcher.and(BssStatDef.Info.AID, ParamMatcher.LE, maxAid);
                    }
                    if (minAid > 0) {
                        matcher.and(BssStatDef.Info.AID, ParamMatcher.GE, minAid);
                    }
                }

                // 注册省份
                if (!province.equals("")) {
                    matcher.and("reg_province", ParamMatcher.EQ, province);
                }
                // 注册城市
                if (!city.equals("")) {
                    matcher.and("reg_city", ParamMatcher.EQ, city);
                }
                //是否开通悦客
                if (hasOpenYk != -1) {
                    if (hasOpenYk == 1) {
                        matcher.and("open_yk_time", ParamMatcher.GT, 0);
                    } else {
                        matcher.and("open_yk_time", ParamMatcher.LE, 0);
                    }
                }
            }
            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("aid")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(matcher)
                    .execute(Core.getFlow());
            FaiList<Param> infoList = execute.getData();

	     	if(infoList == null){
	     		Log.logStd("selectFromAcctStatus err : infoList is null");
	     		return null;
	     	}
	     	
	     	aidList.clear();
	     	for(Param p : infoList){
	     		int aid = p.getInt("aid", 0);
	     		aidList.add(aid);
	     	}
	     	
	    	return aidList;
        }
        return aidList;
    }
    
    private FaiList<Integer> selectFromOssSms(HttpServletRequest request,FaiList<Integer> aidList)throws Exception{
    	//发送短信时间、（未）发送短信时间
        boolean sendMessageTimeCheck = "true".equals(request.getParameter("sendMessageTimeCheck"));  
        boolean noSendMessageCheck = "true".equals(request.getParameter("noSendMessageCheck"));
    
      	//如果需要筛选发送短信时间,先找有发送短信的aid
        if (sendMessageTimeCheck) {
            String sendMessageTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("sendMessageTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String sendMessageTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("sendMessageTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            Dao smsDao = WebOss.getDaoPoolSlave("sungoin").getDao();
            try {
            	//查询在此时间段发送过短信的aid
            	String smsSql = "select distinct aid from ossSms where sendTime is not null and sendStatus = 1 and sendTime>='" + sendMessageTimeStart + " 00:00:00' and sendTime<='" + sendMessageTimeEnd + " 23:59:59 ' ";
            	FaiList<Param> smsInfoList = smsDao.executeQuery(smsSql);
            	if(smsInfoList == null){
            		Log.logStd("selectFromOssSms err: smsInfoList is null");
            		return null;
            	}
            	
            	aidList.clear();
            	for(Param p : smsInfoList){
            		aidList.add(p.getInt("aid", 0));
            	}
            	return aidList;
            } catch (Exception e) {
                Log.logStd("selectFromOssSms err: %s", e);
                return null;
            } finally {
                smsDao.close();
            }
        }
      	
      	//未发送短信筛选
        if (noSendMessageCheck) {
            String sendMessageTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("sendMessageTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
            String sendMessageTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("sendMessageTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
            
            Dao smsDao = WebOss.getDaoPoolSlave("sungoin").getDao();
            try {
            	//查询在此时间段发送过短信的aid
            	String smsSql = "select distinct aid from ossSms where sendTime is not null and sendStatus = 1 and sendTime>='" + sendMessageTimeStart + " 00:00:00' and sendTime<='" + sendMessageTimeEnd + " 23:59:59 ' ";
            	FaiList<Param> smsInfoList = smsDao.executeQuery(smsSql);
            	if(smsInfoList == null){
            		Log.logStd("selectFromOssSms err: smsInfoList is null");
            		return null;
            	}
            	
            	HashSet<Integer> aidSet = new HashSet<Integer>();
            	for(Param p : smsInfoList){
            		aidSet.add(p.getInt("aid", 0));
            	}
            	
            	FaiList<Integer> newAidList = new FaiList<Integer>(); 
            	//反选
            	for(Integer aid : aidList){
            		if(aidSet.add(aid)){
            			newAidList.add(aid);
            		}
            	}
            	
            	return newAidList;
            } catch (Exception e) {
                Log.logStd("selectFromOssSms err: %s", e);
                return null;
            } finally {
                smsDao.close();
            }
        }
      	
        return aidList;
    
    }
    
    private FaiList<Integer> selectFromCorpMark(HttpServletRequest request,FaiList<Integer> aidList)throws Exception{
    	String mark = Parser.parseString(request.getParameter("mark"), ""); 
    	String receiveSale = Parser.parseString(request.getParameter("receiveSale"), "all");
    	
    	//需要有指定领取人才能查到备注
    	if(receiveSale.equals("all")){
    		return aidList;
    	}else{
    		if(!mark.equals("")){
	    		Dao dao = WebOss.getOssBsDao();
	            try {
	                Dao.SelectArg sltArg = new Dao.SelectArg();
	                sltArg.table = "corpMark";
	                sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
	                sltArg.searchArg.matcher.and("mark", ParamMatcher.LK, mark);
	                
	                FaiList<Param> corpMarkList = dao.select(sltArg);
	                if(corpMarkList == null){
	                	Log.logStd("selectFromCorpMark err: corpMarkList is null");
	                	return null;
	                }
	                aidList.clear();
	                for (Param p : corpMarkList) {
	                    aidList.add(p.getInt("aid", 0));
	                }
	                
	                return aidList;
	            }catch(Exception e){
	            	Log.logStd("selectFromCorpMark err: %s", e);
	            	return null;	
	            }finally {
	                dao.close();
	            }
    		}else{
    			return aidList;
    		}
    	}
    }
    
    //从Vip表中查询，跟企业查询同步
    private FaiList<Integer> selectFromVip(HttpServletRequest request,FaiList<Integer> aidList)throws Exception{
    	//产品版本（建站、互动、轻站、悦客）
        int siteVersion = Parser.parseInt(request.getParameter("siteVersion"), -1);                    
        int hdVersion = Parser.parseInt(request.getParameter("hdVersion"), -1);
        int spgVersion = Parser.parseInt(request.getParameter("spgVersion"), -1); 
        int ykVersion = Parser.parseInt(request.getParameter("ykVersion"), -1);
        
        if(siteVersion != -1 || hdVersion != -1 || spgVersion != -1 || ykVersion != -1){
	        SearchArg searchArg = new SearchArg();
	        searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
	        
	        //网站版本
	        if (siteVersion != -1) {
	        	searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.EQ, siteVersion);
	        }
	        //互动版本
	        if (hdVersion != -1) {
	        	searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.EQ, hdVersion);
	        }
	        //轻站版本
	        if (spgVersion != -1) {
	        	searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.EQ, spgVersion);
	        }
	        //悦客版本
	        if (ykVersion != -1) {
	        	searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.EQ, ykVersion);
	        }
	        
	        Log.logStd("matcher sql : %s", searchArg.matcher.toString());
	        OssVip oSysVip = new OssVip();
	        FaiList<Param> vipList = oSysVip.searchVip(searchArg);
			if(vipList == null){
				Log.logStd("selectFromVip err : vipList is null");
				return null;
			}
			
			FaiList<Integer> newAidList = new FaiList<Integer>();
			for(Param p : vipList){
				newAidList.add(p.getInt("aid", 0));
			}
			return newAidList;
        }
        return aidList;
    }
    
    private FaiList<Integer> selectFromYkProf(HttpServletRequest request, FaiList<Integer> aidList, Param openYkMap)throws Exception{
    	//创建店铺时间
    	boolean createStoreTimeCheck = "true".equals(request.getParameter("firstCreateStoreTimeCheck"));
    	
    	//创建店铺时间（现在只有开通悦客的时间）
       	if(createStoreTimeCheck){
       		long firstCreateStoreTimeStart = Parser.parseLong(request.getParameter("firstCreateStoreTimeStart"), 0) * 1000;
            long firstCreateStoreTimeEnd = Parser.parseLong(request.getParameter("firstCreateStoreTimeEnd"), 0) * 1000;
            
          	//查开通悦客时间
   			Dao.SelectArg ykSelect = new Dao.SelectArg();
			ykSelect.table = "yk_prof";
			ykSelect.field = "aid, createTime";
			ykSelect.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
			ykSelect.searchArg.matcher.and("createTime", ParamMatcher.GE, firstCreateStoreTimeStart);
			ykSelect.searchArg.matcher.and("createTime", ParamMatcher.LE, firstCreateStoreTimeEnd);
			
			Log.logStd("matcher sql : %s", ykSelect.searchArg.matcher.toString());
			
			Dao ykProfDao = WebHdOss.getYkProfDao();
			try{
				FaiList<Param> openYkList = ykProfDao.select(ykSelect);
				if(openYkList == null){
					Log.logStd("ykProfDao.select fail");
					return null;
				}
				
				aidList.clear();
				for(Param p : openYkList){
					int aid = p.getInt("aid", 0);
					
					aidList.add(aid);
					openYkMap.setLong(aid+"", p.getLong("createTime", 0L));
				}
			}catch(Exception e){
	        	Log.logStd("selectFromYkProf catch err: %s", e);
	        	return null;
	        }finally{
				ykProfDao.close();
			}
       	}
    	
    	
    	return aidList;
    }
    
    private FaiList<Param> selectFromYkStatus(HttpServletRequest request, FaiList<Integer> aidList)throws Exception{
    	
    	//会员人数区间
        int memberNumStart = Parser.parseInt(request.getParameter("memberNumStart"), 0);    
        int memberNumEnd = Parser.parseInt(request.getParameter("memberNumEnd"), 0);    
        

       	Dao.SelectArg sltArg = new Dao.SelectArg(); 
        sltArg.table = "ykStatus";
        sltArg.field = "aid, memberCount, loginOneWeekCount, optOneWeekCount";
        sltArg.searchArg = new SearchArg();
        sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
           
        if (memberNumStart > 0 || memberNumEnd > 0){
        	sltArg.searchArg.matcher.and("memberCount", ParamMatcher.GE, memberNumStart);
        	sltArg.searchArg.matcher.and("memberCount", ParamMatcher.LE, memberNumEnd);
        }
        
        Log.logStd("sltArg.searchArg.matcher = %s", sltArg.searchArg.matcher.toString());
        Dao bssDao = WebOss.getBssMainDaoMaster();
        try{
        	FaiList<Param> infoList = bssDao.select(sltArg);
        	if(infoList == null){
        		Log.logStd("selectFromYkStatus err: infoList is null");
        		return null;
        	}
        	
        	return infoList;
        }catch(Exception e){
        	Log.logStd("selectFromYkStatus catch err: %s", e);
        	return null;
        }finally{
        	bssDao.close();
        }
        
    }
    
    
    private FaiList<Param> finalList2order(HttpServletRequest request, FaiList<Param> finalAidInfoList)throws Exception{
    	//排列顺序、分页
        int sortBy = Parser.parseInt(request.getParameter("sortBy"), -1);                  
        int currentPage = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = Parser.parseInt(request.getParameter("size"), 10);
        int start = limit * (currentPage - 1);
        int total = 0;
        
        String sortField = "optTime";
        boolean desc = sortBy%2 == 0? false : true;
        if(sortBy == -1 || sortBy == 0){
        	sortField = "optTime";
        }else if(sortBy == 1 || sortBy == 2){
        	sortField = "updateTime";
        }else if(sortBy == 3 || sortBy == 4){
        	sortField = "openYkTime";
        }else if(sortBy == 5 || sortBy == 6){
        	sortField = "loginTime";
        }else if(sortBy == 7 || sortBy == 8){
        	sortField = "memberCount";
        }
        
        SearchArg searchArg = new SearchArg();
        searchArg.limit = limit;
        searchArg.start = start;
        searchArg.cmpor = new ParamComparator(sortField, desc); 
        Searcher searcher = new Searcher(searchArg);
        
        //Log.logStd("finalAidInfoList before %s sort size=%s", sortField, finalAidInfoList.size());
        FaiList<Param> finalList = searcher.getParamList(finalAidInfoList);
        //Log.logStd("finalAidInfoList after %s sort size=%s", sortField, finalAidInfoList.size());
        
        return finalList;
    }
    
    //封装客户列表显示所需的数据
    private FaiList<Param> bindCustomerData(FaiList<Param> finalInfoList, Param saleParam)throws Exception{
    	if(finalInfoList.size() == 0){
    		return finalInfoList;
    	}
    	
    	FaiList<Integer> aidList = new FaiList<Integer>();
    	for(Param p : finalInfoList){
    		aidList.add(p.getInt("aid", 0));
    	}
    	
    	Param markParam = new Param(true);
    	Param acctParam = new Param(true);
    	
    	FaiList<Param> corpMarkList = new FaiList<Param>();
    	//拿备注
        Dao dao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "corpMark";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
            corpMarkList = dao.select(sltArg);
        } finally {
            dao.close();
        }
        for (Param item : corpMarkList) {
            markParam.setString(String.valueOf(item.getInt("aid")), item.getHtmlString("mark"));
        }
        //查询acctStatus表
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);
        fieldList.add(BssStatDef.Info.ACCT);
        fieldList.add(BssStatDef.Info.NAME);
        fieldList.add(BssStatDef.Info.REG_PROVINCE);
        fieldList.add(BssStatDef.Info.REG_CITY);
        fieldList.add(BssStatDef.Info.REG_TIME);
        fieldList.add(BssStatDef.Info.COMPANYGOAL);
        fieldList.add(BssStatDef.Info.JZ_VERSION);
        fieldList.add(BssStatDef.Info.HD_VERSION);
        fieldList.add(BssStatDef.Info.FLYER_VERSION);
        fieldList.add(BssStatDef.Info.PERSON);
        fieldList.add(BssStatDef.Info.REG_EMAIL);
        fieldList.add(BssStatDef.Info.REG_MOBILE);
        fieldList.add(BssStatDef.Info.TA);
        fieldList.add(BssStatDef.Info.TP);
        fieldList.add(BssStatDef.Info.LOGIN_TIME);
        fieldList.add(BssStatDef.Info.OPEN_YK_TIME);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);
        //FaiList<Param> infoList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);


        FaiList<Object> nestExpr = new FaiList<Object>();
        nestExpr.add(IN.contains("aid", aidList));
        FdpDataParamMatcher matcher = AND.expr(nestExpr);

        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid", "reg_aacct", "reg_name", "reg_province", "reg_city", "reg_time", "reg_company_goal", "version_jz"
                ,"version_hd", "version_wcd", "reg_person", "reg_email", "reg_mobile", "reg_ta", "reg_from_spider_keyword", "last_login_time", "open_yk_time")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> infoList = execute.getData();


        for (Param item : infoList) {
            acctParam.setParam("" + item.getInt("aid"), item);
        }
        
      	//根据aid列表获取对应最近上次发送短信时间
        FaiList<Param> smsInfoList = new FaiList<Param>();                            
        HashMap<Integer, String> sendTimeMap = new HashMap<Integer, String>();       

        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(OssSmsDef.Info.AID, ParamMatcher.IN, aidList);
        searchArg.matcher.and(OssSmsDef.Info.SENDSTATUS, ParamMatcher.EQ, 1);                    
        WebChineseSmsCli smsCli = new WebChineseSmsCli(Core.getFlow());                            
        if (!smsCli.init()) {                                                                        
            throw new WebException("WebChineseSmsCli init error");
        }
        smsCli.searchSms(searchArg, smsInfoList);

        if (smsInfoList.size() > 0) {                                                               
            for (Param p : smsInfoList) {
                int aidTemp0 = p.getInt(OssSmsDef.Info.AID);
                String sendTime = Parser.parseDateString(p.getCalendar(OssSmsDef.Info.SENDTIME), "yyyy-MM-dd HH:mm:ss");
                String existSendTime = sendTimeMap.get(aidTemp0);
                if (existSendTime == null) {
                    sendTimeMap.put(aidTemp0, sendTime);
                } else if (sendTime.compareTo(existSendTime) > 0) {                            
                    sendTimeMap.put(aidTemp0, sendTime);
                }
            }
        }
        
        FaiList<Param> taList = getTaList();
        Map<Integer, String> tagMap = getTagMap();
        
        FaiList<Param> returnData = new FaiList<Param>();
        String domain = WebHdOss.getOssDomainUrl();
    	for(Param p : finalInfoList){
    		Param data = new Param();
    		
    		String aid = p.getInt("aid",0)+"";
    		Param acctInfo = acctParam.getParam(aid, new Param());
    		data.setString("aid", aid);
    		//备注
            data.setString("mark", markParam.getString(aid, ""));
    		
    		//企业名称、注册手机、注册时间
    		data.setString("name", acctInfo.getString(BssStatDef.Info.NAME, ""));
    		data.setString("mobile", acctInfo.getString(BssStatDef.Info.REG_MOBILE, ""));
    		data.setString("regTime", Parser.parseDateString(acctInfo.getInt(BssStatDef.Info.REG_TIME, 0), "yyyy-MM-dd HH:mm:ss"));
            
    		//登陆时间、更新时间、领取时间
    		data.setString("loginTime", Parser.parseDateString(p.getCalendar("loginTime"), "yyyy-MM-dd HH:mm:ss"));
    		data.setString("lastUpdateTime", Parser.parseDateString(p.getCalendar("updateTime"), "yyyy-MM-dd HH:mm:ss"));
    		data.setString("receiveTime", Parser.parseDateString(p.getCalendar("receiveTime"), "yyyy-MM-dd HH:mm:ss"));
    		//注册来源
    		int taItem = p.getInt("ta", 0);
            data.setString("ta", PreSaleYkDef.getTaName(taList, taItem));
            //创建店铺时间
            long firstCreateStoreTime = p.getLong("openYkTime", 0L);
            if (firstCreateStoreTime <= 0L) {
                data.setString("firstCreateStoreTime", "暂无数据");
            } else {
                data.setString("firstCreateStoreTime", Parser.parseDateString(firstCreateStoreTime, "yyyy-MM-dd HH:mm:ss"));
            }
           
            int intentTemp = p.getInt("intent", 0);
            data.setString("intentName", PreSaleYkDef.getIntentName(intentTemp));
            data.setString("intent", PreSaleYkDef.getIntentName(intentTemp));
          	
            //标签
            int tagTemp = p.getInt("tag", 0);
            String tagName = tagMap.get(tagTemp);
            if (tagName == null) {
                tagName = "";
            }
            data.setString("tag", tagName);
            
            //关键词
            data.setString(BssStatDef.Info.TP, PreSaleYkDef.getSearchKeyword(acctInfo.getHtmlString(BssStatDef.Info.TP)));
            
            //发送短信时间
            data.setString("sendTime", sendTimeMap.get(p.getInt("aid", 0)));
            
            //销售
            String salesAcctTemp = p.getString(PreSaleHdDef.Info.SALES_ACCT, "");
            data.setString(PreSaleYkDef.Info.SALES_ACCT, saleParam.getString(salesAcctTemp,""));
            returnData.add(data);
            //一周**次数 
            data.setInt("loginOneWeekCount", p.getInt("loginOneWeekCount", 0));
            data.setInt("optOneWeekCount", p.getInt("optOneWeekCount", 0));
            data.setString("aidUrl", domain + "/index.jsp?t=ykSale&u=/cs/corp.jsp?aid=" + aid);
    	}
    	return returnData;
    			
    }

    private static Map getTagMap() throws Exception {
        Param constant = getPageDefInfo("comm", "tag");
        Map<Integer, String> tagMap = new HashMap<Integer, String>();
        if (constant != null) {
            FaiList<Param> tagList = constant.getList("labelList", new FaiList<Param>());
            for (Param item : tagList) {
                int tagItem = item.getInt("label", 0);
                String name = item.getString("name", "");
                tagMap.put(tagItem, name);
            }
        }
        return tagMap;
    }

    private String exportList(HttpServletRequest request, HttpServletResponse response, JspWriter out, FaiList<Param> preSaleList) throws Exception {
        FaiList<String> colKeyList = new FaiList<String>();
        colKeyList.add("aid");
        colKeyList.add("acct");
        colKeyList.add("mobile");
        colKeyList.add("regTime");
        colKeyList.add("ta");
        colKeyList.add("tp");
        colKeyList.add("receiveTime");
        colKeyList.add("lastUpdateTime");
//        colKeyList.add("firstCreateGameTime");
        colKeyList.add("salesAcct");
        colKeyList.add("loginTime");
        colKeyList.add("sendTime");
        colKeyList.add("loginOneWeekCount");
        colKeyList.add("optOneWeekCount");
        colKeyList.add("mark");
        colKeyList.add("tag");
        FaiList<Param> colList = getColList(colKeyList);
        FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
        Param title = new Param();
        title.setString("aid", "aid");
        title.setString("acct", "企业名称");
        title.setString("mobile", "注册手机");
        title.setString("regTime", "注册时间");
        title.setString("ta", "注册来源");
        title.setString("tp", "关键字");
        title.setString("receiveTime", "领取时间");
        title.setString("lastUpdateTime", "最后更新时间");
//        title.setString("firstCreateGameTime", "首次创建游戏时间");
        title.setString("salesAcct", "销售");
        title.setString("loginTime", "最后登陆时间");
        title.setString("sendTime", "上次发送短信时间");
        title.setString("loginOneWeekCount", "一周登陆次数");
        title.setString("optOneWeekCount", "一周操作次数");
        title.setString("mark", "备注");
        title.setString("tag", "标签");
        rowList.add(title);
        for (Param item : preSaleList) {
            rowList.add(item);
        }
        FaiList<Param> optionList = new FaiList<Param>();
        Param option = null;
        int outputCount = rowList.size();
        option = new Param();
        option.setString(MSOfficeConverter.Option.SHEET_NAME, "12份付费统计");
        option.setList(MSOfficeConverter.Option.COL_LIST, colList);
        option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
        optionList.add(option);
        String fileName = "门店销售数据.xls";
        dealBeforeConvert(request, response, out, "客户列表.xls", optionList);
        return "success";
    }

    /*公共方法*/
    private static String getPreSale() throws Exception {
        FaiList<Param> dataList = new FaiList<Param>();
        Param param = null;
        
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(YkSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleYk.getYkSaleList(saleSearchArg, infoList);
        
        FaiList<Integer> sidList = new FaiList<Integer>();                       
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        for (Integer tmpSid : sidList) {
            Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), tmpSid);
            param = new Param();
            param.setInt("sid", tmpSid);
            param.setString("sacct", staffInfo.getString(StaffDef.Info.SACCT, ""));
            param.setString("name", staffInfo.getHtmlString(StaffDef.Info.NAME));
            dataList.add(param);
        }
        return "{\"success\":true, \"dataList\":" + dataList + "}";
    }

    private static FaiList<Param> getTaList() throws Exception {
        FaiList<Param> taList = (FaiList<Param>) Core.getTmpData("_taList");
        if (taList == null) {
            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口
            SearchArg searchArg = new SearchArg();
            taList = sysBssStat.getTaList(searchArg);
            Core.setTmpData("_taList", taList);
        }
        return taList;
    }

    private static void dealBeforeConvert(HttpServletRequest request, HttpServletResponse response, JspWriter out, String fileName, FaiList<Param> optionList) throws Exception {
        out.clear();
        String agent = request.getHeader("User-Agent");
        agent = agent == null ? "" : agent.toUpperCase();
        response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent, fileName) + "\"");
        response.setContentType("application/vnd.ms-excel");
        MSOfficeConverter.excelConverter(response.getOutputStream(), optionList);
        //out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
    }

    private FaiList<Param> getColList(FaiList<String> colKeyList) throws Exception {
        FaiList<Param> colList = new FaiList<Param>();
        for (String key : colKeyList) {
            Param param = new Param();
            param.setString(MSOfficeConverter.Col.KEY, key);
            param.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(param);
        }
        return colList;
    }

    /**退出系统*/
    private String logout(HttpServletResponse response) throws Exception {
        Auth.loginOutFaiCorp(response);
        return "success";
    }

    /*公共方法*/
    private Param checkSendMessage(int sid) throws Exception {
        int rt = Errno.OK;
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        
     	//检验权限
        boolean hasAuth = Auth.checkFaiscoAuth("authYKSale1", false);
        Param result = new Param();
        result.setInt("rt", Errno.ERROR);
        result.setBoolean("success", true);
        if (!hasAuth && sid != 753) {
            result.setString("msg", "没有操作权限，添加失败");
            return result;
        }

        if (!isTimeToSms()) {
            result.setString("msg", "现在已经下班了，系统正在处理发送短信。明天再添加吧~");
            return result;
        }
        
        /******************** 判断操作人员的联系信息是否完善 **********************/
        FaiList<Param> ykSaleList = new FaiList<Param>();
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("sid", ParamMatcher.EQ, sid);
       
        rt = sysPreSaleYk.getYkSaleList(searchArg, ykSaleList);
        if (rt != Errno.OK) {
            Log.logErr(rt, "get ykSale err");
        }
        
        if (ykSaleList == null || ykSaleList.size() == 0) {
            result.setString("msg", "请完善个人联系信息之后，再操作发送短信。");
            return result;
        }
        
        Param saleInfo = ykSaleList.get(0);
        String name = saleInfo.getString(YkSaleDef.Info.NICK_NAME, "");
        String salePhone = saleInfo.getString(YkSaleDef.Info.PHONE, "");
        String acct = saleInfo.getString(YkSaleDef.Info.ACCT, "");
        
        if (salePhone.isEmpty() || name.isEmpty() || acct.isEmpty()) {
            result.setString("msg", "请完善个人联系信息之后，再操作发送短信。");
            return result;
        }
        if (getSaleSid(acct) != sid) {
            result.setString("msg", "没有权限，请领取资源。");
            return result;
        }
        /******************** 判断操作人员的联系信息是否完善 **********************/
        result.setInt("rt", Errno.OK);
        result.setString("acct", acct);
        return result;
    }

    /**批量发送短信*/
    private String batchSendMessage(HttpServletRequest request) throws Exception {
    	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        int sid = Session.getSid();
        
      	//校验权限
        Param checkResult = checkSendMessage(sid);
        if (checkResult.getInt("rt") != Errno.OK) {
            return checkResult.toJson();
        }
        
        String acct = checkResult.getString("acct");

        String aidListStr = request.getParameter("aidList");
        FaiList<Param> aidList = FaiList.parseParamList(aidListStr, new FaiList<Param>());   //bug   原因：其未准确判断前端传来的参数类型。
        Log.logStd("aidList = %s",aidList);
        FaiList<Integer> aidListTmp = new FaiList<Integer>();
        FaiList<Param> banMsgAidList = new FaiList<Param>();
        
      	//选择的短信模板
        int smsId = Parser.parseInt(request.getParameter("smsId"), 0);            

        int nowTime = (int) (Calendar.getInstance().getTimeInMillis() / 1000);
        BssStatCli bssCli = new BssStatCli(Core.getFlow());
        if (!bssCli.init()) {
            Log.logErr("init err");
            checkResult.setString("msg", "系统错误！002");
            return checkResult.toJson();
        }
        for (Param p : aidList) {
            aidListTmp.add(p.getInt("aid", 0));
        }

        //查询禁止发送短信的aid
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleYkDef.Info.AID, ParamMatcher.IN, aidListTmp);
        searchArg.matcher.and(PreSaleYkDef.Info.SALES_ACCT, ParamMatcher.EQ, acct);
        searchArg.matcher.and(PreSaleYkDef.Info.FLAG, ParamMatcher.LAND, PreSaleYkDef.Flag.BAN_SEND_MSG, PreSaleYkDef.Flag.BAN_SEND_MSG);
        
        banMsgAidList = sysPreSaleYk.getAllInfoList(searchArg);
        aidListTmp.clear();
        if (banMsgAidList != null && banMsgAidList.size() > 0) {
            for (Param p : banMsgAidList) {
                aidListTmp.add(p.getInt("aid", 0));
            }
            
            //排除aidList中禁止发送短信的aid
            aidList = Misc.getList(aidList, new ParamMatcher(PreSaleYkDef.Info.AID, ParamMatcher.NOT_IN, aidListTmp));
        }
        FaiList<Param> sendList = new FaiList<Param>();
        for (Param item : aidList) {
            int aid = item.getInt("aid", 0);
            String phone = item.getString("mobile", "");
            //Param userInfo = new Param();
            //int rt = bssCli.getAcctStatusInfo(aid, userInfo);

            //迁移到新的查询方式
            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("*")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(AND.expr(EQ.of("aid", aid)))
                    .execute(Core.getFlow());
            Log.logDbg("execute:"+execute);
            Param userInfo = execute.getData().get(0);
            int rt = execute.getCode().value;

            if (rt == Errno.ERROR) {
                Log.logErr("userInfo is null err aid = %d", aid);
                checkResult.setString("msg", "系统错误！003");
                return checkResult.toJson();
            }
            if (rt == Errno.NOT_FOUND) {
                Log.logErr("userInfo is no found err aid = %d", aid);
                checkResult.setString("msg", "未找到该aid:" + aid + "的信息,请联系管理员");
                return checkResult.toJson();
            }
            int regTime = userInfo.getInt("reg_time", 0);
            if (nowTime - regTime < 3600 * 4) {
                continue;//该客户刚注册4小时内，请稍后再添加短信。
            }
            if (phone.isEmpty()) {
                phone = userInfo.getString("reg_mobile", "");
                if (phone.isEmpty() || phone.length() != 11) {
                    phone = userInfo.getString("reg_verify_mobile", "");
                    if (phone.isEmpty()) {
                        Log.logErr("phone is not found err aid = %d", aid);
                        checkResult.setString("msg", "未找到该aid:" + aid + "的有效电话,请联系管理员");
                        return checkResult.toJson();
                    }
                }
            }
            Param smsInfo = new Param();
            //TODO
            smsInfo.setInt("busType", OssSmsDef.BusType.HD);    
            smsInfo.setInt("aid", aid);
            smsInfo.setInt("sid", sid);
            smsInfo.setString("sales", acct);
            smsInfo.setInt("modelTypeId", smsId);
            smsInfo.setString("phone", phone);
            sendList.add(smsInfo);
        }
        if (sendList.isEmpty()) {
            checkResult.setBoolean("success", false);
            checkResult.setString("msg", "发送失败！发送列表为空！请检查是否存在注册4小时内的aid");
            return checkResult.toJson();
        }
        return realSend(sendList, aidListTmp).toJson();
    }

    /**批量发送短信*/
    private boolean isTimeToSms() throws Exception {
        // 禁止短信，开始时间
        Calendar startClr = Calendar.getInstance();
        startClr.set(Calendar.HOUR_OF_DAY, 19);
        startClr.set(Calendar.MINUTE, 30);
        startClr.set(Calendar.SECOND, 0);
        startClr.set(Calendar.MILLISECOND, 0);

        // 禁止短信，结束时间
        Calendar endClr = Calendar.getInstance();
        endClr.set(Calendar.HOUR_OF_DAY, 7);
        endClr.set(Calendar.MINUTE, 30);
        endClr.set(Calendar.SECOND, 0);
        endClr.set(Calendar.MILLISECOND, 0);


        Calendar now = Calendar.getInstance();

        if (now.after(startClr)) {
            return false;
        }

        return true;
    }

    /**
     * 获取销售名称
     */
    private String getSaleName(String salesAcct) throws Exception {
        int sid = getSaleSid(salesAcct);
        return WebOss.getStaffName(sid);
    }

    /**
     * 获取销售的id
     */
    private int getSaleSid(String salesAcct) throws Exception {
        if (salesAcct == null || salesAcct.isEmpty()) {
            return 0;
        }

        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), salesAcct);
        int sid = staffInfo.getInt(StaffDef.Info.SID, 0);

        return sid;
    }

    /**调用接口发送短信**/
    private Param realSend(FaiList<Param> smsList, FaiList<Integer> banAidList) throws Exception {

    	Param result = new Param();
        result.setBoolean("success", true);
        if (!isTimeToSms()) {
            result.setString("msg", "现在已经下班了，系统正在处理发送短信。明天再添加吧~");
            return result;
        }
        
        Ref<String> statusListRef = new Ref<String>();
        int rt = WebOss.addSaleSms(smsList, statusListRef);
        if (rt != Errno.OK) {
            result.setString("msg", "添加失败! 001");
            Log.logErr("add sms err sid = %d, rt = %d", Session.getSid(), rt);
            return result;
        }
        
     	//出错List
        FaiList<Param> statusList = FaiList.parseParamList(statusListRef.value);    
        if (statusList != null) {
            StringBuilder hasSendAid = new StringBuilder();
            for (Param p : statusList) {
                rt = p.getInt("rt", 0);
                if (rt == Errno.ALREADY_EXISTED) {
                    hasSendAid.append("aid:" + p.getInt("aid") + " ");
                }
            }
            if (rt == Errno.NOT_FOUND) {
                result.setString("msg", "添加失败，短信模板已经删除，请重新选择。");
                return result;
            }
            if (rt == Errno.ALREADY_EXISTED) {
                result.setString("msg", "添加失败，该手机号码今天已经添加发送短信 --" + hasSendAid.toString());
                return result;
            }
            if (rt > 100) {
                result.setString("msg", "添加失败，网络问题请联系管理员。");
                return result;
            }
        }
        //由于销售禁止发送短信客户而没有发送到的aidList
        if (banAidList != null && banAidList.size() > 0) {
            result.setString("msg", "发送成功，禁止发送短信的aid：" + banAidList.toJson());
        } else {
            result.setString("msg", "发送成功");
        }
        result.setInt("rt", 0);
        return result;
    }

    /**
     * 取aid交集
     */
    private FaiList<Integer> getAidComm(FaiList<Integer> list1, FaiList<Integer> list2) throws Exception {
        FaiList<Integer> result = new FaiList<Integer>();
        Set<Integer> set = new HashSet<Integer>();
        for (int aid : list1) {
            set.add(aid);
        }
        for (int aid : list2) {
            if (!set.add(aid)) {
                result.add(aid);
            }
        }
        return result;
    }

    /**
     * 
     * 根据领取人/销售分组条件返回销售列表
     */
    public static FaiList<String> getSaleListByCondition(FaiList<Param> allSaleList, String acct, String groupName) throws Exception {
    	if(allSaleList == null || allSaleList.isEmpty()){
    		Log.logStd("allSaleList is null");
    		return null;
    	}
    	
    	//当前销售
    	String currentAcct = WebOss.getSacct();
    	int sid =  Session.getSid();
    	
		boolean authAdm = Auth.checkFaiscoAuth("authAdm", false);
        boolean saleManage = Auth.checkFaiscoAuth("authYkSaleManage", false);
        //销售组长
        boolean groupLearder = Auth.checkFaiscoAuth("authYkSaleLeader", false);
        boolean authYkSaleLeader1 = groupLearder;// && Auth.checkFaiscoAuth("authYKSale1", false);
    	
      	//选了领取人就忽略销售分组
		FaiList<String> ownYkSaleList = new FaiList<String>();
        
		//普通销售（普通销售只能看自己）
        if (!saleManage && !groupLearder && !authAdm && sid != 1535 && sid != 1355) {
        	ownYkSaleList.clear();
        	String salesAcct = WebOss.getSacct(); 
        	ownYkSaleList.add(salesAcct);
            return ownYkSaleList;
        }
      	
      	//销售组长
        if (groupLearder) {
        	FaiList<Integer> authSidList = new FaiList<Integer>();
            if (authYkSaleLeader1) {
                authSidList = WebOss.getAuthSidList("authYKSale1");
            }
            for (int tmpSid : authSidList) {
                String t_sacct = Acct.getSacct(Web.getFaiCid(), tmpSid);
                ownYkSaleList.add(t_sacct);
            }
        }
        
        //销售经理|管理员
        if(saleManage || authAdm  || sid == 1535 || sid == 1355){
        	ownYkSaleList.clear();
        	for(Param p : allSaleList){
        		ownYkSaleList.add(p.getString(YkSaleDef.Info.ACCT, ""));
        	}
        }
        
    	if(acct.equals("all")){
   			if(groupName.equals("all") || authAdm || saleManage){
   				return ownYkSaleList;
   			}else{
   				ownYkSaleList.clear();
				if(Auth.checkFaiscoAuth(groupName, false)){
					FaiList<Integer> authSidList = WebOss.getAuthSidList(groupName);
					for (int tmpSid : authSidList) {
		                String t_sacct = Acct.getSacct(Web.getFaiCid(), tmpSid);
		                ownYkSaleList.add(t_sacct);
		            }
				}
				return ownYkSaleList;
   			}
    		
    	}else{
        	if(ownYkSaleList.contains(acct)){
        		ownYkSaleList.clear();
        		ownYkSaleList.add(acct);
        	}	        		
        	return ownYkSaleList;
    	}
    }

    /**
     *  获取N天未联系的aid
     *  @param n 未联系天数
     *  @param acct 销售
     */
/*     private FaiList<Integer> getNoContact(int n, String acct) throws Exception {

        //逻辑：
        //  1.拿销售个个人库的n天前领取的aid
        //  2.查未备注的aid
        //  3.返回aid

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        Calendar nDayAgo = Calendar.getInstance();
        nDayAgo = Misc.addDay(nDayAgo, -n);

        FaiList<String> saleList = getGroupSaleList();
        Log.logStd("saleList = %s", saleList);

        SearchArg searchArg = new SearchArg();
        ParamMatcher matcher = new ParamMatcher();
        if (!saleList.isEmpty()) {
            matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, saleList);
        }
        matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LT, nDayAgo);
        searchArg.matcher = matcher;
        searchArg.limit = 50000;
        FaiList<Param> acctList = sysPreSaleHd.getList(searchArg);

        FaiList<Integer> aidList = new FaiList<Integer>();
        if (acctList.isEmpty()) {
            return aidList;
        }
        for (Param p : acctList) {
            aidList.add(p.getInt("aid", 0));
        }

        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.field = "aid";
        selectArg.table = "corpMark";
        matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
        selectArg.searchArg = new SearchArg();
        selectArg.searchArg.matcher = matcher;
        FaiList<Param> markList = new FaiList<Param>();
        Dao dao = WebOss.getOssBsDao();
        try {
            markList = dao.select(selectArg);
        } finally {
            dao.close();
        }
        for (Param p : markList) {
            aidList.remove(p.getInt("aid", 0));
        }
        return aidList;
    } */

    /**
     * @param hour   时
     * @param minute 分
     * @param second 秒
     * @return Calendar类型时间
     * <AUTHOR>
     */
    public static Calendar getCalendar(int hour, int minute, int second) {
        return getCalendar(0, hour, minute, second);
    }

    public static Calendar getCalendar(int floatDay, int hour, int minute, int second) {
        Calendar clr = Calendar.getInstance();
        clr.add(Calendar.DATE, floatDay);
        clr.set(Calendar.HOUR_OF_DAY, hour);
        clr.set(Calendar.MINUTE, minute);
        clr.set(Calendar.SECOND, second);
        return clr;
    }

    /**
     * @param hour   时
     * @param minute 分
     * @param second 秒
     * @return int类型时间
     * <AUTHOR>
     */
    public static int getIntDate(int hour, int minute, int second) {
        return getIntDate(0, hour, minute, second);
    }

    public static int getIntDate(int floatDay, int hour, int minute, int second) {
        return (int) (getCalendar(floatDay, hour, minute, second).getTimeInMillis() / 1000);
    }

    public static int getIntDate() {
        return (int) (Calendar.getInstance().getTimeInMillis() / 1000);
    }


    private String getSendMessageList(HttpServletRequest request) throws Exception {
        Param result = new Param();

        int sidAuth = Session.getSid();
        if(sidAuth != 1355 && sidAuth != 1535 && sidAuth != 987 && sidAuth != 112 && sidAuth != 1736){
        	return "";
        }
        
        result.setBoolean("success", false);
        if (!Auth.checkFaiscoAuth("authYkSaleLeader|authYKSale1", false)) {
            result.setString("msg", "没有权限");
        }
        
//        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
//    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        int pageNo = Parser.parseInt(request.getParameter("pageNo"), 1);
        int limit = 10;
        int total = 0;
        int start = limit * (pageNo - 1);

        // 设置时间
        String begDate = Parser.parseDateString(Parser.parseInt(request.getParameter("begDate"), 0), "yyyy-MM-dd HH:mm:ss");
        String endDate = Parser.parseDateString(Parser.parseInt(request.getParameter("endDate"), 0), "yyyy-MM-dd HH:mm:ss");

        String sendBegDate = Parser.parseDateString(Parser.parseInt(request.getParameter("sendTimeStart"), 0), "yyyy-MM-dd HH:mm:ss");
        String sendEndDate = Parser.parseDateString(Parser.parseInt(request.getParameter("sendTimeEnd"), 0), "yyyy-MM-dd HH:mm:ss");
        String se_sacct = Parser.parseString(request.getParameter("sale"), "all");
        boolean isSendTime = Parser.parseBoolean(request.getParameter("sendTime"), false);

        // 查询条件
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("busType", ParamMatcher.EQ, OssSmsDef.BusType.HD);
        searchArg.matcher.and("createTime", ParamMatcher.GE, begDate);
        searchArg.matcher.and("createTime", ParamMatcher.LE, endDate);
               
        int saleId = 0;
        if (!se_sacct.equals("all")) {
            saleId = getSaleSid(se_sacct);
            searchArg.matcher.and("sid", ParamMatcher.EQ, saleId);
        }
        
        //TODO
        //暂时只有陈广秀
        searchArg.matcher.and("sid", ParamMatcher.EQ, 987);
        
        //OssSmsDef
        if (isSendTime) {
            searchArg.matcher.and("sendTime", ParamMatcher.GE, sendBegDate);
            searchArg.matcher.and("sendTime", ParamMatcher.LE, sendEndDate);
        }

        searchArg.limit = limit;
        searchArg.start = start;
        searchArg.totalSize = new fai.comm.util.Ref<Integer>();

        //获取销售短信列表
        FaiList<Param> smsList = WebOss.getSaleSmsList(searchArg);                    
        total = searchArg.totalSize.value;

        for (Param item : smsList) {//组装数据
            int sid = item.getInt("sid", 0);
            String saleName = WebOss.getStaffName(sid);
            item.setString("saleName", saleName);
            int busType = item.getInt("busType");
            if (busType == OssSmsDef.BusType.HD) {
                item.setString("busType", "互动");
            }
            int sendStatus = item.getInt("sendStatus");
            String statusStr = sendStatus == 0 ? "未发送" : "已发送";
            item.setString("sendStatus", statusStr);
            Calendar createTime = item.getCalendar("createTime");
            item.setString("createTime", Parser.parseDateString(createTime, "yyyy-MM-dd HH:mm:ss"));
        }


        result.setList("dataList", smsList);
        result.setInt("total", total);
        result.setBoolean("success", true);
        return result.toJson();
    }


    /** payRecord.jsp start **/
    private static String getPayRecordList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        if(!Auth.checkFaiscoAuth("authYKSale1|authYkSaleLeader", false)){
            return "没有权限";
        }
        int sid = Session.getSid();
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);

        boolean isYkSaleLeader = Auth.checkFaiscoAuth("authYkSaleLeader", false);

        boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);
        
        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);

    	//查询销售
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleYk.getYkSaleList(saleSearchArg, infoList);
        
        FaiList<Integer> sidList = new FaiList<Integer>();                       
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        
        double totalPrice = 0.0;
        int totalClient = 0;
        FaiList<Param> orderItemList = new FaiList<Param>();
        FaiList<Integer> aidList = new FaiList<Integer>();

        //查询acctPreSaleYk表
        boolean selectPreSaleYk = selectAcctPreSaleYk(request, aidList);
        if(selectPreSaleYk && aidList.size() == 0){
        	return "{\"success\":true, \"dataList\":" + orderItemList + ", \"total\":" + orderItemList.size() + ", \"totalPrice\":0" + ", \"totalClient\":0" + "}";  
        }
        Log.logStd("aidList size = %s", aidList.size());
        
        // 到时相同条件再查询总金额和客户数
        SearchArg searchArg = new SearchArg();
        orderItemList = selectAcctOrderItem(request, searchArg, aidList, sidList);
        Log.logStd("orderItemList size = %s", orderItemList.size());
        
        for(Param p : orderItemList){
        	aidList.clear();
        	aidList.add(p.getInt("aid", 0));
        }
        
        Param newReceTimeParam = new Param();
        HashMap<Integer, Param> oldReceTimeMap = new HashMap<Integer, Param>();
       
        // 获取新旧账号的领取时间
        getOldAndNewReceiveTime(aidList, newReceTimeParam, oldReceTimeMap);

        int total = 0;

        //******************** 统计客户数量以及金额 *******************
        searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
		Log.logStd(searchArg.matcher.toString());
        
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.CountInfo.COUNT_AID);
        fieldList.add(BssStatDef.CountInfo.SUM_PRICE);
        fieldList.add("count(id)");

        //查询数据时限制了数目，这里改为默认值，这样统计总体数据才不会异常
        searchArg.start = 0;
        searchArg.limit = -1;
        FaiList<Param> countList = sysBssStat.getOrderItemCount(fieldList, null, searchArg);
        if (countList.size() > 0) {
            Param countInfo = countList.get(0);
            totalPrice = countInfo.getDouble(BssStatDef.CountInfo.SUM_PRICE, 0.0);
            totalClient = countInfo.getInt(BssStatDef.CountInfo.COUNT_AID, 0);
            total = countInfo.getInt("count(id)", 0);
        }
        
        SearchArg searchArgBss = new SearchArg();
        searchArgBss.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        
        FaiList<String> fieldListBss = new FaiList<String>();
        fieldListBss.add(BssStatDef.Info.AID);
        fieldListBss.add(BssStatDef.Info.REG_MOBILE);
        //FaiList<Param> acctList = sysBssStat.getAllAcctInfoList(fieldListBss, searchArgBss);

        FaiList<Object> nestExpr = new FaiList<Object>();
        nestExpr.add(IN.contains("aid", aidList));
        FdpDataParamMatcher matcher = AND.expr(nestExpr);

        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid", "reg_mobile")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> acctList = execute.getData();


        Param acctParam = new Param(true);
        for (Param acct : acctList) {
            int aid = acct.getInt(BssStatDef.OrderItemInfo.AID, 0);
            acctParam.setParam(String.valueOf(aid), acct);
        }
        for (Param order : orderItemList) {
            int aid = order.getInt(BssStatDef.OrderItemInfo.AID, 0);
            Param acct = acctParam.getParam(String.valueOf(aid), new Param());
            order.setString(BssStatDef.Info.REG_MOBILE, acct.getString(BssStatDef.Info.REG_MOBILE, ""));
        }
        
        //翻译数据
        bindOrderList(orderItemList, newReceTimeParam, oldReceTimeMap);
        
        // 导出到excel
        if (excel && isYkSaleLeader) {
            response.setContentType("application/x-excel");
            //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("付款记录.xls".getBytes("GBK"), "ISO-8859-1"));
            out.clear();//必须得加
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString("aid", "aid");
            cellKey.setString("regTimeStr", "注册时间");
            cellKey.setString("orderId", "订单号");
            cellKey.setString("createTimeStr", "创建时间");
            cellKey.setString("payTimeStr", "支付时间");
            cellKey.setString("refundTimeStr", "退款时间");
            cellKey.setString("payCorp", "支付企业");
            cellKey.setString("sales", "售前销售");
            cellKey.setString("price", "金额");
            cellKey.setString("type", "类型");
            cellKey.setString("statusStr", "状态");
            cellKey.setString("productStr", "产品类型");
            cellKey.setString("corpGoalName", "用途");
            cellKey.setString("receiveTime", "领取时间");
            cellKey.setString("ta", "注册来源");
            cellKey.setString("oldAid", "旧账号");
            cellKey.setString("oldReceiveTime", "旧账号领取时间");

            OssPoi.exportExcel(cellKey, orderItemList, outputStream);

            return "{\"success\":true}";
        }
        return "{\"success\":true, \"dataList\":" + orderItemList + ", \"total\":" + total + ", \"totalPrice\":" + totalPrice + ", \"totalClient\":" + totalClient + "}";
   	}
    	
    // @return 是否使用了acctPreSaleYk的筛选条件
   	private static boolean selectAcctPreSaleYk(HttpServletRequest request, FaiList<Integer> aidList)throws Exception{
		Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
		SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
		  
	    // 获取查询条件 start 
        boolean receiveOnTime = Parser.parseBoolean(request.getParameter("receiveDateFlag"), false);       // 是否查询领取时间
        boolean oldReceiveOnTime = Parser.parseBoolean(request.getParameter("oldReceiveDateFlag"), false); //是否查询旧账号领取时间

        int time_tag = Parser.parseInt(request.getParameter("time_tag"), -1);      // 时间段
        int se_tag = Parser.parseInt(request.getParameter("tag"), -1);            // 标签
		
        if(receiveOnTime || oldReceiveOnTime || time_tag > -1 || se_tag > -1){
        	
        	// 日期设置 START
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DATE, -7);//7天前

            FaiList<String> fieldList = new FaiList<String>();
            
            //如果筛选了旧账号领取时间先去acctPreSaleHd表查领取时间在范围内的的账号，再去审批表查这些账号是否旧账号，是得话将新账号作为订单的查询条件
            FaiList<Integer> oldAidList = new FaiList<Integer>();
            FaiList<Integer> newAidList = new FaiList<Integer>();
            if(oldReceiveOnTime){
            	//旧账号领取时间
                String oldReceiveDateBeg = Parser.parseString(request.getParameter("oldReceiveDateBeg"), Parser.parseSimpleTime(c));
                String oldReceiveDateEnd = Parser.parseString(request.getParameter("oldReceiveDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));

            	fieldList.clear();
                fieldList.add(PreSaleHdDef.Info.AID);

                SearchArg oldArg = new SearchArg();
                ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, oldReceiveDateBeg + " 00:00:00");
                matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, oldReceiveDateEnd + " 23:59:59");
                oldArg.limit = 30000;
                oldArg.matcher = matcher;

                FaiList<Param> tempList = sysPreSaleYk.getAllInfoList(oldArg);
                for(Param p : tempList){
                    oldAidList.add(p.getInt("aid",0));
                }

                //查审批表，关联新账号
                Dao.SelectArg selectArg = new Dao.SelectArg();
                selectArg.field = "aid,oldAid";
                selectArg.table = "ykSaleApprove";
                matcher = new ParamMatcher(HdSaleApproveDef.Info.OLD_AID,ParamMatcher.IN, oldAidList);
                matcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
                matcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
                selectArg.searchArg.matcher = matcher;
                Dao ykDao = WebHdOss.getYkOssDaoSlave();
                try{
                    tempList = ykDao.select(selectArg);
                }finally {
                    ykDao.close();
                }

                for(Param p : tempList){
                    newAidList.add(p.getInt("aid", 0));
                }
            }
               
            Log.logStd("new aidList size = %d", newAidList.size());

            //领取时间查询,只拿成交库就好了
            SearchArg searchArgHdTotal = new SearchArg();
            searchArgHdTotal.matcher = new ParamMatcher();
            fieldList.clear();
            fieldList.add(PreSaleHdDef.Info.AID);
            
            if (receiveOnTime) {
            	//领取时间
                String receiveBegDate = Parser.parseString(request.getParameter("receiveDateBeg"), Parser.parseSimpleTime(c));
                String receiveEndDate = Parser.parseString(request.getParameter("receiveDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveBegDate + " 00:00:00");
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveEndDate + " 23:59:59");
            }
            searchArgHdTotal.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.DEAL);//以防数据了太大

            // 标签
            if (se_tag > -1 ) {
            	searchArgHdTotal.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.EQ, se_tag);
            }
            //TODO:这里用PreSaleHdDef.Flag.RECEIVE_AFTER_1645就报错,先用0x1代替
            if (time_tag > -1) {
                searchArgHdTotal.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, 0x1, time_tag);
            }
     
            if (!searchArgHdTotal.matcher.isEmpty()) {
                FaiList<Param> tempList = sysPreSaleYk.getAllInfoList(searchArgHdTotal);
                for (Param p : tempList) {
                	aidList.add(p.getInt(PreSaleHdDef.Info.AID, 0));
                }
            }
            
            return true;
        }
        return false;
   	}
   

        private static FaiList<Param> selectAcctOrderItem(HttpServletRequest request, SearchArg searchArg, FaiList<Integer> aidList, FaiList<Integer> sidList)throws Exception{
        	SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);
        	
        	// 日期设置 START
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DATE, -7);
            FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();

            /*** 分页参数 ****/
            int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);        // 当前页数
            int limit = Parser.parseInt(request.getParameter("limit"), 10);            // 一页多少条数据
            int start = (pageNo - 1) * limit;                                        // 开始
            Log.logStd("pageNo =%s, limit=%s", pageNo, limit);
            
            // 获取查询条件 start 
            boolean regOnTime = Parser.parseBoolean(request.getParameter("regDateFlag"), false);            // 是否查询注册时间
            boolean creOnTime = Parser.parseBoolean(request.getParameter("creDateFlag"), false);            // 是否查询创建时间
            boolean payOnTime = Parser.parseBoolean(request.getParameter("payDateFlag"), false);            // 是否查询支付时间
            boolean refOnTime = Parser.parseBoolean(request.getParameter("refDateFlag"), false);            // 是否查询退款时间
            boolean receiveOnTime = Parser.parseBoolean(request.getParameter("receiveDateFlag"), false);    // 是否查询领取时间
            boolean oldReceiveOnTime = Parser.parseBoolean(request.getParameter("oldReceiveDateFlag"), false);//是否查询旧账号领取时间
            
          	//支付时间
            String begDate = Parser.parseString(request.getParameter("payDateBeg"), Parser.parseSimpleTime(c));
            String endDate = Parser.parseString(request.getParameter("payDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
            int begDateInt = (int) (Parser.parseCalendar(begDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int endDateInt = (int) (Parser.parseCalendar(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

            //订单创建时间
            String begDate2 = Parser.parseString(request.getParameter("creDateBeg"), Parser.parseSimpleTime(c));
            String endDate2 = Parser.parseString(request.getParameter("creDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
            int begDateInt2 = (int) (Parser.parseCalendar(begDate2 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int endDateInt2 = (int) (Parser.parseCalendar(endDate2 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

            //注册时间
            String begDate3 = Parser.parseString(request.getParameter("regDateBeg"), Parser.parseSimpleTime(c));
            String endDate3 = Parser.parseString(request.getParameter("regDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
            int begDateInt3 = (int) (Parser.parseCalendar(begDate3 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int endDateInt3 = (int) (Parser.parseCalendar(endDate3 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

            //退款时间
            String begDate4 = Parser.parseString(request.getParameter("refDateBeg"), Parser.parseSimpleTime(c));
            String endDate4 = Parser.parseString(request.getParameter("refDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
            int begDateInt4 = (int) (Parser.parseCalendar(begDate4 + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
            int endDateInt4 = (int) (Parser.parseCalendar(endDate4 + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        	
        	int sea_PayType = Parser.parseInt(request.getParameter("payType"), -1);    // 类型
            int seq_cmpor = Parser.parseInt(request.getParameter("sort"), 2);        // 排序
            int seq_status = Parser.parseInt(request.getParameter("status"), 0);    // 订单状态
            int seq_pay = Parser.parseInt(request.getParameter("pay"), 0);            // 支付情况
            int se_price1 = Parser.parseInt(request.getParameter("minPrice"), -1);        // min金额
            int se_price2 = Parser.parseInt(request.getParameter("maxPrice"), -1);        // max金额
            int product = Parser.parseInt(request.getParameter("productType"), 20);    // 产品类型（只查悦客产品）
            int seq_goal = Parser.parseInt(request.getParameter("goal"), -1);            // 用途
            int se_ta = Parser.parseInt(request.getParameter("ta"), -1);            // 注册来源
            int se_tag = Parser.parseInt(request.getParameter("tag"), -1);            // 标签
            int amount = Parser.parseInt(request.getParameter("amount"), 0);            // 购买数量
            int time_tag = Parser.parseInt(request.getParameter("time_tag"), -1);            // 时间段
            int staff_sid = Parser.parseInt(request.getParameter("staff_sid"), 0);    // 领取人
            int seq_aid = Parser.parseInt(request.getParameter("aid"), 0);            // aid
            int seq_orderId = Parser.parseInt(request.getParameter("orderId"), 0);    // 订单号
            
            //String staff_group = Parser.parseString(request.getParameter("staff_group"), "all"); // 销售分组
            String cmd = Parser.parseString(request.getParameter("cmd"), "");        // 判断是否点击过查询(cmd为空代表第一次进入页面)
            
            boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);
            
         	// 获取关联的订单item数据 
            searchArg.matcher = new ParamMatcher();
            if(aidList != null && aidList.size() > 0){
            	searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN, aidList);
            }
            
            searchArg.matcher = new ParamMatcher(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, sidList);                        // 电话销售的销售业绩
            
            if(product != -1){
            	searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, faiTradeStationBaseApi.getBizVerProductIdList(product));
            }else{
                //选择全部产品时：悦客、互动、微传单、助手、网站、商城、轻站
                FaiList<Integer> allProductList = new FaiList<Integer>();
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(15));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(5));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(7));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(9));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(1));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(2));
                allProductList.addAll(faiTradeStationBaseApi.getBizVerProductIdList(8));

                searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, allProductList);
            }
            
            if (amount != 0) {//如果要查询购买数量，要区分按月还是按年存，prop2=1时是按月，其他是按年
                ParamMatcher searchArgTemp = null;
                ParamMatcher searchArgProp2 = new ParamMatcher();
                if (amount == 1) {//购买数量为1年
                    searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.EQ, 1);//按月计算
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 12);//大于等于1个月，小于等于12个月
                    searchArgProp2.and(searchArgTemp);
                    searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.NE, 1);//按年计算的
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.EQ, 1); //购买数量是1年
                    searchArgProp2.or(searchArgTemp);
                    searchArg.matcher.and(searchArgProp2);
                }
                if (amount == 2) {//大于等于2年算多年
                    searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.EQ, 1);//按月计算
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 24);//大于等于三年的版本
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 120);//大于等于三年的版本×
                    searchArgProp2.and(searchArgTemp);
                    searchArgTemp = new ParamMatcher(BssStatDef.OrderItemInfo.PROP2, ParamMatcher.NE, 1);//按年计算
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.GE, 2);
                    searchArgTemp.and(BssStatDef.OrderItemInfo.AMOUNT, ParamMatcher.LE, 10);
                    searchArgProp2.or(searchArgTemp);
                    searchArg.matcher.and(searchArgProp2);
                }
            }
            
          	//如果 创建时间or支付时间or注册时间 都没有勾选的话，默认选择支付时间
            boolean crePayTime = false;
            if (creOnTime == false && !payOnTime && !regOnTime && !refOnTime) {

                if (seq_status == 2) {
                    creOnTime = true;
                } else {
                    payOnTime = true;
                }

                if (!cmd.equals("")) {    // 弹出勾选时间的提示框
                    crePayTime = true;
                }
            }

         	// 类型（支付类型）
            if (sea_PayType != -1) {
                searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TYPE, ParamMatcher.EQ, sea_PayType);
            }
         	
	        if (seq_aid > 0) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.EQ, seq_aid);
	        }
	
	        if (seq_orderId > 0) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.ORDER_ID, ParamMatcher.EQ, seq_orderId);
	        }
	
	        // 注册时间
	        if (regOnTime) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.REG_TIME, ParamMatcher.GE, begDateInt3);
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.REG_TIME, ParamMatcher.LE, endDateInt3);
	        }
	        // 支付时间
	        if (payOnTime) {
	            if (seq_status != 2) {
	                searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, begDateInt);
	                searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, endDateInt);
	            }
	        }
	        // 退款时间
	        if (refOnTime) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.REFUND_TIME, ParamMatcher.GE, begDateInt4);
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.REFUND_TIME, ParamMatcher.LE, endDateInt4);
	        }
	        // 下单时间（订单创建时间）
	        if (creOnTime) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.CREATE_TIME, ParamMatcher.GE, begDateInt2);
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.CREATE_TIME, ParamMatcher.LE, endDateInt2);
	        }
	        // 已退款
	        if (seq_status == 3) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.DEL);
	        }
	        // 处理完成的订单
	        if (seq_status == 1) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.NE, BssStatDef.DelStatus.DEL);
	        }
	        // 待支付的订单
	        if (seq_status == 2) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, FaiOrderDef.Status.FIN_SETTLE);
	        }
	
	        // 销售分组-权限组
	        /* if (!staff_group.equals("all")) {
	            FaiList<Integer> tmpSidList = WebOss.getAuthSidList(staff_group);
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, tmpSidList);
	        } */
	        // 某个销售
	        if (staff_sid != 0) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.EQ, staff_sid);
	        }
	
	        // 有支付
	        if (seq_pay == 1) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GT, 0);
	        }
	        // 无需支付
	        if (seq_pay == 2) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.EQ, 0);
	        }
	
	        // 注册用途
	        if (seq_goal != -1) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.COMPANY_GOAL, ParamMatcher.EQ, seq_goal);
	        }
	        
	        // 注册（注册来源）
	        FaiList<Param> taList = getTaList();                      
	        if (se_ta != -1) {
	            FaiList<Integer> trackList = PreSaleHdDef.getTaList(taList, se_ta);
	            if (se_ta != PreSaleHdDef.PreSaleTa.NOT_IN) {
	            	searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.IN, trackList);
	            } else {
	                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, trackList);
	            }
	        }

	        // 类型（支付类型）
	        if (sea_PayType != -1) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TYPE, ParamMatcher.EQ, sea_PayType);
	        }

	        // 区间金额
	        if (se_price1 >= 0) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GE, Parser.parseDouble(se_price1 + "", 0.0));
	        }
	        if (se_price2 >= 0) {
	            searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.LE, Parser.parseDouble(se_price2 + "", 0.0));
	        }

	        // 排序
	        if (seq_cmpor == 0) {
	            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.ID, true);
	        } else if (seq_cmpor == 1) {
	            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.REG_TIME, true);
	        } else if (seq_cmpor == 2) {
	            searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.PAY_TIME, true);
	        }

	        // 分页
	        if (!excel) {
	            searchArg.start = start;
	            searchArg.limit = limit;
	        }

	        Log.logStd("sql = %s", searchArg.matcher.toString());
	        FaiList<Param> orderItemList = sysBssStat.getOrderItemList(searchArg);
	        
	        Log.logStd("orderList = " + orderItemList);
	        return orderItemList;
        }
        
        private static void getOldAndNewReceiveTime(FaiList<Integer> aidList, Param newReceParam, Map oldReceMap)throws Exception{
        	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
   		    SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
   		  
            
			//******************* 拿到领取时间 ********************

            SearchArg searchArgYk = new SearchArg();
            searchArgYk.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
            
            FaiList<Param> preSaleYkList = sysPreSaleYk.getAllInfoList(searchArgYk);
            for (Param item : preSaleYkList) {
                newReceParam.setParam(String.valueOf(item.getInt("aid")), item);
            }

            //******************** 关联旧账号 ********************
            Dao.SelectArg approveArg = new Dao.SelectArg();
            approveArg.field = "aid,oldAid";
            approveArg.table = "ykSaleApprove";
            ParamMatcher approveMatcher = new ParamMatcher(HdSaleApproveDef.Info.AID, ParamMatcher.IN, aidList);
            approveMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
            approveMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
            approveArg.searchArg.matcher = approveMatcher;
            
            FaiList<Param> approveList = new FaiList<Param>();
            Dao ykDao = WebHdOss.getYkOssDaoSlave();
            try {
                approveList = ykDao.select(approveArg);
                Log.logStd("approve list size = %d", approveList.size());
            } finally {
            	ykDao.close();
            }

            FaiList<Integer> oldAidList = new FaiList<Integer>();
            for (Param p : approveList) {
                int aid = p.getInt("aid", 0);
                int oldAid = p.getInt("oldAid", 0);
                oldAidList.add(oldAid);
                oldReceMap.put(oldAid, p);
            }

            searchArgYk = new SearchArg();
            searchArgYk.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, oldAidList);
            FaiList<Param> oldAidReceiveList = sysPreSaleYk.getAllInfoList(searchArgYk);
            for (Param p : oldAidReceiveList) {
                int oldAid = p.getInt("aid", 0);
                Calendar receiveTime = p.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);
                Param item = (Param)oldReceMap.get(oldAid);
                item.setCalendar("receiveTime", receiveTime);
                int aid = item.getInt("aid", 0);
                oldReceMap.put(aid, item);
            }

        }
        
        private static void bindOrderList(FaiList<Param> orderItemList, Param newReceTimeParam, Map oldReceTimeMap)throws Exception{
        	String domain = WebHdOss.getOssDomainUrl();
        	FaiList<Param> taList = getTaList();
            FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();

            //******************** 数据翻译 ********************
            Map<Integer, String> tagMap = getTagMap();
            for (Param order : orderItemList) {
                int aid = order.getInt(BssStatDef.OrderItemInfo.AID, 0);
                int sid = order.getInt(BssStatDef.OrderItemInfo.S_ID, 0);
                faiTradeStationBaseApi.changeAid(aid);
                faiTradeStationBaseApi.changeSiteId(sid);
                int id = order.getInt(BssStatDef.OrderItemInfo.ORDER_ID, 0);
                String regTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.REG_TIME, 0), "yyyy-MM-dd HH:mm:ss");
                String createTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.CREATE_TIME, 0), "yyyy-MM-dd");
                String payTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.PAY_TIME, 0), "yyyy-MM-dd HH:mm:ss");
                String refundTime = Parser.parseDateString(order.getInt(BssStatDef.OrderItemInfo.REFUND_TIME, 0), "yyyy-MM-dd");
                double price = order.getDouble(BssStatDef.OrderItemInfo.PRICE, 0.0);

                String status = FaiOrderDef.getStatusName(order.getInt(BssStatDef.OrderItemInfo.STATUS, 0));
                String typeStr = FaiOrderDef.getPayTypeName(order.getInt(BssStatDef.OrderItemInfo.PAY_TYPE, 0));
                String productStr = faiTradeStationBaseApi.getName(order.getInt(BssStatDef.OrderItemInfo.PRODUCT_ID, 0));

                // 退款显示已退款
                int isDel = order.getInt(BssStatDef.OrderItemInfo.DEL, 0);

                if (isDel == 1) {
                    status = "已退款";
                }

                // 销售
                String preSaleSalesAcct = "";
                int salesSid = order.getInt(BssStatDef.OrderItemInfo.SALES_SID, 0);
                if (salesSid > 0) {
                    preSaleSalesAcct = WebOss.getStaffName(salesSid);
                }

                int goal = order.getInt(BssStatDef.OrderItemInfo.COMPANY_GOAL);
                String corpGoalName = CorpProfDef.getCorpGoal2Name(goal);

                int ta = order.getInt(BssStatDef.OrderItemInfo.TA);
                String taName = PreSaleHdDef.getTaName(taList, ta);

                // 领取时间
                Param preSaleYkInfo = newReceTimeParam.getParam(String.valueOf(aid), new Param());
                Calendar receiveTimeCal = preSaleYkInfo.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME);
                String receiveTime = "";
                if (receiveTimeCal != null) {
                    receiveTime = Parser.parseString(receiveTimeCal);
                }
                int tag = preSaleYkInfo.getInt(PreSaleHdDef.Info.TAG, 0);
                String tagName = tagMap.get(tag);

                //旧账号
                Param oldParam = (Param)oldReceTimeMap.get(aid);
                int oldAid = 0;
                String oldReceiveTime = "";
                if(oldParam != null){
                    oldAid = oldParam.getInt("oldAid",0);
                    oldReceiveTime = Parser.parseString(oldParam.getCalendar("receiveTime"));
                }

                order.clear();
                order.setInt("aid", aid);
                order.setString("sales", preSaleSalesAcct);//销售
                order.setDouble("price", price);//价格
                order.setInt("orderId", id);
                order.setString("orderUrl", domain + "/cs/orderDetail.jsp?aid=" + aid + "&id=" + id);
                order.setString("aidUrl", domain + "/index.jsp?t=ykSale&u=/cs/corp.jsp?aid=" + aid);
                order.setString("regTimeStr", regTime);
                order.setString("createTimeStr", createTime);
                order.setString("payTimeStr", payTime);
                order.setString("refundTimeStr", refundTime);
                order.setString("receiveTime", receiveTime);//领取时间
                order.setString("payCorp", Acct.getCorpLabel(aid));//支付企业
                order.setString("payCorpSite", Web.getSiteHost(Acct.getCacct(aid)));//企业站点
                order.setString("type", typeStr);//类型
                order.setString("statusStr", status);//状态
                order.setString("productStr", productStr);//产品类型
                order.setString("corpGoalName", corpGoalName);//用途
                order.setString("ta", taName);//注册来源

                if(oldAid == 0){
                    order.setString("oldAid","无");
                }else{
                    order.setInt("oldAid",oldAid);
                    order.setString("oldReceiveTime",oldReceiveTime);
                }

                if (tagName == null) {
                    tagName = "";
                }
                
              	//标签
                order.setString("tagName", tagName);
            }
        }
        
        
        
/** payRecord.jsp end **/


/** meritStatistics.jsp start **/

    /**
     * 查询 时间段内/外 互动销售 领取情况
     */
    private static Param getSalePerSon(int begT, int endT, boolean isInnerTime) throws Exception {
        Dao dao = WebOss.getOssBsDao();
        try {
            /***** 获取acctPreSale表里的领取情况	*****/
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "salesAcct,aid,status";

            // 先按员工分组查出一个大List
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.DEL, ParamMatcher.EQ, false);
            //sltArg.searchArg.matcher.and(PreSaleHdDef.Info.STATUS,ParamMatcher.EQ,PreSaleHdDef.Status.PERSON);//个人库

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//acctPreSaleHd表的receiveTime存的是yyyy-MM-dd HH:mm:ss格式，这里需要做相应处理
            if (isInnerTime) {//时间段内 and (begT < xx < endT)
                sltArg.searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, sdf.format(new java.util.Date(new Long(begT) * 1000)));//秒数 转 毫秒数
                sltArg.searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, sdf.format(new java.util.Date(new Long(endT) * 1000)));
            } else {//时间段外 and (xx<begT )
                ParamMatcher outerTimeMatcher = new ParamMatcher(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, sdf.format(new java.util.Date(new Long(begT) * 1000)));
                //outerTimeMatcher.or(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, sdf.format(new java.util.Date(new Long(endT) * 1000)));
                sltArg.searchArg.matcher.and(outerTimeMatcher);
            }
            //Log.logStd("li test time=%s",new java.util.Date(new Long(begT) * 1000));
            /* //领取数排除B库资源
            sltArg.searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.UN_CONTACT_30DAY,PreSaleHdDef.Flag.UN_CONTACT_30DAY); */
            //排除公众号助手资源和悦客
            sltArg.searchArg.matcher.and(PreSaleHdDef.Info.TAG,ParamMatcher.NE,PreSaleHdDef.Tag.OPENMP_RESOURCE);
			sltArg.searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
		    sltArg.searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
            Log.logStd("unique-test: slect art = %s", sltArg.getSql());
            FaiList<Param> preSaleList = dao.select(sltArg);

            FaiList<Param> recordList = null;
            sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.field = "sacct,aid";

            if (isInnerTime) {//计算每月1号释放掉的个人库资源数
                Calendar tempBeginTimeCal = Parser.parseCalendar(new java.util.Date(new Long(begT) * 1000));
                Calendar tempEndTimeCal = Parser.parseCalendar(new java.util.Date(new Long(endT) * 1000));
                String begTimeStr = Parser.parseDateString(tempBeginTimeCal, "yyyy-MM-dd HH:mm:ss");
                String endTimeStr = Parser.parseDateString(tempEndTimeCal, "yyyy-MM-dd HH:mm:ss");
                tempBeginTimeCal.add(Calendar.MONTH, 1);
                tempEndTimeCal.add(Calendar.MONTH, 1);
                String nextBegTimeStr = Parser.parseDateString(tempBeginTimeCal, "yyyy-MM-dd HH:mm:ss");
                String nextEndTimeStr = Parser.parseDateString(tempEndTimeCal, "yyyy-MM-dd HH:mm:ss");
                //Log.logStd("li test nextBegTimeStr=%s tempBeginTimeCal=%s",nextBegTimeStr,tempBeginTimeCal);
                Log.logStd("unique-test: beginTimeStr = %s , endTimeStr = %s", begTimeStr, endTimeStr);
                sltArg.searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.RELEASE_PERSON);//系统释放
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.GE, nextBegTimeStr);
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, begTimeStr);
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, endTimeStr);

            } else {//非当月领取数 = 个人库中领取时间为该月以前资源数 + 下月释放领取时间未改月之前的资源数
                Calendar createTimeCal = Parser.parseCalendar(new java.util.Date(new Long(begT) * 1000));
                Calendar receiveTimeCal = createTimeCal;
                createTimeCal.add(Calendar.MONTH, 1);
                String createTimeStr = Parser.parseDateString(createTimeCal, "yyyy-MM-dd HH:mm:ss");
                String receiveTimeStr = Parser.parseDateString(receiveTimeCal, "yyyy-MM-dd HH:mm:ss");
                sltArg.searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.RELEASE_PERSON);//系统释放
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.GE, createTimeStr);
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveTimeStr);
            }
            //排除公共号助手资源
            sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.TAG,ParamMatcher.NE,PreSaleHdDef.Tag.OPENMP_RESOURCE);
			sltArg.searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
			sltArg.searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
            //Long testBegin = System.currentTimeMillis();
            recordList = dao.select(sltArg);
            Log.logStd("unique-test: slect art = %s", sltArg.getSql());
            Log.logStd("unique-test: recore list size = %s", recordList.size());
            //Long testEnd = System.currentTimeMillis();
            //Log.logStd("unique-test record serarch  use time = "+(testEnd-testBegin)+" record list size = "+recordList.size());

            //分配的aid列表，用于后续的新账号剔除
            FaiList<Integer> aidList = new FaiList<Integer>();

            Param saleParam = new Param(true);
            HashSet<String> set = new HashSet<String>();
            if (preSaleList != null) {
                for (Param s : preSaleList) {
                    String acct = s.getString("salesAcct");
                    int aid = s.getInt("aid", 0);
                    aidList.add(aid);
                    Param aidParam = saleParam.getParam(acct, new Param(true));
                    aidParam.setInt("" + aid, 1);//存领取的aid
                    int status = s.getInt("status");
                    int notDeal = saleParam.getInt(acct + "-notDealCount", 0);
                    if (status == 1) {
                        saleParam.setInt(acct + "-notDealCount", ++notDeal);//存未成交的领取数。前月和非当月资源要用到。
                    }
                    saleParam.setParam(acct, aidParam);
                }
            }

            //计算已经释放掉的个人库资源数 领取数 = (个人库+成交库+释放资源)去重后的aid数量
            if (recordList != null && !recordList.isEmpty()) {
                for (Param p : recordList) {
                    String acct = p.getString("sacct", "");
                    int aid = p.getInt("aid", 0);
                    aidList.add(aid);
                    //if(saleParam.containsKey(acct+"-notDealCount")){
                    Param aidParam = saleParam.getParam(acct, new Param(true));
                    if (!aidParam.containsKey(aid + "")) {
                        aidParam.setInt("" + aid, 1);
                        int notDeal = saleParam.getInt(acct + "-notDealCount", 0);
                        saleParam.setInt(acct + "-notDealCount", ++notDeal);
                        saleParam.setParam(acct, aidParam);
                    }

                }
            }
            Log.logStd("saleParam = %s", saleParam);

            Param oldAndNewParam = new Param();   //存放新旧账号数据
            /**
             hdSale_1-removeList :[aid_1,aid_2];
             hdSale_1-addList ：[aid_3,aid_4];
             */
            //TODO
            //获取所有销售领取的客户中是否是有新账号
            /* FaiList<Param> approveList = new FaiList<Param>();
            sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleApprove";
            sltArg.field = "aid,oldAid";
            ParamMatcher newAidMatcher = new ParamMatcher();
            newAidMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
            newAidMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
            newAidMatcher.and(HdSaleApproveDef.Info.AID, ParamMatcher.IN, aidList);
            sltArg.searchArg.matcher = newAidMatcher;
            if (sltArg == null || newAidMatcher == null || aidList == null || dao == null) {
                Log.logStd(" args is null");
            }
            approveList = dao.select(sltArg);
            //Log.logStd("li test 领取资源中是新账号的个数为%s",approveList.size());


            HashMap<Integer, Integer> approveMap = new HashMap<Integer, Integer>();//key:oldAid,value:aid
            FaiList<Integer> approveOldAidList = new FaiList<Integer>();
            if (approveList != null && approveList.size() > 0) {
                for (Param p : approveList) {
                    int aid = p.getInt(HdSaleApproveDef.Info.AID, 0);
                    int oldAid = p.getInt(HdSaleApproveDef.Info.OLD_AID, 0);
                    approveOldAidList.add(oldAid);
                    approveMap.put(oldAid, aid);
                }
            }
            //Log.logStd("li test pre remove newAid aidList size=%s", aidList.size());
            //查询旧账号不在领取范围内，则把旧账号对应的新账号移除
            FaiList<Param> oldAidReceiveList = new FaiList<Param>();
            String begTimeCal = Parser.parseDateString(Parser.parseCalendar(new java.util.Date(new Long(begT) * 1000)), "yyyy-MM-dd HH:mm:ss");
            String endTimeCal = Parser.parseDateString(Parser.parseCalendar(new java.util.Date(new Long(endT) * 1000)), "yyyy-MM-dd HH:mm:ss");
            //Log.logStd("li test begTimeCal=%s,endTimeCal=%s", begTimeCal, endTimeCal);
            sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.field = "aid,receiveTime,sacct";
            ParamMatcher oldAidMatcher = new ParamMatcher();
            oldAidMatcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.IN, approveOldAidList);
            oldAidMatcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.PHONE_ALLOT);
            if (isInnerTime) {
                oldAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LT, begTimeCal);
            } else {
                oldAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GT, begTimeCal);
                oldAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LT, endTimeCal);
            }
            sltArg.searchArg.matcher = oldAidMatcher;
            FaiList<Param> oldAidRecordList = dao.select(sltArg);
            //排除旧账号不在领取范围时间的新账号
            for (Param p : oldAidRecordList) {
                int oldAid = p.getInt(HdSaleRecordDef.Info.AID, 0);
                Integer aid = approveMap.get(oldAid);
                if (aid == null) {
                    Log.logStd("aid is null ");
                }
                Param removeParam = Misc.getFirst(preSaleList, PreSaleHdDef.Info.AID, aid);
                String acct = p.getString("sacct");
                Param aidParam = saleParam.getParam(acct, new Param(true));
                //TODO 
                FaiList<Integer> removeList = oldAndNewParam.getList(acct + "-removeList", new FaiList<Integer>());
                // Log.logStd("li test remove aid=%s,sacct=%s",aid,acct);
                removeList.add(aid);
                oldAndNewParam.setList(acct + "-removeList", removeList);
                aidParam.remove(aid + "");
                aidList.remove(aid);
                if (removeParam != null) {
                    int status = removeParam.getInt("status", 0);
                    if (status == 1) {
                        int count = saleParam.getInt(acct + "-notDealCount", 0);
                        saleParam.setInt(acct + "-notDealCount", --count);//如果被剔除的aid是个人库，则把未成交数-1
                    }
                }
            }
            //Log.logStd("li test removeList=%s",removeList);
            Log.logStd("li test  after newAid aidList size=%s", aidList.size());

            // //查询领取资源是否有新账号，如果新账号不在领取范围内，则把新账号加入领取资源
            sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleApprove";
            sltArg.field = "aid,oldAid";
            newAidMatcher = new ParamMatcher();
            newAidMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
            newAidMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
            newAidMatcher.and(HdSaleApproveDef.Info.OLD_AID, ParamMatcher.IN, aidList);
            sltArg.searchArg.matcher = newAidMatcher;
            FaiList<Param> newAidApprove = dao.select(sltArg);
            FaiList<Integer> newAidList = new FaiList<Integer>();//领取资源的新账号
            if (newAidApprove != null && newAidApprove.size() > 0) {
                for (Param p : newAidApprove) {
                    newAidList.add(p.getInt("aid", 0));
                }
            }


            //查询新账号的领取是否在领取时间内，如果不在就把新账号资源加入aidList中
            sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.field = "aid,receiveTime,sacct";
            newAidMatcher = new ParamMatcher();
            newAidMatcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.IN, newAidList);
            newAidMatcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.PHONE_ALLOT);
            //TODO
            //这里逻辑待销售确认
            if (isInnerTime) {
                //ParamMatcher timeMatcher = new ParamMatcher(HdSaleRecordDef.Info.RECEIVE_TIME,ParamMatcher.LT,begTimeCal);
                //timeMatcher.or(HdSaleRecordDef.Info.RECEIVE_TIME,ParamMatcher.GT,endTimeCal);
                newAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LT, begTimeCal);
            } else {
                newAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, begTimeCal);
                newAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, endTimeCal);
            }
            sltArg.searchArg.matcher = newAidMatcher;
            FaiList<Param> newAidRecord = dao.select(sltArg);
            //Log.logStd("li test sql=%s",sltArg.getSql());
            newAidList.clear();
            if (newAidRecord != null && newAidRecord.size() > 0) {
                for (Param p : newAidRecord) {
                    newAidList.add(p.getInt("aid", 0));
                }
                Log.logStd("不在领取时间内的新账号个数%s", newAidList.size());
            }

            //再查询新账号是否有成交
            sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "salesAcct,aid,status";
            newAidMatcher = new ParamMatcher();
            newAidMatcher.and(PreSaleHdDef.Info.DEL, ParamMatcher.EQ, false);
            newAidMatcher.and(PreSaleHdDef.Info.AID, ParamMatcher.IN, newAidList);
            sltArg.searchArg.matcher = newAidMatcher;
            FaiList<Param> newAidPreSale = dao.select(sltArg);
            //Log.logStd("li test newAidPreSale sql=%s",sltArg.getSql());
            //Log.logStd("li test newAidRecord %s", newAidRecord.size());
            //Log.logStd("li test newAidList size=%s", newAidList.size());
            //Log.logStd("li test newAidPreSale size=%s", newAidPreSale.size());
            //把不在时间范围内的新账号添加到aidList
            int newAidCount = 0;
            if (newAidRecord != null && newAidRecord.size() > 0) {
                for (Param p : newAidRecord) {
                    String acct = p.getString("sacct");
                    int aid = p.getInt("aid", 0);
                    Param aidParam = saleParam.getParam(acct, new Param(true));
                    FaiList<Integer> addList = oldAndNewParam.getList(acct + "-addList", new FaiList<Integer>());
                    addList.add(aid);
                    oldAndNewParam.setList(acct + "-addList", addList);
                    aidParam.setInt(aid + "", 1);
                    aidList.add(aid);
                    Param tmp = Misc.getFirst(newAidPreSale, PreSaleHdDef.Info.AID, aid);
                    //Log.logStd("li test aid=%s, tmp=%s",aid,tmp);
                    if (tmp != null) {
                        int status = tmp.getInt("status", 0);
                        newAidCount++;
                        if (status == 1) {
                            int count = saleParam.getInt(acct + "-notDealCount", 0);
                            saleParam.setInt(acct + "-notDealCount", ++count);//如果新添加的aid是未成交，则把未成交数+1
                        }
                    }
                }
                Log.logStd("li test 不在时间范围新账号个数%s", newAidCount);
            }

            saleParam.setParam("oldAndNewParam", oldAndNewParam); */

            /**
             * saleParam 格式
             * {
             *     hdSale1: {
             *     	 "aid_1":1,
             *     	 "aid_2":1
             *     },
             *     hdSale1-notDealCount:2,
             *
             *     hdSale2: {
             *		 "aid_3":1,
             *     	 "aid_4":1
             *     },
             *     hdSale2-notDealCount:2,
             *     oldAndNewParam:{
             *	      hdSale1-removeList: [aid_5,aid_6],
             *        hdSale1-addList : [aid_7,aid_8],
             *     }
             *
             * }
             */
            //Log.logStd("li test adm removeList=%s",saleParam.getList("adm-removeList",new FaiList<Integer>()));
            Log.logStd("unique-test  getSalePerSon oldAndNewParam = %s", oldAndNewParam);
            Log.logStd("unique-test getSalePerSon - saleParam = " + saleParam.toJson());
            return saleParam;
        } finally {
            dao.close();
        }
    }

    /**
     * 返回时间段（内/外）的销售业绩统计。数据包括：销售sid、销售name、首购金额、首购人数，重购金额、重购人数、总金额、arpu、preArpu、nowAndPreArpu、nonNowArpu
     * @param preSales 要统计业绩的销售
     * @param begT 统计时间1
     * @param endT 统计时间2
     * @param isInner 统计时间段内 或 时间段外
     */
    private static FaiList<Param> getMeritList(FaiList<Integer> preSales, int begT, int endT, boolean isMonthOrWeek) throws Exception {
        //private static Param getMeritList(FaiList<Integer> preSales, int begT, int endT, boolean isMonthOrWeek) throws Exception {
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);// presaleHd 的接口
        FaiList<String> filedList = new FaiList<String>();
        filedList.add("sid");
        filedList.add("status");
        SearchArg saleArg = new SearchArg();
        saleArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GE, 0);
        FaiList<Param> saleInfo = new FaiList<Param>();
        int rt = sysPreSaleHd.getSalesList(filedList, saleArg, saleInfo);
        if (rt != Errno.OK) {
            Log.logStd("saleInfo search err");
        }
        HashMap<Integer, Integer> saleStatusMap = new HashMap<Integer, Integer>();
        for (Param p : saleInfo) {
            int status = p.getInt(HdSaleDef.Info.STATUS, 0);
            int sid = p.getInt(HdSaleDef.Info.SID, 0);
            saleStatusMap.put(sid, status);
        }

        Searcher searcher = null;
        Param info = null;                                                                                //行统计信息
        FaiList<Param> orderList = null;                                                                //订单数据
        FaiList<Param> statisList = new FaiList<Param>();                                                // 统计后的信息
        //Param dataParam = new Param();                                                                   //存放最终数据


        /******************** 获取数据组合 ********************/
        // 查bss订单的时候要去除企业团购的订单，企业团购订单不在这个页面计算佣金。
        FaiList<Integer> groupProducts = new FaiList<Integer>();
        groupProducts.add(FaiProductDef.Id.SEO_QUICK);
        groupProducts.add(FaiProductDef.Id.SEO_BAIDU);
        groupProducts.add(FaiProductDef.Id.SEO_WHOLE_NET);
        groupProducts.add(FaiProductDef.Id.YUN_TASK_SEO_DAY);
        groupProducts.add(FaiProductDef.Id.YUN_TASK_SEO_A);
        groupProducts.add(FaiProductDef.Id.YUN_TASK_SEO_B);
        groupProducts.add(FaiProductDef.Id.YUN_TASK_SEO_C);

        SearchArg searchArg = new SearchArg();

        // 拿所有销售的成交订单数据,acctOrderItem
        searchArg.matcher = new ParamMatcher(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.NO_DEL);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GT, 0.0);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, preSales);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, begT);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, endT);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.NOT_IN, groupProducts);
        Log.logStd("unique-test: order beginTime = %s,endTime = %s", begT, endT);
        FaiList<Param> allOrderList = sysBssStat.getOrderItemList(searchArg);

        //个人领取情况，用于统计arpu --  区分周统计和月统计
        Param nowPerson = new Param(true);
        Param prePerson = new Param(true);
        Param nonNowPerson = new Param(true);
        if (isMonthOrWeek) {//月统计 - 领取情况
            Log.logStd("unique-test: 当月/周");
            nowPerson = getSalePerSon(begT, endT, true);//当月
            Log.logStd("li test begTime=%s,endTime=%s, getSalePerSon=%s,isMonthOrWeek=%s", begT, endT, nowPerson, isMonthOrWeek);
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(new Long(begT) * 1000);
            cal.add(Calendar.MONTH, -1);
            int preBegT = (int) (cal.getTimeInMillis() / 1000);
            Log.logStd("unique-test: 前月");
            prePerson = getSalePerSon(preBegT, begT, true);//前月
            nonNowPerson = getSalePerSon(begT, endT, false);//非当月
        } else {//周统计 - 领取情况  这里算的当月资源数量
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(new Long(begT) * 1000);//传入来的时间
            cal.set(Calendar.DAY_OF_MONTH, 1);//1号
            cal.set(Calendar.HOUR_OF_DAY, 0);//0时
            cal.set(Calendar.MINUTE, 0);//0分
            cal.set(Calendar.SECOND, 0);//0秒
            int localMonthStart = (int) (cal.getTimeInMillis() / 1000);//得到本月初时间
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);//下个月初
            int localMonthEnd = (int) (cal.getTimeInMillis() / 1000);//得到下月初时间 也就是本月末
            //nowPerson = getSalePerSon(begT, endT, true);//当月领取。以选定日期为准
            nowPerson = getSalePerSon(localMonthStart, endT, true);//当月领取资源

            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) - 2);//上个月初
            int preMonthStart = (int) (cal.getTimeInMillis() / 1000);//得到上月初时间
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + 1);//本个月初 也就是上月末
            int preMonthEnd = (int) (cal.getTimeInMillis() / 1000);//得到上月末时间
            prePerson = getSalePerSon(preMonthStart, preMonthEnd, true);//前月

            nonNowPerson = getSalePerSon(localMonthStart, localMonthEnd, false);//非当月
        }


        //销售组
        int nowMonthReceiveCount = 0;            //当月领取数量
        int preMonthReceiveCount = 0;            //前月领取数量
        int notNowMonthReceiveCount = 0;        //非当月领取数量


        //虚拟销售
        for (int sid : preSales) {//所有销售
            if (!WebOss.checkStaffLogin(sid)) {
                continue;
            }

            // 根据sid拿到销售信息
            Param staff = Acct.getStaffInfo(Web.getFaiCid(), sid);
            String name = staff.getHtmlString(StaffDef.Info.NAME);
            String sacct = staff.getString(StaffDef.Info.SACCT);


            if (sacct == null) {
                Log.logStd("get preSales sacct err;sid=%d;sacct=%s", sid, sacct);
                continue;
            }

            Param localMonthReceive = nowPerson.getParam(sacct, new Param(true));//当月领取资源
            //Log.logStd("li test localMonthReceive=%s ,sacct=%s ",localMonthReceive.keySet(),sacct);
            //Log.logStd("li test localMonthReceive=%s,sid=%s",localMonthReceive,sid);
            Param preMonthReceive = prePerson.getParam(sacct, new Param(true));//前月月领取资源
            Param nonMonthReceive = nonNowPerson.getParam(sacct, new Param(true));//非当月领取资源
            searchArg.matcher = new ParamMatcher(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.EQ, sid);
            searcher = new Searcher(searchArg);
            FaiList<Param> personOrderList = searcher.getParamList(allOrderList);//个人订单数据
            Log.logStd("sid = %d , personOrderList size = %d", sid, personOrderList.size());

            SearchArg searchArgB = new SearchArg();
            searchArgB.matcher = new ParamMatcher("salesAcct", ParamMatcher.EQ, sacct);
            searchArgB.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.UN_CONTACT_30DAY, PreSaleHdDef.Flag.UN_CONTACT_30DAY);
            FaiList<String> fieldList = new FaiList<String>();
            fieldList.add("aid");
            FaiList<Param> preSaleB = sysPreSaleHd.getList(fieldList, searchArgB);//排除该销售B库资源
            HashSet<Integer> aidSetB = new HashSet();
            for (Param p : preSaleB) {
                aidSetB.add(p.getInt("aid", 0));
            }


            //销售说要大于380才计算成单数。。
           
           /*  for (Param item : personOrderList) {
                Double onePrice = PreSaleHdDef.getPrice(item.getDouble(BssStatDef.OrderItemInfo.PRICE, 0.0), item.getInt(BssStatDef.OrderItemInfo.PRODUCT_ID, -1));//订单金额
                int aid = item.getInt("aid",0);
                if (onePrice > 380) {
                    orderCount++;
                }
                
                
                
                
            } */

            info = new Param();//存储统计信息
            info.setInt("sid", sid);
            info.setString("name", name);
            info.setString("sacct", sacct);
            if (sacct.contains("hdsale")) {
                info.setInt(HdSaleDef.Info.STATUS, 2);//虚拟销售status设成2，用作前端统计
            } else {
                info.setInt(HdSaleDef.Info.STATUS, saleStatusMap.get(sid));
            }

            //info.setInt("orderCount", orderCount); //成单数
            double repeatPrice = 0.0;//重购金额
            Set<Integer> repeatPaySet = new HashSet<Integer>();//成交客户个数 去重

            double firstPrice = 0.0;//首购金额
            Set<Integer> firstPaySet = new HashSet<Integer>();//首购成交客户个数 去重

            double totalPrice = 0.0;
            Set<Integer> totalPaySet = new HashSet<Integer>();//总成交客户个数 去重

            double nowTotalPrice = 0.0; //当月领取当周成交额

            double preTotalPrice = 0.0; //前月领取当周成交额

            double nonNowTotalPrice = 0.0; //非当月领取当周成交额

            /*  searchArgB = new SearchArg();
            searchArgB.matcher = new ParamMatcher("salesAcct", ParamMatcher.EQ, sacct);
            searchArgB.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.UN_CONTACT_30DAY, PreSaleHdDef.Flag.UN_CONTACT_30DAY);
            FaiList<String> fieldList = new FaiList<String>();
            fieldList.add("aid");
            FaiList<Param> preSaleB=sysPreSaleHd.getList(fieldList,searchArgB);//排除该销售B库资源
            HashSet<Integer> aidSetB=new HashSet(); */
            /* for(Param p : preSaleB){
            	aidSetB.add(p.getInt("aid",0));
            }  */

            //Log.logStd("li test aidSetB=%s,sacct=%s",aidSetB,sacct);
            int orderCount = 0;//成单数
            //客户购买大于380，并且一个用户最多只能成一单,去重
            HashSet<Integer> orderCountSet = new HashSet();
            // 统计成交总金额以及成交客户个数
            for (Param order : personOrderList) {
                Double onePrice = PreSaleHdDef.getPrice(order.getDouble(BssStatDef.OrderItemInfo.PRICE, 0.0), order.getInt(BssStatDef.OrderItemInfo.PRODUCT_ID, -1));//订单金额
                if (onePrice <= 0) {
                    continue;
                }
                int aid = order.getInt("aid", 0);
                
               /*  if(localMonthReceive.containsKey(aid+"") || preMonthReceive.containsKey(aid+"") || preMonthReceive.containsKey(aid+"")){
                }else{
                	 continue;
                } */
                if (aidSetB.contains(aid)) {//如果该资源是B库资源，则不计算在成交金额
                    continue;
                }
                //大于380
                if (onePrice > 380) {
                    orderCountSet.add(aid);
                }

                int payTime = order.getInt("payTime", 0);
                int payType = order.getInt(BssStatDef.OrderItemInfo.PAY_TYPE, 0);
                if (payType == FaiOrderDef.PayType.REPEAT) {//重购
                    repeatPrice += onePrice;
                    repeatPaySet.add(aid);
                } else if (payType == FaiOrderDef.PayType.FIRST) {//首购
                    firstPrice += onePrice;
                    firstPaySet.add(aid);
                }
                totalPrice += onePrice;
                totalPaySet.add(aid);

                if (begT < payTime && payTime < endT) {//支付时间是选定时间。即选定日期。
                    if (localMonthReceive.getInt("" + aid, 0) == 1) {//领取时间是当月/周 成交额
                        nowTotalPrice += onePrice;
                    }
                    if (preMonthReceive.getInt("" + aid, 0) == 1) {//领取时间是前月
                        preTotalPrice += onePrice;
                    }
                    if (nonMonthReceive.getInt("" + aid, 0) == 1) {//领取时间是非当月/周 成交额
                        nonNowTotalPrice += onePrice;
                    }
                }
            }

            orderCount = orderCountSet.size();
            info.setInt("orderCount", orderCount); //成单数
            info.setDouble("repeatPrice", Num.round(repeatPrice, 2));
            info.setInt("repeatNum", repeatPaySet.size());

            info.setDouble("firstPrice", Num.round(firstPrice, 2));
            info.setInt("firstNum", firstPaySet.size());

            info.setDouble("totalPrice", Num.round(totalPrice, 2));
            info.setInt("totalNum", totalPaySet.size());
            info.setInt("personCount", localMonthReceive.size());
            info.setDouble("nowTotalPrice", Num.ceil(nowTotalPrice, 2));


            int notDeal = prePerson.getInt(sacct + "-notDealCount", 0);
            info.setInt("prePersonCount", notDeal);
            info.setDouble("preTotalPrice", Num.round(preTotalPrice, 2));

            notDeal = nonNowPerson.getInt(sacct + "-notDealCount", 0);//非当月资源
            info.setInt("nonNowPersonCount", notDeal);
            info.setDouble("nonNowTotalPrice", Num.round(nonNowTotalPrice, 2));


            //最终数据计算
            int personCount = info.getInt("personCount", 0);
            if (personCount > 0) {
                info.setDouble("arpu", Num.round(nowTotalPrice / personCount, 2));
            } else {
                info.setDouble("arpu", 0.0);
            }
            info.setInt("nowAndPrePersonCount", info.getInt("personCount", 0) + info.getInt("prePersonCount", 0));
            info.setDouble("nowAndPreTotalPrice", Num.calculate(info.getDouble("nowTotalPrice", 0.0), info.getDouble("preTotalPrice", 0.0), Num.CalculateType.PLUS, 0));

            int prePersonCount = info.getInt("prePersonCount", 0);
            if (prePersonCount > 0) {
                info.setDouble("preArpu", Num.round(preTotalPrice / prePersonCount, 2));
            } else {
                info.setDouble("preArpu", 0.0);
            }

            //info.setDouble("nowAndPreArpu", Num.calculate(info.getDouble("arpu", 0.0), info.getDouble("preArpu", 0.0), Num.CalculateType.PLUS, 0));

            //unique-2018/10/16: 当月和前月arpu计算公式改为：（当月资源当月业绩+前月资源当月业绩）/（当月资源数+前月资源数），已废弃
            int nowAndPrePersonCount = info.getInt("nowAndPrePersonCount", 0);
            if (nowAndPrePersonCount > 0) {
                info.setDouble("nowAndPreArpu", Num.calculate(info.getDouble("nowAndPreTotalPrice", 0.0), nowAndPrePersonCount, Num.CalculateType.EXCEPT, 2));
            } else {
                info.setDouble("nowAndPreArpu", 0.0);
            }

            //非当月arpu
            int nonNowPersonCount = info.getInt("nonNowPersonCount", 0);
            if (nonNowPersonCount > 0) {
                info.setDouble("nonNowArpu", Num.round(nonNowTotalPrice / nonNowPersonCount, 2));
            } else {
                info.setDouble("nonNowArpu", 0.0);
            }

            //总arpu
            if (personCount != 0 || nonNowPersonCount != 0) {
                double tempTotlePrice = nowTotalPrice + nonNowTotalPrice;
                int tempTotalCount = personCount + nonNowPersonCount;
                info.setDouble("arpuTotal", Num.round(tempTotlePrice / tempTotalCount, 2));
            } else {
                info.setDouble("arpuTotal", 0.0);
            }


            //平均总arpu
            if (personCount != 0) {
                double tempTotlePrice = nowTotalPrice + nonNowTotalPrice;//当月+非当月领取的资源在当月成交的金额
                info.setDouble("averageArpuTotal", Num.round(tempTotlePrice / personCount, 2));
            } else {
                info.setDouble("averageArpuTotal", 0.0);
            }

            statisList.add(info);
        }
        //dataParam.setList("statisList",statisList);
        return statisList;
    }

    //月份统计数据
    private static String getMonthMeritStatisticsList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口

        /******************** 读取配置文件，获取售后信息 ********************/
        Param ossConf = Web.getConf("oss");
        //FaiList<Integer> preSales = WebOss.getAuthSidList("authHDSale");	// 只拿互动销售的
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> preSales = new FaiList<Integer>();                        // 直销互动销售列表
        Param saleInfoParam = new Param();
        for (Param item : infoList) {
            preSales.add(item.getInt("sid"));
            saleInfoParam.setInt(item.getInt("sid") + "", item.getInt("status"));
        }

        /******************** 月份排名参数 ********************/
        int sort = Parser.parseInt(request.getParameter("sort"), 0);
        String begDate = "";                                                                            // 开始日期
        String endDate = "";                                                                            // 结束日期
        Calendar cal = Calendar.getInstance();
        int yearNow = cal.get(Calendar.YEAR);                                                            // 获取当前年
        int monthNow = cal.get(Calendar.MONDAY) + 1;                                                     // 获取当前月
        int year = Parser.parseInt(request.getParameter("year"), yearNow);                               // 年
        int month = Parser.parseInt(request.getParameter("month"), monthNow);                            // 月
        // 设置年月日，月份是从0开始算起的
        cal.set(year, month - 1, 1);
        begDate = Parser.parseString(cal, "yyyy-MM-dd");
        // 设置结束日期为当前月份的最后一天
        cal.set(Calendar.DATE, cal.getActualMaximum(Calendar.DATE));
        endDate = Parser.parseString(cal, "yyyy-MM-dd");

        // acctOrderItem表里是int类型，将时间转换为int去查询
        int begDateInt = (int) (Parser.parseCalendar(begDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int endDateInt = (int) (Parser.parseCalendar(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);

        //Param dataParam= getMeritList(preSales, begDateInt, endDateInt, true);               

        FaiList<Param> statisList = getMeritList(preSales, begDateInt, endDateInt, true);  // 当月统计
        //Param oldAndNewParam = dataParam.getParam("oldAndNewParam");

        /******************** 数据根据条件排序(主要是为了做排名) ********************/
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("name", ParamMatcher.NE, "");
        switch (sort) {
            case 0:
                searchArg.cmpor = new ParamComparator("totalPrice", true);
                break;//根据总金额排序
            case 1:
                searchArg.cmpor = new ParamComparator("firstPrice", true);
                break;//根据首购排序
            case 2:
                searchArg.cmpor = new ParamComparator("repeatPrice", true);
                break;//根据重构金额排序
            case 3:
                searchArg.cmpor = new ParamComparator("arpu", true);
                break;                //根据 当arpu 排序
            case 4:
                searchArg.cmpor = new ParamComparator("preArpu", true);
                break;            //根据 前arpu 排序
            case 5:
                searchArg.cmpor = new ParamComparator("nowAndPreArpu", true);
                break;    //根据 当+前arpu 排序
            case 6:
                searchArg.cmpor = new ParamComparator("nonNowArpu", true);
                break;        //根据 非当arpu 排序
            case 7:
                searchArg.cmpor = new ParamComparator("arpuTotal", true);
                break;
        }
        Searcher searcher = new Searcher(searchArg);
        statisList = searcher.getParamList(statisList);

        //增加销售组(不包含未转正员工)和虚拟销售组

        Param virtualSaleGroup = new Param();
        virtualSaleGroup.setString("name", "虚拟销售组");

        Param realSaleGroup = new Param();
        realSaleGroup.setString("name", "销售组");
        Param realSaleGroup_1 = new Param();
        realSaleGroup_1.setString("name", "销售1组");
        Param realSaleGroup_2 = new Param();
        realSaleGroup_2.setString("name", "销售2组");
        Param realSaleGroup_3 = new Param();
        realSaleGroup_3.setString("name", "销售3组");

        Log.logStd("unique-test:zzzz  =  ");
        FaiList<Integer> sidGroup_1 = WebOss.getAuthSidList("authHDSale1");
        Log.logStd("unique-test: sidGroup_1 size =  %d", sidGroup_1.size());
        FaiList<Integer> sidGroup_2 = WebOss.getAuthSidList("authHDSale2");
        FaiList<Integer> sidGroup_3 = WebOss.getAuthSidList("authHDSale3");
        HashMap<Integer, Integer> sidGroupMap = new HashMap<Integer, Integer>();
        for (Integer i : sidGroup_1) {
            sidGroupMap.put(i, 1);
        }
        for (Integer i : sidGroup_2) {
            sidGroupMap.put(i, 2);
        }
        for (Integer i : sidGroup_3) {
            sidGroupMap.put(i, 3);
        }

        for (Param p : statisList) {
            int sid = p.getInt("sid", 0);
            int group = 0;
            if (sidGroupMap.containsKey(sid)) {
                group = sidGroupMap.get(sid);
            }
            if (p.getString("name", "").contains("虚拟")) {
                addVirtualandRealGroup(p, virtualSaleGroup);
            } else if (saleInfoParam.getInt(sid + "", 0) == 1) {//销售组
                addVirtualandRealGroup(p, realSaleGroup);
                if (group == 1) {
                    addVirtualandRealGroup(p, realSaleGroup_1);
                } else if (group == 2) {
                    addVirtualandRealGroup(p, realSaleGroup_2);
                } else if (group == 3) {
                    addVirtualandRealGroup(p, realSaleGroup_3);
                }
            }
        }

        //计算arpu值
        calculateArpu(virtualSaleGroup);
        calculateArpu(realSaleGroup);
        calculateArpu(realSaleGroup_1);
        calculateArpu(realSaleGroup_2);
        calculateArpu(realSaleGroup_3);

        statisList.add(realSaleGroup_1);
        statisList.add(realSaleGroup_2);
        statisList.add(realSaleGroup_3);
        statisList.add(realSaleGroup);
        statisList.add(virtualSaleGroup);

        boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);            // 导出Excel标志
        if (excel) {
            response.setContentType("application/x-excel");
            //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            //response.setHeader("Content-Disposition", "attachment;filename=" + new String((begDate+"-"+endDate+"业绩统计.xls").getBytes("GBK"), "ISO-8859-1"));
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((begDate + "至" + endDate + "业绩统计.xls").getBytes("GBK"), "ISO-8859-1"));
            out.clear();//必须得加
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();

            cellKey.setString("name", "咨询师");
            cellKey.setString("firstPrice", "首购金额");
            cellKey.setString("firstNum", "首购人数");
            cellKey.setString("repeatPrice", "重购金额");
            cellKey.setString("repeatNum", "重购人数");
            cellKey.setString("orderCount", "成单数");
            cellKey.setString("totalNum", "总人数");
            cellKey.setString("totalPrice", "总金额");

            cellKey.setString("nowTotalPrice", "当月领取成交额");
            cellKey.setString("personCount", "当月领取数");
            cellKey.setString("arpu", "当月arpu");

            //cellKey.setString("averageArpuTotal", "当月平均总arpu");
            cellKey.setString("preTotalPrice", "前月领取成交额");
            cellKey.setString("prePersonCount", "前月领取数");
            cellKey.setString("preArpu", "前月arpu");

            cellKey.setString("nowAndPreTotalPrice", "前月和当月领取成交额");
            cellKey.setString("nowAndPrePersonCount", "前月和当月领取数");
            cellKey.setString("nowAndPreArpu", "前月和当月arpu");

            cellKey.setString("nonNowTotalPrice", "非当月领取成交额");
            cellKey.setString("nonNowPersonCount", "非当月领取数");
            cellKey.setString("nonNowArpu", "非当月arpu");

            cellKey.setString("arpuTotal", "本月总arpu");
            OssPoi.exportExcel(cellKey, statisList, outputStream);

            return "{\"success\":true}";
        }
        String oldAndNewUrl = "oldAndNewAcct.jsp?begTime=" + begDateInt + "&endTime=" + endDateInt + "&isMonth=" + true;

        return "{\"success\":true, \"dataList\":" + statisList + ", \"total\":" + statisList.size() + ", \"oldAndNewUrl\":\"" + oldAndNewUrl + "\"}";
    }

    private static void calculateArpu(Param group) {
        //虚拟销售组当月arpu
        if (group.getInt("personCount", 0) == 0) {
            group.setDouble("arpu", 0.0);
        } else {
            group.setDouble("arpu", Num.ceil(group.getDouble("nowTotalPrice", 0.0) / group.getInt("personCount"), 2));
        }
        //虚拟销售组前月arpu
        if (group.getInt("prePersonCount", 0) == 0) {
            group.setDouble("preArpu", 0.0);
        } else {
            group.setDouble("preArpu", Num.ceil(group.getDouble("preTotalPrice", 0.0) / group.getInt("prePersonCount"), 2));
        }
        //虚拟销售组前月和当月arpu
        if (group.getInt("nowAndPrePersonCount", 0) == 0) {
            group.setDouble("nowAndPreArpu", 0.0);
        } else {
            group.setDouble("nowAndPreArpu", Num.calculate(group.getDouble("nowAndPreTotalPrice", 0.0), group.getInt("nowAndPrePersonCount"), Num.CalculateType.EXCEPT, 2));
        }
        //虚拟销售组非当月arpu
        if (group.getInt("nonNowPersonCount", 0) == 0) {
            group.setDouble("nonNowArpu", 0.0);
        } else {
            group.setDouble("nonNowArpu", Num.ceil(group.getDouble("nonNowTotalPrice", 0.0) / group.getInt("nonNowPersonCount"), 2));
        }
        //虚拟销售组当月总arpu
        if (group.getInt("nonNowPersonCount", 0) != 0 || group.getInt("personCount", 0) != 0) {
            double tempTotalPrice = group.getDouble("nowTotalPrice", 0.0) + group.getDouble("nonNowTotalPrice", 0.0);
            int tempTotalCount = group.getInt("personCount") + group.getInt("nonNowPersonCount");
            group.setDouble("arpuTotal", Num.ceil(tempTotalPrice / tempTotalCount, 2));
        } else {
            group.setDouble("arpuTotal", 0.0);
        }
    }

    //周期统计数据
    private static String getWeekMeritStatisticsList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);            // bss 的接口
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presaleHd 的接口

        /******************** 读取配置文件，获取售后信息 ********************/
        Param ossConf = Web.getConf("oss");
        //FaiList<Integer> preSales = WebOss.getAuthSidList("authHDSale");	// 只拿互动销售的
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        Param saleInfoParam = new Param();
        FaiList<Integer> preSales = new FaiList<Integer>();                        // 直销互动销售列表
        for (Param item : infoList) {
            preSales.add(item.getInt("sid"));
            saleInfoParam.setInt(item.getInt("sid") + "", item.getInt("status"));
        }
        /******************** 周期排名参数 ********************/
        int sort = Parser.parseInt(request.getParameter("sort"), 0);
        //int maxCnt = Parser.parseInt(request.getParameter("rank"), 7);
        String periodBegDate = "";                                                                        // 开始日期
        String periodEndDate = "";                                                                        // 结束日期

        if (request.getParameter("periodDateBeg") == null || request.getParameter("periodDateEnd") == null) {
            Calendar cal2 = Calendar.getInstance();
            //当天星期几(-2是因为在设置时间的时候需要用到，如果获取当前是星期几应该-1)
            cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) - (cal2.get(Calendar.DAY_OF_WEEK) - 2));
            periodBegDate = Parser.parseSimpleTime(cal2);
            cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) + 6);
            periodEndDate = Parser.parseSimpleTime(cal2);
        } else {
            periodBegDate = request.getParameter("periodDateBeg");
            periodEndDate = request.getParameter("periodDateEnd");

            /******************** 设置上一周下一周 ********************/
            int weekDay = Parser.parseInt(request.getParameter("weekDay"), 0);
            if (weekDay == 1) {
                Calendar cal2 = Parser.parseCalendar(periodBegDate, "yyyy-MM-dd");
                cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) - 7);
                periodBegDate = Parser.parseSimpleTime(cal2);
                cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) + 6);
                periodEndDate = Parser.parseSimpleTime(cal2);
            } else if (weekDay == 2) {
                Calendar cal2 = Parser.parseCalendar(periodBegDate, "yyyy-MM-dd");
                cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) + 7);
                periodBegDate = Parser.parseSimpleTime(cal2);
                cal2.set(Calendar.DATE, cal2.get(Calendar.DATE) + 6);
                periodEndDate = Parser.parseSimpleTime(cal2);
            }
        }

        // acctOrderItem表里是int类型，将时间转换为int去查询
        int periodBegDateInt = (int) (Parser.parseCalendar(periodBegDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);
        int periodEndDateInt = (int) (Parser.parseCalendar(periodEndDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000);


        //Param dataParam = getMeritList(preSales, periodBegDateInt, periodEndDateInt, false);                    //当周
        FaiList<Param> statisList2 = getMeritList(preSales, periodBegDateInt, periodEndDateInt, false);                    //当周
        //Param oldAndNewParam = dataParam.getParam("oldAndNewParam");


        /******************** 数据根据条件排序(主要是为了做排名) ********************/
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("name", ParamMatcher.NE, "");
        switch (sort) {
            case 0:
                searchArg.cmpor = new ParamComparator("totalPrice", true);
                break;//根据总金额排序
            case 1:
                searchArg.cmpor = new ParamComparator("firstPrice", true);
                break;//根据首购排序
            case 2:
                searchArg.cmpor = new ParamComparator("repeatPrice", true);
                break;//根据重构金额排序
            case 3:
                searchArg.cmpor = new ParamComparator("arpu", true);
                break;                //根据 当arpu 排序
            case 4:
                searchArg.cmpor = new ParamComparator("preArpu", true);
                break;            //根据 前arpu 排序
            case 5:
                searchArg.cmpor = new ParamComparator("nowAndPreArpu", true);
                break;    //根据 当+前arpu 排序
            case 6:
                searchArg.cmpor = new ParamComparator("nonNowArpu", true);
                break;        //根据 非当arpu 排序
            case 7:
                searchArg.cmpor = new ParamComparator("arpuTotal", true);
                break;
        }
        //searchArg.limit = maxCnt;
        Searcher searcher = new Searcher(searchArg);
        statisList2 = searcher.getParamList(statisList2);

        //增加销售组(不包含未转正员工)和虚拟销售组

        Param virtualSaleGroup = new Param();
        virtualSaleGroup.setString("name", "虚拟销售组");

        Param realSaleGroup = new Param();
        realSaleGroup.setString("name", "销售组");

        for (Param p : statisList2) {
            if (p.getString("name", "").contains("虚拟")) {
                addVirtualandRealGroup(p, virtualSaleGroup);
            } else if (saleInfoParam.getInt(p.getInt("sid", -1) + "", -1) == 1) {
                addVirtualandRealGroup(p, realSaleGroup);
            }
        }
        //计算arpu值
        calculateArpu(virtualSaleGroup);
        calculateArpu(realSaleGroup);

        statisList2.add(realSaleGroup);
        statisList2.add(virtualSaleGroup);


        boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);            // 导出Excel标志
        if (excel) {
            response.setContentType("application/x-excel");
            //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((periodBegDate + "至" + periodEndDate + "业绩统计.xls").getBytes("GBK"), "ISO-8859-1"));
            out.clear();//必须得加
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();

            cellKey.setString("name", "咨询师");
            cellKey.setString("firstPrice", "首购金额");
            cellKey.setString("firstNum", "首购人数");
            cellKey.setString("repeatPrice", "重购金额");
            cellKey.setString("repeatNum", "重购人数");
            cellKey.setString("orderCount", "成单数");
            cellKey.setString("totalNum", "总人数");
            cellKey.setString("totalPrice", "总金额");

            cellKey.setString("nowTotalPrice", "当周领取成交额");
            cellKey.setString("personCount", "当周领取数");
            cellKey.setString("arpu", "当周arpu");
            cellKey.setString("preTotalPrice", "前周领取成交额");
            cellKey.setString("prePersonCount", "前周领取数");
            cellKey.setString("preArpu", "前周arpu");

            cellKey.setString("nowAndPreTotalPrice", "前周和当周领取成交额");
            cellKey.setString("nowAndPrePersonCount", "前周和当周领取数");
            cellKey.setString("nowAndPreArpu", "前周和当周arpu");

            cellKey.setString("nonNowTotalPrice", "非当周领取成交额");
            cellKey.setString("nonNowPersonCount", "非当周领取数");
            cellKey.setString("nonNowArpu", "非当周arpu");
            cellKey.setString("arpuTotal", "本周总arpu");
            OssPoi.exportExcel(cellKey, statisList2, outputStream);

            return "{\"success\":true}";
        }

        String oldAndNewUrl = "oldAndNewAcct.jsp?begTime=" + periodBegDateInt + "&endTime=" + periodEndDateInt + "&isMonth=" + false;

        Log.logStd(" li test oldAndNewUrl=%s", oldAndNewUrl);
        return "{\"success\":true, \"dataList\":" + statisList2 + ", \"total\":" + statisList2.size() + ", \"preSalesCnt\":" + preSales.size() + ", \"oldAndNewUrl\":\"" + oldAndNewUrl + "\"}";
    }

    private static void addVirtualandRealGroup(Param p, Param groupParam) {
        double g_FirstPrice = groupParam.getDouble("firstPrice", 0.0);//首购金额
        double firstPrice = p.getDouble("firstPrice", 0.0);//首购金额
        g_FirstPrice += firstPrice;
        groupParam.setDouble("firstPrice", Num.ceil(g_FirstPrice, 2));

        int g_FirstNum = groupParam.getInt("firstNum", 0);//首购人数
        int firstNum = p.getInt("firstNum", 0);//首购人数
        g_FirstNum += firstNum;
        groupParam.setInt("firstNum", g_FirstNum);

        double g_RepeatPrice = groupParam.getDouble("repeatPrice", 0.0);//重购金额
        double repeatPrice = p.getDouble("repeatPrice", 0.0);//重购金额
        g_RepeatPrice += repeatPrice;
        groupParam.setDouble("repeatPrice", Num.ceil(g_RepeatPrice, 2));

        int g_RepeatNum = groupParam.getInt("repeatNum", 0);//重购人数
        int repeatNum = p.getInt("repeatNum", 0);//重购人数
        g_RepeatNum += repeatNum;
        groupParam.setInt("repeatNum", g_RepeatNum);

        int g_OrderCount = groupParam.getInt("orderCount", 0);//成单数
        int orderCount = p.getInt("orderCount", 0);//成单数
        g_OrderCount += orderCount;
        groupParam.setInt("orderCount", g_OrderCount);

        int g_TotalNum = groupParam.getInt("totalNum", 0);//总人数
        int totalNum = p.getInt("totalNum", 0);//总人数
        g_TotalNum += totalNum;
        groupParam.setInt("totalNum", g_TotalNum);

        double g_TotalPrice = groupParam.getDouble("totalPrice", 0.0);//总金额
        double totalPrice = p.getDouble("totalPrice", 0.0);//总金额
        g_TotalPrice += totalPrice;
        groupParam.setDouble("totalPrice", Num.ceil(g_TotalPrice, 2));

        int g_PersonCount = groupParam.getInt("personCount", 0);//个人库数量
        int personCount = p.getInt("personCount", 0);//总人数
        g_PersonCount += personCount;
        groupParam.setInt("personCount", g_PersonCount);

        double g_NowTotalPrice = groupParam.getDouble("nowTotalPrice", 0.0);//当月成交金额
        double nowTotalPrice = p.getDouble("nowTotalPrice", 0.0);//当月成交金额
        g_NowTotalPrice += nowTotalPrice;
        groupParam.setDouble("nowTotalPrice", Num.round(g_NowTotalPrice, 2));

        int g_PrePersonCount = groupParam.getInt("prePersonCount", 0);//前月个人库数量
        int prePersonCount = p.getInt("prePersonCount", 0);//前月个人库数量
        g_PrePersonCount += prePersonCount;
        groupParam.setInt("prePersonCount", g_PrePersonCount);

        double g_PreTotalPrice = groupParam.getDouble("preTotalPrice", 0.0);//前月成交金额
        double preTotalPrice = p.getDouble("preTotalPrice", 0.0);//前月成交金额
        g_PreTotalPrice += preTotalPrice;
        groupParam.setDouble("preTotalPrice", Num.round(g_PreTotalPrice, 2));

        int g_NonNowPersonCount = groupParam.getInt("nonNowPersonCount", 0);//非当月个人库数量
        int nonNowPersonCount = p.getInt("nonNowPersonCount", 0);//非当月个人库数量
        g_NonNowPersonCount += nonNowPersonCount;
        groupParam.setInt("nonNowPersonCount", g_NonNowPersonCount);

        double g_NonNowTotalPrice = groupParam.getDouble("nonNowTotalPrice", 0.0);//非当月成交金额
        double nonNowTotalPrice = p.getDouble("nonNowTotalPrice", 0.0);//非当月成交金额
        g_NonNowTotalPrice += nonNowTotalPrice;
        groupParam.setDouble("nonNowTotalPrice", Num.round(g_NonNowTotalPrice, 2));

        int v_NowAndPrePersonCount = g_PersonCount + g_PrePersonCount;//当月和前月个人库
        groupParam.setInt("nowAndPrePersonCount", v_NowAndPrePersonCount);

        Double v_NowAndPreTotalPrice = g_NowTotalPrice + g_PreTotalPrice;//当月和前月成交额
        groupParam.setDouble("nowAndPreTotalPrice", Num.round(v_NowAndPreTotalPrice, 2));


    }
/** meritStatistics.jsp end **/


    /** logRecord.jsp start **/
    private static String getLogRecordList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);                            // adm
        boolean leader = Auth.checkFaiscoAuth("authPreSaleLeader", false);                // 直销售前-组长
        boolean isManager = Auth.checkFaiscoAuth("authHDSaleManage", false);
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);
        // 调用oss配置文件拿到直销售前列表;电话组的组长只能看到他们组的领取记录
        //FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");				// 互动销售列表
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> sidList = new FaiList<Integer>();                        // 直销互动销售列表
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
        }
        // 查询调整时间
        boolean setOptTimeFlag = Parser.parseBoolean(request.getParameter("setOptTimeFlag"), false);
        boolean exportNew = Parser.parseBoolean(request.getParameter("new"), false);//废弃 权重导出
        if (setOptTimeFlag) {
            FaiList<Integer> tempSidList = WebOss.getAuthSidList("authAdm");
            sidList = tempSidList.clone();
            tempSidList = WebOss.getAuthSidList("authHDSaleManage");                            // 互动销售组长
            sidList.addAll(tempSidList);
        }


        // 获取员工信息
        FaiList<Param> staffList = new FaiList<Param>();
        FaiList<String> sacctList = new FaiList<String>();
        for (int sid : sidList) {
            Param staff = Acct.getStaffInfo(Web.getFaiCid(), sid);
            if (staff == null) {
                continue;
            }
            staffList.add(staff);
            String sacct = staff.getString(StaffDef.Info.SACCT, "");
            sacctList.add(sacct);
        }

        /* 分页参数 */
        int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = Parser.parseInt(request.getParameter("limit"), 10);
        int start = (pageNo - 1) * limit;
        int total = 0;

        //Url.PageUrl pageUrl = new Url.PageUrl("pageNo");
        //String url = pageUrl.url;

        /* 请求参数 */
        int se_aid = Parser.parseInt(request.getParameter("aid"), 0);
        int se_attrType = Parser.parseInt(request.getParameter("attrType"), -1);                        //类型
        boolean exportExcel = Parser.parseBoolean(request.getParameter("exportFlag"), false);           // 导出判断
        String se_sacct = Parser.parseString(request.getParameter("staff_sacct"), "all");               // 操作人
        boolean isOptTime = Parser.parseBoolean(request.getParameter("optDateFlag"), false);            // 是否查询领取时间
        boolean isCreateTime = Parser.parseBoolean(request.getParameter("creDateFlag"), false);         // 是否查询创建时间
        int tag = Parser.parseInt(request.getParameter("tag"), -1);                                     // 资源标签
        int ta = Parser.parseInt(request.getParameter("ta"), -1);                                       // 注册来源
        // 设置时间
        Calendar clr1 = Calendar.getInstance();
        //创建时间
        String sbegDate = Parser.parseString(request.getParameter("creDateBeg"), Parser.parseSimpleTime(clr1));
        String sendDate = Parser.parseString(request.getParameter("creDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));
        // 客户领取痕迹-领取时间
        String optBegTime = Parser.parseString(request.getParameter("optDateBeg"), Parser.parseSimpleTime(clr1));
        String optEndTime = Parser.parseString(request.getParameter("optDateEnd"), Parser.parseSimpleTime(Calendar.getInstance()));

        // 查询售前领取记录
        SearchArg searchArg = new SearchArg();

        // 条件
        searchArg.matcher = new ParamMatcher();

        if (isCreateTime) {
            searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.GE, sbegDate + " 00:00:00");
            searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME, ParamMatcher.LE, sendDate + " 23:59:59");
        }
        if (isOptTime) {
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, optBegTime + " 00:00:00");
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, optEndTime + " 23:59:59");
        }
        if (sacctList.size() > 0) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
        }

        if (se_aid != 0) {
            searchArg.matcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.EQ, se_aid);
        }
        //searchArg.matcher.and(PreSaleHdDef.Info.FLAG,ParamMatcher.LAND_NE,PreSaleHdDef.Flag.TEST_ALLOT,PreSaleHdDef.Flag.TEST_ALLOT);

        if (!se_sacct.equals("all")) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.EQ, se_sacct);
        }
        if (se_attrType != -1) {
            if (se_attrType == HdSaleRecordDef.AttrType.RELEASE) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getReleaseList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.DROP) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getDropList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.GET) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getInitList());
            } else if (se_attrType == HdSaleRecordDef.AttrType.ALLOT) {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, HdSaleRecordDef.getAllotList());
            } else {
                searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, se_attrType);
            }
        }
        if (exportNew) {
            searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, "2018-08-29 00:00:00");
        }
        if (tag != -1) {
        	if(tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
        		 FaiList<Integer> tagTmpList = new FaiList<Integer>();
        		 tagTmpList.add(72);
        		 tagTmpList.add(52);
        		 searchArg.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.NOT_IN, tagTmpList);
        	}else{
        		searchArg.matcher.and(PreSaleHdDef.Info.TAG, ParamMatcher.EQ, tag);
        	}
        }
        if (ta != -1 || tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC) {
            FaiList<Param> taList = getTaList();
            FaiList<Integer> trackList = PreSaleHdDef.getTaList(taList, ta);
            if (ta != PreSaleHdDef.PreSaleTa.NOT_IN) {
                if(tag == PreSaleHdDef.Tag.ACTIVITY_NOT_BACK_PC){
                	trackList =PreSaleHdDef.getTaList(taList, 11);
                }
                if (ta == PreSaleHdDef.PreSaleTa.MOBI) {
                    searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, 0);
                }
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.IN, trackList);
                Log.logStd("unique-test ta list = %s", trackList);
            } else {
                //li.未知查询，剔除掉移动放量的ta。
                trackList.add(491);
                trackList.add(493);
                trackList.add(2344);
                searchArg.matcher.and(BssStatDef.Info.TA, ParamMatcher.NOT_IN, trackList);
            }
        }
        // 调整领取时间
        if (setOptTimeFlag) {
            searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.EQ, HdSaleRecordDef.AttrType.CREATETIME_CLIENT);
        }

        searchArg.start = start;
        searchArg.limit = limit;
        searchArg.totalSize = new fai.comm.util.Ref<Integer>();
        searchArg.cmpor = new ParamComparator(HdSaleRecordDef.Info.CREATE_TIME, true);

        // 导出的话,limit放大点
        if (exportExcel) {
            searchArg.start = 0;
            searchArg.limit = 10000;
        }


        FaiList<Param> actionList = new FaiList<Param>();
        Dao ossDao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "*";
            if (exportNew) {
                selectArg.table = "hdSaleRecord";
            } else {
                selectArg.table = "hdSaleRecord";
            }
            selectArg.searchArg = searchArg;
            actionList = ossDao.select(selectArg);
        } finally {
            ossDao.close();
        }
        total = searchArg.totalSize.value;
        /* 数据翻译 */
        FaiList<Integer> taAidList = new FaiList<Integer>();
        Map<Integer, String> tagMap = getTagMap();
        for (Param p : actionList) {
            String p_sacct = p.getString(HdSaleRecordDef.Info.SACCT, "");
            int attrType = p.getInt(HdSaleRecordDef.Info.ATTR_TYPE, 0);
            String attrTypeContent = HdSaleRecordDef.getAttrTypeName(attrType);
            //Log.logStd("li test attrTypeContent=%s", attrTypeContent);
            String type = attrType != HdSaleRecordDef.AttrType.GET_EMAIL ? "客户" : "邮箱";
            Param tmpSalesAcct = Acct.getStaffInfo(Web.getFaiCid(), p_sacct);
            String p_name = tmpSalesAcct.getString(StaffDef.Info.NAME, "");
            int tagItem = p.getInt("tag", 0);
            String tagName = tagMap.get(tagItem);
            String createTime = Parser.parseString(p.getCalendar(HdSaleRecordDef.Info.CREATE_TIME));
            String receiveTime = Parser.parseString(p.getCalendar(HdSaleRecordDef.Info.RECEIVE_TIME));
            if (tagName == null) {
                tagName = "";
            }
            p.setString("tagName", tagName);
            p.setString("sacctName", p_name);
            p.setString("attrTypeContent", attrTypeContent);
            p.setString("type", type);
            p.setString("createTime", createTime);
            p.setString("receiveTime", receiveTime);
            int flag = p.getInt(HdSaleRecordDef.Info.FLAG, 0);
            if (Misc.checkBit(flag, HdSaleRecordDef.Flag.IS_AWARD)) {
                p.setString("isAward", "是");
            } else {
                p.setString("isAward", "否");
            }

            if (Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_A)) {
                p.setString("allotType", "A");
            } else if (Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_B)) {
                p.setString("allotType", "B");
            } else if (Misc.checkBit(flag, HdSaleRecordDef.Flag.AVERAGE_ALLOT)) {
                p.setString("allotType", "average");
            } else {
                p.setString("allotType", "未知");
            }

            if (p.getString("taGroupName", "").isEmpty()) {//如果没有注册来源组数据，后面加的，旧数据没有
                taAidList.add(p.getInt("aid"));
            }
        }
        if (taAidList.size() > 0) {
            FaiList<Param> taInfoList = null;
            FaiList<Param> taList = null;
            Dao bssMainDao = WebOss.getBssMainDaoMaster();
            try {
                Dao.SelectArg selectArg = new Dao.SelectArg();
                selectArg.field = "aid,ta";//再查aid，用来查创建游戏数
                selectArg.table = "acctStatus";
                SearchArg searchArgAid = new SearchArg();
                searchArgAid.matcher = new ParamMatcher("aid", ParamMatcher.IN, taAidList);
                selectArg.searchArg = searchArgAid;//matcher 一样
                taInfoList = bssMainDao.select(selectArg);
                taList = bssMainDao.executeQuery("select * from ta");
            } catch (Exception e) {
                Log.logErr(e);
            } finally {
                bssMainDao.close();
            }
            Param taParam = new Param(true);
            for (Param taInfo : taList) {
                String taKey = String.valueOf(taInfo.getInt("ta", 0));
                taParam.setParam(taKey, taInfo);
            }
            for (Param acctInfo : taInfoList) {
                String taKey = String.valueOf(acctInfo.getInt("ta", 0));
                String aidKey = String.valueOf(acctInfo.getInt("aid", 0));
                taParam.setParam(aidKey, taParam.getParam(taKey));
            }
            for (Param allotInfo : actionList) {
                if (allotInfo.getString("taGroupName", "").isEmpty()) {  //如果没有注册来源组数据，后面加的，旧数据没有
                    String aidKey = String.valueOf(allotInfo.getInt("aid"));
                    Param taInfo = taParam.getParam(aidKey);
                    if (taInfo != null) {
                        int innerTa = taInfo.getInt("ta", 0);
                        int groupId = taInfo.getInt("groupId", 0);
                        String taGroupName = PreSaleHdDef.getTaNameByTaAndGroupId(groupId, innerTa);
                        allotInfo.setInt("ta",innerTa);
                        allotInfo.setString("taGroupName", taGroupName);

                    }
                }
            }
        }


        // 导出Excel
        if (exportExcel) {
            response.setContentType("application/x-excel");
            // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("客户领取数据列表.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString("aid", "aid");
            cellKey.setString("createTime", "创建时间");
            cellKey.setString("receiveTime", "领取or释放时间");
            cellKey.setString("sacctName", "账号");
            cellKey.setString("attrTypeContent", "领取类型");
            cellKey.setString("tagName", "资源标签");
            cellKey.setString("type", "领取对象");
            cellKey.setString("action", "内容");
            cellKey.setString("isAward", "是否奖励资源");
            cellKey.setString("taGroupName", "注册来源组");
            cellKey.setString("ta", "ta");
            if (Session.getSid() == 1224 || Session.getSid() == 753 || isManager) {
                cellKey.setString("allotType", "分配方式");
            }
            OssPoi.exportExcel(cellKey, actionList, outputStream);

            return "{\"success\":true}";
        }

        return "{\"success\":true, \"dataList\":" + actionList + ", \"total\":" + total + "}";
    }
/** logRecord.jsp end **/

    /** countStat.jsp start **/
    private static String getCountStatList(HttpServletRequest request) throws Exception {
        Param signupConf = Web.getConf("signup");
        FaiList<String> mobileList = signupConf.getList("mobile");//内部手机
        if (signupConf != null) {
            if (mobileList != null) {
                Log.logDbg("yansen mobileList=%s", mobileList);
            } else {
                Log.logDbg("yansen mobileList null err =%s", mobileList);
            }
        }


        // 销售列表
        //FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);

        FaiList<Integer> sidList = new FaiList<Integer>();                        // 直销互动销售列表
        FaiList<String> sacctList = new FaiList<String>();//互动销售员工账号
        for (Param item : infoList) {
            sidList.add(item.getInt("sid"));
            sacctList.add(item.getString(HdSaleDef.Info.ACCT));
        }
        FaiList<Param> preSaleActionList = null;        // 所有员工领取客户情况

        // 设置时间
        Calendar now = Calendar.getInstance();
        String receiveBegDate = Parser.parseString(request.getParameter("receiveDateBeg"), Parser.parseSimpleTime(now));
        String receiveEndDate = Parser.parseString(request.getParameter("receiveDateEnd"), Parser.parseSimpleTime(now));

        String se_sacct = Parser.parseString(request.getParameter("se_sacct"), "all");
        int isReceiveTime = Parser.parseInt(request.getParameter("isReceiveTime"), 0);                // 领取时间（针对释放记录）

        // 获取当天主动领取客户数量
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdSaleRecord";
        selectArg.field = "sacct, sum(attrType=11) as 'phone', sum(attrType=14) as 'approve', sum(attrType=12) as 'allot'";
        selectArg.group = "sacct";

        // 查询条件
        SearchArg searchArg = selectArg.searchArg;
        searchArg.matcher = new ParamMatcher();

        if (!se_sacct.equals("all")) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.EQ, se_sacct);
        } else if (sacctList.size() > 0) {
            searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
        }


        searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveBegDate + " 00:00:00");
        searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveEndDate + " 23:59:59");
        searchArg.cmpor = new ParamComparator("phone", true);
        searchArg.cmpor.addKey("approve", true);
        searchArg.cmpor.addKey("allot", true);

        Dao dao = WebOss.getOssBsDao();
        FaiList<Param> aidList = null;
        try {
            preSaleActionList = dao.select(selectArg);
            selectArg = new Dao.SelectArg();
            selectArg.field = "sacct,aid,receiveTime";//再查aid，用来查创建游戏数
            selectArg.table = "hdSaleRecord";
            SearchArg searchArgAid = new SearchArg();
            searchArgAid.matcher = new ParamMatcher();
            searchArgAid.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveBegDate + " 00:00:00");
            searchArgAid.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveEndDate + " 23:59:59");
            FaiList<Integer> attrList = new FaiList<Integer>();
            attrList.add(11);
            attrList.add(12);
            attrList.add(14);
            searchArgAid.matcher.and("attrType", ParamMatcher.IN, attrList);
            if (!se_sacct.equals("all")) {
                searchArgAid.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.EQ, se_sacct);
            } else if (sacctList.size() > 0) {
                searchArgAid.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
            }
            selectArg.searchArg = searchArgAid;//matcher 一样
            aidList = dao.select(selectArg);

            selectArg = new Dao.SelectArg();
            selectArg.field = "*";//
            selectArg.table = "hdInnerMobile";
            SearchArg searchArgMobile = new SearchArg();
            searchArgMobile.matcher = new ParamMatcher("mobile", ParamMatcher.IN, mobileList);
            FaiList<Param> dbMobileList = dao.select(selectArg);
            Log.logDbg("yansen mobileList111=%s", mobileList);
            FaiList<Param> notExistList = new FaiList<Param>();
            Param existParam = new Param();
            if (dbMobileList != null && dbMobileList.size() > 0) {
                for (Param item : dbMobileList) {
                    String tempMobile = item.getString("mobile", "");
                    existParam.setInt(tempMobile, 1);
                }
            }
            for (String mobile : mobileList) {
                if (existParam.getInt(mobile, -1) == -1) {
                    Param mobileItem = new Param();
                    mobileItem.setString("mobile", mobile);
                    notExistList.add(mobileItem);
                }
            }
            if (notExistList.size() > 0) {
                rt = dao.batchInsert("hdInnerMobile", notExistList);
                if (rt != Errno.OK) {
                    Log.logErr("yansen update inner mobile err");
                }
            }

        } finally {
            dao.close();
        }
        FaiList<Integer> aidIntList = new FaiList<Integer>();
        for (Param item : aidList) {
            aidIntList.add(item.getInt("aid", 0));
        }

        Dao bssMainDao = WebOss.getBssMainDaoMaster();
        FaiList<Param> gameCountList = null;
        try {
            Dao.SelectArg selectArgGame = new Dao.SelectArg();
            selectArgGame.table = "hdTemplateGame";
            SearchArg searchArgGame = new SearchArg();
            selectArgGame.field = "aid";
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            long createTime = sdf.parse(receiveEndDate + " 23:59:59").getTime() / 1000;
            ParamMatcher matcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            matcher.and("createTime", ParamMatcher.LE, createTime);
            searchArgGame.matcher = matcher;
            selectArgGame.searchArg = searchArgGame;
            selectArgGame.group = "aid";
            gameCountList = bssMainDao.select(selectArgGame);//查询是有记录即可
        } finally {
            bssMainDao.close();
        }
        if (gameCountList != null) {
            Param gameParam = new Param(true);
            Param gameParamAcct = new Param(true);
            for (Param item : gameCountList) {
                int aid = item.getInt("aid", 0);
                gameParam.setInt("" + aid, 1);
            }
            for (Param item : aidList) {
                int aid = item.getInt("aid", 0);
                String sacct = item.getString("sacct", "");
                int flag = gameParam.getInt("" + aid, 0);
                int noCreateGameCount = gameParamAcct.getInt(sacct, 0);
                if (gameParam.getInt("" + aid, 0) == 0) {//没创建过游戏
                    ++noCreateGameCount;
                    gameParamAcct.setInt(sacct, noCreateGameCount);
                }

            }
            for (Param item : preSaleActionList) {
                String sacct = item.getString("sacct", "");
                item.setInt("noCreateGameCount", gameParamAcct.getInt(sacct, 0));
            }
        }

        /* 数据翻译 */
        if (preSaleActionList != null)//防NullPointException
            for (Param p : preSaleActionList) {
                // 获取员工的姓名
                String salesAcct = p.getString(HdSaleRecordDef.Info.SACCT, "");
                Param staffInfo = Misc.getFirst(infoList, HdSaleDef.Info.ACCT, salesAcct);
                if (staffInfo == null) {
                    staffInfo = new Param();
                }
                String salesAcctStr = staffInfo.getString(HdSaleDef.Info.NICK_NAME, "");
                p.setString("salesAcctStr", salesAcctStr);
            }


        return "{\"success\":true, \"dataList\":" + preSaleActionList + "}";
    }
/** countStat.jsp end **/

    /** cleanup.jsp start **/
    private static String getCleanupList(HttpServletRequest request) throws Exception {
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);

        FaiList<Param> preSaleList = null;        // 所有员工客户领取情况
        FaiList<Param> detailList = null;        // 每个离职员工的详细领取情况

        double personTotal = 0;                // 个人库总量
        double dealTotal = 0;                // 成交客户总量
        double leavepersonTotal = 0;        // 离职员工个人库总量
        double leavedealTotal = 0;            // 离职员工成交客户总量

        Staff staff = (Staff) Core.getCorpKit(Web.getFaiCid(), Kid.STAFF);
        FaiList<Param> staffList = staff.getStaffList();
        // 获取没离职的员工列表
        FaiList<String> acctList = new FaiList<String>();
        for (Param p : staffList) {
            if (getChecked(p, StaffDef.AuthBit.LOGIN)) {//return (p.authBit & 0x2) == 0x2;   // 2,3,6,7,9,10,15,
                acctList.add(p.getString(StaffDef.Info.SACCT, ""));
            }
        }

        Dao dao = WebOss.getOssBsDao();
        try {
            /***** 获取acctPreSale表里的领取总量情况	*****/
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "salesAcct,sum(status=1) as 'person', sum(status=2) as 'deal'";
            sltArg.group = "salesAcct";
            SearchArg searchArg = sltArg.searchArg;

            // 先按员工分组查出一个大List
            searchArg.matcher = new ParamMatcher("del", ParamMatcher.EQ, false);
            searchArg.matcher.and("salesAcct", ParamMatcher.NE, "");
            preSaleList = dao.select(sltArg);

            if (preSaleList != null && preSaleList.size() > 0) {
                // 拿出离职员工领取的部分
                ParamMatcher matcher = new ParamMatcher("salesAcct", ParamMatcher.NOT_IN, acctList);
                detailList = Misc.getList(preSaleList, matcher);

                // 求当天任务、个人库以及成交客户的总量
                personTotal = Misc.sum(preSaleList, "person");
                dealTotal = Misc.sum(preSaleList, "deal");

                // 离职员工的个人库以及成交客户总量
                leavepersonTotal = Misc.sum(detailList, "person");
                leavedealTotal = Misc.sum(detailList, "deal");
            }
            /***** 获取acctPreSale表里的领取总量情况	END*****/
        } finally {
            dao.close();
        }

        /* 数据翻译 */
        for (Param p : preSaleList) {
            String salesAcct = p.getString("salesAcct", "");
            // 获取员工的姓名，这样好看点
            Param staffInfo = Misc.getFirst(staffList, StaffDef.Info.SACCT, salesAcct);
            if (staffInfo == null) {
                staffInfo = new Param();
            }
            String salesAcctStr = staffInfo.getString(StaffDef.Info.NAME, "");
            p.setString("salesAcctStr", salesAcctStr);
        }
        for (Param p : detailList) {
            String salesAcct = p.getString("salesAcct", "");
            Param staffInfo = Misc.getFirst(staffList, StaffDef.Info.SACCT, salesAcct);
            if (staffInfo == null) {
                staffInfo = new Param();
            }
            String salesAcctStr = staffInfo.getString(StaffDef.Info.NAME, "");
            p.setString("salesAcctStr", salesAcctStr);
        }
        /*直销售前-客户领取情况*/
        FaiList<Param> cusReceiveList = new FaiList<Param>();
        Param param = new Param();
        param.setString("rowName", "个人库");
        param.setDouble("total", personTotal);
        param.setDouble("leaveTotal", leavepersonTotal);
        cusReceiveList.add(param);
        param = new Param();
        param.setString("rowName", "成交客户");
        param.setDouble("total", dealTotal);
        param.setDouble("leaveTotal", leavedealTotal);
        cusReceiveList.add(param);

        return "{\"success\":true, \"preSaleList\":" + preSaleList + ", \"detailList\":" + detailList + ", \"cusReceiveList\":" + cusReceiveList + "}";
    }
/** cleanup.jsp end **/

    /** setCountLimit.jsp start **/
    private static String getSetCountLimitList(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSale", false);
        if (!hasNotAuth && sid != 753 && sid != 1224 && sid != 1355) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        FaiList<Param> preSaleActionList = null;        // 所有员工领取客户情况
        // 所有员工的信息
        Staff oStaff = (Staff) Core.getCorpKit(Web.getFaiCid(), Kid.STAFF);
        FaiList<Param> staffList = oStaff.getStaffList();
        // 获取当天主动领取客户数量
        FaiList<Param> awardCountList = null;
        //获取 hdSaleAllotRegular销售暂停发放资源的时间
        FaiList<Param> stopAllotList = null;
        Dao.SelectArg sltArg = new Dao.SelectArg();
        sltArg.table = "hdSale";
        FaiList<Param> totalLimit = null;
        Dao dao = WebOss.getOssBsDao();
        try {
            preSaleActionList = dao.select(sltArg);
            if (preSaleActionList == null) {
                preSaleActionList = new FaiList<Param>();
            }
            Calendar now = Calendar.getInstance();
            now.set(Calendar.HOUR_OF_DAY, 0);
            String todayStart = Parser.parseDateString(now, "yyyy-MM-dd HH:mm:ss");
            String sql = "select salesAcct as acct,count(aid) as awardCount from acctPreSaleHd where flag&2 and receiveTime>\'" + todayStart + "\' group by salesAcct";//奖励资源
            awardCountList = dao.executeQuery(sql);
            totalLimit = dao.executeQuery("select * from hdTotalLimitCount");
            stopAllotList = dao.executeQuery("select * from  hdSaleAllotRegular");
        } finally {
            dao.close();
        }
        Param award = new Param(true);
        for (Param item : awardCountList) {
            award.setInt(item.getString("acct"), item.getInt("awardCount", 0));
        }
        FaiList<Integer> sidList = new FaiList<Integer>();
        long nowTime = Calendar.getInstance().getTimeInMillis();

        Log.logStd("li test stopAllotList=%s", stopAllotList);
        for (Param sale : preSaleActionList) {
            sidList.add(sale.getInt(HdSaleDef.Info.SID));
            int sidItem = sale.getInt(HdSaleDef.Info.SID);
            Param staff = Misc.getFirst(staffList, StaffDef.Info.SID, sidItem);
            Param stopInfo = Misc.getFirst(stopAllotList, StaffDef.Info.SID, sidItem);
            if (stopInfo != null && !stopInfo.isEmpty()) {
                long allotStartTime = stopInfo.getLong("allotStartTime", 0L);
                long allotEndTime = stopInfo.getLong("allotEndTime", 0L);
                //Log.logStd("li test sale allotStartTime=%s,allotEndTime=%s,nowTime=%s",allotStartTime,allotEndTime,nowTime);
                sale.setLong("allotStartTime", allotStartTime);
                sale.setLong("allotEndTime", allotEndTime);
                sale.setBoolean("isStopAllot", true);
            } else {
                sale.setLong("allotStartTime", 0L);
                sale.setLong("allotEndTime", 0L);
                sale.setBoolean("isStopAllot", false);
            }
            Log.logStd("li test sale=%s", sale);
            if (staff == null) {
                continue;
            }
            int status = sale.getInt("status", -1);
            String statusStr = "";
            if (status == -1) {
                statusStr = "不分配";
            } else if (status == 0) {
                statusStr = "未转正";
            } else if (status == 1) {
                statusStr = "已转正";
            }
            String t_name = staff.getString(StaffDef.Info.NAME, "");
            int flag = sale.getInt(HdSaleDef.Info.FLAG, 0);
            sale.setString("name", t_name);
            sale.setString("statusStr", statusStr);
            sale.setBoolean("saturdayAllot", Misc.checkBit(flag, HdSaleDef.Flag.SATURDAY_ALLOT));
            sale.setBoolean("sundayAllot", Misc.checkBit(flag, HdSaleDef.Flag.SUNDAY_ALLOT));
            //sale.setBoolean("stopAllot", Misc.checkBit(flag, HdSaleDef.Flag.STOP_ALLOT));
            FaiList<String> list = new FaiList<String>();
            String salesAcct = sale.getString("acct", "");
            Param allotData = Param.parseParam(sale.getString(HdSaleDef.Info.ALLOT_DATA, ""), new Param());
            list.add("奖励资源:" + award.getInt(salesAcct, 0));
            //allotData.setInt("奖励资源",award.getInt(salesAcct,0));
            Set<String> keySet = allotData.keySet();
            int total = 0;
            for (String key : keySet) {
                if (HdSaleDef.AllotData.UPDATE_TIME.equals(key) || HdSaleDef.AllotData.REDUCT.equals(key)) {
                    continue;
                } else {
                    int allot = allotData.getInt(key, 0);
                    total += allot;
                    list.add(key + ":" + allot);
                }
            }
            list.add("总:" + total);
            sale.setList("allotStr", list);
        }
        Param res = new Param();
        res.setBoolean("success", true);
        res.setList("dataList", preSaleActionList);
        res.setList("totalList", totalLimit);
        return res.toJson();
    }

    /*销售转正*/
    private String changeOfficial(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSaleManage", false);
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        int rt = Errno.ERROR;
        String acct = Parser.parseString(request.getParameter("acct"), "");
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, acct);
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add("*");
        FaiList<Param> hdSaleListList = new FaiList<Param>();
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        if (!cli.init()) {
            throw new Exception("cli init error");
        }
        rt = cli.getSalesList(fieldList, searchArg, hdSaleListList);
        if (rt != Errno.OK || hdSaleListList == null || hdSaleListList.size() <= 0) {
            Log.logErr(rt, "set saleInfo err =%s", hdSaleListList);
            return "{\"success\":true, \"msg\":\"设置失败\"}";
        }*/
        Param oldInfo = hdSaleListList.get(0);
        oldInfo.setInt(HdSaleDef.Info.STATUS, 1);
        try {
            // 修改
            ParamUpdater updater = new ParamUpdater(oldInfo);
            ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, acct);
           /* rt = cli.setPreSaleInfo(matcher, updater);
            if (rt != Errno.OK) {
                Log.logErr(rt, "set saleInfo err =%s", oldInfo);
                return "{\"success\":true, \"msg\":\"设置失败\"}";
            }*/
        } catch (Exception exp) {
            Log.logErr("yansen set sale err =%s", exp);
        }
        return "{\"success\":true, \"msg\":\"设置成功\"}";
    }

    /*删除销售*/
    private String delSale(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSale", false);
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        String acct = Parser.parseString(request.getParameter("acct"), "");
       // PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        Dao ossDao = WebOss.getOssBsDao();
        try {
            ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, acct);
            rt = ossDao.delete("hdSale", matcher);
            if (rt != Errno.OK) {
                Log.logErr(rt, "set saleInfo err =%s", matcher);
                return "{\"success\":true, \"msg\":\"删除失败\"}";
            }
        } finally {
            ossDao.close();
        }
        return "{\"success\":true, \"msg\":\"删除成功\"}";
    }
/** setCountLimit.jsp end **/

    /** approve.jsp start **/
    private static String getApproveList(HttpServletRequest request) throws Exception {
        
		int _sid = Session.getSid();
		/* if(_sid != 1355 && _sid != 1535 && _sid != 987 && _sid != 112 && _sid != 1736){
			return "";
		} */

        if(!Auth.checkFaiscoAuth("authYKSale1|authYkSaleLeader", false)){
            return "没有权限";
        }
		
		// 操作的权限
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);                                
        boolean groupLeader = Auth.checkFaiscoAuth("authYkSaleLeader", false);           
        
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);               

        //FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");
        //Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	//SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        // 分页参数
        int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = Parser.parseInt(request.getParameter("limit"), 10);
        int start = limit * (pageNo - 1);
        int total = 0;
        int keyNum = 0;

        /* 请求参数 */
        int se_sid = Parser.parseInt(request.getParameter("sid"), 0);                    // 申请人
        String se_acct = Parser.parseString(request.getParameter("acct"), "");
        int se_status = Parser.parseInt(request.getParameter("status"), -1);             //审批结果
        int se_style = Parser.parseInt(request.getParameter("style"), -1);               //报备类型
        boolean isApplyTime = Parser.parseBoolean(request.getParameter("approveDateFlag"), false);
        boolean isApproveTime = Parser.parseBoolean(request.getParameter("applyDateFlag"), false);

        
        // 设置时间
        Calendar clr = Calendar.getInstance();
        clr.add(Calendar.DATE, -7);

        String applyBegTime = Parser.parseString(request.getParameter("approveDateBeg"), Parser.parseSimpleTime(clr));
        String applyEndTime = Parser.parseString(request.getParameter("approveDateEng"), Parser.parseSimpleTime(Calendar.getInstance()));

        String approveBegTime = Parser.parseString(request.getParameter("applyDateBeg"), Parser.parseSimpleTime(clr));
        String approveEndTime = Parser.parseString(request.getParameter("applyDateEng"), Parser.parseSimpleTime(Calendar.getInstance()));

        // 查询
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleApproveDef.Info.AID, ParamMatcher.GE, 0);


        if (se_sid != 0) {
            searchArg.matcher.and(HdSaleApproveDef.Info.SID, ParamMatcher.EQ, se_sid);
        }
        if (se_status != -1) {
            searchArg.matcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, se_status);
        }
        if (se_style != -1) {
            searchArg.matcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, se_style);
        }
        if (isApplyTime) {
            searchArg.matcher.and(HdSaleApproveDef.Info.CREATE_TIME, ParamMatcher.GE, applyBegTime + " 00:00:00");
            searchArg.matcher.and(HdSaleApproveDef.Info.CREATE_TIME, ParamMatcher.LE, applyEndTime + " 23:59:59");
        }
        if (isApproveTime) {
            searchArg.matcher.and(HdSaleApproveDef.Info.APPROVE_TIME, ParamMatcher.GE, approveBegTime + " 00:00:00");
            searchArg.matcher.and(HdSaleApproveDef.Info.APPROVE_TIME, ParamMatcher.LE, approveEndTime + " 23:59:59");
        }
        if (!se_acct.equals("")) {
            int se_aid = Parser.parseInt(se_acct, 0);
            if (se_aid == 0) {
                se_aid = Acct.getAid(AcctDef.Atype.CORP, se_acct);
            }
            searchArg.matcher.and(HdSaleApproveDef.Info.AID, ParamMatcher.EQ, se_aid);
        }

        searchArg.limit = limit;
        searchArg.start = start;
        searchArg.totalSize = new fai.comm.util.Ref<Integer>();
        searchArg.cmpor = new ParamComparator(HdSaleApproveDef.Info.ID, true);
        FaiList<Param> list = new FaiList<Param>();
        Dao ykDao = WebHdOss.getYkOssDaoSlave();
        try {
            Dao.SelectArg selectArg = new Dao.SelectArg();
            selectArg.field = "*";
            selectArg.table = "ykSaleApprove";
            selectArg.searchArg = searchArg;
            list = ykDao.select(selectArg);
        } finally {
        	ykDao.close();
        }
        total = searchArg.totalSize.value;
        
        // 获取企业账户以及申请人信息
        FaiList<Param> acctList = new FaiList<Param>();

        FaiList<Integer> aidList = new FaiList<Integer>();
        for (Param p : list) {
            aidList.add(p.getInt(HdSaleApproveDef.Info.AID, 0));
        }

        if (aidList.size() > 0) {
            FaiList<String> fieldList = new FaiList<String>();
            fieldList.add(BssStatDef.Info.AID);
            fieldList.add(BssStatDef.Info.ACCT);

            searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);

            //acctList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

            FaiList<Object> nestExpr = new FaiList<Object>();
            nestExpr.add(IN.contains("aid", aidList));
            FdpDataParamMatcher matcher = AND.expr(nestExpr);

            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("aid", "reg_aacct")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(matcher)
                    .execute(Core.getFlow());
            acctList = execute.getData();
        }
        /* 数据翻译 */
        
        String domain = WebHdOss.getOssDomainUrl();
        for (Param p : list) {
            int aid = p.getInt(HdSaleApproveDef.Info.AID, 0);
            int sid = p.getInt(HdSaleApproveDef.Info.SID, 0);
            int style = p.getInt(HdSaleApproveDef.Info.APPROVE_STYLE);        // 报备类型
            int status = p.getInt(HdSaleApproveDef.Info.STATUS, 0);            // 审批结果
            String fileId = p.getString(HdSaleApproveDef.Info.FILEID, "");    // 文件id

            p.setString("applyTime", Parser.parseString(p.getCalendar(HdSaleApproveDef.Info.CREATE_TIME)));    // 申请时间
            p.setString("approveTime", Parser.parseString(p.getCalendar(HdSaleApproveDef.Info.APPROVE_TIME)));    // 审批时间
            // 企业账号
            String acctStr = aid + "";
            Param acctInfo = Misc.getFirst(acctList, BssStatDef.Info.AID, aid);
            if (acctInfo != null) {
                String acct = acctInfo.getString(BssStatDef.Info.ACCT, "");
                if (!acct.equals("")) {
                    acctStr = acct + " / " + acctStr;
                }
            }
            p.setString("url", domain + "/index.jsp?t=ykSale&u=/cs/corp.jsp?aid=" + aid);
            p.setString("acctStr", acctStr);
            p.setString("staffName", WebOss.getStaffName(sid));
            
            String styleStr =  HdSaleApproveDef.getStyleName(style);
            Log.logStd("styleStr = %s", styleStr);
			if(!"".equals(styleStr)){
				p.setString("styleStr", styleStr);	
			}else{
				if(style == 11){
					p.setString("styleStr", "大满贯");
				}else if(style == 12){
					p.setString("styleStr", "种子用户");
				}else{
					p.setString("styleStr", styleStr);
				}
			}
            
            p.setString("statusStr", HdSaleApproveDef.getApproveStatusName(status));
            String coverImgsrc = "";
            if (!fileId.equals("")) {
                coverImgsrc = FileStg.getFaiOssUrl(fileId);
            }
            p.setString("coverImgsrc", coverImgsrc);
        } 
		Log.logStd("list = %s , total = %s", list, total);
        return "{\"success\":true, \"dataList\":" + list + ", \"total\":" + total + "}";
    }
/** approve.jsp end **/

    /** setConf.jsp start **/
    private static String getSetConfList(HttpServletRequest request) throws Exception {
        boolean adm = Auth.checkFaiscoAuth("authAdm", false);
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);    // presaleUtil 的接口

        Param ossConf = Web.getConf("oss");                                        // 调用拿到oss拿到配置文件内容
        //FaiList<Integer> hdSids = WebOss.getAuthSidList("authHDSale");
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        FaiList<Integer> hdSids = new FaiList<Integer>();                        // 直销互动销售列表
        for (Param item : infoList) {
            hdSids.add(item.getInt("sid"));
        }
        FaiList<String> hdSalesAcct = new FaiList<String>();
        for (int sid : hdSids) {
            hdSalesAcct.add(Acct.getSacct(1, sid));
        }

        // 查询互动配置信息
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.HD_ALLOT);
        FaiList<Param> preSalePhoneList = sysPreSaleUtil.searchPreSaleConf(searchArg);

        // 查询互动销售个人库情况
        Dao dao = WebOss.getOssBsDao();
        FaiList<Param> preSaleList = new FaiList<Param>();
        try {
            /***** 获取acctPreSale表里的领取总量情况	*****/
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "salesAcct,sum(status=1) as 'person'";
            sltArg.group = "salesAcct";

            // 先按员工分组查出一个大List
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.DEL, ParamMatcher.EQ, false);
            sltArg.searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, hdSalesAcct);
            preSaleList = dao.select(sltArg);
        } finally {
            dao.close();
        }

        /* 数据翻译 */
        FaiList<Param> personalList = new FaiList<Param>();
        for (int pSid : hdSids) {//个人库
            Param staffInfo = Acct.getStaffInfo(1, pSid);
            if (staffInfo == null) {
                continue;
            }
            String pName = staffInfo.getHtmlString(StaffDef.Info.NAME);
            String pSacct = staffInfo.getHtmlString(StaffDef.Info.SACCT);

            int pCnt = 0;
            Param preSaleInfo = Misc.getFirst(preSaleList, "salesAcct", pSacct);
            if (preSaleInfo != null) {
                pCnt = preSaleInfo.getInt("person", 0);
            }

            Param param = new Param();
            param.setString("sacct", pSacct);
            param.setInt("person", pCnt);
            param.setString("sacctStr", pName + "(" + pSacct + ")");
            personalList.add(param);
        }
        Param result = new Param();
        result.setBoolean("success", true);
        result.setList("personalList", personalList);
        return result.toJson();
    }

    /** 拿销售列表 **/
    private static FaiList<Integer> getSaleSidList() throws Exception {
        FaiList<Param> infoList = getSaleInfoList();
        FaiList<Integer> sidList = new FaiList<Integer>();
        for (Param item : infoList) {
            sidList.add(item.getInt(HdSaleDef.Info.SID, 0));
        }
        return sidList;
    }

    private static FaiList<Param> getSaleInfoList() throws Exception {
        return getSaleInfoListAll(true);
    }

    private static FaiList<Param> getSaleInfoListAll(boolean all) throws Exception {
        return getSaleInfoListAll(all, new ParamMatcher());
    }

    private static FaiList<Param> getSaleInfoListAll(boolean all, ParamMatcher matcher) throws Exception {
    	
    	Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
    	
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = matcher;
        matcher.and(YkSaleDef.Info.SID, ParamMatcher.GT, 0);

        
        int rt = sysPreSaleYk.getYkSaleList(saleSearchArg, infoList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr("get saleList err mat=%s", saleSearchArg.matcher);
        }
        
        FaiList<Param> finalList = new FaiList<Param>();
        if (all) {
            Param info = new Param();
            info.setString("acct", "all");
            info.setString("staffName", "全部");
            info.setString("nickName", "全部");
            finalList.add(info);
        }
        for (Param item : infoList) {
            finalList.add(item);
        }
        return finalList;
    }

    private static String getSaleListNotAll(HttpServletRequest request) throws Exception {
        Param result = new Param();
        FaiList<Param> dataList = getSaleInfoListAll(false);
        result.setBoolean("success", true);
        result.setList("dataList", dataList);
        return result.toJson();
    }

    private static String getSaleList(HttpServletRequest request) throws Exception {
    	Param result = new Param();
    	FaiList<Param> dataList = new FaiList<Param>();
    	
    	Param all = new Param();
    	all.setInt("sid", 0);
    	all.setString("nickName", "所有");
    	dataList.add(all);
    	dataList.addAll(getSaleInfoListAll(false));
    	
    	result.setBoolean("success", true);
        result.setList("dataList", dataList);
        
        return result.toJson();
    }
    
    /******************** 拿客户列表定义的一些数据 ********************/
    private static String getPageDef(HttpServletRequest request) throws Exception {
    	Param constant = Web.getConf("yk_constant");
        String key = request.getParameter("key");
        String tag = request.getParameter("tag");
        Param info = getPageDefInfo(key, tag);
        
        if (info == null) {
            return "";
        }
        return info.toJson();
    }

    private static Param getPageDefInfo(String key, String tag) throws Exception {
        Param constant = Web.getConf("yk_constant");
        if (key == null) {
            return new Param();
        }
        Param info = constant.getParam(key, new Param()).clone();
        Param comm = constant.getParam("comm", new Param());
        if ("ykSaleList".equals(key) || "ykPayRecord".equals(key)) {//客户列表|付款页面还需要tag标签
            info.setParam("tag", comm.getParam("tag"));
        }
        if (tag != null) {
            return info.getParam(tag);
        }
        
        return info;
    }
    /********************拿客户列表定义的一些数据 ********************/


    /******************** Bgein 互动销售->客户列表->维护个个人电话信息 数据 ********************/
    public static String getPersonTelInfo(HttpServletRequest request) throws Exception {

    	
        int sid = Session.getSid();                                              //销售ID
        if(sid != 1355 && sid != 1535 && sid != 987 && sid != 112){
        	return "";
        }
        FaiList<Param> ykSaleList = new FaiList<Param>();                        //销售列表

        SearchArg searchArg = new SearchArg();                                    //查询
        searchArg.matcher = new ParamMatcher("sid", ParamMatcher.EQ, sid);        //查询条件

        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        
        int rt = sysPreSaleYk.getYkSaleList(searchArg, ykSaleList);            //查询
        if (rt != Errno.OK) {
            Log.logErr(rt, "get hdSale err");
        }

        String name = WebOss.getStaffName(sid);
        String phone = "";
        Param personTel = new Param();
        if (!ykSaleList.isEmpty()) {
            personTel = ykSaleList.get(0);
        }

        //短信的模版列表
        FaiList<Param> modelInfoList = new FaiList<Param>();
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(OssSmsDef.ModelInfo.MODELTYPE, ParamMatcher.EQ, OssSmsDef.BusType.HD);
        modelInfoList = WebOss.getModelList(searchArg);


        //获取模板审核状态
        for (Param p : modelInfoList) {
            int type = p.getInt("modelType");                                            //模板类型（0建站，1互动）

            if (type == 0)
                p.setString("modelType", "建站");
            else
                p.setString("modelType", "互动");

            int templateId = p.getInt(OssSmsDef.ModelInfo.TEMPLATE_ID);                                    //模板ID
            SysFaiSmsPlatform smsPlatform = (SysFaiSmsPlatform) Core.getSysKit(Kid.SYS_FAI_SMS);
            Param templateInfo = smsPlatform.getTemplateInfo(Web.getFaiCid(), templateId);
            String statusDes = "";
            switch (templateInfo.getInt(SungoinDef.Info.STATUS, 0)) {
                case SungoinDef.Status.TEMPLATE_ADD_CHECK:
                    statusDes = "提交待审核";
                    break;
                case SungoinDef.Status.TEMPLATE_ADD_REFUND:
                    statusDes = "提交被打回的";
                    break;
                case SungoinDef.Status.TEMPLATE_ADD_SUCCESS:
                    statusDes = "审核通过，模版能使用";
                    break;
                case SungoinDef.Status.TEMPLATE_ADD_FORBIDDEN:
                    statusDes = "禁用模板，模版不能使用";
                    break;
            }
            p.setObject("check", statusDes);                        //添加状态字段

        }

        modelInfoList.add(personTel);                            //添加个人信息到模板数组

        return modelInfoList.toJson();

    }

    /******************** end 互动销售->客户列表->维护个个人电话信息 数据 ********************/

    /*公共方法，只用来修改hdSale表的flag*/
    private int setHdSaleFlag(String acct, String type, boolean updateFlag) throws Exception {
        int rt = Errno.ERROR;
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        if (!cli.init()) {
            throw new Exception("cli init error");
        }*/
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, acct);
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add("*");
        FaiList<Param> hdSaleListList = new FaiList<Param>();
        /*rt = cli.getSalesList(fieldList, searchArg, hdSaleListList);
        if (rt != Errno.OK || hdSaleListList == null || hdSaleListList.size() <= 0) {
            Log.logErr(rt, "set saleInfo err =%s", hdSaleListList);
            return Errno.ERROR;
        }*/
        Param oldInfo = hdSaleListList.get(0);
        int flag = oldInfo.getInt(HdSaleDef.Info.FLAG, 0);
        Log.logStd("li test flag=%s", flag);
        if ("saturdayAllot".equals(type)) {
            flag = Misc.setFlag(flag, HdSaleDef.Flag.SATURDAY_ALLOT, updateFlag);
            Log.logStd("li test flag=%s saturdayAllot", flag);
            ;
        } else if ("sundayAllot".equals(type)) {
            flag = Misc.setFlag(flag, HdSaleDef.Flag.SUNDAY_ALLOT, updateFlag);
            ;
        } else if ("isStopAllot".equals(type)) {//废弃，改在hdSaleAllotRegular表设置暂停发放资源
            Log.logStd("li test flag updateFlag isStopAllot=%s ", updateFlag);
            flag = Misc.setFlag(flag, HdSaleDef.Flag.STOP_ALLOT, !updateFlag);
            Log.logStd("li test flag=%s isStopAllot", flag);
            ;
        } else {
            Log.logErr(rt, "set saleInfo err =%s", oldInfo);
            return Errno.ERROR;
        }
        //修改
        oldInfo.setInt(HdSaleDef.Info.FLAG, flag);
        ParamUpdater updater = new ParamUpdater(oldInfo);
        ParamMatcher matcher = new ParamMatcher(HdSaleDef.Info.ACCT, ParamMatcher.EQ, acct);
        rt = cli.setPreSaleInfo(matcher, updater);
        return rt;
    }


    /*销售周末分配*/
    private String changeWeekendAllot(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSaleManage", false);
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        int rt = Errno.ERROR;
        String acct = Parser.parseString(request.getParameter("acct"), "");
        String type = Parser.parseString(request.getParameter("type"), "");
        boolean updateFlag = Parser.parseBoolean(request.getParameter("updateFlag"), false);
        rt = setHdSaleFlag(acct, type, updateFlag);
        if (rt != Errno.OK) {
            //Log.logErr(rt, "set saleInfo err =%s", oldInfo);
            return "{\"success\":true, \"msg\":\"设置失败\"}";
        }
        return "{\"success\":true, \"msg\":\"设置成功\"}";
    }

    /*是否暂停发放资源*/
    private String isStopAllot(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSaleManage", false);
        FaiList<Param> allotList = new FaiList<Param>();
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        /*  PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
         if (!cli.init()) {
             throw new Exception("cli init error");
         } */
        int rt = Errno.ERROR;
        int saleId = Parser.parseInt(request.getParameter("sid"), 0);
        long allotStartTime = Parser.parseLong(request.getParameter("allotStartTime"), 0L);
        long allotEndTime = Parser.parseLong(request.getParameter("allotEndTime"), 0L);
        boolean isStopAllot = Parser.parseBoolean(request.getParameter("isStopAllot"), false);
        Log.logStd("isStopAllot startTime=%s,endTime=%s sid=%s", allotStartTime, allotEndTime, saleId);
        if (saleId == 0) {
            return "{\"success\":false, \"msg\":\"参数有误\"}";
        }
        Dao ossDao = WebOss.getOssBsDao();
        if (isStopAllot) {//设置暂停发放资源
            Calendar now = Calendar.getInstance();
            now.add(Calendar.DAY_OF_YEAR, -1);

            if (allotStartTime == 0L || allotEndTime == 0L || allotStartTime > allotEndTime || allotStartTime < now.getTimeInMillis() / 1000) {
                return "{\"success\":false, \"msg\":\"请正确选择日期\"}";
            }
            try {
                String sql = "select * from  hdSaleAllotRegular where sid=" + saleId;
                allotList = ossDao.executeQuery(sql);
                Param record = new Param();
                record.setLong("updateTime", Calendar.getInstance().getTimeInMillis());
                record.setLong("allotStartTime", allotStartTime);
                record.setLong("allotEndTime", allotEndTime);
                Log.logStd("li test allotList=%s", allotList);
                if (allotList != null && allotList.size() > 0) {//已有改销售的记录则更新,没有则插入
                    rt = ossDao.update("hdSaleAllotRegular", new ParamUpdater(record), new ParamMatcher("sid", ParamMatcher.EQ, saleId));
                } else {
                    record.setInt("sid", saleId);
                    Log.logStd("li test record=%s", record);
                    rt = ossDao.insert("hdSaleAllotRegular", record);
                }
            } finally {
                ossDao.close();
            }
        } else {
            try {
                ParamMatcher matcher = new ParamMatcher("sid", ParamMatcher.EQ, saleId);
                rt = ossDao.delete("hdSaleAllotRegular", matcher);
            } finally {
                ossDao.close();
            }
        }
        if (rt != Errno.OK) {
            //Log.logErr(rt, "set saleInfo err =%s", oldInfo);
            Log.logStd("li test rt=%s", rt);
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }

        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }

    //取消设置暂停发放员工资源
    /*  private String delStopAllot (HttpServletRequest request) throws Exception {
    	int rt=Errno.ERROR;
    	int sid = Session.getSid();//当前操作员工的sid
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSaleManage", false);
        FaiList<Param> allotList=new FaiList<Param>();
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        //接受参数
        int saleId=Parser.parseInt(request.getParameter("sid"),0);
        Dao ossDao = WebOss.getOssBsDao();
        try{
        	ParamMatcher matcher = new ParamMatcher("sid", ParamMatcher.EQ,saleId);
        	 rt = ossDao.delete("hdSaleAllotRegular", matcher);
        }finally{
        	ossDao.close();
        }
        if(rt==Errno.OK){
        	return "{\"success\":true, \"msg\":\"取消成功\"}";
        }
        return "{\"success\":false, \"msg\":\"取消失败\"}";
    } */

    /*新增hdoss员工*/
    private String addHdOssStaff(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        int sid = Session.getSid();
        boolean hasNotAuth = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL) || WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE) || sid == 753;
        if (!hasNotAuth) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        String acct = Parser.parseString(request.getParameter("acct"), "");
        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), acct);
        sid = staffInfo.getInt(StaffDef.Info.SID, 0);
        if (acct.isEmpty() || sid == 0) {
            return "{\"success\":true, \"msg\":\"参数错误，员工不存在\"}";
        }
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        cli.init();*/
        HdOss hdOss = (HdOss) Core.getCorpKit(1, Kid.HD_OSS);
        Param staff = hdOss.getStaff(sid);
        if (staff == null || staff.isEmpty()) {
            staff = new Param();
            staff.setInt(HdOssStaffDef.Info.SID, sid);
            int auth = 0;
            auth = Misc.setFlag(auth, HdOssStaffDef.Auth.HD_OSS_LOGIN, true);
            staff.setInt(HdOssStaffDef.Info.AUTH, auth);
            staff.setString(HdOssStaffDef.Info.NAME, staffInfo.getString("name", ""));
            staff.setString(HdOssStaffDef.Info.SACCT, staffInfo.getString("sacct", ""));
            rt = hdOss.addStaff(staff);
            if (rt != Errno.OK) {
                Log.logErr("yansen add staff err");
            }
        }
        if (rt != Errno.OK) {
            return "{\"success\":true, \"msg\":\"新增失败\"}";
        }
        return "{\"success\":true, \"msg\":\"新增成功\"}";
    }

    /*新增销售*/
    private String addSale(HttpServletRequest request) throws Exception {
        int rt = Errno.ERROR;
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authHDSaleManage", false);
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }
        String acct = Parser.parseString(request.getParameter("acct"), "");
        Param staffInfo = Acct.getStaffInfo(Web.getFaiCid(), acct);
        sid = staffInfo.getInt(StaffDef.Info.SID, 0);
        if (acct.isEmpty() || sid == 0) {
            return "{\"success\":true, \"msg\":\"参数错误，员工不存在\"}";
        }
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        cli.init();
        Param info = new Param();
        info.setInt(HdSaleDef.Info.SID, sid);
        info.setString(HdSaleDef.Info.ACCT, acct);
        info.setString(HdSaleDef.Info.NICK_NAME, staffInfo.getString("name"));
        info.setInt(HdSaleDef.Info.LIMIT_COUNT, 20);
        info.setInt(HdSaleDef.Info.STATUS, 0);
        rt = cli.addSale(info);
        if (rt != Errno.OK) {
            return "{\"success\":true, \"msg\":\"新增失败\"}";
        }*/
        return "{\"success\":true, \"msg\":\"新增成功\"}";
    }

    /******************** BEGIN 设置销售联系信息 ********************************************************/
    private String setYkSale(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean hasNotAuth = Auth.checkFaiscoAuth("authYKSale1", false);
        if (!hasNotAuth && sid != 753) {
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }

        String name = Parser.parseString(request.getParameter("name"), "");
        String phone = Parser.parseString(request.getParameter("phone"), "");


        Param upInfo = new Param();
        upInfo.setInt(HdSaleDef.Info.SID, sid);
        upInfo.setString(HdSaleDef.Info.NICK_NAME, name);
        upInfo.setString(HdSaleDef.Info.PHONE, phone);

        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
      
        FaiList<Param> ykSaleList = new FaiList<Param>();

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("sid", ParamMatcher.EQ, sid);
        FaiList<String> fieldList = new FaiList<String>();
        
        int rt = sysPreSaleYk.getYkSaleList(searchArg, ykSaleList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "getYkSaleList err");
            return "{\"success\":false, \"msg\":\"系统错误\"}";
        }

        // 新加
        if (ykSaleList == null || ykSaleList.size() == 0) {
            return "{\"success\":false, \"msg\":\"查无该销售\"}";
        }
        
        // 修改
        ParamUpdater updater = new ParamUpdater(upInfo);
        ParamMatcher matcher = new ParamMatcher(YkSaleDef.Info.SID, ParamMatcher.EQ, sid);
        
        rt = sysPreSaleYk.setYkSale(sid, updater);
        if (rt != Errno.OK) {
            Log.logErr(rt, "set ykSaleInfo err =%s", upInfo);
            return "{\"success\":false, \"msg\":\"设置失败\"}";
        }

        return "{\"success\":true, \"msg\":\"设置成功\"}";
    }
    /******************** end 设置销售联系信息 ********************************************************/


    /******************** BEGIN 获取直销互动销售列表 ********************************************************/

    private String getSidList(HttpServletRequest request) throws Exception {

        //判断权限,adm、小雄可进入
        int sid = Session.getSid();
        if (!Auth.checkFaiscoAuth("authAdm|authHDSaleManage", false) && sid != 413) {
            return "没有权限";
        }

        Param ossConf = Web.getConf("oss");                                                        // 调用拿到oss拿到配置文件内容
        FaiList<Integer> sidList = WebOss.getAuthSidList("authHDSale");                            // 直销互动销售列表
        FaiList<Param> saleList = new FaiList<Param>();
        for (Integer tmpSid : sidList) {
            String t_sacct = Acct.getSacct(Web.getFaiCid(), tmpSid);
            String t_name = WebOss.getStaffName(tmpSid);
            Param p = new Param();
            p.setString("name", t_name);
            p.setString("label", t_sacct);
            saleList.add(p);
        }
        return saleList.toJson();
    }
    /******************** end 	获取直销互动销售列表 ********************************************************/


    /******************** BEGIN 未购买原因统计 ********************************************************/

    private String getReasonStat(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        //判断权限,adm、小雄可进入
        int sid = Session.getSid();
        if (!Auth.checkFaiscoAuth("authAdm|authHDSaleManage", false) && sid != 413) {
            return "没有权限";
        }
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();

        //从请求获取时间
        int intBegTime = Parser.parseInt(request.getParameter("regBegTime"), 0);
        int intEndTime = Parser.parseInt(request.getParameter("regEndTime"), 0);


        //获取销售账号
        String salesAcct = Parser.parseString(request.getParameter("salesAcct"), "");


        // 容器（最后返回这个map）
        Param resultParam = new Param(true);

        FaiList<Param> intentList = new FaiList<Param>();                //对进行标记的客户统计情况
        FaiList<Param> markList = new FaiList<Param>();                    //对填写未购买原因的客户统计情况

        Dao dao = WebOss.getOssBsDao();
        Dao bssDao = WebOss.getBssMainDaoMaster();
        java.sql.PreparedStatement stmt = null;
        try {
            // 根据注册时间，获取最小aid;;用regTime来排序，让它走regTime的索引
            String sql = "select aid from acctStatus where regTime>=? order by regTime asc limit 0,1;";
            stmt = bssDao.prepareStatement(sql);
            stmt.setInt(1, intBegTime);
            FaiList<Param> aidInfo = bssDao.executeQuery(stmt, null);
            int minAid = aidInfo.get(0).getInt(BssStatDef.Info.AID, 0);
            stmt.close();

            // 根据注册时间，获取最大aid
            sql = "select aid from acctStatus where regTime<=? order by regTime desc limit 0,1;";
            stmt = bssDao.prepareStatement(sql);
            stmt.setInt(1, intEndTime);
            aidInfo = bssDao.executeQuery(stmt, null);
            int maxAid = aidInfo.get(0).getInt(BssStatDef.Info.AID, 0);
            stmt.close();

            // 基本的查询条件(acctPreSaleHd);
            ParamMatcher matcherPreSale = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.GE, minAid);
            matcherPreSale.and(PreSaleHdDef.Info.AID, ParamMatcher.LE, maxAid);
            if (!salesAcct.equals("全部")) {
                matcherPreSale.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
            } else {
                matcherPreSale.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.NE, "");
            }


            /********************************** 统计客户标记情况  ***********************************/
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "intent, count(1) as cnt";
            sltArg.group = "intent";

            sltArg.searchArg.matcher = new ParamMatcher(matcherPreSale);
            FaiList<Param> cntList = dao.select(sltArg);

            // 统计各标记各版本数量
            for (Param p : cntList) {
                int intent = p.getInt(PreSaleHdDef.Info.INTENT, 0);
                int total = p.getInt("cnt", 0);

                // 统计该标记的情况
                Param intentInfo = new Param();
                String intentStr = PreSaleHdDef.getIntentName(intent);
                if (intent == 0) {
                    intentStr = "未标记";
                }

                // 计算总数
                intentInfo.setInt("总计", total);
                intentInfo.setString(PreSaleHdDef.Info.INTENT, intentStr);

                //初始化
                intentInfo.setInt("互动免费版", 0);
                intentInfo.setInt("互动白银版", 0);
                intentInfo.setInt("互动铂金版", 0);

                // 一次最多查询80000个aid
                FaiList<Integer> aidList = new FaiList<Integer>();
                FaiList<Param> preSaleList = new FaiList<Param>();
                FaiList<Param> bssList = new FaiList<Param>();

                int group = 80000;
                int start = 0;
                int end = 0;
                while (end < total) {

                    end = start + group;
                    if (end > total) {
                        end = total;
                    }

                    // 拿到该标记的aid列表
                    sltArg = new Dao.SelectArg();
                    sltArg.table = "acctPreSaleHd";
                    sltArg.field = "aid";

                    sltArg.searchArg.start = start;
                    sltArg.searchArg.limit = group;
                    sltArg.searchArg.cmpor = new ParamComparator(PreSaleHdDef.Info.AID, true);

                    sltArg.searchArg.matcher = new ParamMatcher(matcherPreSale);
                    sltArg.searchArg.matcher.and(PreSaleHdDef.Info.INTENT, ParamMatcher.EQ, intent);

                    preSaleList = dao.select(sltArg);
                    start += group;            // 下一次查询的start

                    aidList.clear();
                    for (Param preSale : preSaleList) {
                        aidList.add(preSale.getInt(PreSaleHdDef.Info.AID, 0));
                    }

                    // 统计不同网站版本的数量
                    sltArg = new Dao.SelectArg();
                    sltArg.table = "acctStatus";
                    sltArg.field = "hdVersion, count(1) as cnt";
                    sltArg.group = "hdVersion";

                    sltArg.searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
                    bssList = bssDao.select(sltArg);

                    for (Param bss : bssList) {
                        int tmpAid = bss.getInt(BssStatDef.Info.AID, 0);
                        faiTradeStationBaseApi.changeAid(tmpAid);
                        int hdVersion = bss.getInt(BssStatDef.Info.HD_VERSION, 0);
                        String hdVersionStr = faiTradeStationBaseApi.getName(hdVersion);
                        if (hdVersionStr.equals("")) {
                            hdVersionStr = "互动免费版";
                        }

                        int cnt = bss.getInt("cnt", 0);

                        // 叠加数量，放到param里面（该标记的总量）
                        int oldCnt = intentInfo.getInt(hdVersionStr, 0);
                        intentInfo.setInt(hdVersionStr, oldCnt + cnt);
                    }
                }
                intentList.add(intentInfo);
            }

            {
                // 计算总量
                Param allIntentInfo = new Param();
                allIntentInfo.setString(PreSaleHdDef.Info.INTENT, "总计");

                // 总计
                double total = Misc.sum(intentList, "总计");
                allIntentInfo.setInt("总计", (int) total);

                // 互动免费版
                total = Misc.sum(intentList, "互动免费版");
                allIntentInfo.setInt("互动免费版", (int) total);

                // 互动白银版
                total = Misc.sum(intentList, "互动白银版");
                allIntentInfo.setInt("互动白银版", (int) total);

                // 互动铂金版
                total = Misc.sum(intentList, "互动铂金版");
                allIntentInfo.setInt("互动铂金版", (int) total);

                intentList.add(allIntentInfo);

                // 计算比例
                int allTotal = allIntentInfo.getInt("总计", 0);
                java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0");

                for (Param p : intentList) {
                    total = p.getInt("总计", 0);

                    if (allTotal != 0) {
                        double percent = 100 * (double) total / (double) allTotal;
                        p.setString("percent", df.format(percent).toString());
                    } else {
                        p.setString("percent", "0");
                    }
                }
            }
            /********************************** 统计客户标记情况 END  ***********************************/


            /********************************** 统计未购买原因情况  ***********************************/
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.PRE_HD_REASON);
            searchArg.matcher.and(PreSaleUtilDef.Conf.SiteReason.DEL, ParamMatcher.EQ, 0);
            searchArg.matcher.and(PreSaleUtilDef.Conf.SiteReason.FATHER_ID, ParamMatcher.EQ, 0);

            SysPreSale sysPreSale = (SysPreSale) Core.getSysKit(Kid.SYS_PRESALE);
            FaiList<Param> reasonList = sysPreSale.searchPreSaleConf(searchArg);

            FaiList<String> reasonStrList = new FaiList<String>();
            reasonStrList.add("");
            for (Param p : reasonList) {
                reasonStrList.add(p.getString(PreSaleUtilDef.Conf.SiteReason.REASON, ""));
            }

            sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSaleHd";
            sltArg.field = "disBuyReason, count(1) as cnt";
            sltArg.group = "disBuyReason";

            // 先搜索上面的选项，余下的自己填写的，归到 分类其他 里面去
            sltArg.searchArg.matcher = new ParamMatcher(matcherPreSale);
            sltArg.searchArg.matcher.and(PreSaleHdDef.Info.DIS_BUY_REASON, ParamMatcher.IN, reasonStrList);
            cntList = dao.select(sltArg);

            // 统计各备注各版本数量
            for (Param p : cntList) {
                int total = p.getInt("cnt", 0);
                String mark = p.getString(PreSaleHdDef.Info.DIS_BUY_REASON, "");

                // 统计该备注类型的情况
                Param markInfo = new Param();
                markInfo.setString(PreSaleHdDef.Info.DIS_BUY_REASON, mark);
                markInfo.setInt("总计", total);

                //初始化
                markInfo.setInt("互动免费版", 0);
                markInfo.setInt("互动白银版", 0);
                markInfo.setInt("互动铂金版", 0);

                if (mark.equals("")) {
                    markInfo.setString(PreSaleHdDef.Info.DIS_BUY_REASON, "未填写");
                }

                // 一次最多查询80000个aid
                FaiList<Integer> aidList = new FaiList<Integer>();
                FaiList<Param> preSaleList = new FaiList<Param>();
                FaiList<Param> bssList = new FaiList<Param>();

                int group = 80000;
                int start = 0;
                int end = 0;
                while (end < total) {

                    end = start + group;
                    if (end > total) {
                        end = total;
                    }

                    // 拿到该标记的aid列表
                    sltArg = new Dao.SelectArg();
                    sltArg.table = "acctPreSaleHd";
                    sltArg.field = "aid";

                    sltArg.searchArg.start = start;
                    sltArg.searchArg.limit = group;
                    sltArg.searchArg.cmpor = new ParamComparator(PreSaleHdDef.Info.AID, true);

                    sltArg.searchArg.matcher = new ParamMatcher(matcherPreSale);
                    sltArg.searchArg.matcher.and(PreSaleHdDef.Info.DIS_BUY_REASON, ParamMatcher.EQ, mark);

                    preSaleList = dao.select(sltArg);
                    start += group;            // 下一次查询的start

                    aidList.clear();
                    for (Param preSale : preSaleList) {
                        aidList.add(preSale.getInt(PreSaleHdDef.Info.AID, 0));
                    }

                    // 统计不同网站版本的数量
                    sltArg = new Dao.SelectArg();
                    sltArg.table = "acctStatus";
                    sltArg.field = "hdVersion, count(1) as cnt";
                    sltArg.group = "hdVersion";

                    sltArg.searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
                    bssList = bssDao.select(sltArg);

                    for (Param bss : bssList) {
                        int tmpAid = bss.getInt(BssStatDef.Info.AID, 0);
                        faiTradeStationBaseApi.changeAid(tmpAid);
                        int hdVersion = bss.getInt(BssStatDef.Info.HD_VERSION, 0);
                        String hdVersionStr = faiTradeStationBaseApi.getName(hdVersion);
                        if (hdVersionStr.equals("")) {
                            hdVersionStr = "互动免费版";
                        }

                        int cnt = bss.getInt("cnt", 0);

                        // 叠加数量，放到param里面（该标记的总量）
                        int oldCnt = markInfo.getInt(hdVersionStr, 0);
                        markInfo.setInt(hdVersionStr, oldCnt + cnt);
                    }
                }
                markList.add(markInfo);
            }

            {
                // 计算总量
                Param allMarkInfo = new Param();
                allMarkInfo.setString(PreSaleHdDef.Info.DIS_BUY_REASON, "总计");

                // 总计
                double total = Misc.sum(markList, "总计");
                allMarkInfo.setInt("总计", (int) total);

                // 互动免费版
                total = Misc.sum(markList, "互动免费版");
                allMarkInfo.setInt("互动免费版", (int) total);

                // 互动白银版
                total = Misc.sum(markList, "互动白银版");
                allMarkInfo.setInt("互动白银版", (int) total);

                // 互动铂金版
                total = Misc.sum(markList, "互动铂金版");
                allMarkInfo.setInt("互动铂金版", (int) total);

                markList.add(allMarkInfo);

                // 计算比例
                int allTotal = allMarkInfo.getInt("总计", 0);
                java.text.DecimalFormat df = new java.text.DecimalFormat("#0.0");

                for (Param p : markList) {
                    total = p.getInt("总计", 0);
                    if (allTotal != 0) {
                        double percent = 100 * (double) total / (double) allTotal;
                        p.setString("percent", df.format(percent).toString());
                    } else {
                        p.setString("percent", "0");
                    }
                }
            }
            /********************************** 统计未购买原因情况 END  ***********************************/
        } finally {
            Dao.closeStmt(stmt);
            dao.close();
            bssDao.close();
        }
        resultParam.setObject("intentList", intentList);
        resultParam.setObject("markList", markList);


        //输出excel表格
        String type = Parser.parseString(request.getParameter("type"), "");

        if (!type.equals("")) {
            FaiList<Param> excelList = null;
            String title = null;
            if (type.equals("intent")) {
                excelList = intentList;
                title = "对进行标记的客户统计情况.xls";
            } else if (type.equals("mark")) {
                excelList = markList;
                title = "对填写未购买原因的客户统计情况.xls";
            }
            response.setContentType("application/x-excel");
            //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(title.getBytes("GBK"), "ISO-8859-1"));
            out.clear();//必须得加
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();

            if (type.equals("intent")) {
                cellKey.setString(PreSaleHdDef.Info.INTENT, "客户数量");
            } else if (type.equals("mark")) {
                cellKey.setString(PreSaleHdDef.Info.DIS_BUY_REASON, "原因");
            }

            cellKey.setString("percent", "比例(%)");
            cellKey.setString("互动免费版", "免费版");
            cellKey.setString("互动白银版", "白银版");
            cellKey.setString("互动铂金版", "铂金版");
            cellKey.setString("总计", "总计");

            OssPoi.exportExcel(cellKey, excelList, outputStream);


            return "null";

        }


        return resultParam.toJson();

    }
    /******************** END 未购买原因统计 ********************************************************/


    /******************** BEGIN 获取资源标签类型 ********************************************************/

    private String getTagList(HttpServletRequest request) throws Exception {
		Param constant = getPageDefInfo("comm", "tag");
		return constant.getList("labelList", new FaiList<Param>()).toJson();
        //return PreSaleHdDef.getTagList().toJson();

    }

    /******************** END 获取资源标签类型 ********************************************************/



    /*获取可以设置权限的员工列表*/
    private String getAuthStaffList(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        Param result = new Param();
        HdOss hdOss = (HdOss) Core.getCorpKit(1, Kid.HD_OSS);
        Param staff = hdOss.getStaff(sid);
        if (staff == null || staff.isEmpty()) {
            result.setInt("rt", -1);
            result.setString("msg", "员工不存在");
            result.setBoolean("success", false);
            return result.toJson();
        }
        int currentPage = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = Parser.parseInt(request.getParameter("size"), 1);
        String staffStr = request.getParameter("staff");
        int start = limit * (currentPage - 1);
        int auth = staff.getInt(HdOssStaffDef.Info.AUTH, 0);
        SearchArg searchArg = new SearchArg();
        ParamMatcher matcher = new ParamMatcher();
        searchArg.matcher = matcher;
        searchArg.start = start;
        searchArg.limit = limit;
        searchArg.totalSize = new Ref<Integer>();
        if (Misc.checkBit(auth, HdOssStaffDef.Auth.ALL) || Web.getDebug()) {//管理员权限
            matcher.and(HdOssStaffDef.Info.AUTH, ParamMatcher.LAND, HdOssStaffDef.Auth.HD_OSS_LOGIN, HdOssStaffDef.Auth.HD_OSS_LOGIN);
        } else if (Misc.checkBit(auth, HdOssStaffDef.Auth.HD_SALE_MANAGE)) {//销售管理
            matcher.and(HdOssStaffDef.Info.AUTH, ParamMatcher.LAND, HdOssStaffDef.Auth.HD_SALE, HdOssStaffDef.Auth.HD_SALE);
        } else if (Misc.checkBit(auth, HdOssStaffDef.Auth.HD_PRODUCT_MANAGE)) {//互动产品管理
            matcher.and(HdOssStaffDef.Info.AUTH, ParamMatcher.LAND, HdOssStaffDef.Auth.HD_OSS_LOGIN, HdOssStaffDef.Auth.HD_OSS_LOGIN);
        } else {
            result.setInt("rt", -1);
            result.setString("msg", "没有权限！");
            result.setBoolean("success", false);
            return result.toJson();
        }
        if (staffStr != null && !staffStr.isEmpty()) {
            ParamMatcher matOr = new ParamMatcher(HdOssStaffDef.Info.SACCT, ParamMatcher.LK, staffStr);
            matOr.or(HdOssStaffDef.Info.NAME, ParamMatcher.LK, staffStr);
            matcher.and(matOr);
        }
        FaiList<Param> staffList = new FaiList<Param>();
        int rt = hdOss.getStaffList(searchArg, staffList);                // 互动oss员工列表
        if (rt != Errno.OK) {
            Log.logErr("get Staff list err");
            result.setInt("rt", -1);
            result.setString("msg", "获取员工列表错误！");
            result.setBoolean("success", false);
            return result.toJson();
        }
        for (Param item : staffList) {
            int authFlag = item.getInt(HdOssStaffDef.Info.AUTH, 0);
            item.setBoolean("all", Misc.checkBit(authFlag, HdOssStaffDef.Auth.ALL));
            item.setBoolean("hdSaleManage", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_SALE_MANAGE));
            item.setBoolean("hdProductManage", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_PRODUCT_MANAGE));
            item.setBoolean("hdSale", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_SALE));
            item.setBoolean("hdSaleGroupLeader", Misc.checkBit(authFlag, HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER));
        }
        Log.logDbg("yansen searchArg.totalSize=%s", searchArg.totalSize);
        result.setInt("total", searchArg.totalSize.value);
        result.setInt("rt", Errno.OK);
        result.setString("msg", "获取员工列表成功！");
        result.setBoolean("success", true);
        result.setList("dataList", staffList);
        return result.toJson();
    }


    /*设置员工权限*/
    private String setStaffAuth(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        Param result = new Param();
        HdOss hdOss = (HdOss) Core.getCorpKit(1, Kid.HD_OSS);
        Param staff = hdOss.getStaff(sid);
        int rt = 0;
        if (staff == null || staff.isEmpty()) {
            Log.logErr("staff err = %s", staff);
            result.setInt("rt", -1);
            result.setString("msg", "员工不存在");
            result.setBoolean("success", false);
            return result.toJson();
        }
        int auth = staff.getInt(HdOssStaffDef.Info.AUTH, 0);

        //权限校验
        if (!Misc.checkBit(auth, HdOssStaffDef.Auth.ALL) && !Misc.checkBit(auth, HdOssStaffDef.Auth.HD_SALE_MANAGE) && !Misc.checkBit(auth, HdOssStaffDef.Auth.HD_PRODUCT_MANAGE)) {
            Log.logStd("have not auth = %s", staff);
            result.setInt("rt", -1);
            result.setString("msg", "没有权限！");
            result.setBoolean("success", false);
            return result.toJson();
        }
        //获取要更改权限的员工账号
        String acct = request.getParameter("acct");
        //要更改的权限
        int newAuth = HdOssStaffDef.Auth.getAuthFlagByName(request.getParameter("type"));
        if (newAuth == -1) {
            Log.logErr("HdOssStaffDef no such auth define = %s", staff);
            result.setInt("rt", -1);
            result.setString("msg", "没有权限！");
            result.setBoolean("success", false);
            return result.toJson();
        }
        //员工sid
        int sidForAlter = Parser.parseInt(request.getParameter("sid"), 0);
        //checked状态（true/false）
        boolean checked = Parser.parseBoolean(request.getParameter("checked"), false);
        //原标志位
        int oldAuth = Parser.parseInt(request.getParameter("updateFlag"), 0);
        //更新权限后的标志位
        int updateAuth = Misc.setFlag(oldAuth, newAuth, checked);
        //查询条件
        ParamMatcher matcher = new ParamMatcher(HdOssStaffDef.Info.SID, ParamMatcher.EQ, sidForAlter);
        //更新信息
        Param updateInfo = new Param();
        updateInfo.setInt("auth", updateAuth);
        Log.logDbg("yansen Misc.checkBit(updateAuth,HdOssStaffDef.Auth.HD_SALE)=%s", Misc.checkBit(updateAuth, HdOssStaffDef.Auth.HD_SALE));
		/*if(Misc.checkBit(updateAuth,HdOssStaffDef.Auth.HD_SALE)){
			//设置销售权限的话，需要看看这个销售是否存在于销售表，不然需要新增销售。
			Param saleInfo = Acct.getStaffInfo(Web.getFaiCid(), acct);
			int saleSid = saleInfo.getInt(StaffDef.Info.SID, 0);
			Param ossStaff = hdOss.getStaff(saleSid);
			SearchArg searchArgSale = new SearchArg();
			searchArgSale.matcher = new ParamMatcher("sid",ParamMatcher.EQ,saleSid);
			FaiList<Param> hdSaleList = new FaiList<Param>();
			PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
			cli.init();
			FaiList<String> fieldList = new FaiList<String>();
			fieldList.add("*");
			rt = cli.getSalesList(fieldList,searchArgSale, hdSaleList);
			if(rt != Errno.OK ){
				Log.logErr(rt, "get hdSale err");
			}
			Log.logStd("yansen ----设置销售权限 sid=%s matcher=%s saleInfo=%s",sid,matcher,ossStaff);
			if(hdSaleList == null || hdSaleList.size() == 0){
				Param info = new Param();
				info.setInt(HdSaleDef.Info.SID,saleSid);
				info.setString(HdSaleDef.Info.ACCT,saleInfo.getString("sacct"));
				info.setString(HdSaleDef.Info.NICK_NAME,saleInfo.getString("name"));
				info.setInt(HdSaleDef.Info.LIMIT_COUNT,20);
				info.setInt(HdSaleDef.Info.STATUS,0);
				rt = cli.addSale(info);
				Log.logStd("yansen 新增销售 rt =%s,sid=%s info=%s",rt,sid,info);
				if(rt != Errno.OK ){
					return "{\"success\":true, \"msg\":\"设置权限失败：新增员工到销售库失败\"}";
				}
			}
		}*/
        //调用设置员工权限接口
        rt = hdOss.setStaff(matcher, updateInfo);
        if (rt != Errno.OK) {
            result.setInt("rt", rt);
            result.setString("msg", "设置员工权限失败！");
            result.setBoolean("success", false);
            return result.toJson();
        }

        result.setInt("rt", Errno.OK);
        result.setString("msg", "设置员工权限成功！");
        result.setBoolean("success", true);
        return result.toJson();
    }

    /**
     *	销售arpu AB 页面 saleArpuAB.jsp
     */
    private String getSaleArpuAB(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        String se_acct = Parser.parseString(request.getParameter("se_sacct"), "all");
        String sort = Parser.parseString(request.getParameter("sort"), HdSaleArpuDef.Info.UP_ARPU);
        boolean export = Parser.parseBoolean(request.getParameter("export"), false);

        FaiList<Param> saleList = new FaiList<Param>();//转正销售信息---> hdSale表
        FaiList<String> sacctList = new FaiList<String>();//转正销售账号
        FaiList<Integer> sidList = new FaiList<Integer>();//转正销售sid

        Param arpuParam = new Param(true);//最终返回结果
        Param sidToAcctParam = new Param(true);//根据sid拿acct
        Param acctToParam = new Param(true);//根据acct拿param
        Param reveiveCountParam = new Param(true);//销售领取数量
        Param virtualSaleParam = new Param();//虚拟销售信息（price,receiveCount,arpu）


        Param resultInfo = new Param();
        // 设置时间
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        now.add(Calendar.DAY_OF_YEAR, -15);

        String begDateStr = Parser.parseString(request.getParameter("receiveDateBeg"), Parser.parseSimpleTime(now));
        String endDateStr = Parser.parseString(request.getParameter("receiveDateEnd"), Parser.parseSimpleTime(end));
        String receiveBeg = Parser.parseString(request.getParameter("receiveBeg"), Parser.parseSimpleTime(now));
        String receiveEnd = Parser.parseString(request.getParameter("receiveEnd"), Parser.parseSimpleTime(end));

        Log.logStd("unique-test begDateStr = %s , endDateStr = %s , receiveBeg = %s , receiveEnd = %s", begDateStr, endDateStr, receiveBeg, receiveEnd);

        Calendar begDate = Parser.parseCalendar(begDateStr + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Calendar endDate = Parser.parseCalendar(endDateStr + " 23:59:59", "yyyy-MM-dd HH:mm:ss");

        Calendar begCal = Parser.parseCalendar(receiveBeg + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Calendar endCal = Parser.parseCalendar(receiveEnd + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        Dao ossDao = WebOss.getOssBsDao();
        try {
            //获取所有转正员工
            saleList = ossDao.executeQuery("select * from hdSale where status=1");
        } catch (Exception e) {
            Log.logErr(e);
        } finally {
            ossDao.close();
        }
        for (Param staff : saleList) {
            String sacct = staff.getString(HdSaleDef.Info.ACCT);
            sacctList.add(sacct);
            sidToAcctParam.setString(staff.getInt(HdSaleDef.Info.SID) + "", sacct);
            acctToParam.setParam(sacct, staff);
            sidList.add(staff.getInt(HdSaleDef.Info.SID));
        }
        //领取类型是电话销售，随机分配旧资源、申请领取-审批
        FaiList<Integer> attrTypeList = new FaiList<Integer>();
        attrTypeList.add(HdSaleRecordDef.AttrType.PHONE_ALLOT);
        attrTypeList.add(HdSaleRecordDef.AttrType.PHONE_PERSON);
        attrTypeList.add(HdSaleRecordDef.AttrType.APPROVE);

        // 领取客户情况 计算arpu值用
        FaiList<Param> preSaleActionList = null;
        FaiList<Integer> aidIntList = new FaiList<Integer>();//客户aidList
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "acctPreSaleHd";
        selectArg.field = "*";
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.GE, begDate);
        searchArg.matcher.and(PreSaleHdDef.Info.RECEIVE_TIME, ParamMatcher.LE, endDate);
        searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, sacctList);
        //排除标签为公众号助手的资源。
        searchArg.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.NE, PreSaleHdDef.Tag.OPENMP_RESOURCE); 
		searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
		searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
        //searchArg.matcher.and("attrType",ParamMatcher.IN,attrTypeList);
        selectArg.searchArg = searchArg;
        ossDao = WebOss.getOssBsDao();
        try {
            preSaleActionList = ossDao.select(selectArg);//查出所有销售领取的资源
        } finally {
            ossDao.close();
        }


        selectArg = new Dao.SelectArg();
        selectArg.table = "hdSaleRecord";
        selectArg.field = "sacct , count(1) as releaseCount";
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, begDate);
        searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, endDate);
        searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
        //排除标签为公众号助手的资源。
        searchArg.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.NE, PreSaleHdDef.Tag.OPENMP_RESOURCE); 
		searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
		searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
        FaiList<Integer> releaseTypeList = new FaiList<Integer>();
        releaseTypeList.add(HdSaleRecordDef.AttrType.RELEASE_PERSON);
        releaseTypeList.add(HdSaleRecordDef.AttrType.RELEASE_DEAL);
        searchArg.matcher.and("attrType", ParamMatcher.IN, releaseTypeList);
        selectArg.searchArg = searchArg;
        selectArg.group = "sacct";
        ossDao = WebOss.getOssBsDao();
        FaiList<Param> releaseParamList = null;
        try {
            releaseParamList = ossDao.select(selectArg);//查出所有销售领取的资源
        } finally {
            ossDao.close();
        }

        //领取数量统计
        selectArg = new Dao.SelectArg();
        selectArg.table = "hdSaleRecord";
        selectArg.field = "sacct,sum(if(flag&32,1,0)) AS allotACount," +
                "sum(if(flag&64,1,0)) AS allotBCount," +
                "sum(if(flag&2,1,0)) AS awardCount," +
                "sum(if(attrType=11,1,0)) AS allotCount," +
                "sum(if(flag&4 &&((DAYOFWEEK(receiveTime) <> 1 && DAYOFWEEK(receiveTime) <> 7)),1,0)) AS averageCount," +
                "sum(if((DAYOFWEEK(receiveTime) = 1 || DAYOFWEEK(receiveTime) = 7),1,0)) AS weekendCount,"+
                "sum(if(tag=80 or flag&1024,1,0)) as mpCount";
        SearchArg searchArg2 = new SearchArg();
        searchArg2.matcher = new ParamMatcher();
        searchArg2.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, begCal);
        searchArg2.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, endCal);
        searchArg2.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
        searchArg2.matcher.and("attrType", ParamMatcher.IN, attrTypeList);
        searchArg2.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.NE, PreSaleHdDef.Tag.OPENMP_RESOURCE); 
        searchArg2.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
		searchArg2.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
        selectArg.searchArg = searchArg2;
        selectArg.group = "sacct";
        Log.logStd("sql = %s", selectArg.getSql());
        FaiList<Param> receCount = null;
        ossDao = WebOss.getOssBsDao();
        try {
            receCount = ossDao.select(selectArg);
        } finally {
            ossDao.close();
        }
        Log.logStd("rece count =%d", receCount.size());
        for (Param p : receCount) {
            String recordAcct = p.getString("sacct", "");
            if (PreSaleHdDef.BSS.isVirtualSale(recordAcct)) {
                virtualSaleParam.setInt("allotACount", virtualSaleParam.getInt("allotACount", 0) + p.getInt("allotACount", 0));
                virtualSaleParam.setInt("allotBCount", virtualSaleParam.getInt("allotBCount", 0) + p.getInt("allotBCount", 0));
                virtualSaleParam.setInt("awardCount", virtualSaleParam.getInt("awardCount", 0) + p.getInt("awardCount", 0));
                virtualSaleParam.setInt("allotCount", virtualSaleParam.getInt("allotCount", 0) + p.getInt("allotCount", 0));
                virtualSaleParam.setInt("averageCount", virtualSaleParam.getInt("averageCount", 0) + p.getInt("averageCount", 0));
                virtualSaleParam.setInt("weekendCount", virtualSaleParam.getInt("weekendCount", 0) + p.getInt("weekendCount", 0));
                virtualSaleParam.setInt("mpCount", virtualSaleParam.getInt("mpCount", 0) + p.getInt("mpCount", 0));
            }
            Param staff = acctToParam.getParam(recordAcct, new Param());
            staff.setInt("allotACount", p.getInt("allotACount", 0));
            staff.setInt("allotBCount", p.getInt("allotBCount", 0));
            staff.setInt("awardCount", p.getInt("awardCount", 0));
            staff.setInt("allotCount", p.getInt("allotCount", 0));
            staff.setInt("averageCount", p.getInt("averageCount", 0));
            staff.setInt("weekendCount", p.getInt("weekendCount", 0));
            staff.setInt("mpCount",p.getInt("mpCount",0));//统计公众号助手的数量，计算arpu时排除
            //staff.setParam(recordAcct, staff);
        }
 
        //助手资源的aid，等下订单查询排除这部分资源
        FaiList<Integer> mpAidList = new FaiList<Integer>();
        //1.获取aid列表作为订单查询条件
        //2.统计销售领取数量
        for (Param item : preSaleActionList) {
            String acct = item.getString(PreSaleHdDef.Info.SALES_ACCT);
            int aidTemp = item.getInt(PreSaleHdDef.Info.AID,0);
            aidIntList.add(item.getInt("aid", 0));
            int receiveCount = 0;
            if (PreSaleHdDef.BSS.isVirtualSale(acct)) {//虚拟销售总的领取数
                receiveCount = virtualSaleParam.getInt("receiveCount", 0);
                virtualSaleParam.setInt("receiveCount", ++receiveCount);
            }
            receiveCount = reveiveCountParam.getInt(acct, 0);
            reveiveCountParam.setInt(acct, ++receiveCount);
        }
        Log.logStd("before add releaseCount reveiveCountParam = %s,preSaleActionList=%s ", reveiveCountParam,preSaleActionList.size());

//        //排除新账号成交额
//        Log.logStd("排除新账号前的aidIntList size = %d", aidIntList.size());
//        FaiList<Param> approveList = new FaiList<Param>();
//        ossDao = WebOss.getOssBsDao();
//        try {
//            Dao.SelectArg approveArg = new Dao.SelectArg();
//            approveArg.table = "hdSaleApprove";
//            approveArg.field = "aid,oldAid";
//            ParamMatcher approveMatcher = new ParamMatcher();
//            approveMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
//            approveMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
//            approveMatcher.and(HdSaleApproveDef.Info.AID, ParamMatcher.IN, aidIntList);
//            approveArg.searchArg = new SearchArg();
//            approveArg.searchArg.matcher = approveMatcher;
//            approveList = ossDao.select(approveArg);
//            Log.logStd("新账号数量有 %d 个", approveList.size());
//        } finally {
//            ossDao.close();
//        }
//        HashMap<Integer, Integer> approveMap = new HashMap<Integer, Integer>();
//        FaiList<Integer> approveOldAidList = new FaiList<Integer>();
//        for (Param p : approveList) {
//            int aid = p.getInt(HdSaleApproveDef.Info.AID);
//            int oldAid = p.getInt(HdSaleApproveDef.Info.OLD_AID);
//            approveMap.put(oldAid, aid);
//            approveOldAidList.add(oldAid);
//        }
//
//        //查领取时间不在时间范围内的旧账号
//        FaiList<Param> oldReceiveList = new FaiList<Param>();
//        ossDao = WebOss.getOssBsDao();
//        try {
//            Dao.SelectArg oldAidArg = new Dao.SelectArg();
//            oldAidArg.table = "hdSaleRecord";
//            oldAidArg.field = "aid,receiveTime,sacct";
//            ParamMatcher oldAidMatcher = new ParamMatcher();
//            oldAidMatcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.IN, approveOldAidList);
//            oldAidMatcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, attrTypeList);
//            oldAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LT, begDate);
//            oldAidArg.searchArg = new SearchArg();
//            oldAidArg.searchArg.matcher = oldAidMatcher;
//            oldReceiveList = ossDao.select(oldAidArg);
//        } finally {
//            ossDao.close();
//        }
//        int tempNewAidCount = 0;//旧账号不在时间范围内数量
//        for (Param p : oldReceiveList) {
//            Integer oldAid = p.getInt(HdSaleRecordDef.Info.AID);
//            Integer aid = approveMap.get(oldAid);
//            aidIntList.remove(aid);
//            String acct = p.getString(HdSaleRecordDef.Info.SACCT, "");
//            int receiveCount = reveiveCountParam.getInt(acct, 0);//领取数要减去新账号的
//            reveiveCountParam.setInt(acct, --receiveCount);
//            tempNewAidCount++;
//            Log.logStd("aid为%d是新账号且旧账号（%d）不在时间范围内，不计入arpu值 销售：%s", aid, oldAid, acct);
//        }
//        Log.logStd("共有%d个旧账号不在时间范围内", tempNewAidCount);
//        Log.logStd("排除新账号后的aidIntList size = %d", aidIntList.size());
//
//
//        //检查是否有客户用新账号成交。有的话看新账号领取时间是否在时间范围内，是的话就算入加入新账号的aid
//        Log.logStd("加入新账号前的aidIntList size = %d", aidIntList.size());
//        FaiList<Param> oldApproveList = new FaiList<Param>();
//        ossDao = WebOss.getOssBsDao();
//        try {
//            Dao.SelectArg approveArg = new Dao.SelectArg();
//            approveArg.table = "hdSaleApprove";
//            approveArg.field = "aid,oldAid";
//            ParamMatcher approveMatcher = new ParamMatcher();
//            approveMatcher.and(HdSaleApproveDef.Info.APPROVE_STYLE, ParamMatcher.EQ, HdSaleApproveDef.ApproveStyle.OLD);//新旧账号
//            approveMatcher.and(HdSaleApproveDef.Info.STATUS, ParamMatcher.EQ, HdSaleApproveDef.ApproveStatus.AGREE);//同意
//            approveMatcher.and(HdSaleApproveDef.Info.OLD_AID, ParamMatcher.IN, aidIntList);
//            approveArg.searchArg = new SearchArg();
//            approveArg.searchArg.matcher = approveMatcher;
//            approveList = ossDao.select(approveArg);
//            Log.logStd("旧账号的数量有 %d 个", approveList.size());
//            Log.logStd("旧账号aid : %s", approveList);
//        } finally {
//            ossDao.close();
//        }
//        FaiList<Integer> newAidList = new FaiList<Integer>();
//        for (Param p : approveList) {
//            int newAid = p.getInt(HdSaleApproveDef.Info.AID, 0);
//            newAidList.add(newAid);
//        }
//
//        //不在时间范围内的新账号要加入aidList,和增加对应销售的领取数
//        oldReceiveList.clear();
//        ossDao = WebOss.getOssBsDao();
//        try {
//            Dao.SelectArg oldAidArg = new Dao.SelectArg();
//            oldAidArg.table = "hdSaleRecord";
//            oldAidArg.field = "aid,receiveTime,sacct";
//            ParamMatcher oldAidMatcher = new ParamMatcher();
//            oldAidMatcher.and(HdSaleRecordDef.Info.AID, ParamMatcher.IN, newAidList);
//            oldAidMatcher.and(HdSaleRecordDef.Info.ATTR_TYPE, ParamMatcher.IN, attrTypeList);
//            oldAidMatcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, endDate);
//            oldAidArg.searchArg = new SearchArg();
//            oldAidArg.searchArg.matcher = oldAidMatcher;
//            oldReceiveList = ossDao.select(oldAidArg);
//        } finally {
//            ossDao.close();
//        }
//        tempNewAidCount = 0;
//        for (Param p : oldReceiveList) {
//            Integer newAid = p.getInt(HdSaleRecordDef.Info.AID);
//            aidIntList.add(newAid);
//            String acct = p.getString(HdSaleRecordDef.Info.SACCT, "");
//            int receiveCount = reveiveCountParam.getInt(acct, 0);//领取数要加上新账号的
//            reveiveCountParam.setInt(acct, ++receiveCount);
//            tempNewAidCount++;
//            Log.logStd("新账号为 %d , 销售为 %s", newAid, acct);
//        }
//        Log.logStd("共有%d个新账号不在时间范围内", tempNewAidCount);
//        Log.logStd("加入新账号后的aidIntList size = %d", aidIntList.size());

        //加上每月释放
        for (Param p : releaseParamList) {
            String acct = p.getString(HdSaleRecordDef.Info.SACCT, "");
            int receiveCount = reveiveCountParam.getInt(acct, 0);
            reveiveCountParam.setInt(acct, p.getInt("releaseCount", 0) + receiveCount);
        }
        Log.logStd("after add releaseCount reveiveCountParam = %s", reveiveCountParam);
        //付费数据 ， 成单数（要大于380元，并且一个用户只算一单)，另外查以免影响付款金额(付款金额包括小于380的单)
        FaiList<Param> orderList = new FaiList<Param>();
        FaiList<Param> payCountList = null;
        int payTimeStart = (int) (begDate.getTimeInMillis() / 1000);
        int payTimeEnd = (int) (endDate.getTimeInMillis() / 1000);
        Dao bssMainDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg selectArgOrder = new Dao.SelectArg();
            selectArgOrder.table = "acctOrderItem";
            selectArgOrder.field = "sum(if(productId=154,price*0.3,price))as price,salesSid,count(1)as payCount";
            selectArgOrder.searchArg = new SearchArg();
            ParamMatcher orderMatcher = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, payTimeStart);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, payTimeEnd);
            orderMatcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, sidList);
            orderMatcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
            orderMatcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.NO_DEL);
            selectArgOrder.searchArg.matcher = orderMatcher;
            selectArgOrder.group = "salesSid";
            orderList = bssMainDao.select(selectArgOrder);//查询付费订单，付款数。

            //单独查询成单数，多次购买的用户只算一单。
            selectArgOrder = new Dao.SelectArg();
            selectArgOrder.table = "acctOrderItem";
            selectArgOrder.field = "salesSid,count(distinct aid)as payCount";
            selectArgOrder.group = "salesSid";
            //销售说金额大于380才成一单
            orderMatcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GE, 380);
            selectArgOrder.searchArg.matcher = orderMatcher;
            payCountList = bssMainDao.select(selectArgOrder);
            //Log.logStd("li test getSaleArpuAB payCountList=%s", payCountList);

            //把付款数拼接到orderList
            for (Param p : orderList) {
                Param payCountTemp = Misc.getFirst(payCountList, "salesSid", p.getInt("salesSid", 0));
                p.assign(payCountTemp, "payCount");
            }
            //Log.logStd("li test getSaleArpuAB orderList=%s", orderList);

        } catch (Exception e) {
            Log.logErr("getSaleArpuAB err : %s", e);
            PreSaleHdDef.printErr(e);
        } finally {
            bssMainDao.close();
        }
        if (orderList == null) {
            Log.logErr("serach order err order list is null");
        }
        Log.logStd("all sale order  = %s", orderList);

        //查A、B类资源资源aid  ->  查AB类付费金额
        FaiList<Param> abResource = new FaiList<Param>();
        ossDao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg abSelect = new Dao.SelectArg();
            abSelect.table = "hdSaleRecord";
            abSelect.field = "distinct aid,flag,sacct";
            SearchArg abSearchArg = new SearchArg();
            abSearchArg.matcher = new ParamMatcher();
            abSearchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, begDate);
            abSearchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, endDate);
            abSearchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.IN, sacctList);
            abSearchArg.matcher.and("attrType", ParamMatcher.IN, attrTypeList);
            abSearchArg.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.NE, PreSaleHdDef.Tag.OPENMP_RESOURCE);
			abSearchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
			abSearchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
            abSelect.searchArg = abSearchArg;
            abResource = ossDao.select(abSelect);
            Log.logStd("ab resource size = %d", abResource.size());
        } finally {
            ossDao.close();
        }

        FaiList<Integer> aTypeAid = new FaiList<Integer>();
        FaiList<Integer> bTypeAid = new FaiList<Integer>();
        HashMap<String, Integer> aTypeAcctReceiveCountMap = new HashMap<String, Integer>();
        HashMap<String, Integer> bTypeAcctReceiveCountMap = new HashMap<String, Integer>();
        for (Param p : abResource) {
            int aid = p.getInt("aid", 0);
            int flag = p.getInt("flag", 0);
            String acct = p.getString(HdSaleRecordDef.Info.SACCT, "");
            if (Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_A)) {
                aTypeAid.add(aid);
                if (aTypeAcctReceiveCountMap.containsKey(acct)) {
                    int count = aTypeAcctReceiveCountMap.get(acct);
                    aTypeAcctReceiveCountMap.put(acct, ++count);
                } else {
                    aTypeAcctReceiveCountMap.put(acct, 1);
                }
        }else if(Misc.checkBit(flag, HdSaleRecordDef.Flag.TYPE_B)){
                bTypeAid.add(aid);
                if (bTypeAcctReceiveCountMap.containsKey(acct)) {
                    int count = bTypeAcctReceiveCountMap.get(acct);
                    bTypeAcctReceiveCountMap.put(acct, ++count);
                } else {
                    bTypeAcctReceiveCountMap.put(acct, 1);
                }
            }
        }
        //Log.logStd("li test bTypeAcctReceiveCountMap=%s ",bTypeAcctReceiveCountMap);
        Log.logStd("a type aid list size = %d", aTypeAid.size());
        Log.logStd("b type aid list size = %d", bTypeAid.size());

        //a类付费金额
        FaiList<Param> aTypeOrder = new FaiList<Param>();
        bssMainDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg selectArgOrder = new Dao.SelectArg();
            selectArgOrder.table = "acctOrderItem";
            selectArgOrder.field = "sum(if(productId=154,price*0.3,price))as price,salesSid";
            selectArgOrder.group = "salesSid";
            selectArgOrder.searchArg = new SearchArg();
            ParamMatcher orderMatcher = new ParamMatcher("aid", ParamMatcher.IN, aTypeAid);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, payTimeStart);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, payTimeEnd);
            orderMatcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, sidList);
            orderMatcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
            orderMatcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.NO_DEL);
            selectArgOrder.searchArg.matcher = orderMatcher;
            aTypeOrder = bssMainDao.select(selectArgOrder);//查询付费订单
        } catch (Exception e) {
            Log.logErr("yansen e=%s", e);
            PreSaleHdDef.printErr(e);
        } finally {
            bssMainDao.close();
        }

        //b类付费金额
        FaiList<Param> bTypeOrder = new FaiList<Param>();
        bssMainDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg selectArgOrder = new Dao.SelectArg();
            selectArgOrder.table = "acctOrderItem";
            selectArgOrder.field = "sum(if(productId=154,price*0.3,price))as price,salesSid";
            selectArgOrder.group = "salesSid";
            selectArgOrder.searchArg = new SearchArg();
            ParamMatcher orderMatcher = new ParamMatcher("aid", ParamMatcher.IN, bTypeAid);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.GE, payTimeStart);
            orderMatcher.and(BssStatDef.OrderItemInfo.PAY_TIME, ParamMatcher.LE, payTimeEnd);
            orderMatcher.and(BssStatDef.OrderItemInfo.SALES_SID, ParamMatcher.IN, sidList);
            orderMatcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
            orderMatcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, BssStatDef.DelStatus.NO_DEL);
            selectArgOrder.searchArg.matcher = orderMatcher;
            bTypeOrder = bssMainDao.select(selectArgOrder);//查询付费订单
            Log.logStd("li test size of bTypeOrder = %s",bTypeOrder.size());
        } catch (Exception e) {
            Log.logErr("yansen e=%s", e);
            PreSaleHdDef.printErr(e);
        } finally {
            bssMainDao.close();
        }
        //填充acctOrderItem表没有付费记录的销售
        for (String acct : sacctList) {
            int reveiveCount = reveiveCountParam.getInt(acct, 0);
            Param innerParam = new Param();
            innerParam.setDouble("arpu", 0.0);
            innerParam.setDouble("price", 0.0);
            innerParam.setString("payRate", 0.0 + "%");
            innerParam.setInt("receiveCount", reveiveCount);
            innerParam.setInt("payCount", 0);
            arpuParam.setParam(acct, innerParam);
        }


        //计算真实销售的arpu = sum(price)/receiveCount
        for (Param p : orderList) {
            int sid = p.getInt("salesSid", 0);
            int payCount = p.getInt("payCount", 0);
            Double price = p.getDouble("price", 0.0);
            String acct = sidToAcctParam.getString(sid + "", "");
            Double arpu = 0.0;
            Double payRate = 0.0;
            int receiveCount = reveiveCountParam.getInt(acct, 0);
            if (PreSaleHdDef.BSS.isVirtualSale(acct)) {//虚拟销售总成交额
                Double virtualPrice = virtualSaleParam.getDouble("price", 0.0) + price;
                virtualSaleParam.setDouble("price", virtualPrice);
                int virtualPayCount = virtualSaleParam.getInt("payCount", 0) + payCount;
                virtualSaleParam.setInt("payCount", virtualPayCount);
            }
            if (receiveCount != 0) {
                arpu = Num.round(price / receiveCount, 2);
                payRate = Num.round(((double) payCount / receiveCount) * 100, 2);
            }
            Param innerParam = new Param();
            innerParam.setDouble("arpu", arpu);
            innerParam.setDouble("price", price);
            innerParam.setString("payRate", payRate + "%");
            innerParam.setInt("receiveCount", receiveCount);
            innerParam.setInt("payCount", payCount);

            arpuParam.setParam(acct, innerParam);
        }

        //存A类领取数量
        Set<String> aTypeKey = aTypeAcctReceiveCountMap.keySet();
        for (String key : aTypeKey) {
            int aCount = aTypeAcctReceiveCountMap.get(key);
            Param innerParam = arpuParam.getParam(key, new Param());
            innerParam.setInt("aCount", aCount);
            if (PreSaleHdDef.BSS.isVirtualSale(key)) {//虚拟销售组A库
                int virtualACount = virtualSaleParam.getInt("aCount", 0);
                virtualACount += aCount;
                virtualSaleParam.setInt("aCount", aCount);
            }
        }

        //存B类领取数量
        Set<String> bTypeKey = bTypeAcctReceiveCountMap.keySet();
        for (String key : bTypeKey) {
            int bCount = bTypeAcctReceiveCountMap.get(key);
            Param innerParam = arpuParam.getParam(key, new Param());
            innerParam.setInt("bCount", bCount);

            if (PreSaleHdDef.BSS.isVirtualSale(key)) {//虚拟销售组A库
                int virtualACount = virtualSaleParam.getInt("bCount", 0);
                virtualACount += bCount;
                virtualSaleParam.setInt("bCount", bCount);
            }
        }
        //Log.logStd("li test  getSaleArpuAB aTypeAcctReceiveCountMap=%s",aTypeAcctReceiveCountMap);

        //存a类付费金额
        for (Param p : aTypeOrder) {
            int sid = p.getInt("salesSid", 0);
            Double price = p.getDouble("price", 0.0);
            String acct = sidToAcctParam.getString(sid + "", "");

            Param innerParam = arpuParam.getParam(acct, new Param());
            if (PreSaleHdDef.BSS.isVirtualSale(acct)) {//虚拟销售组A库
                Double virtualAPrice = virtualSaleParam.getDouble("aPrice", 0.0);
                virtualAPrice += price;
                virtualSaleParam.setDouble("aPrice", virtualAPrice);
            }
            innerParam.setDouble("aPrice", price);
            arpuParam.setParam(acct, innerParam);
        }

        //存b类付费金额
        for (Param p : bTypeOrder) {
            int sid = p.getInt("salesSid", 0);
            Double price = p.getDouble("price", 0.0);
            String acct = sidToAcctParam.getString(sid + "", "");
            Param innerParam = arpuParam.getParam(acct, new Param());

            if (PreSaleHdDef.BSS.isVirtualSale(acct)) {//虚拟销售组A库
                Double virtualAPrice = virtualSaleParam.getDouble("bPrice", 0.0);
                virtualAPrice += price;
                virtualSaleParam.setDouble("bPrice", virtualAPrice);

            }
            innerParam.setDouble("bPrice", price);
            arpuParam.setParam(acct, innerParam);
        }
        Log.logStd("真实销售arpu=%s,", arpuParam);

        Set<String> saleKeySet = arpuParam.keySet();
        for (String key : saleKeySet) {
            Param innerParam = arpuParam.getParam(key, new Param());
            int aCount = innerParam.getInt("aCount", 0);
            int bCount = innerParam.getInt("bCount", 0);
            Double aPrice = innerParam.getDouble("aPrice", 0.0);
            Double bPrice = innerParam.getDouble("bPrice", 0.0);
            if (aCount == 0) {
                innerParam.setDouble("aArpu", 0.0);
                //Log.logStd("li test  getSaleArpuAB %s aCount==0",key);
            } else {
                innerParam.setDouble("aArpu", Num.round(aPrice / aCount, 2));
            }
            if (bCount == 0) {
                innerParam.setDouble("bArpu", 0.0);
                //Log.logStd("li test  getSaleArpuAB %s bCount==0",key);
            } else {
                innerParam.setDouble("bArpu", Num.round(bPrice / bCount, 2));
            }
            if ((aCount + bCount) == 0) {
                innerParam.setDouble("abArpu", 0.0);
            } else {
                innerParam.setDouble("abArpu", Num.round((aPrice + bPrice) / (aCount + bCount), 2));
            }
            arpuParam.setParam(key, innerParam);
        }

        //计算虚拟销售arpu
        Double virtualPrice = virtualSaleParam.getDouble("price", 0.0);
        int virtualReceiveCount = virtualSaleParam.getInt("receiveCount", 0);
        int virtualPayCount = virtualSaleParam.getInt("payCount", 0);
        Double virtualAPrice = virtualSaleParam.getDouble("aPrice", 0.0);
        Double virtualBPrice = virtualSaleParam.getDouble("bPrice", 0.0);
        int virtualReceiveACount = virtualSaleParam.getInt("aCount", 0);
        int virtualReceiveBCount = virtualSaleParam.getInt("bCount", 0);
        double virtualPayRate = 0.0;
        if (virtualReceiveCount == 0) {
            virtualSaleParam.setDouble("arpu", 0.0);
        } else {
            virtualSaleParam.setDouble("arpu", Num.round(virtualPrice / virtualReceiveCount, 2));
            virtualPayRate = Num.round(((double) virtualPayCount / virtualReceiveCount) * 100, 2);
        }
        if (virtualReceiveACount == 0) {
            virtualSaleParam.setDouble("aArpu", 0.0);
        } else {
            virtualSaleParam.setDouble("aArpu", Num.round(virtualAPrice / virtualReceiveACount, 2));
        }
        if (virtualReceiveBCount == 0) {
            virtualSaleParam.setDouble("bArpu", 0.0);
        } else {
            virtualSaleParam.setDouble("bArpu", Num.round(virtualBPrice / virtualReceiveBCount, 2));
        }
        if ((virtualReceiveACount + virtualReceiveBCount) == 0) {
            virtualSaleParam.setDouble("abArpu", 0.0);
        } else {
            virtualSaleParam.setDouble("abArpu", Num.round((virtualAPrice + virtualBPrice) / (virtualReceiveACount + virtualReceiveBCount), 2));
        }
        virtualSaleParam.setString("nickName", "虚拟销售组");
        virtualSaleParam.setString("acct", "hdSaleGroup");
        virtualSaleParam.setString("payRate", virtualPayRate + "%");
        virtualSaleParam.setDouble("upArpu", 0.0);
        virtualSaleParam.setInt("typeA", 0);
        virtualSaleParam.setInt("typeB", 0);
        Log.logStd("虚拟销售组arpu = %s", virtualSaleParam);


        //计算权重
        FaiList<Param> dataList = new FaiList<Param>();
        Set<String> keySet = arpuParam.keySet();
        for (String key : keySet) {
            Param innerParam = arpuParam.getParam(key);
            Double arpu = innerParam.getDouble("arpu", 0.0);
            Double virtualArpu = virtualSaleParam.getDouble("arpu", 0.0);
            Double upArpu = arpu - virtualArpu;
            if (upArpu < 0) {
                innerParam.setInt("typeA", 1);
                innerParam.setInt("typeB", 5);
            } else if (upArpu <= 5) {
                innerParam.setInt("typeA", 1);
                innerParam.setInt("typeB", 6);
            } else if (upArpu <= 10) {
                innerParam.setInt("typeA", 2);
                innerParam.setInt("typeB", 6);
            } else if (upArpu <= 12.5) {
                innerParam.setInt("typeA", 4);
                innerParam.setInt("typeB", 7);
            } else if (upArpu <= 15) {
                innerParam.setInt("typeA", 5);
                innerParam.setInt("typeB", 7);
            } else if (upArpu <= 17.5) {
                innerParam.setInt("typeA", 6);
                innerParam.setInt("typeB", 9);
            } else if (upArpu <= 20) {
                innerParam.setInt("typeA", 7);
                innerParam.setInt("typeB", 9);
            } else if (upArpu <= 25) {
                innerParam.setInt("typeA", 9);
                innerParam.setInt("typeB", 10);
            } else if (upArpu <= 30) {
                innerParam.setInt("typeA", 10);
                innerParam.setInt("typeB", 10);
            } else if (upArpu > 30) {
                innerParam.setInt("typeA", 11);
                innerParam.setInt("typeB", 12);
            }
            if (PreSaleHdDef.BSS.isVirtualSale(key)) {
                innerParam.setInt("typeA", 4);
                innerParam.setInt("typeB", 7);
            }
            //虚拟销售权重设置
            if (key.equals("hdsale1")) {
                innerParam.setInt("typeA", 11);
                innerParam.setInt("typeB", 12);
            }
            if (key.equals("hdsale2")) {
                innerParam.setInt("typeA", 6);
                innerParam.setInt("typeB", 9);
            }
            if (key.equals("hdsale3")) {
                innerParam.setInt("typeA", 1);
                innerParam.setInt("typeB", 5);
            }


            arpuParam.setParam(key, innerParam);
            Param staff = acctToParam.getParam(key, new Param());
            String nickName = staff.getString("nickName", "");
            innerParam.setDouble("upArpu", Num.ceil(upArpu, 2));
            innerParam.setString("acct", key);
            innerParam.setString("nickName", nickName);
            innerParam.setInt("allotACount", staff.getInt("allotACount", 0));
            innerParam.setInt("allotBCount", staff.getInt("allotBCount", 0));
            innerParam.setInt("awardCount", staff.getInt("awardCount", 0));
            innerParam.setInt("allotCount", staff.getInt("allotCount", 0));
            innerParam.setInt("averageCount", staff.getInt("averageCount", 0));
            innerParam.setInt("weekendCount", staff.getInt("weekendCount", 0));
            dataList.add(innerParam);
        }
        dataList.add(virtualSaleParam);

        Log.logStd("arpuParam = %s", arpuParam);

        SearchArg searchArgLimit = new SearchArg();
        ParamComparator cmpor = new ParamComparator(sort, true);
        searchArgLimit.cmpor = cmpor;
        searchArgLimit.matcher = new ParamMatcher();
        Searcher searcher = new Searcher(searchArgLimit);
        if (!"all".equals(se_acct)) {
            searchArgLimit.matcher.and(HdSaleDef.Info.ACCT, ParamMatcher.EQ, se_acct);
        }
        dataList = searcher.getParamList(dataList);

        if (export) {
            response.setContentType("application/x-excel");
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("客户A/B权重领取表.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString(HdSaleArpuDef.Info.NICK_NAME, "销售");
            cellKey.setString(HdSaleArpuDef.Info.RECEIVE_COUNT, "获取资源数");
            cellKey.setString(HdSaleArpuDef.Info.PAY_COUNT, "付费数");
            cellKey.setString("payRate", "付费率");
            cellKey.setString(HdSaleArpuDef.Info.PRICE, "付费金额");
            cellKey.setString(HdSaleArpuDef.Info.ARPU, "arpu");
            cellKey.setString("aArpu", "A类资源arpu");
            cellKey.setString("bArpu", "B类资源arpu");
            cellKey.setString("abArpu", "AB类资源arpu");
            cellKey.setString(HdSaleArpuDef.Info.UP_ARPU, "arpu提升");
            cellKey.setString("typeA", "A类权重");
            cellKey.setString("typeB", "B类权重");
            cellKey.setString("allotACount", "A类领取数量");
            cellKey.setString("allotBCount", "B类领取数量");
            cellKey.setString("awardCount", "奖励领取数量");
            cellKey.setString("averageCount", "平均分配数量");
            cellKey.setString("weekendCount", "周末领取数量");
            cellKey.setString("allotCount", "领取总数量");
            OssPoi.exportExcel(cellKey, dataList, outputStream);
            return "{\"success\":true}";
        }

        resultInfo.setBoolean("success", true);
        resultInfo.setInt("rt", Errno.OK);
        resultInfo.setList("dataList", dataList);
        return resultInfo.toJson();

    }

    /**
     *	销售arpu页面
     *	getSaleArpu   saleArpu.jsp
     */
    private String getSaleArpu(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        Param resultInfo = new Param();
        String se_sacct = Parser.parseString(request.getParameter("se_sacct"), "all");
        String taGroup = Parser.parseString(request.getParameter("taGroup"), "all");
        String sort = Parser.parseString(request.getParameter("sort"), HdSaleArpuDef.Info.UP_ARPU);
        boolean export = Parser.parseBoolean(request.getParameter("export"), false);

        // 设置时间
        Calendar now = Calendar.getInstance();
        Calendar end = Calendar.getInstance();
        now.add(Calendar.DAY_OF_YEAR, -15);
        String begDate = Parser.parseString(request.getParameter("receiveDateBeg"), Parser.parseSimpleTime(now));
        String endDate = Parser.parseString(request.getParameter("receiveDateEnd"), Parser.parseSimpleTime(end));
        String receiveBeg = Parser.parseString(request.getParameter("receiveBeg"), Parser.parseSimpleTime(now));
        String receiveEnd = Parser.parseString(request.getParameter("receiveEnd"), Parser.parseSimpleTime(end));
        FaiList<Param> dataList = new FaiList<Param>();
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        if (!cli.init()) {
            resultInfo.setBoolean("success", false);
            resultInfo.setInt("rt", Errno.ERROR);
            return resultInfo.toJson();
        }
        cli.getArpuList(dataList, Parser.parseCalendar(begDate + " 00:00:00", "yyyy-MM-dd HH:mm:ss"), Parser.parseCalendar(endDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));*/
        SearchArg searchArgLimit = new SearchArg();
        ParamComparator cmpor = new ParamComparator(sort, true);
        searchArgLimit.cmpor = cmpor;
        searchArgLimit.matcher = new ParamMatcher();
        Searcher searcher = new Searcher(searchArgLimit);
        if (!"all".equals(se_sacct)) {
            searchArgLimit.matcher.and(HdSaleDef.Info.ACCT, ParamMatcher.EQ, se_sacct);
        }
        if (!"all".equals(taGroup)) {
            searchArgLimit.matcher.and(HdSaleArpuDef.Info.GROUP_NAME, ParamMatcher.EQ, taGroup);
        }
        for (Param item : dataList) {
            double rate = item.getDouble(HdSaleArpuDef.Info.RATE, 0.0);
            String rateStr = Num.ceil(rate * 100, 2) + "%";
            item.setString(HdSaleArpuDef.Info.RATE, rateStr);
            if (item.getDouble(HdSaleArpuDef.Info.UP_ARPU, 0.0) == 0.0) {
                item.setDouble(HdSaleArpuDef.Info.UP_ARPU, 0.0);
            }
        }
        dataList = searcher.getParamList(dataList);
        FaiList<Param> list = null;
        FaiList<Param> awardList = null;
        Dao ossDao = WebOss.getOssBsDao();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            String table = "hdSaleRecord";//ossbs库
            sltArg.table = table;
            sltArg.searchArg = new SearchArg();
            sltArg.searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.GE, receiveBeg + " 00:00:00");
            sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.RECEIVE_TIME, ParamMatcher.LE, receiveEnd + " 23:59:59");
            sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.GT, 0);
            //排除公众号助手资源
            sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.TAG, ParamMatcher.NE, PreSaleHdDef.Tag.OPENMP_RESOURCE);
			sltArg.searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND_NE,PreSaleHdDef.Flag.IS_MP_RESOURCE, PreSaleHdDef.Flag.IS_MP_RESOURCE);
			sltArg.searchArg.matcher.and(PreSaleHdDef.Info.BUSINESS, ParamMatcher.EQ, PreSaleHdDef.Business.HD);  //不计算悦客的
            long checkStartTime = Parser.parseCalendar(receiveBeg + " 00:00:00", "yyyy-MM-dd HH:mm:ss").getTimeInMillis();
            if (checkStartTime >= 1543939200000L) {//2018-12-5后生效
                ParamMatcher orMar = new ParamMatcher(HdSaleRecordDef.Info.FLAG, ParamMatcher.LAND, HdSaleRecordDef.Flag.WEIGHT_ALLOT, HdSaleRecordDef.Flag.WEIGHT_ALLOT);
                orMar.or(HdSaleRecordDef.Info.FLAG, ParamMatcher.LAND, HdSaleRecordDef.Flag.IS_AWARD, HdSaleRecordDef.Flag.IS_AWARD);
                sltArg.searchArg.matcher.and(orMar);
            }
            sltArg.group = "sacct,taGroupName";
            sltArg.field = "taGroupName,sacct as acct,count(1) as count";
            if (!"all".equals(se_sacct)) {
                sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.SACCT, ParamMatcher.EQ, se_sacct);
            }
            if (!"all".equals(taGroup)) {
                sltArg.searchArg.matcher.and("taGroupName", ParamMatcher.EQ, taGroup);
            }
            list = ossDao.select(sltArg);
            sltArg.searchArg.matcher.and(HdSaleRecordDef.Info.FLAG, ParamMatcher.LAND, HdSaleRecordDef.Flag.IS_AWARD, HdSaleRecordDef.Flag.IS_AWARD);
            awardList = ossDao.select(sltArg);

            Log.logDbg("yansen 	sltArg.matcher=%s list=%s", sltArg.searchArg.matcher, list);
        } finally {
            ossDao.close();
        }
        Param countParam = new Param();
        for (Param data : list) {
            String taGroupName = data.getString("taGroupName", "");
            String acct = data.getString("acct", "");
            int count = data.getInt("count", 0);
            countParam.setInt(taGroupName + "-" + acct, count);
        }
        Param countAwardParam = new Param();
        for (Param data : awardList) {
            String taGroupName = data.getString("taGroupName", "");
            String acct = data.getString("acct", "");
            int count = data.getInt("count", 0);
            countAwardParam.setInt(taGroupName + "-" + acct, count);
        }
        for (Param data : dataList) {
            String acct = data.getString("acct", "");
            String groupName = data.getString("groupName", "");
            int count = countParam.getInt(groupName + "-" + acct, 0);
            int awardCount = countAwardParam.getInt(groupName + "-" + acct, 0);
            data.setInt("receiveCountSelect", count);
            data.setInt("awardCount", awardCount);
            data.setInt("notInAwardCount", count - awardCount);
        }
        if (export) {
            response.setContentType("application/x-excel");
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("客户领取数据列表.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();

            // 创建导出的表头跟需要导出的数据KEY值
            Param cellKey = new Param();
            cellKey.setString(HdSaleArpuDef.Info.GROUP_NAME, "注册来源");
            cellKey.setString(HdSaleArpuDef.Info.NICK_NAME, "销售");
            cellKey.setString(HdSaleArpuDef.Info.RECEIVE_COUNT, "获取资源数");
            cellKey.setString(HdSaleArpuDef.Info.PAY_COUNT, "付费数");
            cellKey.setString(HdSaleArpuDef.Info.RATE, "付费率");
            cellKey.setString(HdSaleArpuDef.Info.PRICE, "付费金额");
            cellKey.setString(HdSaleArpuDef.Info.ARPU, "arpu");
            cellKey.setString(HdSaleArpuDef.Info.UP_ARPU, "arpu提升");
            cellKey.setString(HdSaleArpuDef.Info.N_NUM, "权重数量");
            cellKey.setString("receiveCountSelect", "领取数量（全部）");
            cellKey.setString("awardCount", "领取数量（奖励）");
            cellKey.setString("notInAwardCount", "领取数量（不含奖励）");
            OssPoi.exportExcel(cellKey, dataList, outputStream);
            return "{\"success\":true}";
        }
        resultInfo.setBoolean("success", true);
        resultInfo.setInt("rt", Errno.OK);
        resultInfo.setList("dataList", dataList);
        return resultInfo.toJson();
    }


    private String getYkInfo(HttpServletRequest request) throws Exception {
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        if (aid <= 0) {
            return "{\"success\":false, \"msg\":\"参数错误 \"}";
        }

        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        
    	Param info = sysPreSaleYk.getInfo(aid);

        Param data = new Param();
        data.setInt("intent", info.getInt(PreSaleHdDef.Info.INTENT, 0));
        data.setString("reason", info.getString(PreSaleHdDef.Info.DIS_BUY_REASON, ""));
        data.setBoolean("showReasonAdmin", Auth.checkFaiscoAuth("authPreSaleLeader", false));
        data.setString("reasonHref", "http://" + Web.getOssDomain() + "/cs/siteReason.jsp?type=12");
        String talkNextTime = Parser.parseSimpleTime(info.getCalendar(PreSaleHdDef.Info.TALK_NEXT_TIME));
        data.setString("intentName", PreSaleHdDef.getIntentName(info.getInt(PreSaleHdDef.Info.INTENT, 0)));
        data.setString("talkNextTime", talkNextTime);
        data.setInt("intentLevel", info.getInt(PreSaleHdDef.Info.INTENT_LEVEL, 0));
        
        int status = info.getInt(PreSaleYkDef.Info.STATUS, 0);
        String statusStr = "未知库";
        if(status == 1){
        	statusStr = "个人库";
        }else if(status == 2 ){
        	statusStr = "成交库";
        }
        data.setString("status", statusStr);

        int flag = info.getInt(PreSaleHdDef.Info.FLAG,0);
        if(Misc.checkBit(flag, PreSaleHdDef.Flag.SPECIAL_FOCUS)){
            data.setBoolean("isFocus",true);
        }else{
            data.setBoolean("isFocus",false);
        }

        if(Misc.checkBit(flag, PreSaleHdDef.Flag.BAN_SEND_MSG)){
            data.setBoolean("banMsg",true);
        }else{
            data.setBoolean("banMsg",false);
        }

        //手机号，邮箱，验证邮箱
        String loginSaleName = WebOss.getSacct();
        Param acctInfo = new Param();
        if (loginSaleName.equals(info.getString("salesAcct", ""))) {
            acctInfo.setBoolean("show", true);
//            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);//bss接口
//            Param aidInfo = sysBssStat.getAcctStatusInfo(aid);

            //迁移到新的查询方式
            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("*")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(AND.expr(EQ.of("aid", aid)))
                    .execute(Core.getFlow());
            Log.logDbg("execute:"+execute);
            Param aidInfo = execute.getData().get(0);

            String mobile = aidInfo.getString("reg_mobile", "");
            if (mobile == "" || mobile.length() != 11) {
                mobile = aidInfo.getString("reg_verify_mobile", "");
            }
            acctInfo.setString("mobile", mobile);//拿手机号
            acctInfo.setString("email", aidInfo.getString("reg_email", "无"));//拿邮箱
            FaiVerifiCation faiVerifiCation = (FaiVerifiCation) Core.getSysKit(Kid.SYS_VERIFICATION);
            int creatorSid = WebOss.getStaffCreatorSidByAid(aid);
            Param emailInfo = new Param();
            faiVerifiCation.getVerifiEmailInfoById(aid, creatorSid, emailInfo);
            acctInfo.setString("verifyEmail", emailInfo.getString(FaiVerifiCationDef.Email.EMAIL, "无"));
        } else {
            acctInfo.setBoolean("show", false);
        }
        FaiList<Param> list = new FaiList<Param>();
        list.add(data);
        list.add(acctInfo);

        return "{\"success\":true, \"data\":" + list.toJson() + "}";
    }

    private String getYkIntentList() throws Exception {
        FaiList<Integer> intentList = PreSaleYkDef.getIntentList();

        FaiList<Param> dataList = new FaiList<Param>();
        for (int value : intentList) {
            String text = PreSaleYkDef.getIntentName(value);
            dataList.add(new Param().setInt("value", value).setString("text", text));
        }
        return "{\"success\":true, \"data\":" + dataList.toJson() + "}";
    }

    private String getYkReasonList() throws Exception {
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);
		Log.logStd("sysPreSaleUtil = %s", sysPreSaleUtil);
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleUtilDef.Conf.TYPE, ParamMatcher.EQ, PreSaleUtilDef.ConfType.PRE_HD_REASON);
        searchArg.matcher.and(PreSaleUtilDef.Conf.SiteReason.DEL, ParamMatcher.EQ, 0);

        searchArg.cmpor = new ParamComparator(PreSaleUtilDef.Conf.SiteReason.WEIGHT, true);
        FaiList<Param> reasonList = sysPreSaleUtil.searchPreSaleConf(searchArg);

        Param data = null;
        FaiList<Param> dataList = new FaiList<Param>();
        for (Param p : reasonList) {
            int id = p.getInt(PreSaleUtilDef.Conf.ID, 0);
            int fatherId = p.getInt(PreSaleUtilDef.Conf.SiteReason.FATHER_ID, 0);
            String reason = p.getString(PreSaleUtilDef.Conf.SiteReason.REASON, "");

            if(reason.contains("游戏")){
            	continue;
            }
            
            data = new Param();
            data.setString("id", id + "");
            data.setString("reason", reason);
            data.setString("fatherId", fatherId + "");
            dataList.add(data);
        }

        return "{\"success\":true, \"data\":" + dataList.toJson() + "}";
    }

    private String runAllot(HttpServletRequest request) throws Exception {
        String runIdList = request.getParameter("runIdList");
        if (runIdList == null) {
            return "{\"success\":true, \"data\":" + "参数错误" + "}";
        }
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow() + 86);
        if (!cli.init()) {
            return "{\"success\":true, \"data\":" + "cli err" + "}";
        }
        int rt = cli.runAllot("[0]");
        if (rt != Errno.OK) {
            Log.logErr("yansen rt=%s", rt);
            return "{\"success\":false, \"data\":" + "rt err" + "}";
        } else {
            Log.logErr("yansen success rt=%s", rt);
        }*/
        return "{\"success\":true, \"data\":" + "成功" + "}";
    }

    private String delActivity(HttpServletRequest request) throws Exception {
        int size = 0;
	/*	Dao ossDao = WebOss.getOssBsDao();
		try{
			ossDao.setAutoCommit(false);
			ParamMatcher mat = new ParamMatcher("createTime",ParamMatcher.GT,"2018-11-13 09:14:00");
			mat.and("createTime",ParamMatcher.LT,"2018-11-13 19:14:00");
			mat.and("status","=",1);
			mat.and("taGroupName","=","活动引流");
			Dao.SelectArg sltArg = new Dao.SelectArg();
			String table = "acctPreSaleHd";//ossbs库
			sltArg.table = table;
			sltArg.searchArg.matcher = mat;
			FaiList<Param> list = ossDao.select(sltArg);
			Log.logDbg("yansen list size=%s",list.size());

			Ref<Integer> re = new Ref<Integer>();
			//ossDao.delete("acctPreSaleHd",mat,re);
			//ossDao.delete("hdSaleRecord",mat,re);
			size = re.value;
		}finally{
			if(size<500){
				ossDao.commit();
			}else{
				ossDao.rollback();
			}
			ossDao.close();
		}*/
        return "成功" + size;
    }

    private String getData100(HttpServletRequest request) throws Exception {
        int size = 0;
        Dao ossDao = WebOss.getOssBsDao();
        Dao bssMainDao = WebOss.getBssMainDaoMaster();
        try {
            String sql = "select distinct(acct.aid),mobile from acctStatus acct,hdTemplateGame hd where acct.aid = hd.aid and acct.openHdTime>1541003588 and acct.hdVersion=0 and acct.mobile is not null and acct.mobile<>''";
            FaiList<Param> mobileList = bssMainDao.executeQuery(sql);
            FaiList<Integer> aidIntList = new FaiList<Integer>();
            for (Param item : mobileList) {
                aidIntList.add(item.getInt("aid"));
            }

            ParamMatcher mat = new ParamMatcher("aid", ParamMatcher.IN, aidIntList);
            Dao.SelectArg sltArg = new Dao.SelectArg();
            String table = "hdSaleRecord";//ossbs库
            sltArg.table = table;
            sltArg.searchArg.matcher = mat;
            FaiList<Param> list = ossDao.select(sltArg);
            Param set = new Param();
            for (Param item : list) {
                set.setInt(item.getInt("aid") + "", 1);
            }

            Param mobileSet = new Param();
            FaiList<Param> mobile = bssMainDao.executeQuery("select * from hdInnerMobile");
            for (Param item : list) {
                mobileSet.setInt(item.getString("mobile"), 1);
            }

            for (Param item : mobileList) {
                String mobileitem = item.getString("mobile");
                if (set.getInt(item.getInt("aid") + "", -1) < 0 && mobileSet.getInt(mobileitem, -1) < 0) {
                    Log.logDbg("yansen aid=%s,mobile=%s", item.getInt("aid"), item.getString("mobile"));
                }
            }

            Log.logDbg("yansen list size=%s", list.size());
        } finally {
            ossDao.close();
            bssMainDao.close();
        }
        return "成功" + size;
    }

    private String getAllotRegular(HttpServletRequest request) throws Exception {
	    /*
	    int rt = Errno.ERROR;
		SysPreSaleHd sysPreSaleHd = (SysPreSaleHd)Core.getSysKit(Kid.SYS_PRESALE_HD);
		FaiList<Param> regularList = new FaiList<Param>();
		rt = sysPreSaleHd.getAllotRegular("*",new SearchArg(),regularList);
		if(rt != Errno.OK){
			return "{\"success\":false }";
		}
		//拿销售名字
		FaiList<String> fieldList = new FaiList<String>(){
			{
			    add("sid");
			    add("nickName");
			}
		};
		FaiList<Param> saleList = new FaiList<Param>();
		SearchArg searchArg = new SearchArg();
		searchArg.limit = 1000;//getSalesList 方法不能穿传searchArg，有时间需要改一下
		rt = sysPreSaleHd.getSalesList(fieldList,searchArg,saleList);
		if(rt != Errno.OK){
			return "{\"success\":false }";
		}
		Param saleNameParam = new Param(true);
		for(Param p:saleList){
		    int sid = p.getInt("sid",-1);
		    String name = p.getString("nickName","");
			saleNameParam.setString(sid+"",name);
		}
		for(Param p:regularList){
		    int sid = p.getInt("sid",-1);
		    if(saleNameParam.containsKey(sid+"")){
		        p.setString("name",saleNameParam.getString(sid+""));
			}
			p.setBoolean("visible",false);
		}*/
        //return "{\"success\":true, \"data\":" + regularList.toJson() + "}";
        return "";
    }

    //resourceAnalyze.jsp
    private String resourceAnalyze(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        int openHdTimeStart = Parser.parseInt(request.getParameter("openHdTimeStart"), -1);
        int openHdTimeEnd = Parser.parseInt(request.getParameter("openHdTimeEnd"), -1);
        int receiveTimeStart = Parser.parseInt(request.getParameter("receiveTimeStart"), -1);
        int receiveTimeEnd = Parser.parseInt(request.getParameter("receiveTimeEnd"), -1);
        boolean receiveTimeCheck = Parser.parseBoolean(request.getParameter("receiveTimeCheck"),false);
        int removeReason = Parser.parseInt(request.getParameter("removeReason"), -1);
        int ta = Parser.parseInt(request.getParameter("ta"), -1);
        int tag = Parser.parseInt(request.getParameter("tag"), -1);
        boolean excle = Parser.parseBoolean(request.getParameter("exportFlag"), false);
        //int flag = Parser.parseInt(request.getParameter("flag"), -1);
        Log.logStd("li test receiveTimeStart=%s,receiveTimeEnd=%s,receiveTimeCheck=%s",receiveTimeStart,receiveTimeEnd,receiveTimeCheck);
        
        Param result = new Param();
        if (!Web.getDebug() && (openHdTimeEnd - openHdTimeStart > 3600 * 24)) {
            result.setInt("total", 0);
            result.setString("msg", "时间跨度不能大于1天");
            return result.toJson();
        }

        FaiList<Param> conditions = FaiList.parseParamList(request.getParameter("checkedConditions"), new FaiList<Param>());
        //Log.logStd("li test conditions=%s", conditions);
        /*PreSaleHdCli cli = new PreSaleHdCli(Core.getFlow());
        if (!cli.init()) {
            return "{\"success\":true, \"data\":" + "cli err" + "}";
        }*/

        int pageNo = Parser.parseInt(request.getParameter("currentPage"), 1);
        int limit = 10;
        int total = 0;
        int start = limit * (pageNo - 1);

        FaiList<Integer> aidList = new FaiList<Integer>();
        
        //如果hdLogin2018、hdLogin2019有数据，说明用户回到电脑
        FaiList<Param> loginList = new FaiList<Param>();
        //Log.logStd("li test openHdTimeStart=%s,openHdTimeEnd=%s",openHdTimeStart,openHdTimeEnd);
        Dao bssMain = WebOss.getBssMainDaoMaster();
        Set<Integer> aidSet = new HashSet<Integer>();//有登录的aid
        try {
            //如果开通互动的时间是2019年以前，则还需查hdLogin2018
            Dao.SelectArg selectArg = new Dao.SelectArg();
            if (openHdTimeStart < 1546272000) {
                selectArg.table = "hdLogin2018";
                selectArg.field = "distinct(aid)";
                selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.GT, 0);
                selectArg.searchArg.matcher.and("time",ParamMatcher.GE,openHdTimeStart);
                //selectArg.searchArg.matcher.and("time",ParamMatcher.LE,openHdTimeEnd);
                FaiList<Param> loginList2018 = bssMain.select(selectArg);
                for (Param p : loginList2018) {
                    aidSet.add(p.getInt("aid", 0));
                }
                loginList.addAll(loginList2018);
            }
            selectArg = new Dao.SelectArg();
            selectArg.table = "hdLogin2019";
            selectArg.field = "distinct(aid)";
            selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.GT,0);
            selectArg.searchArg.matcher.and("time",ParamMatcher.GE,openHdTimeStart);
            //selectArg.searchArg.matcher.and("time",ParamMatcher.LE,openHdTimeEnd);
            FaiList<Param> loginList2019 = bssMain.select(selectArg);
            loginList.addAll(loginList2019);
            for (Param p : loginList) {
                aidSet.add(p.getInt("aid", 0));
                aidList.add(p.getInt("aid", 0));
            }
        } finally {
            bssMain.close();
        }
        
                
        
        FaiList<Param> infoList = new FaiList<Param>();
        SearchArg search = new SearchArg();
        search.limit = limit;
        search.totalSize = new Ref<Integer>();
        search.start = start;
        Param args = new Param();
        args.setInt("openHdTimeStart", openHdTimeStart);
        args.setInt("openHdTimeEnd", openHdTimeEnd);
        args.setInt("tag", tag);
        args.setInt("ta", ta);
        args.setInt("removeReason", removeReason);
        //ParamMatcher matcher = new ParamMatcher();
        ParamMatcher matcher = new ParamMatcher();
       /*  if(flag == 0){
        	matcher.and("aid", ParamMatcher.IN, aidList);
        }else if(flag == 1){
        	matcher.and("aid", ParamMatcher.NOT_IN, aidList);
        } */
        
      //筛选领取时间,有领取时间可以在hdSaleRecord并且是系统分配的;
        FaiList<Param> recordList = new FaiList<Param>();
        if(receiveTimeCheck){
            FaiList<Integer> aidRecordList = new FaiList<Integer>();         	
        	Dao ossDao = WebOss.getOssBsDao();
        	Calendar startCal = Parser.parseCalendar(new java.util.Date(new Long(receiveTimeStart) * 1000));
        	Calendar endCal = Parser.parseCalendar(new java.util.Date(new Long(receiveTimeEnd) * 1000));
        	String startStr = Parser.parseDateString(startCal, "yyyy-MM-dd HH:mm:ss");
        	String endStr = Parser.parseDateString(endCal, "yyyy-MM-dd HH:mm:ss");
        	Log.logStd("li test startStr=%s,endStr=%s",startStr,endStr);
            try {
                FaiList<Integer> attrTypeList = new FaiList<Integer>();
                attrTypeList.add(HdSaleRecordDef.AttrType.PHONE_PERSON);
                attrTypeList.add(HdSaleRecordDef.AttrType.CITY_CLIENT);
                attrTypeList.add(HdSaleRecordDef.AttrType.PHONE_ALLOT);
                attrTypeList.add(HdSaleRecordDef.AttrType.AUTO_CLIENT);
                Dao.SelectArg selectArg = new Dao.SelectArg();
                selectArg.field = "aid";
                selectArg.table = "hdSaleRecord";
                selectArg.searchArg = new SearchArg();
                selectArg.searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.CREATE_TIME,ParamMatcher.GE,startStr);
                selectArg.searchArg.matcher.and(HdSaleRecordDef.Info.CREATE_TIME,ParamMatcher.LE,endStr);
                selectArg.searchArg.matcher.and(HdSaleRecordDef.Info.ATTR_TYPE,ParamMatcher.IN,attrTypeList);
                Log.logStd("li test matcher=%s",selectArg.searchArg.matcher);
                recordList= ossDao.select(selectArg);
            } finally {
                ossDao.close();
            }
            if(recordList == null){
            	Log.logErr("li test recordList is null");
            }else{
                for(Param p : recordList){
                	aidRecordList.add(p.getInt(HdSaleRecordDef.Info.AID,0));
                }
                Log.logStd("li test recordList = %s",recordList.size());
                matcher.and("aid", ParamMatcher.IN, aidRecordList);
            }
        }

        
        matcher.and("openHdTime", ParamMatcher.GE, openHdTimeStart);
        matcher.and("openHdTime", ParamMatcher.LE, openHdTimeEnd);
        FaiList<Integer> trackList = PreSaleHdDef.getTaList(getTaList(), ta);


        search.matcher = matcher;
        Ref<Param> res = new Ref<Param>();
        if (excle) {
            search.start = 0;
            search.limit = 10000;
        }
        int rt = cli.getResourceList(args, infoList, search, res);
        
        result.setBoolean("success", true);
        if (rt != Errno.OK) {
            result.setBoolean("success", false);
            result.setString("msg", "系统错误");
        }
        Map<Integer, String> tagMap = getTagMap();
        String domain = WebHdOss.getOssDomainUrl();
        for (Param item : infoList) {
            int tagTemp = item.getInt(PreSaleDef.Info.TAG, -1);
            int aidTemp = item.getInt("aid", -1);
            int itemTa = item.getInt("ta", 0);
            item.setString("aidUrl", domain + "/index.jsp?t=hdSale&u=/cs/corp.jsp?aid=" + aidTemp);
            if (tagTemp >= 0) {
                String tagName = tagMap.get(tagTemp);
                item.setString("tag", tagName);
            } else {
                item.setString("tag", "未命中");
            }
//            if (itemTa == 0) {
//                item.setString("removeTa", "ta为0 排除");
//            }
        }

        for (Param p : infoList) {
            int aid = p.getInt("aid", 0);
            Param loginParam = new Param();
            if (!aidSet.add(aid)) {
                loginParam = Misc.getFirst(loginList, BssStatDef.Info.AID, p.getInt("aid", 0));
                loginParam.setString("flag", "是");

            }else {
                loginParam.setString("flag", "否");
            }
            p.assign(loginParam, "flag");//是否回到电脑标记
        }

        /* if (flag == 0) {
            infoList = Misc.getList(infoList, "flag", "是");
        } else if (flag == 1) {
            infoList = Misc.getList(infoList, "flag", "否");
        } */


        result.setInt("rt", rt);
        result.setList("list", infoList);
        int size = 0;
        if (search.totalSize.value != null) {
            size = search.totalSize.value;
        }
        result.setInt("total", size);
        result.setParam("res", res.value);
        Log.logStd("li test res = %s",res.value);
        if (excle) {
            response.setContentType("application/x-excel");
            // 浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
            // 所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
            out.clear(); // 清空页面的数据，不然导出的时候会把页面的内容导出！
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("资源分析数据列表.xls".getBytes("GBK"), "ISO-8859-1"));
            ServletOutputStream outputStream = response.getOutputStream();
            Param cellKey = new Param();
            cellKey.setString("aid", "aid");
            cellKey.setString("mobile", "注册手机");
            cellKey.setString("openHdTime", "开通互动时间");
            cellKey.setString("regTime", "注册时间");
            cellKey.setString("taGroupName", "注册来源");
            cellKey.setString("receiveTime", "领取时间");
            cellKey.setString("tag", "命中标签");
//            cellKey.setString("firstCreateGameTime", "首次创建游戏");
            cellKey.setString("ta", "ta");
            cellKey.setString("removeClient", "剔除原因1");
            cellKey.setString("removeComm", "剔除原因2");
            cellKey.setString("removeTa", "剔除原因3");
            cellKey.setString("flag", "是否回到电脑");
            OssPoi.exportExcel(cellKey, infoList, outputStream);

            return "{\"success\":true}";
        }
        //Log.logStd("li test:  info=%s", infoList);
        return result.toJson();

    }
	 /**
     * 查询购买过哪些产品
     */
	private FaiList<Param> getBuyProduct(int aid ) throws Exception {
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        Param checkExist =new Param();
		FaiList<Param> siteList =new FaiList<Param>(); //网站购买产品（包含建站和商城）
		FaiList<Param> mpList =new FaiList<Param>(); //公众号助手购买产品
		FaiList<Param> flyerList =new FaiList<Param>(); //传单购买产品
		FaiList<Param> hdList =new FaiList<Param>(); //hd购买产品
		FaiList<Param> programList =new FaiList<Param>(); //小程序购买产品
		OssVip oSysVip = new OssVip();
        FaiList<Param> vipList = oSysVip.getVipList(aid);
		FaiList<Param> buyList =  new FaiList<Param>();//购买产品列表
        faiTradeStationBaseApi.changeAid(aid);
		
		for(Param vipInfo : vipList){	
			int payProductId = vipInfo.getInt(VipDef.Info.PAY_PRODUCT_ID, 0);
			int sid = vipInfo.getInt(VipDef.Info.S_ID, 0);
			faiTradeStationBaseApi.changeSiteId(sid);
			Calendar expireTime = vipInfo.getCalendar(VipDef.Info.EXPIRE_TIME);
			int id=vipInfo.getInt(VipDef.Info.PAY_PRODUCT_ID, 0);
			String payProductName=faiTradeStationBaseApi.getName(payProductId);
			vipInfo.setString("payProductName",payProductName);
			vipInfo.setString("expireTime","到期时间： "+Parser.parseDateString(expireTime, "yyyy-MM-dd HH:mm:ss"));
			if(!checkExist.getBoolean(id+"",false)&&vipInfo.getDouble(VipDef.Info.VIP_UNIT_PRICE,0.0)>0.0){
				checkExist.setBoolean(id+"",true);
				buyList.add(vipInfo);
			}
			
		}

			
	    return buyList;
	}

    /**
     * 企业信息查询
     */
    private String getCorpInfo(HttpServletRequest request) throws Exception {
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        Log.logStd("aid = %d", aid);
        if (aid < 1) {
            return "aid err";
        }
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();

        Class ykClass = Class.forName("fai.web.inf.SysPreSaleYk");
    	SysPreSaleYk sysPreSaleYk = (SysPreSaleYk) Core.getSysKit(ykClass);
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);   

        SysAcct oSysAcct = (SysAcct) Core.getSysKit(Kid.SYS_ACCT);
        if (sysPreSaleYk == null || sysBssStat == null || oSysAcct == null) {
            Log.logStd("impl null");
            return "impl null";
        }
        
        Param info = oSysAcct.getAcctInfo(aid);
      	//getAcctInfo返回是只读param,需要克隆一份，不然会error
        info = info.clone();
	    
        try {
            int rt = Errno.OK;

            //行业
            int trade = info.getInt(AcctDef.Info.TRADE, 0);
            String tradeName = TradeDef.getName3(TradeDef.getIdById4(trade)) + " - " + TradeDef.getName4(trade) + "(" + trade + ")";
            info.setString("tradeName", tradeName);

            //注册来源
            SearchArg taArg = new SearchArg();
            taArg.matcher = new ParamMatcher("ta", ParamMatcher.EQ, info.getInt("ta", 0));
            FaiList<Param> taList = sysBssStat.getTaList(taArg);
            if (taList.size() == 1) {
                String taName = taList.get(0).getString("taName", "");
                info.setString("taName", taName);
            }

            // 注册主体
            String regSubject = "未知";
            if (trade != 0) {
                switch (trade) {
                    case TradeDef.Id2.NEW_GE_REN:
                        regSubject = "个人";
                        break;
                    case TradeDef.Id2.NEW_XUE_SHENG:
                        regSubject = "学生";
                        break;
                    case TradeDef.Id2.NEW_ZU_ZHI_JI_GOU_OTHER:
                        regSubject = "组织机构";
                        break;
                    default:
                        regSubject = "企业";
                        break;
                }
            }
            if (TradeDef.getGeRenIdList4().contains(trade)) {
                regSubject = "个人";
            }
            info.setString("regSubject", regSubject);

            // 注册时间
            Calendar createTime = info.getCalendar(AcctDef.Info.CREATE_TIME);
            String createTimeName = Parser.parseString(createTime);
            info.setString("createTimeName", createTimeName);

            // 登录时间
            Calendar loginTime = info.getCalendar(AcctDef.Info.LOGIN_TIME);
            String loginTimeName = Parser.parseString(loginTime);

            // 登录时间 如果登录时间和注册时间一样，说明是注册第一次进入，不显示。因为bss不统计这次登录。
            if (!loginTimeName.equals(createTimeName)) {
                info.setString("loginTimeName", loginTimeName);
            }
            OssHd ossHd = new OssHd();
            int hdVer = 0;
            String hdVerName = "-";
            String hdVerBuyName = "-";
            Param hdProfInfo = ossHd.getProf(aid, false);
            int hdFlag = hdProfInfo.getInt(HdProfDef.Info.FLAG, 0);
            hdVer = hdProfInfo.getInt(HdProfDef.Info.VERSION, 0);
            if (Misc.checkBit(hdFlag, HdProfDef.Flag.HAS_OPEN)) {        // 已开通了而且是直销的用户
                switch (hdVer) {
                    case HdProfDef.Version.Corp.ZS:
                        hdVerName = "钻石版";
                        break;
                    case HdProfDef.Version.Corp.BJ:
                        hdVerName = "铂金版";
                        break;
                    case HdProfDef.Version.Corp.BY:
                        hdVerName = "白银版";
                        break;
                    case HdProfDef.Version.Corp.MD:
                        hdVerName = "门店版";
                        break;
                    case HdProfDef.Version.Corp.FRE:
                        hdVerName = "免费版";
                        break;
                }
            }
            info.setString("hdVerName", hdVerName);

				
            OssVip oSysVip = new OssVip();
            FaiList<Param> vipList = oSysVip.getVipList(aid);
            faiTradeStationBaseApi.changeAid(aid);


            // 公众号助手版本
            String pubAssVerName = "";
            FaiList<Param> wxastVipList = Misc.getList(vipList, new ParamMatcher(VipDef.Info.TYPE, ParamMatcher.EQ, VipDef.Type.PUBLIC_ASSISTANT_VIP));
            for (Param programVipInfo : wxastVipList) {
                int payProductId = programVipInfo.getInt(VipDef.Info.PAY_PRODUCT_ID, 0);
                int sid = programVipInfo.getInt(VipDef.Info.S_ID, 0);
                faiTradeStationBaseApi.changeSiteId(sid);
                Calendar expireTime = programVipInfo.getCalendar(VipDef.Info.EXPIRE_TIME);

                pubAssVerName += faiTradeStationBaseApi.getName(payProductId);
                pubAssVerName += "[到期时间：<span style='color:red'>" + Parser.parseString(expireTime) + "</span>]";
            }
            if (wxastVipList.isEmpty()) {
                pubAssVerName = "免费版";
            }
            info.setString("pubAssVerName", pubAssVerName);

//            Param bssAcctInfo = sysBssStat.getAcctStatusInfo(aid);

            //迁移到新的查询方式
            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("*")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(AND.expr(EQ.of("aid", aid)))
                    .execute(Core.getFlow());
            Log.logDbg("execute:"+execute);
            Param bssAcctInfo = execute.getData().get(0);

			Log.logStd("bssAcctInfo =%s",bssAcctInfo);
            // 注册产品
            int regBiz = bssAcctInfo.getInt("reg_biz", 0);
            String regBizName = BssStatDef.getRegBizName(regBiz);
            info.setString("regBizName", regBizName);

            // 首次开通产品
            int firstOpenBiz = bssAcctInfo.getInt("frist_open_biz", 0);
            String firstOpenBizName = BssStatDef.getRegBizName(firstOpenBiz);
            info.setString("firstOpenBizName", firstOpenBizName);

            //关键字
            String tp = PreSaleHdDef.getSearchKeyword(bssAcctInfo.getHtmlString(BssStatDef.Info.TP));
            info.setString("tp", tp);

            // 互动开通时间
            String openHDTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPEN_HD_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openHDTimeName", openHDTimeName);
			// 建站开通时间
			 String openJzTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPEN_SITE_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openJzTimeName", openJzTimeName);
           		
            // 传单开通时间
            String openFlyerTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPEN_WCD_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openFlyerTimeName", openFlyerTimeName);
            // 邮箱开通时间
            String openMailTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPEN_MAIL_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openMailTimeName", openMailTimeName);
            // 助手开通时间
            String openWXTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPENMP_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openWXTimeName", openWXTimeName);
            // 小程序开通时间
            String openProgramTimeName = Parser.parseDateString(bssAcctInfo.getInt(BssStatDef.Info.OPEN_SPG_TIME, 0), "yyyy-MM-dd HH:mm:ss");
            info.setString("openProgramTimeName", openProgramTimeName);
			// 悦客开通时间
			Param ykProf=new Param();
			Dao.SelectArg ykSelect = new Dao.SelectArg();
			ykSelect.table = "yk_prof";
			ykSelect.field = "aid,createTime";
			SearchArg ykSearchArg =new SearchArg();
			ParamMatcher ykMatcher =new ParamMatcher();
			ykMatcher.and("aid",ParamMatcher.EQ,aid);
			ykSearchArg.matcher=ykMatcher;
			ykSelect.searchArg=ykSearchArg;
			Dao ykProfDao = WebHdOss.getYkProfDao();
			try{
				ykProf=ykProfDao.selectFirst(ykSelect);
			}finally{
				ykProfDao.close();
			}
		    
            String openYkTimeName = parseLongDateToString(ykProf.getLong("createTime",0l));	
            info.setString("openYkTimeName", openYkTimeName);

			
            //最后进入管理平台时间
            FaiList<Param> timeList = new FaiList<Param>();
            Dao hdDao = WebOss.getBssMainDaoMaster();
            String intoSystemName = "";
            int intoSystem = 0;

            try {
                if (hdDao != null) {
                	
                    String sql = "select max(time) as maxTime from hdLogin2019 where aid=" + aid;//查询在2019年用户登录
                    timeList = hdDao.executeQuery(sql);

                    if (timeList != null && timeList.size() > 0) {
                        intoSystem = timeList.get(0).getInt("maxTime", 0);
                        if (intoSystem == 0) {
                            timeList = hdDao.executeQuery("select max(time) as maxTime from hdLogin2018 where aid=" + aid);
                            intoSystem = timeList.get(0).getInt("maxTime", 0);
                        }
                    }
                    intoSystemName = Parser.parseDateString(intoSystem, "yyyy-MM-dd HH:mm:ss");
                    Log.logStd("test intoSystem=%s", intoSystem);
                    info.setString("intoSystemName", intoSystemName);
                
                  
                	Dao.SelectArg ykStatus = new Dao.SelectArg();
                	ykStatus.table = "ykStatus";
                	ykStatus.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.EQ, aid);
                	
                	FaiList<Param> ykStatusInfo = hdDao.select(ykStatus);
                	if(ykStatusInfo == null || ykStatusInfo.size() == 0){
                		Log.logStd("ykStatusInfo is null/empty");
                	}else{
                		info.setString("memberNum", ykStatusInfo.get(0).getInt("memberCount", 0)+"");
                	}
                	
                }
                
            } finally {
                hdDao.close();
            }
				
            // 互动用途
            HdBssStatCli hdBssStatCli = new HdBssStatCli(Core.getFlow());
            if (!hdBssStatCli.init()) {
                Log.logErr("Oss hdBssStatCli init err;");
            }
            FaiList<Param> bssStatList = new FaiList<Param>();
            FaiList<Integer> aidList = new FaiList<Integer>();
            aidList.add(aid);
            rt = hdBssStatCli.getOssUserInfo(aidList, bssStatList);
            if (rt != Errno.OK) {
                Log.logErr("Oss hdBssStatCli getOssUserInfo err;");
            }
            if (bssStatList != null && !bssStatList.isEmpty()) {
                int hdGoal = bssStatList.get(0).getInt("openCompanyGoal", 0);
                info.setString("hdGoalName", CorpProfDef.getCorpGoal2Name(hdGoal));
            }

            // 实名手机
            String signupMobile = "";
            Param signupConf = Web.getConf("signup");
            Param regAuthMobileInfo = new Param();
            FaiVerifiCation faiVerifiCation = (FaiVerifiCation) Core.getSysKit(Kid.SYS_VERIFICATION);
            faiVerifiCation.getAuthMobileInfoById(aid, 1, regAuthMobileInfo);
            if (regAuthMobileInfo != null && !regAuthMobileInfo.isEmpty()) {
                signupMobile = regAuthMobileInfo.getString(FaiVerifiCationDef.AuthMobile.MOBILE);
                if (signupConf != null) {
                    FaiList<String> mobileList = signupConf.getList("mobile");
                    if (mobileList != null) {
                        for (String mobileTmp : mobileList) {
                            if (mobileTmp.equalsIgnoreCase(regAuthMobileInfo.getString(FaiVerifiCationDef.AuthMobile.MOBILE))) {
                                signupMobile += "(内部手机)";
                            }
                        }
                    }
                }
            }
            info.setString("signupMobile", signupMobile);

            // 注册区域
            String regCityName = bssAcctInfo.getHtmlString(BssStatDef.Info.REG_CITY);
            info.setString("regCityName", regCityName);

            Dao dao = WebOss.getOssBsDao();
            try {
                //备注
                Dao.SelectArg selectArg = new Dao.SelectArg();
                selectArg.table = "corpMark";
                selectArg.field = "mark";
                selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.EQ, aid);
                FaiList<Param> corpMark_list1 = dao.select(selectArg);
                if (corpMark_list1 != null && corpMark_list1.size() > 0) {
                    String markName = corpMark_list1.get(0).getString("mark");
                    info.setString("markName", markName);
                }

            } finally {
                dao.close();
            }

            CorpProf oCorpProf = (CorpProf) Core.getCorpKit(aid, Kid.CORP_PROF);
            Param corpProf = oCorpProf.getProfInfo();
            if (corpProf != null) {
                //负责人
                String regPersonName = corpProf.getHtmlString(CorpProfDef.Info.REG_PERSON);
                info.setString("regPersonName", regPersonName);

                // 电话
                String regPhoneName = corpProf.getHtmlString(CorpProfDef.Info.REG_PHONE);
                info.setString("regPhoneName", regPhoneName);

                // 手机
                String regMobileName = corpProf.getHtmlString(CorpProfDef.Info.REG_MOBILE);
                info.setString("regMobileName", regMobileName);

                //网站用途
                int corpGoal = corpProf.getInt(CorpProfDef.Info.COMPANY_GOAL1, 0);
                String corpGoalName = CorpProfDef.getCorpGoal2Name(corpGoal);
                info.setString("corpGoalName", corpGoalName);

            }

            //验证手机和邮箱
            int atype = info.getInt(AcctDef.Info.ATYPE, 0);
            Param verifiInfo = getVerifiInfo(info.getInt(AcctDef.Info.AID, 0), atype);
            String verifyEmail = verifiInfo.getString(FaiVerifiCationDef.Email.EMAIL, "");
            String mobile = verifiInfo.getString(FaiVerifiCationDef.Info.PHONE, "");
            info.setString("verifyEmail", verifyEmail);
            info.setString("verifyPhone", mobile);

            //标记信息
            Param biaoji = sysPreSaleYk.getInfo(aid);
            
            Param data = new Param();
            data.setInt("intent", biaoji.getInt(PreSaleHdDef.Info.INTENT, 0));
            data.setString("reason", biaoji.getString(PreSaleHdDef.Info.DIS_BUY_REASON, ""));
            //data.setString("mark", biaoji.getString(PreSaleHdDef.Info.MARK, ""));
            data.setString("reasonHref", "http://" + Web.getOssDomain() + "/cs/siteReason.jsp?type=12");
            int flag = biaoji.getInt("flag", 0);
            data.setInt("flag", flag);
            //Log.logStd("setSpecialFocus jsp flag=%s",flag);
            if (Misc.checkBit(flag, PreSaleHdDef.Flag.SPECIAL_FOCUS)) {
                data.setInt("sFocus", 1);//已经设置为特别关注，sFocus是否设置为特别关注的标记，1为是，0为否
            } else {
                data.setInt("sFocus", 0);//还没设置为特别关注
            }
            //是否禁止发送短信
            if (Misc.checkBit(flag, PreSaleHdDef.Flag.BAN_SEND_MSG)) {
                data.setBoolean("sendMsg", false);
            } else {
                data.setBoolean("sendMsg", true);
            }
            String talkNextTime = Parser.parseSimpleTime(biaoji.getCalendar(PreSaleHdDef.Info.TALK_NEXT_TIME));
            data.setString("intentName", PreSaleHdDef.getIntentName(biaoji.getInt(PreSaleHdDef.Info.INTENT, 0)));
            data.setString("talkNextTime", talkNextTime);
            data.setInt("intentLevel", info.getInt(PreSaleHdDef.Info.INTENT_LEVEL, 0));
            info.setParam("biaoji", data);
            
        } catch (Exception e) {
            PrintUtil.printStackTrace(e, 1, "=========================================");
        }
        FaiList<Param> buyProduct=getBuyProduct(aid);
		if(buyProduct!=null&&!buyProduct.isEmpty()){
			info.setList("buyProduct",buyProduct);
		}	
        Log.logStd("finnnnnnnnnnnnnnnnnnnnnnnnnnnal aid info = %s", info);
        return info.toJson();
    }

    // 获取验证手机和验证邮箱
    private Param getVerifiInfo(int aid, int atype) {
        try {
            int sid = WebOss.getStaffCreatorSidByAid(aid);
            FaiVerifiCation faiVerifiCation = (FaiVerifiCation) Core.getSysKit(Kid.SYS_VERIFICATION);
            Param emailInfo = new Param();
            Param mobileInfo = new Param();
            faiVerifiCation.getVerifiEmailInfoById(aid, sid, emailInfo);
            Param verifiInfo = new Param();
            if (emailInfo != null && !emailInfo.isEmpty()) {
                String email = emailInfo.getString(FaiVerifiCationDef.Email.EMAIL, "");
                verifiInfo.setString(FaiVerifiCationDef.Email.EMAIL, email);
            }

            faiVerifiCation.getVerifiPhoneInfoById(aid, sid, mobileInfo);
            if (mobileInfo != null && !mobileInfo.isEmpty()) {
                String mobile = String.valueOf(mobileInfo.getLong(FaiVerifiCationDef.Info.PHONE));
                verifiInfo.setString(FaiVerifiCationDef.Info.PHONE, mobile);
            }

            if (atype == AcctDef.Atype.AGENT_CORP) {
                faiVerifiCation.getAuthMobileInfoById(aid, sid, mobileInfo);
                if (mobileInfo != null && !mobileInfo.isEmpty()) {
                    String mobile = mobileInfo.getString(FaiVerifiCationDef.AuthMobile.MOBILE);
                    verifiInfo.setString(FaiVerifiCationDef.Info.PHONE, mobile);
                }
            }
            return verifiInfo;
        } catch (Exception e) {
            return new Param();
        }
    }

    // 修改备注
    private String setInfoMark(HttpServletRequest request) throws Exception {
        Auth.checkFaiscoAuth("authCs|authDevManage|authVasSales|authAgentAdm|authPreSale|authHDSale|authHDSaleManage", true);
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);    // presale 的接口

        /************************* 获取参数 *************************/
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        String mark = Parser.parseString(request.getParameter("mark"), "");
        String oldmark = Parser.parseString(request.getParameter("oldmark"), "");

        if (aid == 0) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }
        if (mark.equals("")) {
            return "{\"success\":false, \"msg\":\"备注不能为空\"}";
        }

        String currentOperator = Acct.getStaffInfo(Session.getCid(), Session.getSid()).getHtmlString(StaffDef.Info.NAME);
        String currentSimpleTime = Parser.parseString(Calendar.getInstance());
        mark = "【" + currentOperator + " —— " + currentSimpleTime + "】" + Parser.parseString(request.getParameter("mark"), "");

        if (!oldmark.equals("")) {
            mark += "\r-----------------------\r" + oldmark;
        }


        Dao dao = WebOss.getOssBsDao();
        int rt = Errno.ERROR;
        String sql = "";
        try {
            sql = "replace into corpMark (aid,mark) values (?,?)";

            PreparedStatement stmt = dao.prepareStatement(sql);
            stmt.setInt(1, aid);
            stmt.setString(2, mark);

            rt = dao.executeUpdate(stmt, null, null);
            stmt.close();

        } finally {
            dao.close();
        }
        //如果提交备注成功。如果该资源是B库，并且不在成交库中，则把该资源从B库转入A库
        if (rt == Errno.OK) {
            SearchArg searchArg = new SearchArg();
            searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.EQ, aid);
            searchArg.matcher.and(PreSaleHdDef.Info.FLAG, ParamMatcher.LAND, PreSaleHdDef.Flag.UN_CONTACT_30DAY, PreSaleHdDef.Flag.UN_CONTACT_30DAY);
            searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.NE, PreSaleHdDef.Status.DEAL);
            FaiList<Param> tmpList = sysPreSaleHd.getList(searchArg);
            FaiList<Param> hdRecordList = new FaiList<Param>();
            //Log.logStd("li test setInfoMark tmpList=%s",tmpList);

            //转入A库，修改acctPreSaleHd表
            //hdSaleRecord插入一条记录
            if (tmpList != null && tmpList.size() > 0) {
                int flag = tmpList.get(0).getInt(PreSaleHdDef.Info.FLAG, 0);
                flag = Misc.setFlag(flag, PreSaleHdDef.Flag.UN_CONTACT_30DAY, false);
                Param info = new Param();
                info.setInt(PreSaleHdDef.Info.FLAG, flag);
                info.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
                ParamUpdater updater = new ParamUpdater(info);
                rt = sysPreSaleHd.update(aid, updater);
                if (rt != Errno.OK) {
                    Log.logErr(rt, "setInfoMark update acctPreSaleHd error aid=%s", aid);
                    return "{\"success\":false, \"msg\":\"B库转入失败，请联系管理员！ \"}";
                }
                Dao ossDao = WebOss.getOssBsDao();
                try {
                    searchArg = new SearchArg();
                    searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.AID, ParamMatcher.EQ, aid);
                    Dao.SelectArg selectArg = new Dao.SelectArg();
                    selectArg.field = "aid,sacct,receiveTime,tag,flag,taGroupName,ta";
                    selectArg.table = "hdSaleRecord";
                    selectArg.searchArg = searchArg;
                    hdRecordList = ossDao.select(selectArg);
                    if (hdRecordList != null && hdRecordList.size() > 0) {
                        searchArg = new SearchArg();
                        searchArg.matcher = new ParamMatcher(HdSaleRecordDef.Info.AID, ParamMatcher.EQ, aid);
                        //根据领取时间排序
                        searchArg.cmpor = new ParamComparator("receiveTime", true);
                        Searcher searcher = new Searcher(searchArg);
                        hdRecordList = searcher.getParamList(hdRecordList);
                        //拿到最近领取的记录
                        Param record = hdRecordList.get(0);
                        record.setInt(HdSaleRecordDef.Info.ATTR_TYPE, HdSaleRecordDef.AttrType.BLIB_ENTER);
                        record.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
                        record.setString(HdSaleRecordDef.Info.ACTION, "B库转入");
                        rt = ossDao.insert("hdSaleRecord", record);
                        if (rt != Errno.OK) {
                            Log.logErr("setInfoMark insert hdSaleRecord err record=%s", record.toJson());
                            return "{\"success\":false, \"msg\":\"增加领取记录失败，请联系管理员! \"}";
                        }
                    } else {
                        return "{\"success\":false, \"msg\":\"参数有误，请联系管理员! \"}";
                    }

                } finally {
                    ossDao.close();
                }
            }
        }

        /***** 修改客户更新时间以及联系状态 start *****/
        Param info = new Param();
        info.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
        info.setCalendar(PreSaleHdDef.Info.CONTACT_TIME, Calendar.getInstance());
        info.setCalendar(PreSaleHdDef.Info.OPT_TIME, Calendar.getInstance());
        rt = sysPreSaleHd.update(aid, new ParamUpdater(info));
        /***** 修改客户更新时间成交客户   end  *****/

        if (rt == Errno.OK) {
            info = new Param();
            info.setString("markName", mark);
            return "{\"success\":true, \"msg\":\"修改成功。\", \"data\":" + info + "}";
        } else if (rt == Errno.ARGS_ERROR) {
            return "{\"success\":true, \"msg\":\"参数错误。\"}";
        } else {
            return "{\"success\":false, \"msg\":\"修改失败。\"}";
        }
    }

    //设置企微提醒时间
    private String addQWRemind(HttpServletRequest request) throws Exception {
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD); // presaleHd 的接口
        FaiList<String> fieldList = new FaiList<String>();
        FaiList<Param> salesInfo = new FaiList<Param>();
        fieldList.add("sid");
        fieldList.add("acct");
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(fieldList, searchArg, salesInfo);

        //获取参数
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        Param info = new Param();
        long QWRemindTime = Parser.parseCalendar(request.getParameter("QWRemindTime"), "yyyy-MM-dd HH:mm:ss").getTimeInMillis();
        String QWRemindContent = Parser.parseString(request.getParameter("QWRemindContent"), "");
        long nowTime = Calendar.getInstance().getTimeInMillis();
        int sid = Parser.parseInt(Acct.getStaffInfo(Session.getCid(), Session.getSid()).getHtmlString(StaffDef.Info.SID), 0);//获取当前操作人员工的sid
        String sacct = "";

        if (QWRemindTime <= 0) {
            return "{\"success\":false, \"msg\":\"时间参数错误\"}";
        }
        if (nowTime > QWRemindTime) {
            return "{\"success\":false, \"msg\":\"请设置大于当前时间\"}";
        }
        if (aid <= 0) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }

        if (QWRemindContent.equals("")) {
            return "{\"success\":false, \"msg\":\"请输入提醒内容\"}";
        }

        for (Param p : salesInfo) {
            int sidTmp = p.getInt("sid", 0);
            if (sid == sidTmp) {
                sacct = p.getString("acct", "");//获取当前操作人的英文名.
            }
        }

        Dao ossDao = null;
        try {
            ossDao = WebOss.getOssBsDao();
            String sql = "select salesAcct from acctPreSaleHd where aid=" + aid;
            FaiList<Param> salesList = ossDao.executeQuery(sql);
            if (sid != 112 && sid != 1224 && sid != 1355) {
                if (salesList != null && salesList.size() > 0) {
                    if (!sacct.equals(salesList.get(0).getString("salesAcct", ""))) {
                        return "{\"success\":false, \"msg\":\"没有权限！\"}";//只有客户领取人才有权限设置提醒。
                    }
                }
            }
        } finally {
            ossDao.close();
        }
        info.setInt("aid", aid);
        info.setInt("sid", sid);
        info.setLong("QWRemindTime", QWRemindTime);
        info.setString("QWRemindContent", QWRemindContent);
        info.setInt("status", 0);
        info.setLong("createTime", nowTime);

        rt = sysPreSaleHd.addQWRemind(info);

        if (rt == Errno.OK) {
            return "{\"success\":true, \"msg\":\"设置成功\"}";
        }
        return "{\"success\":true, \"msg\":\"设置失败\"}";
    }

    //用户设置特别关注或者取消特别关注
    private String setSpecialFocus(HttpServletRequest request) throws Exception {
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD); // presaleHd 的接口
        //获取参数
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int flag = Parser.parseInt(request.getParameter("flag"), -1);
        //Log.logStd("setSpecialFocus aid=%s,flag=%s",aid,flag);
        if (aid == 0 || flag == -1) {
            return "{\"success\":false, \"msg\":\"参数有误\"}";
        }
        //Log.logStd("setSpecialFocus aid=%s,flag=%s",aid,flag);
        boolean isFocus = Misc.checkBit(flag, PreSaleHdDef.Flag.SPECIAL_FOCUS);
        //Log.logStd("setSpecialFocus isFocus=%s",isFocus);
        if (isFocus) {
            flag = Misc.setFlag(flag, PreSaleHdDef.Flag.SPECIAL_FOCUS, false);
        } else {
            flag = Misc.setFlag(flag, PreSaleHdDef.Flag.SPECIAL_FOCUS, true);
        }

        //Log.logStd("setSpecialFocus flag=%d", flag);
        Param updater = new Param();
        updater.setInt(PreSaleHdDef.Info.FLAG, flag);
        ParamUpdater info = new ParamUpdater(updater);

        int rt = sysPreSaleHd.update(aid, info);
        //Log.logStd("setSpecialFocus rt=%s", rt);
        if (!isFocus && rt != Errno.OK) {
            Log.logStd("setSpecialFocus update err");
            return "{\"success\":false, \"msg\":\"关注失敗\"}";
        } else if (!isFocus && rt == Errno.OK) {
            return "{\"success\":true, \"msg\":\"关注成功\"}";
        } else if (isFocus && rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"取消失敗\"}";
        } else if (isFocus && rt == Errno.OK) {
            return "{\"success\":true, \"msg\":\"取消成功\"}";
        }
        return "{\"success\":true, \"msg\":\"关注失败\"}";
    }

    //獲取未發送企微提醒列表
    private String getRemindList(HttpServletRequest request) throws Exception {
        int sid = Parser.parseInt(Acct.getStaffInfo(Session.getCid(), Session.getSid()).getHtmlString(StaffDef.Info.SID), 0);//获取当前操作人员工的sid
        Dao hdOssAffairDao = WebHdOss.getHdOssAffairDaoMaster();
        FaiList<Param> remindList = new FaiList<Param>();
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add("id");
        fieldList.add("aid");
        fieldList.add("QWRemindTime");
        fieldList.add("QWRemindContent");
        Dao.SelectArg searchArg = new Dao.SelectArg();
        searchArg.searchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.EQ, sid);
        searchArg.searchArg.matcher.and("status", ParamMatcher.EQ, 0);//未发送状态
        searchArg.table = "hdQWRemind";
        try {
            remindList = hdOssAffairDao.select(searchArg);
            if (remindList != null && remindList.size() > 0) {
                for (Param p : remindList) {
                    String QWRemindTime = Parser.parseString(new Date(p.getLong("QWRemindTime", 0L)));
                    p.setString("QWRemindTime", QWRemindTime);
                }
            }
        } finally {
            hdOssAffairDao.close();
        }
        //Log.logStd("li test getRemindList=%s",remindList);
        return "{\"success\":true, \"remindList\":" + remindList + "}";
    }


    //删除企微提醒
    private String deleteQWRemind(HttpServletRequest request) throws Exception {
        int sid = Parser.parseInt(Acct.getStaffInfo(Session.getCid(), Session.getSid()).getHtmlString(StaffDef.Info.SID), 0);//获取当前操作人员工的sid
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD); // presaleHd 的接口
        //获取参数
        int id = Parser.parseInt(request.getParameter("id"), 0);
        Calendar cal = Parser.parseCalendar(request.getParameter("QWRemindTime"), "yyyy-MM-dd HH:mm:ss");
        long QWRemindTime = Parser.parseLong(cal.getTimeInMillis() + "", 0L);
        if (id == 0 || sid == 0 || QWRemindTime == 0L) {

            Log.logStd("hdQWRemind id=%d,sid=%d,QWRemindTime=%s", id, sid, QWRemindTime);
            return "{\"success\":false, \"msg\":\"参数有误\"}";
        }
        Param info = new Param();
        info.setInt("id", id);
        info.setLong("QWRemindTime", QWRemindTime);
        int rt = sysPreSaleHd.deleteRemind(info);
        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"删除失败\"}";
        }

        if (rt != Errno.OK) {
            return "{\"success\":false, \"msg\":\"删除失败\"}";
        }
        return "{\"success\":true, \"msg\":\"删除成功\"}";
    }

    //特别关注列表
    private String getFocus(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {
        Log.logStd("getFocus list=%s", getYkSaleList2(request, response, out));
        return "{\"success\":true, \"msg\":\"删除失败\"}";
    }

    private String banSendMsg(HttpServletRequest request) throws Exception {
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD); // presaleHd 的接口
        //获取参数
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int flag = Parser.parseInt(request.getParameter("flag"), -1);
        Log.logStd("banSendMsg aid=%s,flag=%s", aid, flag);
        if (aid == 0 || flag == -1) {
            return "{\"success\":false, \"msg\":\"参数有误\"}";
        }

        if (Misc.checkBit(flag, PreSaleHdDef.Flag.BAN_SEND_MSG)) {
            flag = Misc.setFlag(flag, PreSaleHdDef.Flag.BAN_SEND_MSG, false);
        } else {
            flag = Misc.setFlag(flag, PreSaleHdDef.Flag.BAN_SEND_MSG, true);
        }
        Param updater = new Param();
        updater.setInt(PreSaleHdDef.Info.FLAG, flag);
        ParamUpdater info = new ParamUpdater(updater);
        int rt = sysPreSaleHd.update(aid, info);
        if (rt != Errno.OK) {
            return "{\"success\":false,\"msg\":\"操作成功\"}";
        }
        return "{\"success\":true, \"msg\":\"操作成功\"}";
    }

   
    private String getBssData(HttpServletRequest request) throws Exception {
        Param count = new Param(true);
        Dao ossDao = WebOss.getOssBsDao();
        FaiList<Param> infoList = new FaiList<Param>();
        try {
            infoList = ossDao.executeQuery("select distinct(aid),receiveTime from hdSaleRecord where receiveTime>'2018-11-01 00:00:00' and receiveTime <'2019-02-01 00:00:00'");
        	//infoList = ossDao.executeQuery("select distinct(aid),receiveTime from hdSaleRecord where receiveTime>'2018-11-01 00:00:00' and receiveTime <'2019-04-27 00:00:00'");
        } finally {
            ossDao.close();
        }
        Dao bssDao = WebOss.getBssMainDao();
        try {
        	
            FaiList<Param> groupList = bssDao.executeQuery("select ta from ta where groupId in(10,11,12,27,52,53,54,55)");
            //FaiList<Param> groupList = bssDao.executeQuery("select ta from ta where groupId in(10,11,12)");
            FaiList<Integer> taList = new FaiList<Integer>();
            FaiList<Integer> aidList = new FaiList<Integer>();
            for(Param item:groupList){
                taList.add(item.getInt("ta",0));
            }
            int num = 0;
            for(Param item:infoList){
                num++;
                aidList.add(item.getInt("aid",0));
                if(num>100){
                    break;
                }
            }
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctStatus";
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
            sltArg.searchArg.matcher.and("ta",ParamMatcher.IN,taList);
            FaiList<Param> list = bssDao.select(sltArg);
                Log.logDbg("mat=%s", sltArg.searchArg.matcher);
                Log.logDbg("taList=%s", taList);
            Param aidParam = new Param(true);
            for(Param item:list){
                int temp = item.getInt("aid",0);
                if(temp == 0){
                    continue;
                }
                aidParam.setInt(""+temp,1);
            }
          
            for(Param item:infoList){
                int tempAid = item.getInt("aid");
                if(aidParam.getInt(""+tempAid,0) == 1){
                    Calendar dat = item.getCalendar("receiveTime");
                    if(dat != null){
                        String key = Parser.parseString(dat,"yyyy-MM-dd");
                        int value = count.getInt(key,0);
                        count.setInt(key,++value);
                    }
                }
            }
            for(String key:count.keySet()){
                Log.logStd("yansen key=%s", count.getInt(key));
            }
        } finally {
            bssDao.close();
        }
        return count.toJson();
    }
%>

<%
    String output = "";
	Log.logStd("enter ajax");
    try {
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            output = "no cmd find";
        } else if (cmd.equals("getBssData")) {
            output = getBssData(request);
        } else if (cmd.equals("setInfo")) {
            output = setInfo(request);
        } else if (cmd.equals("setReceiveTime")) {
            output = setReceiveTime(request);
        } else if (cmd.equals("setHDSales")) {
            output = setHDSales(request);
        } else if (cmd.equals("allotPerson")) {
            output = allotPerson(request);
        } else if (cmd.equals("addPreSaleApprove")) {
            output = addPreSaleApprove(request);
        } else if (cmd.equals("setApproveStatus")) {
            output = setApproveStatus(request);
        } else if (cmd.equals("delApprove")) {
            output = delApprove(request);
        } else if (cmd.equals("upLoadFile")) {
            output = upLoadFile(request);
        } else if (cmd.equals("cleanLeave")) {
            output = cleanLeave(request);
        } else if (cmd.equals("setYkReason")) {
            output = setYkReason(request);
        } else if (cmd.equals("setCountLimit")) {
            output = setCountLimit(request);
        } else if (cmd.equals("admOpt")) {
            output = admOpt(request, response, out);
        } else if (cmd.equals("recoverData")) {
            output = recoverData(request);
        } else if (cmd.equals("getYkSaleList2")) {//TODO
            output = getYkSaleList2(request, response, out);
        } else if (cmd.equals("logout")) {
            output = logout(response);
        } else if (cmd.equals("batchSendMessage")) {
            output = batchSendMessage(request);
        } else if (cmd.equals("getSendMessageList")) {
            output = getSendMessageList(request);
        } else if (cmd.equals("getPayRecordList")) {
            output = getPayRecordList(request, response, out);
        } else if (cmd.equals("getMonthMeritStatisticsList")) {
            output = getMonthMeritStatisticsList(request, response, out);
        } else if (cmd.equals("getWeekMeritStatisticsList")) {
            output = getWeekMeritStatisticsList(request, response, out);
        } else if (cmd.equals("getLogRecordList")) {
            output = getLogRecordList(request, response, out);
        } else if (cmd.equals("getCountStatList")) {
            output = getCountStatList(request);
        } else if (cmd.equals("getCleanupList")) {
            output = getCleanupList(request);
        } else if (cmd.equals("getSetCountLimitList")) {
            output = getSetCountLimitList(request);
        } else if (cmd.equals("getApproveList")) {
            output = getApproveList(request);
        } else if (cmd.equals("getSetConfList")) {
            output = getSetConfList(request);
        } else if (cmd.equals("getPreSale")) {
            output = getPreSale();
        } else if (cmd.equals("delSale")) {
            output = delSale(request);
        } else if (cmd.equals("changeOfficial")) {
            output = changeOfficial(request);
        } else if (cmd.equals("getSaleList")) {
            output = getSaleList(request);
        } else if (cmd.equals("getPageDef")) {
        	output = getPageDef(request);
        } else if (cmd.equals("getPersonTelInfo")) {//获取账号电话信息
            output = getPersonTelInfo(request);
        } else if (cmd.equals("changeWeekendAllot")) {
            output = changeWeekendAllot(request);
        } else if (cmd.equals("setYkSale")) {
            output = setYkSale(request);
        } else if (cmd.equals("getSidList")) {
            output = getSidList(request);
        } else if (cmd.equals("addSale")) {
            output = addSale(request);
        } else if (cmd.equals("getReasonStat")) {
            output = getReasonStat(request, response, out);
        } else if (cmd.equals("getTagList")) {
            output = getTagList(request);
        } else if (cmd.equals("getAuthStaffList")) {
            output = getAuthStaffList(request);
        } else if (cmd.equals("setStaffAuth")) {
            output = setStaffAuth(request);
        } else if (cmd.equals("getSaleArpu")) {
            output = getSaleArpu(request, response, out);
        } else if (cmd.equals("releasePreSaleDeal")) {
            output = releasePreSaleDeal(request);
        } else if (cmd.equals("getSaleListNotAll")) {
            output = getSaleListNotAll(request);
        } else if (cmd.equals("setAwardCount")) {
            output = setAwardCount(request);
        } else if (cmd.equals("rollDel")) {
            output = rollDel(request);
        } else if (cmd.equals("admOptExportByOneSql")) {
            output = admOptExportByOneSql(request, response, out);
        } else if (cmd.equals("setTotalLimitCount")) {
            output = setTotalLimitCount(request);
        } else if (cmd.equals("getYkInfo")) {
            output = getYkInfo(request);
        } else if (cmd.equals("getYkIntentList")) {
            output = getYkIntentList();
        } else if (cmd.equals("getYkReasonList")) {
            output = getYkReasonList();
        } else if (cmd.equals("runAllot")) {
            output = runAllot(request);
        } else if (cmd.equals("delActivity")) {
            output = delActivity(request);
        } else if (cmd.equals("getData100")) {
            getData100(request);
        } else if (cmd.equals("getAllotRegular")) {
            output = getAllotRegular(request);
        } else if (cmd.equals("resourceAnalyze")) {
            output = resourceAnalyze(request, response, out);
        } else if (cmd.equals("addHdOssStaff")) {
            output = addHdOssStaff(request);
        } else if (cmd.equals("getCorpInfo")) {
            output = getCorpInfo(request);
        } else if (cmd.equals("jasenExport")) {
            output = jasenExport(request, response, out);
        } else if (cmd.equals("getSaleArpuAB")) {
            output = getSaleArpuAB(request, response, out);
        } else if (cmd.equals("setInfoMark")) {
            output = setInfoMark(request);
        } else if (cmd.equals("addQWRemind")) {
            output = addQWRemind(request);
        } else if (cmd.equals("setSpecialFocus")) {
            output = setSpecialFocus(request);
        } else if (cmd.equals("getRemindList")) {
            output = getRemindList(request);
        } else if (cmd.equals("deleteRemind")) {
            output = deleteQWRemind(request);
        } else if (cmd.equals("getFocus")) {
            output = getFocus(request, response, out);
        } else if (cmd.equals("isStopAllot")) {
            output = isStopAllot(request);
        } else if (cmd.equals("banSendMsg")) {
            output = banSendMsg(request);
        } else if (cmd.equals("batchRelease")) {
            output = batchRelease(request);
        }
        
        //TODO
        /*  else if (cmd.equals("getNewAndOldList")) {
            output = getNewAndOldList(request);
        }  */
        else {
            output = "no cmd find";
        }


    } catch (Exception exp) {
        PreSaleHdDef.printErr(exp);
        output = WebOss.checkAjaxException(exp);
    }

    out.print(output);


%>