<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>

<%!
    //getPageDefInfo
    private static Param getPageDefInfo(String key, String tag) throws Exception {
        Param constant = Web.getConf("constant");
        if (key == null) {
            return new Param();
        }
        Param info = constant.getParam(key, new Param()).clone();
        Param comm = constant.getParam("comm", new Param());
        if ("hdSaleList".equals(key) || "hdPayRecord".equals(key)) {//客户列表还需要tag标签
            info.setParam("tag", comm.getParam("tag"));
        }
        if (tag != null) {
            return info.getParam(tag);
        }
        return info;
    }
%>

<%!
    //getTagMap
    private static Map getTagMap() throws Exception {
        Param constant = getPageDefInfo("comm", "tag");
        Map<Integer, String> tagMap = new HashMap<Integer, String>();
        if (constant != null) {
            FaiList<Param> tagList = constant.getList("labelList", new FaiList<Param>());
            for (Param item : tagList) {
                int tagItem = item.getInt("label", 0);
                String name = item.getString("name", "");
                tagMap.put(tagItem, name);
            }
        }
        return tagMap;
    }
%>

<%!
    //获取列表
    private static FaiList<Param> getTaList() throws Exception {
        FaiList<Param> taList = (FaiList<Param>) Core.getTmpData("_taList");
        if (taList == null) {
            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口
            SearchArg searchArg = new SearchArg();
            taList = sysBssStat.getTaList(searchArg);
            Core.setTmpData("_taList", taList);
        }
        return taList;
    }
%>