package fai.webhdoss.model.vo.scRes;

import fai.app.ScResDef;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 素材资源分类VO
 *
 * <AUTHOR> 2025/6/11 14:35
 * @Update jachin 2025/6/11 14:35
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScCategoryVO {
    private int type = ScResDef.Type.OPT_BGM;          // 资源类型
    private String name = "";       // 名字
    private int parentId = -1;      // 上级文件夹id
    private int id = -1;            // 更新的id
    private int pageLimit = 10;     // 每页显示数量
    private int pageNo = 1;         // 当前页数

    public Integer getOffset() {
        return (pageNo - 1) * pageLimit;
    }
}
