<template>
  <div class="mt-[30px]">
    <p class="mb-[10px] text-[16px] font-bold">素材组配置</p>

    <div class="flex items-start">
      <div class="!w-[600px]">
        <el-table :data="templateInfo.resFormList" style="width: 100%">
          <el-table-column
            prop="id"
            label="素材组ID"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="protoResLabel"
            label="原型素材组名称"
            width="150"
          ></el-table-column>
          <el-table-column prop="label" label="模板素材组名称">
            <template slot-scope="scope">
              <el-form-item
                :prop="'resFormList.' + scope.$index + '.label'"
                :rules="[{ validator: validateLabel, trigger: 'change' }]"
              >
                <el-input
                  minlength="1"
                  maxlength="50"
                  v-model="scope.row.label"
                  placeholder="请输入素材组名称"
                  class="!w-[180px]"
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            prop="resId"
            :label="editType === ScProductType.VIDEO ? '示例视频' : '示例图片'"
            width="120"
          >
            <template slot-scope="scope">
              <UploadVideo
                title="上传视频"
                :limit="1"
                :file-list="scope.row._fileList"
                @upload-success="
                  (response) => uploadVideoSuccess(response, scope.row)
                "
                @upload-remove="uploadVideoRemove(scope.row)"
                v-if="editType === ScProductType.VIDEO"
              />
              <UploadImg
                title="上传图片"
                :limit="1"
                :file-list.sync="scope.row._fileList"
                @upload-success="
                  (response) => uploadImgSuccess(response, scope.row)
                "
                @upload-remove="uploadImgRemove(scope.row)"
                v-else
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div
        class="ml-[60px] min-w-[400px] text-[14px] border-[1px] border-gray-300 rounded-[4px] p-[20px] flex flex-col h-auto"
        v-if="editType === ScProductType.VIDEO"
      >
        <p class="mb-[20px] font-bold">剪辑逻辑预览</p>
        <div v-if="templateInfo.scriptCommList.length === 0">空</div>
        <div v-else class="w-full">
          <div class="flex flex-wrap mb-[20px] font-bold">
            <p class="w-[150px]">脚本模块</p>
            <p >选取素材组（原型素材组名称）</p>
          </div>
          <div
            class="flex flex-wrap items-center mb-[15px]"
            v-for="item in templateInfo.scriptCommList"
            :key="item.id"
          >
            <p class="w-[150px]">{{ item.label }}</p>
            <div class="max-w-[280px] flex flex-wrap">
              <p
                class="border-[1px] border-gray-300 rounded-[4px] p-[4px] mr-[10px] mb-[5px]"
                v-for="resItem in item.resList"
                :key="resItem"
              >
                {{ resItem }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UploadVideo from "../common/uploadVideo.vue";
import UploadImg from "../common/uploadImg.vue";
import { ScProductType } from "@/views/scPortal/config/index.js";
export default {
  name: "ResForm",
  components: {
    UploadVideo,
    UploadImg,
  },
  data() {
    return {
      ScProductType,
      isUploading: false,
      percentage: 0,
    };
  },
  props: {
    editType: {
      type: Number,
      default: ScProductType.VIDEO,
    },
    templateInfo: {
      type: Object,
      required: true,
    },
  },
  methods: {
    beforeFileUpload(file) {
      if (file.size > 5 * 1024 * 1024) {
        return this.$message({
          type: "error",
          message: "单个文件超过5MB！",
        });
      }
      this.isUploading = true;
      this.percentage = 0;
    },
    fileUploadSuccess(response) {
      if (response.type >= 1 && response.type <= 6) {
        this.$message({
          type: "success",
          message: "文件 " + response.name + " 上传成功！",
        });
      } else {
        this.$message({
          type: "error",
          message: "文件 " + response.name + " 类型不允许！",
        });
      }
      this.isUploading = false;
    },
    fileUploadProgress(event) {
      this.percentage = Math.round(event.percent);
    },
    fileUploadError(err) {
      this.$message({
        type: "error",
        message: "系统繁忙，请稍后重试！",
      });
      this.isUploading = false;
    },
    /**
     * 上传视频成功
     * @param response 上传响应
     * @param row 当前行
     */
    uploadVideoSuccess(response, row) {
      row.resId = response.id; // 视频id
      row.resType = response.type; // 视频类型
      if (response.coverId) {
        row.coverId = response.coverId; // 视频封面id
        row.coverType = response.coverType; // 视频封面类型
      }
      console.log(response, "uploadVideoSuccess");
    },
    /**
     * 上传图片成功
     * @param response 上传响应
     * @param row 当前行
     */
    uploadImgSuccess(response, row) {
      row.resId = response.id; // 图片id
      row.resType = response.type; // 图片类型
    },
    /**
     * 删除视频
     * @param row 当前行
     */
    uploadVideoRemove(row) {
      row.resId = undefined;
      row.resType = undefined;
      row.coverId = undefined;
      row.coverType = undefined;
    },
    /**
     * 删除图片
     * @param row 当前行
     */
    uploadImgRemove(row) {
      row.resId = undefined;
      row.resType = undefined;
    },
    /**
     * 验证素材组名称
     */
    validateLabel(rule, value, callback) {
      if (!value) {
        callback(new Error("素材组名称不能为空"));
        return;
      }
      callback();
    },
  },
};
</script>

<style scoped></style>
