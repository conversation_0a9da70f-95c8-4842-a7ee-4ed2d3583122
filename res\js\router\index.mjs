// import './tailwindcss.js'; // 临时注释此行，使用其他方式引入Tailwind CSS
import OpenAccount from '@/views/hdPartner/components/OpenAccount.js';
import PartnerAccountList from '@/views/hdPartner/components/PartnerAccountList.js';
import PartnerAccountAidList from '@/views/hdPartner/components/PartnerAccountAidList.js';
import ScProto from '@/views/scPortal/components/scProto/index.vue';
import ScTemplate from '@/views/scPortal/components/scTemplate/index.vue';
import ScMaterial from '@/views/scPortal/components/ScMaterial/index.vue';
import ScCategory from '@/views/scPortal/components/ScCategory/index.vue';

/**
 * 页面路由
 */
function initViewRouter() {
    return new VueRouter({
        routes: [
            // 合作方设置
            {
                path: '/hdPartner',
                redirect: '/hdPartner/openAccount'
            },
            {
                path: '/hdPartner/openAccount',
                component: OpenAccount
            },
            {
                path: '/hdPartner/partnerAccountList',
                component: PartnerAccountList
            },
            {
                path: '/hdPartner/partnerAccountAidList',
                component: PartnerAccountAidList
            },
            // 速创
            {
                path: '/scPortal',
                redirect: '/scPortal/scProto'
            },
            // 原型配置
            {
                path: '/scPortal/scProto',
                component: ScProto
            },
            // 模板配置
            {
                path: '/scPortal/scTemplate',
                component: ScTemplate
            },
            // 素材管理
            {
                path: '/scPortal/scMaterial',
                component: ScMaterial
            },
            // 分类管理
            {
                path: '/scPortal/scCategory',
                component: ScCategory
            }
        ]
    });
} 
window.initViewRouter = initViewRouter;