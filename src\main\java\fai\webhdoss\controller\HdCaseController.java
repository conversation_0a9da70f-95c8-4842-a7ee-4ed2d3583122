package fai.webhdoss.controller;

import fai.app.AcctDef;
import fai.cli.AcctCli;
import fai.comm.util.*;
import fai.hdUtil.HdTool;
import fai.hdUtil.JsonResult;
import fai.web.HdWebApp;
import fai.web.Web;
import fai.web.ymd.HdGameCopyUtil;
import fai.webhdoss.model.vo.HdCasePreViewVO;
import fai.webhdoss.service.HdCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description  案例接口
 * @date 2021/3/14 11:36
 */
@RestController
@Api(value = "案例相关业务接口")
@RequestMapping("/case")
public class HdCaseController {

    @Autowired
    private HdCaseService caseService;

    @PostMapping("/preview")
    @ApiOperation(value = "提交案例预览窗消息接口", httpMethod = "POST")
    public JsonResult settingPreview(@RequestBody HdCasePreViewVO casePreViewVO){
        return JsonResult.ok(caseService.settingPreview(casePreViewVO));
    }

    @PostMapping("/put-preview")
    @ApiOperation(value = "投放案例预览窗消息接口", httpMethod = "POST")
    public JsonResult putPreview(@RequestParam("caseId") @ApiParam(value = "案例id", required = true, defaultValue = "1") Integer caseId,
                                 @RequestParam("oAid") @ApiParam(value = "原aid", required = true, defaultValue = "1") Integer oAid,
                                 @RequestParam("oGameId") @ApiParam(value = "原活动id", required = true, defaultValue = "1") Integer oGameId,
                                 @RequestParam("isPut") @ApiParam(value = "是否投放", required = true, defaultValue = "false") Boolean isPut){
        return caseService.putPreview(caseId, oAid, oGameId, isPut);
    }

    @PostMapping("/preview-status")
    @ApiOperation(value = "更新案例预览窗上架状态接口", httpMethod = "POST")
    public JsonResult updateStatusPreview(@RequestParam("caseId") @ApiParam(value = "案例id", required = true, defaultValue = "1") Integer caseId,
                                          @RequestParam("oAid") @ApiParam(value = "原aid", required = true, defaultValue = "1") Integer oAid,
                                          @RequestParam("oGameId") @ApiParam(value = "原活动id", required = true, defaultValue = "1") Integer oGameId,
                                          @RequestParam("isPutOnShelf") @ApiParam(value = "是否上架", required = true, defaultValue = "false") Boolean isPutOnShelf){
        return caseService.updateStatusPreview(caseId, oAid, oGameId, isPutOnShelf);
    }

    /**以下是案例中台(凡科案例库)相关，不是互动案例库*/
    @GetMapping("/getHdCaseList")
    @ApiOperation(value = "获取所有案例", httpMethod = "GET")
    public JsonResult getHdCaseList(@RequestParam("aid") @NotNull(message = "aid is null") int aid) throws Exception {
        return caseService.getHdCaseList(aid);
    }

    @CrossOrigin("*")
    @RequestMapping("/getCaseListFromHd")
    @ApiOperation(value = "获取所有案例", httpMethod = "POST")
    public JsonResult getCaseListFromHd(@RequestParam("secretKey") @NotNull(message = "secretKey is null") String secretKey,
                                        @RequestParam() @NotNull(message = "caseList is null") String caseList) throws Exception {


        FaiList<Param> faiCaseList = FaiList.parseParamList(caseList);

        return caseService.getCaseListFromHd(secretKey, faiCaseList);
    }

    @GetMapping("/getGameLinkAndQrcode")
    @ApiOperation(value = "获取案例的链接和二维码", httpMethod = "GET")
    public JsonResult getCaseListFromHd(@RequestParam("aid") @NotNull(message = "aid is null") Integer aid,
                                        @RequestParam("siteId") @NotNull(message = "siteId is null") Integer siteId,
                                        @RequestParam("style") @NotNull(message = "style is null") Integer style) throws Exception {

        //判断是否分销
        AcctCli acctCli = new AcctCli(1);
        if(!acctCli.init()){
            Log.logErr("acctCli init err");
            return null;
        }
        boolean isOem = false;
        Param acctPar = new Param();
        int rt = acctCli.getAcctInfo(aid,acctPar);
        if(rt!= Errno.OK){
            Log.logErr(rt,"get acct err");
        }
        int agentAid = acctPar.getInt(AcctDef.Info.AGENT_AID, 0);
        if(agentAid>0){//>0则为分销
            isOem = true;
        }

        String httpStr = Web.getDebug() ? "http://" : "https://";
        String portalHost = isOem ? HdWebApp.getOemHdPortalDomain() : HdWebApp.getHdPortalDomain();
        String gameHost = isOem ? HdWebApp.getOemHdGameDomain() : HdWebApp.getHdGameDomain();
        String token = HdTool.encryptGameId(aid, siteId);
        String gameLink = httpStr + gameHost + "/"+ aid+ "/"+ token +  "/load.html?style=" + style;
        String gameCode = "//" + portalHost + "/qrCode.jsp?cmd=qrurl&siteUrl=" + Encoder.encodeUrl(gameLink);

        return JsonResult.success(new Param().setString("gameLink", gameLink).setString("qrCode", gameCode));
    }

    @RequestMapping(value = "gameCopyTest")
    public JsonResult gameCopyTest(int sourceAid, int sourceGameId, int targetAid) {
        try{
            Param result = HdGameCopyUtil.hdGameCopy(sourceAid,sourceGameId,targetAid);
            return JsonResult.success(result);
        }catch (Exception e){
            return JsonResult.error(e.getMessage());
        }
    }
}
