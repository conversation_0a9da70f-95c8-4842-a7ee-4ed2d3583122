<template>
  <div class="h-full overflow-auto p-[0_30px] pb-[100px]">
    <keep-alive include="ScProtoList">
      <component
        :is="component"
        :editId="editId"
        :createType="createType"
        :isReloadList="isReloadList"
        @changeComponent="changeComponent"
      />
    </keep-alive>
  </div>
</template>

<script>
import ScProtoList from "./scProtoList/index.vue";
import ScProtoEdit from "./scProtoEdit/index.vue";
import { ScProductType } from "@/views/scPortal/config/index.js";

export default {
  name: "ScProto",
  components: {
    ScProtoList,
    ScProtoEdit,
  },
  data() {
    return {
      component: "ScProtoList",
      createType: ScProductType.VIDEO, // 创建类型
      editId: 0, // 编辑id
      isReloadList: false, // 是否刷新列表
    };
  },
  created() {
    this.initConfInfo();
    this.parseUrlParams();
  },
  methods: {
    initConfInfo() {
      this.$store.dispatch("scPortal/getStaffInfo").catch((error) => {
        this.$message.error(error);
      });
    },
    changeComponent(component, data = {}) {
      this.component = component;
      const {
        createType = ScProductType.VIDEO,
        editId = null,
        isReloadList = false,
      } = data;

      this.createType = createType;
      this.editId = editId;
      this.isReloadList = isReloadList;
    },
    /**
     * 解析 URL 参数并处理
     */
    parseUrlParams() {
      // 方法1：使用 Vue Router 获取查询参数
      if (this.$route && this.$route.query) {
        const { protoId } = this.$route.query;
        if (protoId) {
          // 如果有这两个参数，直接跳转到编辑页面
          this.changeComponent("ScProtoEdit", {
            editId: parseInt(protoId),
          });
        }
      }
    },
  },
};
</script>

<style lang="scss">
@import "../../styles/elementui.scss";
</style>
