<template>
  <div class="h-full overflow-auto p-[0_30px] pb-[100px]">
    <keep-alive include="ScProtoList">
      <component
        :is="component"
        :editId="editId"
        :createType="createType"
        :isReloadList="isReloadList"
        @changeComponent="changeComponent"
      />
    </keep-alive>
  </div>
</template>

<script>
import ScProtoList from "./scProtoList/index.vue";
import ScProtoEdit from "./scProtoEdit/index.vue";
import { ScProductType } from "@/views/scPortal/config/index.js";

export default {
  name: "ScProto",
  components: {
    ScProtoList,
    ScProtoEdit,
  },
  data() {
    return {
      component: "ScProtoList",
      createType: ScProductType.VIDEO, // 创建类型
      editId: 0, // 编辑id
      isReloadList: false, // 是否刷新列表
    };
  },
  methods: {
    changeComponent(component, data = {}) {
      this.component = component;
      const {
        createType = ScProductType.VIDEO,
        editId = null,
        isReloadList = false,
      } = data;

      this.createType = createType;
      this.editId = editId;
      this.isReloadList = isReloadList;
    },
  },
};
</script>

<style lang="scss">
@import "../../styles/elementui.scss";
</style>
