// vue-router跳转的导航，非iframe嵌入jsp
const vueRouterNav = [
  'hdPartner',
  'scPortal',
]

var leftNavObj = new Vue({
  el: '.fai-main-nav',
  data: {
    isCollapse: false,
    active: "showLeftNav",
    navList: [],
  },
  updated: function(){
    // 刷新的时候回到对应的导航页面
    var result = Fai.url.getNavFlag();
    var isHdPartner = vueRouterNav.includes(result[1]);
    if(result != "" && (result[0].indexOf(".jsp") > 0 || isHdPartner)){
      var active = result[0].replace("#", "");
      this.active = active;
      setTimeout(function(){
        faiIfram.$data.src = active;
        faiIfram.$data.isUseIframe = !isHdPartner;
      },500)
    }
  },
  methods: {
    handleSelect(index) {
      // 显示/收起侧边导航
      if(index == "showLeftNav") {
        this.isCollapse = this.isCollapse ? false : true;
      }else{
        // 选中导航的时候
        var reg = /\/([\s\S]+)\/([\s\S]+)/gi
        var result = "";
        if ((result = reg.exec(index)) != null) {
          var url = document.location.protocol+"//"+document.location.host;
          url +="#/"+result[1]+"/"+result[2];
          document.location.href = url;
        }
        faiIfram.$data.src = index;
        faiIfram.$data.isUseIframe = !vueRouterNav.includes(result[1]);
      }
    },
  }
});