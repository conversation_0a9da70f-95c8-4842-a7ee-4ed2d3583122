package fai.webhdoss.service.impl;

import fai.MgFilesInfSvr.interfaces.cli.MgFilesInfCli;
import fai.MgFilesInfSvr.interfaces.entity.FilesGroupEntity;
import fai.MgFilesInfSvr.interfaces.utils.MgFilesArg;
import fai.app.FileStgDef;
import fai.app.ScResDef;
import fai.cli.ScResCli;
import fai.comm.jnetkit.server.fai.RemoteStandResult;
import fai.comm.middleground.FaiValObj;
import fai.comm.util.*;
import fai.comm.util.exception.ScAssert;
import fai.comm.util.tts.ScTTSUtil;
import fai.comm.util.util.ScFileUtil;
import fai.entity.res.ScResEntity;
import fai.entity.res.ScResExtraModal;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.web.Core;
import fai.web.FileUpload;
import fai.web.Web;
import fai.webhdoss.model.vo.scRes.ScResListVO;
import fai.webhdoss.model.vo.scRes.ScResVO;
import fai.webhdoss.service.ScResService;
import fai.webhdoss.util.ScFileUploadUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.ByteBuffer;
import java.util.Calendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 25-05-28 17:54
 */
@Service
public class ScResServiceImpl implements ScResService {

    private final ScResCli scResCli;

    @Autowired
    public ScResServiceImpl(ScResCli scResCli) {
        this.scResCli = scResCli;
    }

    @Override
    public JsonResult getScCategoryList(ScResVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        HdAssert.Biz.judgeNotLog(vo.getType() <= 0, Errno.ARGS_ERROR, "类型不能为空");

        List<Param> list = getCategoryList(vo.getType());
        JsonResult result = JsonResult.success(list);
        result.setTotalSize(list.size());
        return result;
    }

    public List<Param> getCategoryList(int type) {
        ScAssert.Biz.judgeNotLog(type != ScResDef.Type.OPT_VOICE && type != ScResDef.Type.OPT_BGM, Errno.ERROR, "不支持的资源类型！");
        final int sysAid = Web.getFaiCid();

        String name = type == ScResDef.Type.OPT_BGM ? "背景音乐" : "配音";
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.AID, ParamMatcher.EQ, sysAid);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.GROUP_NAME, ParamMatcher.EQ, name);

        List<Param> groupList = getMgFilesGroupList(sysAid, searchArg);
        if (groupList.isEmpty()) return groupList;

        Param parentFolderEntity = groupList.get(0);
        Integer parentId = parentFolderEntity.getInt("id", -1);

        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.PARENT_ID, ParamMatcher.EQ, parentId);
        searchArg.matcher.and(FilesGroupEntity.GroupInfo.AID, ParamMatcher.EQ, sysAid);
        searchArg.start = 0;
        searchArg.limit = 1000;
        List<Param> subList = getMgFilesGroupList(sysAid, searchArg);

        Param allFolder = new Param(); // 增加一个全部的分类
        allFolder.setInt("id", 0);
        allFolder.setString("name", "全部");
        subList.add(0, allFolder);
        return subList;
    }
    private List<Param> getMgFilesGroupList(int aid, SearchArg searchArg) {
        final int tid = FaiValObj.TermId.SC;
        MgFilesInfCli mgCli = new MgFilesInfCli(Core.getFlow());
        mgCli.init();
        MgFilesArg mgFilesArg = new MgFilesArg.Builder(aid, tid, 0, 0, 0)
                .setSearchArg(searchArg)
                .build();
        FaiList<Param> groupList = new FaiList<>();
        int rt = mgCli.getFilesGroupList(mgFilesArg, groupList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "get groupList error;aid=" + aid);
            return new FaiList<>();
        }
        return groupList.stream().map(this::convertFolder).collect(Collectors.toList());
    }
    private Param convertFolder(Param groupInfo) {
        Param folder = new Param();
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.AID);
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.PARENT_ID);
        folder.assign(groupInfo, FilesGroupEntity.GroupInfo.RL_GROUP_ID);
        folder.setString("name", groupInfo.getString(FilesGroupEntity.GroupInfo.GROUP_NAME, ""));
        folder.setInt("id", groupInfo.getInt(FilesGroupEntity.GroupInfo.RL_GROUP_ID, 0));
        folder.setCalendar("createTime", groupInfo.getCalendar(FilesGroupEntity.GroupInfo.CREATE_TIME));
        folder.setCalendar("updateTime", groupInfo.getCalendar(FilesGroupEntity.GroupInfo.UPDATE_TIME));
        return folder;
    }

    @Override
    public JsonResult addScRes(ScResVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");

        int type = vo.getType();
        String coverId = vo.getCover();
        HdAssert.Biz.judgeNotLog(type <= 0, Errno.ARGS_ERROR, "类型不能为空");

        // 配音数据检查
        Ref<String> msgRef = new Ref<>();
        int rt = checkTimbre(vo, "add", msgRef);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "checkTimbre error;vo=%s", vo);
            return JsonResult.error(rt, msgRef.value);
        }

        // 在上传时，已经往 scRes 表写入过数据了
        // 这里要做的事情是将 音频文件的 resId 和 音频的封面 cover 进行关联
        // 也就是执行更新操作
        ScResExtraModal extraModal = new ScResExtraModal();
        extraModal.setCover(coverId);
        extraModal.setVoiceType(vo.getVoiceType());
        extraModal.setExtraType(vo.getExtraType());
        extraModal.setDuration(vo.getDuration());
        extraModal.setBlockAuto(vo.getBlockAuto());

        ScResEntity scRes = new ScResEntity();
//        scRes.setType(type);
        scRes.setResId(vo.getResId());
        scRes.setName(vo.getName());
        scRes.setFolderId(vo.getCategoryId());
        scRes.setFileSize(vo.getFileSize());
        scRes.setFileType(vo.getFileType());
        scRes.setExtraModal(extraModal);

        Log.logStd("addRes=%s; vo=%s;", scRes, vo);

        String resId = scRes.getResId();
        String name = scRes.getName();
        int fileType = scRes.getFileType();
        HdAssert.Biz.judgeNotLog(Str.isEmpty(resId), Errno.ARGS_ERROR, "请先上传音频");
        HdAssert.Biz.judgeNotLog(Str.isEmpty(name), Errno.ARGS_ERROR, "名称不能为空");
        HdAssert.Biz.judgeNotLog(fileType <= 0, Errno.ARGS_ERROR, "文件类型不能为空");
        RemoteStandResult result = scResCli.setRes(aid, scRes);
        if (!result.isSuccess()) {
            return JsonResult.error("添加失败", result.getRt());
        }

        // 将状态从临时改为正式
        result = turnResToNormal(aid, resId, coverId);
        if (!result.isSuccess()) {
            return JsonResult.error("添加失败，设置状态失败", result.getRt());
        }
        return JsonResult.success("添加成功");
    }

    /**
     * 1. 检查配音id是否唯一，如果存在，则返回错误
     * 2. 检查配音音频是否存在，如果不存在，则生成配音音频并上传
     * @param vo 前端传来的参数
     * @param method add/set，对应添加和修改
     * @param msgRef 错误信息
     * @return 错误码
     */
    private int checkTimbre(ScResVO vo, String method, Ref<String> msgRef) {
        int rt = Errno.OK;
        int type = vo.getType();
        // 不是配音的都不需要处理
        if (type != ScResDef.Type.OPT_VOICE) {
            return rt;
        }
        int aid = Core.getAid();
        // 配音id必须唯一，不能重复
        String voiceType = vo.getVoiceType();
        if (Str.isEmpty(voiceType)) {
            msgRef.value = "配音id不能为空";
            return Errno.ARGS_ERROR;
        }
        if (vo.getFileType() == -1) {
            vo.setFileType(FileEx.Type.WAV); // 都是 wav 格式的
        }

        List<ScResEntity> existList = getExistVoice(voiceType);

        String resId = vo.getResId();
        if ("add".equals(method)) {
            // 添加配音时，直接判断是否已有相同的配音id
            int count = existList.size();
            if (count > 0) {
                msgRef.value = "配音id已存在";
                return Errno.ALREADY_EXISTED;
            }

            // 如果前端没有传resId，则说明需要后端处理，生成配音音频并且上传
            if (!resId.isEmpty()) {
                Log.logStd("already has audio, resId=%s", resId);
                return rt;
            }

            // 后端处理配音音频，生成resId，并上传
            return buildTimbre(vo, msgRef);
        }
        if ("set".equals(method)) {
            if (resId == null || resId.isEmpty()) {
                msgRef.value = "参数错误，resId不能为空";
                return Errno.ARGS_ERROR;
            }
            // 修改配音时，判断是否有相同的配音id
            int count = 0;
            for (ScResEntity scRes : existList) {
                if (!scRes.getResId().equals(resId)) {
                    count++;
                }
            }
            if (count > 0) {
                msgRef.value = "配音id已存在";
                return Errno.ALREADY_EXISTED;
            }

            RemoteStandResult remoteResult = scResCli.getRes(aid, resId);
            rt = remoteResult.getRt();
            if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
                Log.logErr(rt, "getRes error;resId=%s", resId);
                msgRef.value = "获取配音数据失败";
                return rt;
            }
            ScResEntity existScRes = remoteResult.getObject(ScResDef.Protocol.Key.INFO, ScResEntity.class);
            if (existScRes == null) {
                msgRef.value = "当前配音不存在，请刷新页面后重试";
                return Errno.ARGS_ERROR;
            }
            FaiList<String> delResIds = new FaiList<>();
            ScResExtraModal existExtra = existScRes.getExtraModal();
            if (existExtra == null) {
                msgRef.value = "配音数据错误，请刷新页面后重试";
                return Errno.ARGS_ERROR;
            }
            String oldVoiceType = existExtra.getVoiceType();
            // 判断如果修改到了配音id，则需要重新生成配音音频并上传
            if (Str.isEmpty(oldVoiceType) && !oldVoiceType.equals(voiceType)) {
                // 后端处理配音音频，生成resId，并上传
                rt = buildTimbre(vo, msgRef);
                if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
                    Log.logErr(rt, "buildTimbre error;");
                    return rt;
                }
                // 旧的resId，需要删除
                delResIds.add(existScRes.getResId());
                Log.logStd("voiceType changed, old=%s; new=%s; resId=%s", oldVoiceType, voiceType, resId);
            }
            // 判断封面是否有改动
            String newCoverId = vo.getCover();
            if (!Str.isEmpty(newCoverId) && !newCoverId.equals(existExtra.getCover())) {
                delResIds.add(newCoverId);
                Log.logStd("cover changed, old=%s; new=%s; resId=%s", existExtra.getCover(), newCoverId, resId);
            }

            // 如果配音音频有改动，或者封面有改动，则需要删除旧的配音音频和封面
            if (!delResIds.isEmpty()) {
                Log.logStd("timbreId or cover changed, need to del resId=%s", delResIds);
                RemoteStandResult result = scResCli.batchDelRes(aid, delResIds);
                rt = result.getRt();
                if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
                    Log.logErr(rt, "batchDelRes error;ids=%s;", delResIds);
                    return rt;
                }
            }
//            vo.setId(existScRes.getId()); // 拿到原来的id，用于更新 前端会传。不用自己拿了
        }
        return rt;
    }

    private List<ScResEntity> getExistVoice(String voiceType) {
        final int aid = Core.getAid();
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.matcher.and(ScResDef.Info.AID, ParamMatcher.EQ, aid);
        searchArg.matcher.and(ScResDef.Info.TYPE, ParamMatcher.EQ, ScResDef.Type.OPT_VOICE);
        searchArg.matcher.and(ScResDef.Info.STATUS, ParamMatcher.EQ, ScResDef.Status.NORMAL);

        RemoteStandResult resCount = scResCli.getResList(aid, searchArg, "", "");
        HdAssert.Biz.judge(!resCount.isSuccess(), Errno.ERROR, "获取配音数据失败").log("get res count error;matcher=%s;", searchArg.matcher);

        @SuppressWarnings("unchecked")
        List<ScResEntity> existList = resCount.getObject(ScResDef.Protocol.Key.LIST, List.class);
        HdAssert.Biz.judge(existList == null, Errno.ERROR, "获取配音数据失败").log("existList is null, voiceType=%d", voiceType);

        assert existList != null;
        if (existList.isEmpty()) {
            return new FaiList<>();
        }

        List<ScResEntity> collect = existList.stream().filter(e -> {
            ScResExtraModal extraModal = e.getExtraModal();
            if (extraModal == null) {
                return false;
            }
            String existVoiceType = extraModal.getVoiceType();
            if (Str.isEmpty(existVoiceType)) {
                return false;
            }
            return existVoiceType.equals(voiceType);
        }).collect(Collectors.toList());
        Log.logStd("voiceType=%d;filter size=%d", voiceType, collect.size());
        return collect;
    }

    // 后端处理配音音频，生成resId，并上传
    private int buildTimbre(ScResVO vo, Ref<String> msgRef) {
        final String text = "您好，欢迎来到速创。灵感来袭，快速上手，只需一步，创意即刻呈现。";
        String voiceType = vo.getVoiceType();
        final String path = String.format("/home/<USER>/tmp/sc/voice/%s.wav", voiceType);

        // 判断是否已有配音音频
        ByteBuffer wavBuffer;
        if (new File(path).exists()) {
            Log.logStd("voice already existed, path=%s", path);
            wavBuffer = ScFileUtil.toByteBuffer(path);
        } else {
            FaiList<String> textList = new FaiList<>();
            textList.add(text);
            Log.logStd("start tts task; voiceType=%d;", voiceType);
            Pair<Boolean, Param> result = new Pair<>(false, null);
            try {
                result = ScTTSUtil.textToVoiceSaveWav(path, textList, voiceType, vo.getExtraType());
            } catch (Exception e) {
                e.printStackTrace();
                Log.logErr("tts error;text=%s;", text, e);
            }

            Param second = result.second;
            if (!result.first || second == null) {
                Log.logErr("build voice error;result=%s", result);
                msgRef.value = "生成配音音频失败";
                return Errno.ERROR;
            }
            wavBuffer = second.getBuffer("wavBuffer");
        }

        if (wavBuffer == null) {
            msgRef.value = "生成配音音频失败";
            Log.logErr("wavBuffer is null, path=%s", path);
            return Errno.ERROR;
        }

        String name = vo.getName() + ".wav";
        Param uploadInfo = new Param();
        int rt = ScFileUploadUtil.uploadFileSc(wavBuffer, name, Core.getAid(), ScResDef.Type.OPT_VOICE, uploadInfo);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "uploadFileSc error;vo=%s", vo);
            return rt;
        }
        String resId = uploadInfo.getString(FileUpload.Info.ID, "");
        Log.logStd("uploadFileSc resId=%s", resId);
        if (Str.isEmpty(resId)) {
            Log.logErr("uploadFileSc error;vo=%s", vo);
            msgRef.value = "上传配音音频失败";
            return Errno.ERROR;
        }
        vo.setResId(resId);
        Log.logStd("build voice success;resId=%s; vo=%s", resId, vo);
        return rt;
    }

    // 将 resId 和 cover 的状态从临时改为正式
    private RemoteStandResult turnResToNormal(int aid, String resId, String coverId) {
        // 将状态从临时改为正式
        ScResEntity upScRes = new ScResEntity();
        upScRes.setStatus(ScResDef.Status.NORMAL);
        upScRes.setUpdateTime(Calendar.getInstance());

        FaiList<String> resIdList = new FaiList<>();
        resIdList.add(resId);
        if (coverId != null && !coverId.isEmpty()) {
            resIdList.add(coverId);
        }
        ParamMatcher matcher = new ParamMatcher();
        matcher.and(ScResDef.Info.AID, ParamMatcher.EQ, aid);
        matcher.and(ScResDef.Info.RES_ID, ParamMatcher.IN, resIdList);
//        matcher.and(ScResDef.Info.TYPE, ParamMatcher.EQ, type);
        return scResCli.batchSetRes(upScRes, matcher);
    }

    @Override
    public JsonResult setScRes(ScResVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        int type = vo.getType();
        String voiceType = vo.getVoiceType();
        HdAssert.Biz.judgeNotLog(type == ScResDef.Type.OPT_VOICE && (Str.isEmpty(voiceType)), Errno.ARGS_ERROR, "请填写配音id");

        // 配音数据检查
        Ref<String> msgRef = new Ref<>();
        int rt = checkTimbre(vo, "set", msgRef);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "checkTimbre error;vo=%s", vo);
            return JsonResult.error(rt, msgRef.value);
        }

        ScResExtraModal extraModal = new ScResExtraModal();
        extraModal.setVoiceType(voiceType);
        extraModal.setCover(vo.getCover());
        extraModal.setExtraType(vo.getExtraType());
        extraModal.setDuration(vo.getDuration());
        extraModal.setBlockAuto(vo.getBlockAuto());

        ScResEntity scRes = new ScResEntity();
        scRes.setResId(vo.getResId());
        scRes.setStatus(ScResDef.Status.NORMAL);
        scRes.setName(vo.getName());
        scRes.setFolderId(vo.getCategoryId());
        scRes.setExtraModal(extraModal);

        String resId = scRes.getResId();
        HdAssert.Biz.judgeNotLog(Str.isEmpty(resId), Errno.ARGS_ERROR, "请先上传音频");
        String name = scRes.getName();
        HdAssert.Biz.judgeNotLog(Str.isEmpty(name), Errno.ARGS_ERROR, "名称不能为空");

        // 可能会涉及到 resId 的变化，必须使用id来更新
        int setId = vo.getId();
        HdAssert.Biz.judgeNotLog(setId <= 0, Errno.ARGS_ERROR, "获取数据id失败");
        Log.logStd("upScRes=%s; vo=%s", scRes, vo);

        ParamMatcher matcher = new ParamMatcher();
        matcher.and(ScResDef.Info.AID, ParamMatcher.EQ, aid);
        matcher.and(ScResDef.Info.TYPE, ParamMatcher.EQ, type);
        matcher.and(ScResDef.Info.ID, ParamMatcher.EQ, setId);   // 使用id来更新
        RemoteStandResult result = scResCli.batchSetRes(scRes, matcher);
        if (!result.isSuccess()) {
            return JsonResult.error("修改失败", result.getRt());
        }

        // 可能音频和封面都有改动，需要将新的文件设置为正式资源
        result = turnResToNormal(aid, resId, vo.getCover());
        if (!result.isSuccess()) {
            return JsonResult.error("修改失败，设置状态失败", result.getRt());
        }
        return JsonResult.success("修改成功");
    }

    @Override
    public JsonResult delScRes(ScResVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");

        String resId = vo.getResId();
        String coverId = vo.getCover();

        HdAssert.Biz.judgeNotLog(Str.isEmpty(resId), Errno.ARGS_ERROR, "音频ID不能为空");

        FaiList<String> idList = new FaiList<>();
        idList.add(resId);
        if (coverId != null && !coverId.isEmpty()) { // 有封面时一并删除
            idList.add(coverId);
        }
        RemoteStandResult result = scResCli.batchDelRes(aid, idList);
        if (!result.isSuccess()) {
            return JsonResult.error("删除失败", result.getRt());
        }
        return JsonResult.success("删除成功");
    }

    @Override
    public JsonResult getScResList(ScResListVO vo) {
        int aid = Core.getAid();
        HdAssert.Biz.judgeNotLog(aid <= 0, Errno.ARGS_ERROR, "请先登录");
        HdAssert.Biz.judgeNotLog(vo == null, Errno.ARGS_ERROR, "参数不能为空");
        HdAssert.Biz.judgeNotLog(vo.getType() <= 0, Errno.ARGS_ERROR, "类型不能为空");

        String name = vo.getName();
        int categoryId = vo.getCategoryId();

        // 分页信息
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.start = vo.getOffset();
        searchArg.limit = vo.getPageLimit();
        searchArg.totalSize = new Ref<>();

        // 默认按更新时间倒序
        searchArg.cmpor = new ParamComparator(ScResDef.Info.UPDATE_TIME, true);

        // 筛选条件
        searchArg.matcher.and(ScResDef.Info.TYPE, ParamMatcher.EQ, vo.getType());
        searchArg.matcher.and(ScResDef.Info.STATUS, ParamMatcher.EQ, ScResDef.Status.NORMAL);
        searchArg.matcher.and(ScResDef.Info.AID, ParamMatcher.EQ, aid);

        if (name != null && !name.isEmpty()) {
            searchArg.matcher.and(ScResDef.Info.NAME, ParamMatcher.LK, name);
        }
        if (categoryId > 0) {
            searchArg.matcher.and(ScResDef.Info.FOLDER_ID, ParamMatcher.EQ, categoryId);
        }
        if (vo.getExtraType() > 0) {
            searchArg.matcher.and(ScResDef.Info.EXTRA, ParamMatcher.LK, "\"extraType\":" + vo.getExtraType());
        }

        // 获取数据
        RemoteStandResult remoteResult = scResCli.getResList(aid, searchArg, "", "");
        if (!remoteResult.isSuccess()) {
            return JsonResult.error("获取失败", remoteResult.getRt());
        }

        @SuppressWarnings("unchecked")
        List<ScResEntity> list = remoteResult.getObject(ScResDef.Protocol.Key.LIST, List.class);
        int totalSize = remoteResult.getObject(ScResDef.Protocol.Key.TOTAL_SIZE, Integer.class);

        List<Param> categoryList = getCategoryList(vo.getType());
        Param id2NameMap = new Param(true);
        for (Param category : categoryList) {
            int id = category.getInt("id", -1);
            String categoryName = category.getString("name", "-");
            id2NameMap.setString(String.valueOf(id), categoryName);
        }

        FaiList<Param> resultList = new FaiList<>();
        for (ScResEntity resEntity : list) {
            Param info = resEntity.toParam();
            if (info == null) {
                continue;
            }
            ScResExtraModal extraModal = resEntity.getExtraModal();
            if (extraModal != null) {
                info.append(extraModal.toParam());
                String cover = extraModal.getCover();
                if (!Str.isEmpty(cover)) {
                    Param fileInfo = FileStgDef.parseFileId(cover);
                    info.setInt("coverType", fileInfo.getInt(FileStgDef.Info.TYPE));
                }
                Integer extraType = extraModal.getExtraType();
                if (extraType != null) {
                    info.setInt("extraType", extraType);
                    info.setString("extraTypeName", ScResDef.ExtraType.getName(extraType));
                }
            }

            // 补充分类名称
            int folderId = resEntity.getFolderId();
            String categoryName = id2NameMap.getString(String.valueOf(folderId), "-");
            info.setString("categoryName", categoryName);
            info.setInt("categoryId", folderId);

            // 返回结果
            resultList.add(info);
        }

        JsonResult result = JsonResult.success(resultList);
        result.setTotalSize(totalSize);
        return result;
    }

    @Override
    public JsonResult uploadTmpFile(HttpServletRequest request, int scResType) {
        // 大小限制：100M
        int limitM = 100;

        Param info = new Param();
        int rt = ScFileUploadUtil.uploadFile4Sc(request.getSession().getServletContext(), request, scResType, Web.getFaiCid(), info, limitM);
        JsonResult result = new JsonResult(rt);

        Integer limit = info.getInt(FileUpload.Info.LIMIT, limitM);
        switch (rt) {
            case Errno.OK:
                info.setBoolean("success", true);
                result.setMsg("上传成功");
                result.setData(info);
                return result;
            case FileUpload.ErrnoUpload.FILE_SIZE_LIMIT:
                result.setMsg("上传失败：单个文件的大小超过" + limit + "M");
                return result;
            case FileUpload.ErrnoUpload.IMG_SIZE_LIMIT:
                result.setMsg("上传失败：文件大小超过" + limit + "M，请压缩处理后再上传");
                return result;
            case FileUpload.ErrnoUpload.FILE_CTRL_NOT_FOUND:
                result.setMsg("上传失败：请尝试更换您的浏览器");
                return result;
            case FileUpload.ErrnoUpload.FILE_TYPE_INVALID:
                result.setMsg("上传失败：文件格式不合法（只能设置jpg、jpeg、gif、bmp或png格式的图片）");
                return result;
            case FileUpload.ErrnoUpload.FILE_NAME_EXIST:
                result.setMsg("上传失败：文件名已经存在（请修改文件名后再试）");
                return result;
            case FileUpload.ErrnoUpload.FILE_TYPE_FORMAT_ERROR:
                Integer width = info.getInt(FileUpload.Info.WIDTH);
                Integer height = info.getInt(FileUpload.Info.HEIGHT);
                if (width != null && height != null) {
                    result.setMsg("压缩图片错误，建议缩小图片为" + width + "*" + height + "以内");
                } else {
                    result.setMsg("读取图片错误，建议您先使用图像处理软件转换图片格式");
                }
                return result;
            case FileUpload.ErrnoUpload.FILE_MP3_FORMAT_ERROR:
                result.setMsg("您上传的文件不是标准的mp3格式，请检查后再试");
                return result;
            case FileUpload.ErrnoUpload.FILE_FLV_FORMAT_ERROR:
                result.setMsg("您上传的文件不是标准的flv格式，请检查后再试");
                return result;
            case Errno.SVR_READONLY:
                result.setMsg("系统维护中，暂时不能保存，请稍后重试");
                return result;
            case FileUpload.ErrnoUpload.OVER_LIMIT:
                result.setMsg("上传失败：超过当前网站版本的资源库容量");
                return result;
            default:
                result.setMsg("系统错误 请稍后再试");
                return result;
        }
    }

}
