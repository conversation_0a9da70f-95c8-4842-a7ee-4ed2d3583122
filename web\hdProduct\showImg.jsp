<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
    String sku = Parser.parseString(request.getParameter("sku"), "");
    String key = Parser.parseString(request.getParameter("key"), "");
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>商品详情字段</title>
    <%=Web.getToken()%>
    <style>
        * {
			margin: 0;
			padding: 0;
		}
		body {
			background-color: #000;
		}
		#app {
			width: 600px;
			margin: 0 auto;
			background-color: #fff;
		}
		#app img {
			width: 100%;
			vertical-align: middle;
        }
        [v-cloak]{
			display: none;
		}
		::-webkit-scrollbar-track {
		  border-radius: 0px;
		  background: #e7e7e7;
		  position: absolute;
		}
		::-webkit-scrollbar-thumb {
		  border-radius: 10px;
		  background: #ccc;
		  position: absolute;
		}
		.scrollBox::-webkit-scrollbar-thumb {
		    background: #ccc;
		}
		.scrollBox:hover::-webkit-scrollbar-thumb {
		    background: #949494;
		}
		.scrollBox::-webkit-scrollbar-thumb:hover {
		    background: #949494;
		}
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <div v-if="key === 'wxintroduction'">
            <div v-for= "(imgItem, index) in imgArr" :key="index">
                <img :src="imgItem"/>
            </div>
        </div>
        <div v-else>
            <div v-html="htmlStr"></div>
        </div>
    </div>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
    <script>
        var app = new Vue({
            el: "#app",
            data: {
                sku: '<%=sku%>',
                key: '<%=key%>',
                imgArr: [],
                htmlStr: ''
            },
            created: function () {
                this.getJdGiftBySku();
            },
            methods: {
                getJdGiftBySku: function () {
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/ajax/hdJdGift_h.jsp?cmd=getJdGiftByJd',
                        data: {
                            sku: _this.sku
                        },
                        error: function(){
                            alert('系统繁忙，请稍后重试');
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                if(_this.key === 'wxintroduction'){
                                    _this.imgArr = $.parseJSON(result.info[_this.key]);
                                }else{
                                    _this.htmlStr = result.info[_this.key];
                                }
                            }else{
                                alert('商品参数错误');
                            }
						}
                    });
                }
            }
        });
    </script>
</body>
</html>