<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.webportal.*"%>
<%@ page import="java.nio.*"%>
<%@ page import="java.io.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.web.inf.HdOss" %>
<%@ page import="fai.hdUtil.HdMSOfficeConverter" %>
<%@ page import="fai.webhdoss.WebHdOss" %>
<%@ page import="fai.web.inf.Kid" %>
<%@ page import="java.util.Set" %>
<%@ page import="java.util.HashSet" %>
<%@ page import="java.util.*"%>

<%!
	private String uploadAwardCodeExcel(HttpServletRequest request) throws Exception{
		FormRequest freq = new FormRequest();
		int rt = Errno.ERROR;
		FaiList<Param> cl  =  null;
		rt = freq.attachServlet(getServletContext(), request); // 上传到临时目录
		if (rt != Errno.OK) {
			return "{\"success\":false,\"msg\":\"文档格式错误，有可能数据项被修改、安慰奖项填写多个，请下载模板重新编辑再上传。\"}";
		}
		String ctrl = request.getParameter("ctrl");
		if (ctrl == null || ctrl.isEmpty()) {
			ctrl = "filedata";
		}
		FormFile formFile = freq.getFile(ctrl); // 	获取上传的临时文件

		if (formFile == null) {
			return "{\"success\":false,\"msg\":\"参数错误\"}";
		}
		String fileName = formFile.getFileName();

		// 解析文档并获取表头信息
		Param rowParam = new Param();
		rowParam.setString( WXDeviceDef.Info.DEVICE_ID , "设备ID" );
		rowParam.setString( WXDeviceDef.Info.UUID , "设备UUID" );
		rowParam.setString( WXDeviceDef.Info.MAJOR , "设备major值(10进制)" );
		rowParam.setString( WXDeviceDef.Info.MINOR , "设备minor值(10进制)" );
		rowParam.setString( WXDeviceDef.Info.NOTE , "备注信息" );

		Param _rowParam = new Param();
		cl = MSOfficeConverter.analyzeShakeDeviceExcel(fileName, formFile.get(), rowParam,  _rowParam, "设备ID");

		if(cl == null){
			return "{\"success\":false,\"tip\":true,\"msg\":\"解析错误，请检查文档格式。\"}";
		}

		if(cl.isEmpty()){
			return "{\"success\":false,\"tip\":true,\"msg\":\"没有可导入的数据，请检查文档数据。\"}";
		}
		return "{\"success\":true,\"codeList\": "+cl.toJson()+",\"msg\":\"上传成功\",\"filename\":\""+fileName+"\"}";
	}

	private String addWXDeviceList(HttpServletRequest request) throws Exception{
		FaiList<Param> deviceList = FaiList.parseParamList(request.getParameter("deviceList"),new FaiList<Param>());
		OssHd ossHd = new OssHd();
		FaiList<Integer> pageList = new FaiList<Integer>();
		pageList.add(3075070);
		int rt = ossHd.addWXDevice(deviceList,pageList);
		if(rt != Errno.OK) {
			return "{\"success\":false,\"rt\":"+ rt +",\"msg\":\"列表数据存在错误，请检查。\"}";
		}
		return "{\"success\":true,\"msg\":\"添加设备成功\"}";
	}

	private String addWXPage(HttpServletRequest request) throws Exception{
		OssHd ossHd = new OssHd();
		Ref<Integer> pageId = new Ref<Integer>();
		int rt = ossHd.addWXPage(1, HdProfDef.DefaultAppId.HD, WxAuthorizerDef.Belong.HD, Web.getEnvMode(), pageId);
		if(rt != Errno.OK) {
			return "{\"success\":false,\"rt\":"+ rt +",\"msg\":\"添加页面失败\"}";
		}
		return "{\"success\":true,\"msg\":\"添加页面成功\",\"pageId\":"+pageId.value+"}";
	}

	private String getUploadSize(HttpServletRequest request) throws Exception{
		String res = "";
		int cid = 1;
		String fileMd5 = request.getParameter("fileMd5");
		String filename = request.getParameter("fileName");
		// totalSize 需要加上
		Long fileSplitSize = Parser.parseLong(request.getParameter("fileSplitSize"),0l);
		if(fileMd5==null||fileSplitSize==null){
		   res = "{\"success\":false,\"msg\":\"参数错误\"}";
		   return res;
		}

		Param info = new Param();
		int rt = FileUpload.getFileUploadSize(cid,fileMd5,filename,fileSplitSize,info);

		switch(rt) {
		case Errno.OK:
			info.setBoolean("success", true);
			res = info.toJson();
			break;
		case FileUpload.ErrnoUpload.TMP_FILE_CLEAR_FAILED:
			res = "{\"success\":false,\"msg\":\"临时文件删除失败\"}";
			break;
		case Errno.ARGS_ERROR:
			res = "{\"success\":false,\"msg\":\"参数错误\"}";
			break;
	   }

		return res;
	}

	private String upload(HttpServletRequest request) throws Exception{
		String res = "";
		int cid = 1;
		int maxWidth = Parser.parseInt(request.getParameter("maxWidth"), -1);
		int maxHeight = Parser.parseInt(request.getParameter("maxHeight"), -1);
		int imgMode = Parser.parseInt(request.getParameter("imgMode"), -1);
		Param info = new Param();
		int rt = FileUpload.advanceUpload4Hd(getServletContext(), request, cid,1, maxWidth, maxHeight, imgMode,  info);
		switch (rt) {
		case Errno.OK:
			info.setBoolean("success", true);
			res = info.toJson();
			break;
		case FileUpload.ErrnoUpload.FILE_SIZE_LIMIT:
			int limit = info.getInt(FileUpload.Info.LIMIT);
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"上传失败：单个文件的大小超过" + limit + "M。\"}";
			break;
		case FileUpload.ErrnoUpload.IMG_SIZE_LIMIT:
			limit = info.getInt(FileUpload.Info.LIMIT);
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"上传失败：图片大小超过" + limit + "M，请压缩处理后再上传。\"}";
			break;
		case FileUpload.ErrnoUpload.FILE_CTRL_NOT_FOUND:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"上传失败：请尝试更换您的浏览器。\"}";
			break;
		case FileUpload.ErrnoUpload.FILE_TYPE_INVALID:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"上传失败：图片格式不合法。（只能设置jpg、jpeg、gif、bmp或png格式的图片）\"}";
			break;
		case FileUpload.ErrnoUpload.FILE_NAME_EXIST:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"上传失败：文件名已经存在。（请修改文件名后再试）\"}";
			break;
		case FileUpload.ErrnoUpload.FILE_TYPE_FORMAT_ERROR:
			Integer width = info.getInt(FileUpload.Info.WIDTH);
			Integer height = info.getInt(FileUpload.Info.HEIGHT);
			if (width != null && height != null) {
				res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"压缩图片错误，建议缩小图片为" + width + "*" + height + "以内。\"}";
			} else {
				res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"读取图片错误，建议您先使用图像处理软件转换图片格式。\"}";
			}
			break;
		case FileUpload.ErrnoUpload.FILE_MP3_FORMAT_ERROR:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"您上传的文件不是标准的mp3格式，请检查后再试。\"}";
			break;
		case FileUpload.ErrnoUpload.FILE_FLV_FORMAT_ERROR:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"您上传的文件不是标准的flv格式，请检查后再试。\"}";
			break;
		case Errno.SVR_READONLY:
			res = "{\"success\":false,\"msg\":\"系统维护中，暂时不能保存，请稍后重试。\"}";
			break;
		case FileUpload.ErrnoUpload.OVER_LIMIT:
			res = "{\"success\":false,\"rt\":" + Errno.SIZE_LIMIT + ",\"msg\":\"上传失败：超过当前网站版本的资源库容量。\"}";
			break;
		default:
			res = "{\"success\":false,\"rt\":" + rt + ",\"msg\":\"系统错误 请稍后再试。\"}";
		}

		return res;
	}

	private String uploadMteduVipCodeList(HttpServletRequest request) throws Exception{
		FormRequest freq = new FormRequest();
		int ver = Parser.parseInt(request.getParameter("ver"), 0);
		int rt = freq.attachServlet(getServletContext(), request); // 上传到临时目录
		if (rt != Errno.OK) {
			return new HdGameDef.ErrorInfo(rt, "文档格式错误，有可能数据项被修改").toString();
		}
		String ctrl = request.getParameter("ctrl");
		if (ctrl == null || ctrl.isEmpty()) {
			ctrl = "filedata";
		}
		FormFile formFile = freq.getFile(ctrl); // 	获取上传的临时文件

		String fileName = formFile.getFileName();

		// 解析文档并获取表头信息advanceUpload
		Param rowParam = new Param();
		rowParam.setString( HdMteduVipCodeDef.Info.CODE , "激活码");

		FaiList<Param> list = new FaiList<Param>();
		Param _rowParam = new Param();
		try{
			list = HdMSOfficeConverter.analyzeStaffExcel(fileName, formFile.get(), rowParam,  _rowParam, "激活码");
		}catch(Exception e){
			rt = Errno.ARGS_ERROR;
			return new HdGameDef.ErrorInfo(rt, "文件格式错误，请确认后再重新上传。").toString();
		}

		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);

		SearchArg searchArg = new SearchArg();
		searchArg.matcher = new ParamMatcher(HdMteduVipCodeDef.Info.VER, ParamMatcher.EQ, ver);
		FaiList<Param> exisitList = new FaiList<Param>();
		rt = hdOss.getMteduVipCodeList(searchArg, exisitList);
		if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
			return new HdGameDef.ErrorInfo(rt, "系统繁忙，请稍后再试").toString();
		}
		Set<String> existsCodeSet = new HashSet<String>();
		for(Param item : exisitList){
			String code = item.getString(HdMteduVipCodeDef.Info.CODE);
			existsCodeSet.add(code);
		}

		FaiList<String> existsCodeList = new FaiList<String>();
		FaiList<String> codeList = new FaiList<String>();
		for(Param item : list){
			String code = item.getString("code");
			if(existsCodeSet.contains(code)){
				existsCodeList.add(code);
				continue;
			}
			codeList.add(code);
		}

		rt = hdOss.batchAddMteduVipCode(ver, codeList);
		if(rt != Errno.OK){
			return new HdGameDef.ErrorInfo(rt, "系统繁忙，请稍后再试").toString();
		}
		return new HdGameDef.ErrorInfo(rt, "上传成功").add("existsCodeList", existsCodeList).toString();
	}
	
	
	private String uploadHdCaseList(HttpServletRequest request) throws Exception{
		FormRequest freq = new FormRequest();
		int sid = Session.getSid();
		int rt = 0;
		if(sid <= 0){
			rt = Errno.ARGS_ERROR;
			return HdGameDef.ErrorInfo.getErrInfo(rt, "未登陆");
		}
		rt = freq.attachServlet(getServletContext(), request); // 上传到临时目录
		if (rt != Errno.OK) {
			return HdGameDef.ErrorInfo.getErrInfo(rt, "文档格式错误，有可能数据项被修改");
		}
		String ctrl = "filedata";

		FormFile formFile = freq.getFile(ctrl); // 	获取上传的临时文件

		String fileName = formFile.getFileName();

		// 解析文档并获取表头信息advanceUpload
		Param rowParam = new Param();
		rowParam.setString(HdCaseDef.Info.ID, "序号");
		rowParam.setString(HdCaseDef.Info.O_AID, "aid - 原账号（必填）");
        rowParam.setString(HdCaseDef.Info.O_ACCT_NAME, "企业名称 - 原账号（必填）");
        rowParam.setString(HdCaseDef.Info.O_GAME_ID, "活动id - 原账号（必填）");
		rowParam.setString(HdCaseDef.Info.GAME_ID, "活动id - 案例账号（必填）");
		rowParam.setString(HdCaseDef.Info.GAME_NAME, "活动模板（必填）");
        rowParam.setString(HdCaseDef.Info.TRADE, "行业（必填）");
        rowParam.setString(HdCaseDef.Info.SCENE, "场景（必填）");
        rowParam.setString(HdCaseDef.Info.GAME_URL, "活动链接 - 案例账号（必填）");
		rowParam.setString(HdCaseDef.Info.LABEL, "活动分类（必填）");
		rowParam.setString(HdCaseDef.Info.ACTIVITY_BRIGHT, "活动亮点（必填）");
        rowParam.setString(HdCaseDef.Info.ACTIVITY_FUNC, "活动功能（必填）");
        rowParam.setString(HdCaseDef.Info.REMARK, "备注");
//		rowParam.setString(HdCaseDef.Info.ACTIVITY_EFFECT, "活动效果（必填）");
		
		FaiList<Param> list = new FaiList<Param>();
		HashSet<String> existSet = new HashSet<String>();
		Param _rowParam = new Param();
		HdOss hdOss = (HdOss) WebHdOss.getCorpKit(Kid.HD_OSS);
		
		int successNum = 0;
		int existNum = 0;
		int failNum = 0;
		FaiList<Param> existList = new FaiList<Param>();
		SearchArg searchArg = new SearchArg();
		hdOss.getHdCaseListFromDB(new Param(), searchArg, existList);
		if(existList == null){
			existList = new FaiList<Param>();
		}
		for(Param info : existList){
			int oAid = info.getInt(HdCaseDef.Info.O_AID);
			int oGameId = info.getInt(HdCaseDef.Info.O_GAME_ID);
			existSet.add(oAid + "-" + oGameId);
		}
		Calendar now = Calendar.getInstance();
		list = HdMSOfficeConverter.analyzeStaffExcel(fileName, formFile.get(), rowParam,  _rowParam, "序号");
		FaiList<Param> insertList = new FaiList<Param>();
		for(Param info : list){
			Log.logDbg("liqin info=%s",info);
			info.setCalendar(HdCaseDef.Info.CREATE_TIME, now);
			int id = Parser.parseInt(info.getString(HdCaseDef.Info.ID, ""), -1);
			if(id < 0){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号格式错误");
			}
			info.setInt(HdCaseDef.Info.ID, id);
			int oAid = Parser.parseInt(info.getString(HdCaseDef.Info.O_AID, ""), -1);
			if(oAid < 0){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"原aid格式错误");
			}
			info.setInt(HdCaseDef.Info.O_AID, oAid);
			int oGameId = Parser.parseInt(info.getString(HdCaseDef.Info.O_GAME_ID, "").replace("HD",""), -1);
			if(oGameId < 0){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"原活动id格式错误");
			}
			info.setInt(HdCaseDef.Info.O_GAME_ID, oGameId);
			int gameId = Parser.parseInt(info.getString(HdCaseDef.Info.GAME_ID, "").replace("HD",""), -1);
			if(gameId < 0){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"案例活动id格式错误");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.GAME_NAME))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动模板为空");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.GAME_URL))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动链接为空");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.TRADE))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"行业为空");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.SCENE))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"场景为空");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.LABEL))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动分类为空");
			}
			if(Str.isEmpty(info.getString(HdCaseDef.Info.ACTIVITY_BRIGHT))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动亮点为空");
			}
//			if(Str.isEmpty(info.getString(HdCaseDef.Info.ACTIVITY_EFFECT))){
//				rt = Errno.ARGS_ERROR;
//				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动效果为空");
//			}
            if(Str.isEmpty(info.getString(HdCaseDef.Info.ACTIVITY_FUNC))){
				rt = Errno.ARGS_ERROR;
				return HdGameDef.ErrorInfo.getErrInfo(rt, "序号" + id +"活动功能为空");
			}
			info.setInt(HdCaseDef.Info.GAME_ID, gameId);
		}
		for(Param info : list){
			info.setInt(HdCaseDef.Info.PROTOTYPE_ID, 0);
			info.setString(HdCaseDef.Info.COMPANY_LOGO_URL, "");
			info.setInt(HdCaseDef.Info.FLAG, 0);
			info.setCalendar(HdCaseDef.Info.UPDATE_TIME, Calendar.getInstance());
			int oAid = info.getInt(HdCaseDef.Info.O_AID);
			int oGameId = info.getInt(HdCaseDef.Info.O_GAME_ID);
			if(existSet.add(oAid + "-" + oGameId)){
				rt = hdOss.addHdCase(info);
				if(rt != Errno.OK){
					Log.logErr(rt, "addHdCase err info=%s",info);
					failNum ++;
				}else{
					successNum ++;
				}
			}else{
				existNum ++;
			}
		}
		Param recordInfo = new Param();
		recordInfo.setCalendar(HdCaseDef.Info4Record.CREATE_TIME, now);
		recordInfo.setInt(HdCaseDef.Info4Record.SID, sid);
		recordInfo.setString(HdCaseDef.Info4Record.FILENAME, fileName);
		recordInfo.setString(HdCaseDef.Info4Record.FAILREASON, existNum > 0 ? "与现有案例重复个数" + existNum : failNum > 0 ? "系统错误个数" + failNum : "");
		recordInfo.setInt(HdCaseDef.Info4Record.SUCCESSNUM, successNum);
		recordInfo.setInt(HdCaseDef.Info4Record.FAILNUM, existNum + failNum);
		recordInfo.setInt(HdCaseDef.Info4Record.TOTAL, list.size());
		rt = hdOss.addHdCase4Record(recordInfo);
		if(rt != Errno.OK){
			Log.logErr(rt, "addHdCase4Record err info=%s", recordInfo);
			return HdGameDef.ErrorInfo.getErrInfo(rt, "添加上传记录失败");
		}
		
		return HdGameDef.ErrorInfo.getErrInfo(rt, "上传成功");
	}

%>
<%
	String output = "";
	try
	{
	    int cid = Web.getFaiCid();
	  //  Auth.checkAuthSite(cid, StaffAuthSiteDef.AuthSite.AUTH_ALL, false, true);	// 允许随便一个网站管理员操作
	    Auth.checkFaiscoAuth("authHdManage", false);
		String cmd = request.getParameter("cmd");
		if (cmd == null){
			return;
		}
		if(cmd.equals("uploadAwardCodeExcel")){
			output = uploadAwardCodeExcel(request);
		}else if(cmd.equals("addWXDeviceList")){
			output = addWXDeviceList(request);
		}else if(cmd.equals("addWXPage")){
			output = addWXPage(request);
		}else if(cmd.equals("upload")){
			output = upload(request);
		}else if(cmd.equals("getUploadSize")){
			output = getUploadSize(request);
		}else if(cmd.equals("uploadMteduVipCodeList")){
			output = uploadMteduVipCodeList(request);
		}else if(cmd.equals("uploadHdCaseList")){
			output = uploadHdCaseList(request);
		}
	}
	catch (Exception exp)
	{
		output = WebOss.checkAjaxException(exp);
	}
	out.print(output);
%>