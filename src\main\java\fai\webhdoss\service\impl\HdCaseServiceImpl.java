package fai.webhdoss.service.impl;

import com.alibaba.fastjson.JSONArray;
import fai.app.*;
import fai.cli.CorpProfCli;
import fai.cli.HdGameCli;
import fai.cli.HdOssAffairCli;
import fai.cli.HdStatCli;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.hdUtil.exception.constant.ProfessionExceptionEnum;
import fai.web.App;
import fai.web.Core;
import fai.web.WebException;
import fai.webhdoss.model.vo.HdCasePreViewVO;
import fai.webhdoss.service.HdCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 * @Description 案例业务逻辑封装实现
 * @date 2021/3/14 11:38
 */
@Service
public class HdCaseServiceImpl implements HdCaseService {

    @Autowired
    private HdOssAffairCli ossAffairCli;

    @Autowired
    private HdGameCli HdGameCli;

    @Autowired
    private CorpProfCli createCli;

    @Override
    public int settingPreview(HdCasePreViewVO casePreViewVO) {
        Param caseParam = getOneCaseById(casePreViewVO.getOAid(), casePreViewVO.getCaseId());
        int flag = caseParam.getInt(HdCaseDef.Info.FLAG, 0);

        Param previewParam = new Param();
        int prototypeId = BssHdPuBUseTypeDef.DealUtil.getStyleByName(casePreViewVO.getPrototypeName());
        flag = Misc.setFlag(flag, HdCaseDef.Flag.IS_PUT_ON_SHELF, casePreViewVO.getIsPutOn());
        flag = Misc.setFlag(flag, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW, true);
        flag = Misc.setFlag(flag, HdCaseDef.Flag.IS_ADD_PREVIEW_WINDOW_INFO, casePreViewVO.getIsPut());
        previewParam.setString(HdCaseDef.Info.COMPANY_LOGO_URL, casePreViewVO.getLogoUrl());
        previewParam.setCalendar(HdCaseDef.Info.UPDATE_TIME, Calendar.getInstance());
        previewParam.setInt(HdCaseDef.Info.PROTOTYPE_ID, prototypeId);
        previewParam.setInt(HdCaseDef.Info.FLAG, flag);

        ParamUpdater updater = new ParamUpdater(previewParam);
        ParamMatcher matcher = new ParamMatcher(HdCaseDef.Info.ID, ParamMatcher.EQ, casePreViewVO.getCaseId());
        matcher.and(HdCaseDef.Info.O_AID, ParamMatcher.EQ, casePreViewVO.getOAid());
        matcher.and(HdCaseDef.Info.O_GAME_ID, ParamMatcher.EQ, casePreViewVO.getOGameId());
        return ossAffairCli.setCase(Core.getAid(), updater, matcher);
    }

    @Override
    public JsonResult putPreview(Integer caseId, Integer oAid, Integer oGameId, boolean isPut) {
        Param caseParam = getOneCaseById(oAid, caseId);
        int flag = caseParam.getInt(HdCaseDef.Info.FLAG, 0);

        boolean isOldPut = Misc.checkBit(flag, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW);

        if (isOldPut == isPut){
            return JsonResult.ok("已经是" + (isPut ? "投放" : "不投放") +"状态 无需再更改", Errno.OK);
        }
        Param previewParam = new Param().setInt(HdCaseDef.Info.FLAG, Misc.setFlag(flag, HdCaseDef.Flag.IS_PUT_PREVIEW_WINDOW, isPut));
        int rt = updateCase(caseId, oAid, oGameId, previewParam);
        HdAssert.System.isErrnoOk(rt, (isPut ? "投放" : "不投放") + "失败").log("putPreview setCase err; caseId=%d;", caseId);
        return JsonResult.ok(rt);
    }

    @Override
    public JsonResult updateStatusPreview(Integer caseId, Integer oAid, Integer oGameId, boolean isPutOnShelf) {
        Param caseParam = getOneCaseById(oAid, caseId);
        int flag = caseParam.getInt(HdCaseDef.Info.FLAG, 0);

        boolean isOldPutOn = Misc.checkBit(flag, HdCaseDef.Flag.IS_PUT_ON_SHELF);
        if (isOldPutOn == isPutOnShelf){
            return JsonResult.ok("已经是" + (isPutOnShelf ? "上架" : "下架") +"状态 无需再更改", Errno.OK);
        }
        Param previewParam = new Param().setInt(HdCaseDef.Info.FLAG, Misc.setFlag(flag, HdCaseDef.Flag.IS_PUT_ON_SHELF, isPutOnShelf));
        int rt = updateCase(caseId, oAid, oGameId, previewParam);
        HdAssert.System.isErrnoOk(rt, (isPutOnShelf ? "上架" : "下架") + "失败").log("updateStatusPreview setCase err; caseId=%d;", caseId);
        return JsonResult.ok(rt);
    }

    private int updateCase(Integer caseId, Integer oAid, Integer oGameId, Param previewParam) {
        ParamUpdater updater = new ParamUpdater(previewParam);
        ParamMatcher matcher = new ParamMatcher(HdCaseDef.Info.ID, ParamMatcher.EQ, caseId);
        matcher.and(HdCaseDef.Info.O_AID, ParamMatcher.EQ, oAid);
        matcher.and(HdCaseDef.Info.O_GAME_ID, ParamMatcher.EQ, oGameId);
        return ossAffairCli.setCase(Core.getAid(), updater, matcher);
    }

    private Param getOneCaseById(Integer oAid, Integer caseId){
        HdAssert.Biz.isExist(caseId, ProfessionExceptionEnum.ARGS_ERR);
        if (caseId <= 0) {
            return new Param();
        }
        FaiList<Param> list = new FaiList<>();
        SearchArg searchArg = new SearchArg();
        ParamMatcher paramMatcher = new ParamMatcher(HdCaseDef.Info.ID, ParamMatcher.EQ, caseId);
        paramMatcher.and(HdCaseDef.Info.O_AID, ParamMatcher.EQ, oAid);
        searchArg.matcher = paramMatcher;
        searchArg.cmpor = new ParamComparator(HdCaseDef.Info.UPDATE_TIME, true);
        Log.logStd("getOneCaseById searchArg=%s", searchArg.toString());
        int rt = ossAffairCli.getHdCaseListFromDB(new Param(), searchArg, list);
        HdAssert.System.isErrnoOk(rt, "settingPreview getHdCaseListFromDB err").log("settingPreview getHdCaseListFromDB err caseId=%d;", caseId);
        return list.get(0);
    }

    @Override
    public JsonResult getHdCaseList(int aid)  {
        try{
            HdGameCli hdGameCli = new HdGameCli(Core.getFlow());
            if (!hdGameCli.init()){
                throw new Exception("HdGameCli err");
            }
            return JsonResult.success(hdGameCli.getHdCaseList(aid));
        }catch (Exception e){
            return JsonResult.error(e.getMessage());
        }
    }

    @Override
    public JsonResult getCaseListFromHd(String secretKey, FaiList<Param> caseList)throws Exception {
        if (!getSecretKey().equals(secretKey)){
            return JsonResult.error("secretKey err");
        }

        for (Param map : caseList) {
            Integer aid = map.getInt("aid");
            Integer siteId = map.getInt("siteId");

            Param game = new Param();
            HdGameCli.getGameInfo(aid, siteId, game);
            if (game.isEmpty()){
                continue;
            }

            map.setString(HdGameDef.Info.NAME, game.getString(HdGameDef.Info.NAME));
            map.setInt("version", game.getInt(HdGameDef.Info.VERSION));
            Ref<Integer> ref = new Ref();
            ossAffairCli.getHdTradeByAid(aid, ref);
            map.setInt("trade", ref.value);

            Param settingInfo = Param.parseParamNullIsEmpty(game.getString(HdGameDef.Info.SETTING, ""));
            String logo = settingInfo.getString(HdGameDef.Setting.HOST_LOGO, "");
            if (!"".equals(logo)){
                Param tmpInfo = Param.parseParamNullIsEmpty(logo);
                logo = tmpInfo.getString("path", "");
            }
            map.setString("logo", logo);

            map.setInt("templateId", settingInfo.getInt("modId", 0));

            Param additional = new Param();

            //添加additinal其他数据(游戏类型,玩法,节日,场景,活动开始时间,活动结束时间)
            additionalData(additional, game);

            //additional (浏览人数,参与人数,分享人数)
            GameRecord(aid, siteId, game.getInt(HdGameDef.Info.STYLE), additional);

            //活动是否已经发布
            map.setInt("labelFlag_1",Misc.checkBit(game.getInt(HdGameDef.Info.FLAG), HdGameDef.Flag.PUBLISH)?1:0);

            map.setParam("additional", additional);

        }
        return JsonResult.success(caseList);
    }

    /**
     * 获取(浏览人数,参与人数,分享人数)
     * @param aid
     * @param gameId
     * @param style
     * @return
     * @throws Exception
     */
    private void GameRecord(int aid, int gameId, int style, Param additional) throws Exception {
        Param selectOption = new Param();
        // 默认时间拿现在的前后十年
        // 默认统计类别
        String commonStatType = HdStatDef.StatLogs.StatType.NUM_PV + " ".concat(HdStatDef.StatLogs.StatType.NUM_PTCP + " ")
                .concat(HdStatDef.StatLogs.StatType.NUM_SHARE + " ");
        selectOption.setInt(HdStatDef.SelectOption.START_TIME, (int) (System.currentTimeMillis() / 1000) - (60 * 60 * 24 * 3650));
        selectOption.setInt(HdStatDef.SelectOption.END_TIME, (int) (System.currentTimeMillis() / 1000) + (60 * 60 * 24 * 3650));
        selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, commonStatType);
        selectOption.setInt(HdStatDef.SelectOption.TIME_TYPE, HdStatDef.SelectOption.TimeType.DAY);
        selectOption.setInt(HdStatDef.SelectOption.CANAL, -1);
        selectOption.setInt(HdStatDef.SelectOption.AID, aid);
        selectOption.setInt(HdStatDef.SelectOption.GAME_ID, gameId);
        // 拿传播数据里面的参与人数，分享人数，浏览人数, 不从hdgame拿是为了保持跟传播数据一致，不然必来工单。
        FaiList<Param> selectResultList = new FaiList<Param>();
        int rt =selectData(selectOption, selectResultList,aid);
        if (rt != Errno.OK){
            Log.logErr("get hdStat info err aid=%s,gameid=%s",aid,gameId);
        }
        Param param1 = countGameRecord(style,selectResultList,selectOption);
        additional.append(param1);
    }

    private void additionalData(Param additional, Param game){

        Integer style = game.getInt(HdGameDef.Info.STYLE);
        //游戏类型
        additional.setInt("int_41", style);
        Param setting = Param.parseParam(game.getString(HdGameDef.Info.SETTING));
        Integer modId = setting.getInt(HdGameDef.Setting.MODID);
        try{
            if (modId == 0){
                FaiList<Param> modelList = new FaiList<Param>();
                SearchArg searchArg1 = new SearchArg();
                searchArg1.matcher = new ParamMatcher(HdModelDef.Info.RSTYLE, ParamMatcher.EQ, style);
                ossAffairCli.getHdModelList(searchArg1, modelList);
                if (modelList.size() == 0){
                    //玩法
                    additional.setInt("int_42", HdTradeDef.NewGroup.NONE);
                    //节日
                    additional.setInt("int_43", HdTradeDef.NewGroup.NONE);
                    //场景
                    additional.setInt("int_44", HdTradeDef.NewGroup.NONE);
                }else{
                    Param model = modelList.get(0);
                    //玩法
                    additional.setInt("int_42", model.getInt(HdModelDef.Info.KEY3));
                    //节日
                    additional.setInt("int_43", model.getInt(HdModelDef.Info.KEY1));
                    //场景
                    additional.setInt("int_44", getSenceId(model.getInt(HdModelDef.Info.CATEGORY2)));
                }
            }else{
                Param model = new Param();
                ossAffairCli.getHdModelInfo(modId, model);
                //玩法
                additional.setInt("int_42", model.getInt(HdModelDef.Info.KEY3));
                //节日
                additional.setInt("int_43", model.getInt(HdModelDef.Info.KEY1));
                //场景
                additional.setInt("int_44", getSenceId(model.getInt(HdModelDef.Info.CATEGORY2)));
            }
        }catch (Exception e){
            Log.logErr("dkErr:"+e.getMessage()+",style:"+style);
        }
        //活动开始时间
        additional.setInt("int_45", getIntTimeStamp(game.getCalendar(HdGameDef.Info.START_TIME)));
        //活动结束时间
        additional.setInt("int_46",getIntTimeStamp(game.getCalendar(HdGameDef.Info.END_TIME)));
        //判断该活动是否是复制而来的
    }
    private int getIntTimeStamp(Calendar time){
        String timeStr = String.valueOf(time.getTimeInMillis());
        return Integer.parseInt(timeStr.substring(0, 10));
    }
    //获取场景id
    private int getSenceId(int category2){
        if (Misc.checkBit(category2, HdModelDef.Category2.BRAND_SPREAD)){
            return HdModelCategoryDisplayDef.CategoryDisplay.BRAND_SPREAD;
        }
        if (Misc.checkBit(category2, HdModelDef.Category2.EARN_MORE_FANS)){
            return HdModelCategoryDisplayDef.CategoryDisplay.EARN_MORE_FANS;
        }
        if (Misc.checkBit(category2, HdModelDef.Category2.PAYMENT)){
            return HdModelCategoryDisplayDef.CategoryDisplay.PAYMENT;
        }
        if (Misc.checkBit(category2, HdModelDef.Category2.KEEP_ACTIVE_FANS)){
            return HdModelCategoryDisplayDef.CategoryDisplay.KEEP_ACTIVE_FANS;
        }
        return 0;
    }

    public static String getSecretKey(){
        String today = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String secretKey = MD5Util.MD5Encode("mgCase-" + today, "utf-8");
        Log.logStd("secretKey:"+secretKey);
        return secretKey;
    }

    private Param countGameRecord(int style,FaiList<Param> selectResultList,Param selectOption){
        // 关注红包只有关注人数和领取人数，产品只要关注人数
        if (style == HdGameDef.Style.GZHB) {
            selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, HdStatDef.StatLogs.StatType.NUM_SUBSCRIBE + " ");
        }

        Param additional = new Param();
        for (Param result : selectResultList) {
            int statType = result.getInt(HdStatDef.SelectResult.STAT_TYPE, -1);
            String sdl = result.getString(HdStatDef.SelectResult.SELECT_DATA_LIST, "");
            int num = 0;
            try {
                @SuppressWarnings("rawtypes")
                List<FaiList> faiLists = JSONArray.parseArray(sdl, FaiList.class);
                Log.logDbg("SELECT_DATA_LIST : %s", faiLists);
                for (@SuppressWarnings("rawtypes") FaiList faiList : faiLists) {
                    num += (Integer) faiList.get(1);
                }
            } catch (ClassCastException cce) {
                num = 0;
            }
            switch (statType) {
                case HdStatDef.StatLogs.StatType.NUM_PV:
                    //info.setInt(HdGameDef.Info.VIEW, num);
                    additional.setInt("int_1",num);
                    break;
                case HdStatDef.StatLogs.StatType.NUM_SUBSCRIBE:
                    // 重置statType
                    selectOption.setString(HdStatDef.SelectOption.STAT_TYPE_LIST, "1 2 4");
                case HdStatDef.StatLogs.StatType.NUM_PTCP:
                    //info.setInt(HdGameDef.Info.PLAYER, num);
                    additional.setInt("int_2",num);
                    break;
                case HdStatDef.StatLogs.StatType.NUM_SHARE:
                    //info.setInt(HdGameDef.Info.SHARE, num);
                    additional.setInt("int_3",num);
                    break;
            }
        }

        return additional;
    }

    public int selectData(Param selectOption, FaiList<Param> selectResultList, int aid) throws Exception {
        int rt = Errno.OK;
        if (selectOption == null || selectOption.isEmpty() || selectResultList == null) {
            rt = Errno.ARGS_ERROR;
            App.logErr(rt, "select data error; args error; selectOption = %s, statDataList = %s", selectOption, selectResultList);
            return rt;
        }

        HdStatCli cli = new HdStatCli(Core.getFlow());
        if (!cli.init()) {
            throw new WebException("SiteStatCli init error");
        }
        rt = cli.getStatData(aid, selectResultList, selectOption);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            App.logErr(rt, "selectData err; selectOption=%s", selectOption.toJson());
            return rt;
        }

        if (selectResultList != null && !selectResultList.isEmpty()) {
            int timeType = selectOption.getInt(HdStatDef.SelectOption.TIME_TYPE);
            FaiList<Param> selectResultListClone = selectResultList.clone();
            selectResultList.clear();
            analysisResultList(selectResultListClone, selectResultList, timeType);
        }

        return rt;
    }

    private static void analysisResultList (FaiList<Param> selectResultList, FaiList<Param> newSelectResultList, int timeType) {
        for (int i = 0; i < selectResultList.size(); i++) {
            Param selectResult = selectResultList.get(i);
            Param newSelectResult = new Param();

            int statType = selectResult.getInt(HdStatDef.SelectResult.STAT_TYPE, 0);
            String statDataListStr = selectResult.getString(HdStatDef.SelectResult.SELECT_DATA_LIST);
            if (selectResultList == null || selectResultList.isEmpty() || statDataListStr == null || statDataListStr.isEmpty() || statType == 0) {
                continue;
            }
            FaiList<FaiList<Integer>> newStatDataList = new FaiList<FaiList<Integer>>();
            FaiList<Param> statDataList = FaiList.parseParamList(statDataListStr);
            Map<Integer, Integer> map = new HashMap<Integer, Integer>();
            for (int j = 0; j < statDataList.size(); j++) {
                Param statData = statDataList.get(j);
                int count = statData.getInt("count(*)");
                int time = statData.getInt(HdStatDef.StatLogs.TIME);

                Calendar cal = Calendar.getInstance();
                cal.setTimeInMillis((long)(time)*1000);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                switch (timeType) {
                    case HdStatDef.SelectOption.TimeType.HOUR:	// H: 小时
                        break;
                    case HdStatDef.SelectOption.TimeType.DAY:	// D: 天
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        break;
                    case HdStatDef.SelectOption.TimeType.MONTH:	// M: 月
                        //cal.set(Calendar.DAY_OF_MONTH, 0);
                        cal.set(Calendar.HOUR_OF_DAY, 0);
                        break;
                    default:
                        App.logErr("analysisResultList timeType not found; timeType=" + timeType);
                        break;
                }
                time = (int)(cal.getTimeInMillis() / 1000);

                if(map.containsKey(time)){
                    int sum = map.get(time);
                    map.put(time, sum + count);
                }else{
                    map.put(time, count);
                }
            }
            for(Integer time : map.keySet()){
                Integer count = map.get(time);
                FaiList<Integer> newStatData = new FaiList<Integer>();
                newStatData.add(time);
                newStatData.add(count);
                newStatDataList.add(newStatData);
            }

            newSelectResult.setInt(HdStatDef.SelectResult.STAT_TYPE, statType);
            newSelectResult.setString(HdStatDef.SelectResult.SELECT_DATA_LIST, newStatDataList.toJson());
            newSelectResultList.add(newSelectResult);
        }
    }

}
