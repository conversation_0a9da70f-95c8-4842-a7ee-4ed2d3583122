<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>

<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.*" %>
<%@ page import="fai.cli.BssStatCli" %>
<%@ page import="fai.cli.*" %>
<%@ page import="java.sql.PreparedStatement" %>

<%!
    /**
     * 申领回A库
     */
    public String backToAkuApprove(HttpServletRequest request) throws Exception {
        //校验权限
        if (!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        // presale 的接口
        SysPreSaleUtil sysPreSaleUtil = (SysPreSaleUtil) Core.getSysKit(Kid.SYS_PRESALE_UTIL);

        //request数据
        int sid = Session.getSid();
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int oldAid = Parser.parseInt(request.getParameter("oldAid"), 0);//旧aid
        int approveStyle = Parser.parseInt(request.getParameter("approveStyle"), -1);// 报备类型
        String mark = Parser.parseString(request.getParameter("mark"), "");//备注
        String fileId = Parser.parseString(request.getParameter("fileId"), "");// 文件id

        //校验参数
        if (aid == 0 || approveStyle != HdSaleApproveDef.ApproveStyle.BACK_TO_A_KU || approveStyle == -1) {
            return "{\"success\":false, \"msg\":\"参数错误\"}";
        }

        //封装数据
        Param data = new Param();
        data.setInt(HdSaleApproveDef.Info.AID, aid);
        data.setInt(HdSaleApproveDef.Info.OLD_AID, oldAid);
        data.setInt(HdSaleApproveDef.Info.SID, sid);
        data.setInt(HdSaleApproveDef.Info.APPROVE_STYLE, approveStyle);
        data.setInt(HdSaleApproveDef.Info.STATUS, HdSaleApproveDef.ApproveStatus.INIT);
        data.setString(HdSaleApproveDef.Info.MARK, mark);
        data.setString(HdSaleApproveDef.Info.FILEID, fileId);
        data.setCalendar(HdSaleApproveDef.Info.CREATE_TIME, Calendar.getInstance());

        Dao dao = WebOss.getOssBsDao();
        try {
            int rt = dao.insert("hdSaleApprove", data);
            if (rt != Errno.OK) {
                return "{\"success\":false, \"msg\":\"申请失败。\"}";
            }
        } finally {
            dao.close();
        }
        return "{\"success\":true, \"msg\":\"申请成功\"}";
    }


%>

<%
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        Log.logStd("unique-test cmd = %s",cmd);
        if(cmd == null){
            output = "no cmd find";
        }else if(cmd.equals("backToAkuApprove")){
            output = backToAkuApprove(request);
        }

    }catch (Exception e){
        PreSaleHdDef.printErr(e);
        output = WebOss.checkAjaxException(e);
    }
    out.print(output);


%>