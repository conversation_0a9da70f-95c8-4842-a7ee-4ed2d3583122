/* Turkish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> <PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['tr'] = {
		closeText: 'kapat',
		prevText: '&#x3c;geri',
		nextText: 'ileri&#x3e',
		currentText: 'bugün',
		monthNames: ['<PERSON>ca<PERSON>','<PERSON><PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','Kasım','Aral<PERSON><PERSON>'],
		monthNamesShort: ['Oca','<PERSON>ub','Mar','Ni<PERSON>','May','<PERSON><PERSON>',
		'<PERSON><PERSON>','A<PERSON><PERSON>','<PERSON>yl','E<PERSON>','Ka<PERSON>','Ara'],
		dayNames: ['<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		dayNamesShort: ['Pz','Pt','Sa','<PERSON><PERSON>','<PERSON><PERSON>','Cu','Ct'],
		dayNamesMin: ['Pz','Pt','Sa','Ça','Pe','Cu','Ct'],
		weekHeader: 'Hf',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['tr']);
});