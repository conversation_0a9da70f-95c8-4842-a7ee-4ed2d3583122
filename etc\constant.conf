{
    hdSaleList: {
		taList:{
			value:-1,
			name:"注册来源",
			labelList:[
                {name:"全部",label:-1},
				{name:"建站来源",label:1},
				{name:"互动推广",label:3},
				{name:"微传单推广",label:4},
				{name:"线下广告",label:6},
				{name:"移动端推广",label:7},
				{name:"自来",label:8},
				{name:"未知",label:9},
				{name:"SEO",label:10},
				{name:"活动引流",label:11},
				{name:"移动端放量",label:12}
			]
		},
        intent:{
            value:-1,
            name:"标记",
            labelList:[
                {name:"全部",label:-1},
				{name:"空",label:0},
                {name:"无意向",label:5},
                {name:"未接通",label:6},
                {name:"待跟进",label:7},
                {name:"有意向",label:1},           
                {name:"仅建站",label:8}
            ]
        },
        goal:{
            value:-1,
            name:"注册用途",
            labelList:[
                {name:"全部",label:-1},
                {name:"未知",label:0},
                {name:"企业/组织官网",label:1},
                {name:"推广产品",label:2},
                {name:"在线商城",label:3},
                {name:"个人网站",label:5},
                {name:"其他",label:7},
                {name:"发布信息",label:9},
                {name:"互动-微信吸粉",label:100},
                {name:"互动-门店引流",label:101},
                {name:"互动-电商引流",label:102},
                {name:"互动-现场活动",label:103},
                {name:"互动-品牌传播",label:104},
                {name:"互动-其他",label:105},
                {name:"互动-未知",label:106}
            ]
        },
        siteVersion:{
            value:-1,
            name:"网站版本",
            labelList:[
                {name:"全部",label:-1},
                {name:"网站免费版",label:0},
                {name:"历史专业版",label:4},
                {name:"网站专业版",label:14},
                {name:"网站标签版",label:24},
                {name:"商城基础版",label:16},
                {name:"商城旗舰版",label:30}
            ]
        },
        regBiz:{
            value:-1,
            name:"注册产品",
            labelList:[
                {name:"全部",label:-1},
                {name:"建站",label:0},
                {name:"互动",label:1},
                {name:"微传单",label:2},
                {name:"微信公众号",label:3}
            ]
        },
        hdVersion:{
            value:-1,
            name:"互动版本",
            labelList:[
                {name:"全部",label:-1},
                {name:"互动免费版",label:0},
                {name:"互动白银版",label:2},
                {name:"互动铂金版",label:1},
                {name:"互动门店版",label:3},
                {name:"互动钻石版",label:4}
            ]
        },
        cdVersion:{
            value:-1,
            name:"微传单版本",
            labelList:[
                {name:"全部",label:-1},
                {name:"微传单免费版",label:0},
                {name:"微传单尊享版",label:125}
            ]
        },
        sortBy:{
            value:-1,
            name:"排列顺序",
            labelList:[
                {name:"最后更新时间倒序",label:-1},
                {name:"最后更新时间顺序",label:0},
                {name:"最后登录时间倒序",label:1},
                {name:"最后登录时间顺序",label:2},
                {name:"最后注册时间倒序",label:3},
                {name:"最后注册时间顺序",label:4},
                {name:"一周登录次数顺序",label:5},
                {name:"一周登录次数倒序",label:6},
                {name:"一周操作次数倒序",label:7},
                {name:"一周操作次数顺序",label:8},
				{name:"最后咨询客服时间顺序",label:9},
				{name:"最后咨询客服时间倒序",label:10}
            ]
        },
		wxVersion:{
            value:-1,
            name:"助手版本",
            labelList:[
                {name:"全部",label:-1},
                {name:"基础版",label:261},
                {name:"专业版",label:281},
                {name:"旗舰版",label:598}
            ]
        },
        saleGroup:{
            value:"all",
            name:"销售分组",
            labelList:[
                {name:"全部",label:"all"},
                {name:"真实销售组",label:"authHDSaleEnergy"},
                {name:"虚拟销售组",label:"authHDSaleAI"},
                {name:"销售1组",label:"authHDSale1"},
                {name:"销售2组",label:"authHDSale2"},
                {name:"销售3组",label:"authHDSale3"},
                {name:"销售4组",label:"authHDSale4"},
                {name:"方案销售组",label:"authHDSale5"}
            ]
        },
        receiveSale:{
            value:"all",
            name:"领取人",
            labelList:[
                {name:"全部",label:"all"}
            ]
        },
        coupon:{
            value:-1,
            name:"有现金券且未过期",
            labelList:[
                {name:"全部",label:-1},
                {name:"后台字段发送",label:0},
                {name:"人工添加",label:1}
            ]
        },
        intentLevel:{
            value:-1,
            name:"意向度",
            labelList:[
                {name:"全部",label:-1},
                {name:"A",label:1},
                {name:"B",label:2},
                {name:"C",label:3}
            ]
        },
		
		element2default: {
			aid: "",
			mark: "",
			joinNumStart: "",
			regTimeCheck: false,
			lastUpdateTimeCheck: false,
			receiveTimeCheck: false,
			nextTalkTimeCheck: false,
			sendMessageTimeCheck: false,
			noSendMessageCheck: false,
			firstCreateGameTimeCheck: false,
			needloginTime: false,
			ta: -1,
			siteVersion: -1,
			hdVersion: -1,
			cdVersion: -1,
			wxVersion: -1,
			sortBy: -1,
			oneWeekSortBy: -1,
			saleGroup: "all",
			receiveSale: "all",
			tag: -1,
			coupon: -1,
			intentLevel: -1,
			intent: -1,
			business: -1,
			hasOpenYk: -1,
			hasCorpName: -1,
			isWeekendResource: -1,
			allotType: -1,
			args: false,
			focusAndStopSend: false
		},
		
		element2value: {
			aid: "aid",
			mark: "备注",
			joinNumStart: "参与人数",
			regTimeCheck: "注册时间",
			lastUpdateTimeCheck: "更新时间",
			receiveTimeCheck: "领取时间",
			nextTalkTimeCheck: "下次联系时间",
			sendMessageTimeCheck: "发送短信时间",
			noSendMessageCheck: "未发送短信时间",
			firstCreateGameTimeCheck: "首次创建游戏时间",
			needloginTime: "登录时间",
			ta: "注册来源",
			siteVersion: "网站版本",
			hdVersion: "互动版本",
			cdVersion: "传单版本",
			wxVersion: "助手版本",
			sortBy: "排列顺序",
			oneWeekSortBy: "第二个排列顺序",
			saleGroup: "销售分组",
			receiveSale: "领取人",
			tag: "标签",
			coupon: "有现金券且未过期",
			intentLevel: "意向度",
			intent: "标记",
			business: "业务",
			hasOpenYk: "是否开通悦客",
			hasCorpName: "有企业名字",
			isWeekendResource: "是否周末分配的资源",
			allotType: "分配方式",
			args: "未联系时间",
			focusAndStopSend: "重点关注",
			search: "查询",
			batchMessange: "批量短信",
			batchRelease: "批量释放",
			batchToA: "批量转入A库",
			batchToB: "批量转入B库",
			batchBan15Day: "批量禁用优惠(15天)",
			batchBan: "批量禁用优惠(手动)",
			batchNoBan: "批量解除禁用",
			excel: "导出",
			allocationLogic: "资源分配逻辑",
			displayLogic: "展示逻辑",
			prohibitSendList: "禁止发送短信列表"
		},
		
		element2Type: {
			aid: 2,
			mark: 2,
			joinNumStart: 2,
			regTimeCheck: 3,
			lastUpdateTimeCheck: 3,
			receiveTimeCheck: 3,
			nextTalkTimeCheck: 3,
			sendMessageTimeCheck: 3,
			noSendMessageCheck: 3,
			firstCreateGameTimeCheck: 3,
			needloginTime: 3,
			ta: 4,
			siteVersion: 4,
			hdVersion: 4,
			cdVersion: 4,
			wxVersion: 4,
			sortBy: 4,
			oneWeekSortBy: 4,
			saleGroup: 4,
			receiveSale: 4,
			tag: 4,
			coupon: 4,
			intentLevel: 4,
			intent: 4,
			business: 4,
			hasOpenYk: 4,
			hasCorpName: 4,
			isWeekendResource: 4,
			allotType: 4,
			args: 4,
			focusAndStopSend: 1,
			search: 1,
			batchMessange: 1,
			batchRelease: 1,
			batchToA: 1,
			batchToB: 1,
			batchBan15Day: 1,
			batchBan: 1,
			batchNoBan: 1
		}
    },
    hdPayRecord:{
        productType:{
            value: 0,
            name:"产品类型",
            labelList:[
                {name:"全部",label:0},
                {name:"互动",label:3},
                {name:"微传单",label:4},
                {name:"门店版",label:9},
                {name:"互动+微传单",label:10},
                {name:"钻石版",label:11},
                {name:"铂金版",label:12},
                {name:"白银版",label:13},
                {name:"去广告",label:14},
                {name:"公众号助手",label:15},
                {name:"助手基础版",label:16},
                {name:"助手专业版",label:17},
                {name:"助手旗舰版",label:21},
                {name:"传单尊享版",label:18},
                {name:"微传单乐享版",label:19},
				{name:"悦客",label:20}
            ]
        },
		
		element2default: {
			regDateFlag: false,
			creDateFlag: false,
			payDateFlag: false,
			refDateFlag: false,
			receiveDateFlag: false,
			oldReceiveDateFlag: false,
			payType: -1,
			sort: 2,
			status: 0,
			pay: 0,
			minPrice: "",
			productType: 0,
			amount: 0,
			goal: -1,
			ta: -1,
			staff_group: "all",
			staff_sid: "0",
			tag: -1,
			time_tag: -1,
			aid: "",
			orderId: "",
			newA_BLib: -1,
			Business: -1,
			abType: -1,
			isWeekendResource: -1,
			allotType: -1,
			approveStyle: -1
			
		},
		
		element2value: {
			regDateFlag: "注册时间",
			creDateFlag: "创建时间",
			payDateFlag: "支付时间",
			refDateFlag: "退款时间",
			receiveDateFlag: "领取时间",
			oldReceiveDateFlag: "旧账号领取时间",
			payType: "付款类型",
			sort: "排序",
			status: "订单状态",
			pay: "支付情况",
			minPrice: "金额",
			productType: "产品类型",
			amount: "购买数量",
			goal: "注册用途",
			ta: "注册来源",
			staff_group: "销售分组",
			staff_sid: "领取人",
			tag: "标签",
			time_tag: "时间段",
			aid: "aid",
			orderId: "订单号",
			newA_BLib: "新A/B库",
			Business: "业务",
			abType: "ab类资源",
			isWeekendResource: "是否周末分配资源",
			allotType: "分配方式",
			approveStyle: "报备类型",
			excel: "导出",
			search: "查询",
			batchMessange: "批量短信"
			
		},
		
		element2Type: {
			regDateFlag: 3,
			creDateFlag: 3,
			payDateFlag: 3,
			refDateFlag: 3,
			receiveDateFlag: 3,
			oldReceiveDateFlag: 3,
			payType: 4,
			sort: 4,
			status: 4,
			pay: 4,
			minPrice: 2,
			productType: 4,
			amount: 4,
			goal: 4,
			ta: 4,
			staff_group: 4,
			staff_sid: 4,
			tag: 4,
			time_tag: 4,
			aid: 2,
			orderId: 2,
			newA_BLib: 4,
			Business: 4,
			abType: 4,
			isWeekendResource: 4,
			allotType: 4,
			approveStyle: 4,
			
			search: 1,
			batchMessange: 1
		}


    },
    comm:{
      	taNameList:{
			value:'all',
			name:"注册来源",
			labelList:[
                {name:"全部",label:'all'},
				{name:"建站来源",label:'建站来源'},
				{name:"互动推广",label:'互动推广'},
				{name:"微传单推广",label:'微传单推广'},
				{name:"线下广告",label:'线下广告'},
				{name:"移动端推广",label:'移动端推广'},
				{name:"自来",label:'自来'},
				{name:"未知",label:'未知'},
				{name:"SEO",label:'SEO'},
				{name:"活动引流",label:'活动引流'},
				{name:"移动端放量",label:'移动端放量'}
			]
		},
        taList:{
			value:'-1',
			name:"注册来源",
			labelList:[
                {name:"全部",label:-1},
				{name:"建站来源",label:1},
				{name:"互动推广",label:3},
				{name:"微传单推广",label:4},
				{name:"线下广告",label:6},
				{name:"移动端推广",label:7},
				{name:"自来",label:8},
				{name:"未知",label:9},
				{name:"SEO",label:10},
				{name:"活动引流",label:11},
				{name:"移动端放量",label:12}
			]
		},
        arpuSort:{
            value:'upArpu',
            name:"排序",
            labelList:[
                {name:"arpu提升倒序",label:'upArpu'},
                {name:"arpu倒序",label:'arpu'}
            ]
        },
        arpuABSort:{
            value:'upArpu',
            name:"排序",
            labelList:[
                {name:"arpu提升倒序",label:'upArpu'},
                {name:"arpu倒序",label:'arpu'},
                {name:"权重倒序",label:'typeA'}
            ]
        },
        tag:{
            value:-1,
            name:"资源标签",
            labelList:[
                {name:"全部",label:-1},
                {name:"按昨天首次创建时间分配",label:50},
                {name:"按公众号注册分配",label:52},
                {name:"未转正员工分配",label:53},
                {name:"工作日分配-周末资源",label:54},
                {name:"非当月有创建游戏当天有登录资源",label:55},
                {name:"互动-非当月无创建游戏当天有登录资源",label:57},
                {name:"每半小时分配创建游戏",label:56},
                {name:"历史资源",label:59},
                {name:"未转正员工月度分配",label:60},
                {name:"周末分配-周末资源",label:65},
                {name:"新员工入职分配 6000",label:66},
                {name:"手动分配",label:0},
                {name:"当天开通互动资源",label:68},
                {name:"当天助手介绍页曝光资源",label:69},
                {name:"当天seo未创建游戏",label:70},
                {name:"当天自来未创建游戏",label:71},
                {name:"活动引流回到电脑",label:72},
                {name:"移动端放量",label:73},
                {name:"昨日seo未创建游戏",label:74},
                {name:"昨日自来未创建游戏",label:75},
                {name:"抖音资源",label:76},
                {name:"昨日开通互动资源",label:77},
                {name:"周五创建游戏未下发",label:78},
				{name:"公众号助手",label:80},
				{name:"昨日开通悦客",label:81},
				{name:"当天开通悦客",label:82},
				{name:"悦客历史资源",label:89},
				{name:"非当月资源",label:96},
				{name:"主动领取公海库资源",label:95},
				{name:"续费资源",label:98},
				{name:"券宝资源",label:97},
				{name:"活动引流资源",label:99},
				{name:"公海资源",label:101}
            ]
        }

    }
}