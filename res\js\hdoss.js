﻿Fai = {
	confirm: function(msg, title) {
		if(title == undefined) {
			title = "提示";
		}

		return new Promise(fun => {
			Vue.prototype.$confirm(msg, title, {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(action => {
				return fun();
			}).catch(() => {});
		});

	},
	

	// Fai.alert(msg, title, config).then(callback);
	alert: function(msg, title, config) {
		if(title == undefined) {
			title = "提示";
		}

		return new Promise(fun => {
			Vue.prototype.$alert(msg, title, config).then(action => {
				return fun();
			}).catch(() => {});
		});
	},

	loading: function() {
		initLoadingDiv();
		var faiLoading = new Vue({
			el: '#fai-loading',
			data: {
				loading: false
			}
		})

		// 请求前的拦截器
		Vue.http.interceptors.push(function(request, next) {
			var flag = true;
			setTimeout(function() {
				faiLoading.$data.loading = flag;
			}, 500)
			next(function(response) {
				flag = false;
				faiLoading.$data.loading = flag;
				return response;
			})
		})

		// 如果没有#fai-loading这个dom，则生成div，拼接到body
		function initLoadingDiv() {
			var div = document.getElementById('fai-loading');
			if(div == undefined || div == null) {
				div = document.createElement('div');
				div.id = 'fai-loading';
				div.setAttribute('v-loading.fullscreen.lock', 'loading');
				document.body.appendChild(div);
			}
		}
	},

	http: {
		/**
		 * 封装后的http
		 * @param {Object} m_url  方法
		 * @param {Object} m_param 参数
		 * @boolean {Object} m_hint 是否要提示(默认执行弹窗)
		 * 例子：Fai.http.post().then(result  => {})
		 */
		post: function(m_url, m_param, m_hint) {
			// loading
			Fai.loading();

			return new Promise(fun => {
				Vue.http.post("/ajax/" + m_url, m_param, {emulateJSON: true}).then(response => {
					var obj = response.data;
					var success = obj.success;

					var type = "error";
					var typeTitle = "错误";

					if(success) {
						type = "success";
						typeTitle = "提示";
					}

					// 如果有提示，需要点完确认后才执行fun的代码
					// isHint 默认弹窗 或者 如果success不成功也弹窗 !!!
					if(m_hint !== false || !success) {
						Fai.alert(obj.msg, typeTitle, {
							confirmButtonText: '确定',
							closeOnClickModal: true,
							type: type,
						}).then(action => {
							// 点击确定后执行回调
							typeof fun == 'function' && fun(obj);
						});
					} else {
						// 执行回调
						typeof fun == 'function' && fun(obj);
					}
					
				}, response => {
					// error 
					Fai.alert('服务繁忙，请稍候重试', '错误', {
						confirmButtonText: '确定',
						type: 'error',
					});
				});
			});
		}
	},

	url: {
		// 获取#号后面标志
		getNavFlag: function() {
			// 刷新的时候回到对应的导航 
			var reg = /#\/([\s\S]+)\/([\s\S]+)/
			var reg2 = /#\/([\s\S]+)/
			var result = "";
			if((result = reg.exec(document.location.href)) != null || (result = reg2.exec(document.location.href)) != null) {
				return result;
			}
			return "";
		},

		// 获取url里？后面的字符串
		getUrlQueryStr: function() {
			var queryStr = location.search;
			return queryStr.startsWith('?') ? queryStr.slice(1) : queryStr;
		},

		// 获取url里？后面的参数，返回json对象
		getUrlQueryParam: function() {
			var queryStr = Fai.url.getUrlQueryStr();

			var param = {};
			var strs = queryStr.split('&');
			strs.forEach(function(item, index) {
				if(item!=""){
					var paramItem = item.split('=');
					param[paramItem[0]] = paramItem[1];
				}
			});
			
			return param;
		},
		
		// 获取url的参数
		getParameter : function(key) {
			var param = Fai.url.getUrlQueryParam();
			if(param[key]==undefined){
				return null;
			}
			return param[key];
		},
	},

	table: {
		/**
		 * 
		 * @param {Object} obj 表格对象
		 * @param {Object} total 总数
		 * @param {Object} getDataList 获取数据的方法
		 * @param {Object} urlParam 获取数据的参数
		 */
		pagination: function(obj, total, getDataList, urlParam) {
			// 获取表格的class名
			var className = obj.$el.className;

			if(total == undefined || total == 0) {
				total = 1;
			}

			var div = document.getElementById(className + '-page');
			if(div == undefined || div == null) {
				var div1 = '<el-pagination @size-change="setlimit" @current-change="jumpPage" ' +
					':current-page="pageNow" :page-sizes="[10, 20, 30, 50, 100]" :page-size="limit" ' +
					'layout="total, sizes, prev, pager, next, jumper" :total="total" background> </el-pagination>';
				div = document.createElement('div');
				div.id = className + '-page'
				div.className = "fai-page"
				div.innerHTML = div1;
				// 在元素后面追加
				insertAfter(div, obj.$el);
				div = document.getElementById('fai-domain-list-page');

				// 分页对象
				window["_faiPage_" + className] = new Vue({
					el: '#' + className + '-page',
					data: {
						pageNow: 1,
						limit: 20,
						total: total,
					},
					methods: {
						// 设置显示数
						setlimit(val) {
							this.limit = val
							urlParam.limit = val;
							getDataList(urlParam)
						},
						// 跳转页数
						jumpPage(val) {
							urlParam.pageNow = val;
							getDataList(urlParam)
						}
					},
				})
			}
			// 设置总数
			window["_faiPage_" + className].$data.total = total;

			// 在元素后面追加的方法
			function insertAfter(newElement, targentElement) {
				var parent = targentElement.parentNode;
				if(parent.lastChild == targentElement) {
					parent.appendChild(newElement);
				} else {
					parent.insertBefore(newElement, targentElement.nextSibling)
				}
			}
		},

	},

	tool: {
		// 浮动水平滚动条
		scrollbar: function(obj) {
			if(obj.$data._slider == false) {
				return;
			}

			var div = document.getElementById('fai-slider');
			if(div == undefined || div == null) {
				// 获取横向滚动条的可滚动区域
				var max = obj.$el.getElementsByClassName('el-table__body')[0].clientWidth - obj.$el.offsetWidth;
				if(max <= 0) {
					return;
				}

				div = document.createElement('el-slider');
				div.id = 'fai-slider';
				div.setAttribute('v-if', 'show');
				div.setAttribute('v-model', 'slider');
				div.setAttribute(':show-tooltip', "false");
				div.setAttribute(':max', "max");
				div.setAttribute(':format-tooltip', 'formatTooltip');
				document.body.appendChild(div)

				// 绑定对象
				faiSlider = new Vue({
					el: '#fai-slider',
					data: {
						slider: 0,
						show: true,
						max: max + 20,
					},
					methods: {
						formatTooltip(val) {
							// 控制滚动条位置
							obj.$el.getElementsByClassName('el-table__body-wrapper')[0].scrollLeft = val;
						}
					}
				})

				// 让对象只调用一次
				obj.$data._slider = false;

				/************************* 监控页面变化的时候改变对象移动宽度 *************************/
				// 获取已经赋值的函数
				var oldOnresize = window.onresize;
				window.onresize = function() {
					// 执行已经赋值的函数
					if(oldOnresize) {
						oldOnresize();
					}
					setTimeout(function() {
						// 获取横向滚动条的可滚动区域
						var max = obj.$el.getElementsByClassName('el-table__body')[0].clientWidth - obj.$el.offsetWidth;
						if(max <= 0) {
							// 控制显示隐藏
							faiSlider.$data.show = false;
							return;
						}
						// 控制显示隐藏
						faiSlider.$data.show = true;
						// 设置可移动距离
						faiSlider.$data.max = max + 20;
					}, 500)
				};
			}
		},
		
		getCookie: function (name){
			var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
			if(arr=document.cookie.match(reg)){
				return unescape(arr[2]);
			}else{
				return null;
			}
		},

		// 获取分页数据
		getPageList: function(list, pageNow, limit, total) {
			var newList = [];
			// 开始下标
			var start = (pageNow - 1) * limit;
			// 结束下标
			var length = pageNow * limit;
			// 如果结束下标大于总数则取总数
			if(length > total) {
				length = total;
			}
			for(var i = start; i < length; i++) {
				newList.push(list[i]);
			}
			return newList;
		},
		//传入一个data,返回一个0时0分的时间戳
		getDateTimeStart(data){
			data.setHours(0);
			data.setMinutes(0);
			data.setSeconds(0);
			return parseInt(data.getTime()/1000);
		},
		//传入一个data,返回一个23时59分的时间戳
		getDateTimeEnd(data){
			data.setHours(23);
			data.setMinutes(59);
			data.setSeconds(59);
			return parseInt(data.getTime()/1000);
		},
		//将一个json对象转为url参数形式
		parseJsonToUrlParam(urlParam,first){
			if(urlParam == undefined){
				return;
			}
			var url = "";
			var i = 0;
			for(var key in urlParam){
				//console.log(key);
				if(key==undefined || urlParam[key] == undefined){
					continue;
				}
				if(i == 0 && first){
					url = "?"+key+"="+urlParam[key]
				}else{
					if(key!=undefined && urlParam[key] !=undefined){
						url += "&"+key+"="+urlParam[key]
					}
				}
				i++;
			}
			return url;
		},
		//格式化日期，可传入参数类型[时间戳、日期字符串、UTC格式字符串...]
		dateFormatter: function(data){//默认返回yyyy-MM-dd
			var d = new Date(data);
			var year = d.getFullYear();
			var month = (d.getMonth()+1)<10 ? '0'+(d.getMonth()+1) : (d.getMonth()+1);
			var day = d.getDate()<10 ? '0'+d.getDate() : d.getDate();
			if(arguments[1]){//可传第二个参数true，返回yyyy-MM-dd HH:mm:ss
				var hour = d.getHours()<10 ? '0'+d.getHours() : d.getHours();
				var minute = d.getMinutes()<10 ? '0'+d.getMinutes() : d.getMinutes();
				var second = d.getSeconds()<10 ? '0'+d.getSeconds() : d.getSeconds();
				return [year, month, day].join('-') + " " + [hour, minute, second].join(':');
			}else{
				return [year, month, day].join('-');
			}
		},
		//获得某月的天数,可传入参数类型[时间戳、日期字符串、UTC格式字符串...]
		getMonthDays: function(data){
			var monthStartDate = new Date(data);
			monthStartDate.setDate(1);
			var monthEndDate = new Date(monthStartDate);
			monthEndDate.setMonth(monthStartDate.getMonth() + 1);
			var days = (monthEndDate - monthStartDate)/(1000 * 60 * 60 * 24);
			return days;
		},
		
		
	},
	
	iframe: {
		notify: function(el){
			if(Fai.url.getParameter("isIframe")){
				// 设置宽度跟高度，让父窗口知道自己的宽高
				var param = {};
				param.width = el.scrollWidth;
				param.height = el.scrollHeight;
				Fai.iframe.setWindowName(param);
				
				// 调用
				window.parent.resetIframe();
			}
		},
		
		// 给父窗口传递信息
		setWindowName: function(param){
			window.name = JSON.stringify(param); 
		},
		
		// 重设iframe高度和宽度，由iframe页面调用
		setIframeAttr: function(iframeId, boxBoj){
			setTimeout(function() {
				var iframe = document.getElementById(iframeId);
			 	if(iframe != null) {
				 	iframe = JSON.parse(iframe.contentWindow.name);
				 	boxBoj.width = iframe.width+"px";
		 			boxBoj.height = iframe.height+10+"px"; 
			 	}
			}, 500)
		}
	}
	

};

;(function (Fai) {
    'use strict';

    /*
    * Add integers, wrapping at 2^32. This uses 16-bit operations internally
    * to work around bugs in some JS interpreters.
    */
    function safe_add(x, y) {
        var lsw = (x & 0xFFFF) + (y & 0xFFFF),
            msw = (x >> 16) + (y >> 16) + (lsw >> 16);
        return (msw << 16) | (lsw & 0xFFFF);
    }

    /*
    * Bitwise rotate a 32-bit number to the left.
    */
    function bit_rol(num, cnt) {
        return (num << cnt) | (num >>> (32 - cnt));
    }

    /*
    * These functions implement the four basic operations the algorithm uses.
    */
    function md5_cmn(q, a, b, x, s, t) {
        return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
    }
    function md5_ff(a, b, c, d, x, s, t) {
        return md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);
    }
    function md5_gg(a, b, c, d, x, s, t) {
        return md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);
    }
    function md5_hh(a, b, c, d, x, s, t) {
        return md5_cmn(b ^ c ^ d, a, b, x, s, t);
    }
    function md5_ii(a, b, c, d, x, s, t) {
        return md5_cmn(c ^ (b | (~d)), a, b, x, s, t);
    }

    /*
    * Calculate the MD5 of an array of little-endian words, and a bit length.
    */
    function binl_md5(x, len) {
        /* append padding */
        x[len >> 5] |= 0x80 << ((len) % 32);
        x[(((len + 64) >>> 9) << 4) + 14] = len;

        var i, olda, oldb, oldc, oldd,
            a =  1732584193,
            b = -271733879,
            c = -1732584194,
            d =  271733878;

        for (i = 0; i < x.length; i += 16) {
            olda = a;
            oldb = b;
            oldc = c;
            oldd = d;

            a = md5_ff(a, b, c, d, x[i],       7, -680876936);
            d = md5_ff(d, a, b, c, x[i +  1], 12, -389564586);
            c = md5_ff(c, d, a, b, x[i +  2], 17,  606105819);
            b = md5_ff(b, c, d, a, x[i +  3], 22, -1044525330);
            a = md5_ff(a, b, c, d, x[i +  4],  7, -176418897);
            d = md5_ff(d, a, b, c, x[i +  5], 12,  1200080426);
            c = md5_ff(c, d, a, b, x[i +  6], 17, -1473231341);
            b = md5_ff(b, c, d, a, x[i +  7], 22, -45705983);
            a = md5_ff(a, b, c, d, x[i +  8],  7,  1770035416);
            d = md5_ff(d, a, b, c, x[i +  9], 12, -1958414417);
            c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
            b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
            a = md5_ff(a, b, c, d, x[i + 12],  7,  1804603682);
            d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
            c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
            b = md5_ff(b, c, d, a, x[i + 15], 22,  1236535329);

            a = md5_gg(a, b, c, d, x[i +  1],  5, -165796510);
            d = md5_gg(d, a, b, c, x[i +  6],  9, -1069501632);
            c = md5_gg(c, d, a, b, x[i + 11], 14,  643717713);
            b = md5_gg(b, c, d, a, x[i],      20, -373897302);
            a = md5_gg(a, b, c, d, x[i +  5],  5, -701558691);
            d = md5_gg(d, a, b, c, x[i + 10],  9,  38016083);
            c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
            b = md5_gg(b, c, d, a, x[i +  4], 20, -405537848);
            a = md5_gg(a, b, c, d, x[i +  9],  5,  568446438);
            d = md5_gg(d, a, b, c, x[i + 14],  9, -1019803690);
            c = md5_gg(c, d, a, b, x[i +  3], 14, -187363961);
            b = md5_gg(b, c, d, a, x[i +  8], 20,  1163531501);
            a = md5_gg(a, b, c, d, x[i + 13],  5, -1444681467);
            d = md5_gg(d, a, b, c, x[i +  2],  9, -51403784);
            c = md5_gg(c, d, a, b, x[i +  7], 14,  1735328473);
            b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);

            a = md5_hh(a, b, c, d, x[i +  5],  4, -378558);
            d = md5_hh(d, a, b, c, x[i +  8], 11, -2022574463);
            c = md5_hh(c, d, a, b, x[i + 11], 16,  1839030562);
            b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
            a = md5_hh(a, b, c, d, x[i +  1],  4, -1530992060);
            d = md5_hh(d, a, b, c, x[i +  4], 11,  1272893353);
            c = md5_hh(c, d, a, b, x[i +  7], 16, -155497632);
            b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
            a = md5_hh(a, b, c, d, x[i + 13],  4,  681279174);
            d = md5_hh(d, a, b, c, x[i],      11, -358537222);
            c = md5_hh(c, d, a, b, x[i +  3], 16, -722521979);
            b = md5_hh(b, c, d, a, x[i +  6], 23,  76029189);
            a = md5_hh(a, b, c, d, x[i +  9],  4, -640364487);
            d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
            c = md5_hh(c, d, a, b, x[i + 15], 16,  530742520);
            b = md5_hh(b, c, d, a, x[i +  2], 23, -995338651);

            a = md5_ii(a, b, c, d, x[i],       6, -198630844);
            d = md5_ii(d, a, b, c, x[i +  7], 10,  1126891415);
            c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
            b = md5_ii(b, c, d, a, x[i +  5], 21, -57434055);
            a = md5_ii(a, b, c, d, x[i + 12],  6,  1700485571);
            d = md5_ii(d, a, b, c, x[i +  3], 10, -1894986606);
            c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
            b = md5_ii(b, c, d, a, x[i +  1], 21, -2054922799);
            a = md5_ii(a, b, c, d, x[i +  8],  6,  1873313359);
            d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
            c = md5_ii(c, d, a, b, x[i +  6], 15, -1560198380);
            b = md5_ii(b, c, d, a, x[i + 13], 21,  1309151649);
            a = md5_ii(a, b, c, d, x[i +  4],  6, -145523070);
            d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
            c = md5_ii(c, d, a, b, x[i +  2], 15,  718787259);
            b = md5_ii(b, c, d, a, x[i +  9], 21, -343485551);

            a = safe_add(a, olda);
            b = safe_add(b, oldb);
            c = safe_add(c, oldc);
            d = safe_add(d, oldd);
        }
        return [a, b, c, d];
    }

    /*
    * Convert an array of little-endian words to a string
    */
    function binl2rstr(input) {
        var i,
            output = '';
        for (i = 0; i < input.length * 32; i += 8) {
            output += String.fromCharCode((input[i >> 5] >>> (i % 32)) & 0xFF);
        }
        return output;
    }

    /*
    * Convert a raw string to an array of little-endian words
    * Characters >255 have their high-byte silently ignored.
    */
    function rstr2binl(input) {
        var i,
            output = [];
        output[(input.length >> 2) - 1] = undefined;
        for (i = 0; i < output.length; i += 1) {
            output[i] = 0;
        }
        for (i = 0; i < input.length * 8; i += 8) {
            output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << (i % 32);
        }
        return output;
    }

    /*
    * Calculate the MD5 of a raw string
    */
    function rstr_md5(s) {
        return binl2rstr(binl_md5(rstr2binl(s), s.length * 8));
    }

    /*
    * Calculate the HMAC-MD5, of a key and some data (raw strings)
    */
    function rstr_hmac_md5(key, data) {
        var i,
            bkey = rstr2binl(key),
            ipad = [],
            opad = [],
            hash;
        ipad[15] = opad[15] = undefined;                        
        if (bkey.length > 16) {
            bkey = binl_md5(bkey, key.length * 8);
        }
        for (i = 0; i < 16; i += 1) {
            ipad[i] = bkey[i] ^ 0x36363636;
            opad[i] = bkey[i] ^ 0x5C5C5C5C;
        }
        hash = binl_md5(ipad.concat(rstr2binl(data)), 512 + data.length * 8);
        return binl2rstr(binl_md5(opad.concat(hash), 512 + 128));
    }

    /*
    * Convert a raw string to a hex string
    */
    function rstr2hex(input) {
        var hex_tab = '0123456789abcdef',
            output = '',
            x,
            i;
        for (i = 0; i < input.length; i += 1) {
            x = input.charCodeAt(i);
            output += hex_tab.charAt((x >>> 4) & 0x0F) +
                hex_tab.charAt(x & 0x0F);
        }
        return output;
    }

    /*
    * Encode a string as utf-8
    */
    function str2rstr_utf8(input) {
        return unescape(encodeURIComponent(input));
    }

    /*
    * Take string arguments and return either raw or hex encoded strings
    */
    function raw_md5(s) {
        return rstr_md5(str2rstr_utf8(s));
    }
    function hex_md5(s) {
        return rstr2hex(raw_md5(s));
    }
    function raw_hmac_md5(k, d) {
        return rstr_hmac_md5(str2rstr_utf8(k), str2rstr_utf8(d));
    }
    function hex_hmac_md5(k, d) {
        return rstr2hex(raw_hmac_md5(k, d));
    }
    
    Fai.md5 = function (string, key, raw) {
        if (!key) {
            if (!raw) {
                return hex_md5(string);
            } else {
                return raw_md5(string);
            }
        }
        if (!raw) {
            return hex_hmac_md5(key, string);
        } else {
            return raw_hmac_md5(key, string);
        }
    };
    
}(Fai));

/*iframe弹窗模板组件*/
Vue.component('fai-iframe-box', {
	template: ` 
			<div class="fai-iframe-box">
	    		<div class="fai-iframe-box-div">
					<slot name='fai-iframe-data'></slot>
				</div>
				<div class="el-dialog__footer fai-dialog__footer">
					<span class="dialog-footer">
						<el-button @click="close">取消</el-button>
						<el-button type="primary" @click="submit">确 定</el-button>
					</span>
				</div>
			</div>
			`,
	props:["close", "submit"],
});

/**
 *  例子
 *  <fai-iframe-box-dialog 
	  class="fai-iframe-box-dialog" 
	  :src="src" 
	  :visible.sync="dialogVisible" 
	  :close="beforeClose" 
	  :width.sync="width" 
	  :height.sync="height">
	</fai-iframe-box-dialog>
 */
Vue.component('fai-iframe-box-dialog', {
	template: ` 
			<div>
				<el-dialog title="编辑" :visible.sync="m_visible" :before-close="close" :width="width">
					<iframe v-bind:src="src" id="fai-iframeId" :style="'width:100%;height: '+height+';'" frameborder="0" scrolling="yes"></iframe>
				</el-dialog>
			</div>
			`,
	props: {
		src: {
			default:"",
		},
		isIframe:{
			default:"",
		},
		visible: {
			default:false,
		},
		width: {
			default:"600px",
		},
		height: {
			default:"500px",
		},
		close: {
			default:function () {},
		},
	},
	data: function(){
		return {
			m_visible: this.visible,
			m_width: this.width,
			m_height: this.height,
		}
	},
	watch: {
		// 监听 visible的变化
		visible: function(val, oldVal){
			this.m_visible = val;
		},
		// 监听m_visible的变化改变父组件的值
		m_visible: function(val, oldVal){
			// 改变父组件visible的值
			this.$emit('update:visible', val);
			// 关闭的时候改回默认宽度大小
			if(!val){
				this.$emit('update:width', this.m_width);
				this.$emit('update:height', this.m_height);
			}
		},
	}
});

