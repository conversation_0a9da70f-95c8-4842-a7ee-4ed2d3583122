package fai.webhdoss.service;

import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scRes.ScResListVO;
import fai.webhdoss.model.vo.scRes.ScResVO;

import javax.servlet.http.HttpServletRequest;

public interface ScResService {

    // 分类列表
    JsonResult getScCategoryList(ScResVO vo);

    // 新增
    JsonResult addScRes(ScResVO vo);

    // 更新
    JsonResult setScRes(ScResVO vo);

    // 删除
    JsonResult delScRes(ScResVO vo);


    JsonResult getScResList(ScResListVO vo);

    // 上传临时文件
    JsonResult uploadTmpFile(HttpServletRequest request, int scResType);

    FaiList<Param> getScResListNew(ScResListVO vo);
}
