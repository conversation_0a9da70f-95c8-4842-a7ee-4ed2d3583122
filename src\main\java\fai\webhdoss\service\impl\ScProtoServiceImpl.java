package fai.webhdoss.service.impl;

import com.alibaba.fastjson.JSON;
import fai.app.ScProtoDef;
import fai.cli.ScProtoCli;
import fai.comm.jnetkit.server.fai.RemoteStandResult;
import fai.comm.util.*;
import fai.comm.util.api.ScDifyUtil;
import fai.entity.ScProtoEntity;
import fai.hdUtil.HdCalendarUtil;
import fai.hdUtil.HdMisc;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.HdAssert;
import fai.web.Web;
import fai.webhdoss.model.vo.scProto.ScProtoListVO;
import fai.webhdoss.model.vo.scProto.ScProtoVO;
import fai.webhdoss.service.ScProtoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ScProtoServiceImpl implements ScProtoService {

    @Autowired
    private ScProtoCli scProtoCli;

    /**
     * 这里有三种情况
     * 1.新建原型点击同步（只需要返回dify的数据,只传apiKey）
     * 2.编辑原型获取数据（只需要返回保存的原型数据，只传id）
     * 3.编辑原型点击同步 / 修改apiKey （以原型的数据为基础，补充保存的数据，传apiKey+id）
     **/
    @Override
    public JsonResult syncProto(int type, String apiKey, int id) throws Exception {
        // 返回数据
        Param resultInfo = new Param();

        // 同步
        if (!Str.isEmpty(apiKey)) {
            RemoteStandResult oldInfoResult = scProtoCli.getScProtoInfoByKey(apiKey);
            ScProtoEntity entity = oldInfoResult.getObject(ScProtoDef.Protocol.Key.INFO, ScProtoEntity.class);

            // 判断apiKey是否已存在
            boolean checkApiKey = false;
            if (id < 1) {
                // 如果是新建
                checkApiKey = entity != null;
            } else if (entity != null) {
                // 修改apiKey
                checkApiKey = entity.getId() != id;
            }
            HdAssert.Biz.judgeNotLog(checkApiKey, Errno.ALREADY_EXISTED
                    , String.format("已存在原型(ID: %s)关联了当前配置的Apikey", Optional.ofNullable(entity).isPresent() ? entity.getId() : ""));

            // 获取apiKey获取dify- 描述数据
            ScResult difyResult = ScDifyUtil.getBaseInfo(Web.isDev(), apiKey);
            HdAssert.Biz.judgeNotLog(!difyResult.isSuc(), difyResult.getRt(), "获取dify描述信息失败：" + difyResult.getMsg());
            Param difyDescInfo = difyResult.getData();

            // 获取apiKey获取dify 数据
            difyResult = ScDifyUtil.getParameters(Web.isDev(), apiKey);
            HdAssert.Biz.judgeNotLog(!difyResult.isSuc() && difyResult.getMsg().contains("App unavailable"), difyResult.getRt(), "dify原型未发布，请先发布");
            HdAssert.Biz.judgeNotLog(!difyResult.isSuc(), difyResult.getRt(), "获取dify数据失败:" + difyResult.getMsg());
            Param difyInfo = difyResult.getData();

            // 拼接结果数据返回
            // 脚本数据
            FaiList<Param> scriptList = FaiList.parseParamList(difyDescInfo.getString("description"), new FaiList<>());
            FaiList<Param> scriptCommList = new FaiList<>();
            for (Param p : scriptList) {
                Param tmpInfo = new Param();
                tmpInfo.setString(ScProtoDef.Script.Comm.LABEL, p.getString("title", ""));
                tmpInfo.setString(ScProtoDef.Script.Comm.DESC, p.getString("desc", ""));
                tmpInfo.setList(ScProtoDef.Script.Comm.RES_LIST, new FaiList<>());

                scriptCommList.add(tmpInfo);
            }

            // 输入数据拼接
            FaiList<Param> userInputFormList = difyInfo.getListNullIsEmpty("user_input_form");
            FaiList<Param> inputFormList = new FaiList<>();
            for (Param p : userInputFormList) {
                Param pp = p.getParamNullIsEmpty("text-input");

                Param tmpInfo = new Param();
                tmpInfo.setString(ScProtoDef.InputForm.VARIABLE, pp.getString("variable", ""));
                tmpInfo.setString(ScProtoDef.InputForm.LABEL, pp.getString("label", ""));
                tmpInfo.setInt(ScProtoDef.InputForm.MAX_LENGTH, pp.getInt("max_length", 0));
                tmpInfo.setBoolean(ScProtoDef.InputForm.REQUIRED, pp.getBoolean("required", false));
                tmpInfo.setInt(ScProtoDef.InputForm.FILED_TYPE, ScProtoDef.FiledType.TEXT);

                inputFormList.add(tmpInfo);
            }

            // 拼接返回的数据
            resultInfo.setInt(ScProtoDef.Info.TYPE, type);
            resultInfo.setString(ScProtoDef.Info.API_KEY, apiKey);
            resultInfo.setString(ScProtoDef.Info.NAME, difyDescInfo.getString("name", ""));
            resultInfo.setList("inputFormList", inputFormList);
            resultInfo.setList("commList", scriptCommList);
            resultInfo.setList("resFormList", new FaiList<>());

            // 拼接图文才有的字段
            if (type == ScProtoDef.Type.IMG_TEXT) {
                Param coverInfo = new Param();
                coverInfo.setList(ScProtoDef.Script.Graphic.STYLE_LIST, new FaiList<>());
                coverInfo.setList(ScProtoDef.Script.Graphic.RES_LIST, new FaiList<>());

                // 设置一个空的封面图和次数的配置
                resultInfo.setParam("cover", coverInfo);
                resultInfo.setParam("secondary", coverInfo.clone());
            }

            // 新建同步
            if (id < 1) {
                return JsonResult.success(resultInfo);
            }
        }

        if (id > 0) {
            // 编辑
            RemoteStandResult scProtoInfoById = scProtoCli.getScProtoInfoById(id);
            HdAssert.Biz.judgeNotLog(scProtoInfoById.getRt() == Errno.NOT_FOUND, scProtoInfoById.getRt(), "原型不存在，请刷新后重试");
            HdAssert.Biz.judgeNotLog(scProtoInfoById.getRt() != Errno.OK, scProtoInfoById.getRt(), "获取原型信息失败，请稍后重试");

            ScProtoEntity entity = scProtoInfoById.getObject(ScProtoDef.Protocol.Key.INFO, ScProtoEntity.class);
            HdAssert.Biz.judgeNotLog(entity == null, "原型不存在，请稍后重试");

            // 拼接返回的数据
            resultInfo.setInt(ScProtoDef.Info.ID, entity.getId());
            resultInfo.setInt(ScProtoDef.Info.TYPE, entity.getType());
            resultInfo.setString(ScProtoDef.Info.API_KEY, resultInfo.getString(ScProtoDef.Info.API_KEY, entity.getApiKey()));
            resultInfo.setString(ScProtoDef.Info.NAME, entity.getName());

            // inputForm 补充旧数据
            FaiList<Param> oldList = FaiList.parseParamList(entity.getInputForm(), new FaiList<>());
            Param oldMap = HdMisc.listToParam(oldList, ScProtoDef.InputForm.VARIABLE, ScProtoDef.InputForm.FILED_TYPE);
            FaiList<Param> baseList = Str.isEmpty(apiKey) ? oldList : resultInfo.getListNullIsEmpty("inputFormList");
            for (Param p : baseList) {
                String p_variable = p.getString(ScProtoDef.InputForm.VARIABLE, "");
                int p_filedType = p.getInt(ScProtoDef.InputForm.FILED_TYPE, ScProtoDef.FiledType.TEXT);

                // 补充db数据补充
                p.setInt(ScProtoDef.InputForm.FILED_TYPE, oldMap.getInt(p_variable, p_filedType));
            }
            resultInfo.setList("inputFormList", baseList);

            // script 补充旧数据
            Param scriptInfo = Param.parseParamNullIsEmpty(entity.getScript());
            oldList = scriptInfo.getListNullIsEmpty("comm");

            baseList = Str.isEmpty(apiKey) ? oldList : resultInfo.getListNullIsEmpty("commList");
            for (int i = 0; i < baseList.size(); i++) {
                if (i >= oldList.size()) {
                    break;
                }

                Param p = baseList.get(i);
                p.setList(ScProtoDef.Script.Comm.RES_LIST, oldList.get(i).getListNullIsEmpty(ScProtoDef.Script.Comm.RES_LIST));
            }
            resultInfo.setList("commList", baseList);

            // 补充图文特有信息
            oldList = scriptInfo.getListNullIsEmpty("graphic");
            for (Param p : oldList) {
                resultInfo.setParam(p.getString(ScProtoDef.Script.Graphic.KEY, ""), p);
            }

            // resFormList 补充资源信息
            resultInfo.setList("resFormList", FaiList.parseParamList(entity.getResForm(), new FaiList<>()));
        }

        return JsonResult.success(resultInfo);
    }

    @Override
    public JsonResult setScProtoInfo(ScProtoVO vo) {
        // 前置校验
        HdAssert.Biz.judgeNotLog(CollectionUtil.isEmpty(vo.getInputFormList()), Errno.ARGS_ERROR, "原型表单不能为空");
        HdAssert.Biz.judgeNotLog(CollectionUtil.isEmpty(vo.getCommList()), Errno.ARGS_ERROR, "剪辑逻辑不能为空");
        HdAssert.Biz.judgeNotLog(CollectionUtil.isEmpty(vo.getResFormList()), Errno.ARGS_ERROR, "素材组不能为空");

        // 前置校验：特殊字段保证校验
        boolean hadCntArg = vo.getInputFormList().stream().anyMatch(p -> "output_quantity".equals(p.getVariable()));
        HdAssert.Biz.judgeNotLog(!hadCntArg, Errno.ARGS_ERROR, "dify中缺少脚本数量定义：output_quantity");

        int id = vo.getId();
        if (id > 0) {
            // 查询数据，对比apiKey是否一致
            RemoteStandResult scProtoInfoById = scProtoCli.getScProtoInfoById(id);
            HdAssert.Biz.judgeNotLog(scProtoInfoById.getRt() == Errno.NOT_FOUND, scProtoInfoById.getRt(), "原型不存在，请刷新后重试");
        } else {
            RemoteStandResult oldInfoResult = scProtoCli.getScProtoInfoByKey(vo.getApiKey());
            ScProtoEntity entity = oldInfoResult.getObject(ScProtoDef.Protocol.Key.INFO, ScProtoEntity.class);
            HdAssert.Biz.judgeNotLog(entity != null, Errno.ALREADY_EXISTED
                    , String.format("已存在原型(ID: %s)关联了当前配置的Apikey", Optional.ofNullable(entity).isPresent() ? entity.getId() : ""));
        }

        // 拼接数据
        Param scriptInfo = new Param();
        FaiList<Param> scriptCommList = FaiList.parseParamList(JSON.toJSONString(vo.getCommList()), new FaiList<>());
        FaiList<Param> scriptGraphicList = new FaiList<>();
        Param scriptGraphicCoverInfo = Param.parseParamNullIsEmpty(JSON.toJSONString(vo.getCover()));
        scriptGraphicCoverInfo.setString(ScProtoDef.Script.Graphic.KEY, "cover");
        Param scriptGraphicSecondaryInfo = Param.parseParamNullIsEmpty(JSON.toJSONString(vo.getSecondary()));
        scriptGraphicSecondaryInfo.setString(ScProtoDef.Script.Graphic.KEY, "secondary");
        scriptGraphicList.add(scriptGraphicCoverInfo);
        scriptGraphicList.add(scriptGraphicSecondaryInfo);
        scriptInfo.setList(ScProtoDef.Script.COMM, scriptCommList);
        scriptInfo.setList(ScProtoDef.Script.GRAPHIC, scriptGraphicList);

        ScProtoEntity entity = new ScProtoEntity();
        entity.setType(vo.getType());
        entity.setName(vo.getName());
        entity.setApiKey(vo.getApiKey());
        entity.setInputForm(JSON.toJSONString(vo.getInputFormList()));
        entity.setScript(scriptInfo.toJson());
        entity.setResForm(JSON.toJSONString(vo.getResFormList()));

        RemoteStandResult remoteStandResult;
        if (id > 0) {
            entity.setId(id);
            remoteStandResult = scProtoCli.setScProtoInfoById(entity);
        } else {
            remoteStandResult = scProtoCli.addScProtoInfo(entity);
        }

        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "保存失败，请稍后重试");

        // 组装数据保存
        return JsonResult.success("保存成功");
    }

    @Override
    public JsonResult getScProtoList(ScProtoListVO vo) {
        // 获取参数
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        HdMisc.setMatcher(vo.getType() >= 0, searchArg.matcher, ScProtoDef.Info.TYPE, ParamMatcher.EQ, vo.getType());
        HdMisc.setMatcher(vo.getStatus() >= 0, searchArg.matcher, ScProtoDef.Info.STATUS, ParamMatcher.EQ, vo.getStatus());
        HdMisc.setMatcher(!ScMisc.isEmpty(vo.getCreateTimeStart()) && !ScMisc.isEmpty(vo.getCreateTimeEnd())
                , searchArg.matcher, ScProtoDef.Info.CREATE_TIME, ParamMatcher.GE, HdCalendarUtil.getOneDayFirst(vo.getCreateTimeStart()));
        HdMisc.setMatcher(!ScMisc.isEmpty(vo.getCreateTimeEnd()) && !ScMisc.isEmpty(vo.getCreateTimeStart())
                , searchArg.matcher, ScProtoDef.Info.CREATE_TIME, ParamMatcher.LE, HdCalendarUtil.getOneDayFirst(vo.getCreateTimeEnd()));

        if (!Str.isEmpty(vo.getKeyword()) && !Str.isEmpty(vo.getKey())) {
            // 名称做模糊搜索，其他做精准搜索
            if (ScProtoDef.Info.NAME.equals(vo.getKey())) {
                searchArg.matcher.and(ScProtoDef.Info.NAME, ParamMatcher.LK, vo.getKeyword());
            } else if ("all".equals(vo.getKey())) {
                // 全部搜索
                ParamMatcher orPM = new ParamMatcher(ScProtoDef.Info.NAME, ParamMatcher.LK, vo.getKeyword());
                orPM.or(ScProtoDef.Info.API_KEY, ParamMatcher.LK, vo.getKeyword());
                if (StringUtils.isNumeric(vo.getKeyword())) {
                    orPM.or(ScProtoDef.Info.ID, ParamMatcher.EQ, Integer.valueOf(vo.getKeyword()));
                }
                searchArg.matcher.and(orPM);

            } else {
                searchArg.matcher.and(vo.getKey(), ParamMatcher.EQ, vo.getKeyword());
            }
        }
        searchArg.start = vo.getOffset();
        searchArg.limit = vo.getPageLimit();
        searchArg.totalSize = new Ref<>(0);
        searchArg.cmpor = new ParamComparator(ScProtoDef.Info.ID, true);

        RemoteStandResult remoteStandResult = scProtoCli.getScProtoList(searchArg, "", "");
        if (remoteStandResult.getRt() == Errno.NOT_FOUND) {
            return JsonResult.success(new FaiList<>());
        }
        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "获取列表失败，请稍后重试");

        List<ScProtoEntity> list = remoteStandResult.getObject(ScProtoDef.Protocol.Key.INFO_LIST, List.class);
        int totalSize = remoteStandResult.getObject(ScProtoDef.Protocol.Key.TOTAL_SIZE, Integer.class);

        // 拼接参数
        FaiList<Param> returnList = new FaiList<>();
        for (ScProtoEntity p : list) {
            Param tmpInfo = new Param();
            tmpInfo.setInt(ScProtoDef.Info.ID, p.getId());
            tmpInfo.setString(ScProtoDef.Info.TYPE, ScProtoDef.Type.getName(p.getType()));
            tmpInfo.setString(ScProtoDef.Info.API_KEY, p.getApiKey());
            tmpInfo.setString(ScProtoDef.Info.NAME, p.getName());
            tmpInfo.setInt(ScProtoDef.Info.STATUS, p.getStatus());
            tmpInfo.setString("statusName", ScProtoDef.Status.getName(p.getStatus()));
            tmpInfo.setString(ScProtoDef.Info.UPDATE_TIME, Parser.parseSecondString(p.getUpdateTime()));
            tmpInfo.setString(ScProtoDef.Info.CREATE_TIME, Parser.parseSecondString(p.getCreateTime()));
            returnList.add(tmpInfo);
        }

        JsonResult result = JsonResult.success(returnList);
        result.setTotalSize(totalSize);

        return result;
    }

    @Override
    public JsonResult setScProtoStatus(int id, int status) {
        // 获取参数
        RemoteStandResult remoteStandResult = scProtoCli.getScProtoInfoById(id);
        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "原型不存在，请刷新重试");

        // 拼接参数
        ScProtoEntity entity = new ScProtoEntity();
        entity.setId(id);
        entity.setStatus(status);

        remoteStandResult = scProtoCli.setScProtoInfoById(entity);
        HdAssert.Biz.judgeNotLog(remoteStandResult.getRt() != Errno.OK, remoteStandResult.getRt(), "设置失败，请稍后重试");

        return JsonResult.success("设置成功");
    }

    @Override
    public JsonResult getScProtoNameList(int type, String keyword, int pageNo, int pageLimit) {
        // 获取数据
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(ScProtoDef.Info.STATUS, ParamMatcher.EQ, ScProtoDef.Status.USING);
        searchArg.matcher.and(ScProtoDef.Info.TYPE, ParamMatcher.EQ, type);
        HdMisc.setMatcher(!Str.isEmpty(keyword), searchArg.matcher, ScProtoDef.Info.NAME, ParamMatcher.LK, keyword);

        searchArg.start = (pageNo - 1) * pageLimit;
        searchArg.limit = pageLimit;
        searchArg.cmpor = new ParamComparator(ScProtoDef.Info.ID, true);
        searchArg.totalSize = new Ref<>(0);

        String fields = ScProtoDef.Info.ID + "," + ScProtoDef.Info.NAME;
        RemoteStandResult standResult = scProtoCli.getScProtoList(searchArg, fields, "");
        if (standResult.getRt() == Errno.NOT_FOUND) {
            return JsonResult.success(new FaiList<>());
        }
        HdAssert.Biz.judgeNotLog(standResult.getRt() != Errno.OK, standResult.getRt(), "获取列表失败，请稍后重试");

        // 拼接数据返回
        List<ScProtoEntity> list = standResult.getObject(ScProtoDef.Protocol.Key.INFO_LIST, List.class);
        int totalSize = standResult.getObject(ScProtoDef.Protocol.Key.TOTAL_SIZE, Integer.class);
        FaiList<Param> returnList = new FaiList<>();
        for (ScProtoEntity entity : list) {
            Param tmpInfo = new Param();
            tmpInfo.setInt(ScProtoDef.Info.ID, entity.getId());
            tmpInfo.setString(ScProtoDef.Info.NAME, entity.getId() + "-" + entity.getName());

            returnList.add(tmpInfo);
        }

        JsonResult jsonResult = JsonResult.success(returnList);
        jsonResult.setTotalSize(totalSize);
        return jsonResult;
    }

    @Override
    public JsonResult getConfInfo() {
        FaiList<Param> filedTypeList = HdMisc.refDefList(ScProtoDef.FiledType.class);
        FaiList<Param> coverStyleList = HdMisc.refDefList(ScProtoDef.CoverStyle.class);
        FaiList<Param> secondaryStyleList = HdMisc.refDefList(ScProtoDef.SecondaryStyle.class);

        Param info = new Param();
        info.setList("filedTypeList", filedTypeList);
        info.setList("coverStyleList", coverStyleList);
        info.setList("secondaryStyleList", secondaryStyleList);

        return JsonResult.success(info);
    }
}
