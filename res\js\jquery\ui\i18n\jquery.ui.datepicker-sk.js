/* Slovak initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['sk'] = {
		closeText: 'Zavrie<PERSON>',
		prevText: '&#x3c;Predchád<PERSON><PERSON><PERSON><PERSON>',
		nextText: 'Nasledujúci&#x3e;',
		currentText: 'D<PERSON>',
		monthNames: ['<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','August','September','Október','November','December'],
		monthNamesShort: ['Jan','Feb','Mar','Apr','<PERSON><PERSON><PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','Aug','Sep','Okt','Nov','Dec'],
		dayNames: ['Nedeľa','Pondelok','<PERSON><PERSON><PERSON>','<PERSON><PERSON>a','<PERSON>tvrt<PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>'],
		dayNamesShort: ['<PERSON>','Pon','U<PERSON>','Str','Štv','<PERSON><PERSON>','Sob'],
		dayNamesMin: ['Ne','Po','Ut','St','Št','Pia','So'],
		weekHeader: 'Ty',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['sk']);
});
