/**
 * 获取域名
 * @returns 
 */
export const getDomain = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/getDomain").then(response => {
      resolve(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 获取模板信息
 * @param {*} data 
 * @param {number} data.id 模版id（编辑的时候需要传） 可选
 * @param {number} data.protoId 原型id 必填
 * @returns 
 */
export const getScTemplateInfo = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/getScTemplateInfo", {
      params: data
    }).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 获取配置信息
 * @returns 
 */
export const getTemplateConfInfo = () => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/getConfInfo").then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 设置模板信息
 * @param {*} data 
 * @returns 
 */
export const setScTemplateInfo = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/template/setScTemplateInfo", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 设置模板状态
 * @param {*} data 
 * @param {number} data.id 模版id
 * @param {number} data.status 状态：0-已上架、1-未上架、2-删除
 * @returns 
 */
export const setScTemplateStatus = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/setScTemplateStatus", {
      params: data
    }).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 获取模板列表
 * @param {*} data 
 * @param {number} data.type 类型：0-视频、1-图文
 * @param {string} data.keyword 关键字
 * @param {number} data.status 状态：0-已上架、1-未上架、2-删除
 * @param {string} data.createTimeStart 创建时间开始
 * @param {string} data.createTimeEnd 创建时间结束
 * @param {number} data.pageNo 页码
 * @param {number} data.pageLimit 每页条数
 * @returns 
 */
export const getScTemplateList = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.post("/api/template/getScTemplateList", data).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};

/**
 * 复制模板
 * @param {*} data 
 * @param {number} data.id 模版id
 * @returns 
 */
export const copyScTemplate = (data) => {
  return new Promise((resolve, reject) => {
    Vue.http.get("/api/template/copyScTemplate", {params: data}).then(response => {
      resolve(response.body);
      console.log(response.body);
    }, error => {
      console.log(error);
      reject(error);
    });
  });
};
