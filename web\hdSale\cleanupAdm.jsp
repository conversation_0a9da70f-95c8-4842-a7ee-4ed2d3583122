<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(Session.getSid() != 753 && !Web.getDebug()){out.println("没有权限");return;}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-cleanupAdm">

		<!--查询条件 start-->
		<div class="fai-cleanupAdm-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<div>
				<el-form-item label="主表">
					<el-select v-model="form.mainTable">
						<el-option v-for="select in mainTable" :label="select.name" :value="select.name" :key="select.name"></el-option>
					</el-select>
					 <el-input v-model="form.mainSql" placeholder="请输入主表sql"></el-input>
				</el-form-item>
				</div>
				<div>
				<el-form-item label="副表">
					<template>
						<el-checkbox  v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
						<div style="margin: 15px 0;"></div>
						<el-checkbox-group v-model="checkedTables" @change="handleCheckedCitiesChange">
							<el-checkbox v-for="table in tables" :label="table" :key="table.name">{{table}}

							</el-checkbox>
						</el-checkbox-group>
					</template>
				</el-form-item>
				</div>

				<el-form-item label="其他信息">
					<template>
						<el-checkbox  v-model="checkOtherAll" @change="handleCheckAllOther">全选</el-checkbox>
						<div style="margin: 15px 0;"></div>
						<el-checkbox-group v-model="checkedOthers" @change="handleCheckedOtherChange">
							<el-checkbox v-for="table in otherInfo" :label="table" :key="table.name">{{table}}
							</el-checkbox>
						</el-checkbox-group>
					</template>
				</el-form-item>

				<div>
				<el-form-item>
					<el-button type="primary"  @click="onSubmit('select')">查询</el-button>
					<el-button type="primary"  @click="onSubmit('export')">导出</el-button>
				</el-form-item>
				</div>
				<div>
					<el-input v-model="rollRecord" placeholder="请输入需要回滚的sql"></el-input>
					<el-form-item><el-button type="primary"  @click="rollDel">回滚</el-button></el-form-item>
				</div>

				<el-form-item label="单表查询导出">
					<el-select v-model="form.oneTable">
						<el-option v-for="select in tables" :label="select" :value="select" :key="select"></el-option>
					</el-select>
						<el-input v-model="form.oneSql" placeholder="请输入主表sql"></el-input>
						<el-input v-model="form.cellKey" placeholder="请输入需要导出字段"></el-input>
				</el-form-item>
				<el-form-item>
					<el-button type="primary"  @click="oneSql('select')">sql查询</el-button>
					<el-button type="primary"  @click="oneSql('export')">sql导出</el-button>
				</el-form-item>
				<br>
				<el-form-item label="执行分配方式">
						<el-input v-model="form.runIdList" placeholder=""></el-input>
					</el-form-item>
					<el-form-item>
						<el-button type="primary"  @click="runAlot()">执行</el-button>
					</el-form-item>

			</el-form>
		</div>
		<!--查询条件 end-->

	</body>


	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-cleanupAdm-search',
			data: {
				mainTable: [
					{name:"hd-hdProf",dataBase:"hd"},
					{name:"hd-hdGame",dataBase:"hd"},
					{name:"bssMain-acctStatus",dataBase:"actStatus"}
				],
				checkAll:false,
				checkOtherAll:false,
				checkedTables:[],
				checkedOthers:[],
				rollRecord:'',
				tables: [
					"hd-hdProf",
					"hd-hdGame",
					"bssMain-acctStatus",
					"hd-hdStore",
					"bssMain-ta",
					"bssMain-taGroup"
				],
				otherInfo:[
					"loginTime",
					"publishGame",
					"createGame",
					"ta",
				],
				form: {//这里是为了填充默认值
					mainTable: '',
					mainSql: '',
					oneSql: '',
					oneTable: '',
					cellKey: '',
					runIdList:'',
				},
			},
			created:function(){
			},
			methods: {
				onSubmit(val) {
					var data = {
							"mainTable":this.form.mainTable,
							"mainSql":this.form.mainSql,
							"tables":JSON.stringify(this.checkedTables),
							"others":JSON.stringify(this.checkedOthers),
							"type":val,
					};
					if("export" === val){
						window.open("/ajax/hdSale_h.jsp?cmd=admOpt" + Fai.tool.parseJsonToUrlParam( data));
					}else{
						Fai.http.post("hdSale_h.jsp?cmd=admOpt",data, false).then(result => {
								if(result.success){
									this.$message({showClose: true, type: 'success', message: result.msg});
								}else{
									this.$message({showClose: true, type: 'success', message: "系统错误"});
								}
						});
					}
				},
				runAlot(){
					console.info(this.form.runIdList);
					Fai.http.post("hdSale_h.jsp?cmd=runAllot",{"runIdList":this.form.runIdList}, false).then(result => {
						if(result.success){
							this.$message({showClose: true, type: 'success', message: result.msg});
						}
					});
				},
				oneSql(val) {
					var data = {
							"oneSql":this.form.oneSql,
							"oneTable":this.form.oneTable,
							"type":val,
							"cellKey": this.form.cellKey,
					};
					if("export" === val){
						window.open("/ajax/hdSale_h.jsp?cmd=admOptExportByOneSql" + Fai.tool.parseJsonToUrlParam( data));
					}else{
						Fai.http.post("hdSale_h.jsp?cmd=admOptExportByOneSql",data, false).then(result => {
								if(result.success){
									this.$message({showClose: true, type: 'success', message: result.msg});
								}else{
									//Fai.http.post已经封装了错误提示
								}
						});
					}
				},
				rollDel(){
					var data = {
						"rollRecord":this.rollRecord,
					};
					Fai.http.post("hdSale_h.jsp?cmd=rollDel",data, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}else{
								//Fai.http.post已经封装了错误提示
							}
					});
				},
				handleCheckAllChange(val){
					console.info(val);
					this.checkedTables = val ? this.tables : [];
					console.info(this.checkedTables);
				},
				handleCheckedCitiesChange(value){
					let checkedCount = value.length;
					this.checkAll = checkedCount === this.tables.length;
					this.isIndeterminate = checkedCount > 0 && checkedCount < this.tables.length;
				},
				handleCheckAllOther(val){
					console.info(val);
					this.checkedOthers = val ? this.otherInfo : [];
					console.info(this.checkedOthers);
				},
				handleCheckedOtherChange(value){
					let checkedCount = value.length;
					this.checkAll = checkedCount === this.tables.length;
					this.isIndeterminate = checkedCount > 0 && checkedCount < this.tables.length;
				},
		    }
		});


	</script>

</html>



