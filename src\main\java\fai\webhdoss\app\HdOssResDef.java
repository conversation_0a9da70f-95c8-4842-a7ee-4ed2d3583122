package fai.webhdoss.app;

import fai.comm.util.FaiList;
import fai.comm.util.Log;
import fai.comm.util.Param;
import fai.web.Request;
import fai.web.Web;

public class HdOssResDef {
	
	public static String getResPath(String key) {
		FaiList<Param> resList = getResList();
		String resPath = "{error: key=" + key + "};";
		for (Param resInfo : resList) {
			// 跳过空的
			if (resInfo == null || resInfo.isEmpty()) {
				continue;
			}
			
			// 找到key
			String resKey = resInfo.getString(ResConfDef.ResList.KEY);
			if (resKey == null || resKey.isEmpty()) {
				Log.logDbg("res key not found. key=%s", key);
				continue;
			}
			
			if (resKey.equals(key)) {
				// debug模式
				if (m_resDebug) {
					resPath = resInfo.getString(ResConfDef.ResList.DEBUG);
				} else {
					resPath = resInfo.getString(ResConfDef.ResList.NORMAL);
				}
				
				// 跳出
				break;
			}
		}
		
		return m_resRoot + resPath;
	}
	
	/**
	 * getResList
	 * @return (FaiList<Param>)
	 */
	public static FaiList<Param> getResList() {
		// 配置信息
		Param initInfo = getConf();
		
		// 资源列表
		FaiList<Param> resList = initInfo.getList(ResConfDef.RES_LIST);
		
		return resList;
	}
	
	/**
	 * 生成返回配置
	 * @return (Param)
	 */
	private static Param getConf() {
		// 配置文件
		Param initInfo = Web.getConf(ResConfDef.RES_CONF_KEY);
		if (initInfo == null) {
			Log.logErr("init res conf error!");
			return null;
		}
		
		// 资源列表
		FaiList<Param> resList = initInfo.getList(ResConfDef.RES_LIST);
		if(resList == null) {
			Log.logErr("init resList error!");
			return null;
		}
		
		// 设置resRoot路径
		try {
//			m_resRoot = FileStg.getFaiOptOssResRoot();
			m_resRoot = (Web.isDev()||Web.isPre() ? "http" : "https") + "://hdoss." + Web.getFaiSysDomain();;
		} catch (Exception e) {
			Log.logErr("init m_resRoot error!");
		}
		
		return initInfo;
	}
	
	private static class ResConfDef {
		private static final String RES_CONF_KEY = "hdOssRes";
		private static final String RES_LIST = "resList";
		private static class ResList {
			private static final String KEY = "k";
			private static final String DEBUG = "d";
			private static final String NORMAL = "n";
		}
	}
	
	private static boolean m_resDebug = Web.getDebug();
	private static String m_resRoot = "";
	
}
