<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.webhdoss.*" %>
<%
	boolean authHdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
	String domain = WebHdOss.getDomainUrl();
	boolean isFromCrm = Parser.parseBoolean(request.getParameter("_formCrm"), false);
	String crmDomain = "http://crm."+Web.getHomeDomain();

%>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>不购买原因</title>
        <%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<%--<script src="<%=HdOssResDef.getResPath("js_jquery_core")%>" type="text/javascript" charset="utf-8"></script>
		<script src="<%=HdOssResDef.getResPath("js_jquery_ui")%>" type="text/javascript" charset="utf-8"></script>--%>
		<script src="<%=HdOssResDef.getResPath("js_jquery_min")%>" type="text/javascript" charset="utf-8"></script>
    </head>
    <body class="saleBox">
		<div style="width: 100%">
			<div class="reasonBox">
				<p style="margin:10px;width: auto !important;height:50px;font-size: 14px;color: #606266;">
					<span v-if=info.show>
						<span style="cursor: pointer;text-decoration: underline;color: #666666;" type="primary" size="mini" :onClick="'focusMethod()'">{{focus}}</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<span style="cursor: pointer;text-decoration: underline;color: #666666;" type="primary" size="mini" :onClick="'banSendMessage()'">{{banMsg}}</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</span>

					互动：    标记(意向)：<span style="cursor: pointer;text-decoration: underline;color: #666666;margin-right:30px;" type="primary" size="mini" :onClick="'showParentBox()'">{{hdIntent}}</span>
					归属库：<span style="text-decoration: underline;color: blue;" type="primary" size="mini">{{statusStr}}</span>&nbsp;&nbsp;&nbsp;&nbsp;
					<!-- <el-button @click.native.prevent="turnStatus('historian')" type="text" size="small">转入A库</el-button>
					<el-button @click.native.prevent="turnStatus('invalid')" type="text" size="small">转入B库</el-button> -->

                    <%--status: 0隐藏库、1个人库/当前库/当天任务、2成交库、3A库/个人活跃库、4B库/长期库、5推荐库、6公共库、7虚拟销售库、8无效库、9优质资源库、10无账号库、11资源库--%>
					<el-button v-if="isFromCrm && (status == 1 || status == 4)" @click.native.prevent="crmTurnStatus('changeToA')" type="text" size="small">转入A库</el-button>&nbsp;&nbsp;&nbsp;&nbsp;
                    <el-button v-if="isFromCrm && (status == 1 || status == 3)" @click.native.prevent="crmTurnStatus('changeToB')" type="text" size="small">转入B库</el-button>&nbsp;&nbsp;&nbsp;&nbsp;
					<%--<el-button v-if="isFromCrm && status != 8 && status != 5 && status != 0" :onClick="'transToInvalid()'" type="text" size="small">转入无效库</el-button>&nbsp;&nbsp;--%>

					<el-button v-if="!isFromCrm" @click.native.prevent="turnStatus('historian')" type="text" size="small">转入A库</el-button>&nbsp;&nbsp;&nbsp;&nbsp;
					<el-button v-if="!isFromCrm" @click.native.prevent="turnStatus('invalid')" type="text" size="small">转入B库</el-button>&nbsp;&nbsp;&nbsp;&nbsp;
                    <!-- <el-button @click.native.prevent="batchReceivePreSale()" type="text" size="small"><font style="color:red;font-weight:bold;text-decoration:underline;">公海库领取</font></el-button> -->
                    <br/>
					<span v-if=info.show>手机号：{{info.mobile}}</span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span v-if=info.show>注册邮箱：{{info.email}}</span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span v-if=info.show>验证邮箱：{{info.verifyEmail}}</span>
				</p>

			</div>

		</div>
	<script type="text/javascript">
		document.domain = "<%=Web.getPortalDomain()%>";
		var url = location.search; //获取url中"?"符后的字串
		var theRequest = new Object();
		if (url.indexOf("?") != -1) {
			var str = url.substr(1);
			strs = str.split("&");
			for(var i = 0; i < strs.length; i ++) {
				theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
			}
		}
		var aid = theRequest.aid;
		var acct = "<%=WebOss.getSacct()%>";
		var reasonBox = new Vue({
			el:'.reasonBox',
			data:{
				hdIntent:"-",
				info:{
					show:false,
					mobile:'',
					email:'',
					verifyEmail:''
				},
				status: undefined,
				statusStr: "未知库",
				focus:'重点关注',
				banMsg:'禁止发送短信',
				isFromCrm: <%=isFromCrm%>
			},
			created:function(){
				getHdInfo(aid);
			},

			methods: {
				turnStatus(type){
					let lib = (type =='historian')?'B':'A';
						var arg = {
							"cmd":'turnStatus',
							"aid":aid,
							"salesAcct":acct,
							'type':type,
						}
						Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
							this.$message({
							type: 'success',
							message: response.data.msg
							});
							this.getDataListByArgs();
						}, response => {
							this.$message({
							type: 'warning',
							message: '系统错误!'
							});
						});
				},
				//TODO 复用js代码
				batchReceivePreSale(){
					var arg = {
						"cmd":"batchReceivePreSale",
						"aid":aid,
						"status":'8',
					}
					console.log("aidList = "+arg.aidList);
					Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
						if(response.data.success){
							this.$message({
								type: 'success',
								message: response.data.msg
							});
							//this.getDataListByArgs();
						}else{
							this.$message({
								type: 'warning',
								message: response.data.msg,
							});
						}
					}, response => {
						this.$message({
						type: 'warning',
						message: '系统错误!'
						});
					});

				},
				crmTurnStatus: function (type) {
					var that = this;
					$.ajax({
						type: "get",
						external: true,
						url: '<%=crmDomain%>/ajax/customer_h.jsp?cmd=batchTurnStatus&callBack=jsonpCallBack',
						data: {
							aidList: "[" + aid + "]",
							type: type,
							status: that.status,
							department: 0
						},
						dataType: "jsonp",
					});
				},
				jsonpResult: function (type, msg) {
					this.$message({
						type: type,
						message: msg
					});
				}
			}
		});

		function jsonpCallBack(result) {
			console.log("jsonpCallBack", result);
			if(result.success){
				reasonBox.jsonpResult("success", result.msg);
                getHdInfo(aid);
			}else {
				reasonBox.jsonpResult('warning', result.msg)
			}
		}


		function getHdInfo(aid){
			var cmd = "";
			if(<%=isFromCrm%>){
				cmd = "getCrmHdInfo";
			}else{
				cmd = "getHdInfo";
			}
			Fai.http.post("hdSale_h.jsp", { aid: aid, cmd : cmd}, false).then(result => {
				if(result.success){
					var list = result.data;
					console.log(list);
					reasonBox.status = list[0].status;
					reasonBox.statusStr = list[0].statusStr;
					reasonBox.hdIntent = list[0].intentName;
					reasonBox.info = list[1];
					if(list[0].isFocus){
						reasonBox.focus = "取消关注"
					}else{
						reasonBox.focus = "重点关注"
					}

					if(list[0].banMsg){
						reasonBox.banMsg = "允许发送短信"
					}else{
						reasonBox.banMsg = "禁止发送短信"
					}
				}
			});
		}

		//关注or取关aid
		function focusMethod() {
			var cmd = "focus";
			if(<%=isFromCrm%>){
				cmd = "focus4Crm";
			}
			Fai.http.post("external/corp_h.jsp", { aid: aid,cmd : cmd}, false).then(result => {
				if(result.success){
					if(reasonBox.focus == "重点关注"){
						reasonBox.focus = "取消关注";
					}else{
						reasonBox.focus = "重点关注";
					}
				}
			});
		}
		//禁止发送短信
		function banSendMessage() {
			var cmd = "banSendMessage";
			if(<%= isFromCrm%>){
				cmd = "banSendMessage4Crm";
			}
			Fai.http.post("external/corp_h.jsp", { aid: aid,cmd : cmd}, false).then(result => {
				if(result.success){
					if(reasonBox.banMsg == "禁止发送短信"){
						reasonBox.banMsg = "允许发送短信";
					}else{
						reasonBox.banMsg = "禁止发送短信";
					}
				}
			});
		}

		/* //转入A、B库
		function turnStatus(type){
			let lib = (type =='historian')?'B':'A';
			this.$confirm('确定将该客户转入'+lib+'库?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":'turnStatus',
					"aid":aid,
					"salesAcct":acct,
					'type':type,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					this.$message({
					type: 'success',
					message: response.data.msg
					});
					this.getDataListByArgs();
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});

			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});
			  });
		} */


		function showParentBox(){
			// 给父页面传递参数
			var param = {};
			param.src = '<%=crmDomain%>'+"/page/signBox.jsp?department=0&aid="+aid;
			param.title = "互动销售信息";
			param.width = "800px";
			param.height = "550px";
			Fai.iframe.setWindowName(param);
			window.parent.showParentBox(theRequest.iframeId);
		}

		function transToInvalid() {
			// 给父页面传递参数
			var param = {};
			param.src = '<%=domain%>'+"/hdSale/transToInvalid.jsp?isIframe=true&aid="+aid+"&_formCrm=<%=isFromCrm %>";
			param.title = "转入无效库";
			param.width = "596px";
			param.height = "300px";
			Fai.iframe.setWindowName(param);
			window.parent.showParentBox(theRequest.iframeId);
		}
    	</script>
 	</body>
</html>
