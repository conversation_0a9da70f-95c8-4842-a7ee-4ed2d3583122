/* Albanian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
jQuery(function($){
	$.datepicker.regional['sq'] = {
		closeText: 'mbylle',
		prevText: '&#x3c;mbrapa',
		nextText: '<PERSON><PERSON><PERSON><PERSON>&#x3e;',
		currentText: 'sot',
		monthNames: ['<PERSON><PERSON>','<PERSON>h<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON><PERSON>',
		'<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>'],
		monthNamesShort: ['<PERSON>','Shk','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>',
		'<PERSON><PERSON>','<PERSON>','<PERSON>ht','Tet','Nën','Dhj'],
		dayNames: ['<PERSON>l','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>','<PERSON>'],
		dayNamesShort: ['<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>','<PERSON><PERSON>','<PERSON>h'],
		dayNamesMin: ['<PERSON>','<PERSON><PERSON>','Ma','Më','En','Pr','Sh'],
		weekHeader: 'Ja',
		dateFormat: 'dd.mm.yy',
		firstDay: 1,
		isRTL: false,
		showMonthAfterYear: false,
		yearSuffix: ''};
	$.datepicker.setDefaults($.datepicker.regional['sq']);
});
