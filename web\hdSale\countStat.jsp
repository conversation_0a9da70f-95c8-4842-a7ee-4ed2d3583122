<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%
if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
	out.println("没有权限");
	return;
}

if(!Web.getDebug() && WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE) && WebOss.getSid() != 877){
    out.println("请到新销售系统进行操作");
    return;
} 

%>


<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>领取数量查询</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-countStat">
		<!--页面顶部标题-->
		<!-- <div style="text-align: center;margin-bottom: 10px;">
			<b style="font-size: 20px;">互动销售领取情况</b>
		</div> -->

		<!--查询条件 start-->
		<div class="fai-countStat-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
			    <!-- <el-form-item label="操作日期(领取or释放)">
					<el-date-picker class="fai-date" v-model="form.dateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.dateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item> -->
			    <el-form-item label="领取时间">
			    	<!-- <el-checkbox v-model="form.receiveDateFlag"></el-checkbox> -->
					<el-date-picker class="fai-date" v-model="form.receiveDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.receiveDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
				<el-form-item label="销售">
					<el-select v-model="form.se_sacct" filterable>
						<el-option label="所有" value="all"></el-option>
						<el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sacct" :key="preSale.sacct"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
					<el-button v-if="this.auth.authAdm" icon="el-icon-download" type="primary"  @click="exportExcel">导出资源明细</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="fai-countStat-list" v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border show-summary style="width: 100%" height="750">
				<el-table-column label="销售" width="200px" fixed>
					<template slot-scope="scope">
				        <span>{{scope.row.saleName}} ({{scope.row.acct}})</span>
					</template>
				</el-table-column width="200px"><!--fixed:固定-->
				<el-table-column width="100px" label="平均分配资源" prop="averageCount"></el-table-column>
				<el-table-column width="100px" label="奖励资源分配" prop="awardCount" ></el-table-column>
				<el-table-column width="100px" label="系统分配" prop="systemCount"></el-table-column>

				<el-table-column width="100px" label="公海库领取" prop="ghCount" ></el-table-column>

				<el-table-column width="100px" label="管理员审批" prop="approveCount"></el-table-column>
				<el-table-column width="100px" label="续费资源" prop="renewCount" ></el-table-column>

				<el-table-column width="100px" label="资源合计" prop="allCount" ></el-table-column>

				<el-table-column width="100px" label="互动资源" prop="hdCount"></el-table-column>
				<el-table-column width="100px" label="助手资源" prop="mpCount" ></el-table-column>
				<el-table-column width="100px" label="悦客资源" prop="ykCount" ></el-table-column>
				
			</el-table>
		</div>
		
		<!--数据表格 end-->

	</body>


	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-countStat-search',
			data: {
				form: {//这里是为了填充默认值
					// dateBeg: Fai.tool.dateFormatter(new Date()),
					// dateEnd: Fai.tool.dateFormatter(new Date()),
					// receiveDateFlag: true,
					receiveDateBeg: Fai.tool.dateFormatter(new Date()),
					receiveDateEnd: Fai.tool.dateFormatter(new Date()),
					se_sacct: "all",
					business:-1,
					
				},
				preSaleList: [],
				editable: false,
				clearable: false,
				business:{
					name:"全部",
					value:-1,
					labelList:[
						{name:"全部",label:-1},
						{name:"互动",label:0},
						{name:"悦客",label:1},
						{name:"公众号助手",label:2},
					],
				},
				excel: false
			},
			created:function(){
				Fai.http.post("hdSale_h.jsp?cmd=getPreSale", "", false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
				getDataList(this.form);
			},
			methods: {
				onSubmit() {
					console.log(this.form);
					getDataList(this.form);
				},
				exportExcel(){
					this.form.excel = true;
					window.open("/ajax/hdSale_h.jsp?cmd=getCountStatList" + Fai.tool.parseJsonToUrlParam(this.form));
				},
		    }
		});
		
		var faiDataList = new Vue({
			el: '.fai-countStat-list',
			data: {
				tableData: [],
			},
			created:function(){
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
		    }
		});
        
		// 获取数据
		function getDataList(urlParam){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getCountStatList", urlParam, false).then(result => {
				if(result.success){
					faiDataList.tableData = result.dataList;
				}
			});
		}

	</script>

</html>



