<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebOss.checkSession(response)){return;}%>
<%
	if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL) && !WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){
		out.println("没有权限");return;
	}
%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>权限设置</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-staff">
		<div id="auth-conf-app"></div>
	</body>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_authConf")%>"></script>

</html>



