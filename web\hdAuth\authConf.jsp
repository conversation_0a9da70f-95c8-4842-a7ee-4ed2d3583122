<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebOss.checkSession(response)){return;}%>
<%
	if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL) && !WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){
		out.println("没有权限");return;
	}
%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>权限设置</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-staff">
		<!--数据表格 start-->
		<div class="fai-staff-list" v-cloak>
			<div style="margin: 10px;padding: 5px;">
				<span>搜索员工：<el-input class="short-input" v-model="staff"  placeholder="IT系统员工英文名/中文名"></el-input></span>
				<el-button type="primary" size="small"  @click="getDataList()" style="display: inline;">查询</el-button>
			</div>
			<div style="margin: 10px;padding: 5px;">
				新增员工：<el-input v-model="newStaff" placeholder="IT系统员工英文名"></el-input>
				<el-button type="primary" size="small"  @click="addNewStaff">新增</el-button>
			</div>
			<el-table :data="tableData" row-key="rowkey" stripe border  >
				<el-table-column label="姓名" prop="name"></el-table-column>
				<el-table-column label="账号" prop="sacct" ></el-table-column>
				<el-table-column label="操作" v-if="this.auth.hdSaleManage && this.auth.authAdm  " type="index" width="530"   fixed="right">
					<template slot-scope="scope">
						<el-button  type="" size="mini">adm</el-button>
						<el-checkbox  v-model="scope.row.all"  @change="setAuth(scope.row.sacct,'all',scope.row.auth,scope.row.sid,scope.row.all)"></el-checkbox>
						<el-button type="" size="mini" >互动销售管理</el-button>
						<el-checkbox v-model="scope.row.hdSaleManage" @change="setAuth(scope.row.sacct,'hdSaleManage',scope.row.auth,scope.row.sid,scope.row.hdSaleManage)"></el-checkbox>
						<el-button type="" size="mini" >互动销售组长</el-button>
						<el-checkbox v-model="scope.row.hdSaleGroupLeader" @change="setAuth(scope.row.sacct,'hdSaleGroupLeader',scope.row.auth,scope.row.sid,scope.row.hdSaleGroupLeader)"></el-checkbox>
						<el-button type="" size="mini" >互动销售</el-button>
						<el-checkbox v-model="scope.row.hdSale" @change="setAuth(scope.row.sacct,'hdSale',scope.row.auth,scope.row.sid,scope.row.hdSale)"></el-checkbox>
					</template>
				</el-table-column>
				<!--fixed:固定-->
			</el-table>
			<div class="block">
					<el-pagination  @current-change="getDataList()" :current-page.sync="pageData.currentPage" 
						:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
					</el-pagination>
			</div>
		</div>
		
		<!--数据表格 end-->

	</body>


	<script type="text/javascript">
		var faiDataList = new Vue({
			el: '.fai-staff-list',
			data: {
				tableData: [],
				pageData:{
					size:10,
					total:0,
					currentPage:1
				},
				staff:'',
				newStaff:'',
			},
			created:function(){
				this.getDataList();//进入页面加载一次数据！
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
				setAuth(acct,type,updateFlag,sid,checked){
					console.info(acct,type,updateFlag,sid,checked);
					this.$confirm('确定要设置该账号 '+acct+' 权限吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"setStaffAuth", "acct":acct,"type":type,"updateFlag":updateFlag,"sid":sid,"checked":checked}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});					
							}
							this.getDataList();//操作成功刷新数据表格
							
						});
					}).catch(() => { });
				},
				getDataList(){
					console.info(this.pageData);
					var arg = {
						"cmd":"getAuthStaffList",
						"size":this.pageData.size,
						"currentPage":this.pageData.currentPage,
						"staff":this.staff,
					};
					Fai.http.post("hdSale_h.jsp?cmd=getAuthStaffList", arg, false).then(result => {
						console.info(result);
						if(result.success){
							if(result.dataList.length == 0){
								this.$message({showClose: true, type: 'error', message: "暂无员工，请先新增员工账号"});		
							}
							faiDataList.tableData = result.dataList;
							this.pageData.total = result.total;
						}
					});
				},
				addNewStaff(){
					if(this.newStaff.trim()==""){
						this.$message({showClose: true, type: 'error', message:"请输入正确账号"});
						return;
					}
					this.$confirm('确定要新增销售 '+this.newStaff+' 吗？', '提示', {
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then(() => {//确定
						Fai.http.post("hdSale_h.jsp", {"cmd":"addHdOssStaff", "acct":this.newStaff}, false).then(result => {
							if(result.success){
								this.$message({showClose: true, type: 'success', message: result.msg});
							}
							getDataList();//操作成功刷新数据表格	
						});
					}).catch(() => { });
				},

						
		    }
		});
	</script>

</html>



