package fai.webhdoss.controller;

import fai.app.*;
import fai.cli.HdGiftCli;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.web.App;
import fai.web.Core;
import fai.web.Session;
import fai.web.inf.HdGift;
import fai.web.inf.Kid;
import fai.web.inf.Staff;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @Description 案例接口
 * @date 2021/3/14 11:36
 */
@RestController
@RequestMapping("/gift")
public class HdGiftController {


    private FaiList<Param> getJdGiftList(SearchArg searchArg, FaiList<String> fields, int agentAid, boolean isOem) throws Exception {
        HdGiftCli cli = new HdGiftCli(Core.getFlow());
        cli.init();
        int rt = Errno.OK;
        FaiList<Param> list = new FaiList<Param>();
        rt = cli.getJdGiftListFromDB(searchArg, fields, list);
        if (rt != Errno.OK && rt != Errno.ARGS_ERROR && rt != Errno.NOT_FOUND) {
            App.logErr("getEntityList err;");
            return list;
        }
        if (list == null) {
            list = new FaiList<Param>();
        }


        return list;
    }

    @GetMapping("/getGiftTypeList")
    public JsonResult getGiftTypeList() throws Exception {

        SearchArg searchArg = new SearchArg();
        FaiList<String> fields = new FaiList<String>();
        fields.add(HdJdGiftDef.Info.TYPE_B);
        fields.add(HdJdGiftDef.Info.FK_NAME);


        FaiList<Param> jdGiftList = getJdGiftList(searchArg, fields, 0, false);
        FaiList<Integer> giftList = HdAssetDef.getGiftList();

        FaiList<HashMap> giftListBak = new FaiList<HashMap>();
        FaiList<HashMap> jdgiftListBak = new FaiList<HashMap>();

        for (Integer type : giftList) {
            HashMap<Integer, String> map = new HashMap<Integer, String>();
            map.put(type, HdAssetDef.getAssetName(type));
            giftListBak.add(map);
        }


        for (Param item : jdGiftList) {

            int tp = item.getInt(HdJdGiftDef.Info.TYPE_B);
            String na = item.getString(HdJdGiftDef.Info.FK_NAME);
            HashMap<Integer, String> map = new HashMap<Integer, String>();
            map.put(tp, na);
            jdgiftListBak.add(map);

        }

        /*HashMap<Integer, String> map10019 = new HashMap<Integer, String>();
        map10019.put(10019, "名创优品面包粒子U型枕(旧)");
        HashMap<Integer, String> map10017 = new HashMap<Integer, String>();
        map10017.put(10017, "苏泊尔养生壶(旧)");
        jdgiftListBak.add(map10019);
        jdgiftListBak.add(map10017);*/

        HashMap<String, FaiList<HashMap>> map = new HashMap<String, FaiList<HashMap>>();
        map.put("giftListBak", giftListBak);
        map.put("jdgiftListBak", jdgiftListBak);
        return JsonResult.ok(map);
    }


    @PostMapping("/clearAsset")
    private String clearAsset(HttpServletRequest request) throws Exception {
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        int giftType = Parser.parseInt(request.getParameter("giftType"), 0);
        //是否清空部分
        boolean isClearPart = Parser.parseBoolean(request.getParameter("isClearPart"), false);
        int clearPartNum = Parser.parseInt(request.getParameter("clearPartNum"), 0);

        if (aid <= 0) {
            return HdGameDef.ErrorInfo.getErrInfo(Errno.ARGS_ERROR, "参数错误");
        }
        if (isClearPart && clearPartNum <= 0) {
            return HdGameDef.ErrorInfo.getErrInfo(Errno.ARGS_ERROR, "参数错误");
        }

        HdGift hdGift = (HdGift) Core.getCorpKit(aid, Kid.HD_GIFT);
        int rt = 0;
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(HdAssetDef.Info.AMOUNT, ParamMatcher.GT, 0);
        if (giftType > 0) {
            searchArg.matcher.and(HdAssetDef.Info.TYPE_B, ParamMatcher.EQ, giftType);
        } else {
            searchArg.matcher.and(HdAssetDef.Info.TYPE_B, ParamMatcher.GE, HdAssetDef.TypeB.CHARGE_20);
            searchArg.matcher.and(HdAssetDef.Info.TYPE_B, ParamMatcher.LE, HdAssetDef.TypeB.CHARGE_100);
        }
        FaiList<Param> assetList = hdGift.getAssetList(0, 0, searchArg);
        HdGiftCli cli = new HdGiftCli(Core.getFlow());
        cli.init();
        for (Param item : assetList) {
            int typeB = item.getInt(HdAssetDef.Info.TYPE_B, 0);
            int amount = item.getInt(HdAssetDef.Info.AMOUNT, 0);
            rt = hdGift.refundGift(typeB, isClearPart ? clearPartNum : amount, 3);
            if (rt != Errno.OK) {
                Log.logErr("refund asset err; aid=%s, typeB=%s, amount=%s", aid, typeB, amount);
                return HdGameDef.ErrorInfo.getErrInfo(rt, "操作失败！");
            }
            //addDeleteFlowL(int aid, int type, int num, int cid, int sid, int isAll)
            int cid = Session.getCid();
            int sid = Session.getSid();
            int rt2 = cli.addDeleteFlowL(aid, typeB, isClearPart ? clearPartNum : amount, cid, sid, isClearPart ? 0 : 1);
            Log.logStd(rt, "addDeleteFlowL cid=%s sid=%s aid=%s isClearPart=%s clearPartNum=%s  amount=%s", cid, sid, aid, isClearPart, clearPartNum, amount);
        }

        return HdGameDef.ErrorInfo.getErrInfo(rt);
    }


    @GetMapping("/getDeleteFlowList")
    private String getDeleteFlowList(HttpServletRequest request) throws Exception {

        int aid = Core.getAid();
        int cid = Session.getCid();
        int sid = Session.getSid();
        HdGiftCli cli = new HdGiftCli(Core.getFlow());
        cli.init();
        SearchArg searchArg = new SearchArg();
        searchArg.totalSize = new Ref<Integer>();
        searchArg.matcher = new ParamMatcher(HdAssetFlowDef.DeleteInfo.CID, ParamMatcher.EQ, cid);
        searchArg.matcher.and(HdAssetFlowDef.DeleteInfo.SID, ParamMatcher.EQ, sid);
        FaiList<Param> list = new FaiList<Param>();
        int rt = cli.getDeleteFlowList(Core.getAid(), searchArg, list, null);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            App.logErr(rt, "getAssetList error;aid=%d", aid);
        }


        Param result = new Param();
        if (list == null) {
            result.setInt("rt", Errno.ERROR);
            result.setBoolean("success", false);
            result.setList("list", new FaiList<Param>());
            return result.toJson();
        }

        FaiList<Param> data = new FaiList<Param>();
        for (Param item : list) {

            int typeB = item.getInt(HdAssetFlowDef.DeleteInfo.TYPE);
            String typeBname = HdAssetDef.getAssetName(typeB);

            item.setString("typeBname", typeBname);

            int cidP = item.getInt(HdAssetFlowDef.DeleteInfo.CID);
            int sidP = item.getInt(HdAssetFlowDef.DeleteInfo.SID);
            String staffLabel = getStaffLabel(cidP, sidP);
            item.setString("staffLabel", staffLabel);
        }

        result.setInt("rt", Errno.OK);
        result.setList("list", list);
        result.setBoolean("success", true);
        return result.toJson();
    }

    @GetMapping("/exprotDeleteFlowList")
    private void exprotDeleteFlowList(HttpServletRequest request, HttpServletResponse response) throws Exception {

        int aid = Core.getAid();
        HdGiftCli cli = new HdGiftCli(Core.getFlow());
        cli.init();
        SearchArg searchArg = new SearchArg();
        searchArg.totalSize = new Ref<>();
        FaiList<Param> list = new FaiList<>();
        int rt = cli.getDeleteFlowList(Core.getAid(), searchArg, list, null);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            App.logErr(rt, "getAssetList error;aid=%d", aid);
        }
        Log.logStd("list = %s", list);
        FaiList<Param> colList = new FaiList<Param>();
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY, HdAssetFlowDef.DeleteInfo.AID );
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY, HdAssetFlowDef.DeleteInfo.TYPE );
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY, HdAssetFlowDef.DeleteInfo.ISALL);
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY,HdAssetFlowDef.DeleteInfo.NUM );
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY, HdAssetFlowDef.DeleteInfo.SID );
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }
        {
            Param k = new Param();
            k.setString( MSOfficeConverter.Col.KEY,HdAssetFlowDef.DeleteInfo.CREATE_TIME );
            k.setInt( MSOfficeConverter.Col.WIDTH,20 );
            colList.add( k );
        }

        FaiList<Param> rowList = new FaiList<Param>();
        Param p = new Param();
        p.setString(HdAssetFlowDef.DeleteInfo.AID  , "aid");
        p.setString(HdAssetFlowDef.DeleteInfo.TYPE, "礼品名称");
        p.setString(HdAssetFlowDef.DeleteInfo.ISALL, "是否全部");
        p.setString(HdAssetFlowDef.DeleteInfo.NUM , "数量");
        p.setString(HdAssetFlowDef.DeleteInfo.SID , "操作人");
        p.setString(HdAssetFlowDef.DeleteInfo.CREATE_TIME , "操作时间");
        rowList.add( p );


        for (Param item : list) {
            Param row = new Param();
            int typeB = item.getInt(HdAssetFlowDef.DeleteInfo.TYPE);
            String typeBname = HdAssetDef.getAssetName(typeB);
            int cidP = item.getInt(HdAssetFlowDef.DeleteInfo.CID);
            int sidP = item.getInt(HdAssetFlowDef.DeleteInfo.SID);
            String staffLabel = getStaffLabel(cidP, sidP);

            row.setInt(HdAssetFlowDef.DeleteInfo.AID  , item.getInt(HdAssetFlowDef.DeleteInfo.AID,0));
            row.setString(HdAssetFlowDef.DeleteInfo.TYPE, typeBname);
            row.setString(HdAssetFlowDef.DeleteInfo.ISALL,  item.getInt(HdAssetFlowDef.DeleteInfo.ISALL) == 1? "是":"否");
            row.setInt(HdAssetFlowDef.DeleteInfo.NUM , item.getInt(HdAssetFlowDef.DeleteInfo.NUM,0));
            row.setString(HdAssetFlowDef.DeleteInfo.SID ,staffLabel);
            row.setString(HdAssetFlowDef.DeleteInfo.CREATE_TIME ,Parser.parseDateString(item.getCalendar(HdAssetFlowDef.DeleteInfo.CREATE_TIME)));
            rowList.add( row );
        }

        FaiList<Param> optionList = new FaiList<Param>();
        Param option = new Param();
        option.setString( MSOfficeConverter.Option.SHEET_NAME,"用户列表" );
        option.setList( MSOfficeConverter.Option.COL_LIST,colList );
        option.setList( MSOfficeConverter.Option.ROW_LIST,rowList );
        optionList.add( option );
        String fileName = "清空日志-" + Parser.parseDateString( Calendar.getInstance() );
        dealBeforeConvert( request, response, fileName, optionList );
    }

    public static void dealBeforeConvert (HttpServletRequest request, HttpServletResponse response, String fileName , FaiList<Param> optionList )throws Exception{
        //out.clear();
        if(!Str.isEmpty(fileName) && (!fileName.contains("xls") || optionList.get(0).getList(MSOfficeConverter.Option.ROW_LIST).size() > 65535)){
            fileName += ".xlsx";
        }
        if (optionList.get(0).getList(MSOfficeConverter.Option.ROW_LIST).size() > 65535 && fileName.endsWith(".xls")){
            fileName = fileName.replace(".xls",".xlsx");
        }
        String agent = request.getHeader("User-Agent");
        agent = agent == null ? "" : agent.toUpperCase();
        response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent,fileName) + "\"");
        response.setContentType("application/vnd.ms-excel");
        MSOfficeConverter.excelConverter(response.getOutputStream(),optionList,fileName.endsWith(".xlsx"));
        response.getOutputStream().flush();
        //HdMSOfficeConverter.exportExcelSxssf(response.getOutputStream(), optionList);
        //out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
    }


    private String getStaffLabel(int cid, int sid) throws Exception {
        Staff staff = (Staff) Core.getCorpKit(cid, Kid.STAFF);
        Param info = staff.getStaffInfo(sid);
        String name = info.getString(StaffDef.Info.NAME);
        String sacct = info.getString(StaffDef.Info.SACCT);
        String label = null;
        if (name == null || name.equals(sacct)) {
            label = sacct;
        } else {
            label = name + "<" + sacct + ">";
        }
        return label;
    }

}
