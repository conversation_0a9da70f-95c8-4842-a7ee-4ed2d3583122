package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateVideoVO {
    // 视频资源Id
    private String videoId;

    // 视频资源Id类型
    private int videoType;

    // 首帧图资源Id
    private String coverId;

    // 首帧图资源Id类型
    private int coverType;
}
