<template>
  <el-popover placement="top-start" width="480" trigger="hover">
    <div class="video-example-content">
      <p>
        想挑战自己?想体验攀岩的乐趣?<br />来
        <span data-tag-type="store_name">
          <span class="script-example-tag"> 圆圆攀岩馆 </span>
          <span class="script-example-tag-label" contenteditable="false">
            店铺名称
          </span>
        </span>
        ，让你轻松入坑，一试上瘾!<br />场馆
        <span data-tag-type="store_special">
          <span class="script-example-tag">干净整洁</span>
          <span class="script-example-tag-label" contenteditable="false">
            店铺特色
          </span>
        </span>
        ，环境超棒!<br />
        <span data-tag-type="store_special">
          <span class="script-example-tag"> 交通便利 </span>
          <span class="script-example-tag-label" contenteditable="false">
            店铺名称
          </span>
        </span>
        ，离地铁站近，想爬就爬!<br />专业教学，不管你是新手还是高手，都能找到属于自己的攀登乐趣！<br />我是
        <span data-tag-type="store_founder">
          <span class="script-example-tag"> 圆圆 </span>
          <span class="script-example-tag-label" contenteditable="false">
            创始人
          </span>
        </span>
        ，从
        <span data-tag-type="store_open">
          <span class="script-example-tag"> 2014 </span>
          <span class="script-example-tag-label" contenteditable="false">
            开业年份
          </span>
        </span>
        年开馆到现在，一直希望让更多人体验攀岩的快乐!<br />还等什么?快来挑战你的极限!
      </p>
    </div>
    <!-- <el-button >hover 激活</el-button> -->
    <p class="text-blue-500 cursor-pointer ml-[10px]" slot="reference">
      查看示例
    </p>
  </el-popover>
</template>

<script>
export default {
  name: "ViewScriptExample",
};
</script>

<style lang="scss" scoped>
.video-example-content {
  color: #333333;
  font-size: 14px;
  line-height: 25px;
  background: #f3f3f5;
  padding: 10px;
}
</style>
