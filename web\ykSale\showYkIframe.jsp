<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<%@ page import="fai.webhdoss.*" %>
<%
	boolean authHdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
	String domain = WebHdOss.getDomainUrl();;

	boolean isFromCrm = Parser.parseBoolean(request.getParameter("_formCrm"), false);
	String crmDomain = WebHdOss.getCrmDomain();
%>
<!DOCTYPE html>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>不购买原因</title>
        <%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
    </head>
    <body class="saleBox">
		<div style="width: 100%">
			<div class="reasonBox">
				<p style="margin:10px;width: auto !important;height:50px;font-size: 14px;color: #606266;">
					悦客：
					<span v-if=info.show>
						<span style="cursor: pointer;text-decoration: underline;color: #666666;" type="primary" size="mini" :onClick="'focusMethod()'">{{focus}}</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
						<span style="cursor: pointer;text-decoration: underline;color: #666666;" type="primary" size="mini" :onClick="'banSendMessage()'">{{banMsg}}</span>
						&nbsp;&nbsp;&nbsp;&nbsp;
					</span>

					标记(意向)：<span style="cursor: pointer;text-decoration: underline;color: #666666;margin-right:30px;" type="primary" size="mini" :onClick="'showParentBox()'">{{ykIntent}}</span>
					归属库：<span style="text-decoration: underline;color: blue;" type="primary" size="mini">{{status}}</span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span v-if=info.show>手机号：{{info.mobile}}</span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<br/>
					<span v-if=info.show>注册邮箱：{{info.email}}</span>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span v-if=info.show>验证邮箱：{{info.verifyEmail}}</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </p>

			</div>

		</div>
	<script type="text/javascript">
		document.domain = "<%=Web.getPortalDomain()%>";
		var url = location.search; //获取url中"?"符后的字串
		var theRequest = new Object();
		if (url.indexOf("?") != -1) {
			var str = url.substr(1);
			strs = str.split("&");
			for(var i = 0; i < strs.length; i ++) {
				theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
			}
		}
		var aid = theRequest.aid;
		var acct = "<%=WebOss.getSacct()%>";
		var reasonBox = new Vue({
			el:'.reasonBox',
			data:{
				ykIntent:"-",
				info:{
					show:false,
					mobile:'',
					email:'',
					verifyEmail:''
				},
				status: '未知库',
				focus:'重点关注',
				banMsg:'禁止发送短信'
			},
			created:function(){
				getYkInfo(aid);
			},

			methods: {

			}
		});


		function getYkInfo(aid){
			Fai.http.post("ykSale/ykSale_h.jsp?cmd=getYkInfo", { aid: aid, }, false).then(result => {
				if(result.success){
					var list = result.data;
					console.log(list);
					reasonBox.status = list[0].status;
					reasonBox.ykIntent = list[0].intentName;
					reasonBox.info = list[1];
					if(list[0].isFocus){
						reasonBox.focus = "取消关注"
					}else{
						reasonBox.focus = "重点关注"
					}

					if(list[0].banMsg){
						reasonBox.banMsg = "允许发送短信"
					}else{
						reasonBox.banMsg = "禁止发送短信"
					}
				}
			});
		}

		//关注or取关aid
		function focusMethod() {
			Fai.http.post("external/corp_h.jsp?cmd=yk_focus", { aid: aid}, false).then(result => {
				if(result.success){
					if(reasonBox.focus == "重点关注"){
						reasonBox.focus = "取消关注";
					}else{
						reasonBox.focus = "重点关注";
					}
				}
			});
		}
		//禁止发送短信
		function banSendMessage() {
			Fai.http.post("external/corp_h.jsp?cmd=yk_banSendMessage", { aid: aid}, false).then(result => {
				if(result.success){
					if(reasonBox.banMsg == "禁止发送短信"){
						reasonBox.banMsg = "允许发送短信";
					}else{
						reasonBox.banMsg = "禁止发送短信";
					}
				}
			});
		}

		function showParentBox(){
			// 给父页面传递参数
			var param = {};
			param.src = '<%=crmDomain%>' + '/page/signBox.jsp?department=1&aid=' + aid;
			param.title = "悦客销售信息";
			param.width = "596px";
			param.height = "454px";
			Fai.iframe.setWindowName(param);
			window.parent.showParentBox(theRequest.iframeId);
		}
    	</script>
 	</body>
</html>
