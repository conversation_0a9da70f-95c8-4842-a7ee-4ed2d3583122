<template>
  <div class="fai-staff-list" v-cloak>
    <!-- 搜索区域 -->
    <div class="search-section">
      <span>
        搜索员工：
        <el-input 
          class="short-input" 
          v-model="staff" 
          placeholder="IT系统员工英文名/中文名"
          @keyup.enter="getDataList"
        />
      </span>
      <el-button type="primary" size="small" @click="getDataList">
        查询
      </el-button>
    </div>

    <!-- 新增员工区域 -->
    <div class="add-section">
      新增员工：
      <el-input 
        v-model="newStaff" 
        placeholder="IT系统员工英文名"
        @keyup.enter="addNewStaff"
        class="add-input"
      />
      <el-button type="primary" size="small" @click="addNewStaff">
        新增
      </el-button>
    </div>

    <!-- 数据表格 -->
    <el-table 
      :data="tableData" 
      row-key="rowkey" 
      stripe 
      border
      v-loading="loading"
      element-loading-text="加载中..."
    >
      <el-table-column label="姓名" prop="name" />
      <el-table-column label="账号" prop="sacct" />
      <el-table-column 
        label="操作" 
        v-if="hasPermission" 
        width="650" 
        fixed="right"
      >
        <template slot-scope="scope">
          <div class="auth-controls" style="margin-bottom: 10px;">
            <div class="auth-title">全部</div>
            <div class="auth-item">
              <el-button type="" size="mini">adm</el-button>
              <el-checkbox 
                v-model="scope.row.all" 
                @change="setAuth(scope.row.sacct, 'all', scope.row.auth, scope.row.sid, scope.row.all)"
              />
            </div>
          </div>
          <div class="auth-controls" style="margin-bottom: 10px;">
            <div class="auth-title">互动</div>
            <div class="auth-item">
              <el-button type="" size="mini">互动销售管理</el-button>
              <el-checkbox 
                v-model="scope.row.hdSaleManage" 
                @change="setAuth(scope.row.sacct, 'hdSaleManage', scope.row.auth, scope.row.sid, scope.row.hdSaleManage)"
              />
            </div>
            <div class="auth-item">
              <el-button type="" size="mini">互动销售组长</el-button>
              <el-checkbox 
                v-model="scope.row.hdSaleGroupLeader" 
                @change="setAuth(scope.row.sacct, 'hdSaleGroupLeader', scope.row.auth, scope.row.sid, scope.row.hdSaleGroupLeader)"
              />
            </div>
            <div class="auth-item">
              <el-button type="" size="mini">互动销售</el-button>
              <el-checkbox 
                v-model="scope.row.hdSale" 
                @change="setAuth(scope.row.sacct, 'hdSale', scope.row.auth, scope.row.sid, scope.row.hdSale)"
              />
            </div>
          </div>
          <div class="auth-controls">
            <div class="auth-title">速创</div>
            <div class="auth-item">
              <el-button type="" size="mini">速创PM</el-button>
              <el-checkbox 
                v-model="scope.row.scPm" 
                @change="setAuth(scope.row.sacct, 'scPm', scope.row.auth, scope.row.sid, scope.row.scPm)"
              />
            </div>
            <div class="auth-item">
              <el-button type="" size="mini">速创运营</el-button>
              <el-checkbox 
                v-model="scope.row.scOperation" 
                @change="setAuth(scope.row.sacct, 'scOperation', scope.row.auth, scope.row.sid, scope.row.scOperation)"
              />
            </div>
            <div class="auth-item">
              <el-button type="" size="mini">速创开发</el-button>
              <el-checkbox 
                v-model="scope.row.scDevelop" 
                @change="setAuth(scope.row.sacct, 'scDevelop', scope.row.auth, scope.row.sid, scope.row.scDevelop)"
              />
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination 
        @current-change="handleCurrentChange" 
        :current-page.sync="pageData.currentPage" 
        :page-size="pageData.size" 
        layout="total, prev, pager, next, jumper" 
        :total="pageData.total"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuthConf',
  data() {
    return {
      tableData: [],
      pageData: {
        size: 10,
        total: 0,
        currentPage: 1
      },
      staff: '',
      newStaff: '',
      loading: false,
      // 权限控制
      auth: {
        hdSaleManage: true,
        authAdm: true
      }
    }
  },
  computed: {
    hasPermission() {
      return this.auth.hdSaleManage && this.auth.authAdm;
    }
  },
  created() {
    this.getDataList();
  },
  methods: {
    /**
     * 设置员工权限
     */
    setAuth(acct, type, updateFlag, sid, checked) {
      console.info('设置权限参数:', { acct, type, updateFlag, sid, checked });
      
      this.$confirm(`确定要设置该账号 ${acct} 权限吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        
        Fai.http.post("hdSale_h.jsp", {
          cmd: "setStaffAuth",
          acct: acct,
          type: type,
          updateFlag: updateFlag,
          sid: sid,
          checked: checked
        }, false).then(result => {
          this.loading = false;
          
          if (result.success) {
            this.$message({
              showClose: true,
              type: 'success',
              message: result.msg || '权限设置成功'
            });
            this.getDataList();
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: result.msg || '设置权限失败'
            });
          }
        }).catch(error => {
          this.loading = false;
          console.error('设置权限失败:', error);
          this.$message({
            showClose: true,
            type: 'error',
            message: '网络错误，请稍后重试'
          });
        });
      }).catch(() => {
        // 用户取消操作
      });
    },

    /**
     * 获取员工列表数据
     */
    getDataList() {
      console.info('获取数据列表，参数:', this.pageData);
      this.loading = true;
      
      const params = {
        cmd: "getAuthStaffList",
        size: this.pageData.size,
        currentPage: this.pageData.currentPage,
        staff: this.staff.trim()
      };

      Fai.http.post("hdSale_h.jsp?cmd=getAuthStaffList", params, false).then(result => {
        this.loading = false;
        console.info('获取数据结果:', result);
        
        if (result.success) {
          this.tableData = result.dataList || [];
          this.pageData.total = result.total || 0;
          
          if (this.tableData.length === 0) {
            this.$message({
              showClose: true,
              type: 'info',
              message: "暂无员工数据"
            });
          }
        } else {
          this.$message({
            showClose: true,
            type: 'error',
            message: result.msg || '获取数据失败'
          });
        }
      }).catch(error => {
        this.loading = false;
        console.error('获取数据失败:', error);
        this.$message({
          showClose: true,
          type: 'error',
          message: '网络错误，请稍后重试'
        });
      });
    },

    /**
     * 新增员工
     */
    addNewStaff() {
      const staffName = this.newStaff.trim();
      
      if (!staffName) {
        this.$message({
          showClose: true,
          type: 'warning',
          message: "请输入正确的员工账号"
        });
        return;
      }

      this.$confirm(`确定要新增员工 ${staffName} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.loading = true;
        
        Fai.http.post("hdSale_h.jsp", {
          cmd: "addHdOssStaff",
          acct: staffName
        }, false).then(result => {
          this.loading = false;
          
          if (result.success) {
            this.$message({
              showClose: true,
              type: 'success',
              message: result.msg || '新增员工成功'
            });
            this.newStaff = '';
            this.getDataList();
          } else {
            this.$message({
              showClose: true,
              type: 'error',
              message: result.msg || '新增员工失败'
            });
          }
        }).catch(error => {
          this.loading = false;
          console.error('新增员工失败:', error);
          this.$message({
            showClose: true,
            type: 'error',
            message: '网络错误，请稍后重试'
          });
        });
      }).catch(() => {
        // 用户取消操作
      });
    },

    /**
     * 分页改变事件
     */
    handleCurrentChange(page) {
      this.pageData.currentPage = page;
      this.getDataList();
    }
  }
}
</script>

<style lang="scss" scoped>
.fai-staff-list {
  padding: 0 20px 20px; 
  
  .search-section, .add-section {
    margin: 10px 0;
    padding: 10px;
    
    .short-input, .add-input {
      width: 200px;
      margin: 0 10px;
    }
  }

  .auth-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    .auth-title {
      margin-right: 10px;
    }
    .auth-item {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 4px;
    }
  }

  .pagination-section {
    margin-top: 20px;
    text-align: center;
  }
}

/* 深度选择器兼容Element UI样式 */
::v-deep .el-table {
  .el-button--mini {
    margin-right: 4px;
    font-size: 12px;
  }
  
  .el-checkbox {
    margin-right: 10px;
  }
}

::v-deep .el-loading-text {
  color: #409eff;
}
</style>
