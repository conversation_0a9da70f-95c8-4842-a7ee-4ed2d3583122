package fai.webhdoss.controller;

import com.alibaba.fastjson.JSON;
import fai.app.AcctDef;
import fai.app.HdGameDef;
import fai.comm.cache.redis.RedisCacheManager;
import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.hdUtil.JsonResult;
import fai.web.Core;
import fai.web.inf.HdGame;
import fai.web.inf.Kid;
import fai.web.inf.SysAcct;
import fai.webhdoss.WebHdOss;
import fai.webhdoss.model.vo.HdActivityPlanVO;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("test")
public class TestController {//123

    @Autowired
    private RedisCacheManager codis;//465789111

    private final int SEVEN_DAY = 60 * 24 * 7;

    private String getFKey(int planId) {
        String ACTIVITY_PLAN_KEY_PRE = "activity-test";
        return ACTIVITY_PLAN_KEY_PRE + ":" + planId;
    }

    private String getHKey() {
        return "activity-test";
    }

    /**
     * 创建方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @PostMapping("activity-plan")
    public JsonResult saveActivityPlan(@ApiParam("活动方案") @Validated @RequestBody HdActivityPlanVO hdActivityPlanVO) {
        int planId = hdActivityPlanVO.getId();
        String redisKey = getFKey(planId);

        codis.hset(getHKey(), redisKey, JSON.toJSONString(hdActivityPlanVO));
        return JsonResult.success();
    }

    /**
     * 获取方案
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @GetMapping("activity-plan/{planId}")
    public JsonResult getActivityPlan(@ApiParam("方案编号") @PathVariable("planId") int planId) {

        String json = codis.hget(getHKey(), getFKey(planId));
        HdActivityPlanVO hdActivityPlanVO = JSON.parseObject(json, HdActivityPlanVO.class);
        return JsonResult.success(hdActivityPlanVO);
    }


    /**
     * 获取方案列表
     *
     * @param planId
     * @return
     * <AUTHOR>
     */
    @GetMapping("activity-plan")
    public JsonResult getActivityPlanList(@RequestParam("page") int page, @RequestParam("limit") int limit,
                                          @RequestParam("relationFlyerState") int relationFlyerState,
                                          @RequestParam("tradeId") int tradeId) {

        Map<String, String> stringStringMap = codis.hgetAll(getHKey());
        FaiList<HdActivityPlanVO> resultList = new FaiList<HdActivityPlanVO>();
        FaiList<HdActivityPlanVO> planList = new FaiList<HdActivityPlanVO>();

        for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
            resultList.add(JSON.parseObject(entry.getValue(), HdActivityPlanVO.class));
        }
        boolean relateFlyer = relationFlyerState == 1;
        for (HdActivityPlanVO activityPlanVO : resultList) {
            if (relateFlyer && activityPlanVO.getFlyerModId().length() != 0) {
                if (tradeId > 0 && activityPlanVO.getTradeId() == tradeId) {
                    planList.add(activityPlanVO);
                }

                if (tradeId <= 0) {
                    planList.add(activityPlanVO);
                }
            }

            if (!relateFlyer && activityPlanVO.getFlyerModId().length() == 0) {
                if (tradeId > 0 && activityPlanVO.getTradeId() == tradeId) {
                    planList.add(activityPlanVO);
                }

                if (tradeId <= 0) {
                    planList.add(activityPlanVO);
                }
            }
        }
        limit = planList.size() < limit ? planList.size() : limit;

        List<HdActivityPlanVO> finalList = planList.subList(page, limit);
        Param response = new Param();
        response.setList("dataList", new FaiList<HdActivityPlanVO>(finalList));
        response.setInt("total", finalList.size());

        return JsonResult.success(response);
    }


    /**
     * 通过企业账号获取aid和gameId
     *
     * @param acct
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    @GetMapping("aid/{acct}/{gameId}")
    public JsonResult getAidByAcct(@ApiParam("企业账号") @PathVariable("acct") String acct,
                                   @ApiParam("gameId") @PathVariable("gameId") Integer gameId) throws Exception {
        SysAcct sysAcct = (SysAcct) Core.getSysKit(Kid.SYS_ACCT);

        Param acctInfo = sysAcct.getAcctInfo(AcctDef.Atype.CORP, acct);
        Integer aid = acctInfo.getInt(AcctDef.Info.AID, 0);

        HdGame hdGame = (HdGame) WebHdOss.getCorpKit(aid, Kid.HD_GAME);
        Param game = hdGame.getGameInfoByField(aid, gameId, new FaiList<String>(Collections.singletonList(HdGameDef.Info.STYLE)));
        int style = game.getInt(HdGameDef.Info.STYLE, -1);
        String nameByStyle = HdGameDef.Style.getNameByStyle(style);


        return JsonResult.success(new Param().setInt(AcctDef.Info.AID, aid).setString("prototype", nameByStyle));
    }

}
