package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *  列表查询vo
 * <AUTHOR> 2025/4/18 11:35
 * @Update HLS 2025/4/18 11:35
**/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateListVO {
    // 类型
    private int type = -1;

    // 模版名称 / 原型id / 模版id
    private String key = "";

    // 模版名称 / 原型id / 模版id值
    private String keyword = "";

    // 状态
    private int status  = -1;

    // 创建时间
    private String createTimeStart;

    private String createTimeEnd;

    // 分页
    private int pageNo = 1;

    private int pageLimit = 20;

    public Integer getOffset() {
        return (pageNo - 1) * pageLimit;
    }

}
