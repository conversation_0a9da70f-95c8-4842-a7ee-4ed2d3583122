package fai.webhdoss.controller;


import fai.app.HdBlackAidDef;
import fai.app.HdProfDef;
import fai.app.StaffDef;
import fai.cli.FaiMarketCli;
import fai.cli.HdProfCli;
import fai.cli.HdRecordCli;
import fai.cli.StaffCli;
import fai.comm.util.*;
import fai.hdUtil.HdMisc;
import fai.hdUtil.JsonResult;
import fai.hdUtil.collector.FaiListCollector;
import fai.web.App;
import fai.webhdoss.WebHdOss;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Calendar;

/**
 * 互动模板列表相关
 *
 * <AUTHOR>
 * @date 20240812
 */
@RestController
@Api(value = "互动封禁红包aid列表")
@RequestMapping("/blackAid")
public class HdBlackAidListController {

    @Autowired
    HdRecordCli hdRecordCli;

    @Autowired
    HdProfCli hdProfCli;

    @Autowired
    StaffCli staffCli;

    @Autowired
    FaiMarketCli faiMarketCli;

    @RequestMapping("/list")
    @ApiOperation(value = "列表")
    public JsonResult getList(@RequestParam(required = false, defaultValue = "0") Integer aid,
                              @RequestParam(defaultValue = "1") @ApiParam(value = "页数", defaultValue = "1", example = "1") int pageNo,
                              @RequestParam(defaultValue = "10") @ApiParam(value = "每一页的数量", defaultValue = "10", example = "10") int pageSize) throws Exception {

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.totalSize = new Ref<>();
        searchArg.start = (pageNo - 1) * pageSize;
        searchArg.limit = pageSize;
        ParamComparator comp = new ParamComparator(HdBlackAidDef.Info.CREATE_TIME, true);
        searchArg.cmpor = comp;
        if (aid > 0) {
            searchArg.matcher.and(HdBlackAidDef.Info.AID, ParamMatcher.LK, aid);
        }
        Log.logDbg("searchArg = %s", searchArg.matcher);
        Log.logDbg("lstart = %s;limit = %s", searchArg.start, searchArg.limit);

        FaiList<Param> list = new FaiList<Param>();
        int rt = hdRecordCli.getBlackAidList(1, searchArg, list);
        if (rt != Errno.OK) {
            App.logErr(rt, "get hd black list err searchArg = %s", searchArg);
            return JsonResult.error();
        }
        int totalCount = 0;
        if (searchArg.totalSize != null && searchArg.totalSize.value != null) {
            totalCount = searchArg.totalSize.value;
        }

        FaiList<Integer> aidList = list.stream().map(e -> e.getInt(HdProfDef.Info.AID, 0)).filter(e -> e > 0).collect(new FaiListCollector<Integer>());
        ParamMatcher matcher = new ParamMatcher(HdProfDef.Info.AID, ParamMatcher.IN, aidList);
        FaiList<Param> tmpAidList = new FaiList<>();
        hdProfCli.getProfListFromDB(1, tmpAidList, matcher);
        Param aidMap = HdMisc.listToParam(tmpAidList, HdProfDef.Info.AID);

        for (Param param : list) {
            Integer p_aid = param.getInt(HdBlackAidDef.Info.AID, 0);
            Integer p_sid = param.getInt(HdBlackAidDef.Info.RELIEVE_SID, 0);
            Integer p_flag = param.getInt(HdBlackAidDef.Info.FLAG, 0);

            Param prof = aidMap.getParamNullIsEmpty(p_aid + "");
            int flagC = prof.getInt(HdProfDef.Info.FLAG_C, 0);

            FaiList<String> reason = new FaiList<>();
            if (Misc.checkBit(flagC, HdProfDef.FlagC.BLACK_AID_CHEAT_BY_RP_SEND)) {
                reason.add("派发异常-封禁");
            }
            if (Misc.checkBit(flagC, HdProfDef.FlagC.BLACK_AID_CHEAT_BY_RP_SEND_CHARGE)) {
                reason.add("充值/派发异常-封禁");
            }
            if (Misc.checkBit(p_flag, HdBlackAidDef.Flag.MG_PAY_CHEAT_THREE)) {
                reason.add("充值异常-3条");
            }
            param.setList("reasonList", reason);
            if (p_sid > 0) {
                Param sidInfo = new Param();
                staffCli.getStaffInfo(1, p_sid, sidInfo);
                String name = sidInfo.getString(StaffDef.Info.NAME, "");
                param.setString("relieveSidName", name);
            }
        }
        JsonResult jsonResult = JsonResult.success(list);
        jsonResult.setTotalSize(totalCount);
        return jsonResult;
    }

    @RequestMapping("/get")
    @ApiOperation(value = "单个", httpMethod = "GET")
    public JsonResult getOne(@RequestParam Integer aid) {
        Param blackAidInfo = new Param();
        hdRecordCli.getBlackAid(aid, blackAidInfo);
        return JsonResult.success(blackAidInfo);
    }

    @RequestMapping("/add")
    @ApiOperation(value = "添加", httpMethod = "GET")
    public JsonResult add(@RequestParam Integer aid) {
        Param blackAidInfo = new Param();
        blackAidInfo.setInt(HdBlackAidDef.Info.AID,aid);
        blackAidInfo.setInt(HdBlackAidDef.Info.STATUS,HdBlackAidDef.Status.BANNED);
        blackAidInfo.setCalendar(HdBlackAidDef.Info.CREATE_TIME,Calendar.getInstance());
        blackAidInfo.setInt(HdBlackAidDef.Info.AGENT_AID,1);

        return JsonResult.success(hdRecordCli.addBlackAid( blackAidInfo));
    }


    @RequestMapping("/unbanned")
    @ApiOperation(value = "解封", httpMethod = "GET")
    public JsonResult unbanned(@RequestParam Integer aid) throws Exception {

        int sid = WebHdOss.getSid();
        if (0 == sid) {
            return JsonResult.error("未登录");
        }

        Param blackAidInfo = new Param();
        hdRecordCli.getBlackAid(aid, blackAidInfo);
        if (Str.isEmpty(blackAidInfo)) return JsonResult.error("aid 不存在封禁记录");
        Integer status = blackAidInfo.getInt(HdBlackAidDef.Info.STATUS, HdBlackAidDef.Status.BANNED);
        if (HdBlackAidDef.Status.BANNED != status) return JsonResult.error("aid 不是封禁状态");

        Param data = new Param();
        data.setInt(HdBlackAidDef.Info.AID, aid);
        data.setInt(HdBlackAidDef.Info.STATUS, HdBlackAidDef.Status.UN_BANNED);
        data.setCalendar(HdBlackAidDef.Info.RELIEVE_TIME, Calendar.getInstance());
        data.setInt(HdBlackAidDef.Info.RELIEVE_SID, sid);

        int rt = hdRecordCli.setBlackAid(1, data);
        if (rt != Errno.OK) {
            App.logErr(rt, "set hd black aid err data = %s", data);
            return JsonResult.error(rt, "aid 解封失败");
        }
        return JsonResult.success();
    }

}
