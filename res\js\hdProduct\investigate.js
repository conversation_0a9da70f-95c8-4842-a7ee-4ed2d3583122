( () => {
    let invastiGate = new Vue({
        el : "#invasitigateBox",
        data : {
            title : "互动调查问卷记录",
            formQuery :{
                invastiNum : '',
                invastiName:'',
                invastiRemark:'',
                page : 1,
                allPageNum : 1,
                pageSize : 10
            },
            rules :{
                invastiNum : [
                    { type: 'number', message: '问卷编码必须为数字值', trigger: 'change'}
                ],
                invastiName : [
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                invastiRemark : [
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
            },
            tableData :[],
            isLoading: false,
            dialogFormVisible: false,
            invasiFormData: {
                id: '',
                type: '',
                title: '',
                remark: '',
                questions: ''
            },
            formLabelWidth: '120px'
        },
        methods:{
            searchInvasi : function(formName,isFirst){
                let formQuery = this.formQuery;
                if( isFirst ){
                    this.getSearchList({
                        page : 1,
                        pageSize : 10
                    });
                }else{
                    // this.$refs[formName].validate((valid) => {
                    //     if(valid) {
                    //         this.getSearchList(formQuery);
                    //     }else{
                    //         this.$message({
                    //             type: 'warning',
                    //             message: '请检查问卷信息是否正确'
                    //         });
                    //         return false;
                    //     }
                    // });
                    let number = parseInt(formQuery.invastiNum);
                    if( !isNaN(number) && number <= 0 ){
                        this.$message({
                            type: 'warning',
                            message: '问卷编码不正确，请重新填写'
                        });
                        return false;
                    }
                    this.getSearchList(formQuery);
                }
            },
            getSearchList : function(formData){
                if(!formData)return;
                this.isLoading = true;
                let arg = {
                    "cmd":"getResearchQuestionsList4Oss",
                }; 
                Object.assign(arg,formData);
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {
                    let data = response.data;
                    this.isLoading = false;
                    if( data.success ){
                        this.tableData = data.list;
                    }else{
                        this.$message({
                            type: 'warning',
                            message: data.msg
                        });
                    }                  
                }, response => {
                    this.isLoading = false;
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            },
            handleLookInfo : (lineInfo) =>{
                let type = lineInfo.type;
                let openUrl = exposeParam.hdossRoot+"/hdProduct/hdInvastigateInfo.jsp?investigateId="+type;
                window.open(openUrl);
            },
            handleSee      : (lineInfo) =>{
                let type = lineInfo.type;
                let arg = {
                    "cmd":"getResearchQuestionsByType4Oss",
                    "type":type
                }; 
                invastiGate.isLoading = true;
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {
                    let data = response.data;
                    invastiGate.isLoading = false;
                    if( data.success ){
                        getQuestion({
                            type : type,
                            data : data.data
                        });
                    }else{
                        invastiGate.$message({
                            type: 'warning',
                            message: data.msg
                        });
                    }                  
                }, response => {
                    invastiGate.isLoading = false;
                    invastiGate.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                }); 
            },
            cancelAddInvasi() {
                this.dialogFormVisible = false;
            },
            addInvasi() {
                this.cancelAddInvasi();
                this.isLoading = true;
                let formData = this.invasiFormData;
                let arg = {
                    "cmd":"addQuestions",
                }; 
                Object.assign(arg,formData);
                console.log(arg);
                Vue.http.post("/ajax/hdProduct_h.jsp", arg, {emulateJSON:true}).then(response => {
                    let data = response.data;
                    this.isLoading = false;
                    this.$message({
                        type: 'warning',
                        message: data.msg
                    });                 
                }, response => {
                    this.isLoading = false;
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            }
        }
    });
    invastiGate.searchInvasi('formQuery',true);
    function getQuestion(option){
        var poupOption = JSON.parse(option.data);
        var hasInitIndex = 0;
        let box = '';
        let questionTitle = '';
        switch( option.type ){
            case 4:
            case 5:
                questionTitle = poupOption[0].question;
                let dataList = poupOption[0].answer;
                dataList.forEach( (data,index) => {
                    box += '<div>'+data.name+'</div>';
                    if( data.needRemark ){
                        box += '<div><input type="text"></div>';
                    }
                } );
                break;
            default:
            box = ''+
            '<div class="middle">'+
                '<div class="questionBox">'+
                    '<div class="quesition">';
            poupOption.forEach( (data,index) => {
                box += '<div class="questionInner">'+data.question+'</div>';
                if( data.answer.length > 0 ){
                    var answerArray = data.answer;
                    if( typeof answerArray[0].question != 'undefined' ){
                        var theArray = data.answer;
                        theArray.forEach( (theData,theIndex) => {
                            if( theIndex > 3 ){
                                box += '<div class="questionBoxer hide">';
                            }
                            box += '<div class="questionInner">'+theData.question+'</div>';
                            box += getAnswerBox(theData.answer,theData.isRemark);
                            if( theIndex > 3 ){
                                box +=  '</div>';
                            }
                        })
                    }else{
                        box += getAnswerBox(answerArray);
                    }
                }
            } );
            questionTitle = '问卷调查';
            break;
        }      
        //获取选项
        function getAnswerBox(array , isRemark){
            if( isRemark ){
                return  '<div class="otherInput">'+
                            '<input class="userInput" type="text" placeholder="请输入内容" maxlength="10" >'+
                        '</div>';
            }
            if( !array || !Array.isArray(array) || array.length == 0)return;
            var str = '<div class="answerBox clearFix" index="'+hasInitIndex+'">';
            var hasInput = false;
            array.forEach((theData,theIndex) =>{
                str += '<span class="answerItem" val="'+theData.id+'">'+theData.name+'</span>';
                if( theData.id == 0 ){
                    hasInput = true;
                }
            })
            str += '</div>';
            if( hasInput ){
                str +=  '<div class="otherInput hide">'+
                            '<input class="userInput" type="text" placeholder="请输入内容" maxlength="10" >'+
                        '</div>';
            }
            hasInitIndex++;
            return str;
        }
        invastiGate.$confirm(box,questionTitle,{
            showCancelButton:false,
            showConfirmButton:false,
            dangerouslyUseHTMLString:true,
            customClass:"poupInvastigate"
        }).then(() => {
        
        }).catch(() => {
            
        });
    }
} )()