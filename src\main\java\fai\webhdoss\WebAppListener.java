package fai.webhdoss;


import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.ServletRequestEvent;
import javax.servlet.ServletRequestListener;
import javax.servlet.http.HttpServletRequest;

import fai.comm.config.FaiConfig;
import fai.comm.util.ConfPool;
import fai.comm.util.Log;
import fai.comm.util.Str;
import fai.hdUtil.rpc.HdFaiClientProxyFactory;
import fai.sdk.fdpdata.FdpDataSDK;
import fai.web.Core;
import fai.web.Session;
import fai.web.Web;
//测试
public class WebAppListener implements ServletContextListener,
		ServletRequestListener, Log.Callback {
	@Override
	public void contextInitialized(ServletContextEvent event) {
		Core.init();
		ServletContext context = event.getServletContext();
		Web.init(context, this);
		initWebConf(context);

		HdFaiClientProxyFactory.initFlowGenerator(Core::getFlow);

		//数据平台查询sdk初始化，互动统一appId 1014 https://train.faisco.biz/YUlllA
		FdpDataSDK.init(1014);
	}
	// 初始化资源文件的配置信息
	private void initWebConf(ServletContext context){
		String etcRoot = "/home/<USER>/etc/web/hdoss/";
		if (Web.isPre()) {
			//dep2的话，因为涉及到两个分支的环境代码，所以这里强制dep2测试的环境从另一个目录下去读取配置文件
			etcRoot = "/home/<USER>/git/web/hdoss/etc/";
			Log.logStd("dep2配置文件读取路径：etcRoot=%s", etcRoot);
		}
		
		if (!Str.isEmpty(context.getInitParameter("etcRoot"))) {
            etcRoot = context.getInitParameter("etcRoot");
        }
		
		String contextRpath = context.getRealPath("/");
		int deverPosition = contextRpath.indexOf("/dev-svn/");
		String dever = "";
		if( Web.getDebug() && deverPosition > 0 ){
			dever = contextRpath.substring( deverPosition + "/dev-svn/".length() );
			int deverEnd = dever.indexOf('/');
			if( deverEnd <= 0 ){
				dever = "";
			}else{
				dever = dever.substring( 0, deverEnd );
				etcRoot = "/home/<USER>/dev-svn/" + dever + "/etc/web/hdoss/";
			}
		}
		
		Web.setConf("hdOssRes", etcRoot + "res_hdoss.conf");
		Web.setConf("constant", etcRoot + "constant.conf");
		Web.setConf("yk_constant", etcRoot + "yk_constant.conf");
		//**	跨aid模板复制需要读取hdportal下的配置文件xxx.conf
//		Web.setConf("hdGameInitData", "/home/<USER>/etc/web/hdportal/gameInitData.conf");
//		Web.setConf("hdportalWeb", "/home/<USER>/etc/web/hdportal/hdportal.conf");
		Web.setConf("hdgameTemplate", "/home/<USER>/etc/web/hdportal/template.conf");
		Web.setConf("faiopenid", "/home/<USER>/etc/web/hdportal/faiopenid.conf");
		ConfPool.setFaiConfigGlobalConf("openCodeNew", FaiConfig.EtcType.ENV);
		ConfPool.setFaiConfigGlobalConf("hdportal", FaiConfig.EtcType.ENV);		//hdportal.conf从本地磁盘文件改为读取配置中心
		ConfPool.setFaiConfigGlobalConf("gameInitData", FaiConfig.EtcType.ENV);//活动初始化数据
		//案例筛选库权限配置文件 线上
		ConfPool.setFaiConfigGlobalConf("caseFilterAuthorityList", FaiConfig.EtcType.ENV);
		//案例筛选库权限配置文件 本地
		ConfPool.setFaiConfigGlobalConf("caseFilterAuthorityListDebug", FaiConfig.EtcType.ENV);
		//**
		//案例筛选库权限配置文件 线上
		ConfPool.setFaiConfigGlobalConf("caseFilterAuthorityList", FaiConfig.EtcType.ENV);
		//案例筛选库权限配置文件 本地
		ConfPool.setFaiConfigGlobalConf("caseFilterAuthorityListDebug", FaiConfig.EtcType.ENV);
		ConfPool.setFaiConfigGlobalConf("hdGift", FaiConfig.EtcType.ENV);
		//hdOemBackDoMain
		ConfPool.setFaiConfigGlobalConf("hdOemBackDoMain", FaiConfig.EtcType.ENV);
		ConfPool.setFaiConfigGlobalConf("faiDomainList", FaiConfig.EtcType.ENV);
	}

	@Override
	public void contextDestroyed(ServletContextEvent event) {
		Log.logStd("AppEnd");
	}

	@Override
	public void requestInitialized(ServletRequestEvent event) {
		HttpServletRequest request = (HttpServletRequest) event.getServletRequest();
		Core.setRequest(request);
		Core.initCliId();
		int aid = 0;
		try {
			aid = Session.getCid();
		} catch (Exception e) {
			Log.logErr("get session cid err", e);
		}
		Core.setAid(aid);
	}

	@Override
	public void requestDestroyed(ServletRequestEvent event) {
		Core.setRequest(null);
	}

	@Override
	public String getHeadMsg() {
		int flow = Core.getFlow();
		if (flow == 0) {
			return null;
		}
		return Str.format("%d", flow);
	}
}