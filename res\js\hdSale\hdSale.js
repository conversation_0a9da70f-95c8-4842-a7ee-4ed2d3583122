/*--------------客户列表 */

Date.prototype.format = function(format)
{
    var o = {
        "M+" : this.getMonth()+1, //month
        "d+" : this.getDate(),    //day
        "h+" : this.getHours(),   //hour
        "m+" : this.getMinutes(), //minute
        "s+" : this.getSeconds(), //second
        "q+" : Math.floor((this.getMonth()+3)/3),  //quarter
        "S" : this.getMilliseconds() //millisecond
    }
    if(/(y+)/.test(format)) format=format.replace(RegExp.$1,
        (this.getFullYear()+"").substr(4 - RegExp.$1.length));
    for(var k in o)if(new RegExp("("+ k +")").test(format))
        format = format.replace(RegExp.$1,
            RegExp.$1.length==1 ? o[k] :
                ("00"+ o[k]).substr((""+ o[k]).length));
    return format;
}

var localTime = new Date();

//数据埋点数据
var element2default = {};
var element2count = {
	"1": {},
	"2": {},
	"3": {},
	"4": {}
};

var tabChange = false;
var invalidSearch = false;
var otherBtn = false;

// v-dialogDrag: 弹窗拖拽属性
Vue.directive('dialogdrag', {
  bind(el, binding, vnode) {
    const dialogHeaderEl = el.querySelector('.el-dialog__header');
    const dragDom = el.querySelector('.el-dialog');
    //dialogHeaderEl.style.cursor = 'move';
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dragDom.style.cssText += ';top:0px;'
  
    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const sty = (function() {
        if (window.document.currentStyle) {
            return (dom, attr) => dom.currentStyle[attr];
        } else{
            return (dom, attr) => getComputedStyle(dom, false)[attr];
        }
    })()    
     
    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;
       
      const screenWidth = document.body.clientWidth; // body当前宽度
        const screenHeight = document.documentElement.clientHeight; // 可见区域高度(应为body高度，可某些环境下无法获取) 
         
        const dragDomWidth = dragDom.offsetWidth; // 对话框宽度
        const dragDomheight = dragDom.offsetHeight; // 对话框高度
         
        const minDragDomLeft = dragDom.offsetLeft;
        const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;
         
        const minDragDomTop = dragDom.offsetTop;
        const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomheight;
  
       
      // 获取到的值带px 正则匹配替换
      let styL = sty(dragDom, 'left');
      let styT = sty(dragDom, 'top');
  
      // 注意在ie中 第一次获取到的值为组件自带50% 移动之后赋值为px
      if(styL.includes('%')) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, '') / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, '') / 100);
      }else {
        styL = +styL.replace(/\px/g, '');
        styT = +styT.replace(/\px/g, '');
      };
       
      document.onmousemove = function (e) {
        // 通过事件委托，计算移动的距离 
                let left = e.clientX - disX;
                let top = e.clientY - disY;
                 
                // 边界处理
               /* if (-(left) > minDragDomLeft) {
                    left = -(minDragDomLeft);
                } else if (left > maxDragDomLeft) {
                    left = maxDragDomLeft;
                }
                 
                if (-(top) > minDragDomTop) {
                    top = -(minDragDomTop);
                } else if (top > maxDragDomTop) {
                    top = maxDragDomTop;
                }*/
  
        // 移动当前元素 
                dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };
  
      document.onmouseup = function (e) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    } 
  }
});

	
var hdSale = new Vue({
	el:'#hdSale-list',
	data: {
		dataList: [],
        parentBoxAuth:{},
		pageData:{
			currentPage:1,
			total:0,
			size:20,
		},
		pageGhData:{
			currentPage:1,
			total:0,
			size:20,
		},
		tabNameDeal:"成交库(0)",
		//tabNamePerson:"A库(0)",
        //tabNamePerson:"个人库(0)",
		tabNamePerson:"当月库(0)",
		tabNameA:"A库(0)",
        tabNameB:"B库(0)",
        tabNameGh:"公海库",
        tabNameInvalid:"无效库",
		tab:1,
		logicImgSrc:'',
		message:{
			messageModelList:[],//短信模板
			messageModel:"",//选择的短信模板
			singleMobile:"",//单个客户手机
			page:1,//当前页
			dataList:[],
			addTimeStart:new Date(),
			addTimeEnd:new Date(),
			total:0,
			sendTime:{
				start:localTime,
				end:localTime,
				check:false,
			},
		},
		aidList:{
			dataList:[],
			total:0,
		},
		//修改领取时间
		editReceiveTimeData:{
			aid:0,
			oldReceiveTime:'',
			newReceiveTime:'',
			sale:'',
			picture:'',
			name:'filedata',
			fileId:''
		},
        //修改领取时间
        backToAkuData:{
            aid:0,
            sale:'',
            picture:'',
            name:'filedata',
            fileId:''
        },
		personInfo:{					//个人电话信息
			nickName:"",
			phone:"",
			modelInfoList:[],			//短信模板
		},
		QWRemindInfo:{                 //企微发送列表
			remindList:[],
			page:1,//当前页
			total:0,
			remindStartTime:new Date(),
			remindEndTime:new Date()
		},
		reasonStat:{					//未购买原因
			hasGet:0,					//是否已经获取了信息,因为有concat函数，避免多次获取
			value:'全部',
			labelList:[
				{name:"全部",label:"全部"}
			],
			regTime:{					//注册时间
				start:localTime,
				end:localTime,
			},
			totolList:{
				intentList:[
					{
						'intent':'总计',
						'percent':0,
						'互动免费版':0,
						'互动白银版':0,
						'互动铂金版':0,
						'总计':0
					},
				],
				markList:[
					{
						'disBuyReason':'总计',
						'percent':0,
						'互动免费版':0,
						'互动白银版':0,
						'互动铂金版':0,
						'总计':0
					},
				],
			}				//未购买原因统计
		},
        aidMsgDialog:{
            show: false,
			data: '',
			biaoji: '',
            inputNote: '',
			mark: '',
			editMark: '',
			QWRemindTime:new Date(),//填充企微提醒时间
			pickerOptions:{//设置企微提醒时间大于当前时间
		          disabledDate(time) {
		              return time.getTime() < Date.now();
		            }
			},
			index:'',
			sFlag:'重点关注',
			sendMsg:true,
			index:'',
			oldIndex:'',//按最后更新时间排序提交备注会刷新，index是0，销售点击下一个希望是未刷新之前的下一个,用此变量记录
			lastConsultTime:'',//最后咨询客服时间
			oneWeekTalkNum:'',//一周咨询客服次数
			token:''
        },
        showCustomPagelog:{
        	show: false,
        	data:[],
        	value:[],
        	originalValue:[]
        },

		biaojiDialog:{
			show: false,
			aid:'',
			data: '',
            intent: '',
            reason: '',
            intentLevel: '',
            intentList: '',
            reasonList:'',
            talkNextTime:'',
            corpMark:'',
            remindContent:'0'
		},
		buyProductDialog:{
			show:false
		},

		textStyle_10:{
            color: ''
		},
        textStyle_20:{
            color: ''
        },
        textStyle_28:{
            color: ''
        },
        textStyle_follow:{
            color: ''
        },
        textStyle_ban_msg:{
            color: ''
        },

		logicImgVisible:false,
		dialogVisible:false,
		ediReceiveTimePanel:false,
		backToAkuPanel:false,
		libTab:false,

		form:{
			status:this.tab,
			province:"",
			city:"",
			aid:"",
			mark:"",
			joinNumStart:"",
			joinNumEnd:"",
			
			regTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			lastUpdateTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			receiveTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			sendMessageTime:{
				start:localTime,
				end:localTime,
				check:false,
				noSendCheck:false,
			},
			nextTalkTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			firstCreateGameTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			loginTime:{
				start:localTime,
				end:localTime,
				check:false
			},
			ta:{
				name:"注册来源",
				value:-1,
				labelList:[]
			},
			wxVersion:{
				name:"助手版本",
		        value:-1,
		        labelList:[]
		     },
			intent:{
				name:"标记",
				value:-1,
				labelList:[]
			},
			goal:{
				name:"注册用途",
				value:-1,
				labelList:[]
			},
			siteVersion:{
				name:"网站版本",
				value:-1,
				labelList:[
				]
			},
			regBiz:{
				name:"注册产品",
				value:-1,
				labelList:[
				]
			},
			hdVersion:{
				name:"互动版本",
				value:-1,
				labelList:[]
			},
			cdVersion:{
				value:-1,
				name:"微传单版本",
				labelList:[]
			},
			sortBy:{
				name:"排列顺序",
				value:-1,
				labelList:[]
			},
			oneWeekSortBy:{
				value:-1,
				name:"第二个排列顺序",
				labelList:[
					{name:"无",label:-1},
					{name:"一周登录次数倒序",label:0},
					{name:"一周登录次数顺序",label:1},
					{name:"一周操作次数倒序",label:2},
					{name:"一周操作次数顺序",label:3}
				]
			},
			saleGroup:{
				value:"all",
				name:"销售分组",
				labelList:[]
			},
			
			receiveSale:{
				value:"all",
				name:"领取人",
				labelList:[
					{name:"全部",label:"all"},
				]
			},
			tag:{
				name:"标签",
				value:-1,
				labelList:[{name:"全部",label:"-1"}]
			},
			timeRange:{
				name:"时间段",
				value:'-1',
				labelList:[
					{name:"全部",label:"-1"},
					{name:"15:45前",label:"0"},
					{name:"15:45后",label:"1"}
				]
			},
			coupon:{
				name:"有现金券且未过期",
				value:-1,
				labelList:[
				]
			},
			intentLevel:{
				name:"意向度",
				value:-1,
				labelList:[
				]
			},
			business:{
				name:"全部",
				value:-1,
				labelList:[
					{name:"全部",label:-1},	
					{name:"互动",label:0},
					{name:"悦客",label:1},
					{name:"公众号助手",label:2},
				]
			},
			hasOpenYk:{
				name:"全部",
				value:-1,
				labelList:[
					{name:"全部",label:-1},	
					{name:"未开通悦客",label:0},
					{name:"已开通悦客",label:1},
				]
			},
			hasCorpName:{
				name:"全部",
				value:-1,
				labelList:[
					{name:"全部",label:-1},	
					{name:"无企业名称",label:0},
					{name:"有企业名称",label:1},
				]
			},
//			isAward:{    移除奖励资源选项
//				name:"奖励",
//				value:"-1",
//				labelList:[
//					{name:"不限",label:"-1"},
//					{name:"是",label:"0"},
//					{name:"否",label:"1"}
//				]
//			},
			isWeekendResource:{
				name:"不限",
				value:-1,
				labelList:[
					{name:"不限",label:-1},
					{name:"否",label:0},
					{name:"是",label:1}
				]
			},
			args:{
				name:"不限",
				value:false,
				labelList:[
				     {name:"不限",label:false},
				     {name:"10天未联系",label:"noContact10Day"},
				     {name:"20天未联系",label:"noContact20Day"},
				     {name:"28天未联系",label:"noContact28Day"},
				]
			},
			allotType:{
				name:"不限",
				value:-1,
				labelList:[
				    {name:"全部",label:-1},
					{name:"平均分配",label:0},
					{name:"奖励资源",label:1},
					{name:"续费资源",label:2},
					{name:"公海库领取",label:3},
					{name:"审批领取",label:4},
				]
			},
			excel:false,
			A_BLib:{
				name:"A/B库",
				value:"0",
				labelList:[],
			},
			isSend:{
				name:"全部",
				value:-1,
				labelList:[
				     {name:"全部",label:-1},
				     {name:"是",label:1},
				     {name:"否",label:0}
				],
			},
			remindTime:{
				start:localTime,
				end:localTime
			}
		},
		// navList: [{title:"主页",url:"index"},{title:"互动销售",url:"hdSale"}],
		active:"index",
		title:"个人库",
		
		// 无效库
		invalidForm: {
            salesAcct: '',
            isPay: 0,
            invalidType: 0,
            payTimeSelect: false,
            createTimeSelect: false,
            loginTimeSelect: false,
            payTimeBeg: localTime.format('yyyy-MM-dd'),
            payTimeEnd: localTime.format('yyyy-MM-dd'),
            createTimeBeg: localTime.format('yyyy-MM-dd'),
            createTimeEnd: localTime.format('yyyy-MM-dd'),
            loginTimeBeg: localTime.format('yyyy-MM-dd'),
            loginTimeEnd: localTime.format('yyyy-MM-dd'),
		},
        invalidDataList: [],
		isShowInvalidDialog: false,
		invalidType: 1,
		invalidMark: '',
		pageInvalid: {
			currentPage: 1,
			total: 0
		}
	},

	created: function(){
		element2count["1"]['search'] = -1;
		element2count["2"]['search'] = 0;
		element2count["3"]['search'] = 0;
		element2count["4"]['search'] = 0;

		// 刷新的时候回到对应的导航页面
		this.getDataListByArgs();
		this.getDefList();
		this.getMessageModelList();
		this.initBiaojiInfo();
		this.getCustomPageInfo();
	},
	methods: {
		getSendMessageList(){
			var arg = {
				"cmd":"getSendMessageList",
				"pageNo":this.message.page,
				"begDate":Fai.tool.getDateTimeStart(this.message.addTimeStart),
				"endDate":Fai.tool.getDateTimeEnd(this.message.addTimeEnd),
				"sendTimeStart":Fai.tool.getDateTimeStart(this.message.sendTime.start),
				"sendTimeEnd":Fai.tool.getDateTimeEnd(this.message.sendTime.end),
				"sendTime":this.message.sendTime.check,
				"sale":this.form.receiveSale.value,
			}
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				var data = response.data;
				if(data.success){
					this.message.dataList = data.dataList;
					this.message.total = data.total;
				}else{
					this.$message({
						type: 'warning',
						message: data.msg,
					});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
        getRemindList(excel){
			var arg={
			   "cmd":"getRemindList",
			   "remindStart":Fai.tool.getDateTimeStart(this.QWRemindInfo.remindStartTime),
			   "remindEnd":Fai.tool.getDateTimeStart(this.QWRemindInfo.remindEndTime),
			   "sale":this.form.receiveSale.value,
			   "pageNo":this.QWRemindInfo.page,
			   "isRemind":this.form.isSend.value,
			   "excel":excel
			}
			if(excel==true){	
				window.location.href =  '/ajax/hdSale_h.jsp'+Fai.tool.parseJsonToUrlParam(arg,true);
				
			}else{
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					var data = response.data;
					if(data.success){
						this.QWRemindInfo.remindList = data.dataList;
						
						this.dataList = data.dataList;
						this.QWRemindInfo.total = data.total;
					}else{
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			}
		},
		deleteRemind(index,remindList){
			this.$confirm('确定删除?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				  var arg={
					"cmd":"deleteRemind",
				    "id":remindList[index].id,
				    "nickName":remindList[index].nickName,
					"QWRemindTime":remindList[index].QWRemindTime,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					var data = response.data;
					if(data.success){
						this.$message({
							type: 'success',
							message: data.msg,
						});
						this.getRemindList();
					}else{
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});          
			  });
		},
		
		
		changeStatus(tab,event){
			tabChange = true;

            this.form.sortBy.value = -1;
			 if(tab.index =="0"){//当月库
			 	this.tab = 1;
			 	this.getDataListByArgs();
			 	this.getCustomPageInfo();
			 }else if(tab.index=="1"){//A库
                this.tab = 2;
                this.getDataListByArgs();
                this.getCustomPageInfo();
             }else if(tab.index=="2"){//B库
			 	this.tab = 3;
			 	this.getDataListByArgs();
			 	this.getCustomPageInfo();
			 }else if(tab.index == "3"){//成交库
				 this.tab = 4;
				 this.getDataListByArgs();
				 this.getCustomPageInfo();
			 }else if(tab.index=="4"){//短信列表
			 	this.tab = 5;
			 	this.getSendMessageList();
			 }else if(tab.index == "5"){//维护个人电话信息
			 	if(this.tab != 6){
			 		this.tab = 6;
			 		this.getSaleMsg();
			 	}
			 }else if(tab.index == "6"){//企微提醒列表
			 	this.tab = 7;
			 	this.getRemindList();
			 }else if(tab.index == "7"){//公海库
				 this.tab = 8;
				 this.form.sortBy.value = "1";
				 this.pageData.currentPage=1;
				 this.getDataListGh();
			 }else if(tab.index == "8"){//无效库
				 this.tab = 9;
				 this.pageData.currentPage=1;
				 this.getInvalidDataList();
			 }

			tabChange = false;
		},
		
		//释放当月库和成交库，A库和B库
		releasePreSale(index,dataList){
			//let cmd = this.tab == 1 ? 'releasePreSale':'releasePreSaleDeal';
			let cmd ='';
			if(this.tab === 1 || this.tab === 2 || this.tab === 3){
				cmd ='releasePreSale';
			}else if(this.tab === 4){
				cmd ='releasePreSaleDeal';
			}
			this.$confirm('确定释放该客户?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":cmd,
					"aid":dataList[index].aid,
					"salesAcct":dataList[index].salesAcct,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					this.$message({
					type: 'success',
					message: response.data.msg
					});

					invalidSearch = true;
					this.getDataListByArgs();
					invalidSearch = false;
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});          
			  });
		},
		
		
		turnStatus(index,dataList,type){
			let lib = (type =='historian')?'A':'B';
			this.$confirm('确定将该客户转入'+lib+'库?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":'turnStatus',
					"aid":dataList[index].aid,
					"salesAcct":dataList[index].salesAcct,
					'type':type,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					this.$message({
					type: 'success',
					message: response.data.msg
					});

					invalidSearch = true;
					this.getDataListByArgs();
					invalidSearch = false;
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});          
			  });
		},
		
		editReceiveTime(val){
			if(val){//确定上传
				var arg = {
					"cmd":"addPreSaleApprove",
					"aid":this.editReceiveTimeData.aid,
					"fileId":this.editReceiveTimeData.fileId,
					"receiveTime":Fai.tool.dateFormatter(this.editReceiveTimeData.newReceiveTime.getTime(),"yyyy-MM-dd HH:mm:ss"),
					"salesAcct":this.editReceiveTimeData.sale,
					"approveStyle":7,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					var data = response.data;
					if(data.success){
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}else{
						this.$message({
							type: 'warning',
							message: data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			}
			this.ediReceiveTimePanel = false;
		},
		//提交领回B库申请
        submitBackToAkuData(val){
            if(val){//确定上传
                var arg = {
                    "cmd":"backToAkuApprove",
                    "aid":this.backToAkuData.aid,
                    "fileId":this.backToAkuData.fileId,
                    "salesAcct":this.backToAkuData.sale,
                    "approveStyle":8,
                }
                Vue.http.post("/ajax/hdSale/approve_h.jsp", arg, {emulateJSON:true}).then(response => {
                    var data = response.data;
                    if(data.success){
                        this.$message({
                            type: 'warning',
                            message: data.msg,
                        });
                    }else{
                        this.$message({
                            type: 'warning',
                            message: data.msg,
                        });
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            }
            this.backToAkuPanel = false;
        },
		showEditReceiveTime(index,dataList){
			this.ediReceiveTimePanel = true;
			var data = dataList[index];
			this.editReceiveTimeData.aid = data.aid;
			this.editReceiveTimeData.oldReceiveTime = data.receiveTime;
			this.editReceiveTimeData.sale = data.salesAcct;
		},
		showBackToAkuPanel(index,dataList){
			this.backToAkuPanel = true;
			var data = dataList[index];
			this.backToAkuData.aid = data.aid;
			this.backToAkuData.sale = data.salesAcct;
		},
		//企业数据
		getCorpInfo(aid){
            var arg = {
                "aid":aid,
                "cmd":this.tab==8?"getGhCorpInfo":"getCorpInfo",
            }
            Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
                this.aidMsgDialog.data = response.data;
                this.aidMsgDialog.biaoji = response.data.biaoji;
                if(this.aidMsgDialog.biaoji.sFocus==1){
                	this.aidMsgDialog.sFlag='取消关注';
                }else{
                	this.aidMsgDialog.sFlag='重点关注';
                }
                this.aidMsgDialog.sendMsg=this.aidMsgDialog.biaoji.sendMsg;
                
                let mark = response.data.markName;
                if(typeof obj == "undefined" || obj == null || obj == ""){
                    this.aidMsgDialog.mark = mark;
				}else{
                    this.aidMsgDialog.mark = mark.replace("-----------------------","\n-----------------------\n");
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
		},
		
		//获取客服token
		getToken(aid){
			var args = {
					 "appid":"5ef1e33a54de47f9a7350ac6836989a0",
					 "appsecret":"a6e899500eca498a7583518d9bdc5caf",
				}
				//console.log("startTime="+startTime+",aid="+aid+",endTime=%s"+endTime);
				 Vue.http.post("http://kf.it.faisco.cn/auth/getToken.action",args, {emulateJSON:true}).then(response => {
		                 if(response.body.statusCode == 200){
		                	 this.aidMsgDialog.token = response.body.data.token;
		                	 this.getCustomService(aid);
		                 }else{
		                	 console.log(response.body.statusCode);
		                	 this.$message({
		 	                    type: 'warning',
		 	                    message: 'token获取有误!'
		 	                });
		                 }
		            }, response => {
		                this.$message({
		                    type: 'warning',
		                    message: '系统错误!'
		                });
		            });
		},
		//判断是否有客服记录
		getCustomService(aid){
			var startTime =localTime.getTime() - 7*24*60*60*1000;
			var endTime = localTime.getTime();
			var args = {
				"aid" : aid,
				"appid":"5ef1e33a54de47f9a7350ac6836989a0",
				"appsecret":"a6e899500eca498a7583518d9bdc5caf",
				"token" :  this.aidMsgDialog.token,
				"startTime":startTime,
				"endTime": endTime,
			 }
			 Vue.http.post("http://kf.it.faisco.cn/auth/hd/getLastTimeAndTalkNum.action",args, {emulateJSON:true}).then(response => {
                 if(response.body.statusCode == 200){
                	 this.aidMsgDialog.lastConsultTime = response.body.data.lastTime;
                	 this.aidMsgDialog.oneWeekTalkNum = response.body.data.oneWeektalknum;
                	 console.log(this.aidMsgDialog.lastConsultTime+","+this.aidMsgDialog.oneWeekTalkNum);
                 }else{
                	 this.$message({
 	                    type: 'warning',
 	                    message: '系统错误!'
 	                });
                 }
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
			 
		},
		showAidMsgDialog(index,dataList){
			//备注信息
            this.aidMsgDialog.show = true;
            this.aidMsgDialog.aidUrl = dataList[index].aidUrl;
			let aid = dataList[index].aid;
			this.aidMsgDialog.loginOneWeekCount =dataList[index].loginOneWeekCount;
            this.aidMsgDialog.optOneWeekCount = dataList[index].optOneWeekCount;
            this.aidMsgDialog.firstCreateGameTime = dataList[index].firstCreateGameTime;
            this.aidMsgDialog.index=index;
            //this.getToken(aid);
			//企业信息
            this.getCorpInfo(aid);
		},
		//zhs   2019-11-14 对用户自定义页面代码
		showCustomPageInfo(){
			//备注信息
            this.showCustomPagelog.show = true;
		},
		//zhs 2019-11-14 用于提交用户自定义页面数据	
		commitCustomPageInfo(){
			var configInfo = "";
			for(var i in this.showCustomPagelog.value){
	              if(i < this.showCustomPagelog.value.length-1){
	            	  configInfo = configInfo +this.showCustomPagelog.value[i]+",";
	              }else{
	            	  configInfo= configInfo +this.showCustomPagelog.value[i];
	              }
	            }
			var arg={
					"cmd": "setCustomPageConfigInfo",
					"moduleId": this.tab,
					"configInfo" : configInfo
			}
			let url = "/ajax/hdSale_h.jsp";
			
			//判断用户有没有进行修改过内容,保证不用重复提交，避免后台的请求压力
			var isChange = false;
			if (this.showCustomPagelog.value.length !== this.showCustomPagelog.originalValue.length) {
	            isChange = true;
	        } else {
	        	this.showCustomPagelog.value.forEach(item => {
	                if (this.showCustomPagelog.originalValue.indexOf(item) === -1) {
	                    flag = true;
	                }
	            })
	        }
			if(isChange){
				Vue.http.post(url,arg, {emulateJSON:true}).then(response =>{
					if(response.body.success == true){
	                    this.$message({
	                        type: 'success',
	                        message: "已根据你的喜好重新布局"
	                    });	
	                    this.showCustomPagelog.originalValue = this.showCustomPagelog.value;
					}else{
	                    this.$message({
	                        type: 'warning',
	                        message: '保存失败，请重试!'
	                    });
					}
	            }, response => {
	                this.$message({
	                    type: 'warning',
	                    message: '保存失败，请重试!'
	                });
	            });
			}
		},
		//zhs 2019-11-14  获取用户自定义页面的信息
		getCustomPageInfo(){
			var arg={
					"cmd":"getCustomPageConfigInfo",
					"moduleId":this.tab
				}
			let url = "/ajax/hdSale_h.jsp";
			Vue.http.post(url,arg, {emulateJSON:true}).then(response =>{
				this.showCustomPagelog.value = [];
				this.showCustomPagelog.originalValue= [];
				if(response.body.success == true){
                    this.showCustomPagelog.value=response.body.customPageInfo.split(",");
                    this.showCustomPagelog.originalValue= response.body.customPageInfo.split(",");
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
                    this.showCustomPagelog.value.splice(0,this.showCustomPagelog.value.length);
                    this.showCustomPagelog.originalValue.splice(0,this.showCustomPagelog.originalValue.length);
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '获取用户定义页面信息失败!'
                });
            });
		},
		//zhs 2019-11-14判断用户页面元素是否显示
		isShowInPage(element){
			if(this.showCustomPagelog.value.length-1 == 0 ){
				return true;
			}else{
				var index = this.showCustomPagelog.value.indexOf(element);
				if(index < 0 ){
					return false;
				}else{
					return true;
				}
			}
			return false;
		},
		//设置特别关注或者取消关注
		setSpecialFocus(aid,flag){
			var arg={
				"cmd":"setSpecialFocus",
				"aid":aid,
				"flag":flag,
			}
			//alert(aid+","+flag);
			let url = "/ajax/hdSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: response.body.msg
                    });
					this.getCorpInfo(aid);
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '关注失败!'
                });
            });
		},
		//提交备注
        submitMark(aid,mark,oldmark){
            if(oldmark == undefined){
            	oldmark = ''
			}
            if (typeof mark == "undefined" || mark == null || mark == ""){
                this.$message({
                    type: 'warning',
                    message: '备注不能为空!'
                });
                return;
            }
            var arg = {
                "cmd":"setInfoMark",
				"aid":aid,
				"mark":mark,
				"oldmark":oldmark
            }
            let url = "/ajax/hdSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: '添加成功!'
                    });
                    this.aidMsgDialog.editMark = "";
                    this.aidMsgDialog.mark = response.body.data.markName;
                    //alert(this.aidMsgDialog.index);
                    //提交完备注后，如果是按照更新时间排序改index就会变成0，所以将当前index赋值给oldIndex
                    // if(this.form.sortBy.value==-1 || this.form.sortBy.value==0){
                    // 	this.aidMsgDialog.oldIndex=this.aidMsgDialog.index;
                    // 	//alert(this.aidMsgDialog.oldIndex);
                    // }
                    for(var i = 0;i < this.dataList.length;i++){
                        if(this.dataList[i].aid == aid){
                            this.dataList[i].mark = mark;
                        }
                    }
                    //this.getDataListByArgs();
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '添加失败!'
                });
            });

		},
		//初始化标记选项
		initBiaojiInfo(){
            Fai.http.post("hdSale_h.jsp?cmd=getHdIntentList", "", false).then(result => {
                if (result.success) {
                    this.biaojiDialog.intentList = result.data;
                }
            });
            Fai.http.post("hdSale_h.jsp?cmd=getHdReasonList", "", false).then(result => {
                if (result.success) {
                    this.biaojiDialog.reasonList = result.data;
                }
            });
		},

		//标记对话框
		showBiaojiBox(aid){
            this.biaojiDialog.show = true;
            this.biaojiDialog.aid = aid;
		},

		//标记窗口关闭
        biaojiClose(){
            this.biaojiDialog.show = false;
            this.biaojiDialog.aid = '';
            this.biaojiDialog.reason = '';
            this.biaojiDialog.intent = '';
            this.biaojiDialog.talkNextTime = '';
            this.biaojiDialog.intentLevel = "";
            this.biaojiDialog.corpMark = "";
		},
		//购买产品窗口关闭
		buyProductDialogClose(){
			this.buyProductDialog.show = false;
		},
		
        closeAidDialog(){
        	this.aidMsgDialog.index = '';
        	this.aidMsgDialog.oldIndex='';
        	//console.log("aid index = ",this.aidMsgDialog.index);
		},

		//提交标记表单
        submitBiaoji(){
            let mark = this.biaojiDialog.corpMark;
            if (typeof mark == "undefined" || mark == null || mark == ""){
                this.$message({
                    type: 'warning',
                    message: '备注不能为空!'
                });
                return;
            }
			
			let intent = this.biaojiDialog.intent;
			
            if (typeof intent != "undefined" && intent != null && intent != ""&& intent==5){
                let reason = this.biaojiDialog.reason;
				if (typeof reason == "undefined" || reason == null || reason == ""){
					this.$message({
						type: 'warning',
						message: '原因不能为空'
					});
					return;
                }
            }
			
		

            var arg = {
                "cmd":"setHDReason",
				"aid": this.biaojiDialog.aid,
				"reason":this.biaojiDialog.reason,
                "intent":this.biaojiDialog.intent,
				"talkNextTime":Fai.tool.dateFormatter(this.biaojiDialog.talkNextTime,"yyyy-MM-dd HH:mm:ss"),
				"intentLevel":this.biaojiDialog.intentLevel,
				"corpMark":this.biaojiDialog.corpMark,
				"remindContent":this.biaojiDialog.remindContent,
            }

            let url = "/ajax/hdSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
            	//console.log("talkNextTime = "+this.biaojiDialog.talkNextTime);
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: response.body.msg
                    });
					//更新数据
                    this.getCorpInfo(this.biaojiDialog.aid);
                    //this.getDataListByArgs();
                    //改备注
					for(var i = 0;i < this.dataList.length;i++){
						if(this.dataList[i].aid == this.biaojiDialog.aid){
                            this.dataList[i].mark = this.biaojiDialog.corpMark;
                            this.dataList[i].intent = this.getIntentName(this.biaojiDialog.intent);
						}
					}
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
                this.biaojiClose();
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '网络错误，请重试或联系管理员!'
                });
            });
		},
		
		//查看购买产品
        showBuyProduct(){
			this.buyProductDialog.show = true;
		},

		//aid弹窗切换aid pageData:{
        //currentPage:1,
        //total:0,
        //size:20,
   // },
        changeAid(opt,aid){
			let index = -1;
			let listLength = this.pageData.size -1;
            let totalPage = parseInt(this.pageData.total / this.pageData.size) + 1;

            //如果不是按照更新时间排序就可以用这方法，获取当前aid所在index
        	for(var i = 0;i < this.dataList.length;i++){
                if(this.dataList[i].aid == aid){
					index = i;
					this.aidMsgDialog.index= index +1 ;
                }
            }
           // if((this.form.sortBy.value==-1 || this.form.sortBy.value==0) && this.aidMsgDialog.oldIndex !=''){
        	//     if(this.form.sortBy.value==-1){//倒序
        	//     	if(opt=='up'){
           //      		index=this.aidMsgDialog.oldIndex+1;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex-1;
           //      	}else if(opt='down'){
           //      		index=this.aidMsgDialog.oldIndex;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex+1;
           //      	}
           //      	this.aidMsgDialog.index=index+1;
        	//     }else if(this.form.sortBy.value==0){
        	//     	if(opt=='up'){
           //      		index=this.aidMsgDialog.oldIndex;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex-1;
           //      	}else if(opt='down'){
           //      		index=this.aidMsgDialog.oldIndex-1;
           //      		this.aidMsgDialog.oldIndex=this.aidMsgDialog.oldIndex+1;
           //      	}
        	//     	this.aidMsgDialog.index=index+1;
        	//     }
           //
           //  }else{
           //  	//如果不是按照更新时间排序并且没有提交过备注的就可以用这方法，获取当前aid所在index
           //  	for(var i = 0;i < this.dataList.length;i++){
           //          if(this.dataList[i].aid == aid){
    		// 			index = i;
    		// 			this.aidMsgDialog.index= index +1 ;
           //          }
           //      }
           //  }
            console.log(index);
            console.log(listLength);

            //上一个aid
            if(opt == 'up'){
            	if(index == 0){//判断是不是第一页，是的话弹窗提示，不是的话获取上一页的最后一个
					if(this.pageData.currentPage == 1){
                        this.$message({
                            type: 'warning',
                            message: '已经是第一个了!'
                        });
					}else{//翻页
                        //this.pageData.currentPage -= 1;
                        this.$message({
                            type: 'warning',
                            message: '这是本页第一个了!'
                        });
					}
				}else if(index == -1){//翻页后找不到aid将index谁为
                    this.getCorpInfo(this.dataList[listLength].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[listLength].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[listLength].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[listLength].optOneWeekCount;
					this.aidMsgDialog.firstCreateGameTime = this.dataList[listLength].firstCreateGameTime;
				}else{
            		this.getCorpInfo(this.dataList[index-1].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[index-1].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[index-1].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[index-1].optOneWeekCount;
					this.aidMsgDialog.firstCreateGameTime = this.dataList[index-1].firstCreateGameTime;
				}
			}


			//下一个aid
			if(opt == 'down'){
				if(this.pageData.currentPage == totalPage ){ //最后一页情况
                    let tempIndex = index + 1;
					if(this.dataList.length == tempIndex){
                        this.$message({
                            type: 'warning',
                            message: '已经是最后一个了!'
                        });
					}else{
                        this.getCorpInfo(this.dataList[index+1].aid);
                        this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                        this.aidMsgDialog.loginOneWeekCount = this.dataList[index+1].loginOneWeekCount;
                        this.aidMsgDialog.optOneWeekCount = this.dataList[index+1].optOneWeekCount;
						this.aidMsgDialog.firstCreateGameTime = this.dataList[index+1].firstCreateGameTime;
					}
				}else if(index == listLength){//最后一个情况
					//获取下一页数据
                   // this.pageData.currentPage += 1;销售说不需要自动翻页
                    this.$message({
                        type: 'warning',
                        message: '已经是本页最后一个啦!'
                    });
				}else{
                    this.getCorpInfo(this.dataList[index+1].aid);
                    this.aidMsgDialog.aidUrl = this.dataList[index+1].aidUrl;
                    this.aidMsgDialog.loginOneWeekCount = this.dataList[index+1].loginOneWeekCount;
                    this.aidMsgDialog.optOneWeekCount = this.dataList[index+1].optOneWeekCount;
					this.aidMsgDialog.firstCreateGameTime = this.dataList[index+1].firstCreateGameTime;
				}
			}

		},

		getIntentName(intent){
            switch(intent)
            {
                case 5:
                    return "无意向";
                    break;
                case 1:
                    return "有意向";
                    break;
                case 6:
                    return "未接通";
                    break;
                case 7:
                    return "待跟进";
                    break;
                case 0:
                    return "空";
                    break;
                case 8:
                    return "仅建站";
                    break;
                default:
                    return "未知";
            }
		},

		handleRemove(data){
            this.editReceiveTimeData.fileId = '';
            this.backToAkuData.fileId = '';
		},
		beforeFileUpload: function (file) {
			if (file.size > 5 * 1024 * 1024) {
				this.$message({
					type: "error",
					message: "单个文件超过5MB！"
				});
				return false;
			}
		},
		fileUploadSuccess: function (response) {
			if(response.success){
				this.$message({
					type: "success",
					message: "上传成功!"
				});
				this.editReceiveTimeData.fileId = response.fileId;
                this.backToAkuData.fileId = response.fileId;
			}
		},
		fileUploadError: function (err) {
			var _this = this;
			this.$message({
				type: "error",
				message: "系统繁忙，请稍后重试!"
			});
		},
		sendMessag(index,dataList){
			this.message.messageList = [];
			var single='';
			//console.log(this.sendMessag.index);
			if( typeof  this.sendMessag.index != "undefined"){
				let index = this.aidMsgDialog.index;
				single = dataList[index];
			}else{
				single=dataList[index];
			}
			var sendTime = single.sendTime;
			if(sendTime){
				if((new Date(sendTime).getTime())/1000 > Fai.tool.getDateTimeStart(new Date())){
					this.$message({
						type: 'warning',
						message: '该aid今天已经发送过短信!'
					});
					return;
				}
			}
			this.message.messageList.push(single);
			this.message.singleMobile = single.mobile;
			this.dialogVisible = true;//展示弹窗
		},
		getMessageModelList(){
			var arg = {
				"cmd":"getMessageModelList",
			}
			Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
				this.message.messageModelList = response.data.dataList;
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		batchSendMessag(){
			otherBtn = true;
			if(isNaN(element2count[this.tab]['batchMessange'])){
				element2count[this.tab]['batchMessange'] = 1;
			}else{
				element2count[this.tab]['batchMessange'] += 1;
			}
			this.dialogVisible = true;

			this.message.singleMobile = "";
			if(this.message.messageList == undefined ||  this.message.messageList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
			}

		},
	 /*batchTurnStatus(type){
			if(this.message.messageList == undefined ||  this.message.messageList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
			}
			if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
			
			var aidSize = this.aidList.dataList.length;  //aid长度
			var aidList = [];//组装参数
			if(messageSize>=1&&this.message.singleMobile==""){//证明是多选
				for(var index = 0;index<messageSize;index++){
					var message = this.message.messageList[index];
					var aid = message.aid;
					aidList.push(aid);
				}
			}
			
			if(aidSize>=1){//证明是多选
				for(var index = 0;index<messageSize;index++){
					var message = this.aidList.dataList[index];
					var aid = message.aid;
					aidList.push(aid);
					alert(aid);
				}
			}
			
			
			console.log("aidList="+aidList);
			var arg = {
				"cmd":"batchTurnStatus",
				"type":type,
				"aidList":JSON.stringify(aidList),
				"status":this.tab,
			}
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				if(response.data.success){
					this.$message({
						type: 'success',
						message: response.data.msg
					});
					this.getDataListByArgs();
				}else{
					this.$message({
						type: 'warning',
						message: response.data.msg,
					});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		}
	  },*/
		
		confirmBatch(type){
			if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
		   }else{
			    var that = this;
				this.$confirm('确定批量释放到【公海库】吗?', '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '取消',
					type: 'warning'
				  }).then(() => {
					  that.batchTurnStatus(type);
				  }).catch(() => {
					this.$message({
					  type: 'info',
					  message: '已取消删除'
					});          
				  });
		   }
		},
		
		
		
		
		//批量改变库状态
		batchTurnStatus(type){
			otherBtn = true;
			var configKey = '';
			if(type == 'release'){
				configKey = 'batchRelease';
			}else if(type == 'changeToA' || type == 'changeNowToA'){
				configKey = 'batchToA';
			}else if(type == 'changeToB'){
				configKey = 'batchToB';
			}

			if(isNaN(element2count[this.tab][configKey])){
				element2count[this.tab][configKey] = 1;
			}else{
				element2count[this.tab][configKey] += 1;
			}

		  if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
				this.$message({
					type: 'warning',
					message: "请至少选择一个客户",
				});
				return;
		   }
			//var messageSize = this.message.messageList.length;  //aid长度
			var aidSize = this.aidList.dataList.length;  //aid长度
			var aidList = [];//组装参数
			/*if(messageSize>=1&&this.message.singleMobile==""){
				for(var index = 0;index<messageSize;index++){
					var message = this.message.messageList[index];
					var aid = message.aid;
					aidList.push(aid);
				}
			}*/
			if(aidSize>=1){//证明是多选
				for(var index = 0;index<aidSize;index++){
					var message = this.aidList.dataList[index];
					var aid = message.aid;
					aidList.push(aid);
				}
			}
			//console.log("aidList="+aidList);
			//console.log(this.tab + " : " + type);
			var arg = {
				"cmd":type=='release'?"batchRelease":"batchTurnStatus",
				"type":type,
				"aidList":JSON.stringify(aidList),
				"status":this.tab,
			}
			//console.log("arg = "+arg);
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				if(response.data.success){
					this.$message({
						type: 'success',
						message: response.data.msg
					});

					otherBtn = true;
					invalidSearch = true;
					this.getDataListByArgs();
					invalidSearch = false;
				}else{
					this.$message({
						type: 'warning',
						message: response.data.msg,
					});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		//aid弹窗释放到公海库按钮,复用ajax的
		releaseRecourse(aid){
			var aidList = [];//组装参数
			aidList.push(aid);
			this.$confirm('确定释放该客户?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":"batchRelease",
					"aidList":JSON.stringify(aidList),
					"status":this.tab,
				}
				console.log("aidList="+aidList);
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					this.$message({
					type: 'success',
					message: response.data.msg
					});

					invalidSearch = true;
					this.getDataListByArgs();
					invalidSearch = false;
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消删除'
				});          
			  });
		},
		//领取公海库资源到A库
		batchReceivePreSale(){
			  //console.log(this.aidList.dataList);
			  if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
					this.$message({
						type: 'warning',
						message: "请至少选择一个客户",
					});
					return;
			   }
			var aidSize =this.aidList.dataList.length;  //aid长度
			var aidList = [];//组装参数
			if(aidSize>=1){//证明是多选
				if(aidSize>10){
					this.$message({
						type: 'warning',
						message: '公海库一次批量领取不得超过10条!'
						});
					return;
				}
				for(var index = 0;index<aidSize;index++){
					var message = this.aidList.dataList[index];
					var aid = parseInt(message.aid);
					aidList.push(aid);
				}
			}
			console.log("aidList="+aidList);
			var arg = {
				"cmd":"batchReceivePreSale",
				"aidList":JSON.stringify(aidList),
				"status":this.tab,
			}
			//console.log("arg = "+arg);
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				if(response.data.success){
					this.$message({
						type: 'success',
						message: response.data.msg
					});
					this.getDataListGh();
				}else{
					this.$message({
						type: 'warning',
						message: response.data.msg,
					});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		
		},
		
		handleSelectionChange(val){
			console.log("val = "+val);
			var data = [];
			var aidData = [];
			for(var i=0;i<val.length;i++){
				var single = val[i];
				var sendTime = single.sendTime;
				aidData.push(single);
				if(sendTime){
					var sendTimeSecond = new Date(sendTime).getTime() / 1000;
					var date = new Date();
					var today = Fai.tool.getDateTimeStart(new Date());
					var lastTwoDay = new Date(date.getFullYear(), date.getMonth() + 1, -1).getTime() / 1000;
					if(sendTimeSecond > today){//当天已发送过短信
						this.$message({
							type: 'warning',
							message: "aid:"+single.aid+" 当天已发送过短信",
						});
						continue;
					}
					if( today < lastTwoDay && sendTimeSecond > today - 3 * 24 * 60 * 60   ){//3天内发过短信的不可以再发，月底最后2天除外
						this.$message({
							type: 'warning',
							message: "aid:"+single.aid+" 3天内已发送过短信",
						});
						continue;
					}				
				}
				data.push(single);
			}
			this.message.messageList = data;
			this.aidList.dataList = aidData;
		},
		handleMessage(send) {//关闭发送短信的弹窗
			this.dialogVisible = false;
			if(send){//如果确定
				if(this.message.messageModel == ""){
					this.$message({
						type: 'warning',
						message: "尚未选择短信模板！",
					});
					return;
				}
				
				var messageSize = this.message.messageList.length;//发送列表Length
				if(messageSize<=0){
					this.message.singleMobile = "";
					return;
				}
				var realMessageList = [];//组装参数
				if(messageSize>1&&this.message.singleMobile==""){//证明是多选
					for(var index = 0;index<messageSize;index++){
						var message = this.message.messageList[index];
						if(message.mobile==""){
							continue;
						}
						var item = {};
						item.aid = message.aid;
						item.mobile = message.mobile;
						realMessageList.push(item);
					}
				}else{//单个发送
					var message = this.message.messageList[0];
					var item = {};
					item.aid = message.aid;
					item.mobile = this.message.singleMobile;
					realMessageList.push(item);
				}
				var arg = {
					"cmd":"batchSendMessage",
					"aidList": JSON.stringify(realMessageList),
					"smsId":this.message.messageModel,
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					if(response.data.success){
						this.$message({
							type: 'success',
							message: response.data.msg
						});
					}else{
						this.$message({
							type: 'warning',
							message: response.data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			}
		},
		changeIntent(aid,intent,salesAcct,mark){
			this.$confirm('确定修改?', '提示', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			  }).then(() => {
				var arg = {
					"cmd":"setInfo",
					"aid":aid,
					"intent":intent,
					"salesAcct":salesAcct,
					"mark":mark,
					"type":"intent",
				}
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					if(response.data.success){
						this.$message({
							type: 'success',
							message: '修改成功!'
						});

						invalidSearch = true;
						this.getDataListByArgs();
						invalidSearch = false;
					}else{
						this.$message({
							type: 'warning',
							message: response.data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
			
			  }).catch(() => {
				this.$message({
				  type: 'info',
				  message: '已取消修改'
				});          
			  });
		},
		getMessageList(){
			var arg = {
				"cmd":"getSaleList",
				"pageNo":this.message.page,
			}
			Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
				this.message.messageModelList = response.data.dataList;
				this.dialogVisible = true;//展示弹窗
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		getDefList(){
			var arg = {
				"cmd":"getPageDef",
				"key":"hdSaleList"
			}
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
				             
				var hdSaleList = response.data;
				console.log(hdSaleList);
				this.form.cdVersion = hdSaleList.cdVersion;
				this.form.hdVersion = hdSaleList.hdVersion;
				this.form.regBiz = hdSaleList.regBiz;
				this.form.siteVersion = hdSaleList.siteVersion;
				this.form.goal = hdSaleList.goal;
				this.form.intent = hdSaleList.intent;
				this.form.ta = hdSaleList.taList;
				this.form.sortBy = hdSaleList.sortBy;
				this.form.saleGroup = hdSaleList.saleGroup;
				this.form.tag = hdSaleList.tag;
				this.form.coupon = hdSaleList.coupon;
				this.form.intentLevel = hdSaleList.intentLevel;
				this.form.wxVersion=hdSaleList.wxVersion;

				element2default = hdSaleList.element2default;
				//遍历其选项得json
				for(var i in hdSaleList.element2value){
					this.showCustomPagelog.data.push({"key":i,"label":hdSaleList.element2value[i]});
				}
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		getOtherLibList(param){
            this.changStyle(param);
			this.libTab = param;
            this.getDataListByArgs(param);
		},
		changStyle(param){
			this.textStyle_10.color = "";
            this.textStyle_20.color = "";
            this.textStyle_28.color = "";
            this.textStyle_ban_msg.color = "";
            this.textStyle_follow.color = "";

            if(param == 'noContact10Day'){
                this.textStyle_10.color = "grey";
            }else if (param == 'noContact20Day'){
                this.textStyle_20.color = "grey";
            }else if (param == 'noContact28Day'){
                this.textStyle_28.color = "grey";
            }else if (param == 'follow'){
                this.textStyle_follow.color = "grey";
            }else if (param == 'banSendMsgAid'){
                this.textStyle_ban_msg.color = "grey";
            }

		},
		//数据埋点记录次数
		dataPointCount(urlParam){
			//不是切换库或者其他非查询按钮引发的查询，才算上有效的查询
			if(!tabChange && !invalidSearch){
				//遍历筛选项的值是否与默认值一致，不一致说明该值用过查询，则记录次数+1
				//最终传给后端的都是有使用过筛选的表单
				for(let key in element2default){
					if(urlParam[key] != element2default[key]){
						if(isNaN(element2count[this.tab][key])){
							element2count[this.tab][key] = 1;
						}else{
							element2count[this.tab][key] += 1;
						}
					}
				}
				element2count[this.tab]['search'] += 1;
			}

			//alert(JSON.stringify(element2count));

		},
		getDataListByArgs(param){
			var excel = "excel"===param;
			var urlParam = {
				cmd:'getHdSaleList',
				currentPage:this.pageData.currentPage,
                labelIndex:this.tab,
				aid:this.form.aid,
				mark:this.form.mark,
				joinNumStart:this.form.joinNumStart,
				joinNumEnd:this.form.joinNumEnd,
				excel:excel,
				regTimeCheck:this.form.regTime.check,
				regTimeStart:Fai.tool.getDateTimeStart(this.form.regTime.start),
				regTimeEnd:Fai.tool.getDateTimeEnd(this.form.regTime.end),
				lastUpdateTimeCheck:this.form.lastUpdateTime.check,
				lastUpdateTimeStart:Fai.tool.getDateTimeStart(this.form.lastUpdateTime.start),
				lastUpdateTimeEnd:Fai.tool.getDateTimeEnd(this.form.lastUpdateTime.end),
				receiveTimeCheck:this.form.receiveTime.check,
				receiveTimeStart:Fai.tool.getDateTimeStart(this.form.receiveTime.start),
				receiveTimeEnd:Fai.tool.getDateTimeEnd(this.form.receiveTime.end),
				nextTalkTimeCheck:this.form.nextTalkTime.check,
				nextTalkTimeStart:Fai.tool.getDateTimeStart(this.form.nextTalkTime.start),
				nextTalkTimeEnd:Fai.tool.getDateTimeEnd(this.form.nextTalkTime.end),

				sendMessageTimeCheck:this.form.sendMessageTime.check,
				noSendMessageCheck:this.form.sendMessageTime.noSendCheck,
				sendMessageTimeStart:Fai.tool.getDateTimeStart(this.form.sendMessageTime.start),
				sendMessageTimeEnd:Fai.tool.getDateTimeEnd(this.form.sendMessageTime.end),
				
				firstCreateGameTimeCheck:this.form.firstCreateGameTime.check,
				firstCreateGameTimeStart:Fai.tool.getDateTimeStart(this.form.firstCreateGameTime.start),
				firstCreateGameTimeEnd:Fai.tool.getDateTimeEnd(this.form.firstCreateGameTime.end),
				
				
				needloginTime:this.form.loginTime.check,
				loginTimeStart:Fai.tool.getDateTimeStart(this.form.loginTime.start),
				loginTimeEnd:Fai.tool.getDateTimeEnd(this.form.loginTime.end),
				ta:this.form.ta.value,
				goal:this.form.goal.value,
				
				siteVersion:this.form.siteVersion.value,
				hdVersion:this.form.hdVersion.value,
				cdVersion:this.form.cdVersion.value,
				wxVersion:this.form.wxVersion.value,
				regBiz:this.form.regBiz.value,
				sortBy:this.form.sortBy.value,
				oneWeekSortBy:this.form.oneWeekSortBy.value,
				saleGroup:this.form.saleGroup.value,
				receiveSale:this.form.receiveSale.value,
				tag:this.form.tag.value,
				timeRange:this.form.timeRange.value,
				coupon:this.form.coupon.value,
				intentLevel:this.form.intentLevel.value,
				intent:this.form.intent.value,
				size:this.pageData.size,
				business:this.form.business.value,
				hasOpenYk:this.form.hasOpenYk.value,
				hasCorpName:this.form.hasCorpName.value,
//				isAward:this.form.isAward.value,
				A_BLib:this.form.A_BLib.value,
				isWeekendResource:this.form.isWeekendResource.value,
				allotType:this.form.allotType.value,
                args: this.form.args.value,
                focusAndStopSend:this.libTab
			}

			//将请求的参数与配置文件获取的默认值作对比
			this.dataPointCount(urlParam);

			if(excel){	
				window.location.href =  '/ajax/hdSale_h.jsp'+Fai.tool.parseJsonToUrlParam(urlParam,true);
				
			}else{
				//console.log("args = "+JSON.stringify(urlParam));
				Vue.http.post("/ajax/hdSale_h.jsp", urlParam, {emulateJSON:true}).then(response => {
					// success
					this.dataList = response.data.dataList;
					this.logicImgSrc = response.data.logicImgSrc;
					this.form.receiveSale.labelList =  response.data.saleList;
					this.pageData.total = response.data.total;
					//预览最后一条备注
					for(var i=0;i<response.data.dataList.length;i++){
						if(response.data.dataList[i].mark.length>0){
							//this.dataList[i].mark=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							var markTmp=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							if(markTmp.indexOf("-")>0){
								markTmp=markTmp.substring(0,markTmp.indexOf("-"));
							}
							 if(markTmp.charAt(0)==('【')){
								this.dataList[i].mark=markTmp.substring(markTmp.indexOf("】")+1);
							}else{
								this.dataList[i].mark=markTmp;
							}
							if(response.data.dataList[i].intent =='无意向'){
								if(markTmp.indexOf("。")>0){
									markTmp = markTmp.substring(markTmp.indexOf("。")+1);
									this.dataList[i].mark=markTmp;
								}
							}
						}
					}
					
					
					if( this.tab===1){
						this.tabNamePerson = "当月库("+this.pageData.total+")";
					}
					else if(this.tab===2){
						this.tabNameA = "A库("+this.pageData.total+")";
					}
                    else if(this.tab===3){
						this.tabNameB = "B库("+this.pageData.total+")";
					}else if(this.tab ===4){
						this.tabNameDeal = "成交库("+this.pageData.total+")";
					}
				}, response => {
					console.info("err");
				});
                
			}
		},
		
		getDataListGh(param){

			var excel = "excel"===param;
			var urlParam = {
				cmd:'getHdGhList',
				currentPage:this.pageGhData.currentPage,
                labelIndex:this.tab,
				aid:this.form.aid,
				joinNumStart:this.form.joinNumStart,
				joinNumEnd:this.form.joinNumEnd,
				regTimeCheck:this.form.regTime.check,
				regTimeStart:Fai.tool.getDateTimeStart(this.form.regTime.start),
				regTimeEnd:Fai.tool.getDateTimeEnd(this.form.regTime.end),
				firstCreateGameTimeCheck:this.form.firstCreateGameTime.check,
				firstCreateGameTimeStart:Fai.tool.getDateTimeStart(this.form.firstCreateGameTime.start),
				firstCreateGameTimeEnd:Fai.tool.getDateTimeEnd(this.form.firstCreateGameTime.end),
				
				ta:this.form.ta.value,
				goal:this.form.goal.value,
				
				siteVersion:this.form.siteVersion.value,
				hdVersion:this.form.hdVersion.value,
				cdVersion:this.form.cdVersion.value,
				oneWeekSortBy:this.form.oneWeekSortBy.value,
				sortBy:this.form.sortBy.value,
				size:this.pageGhData.size,
				hasCorpName:this.form.hasCorpName.value,
                args: this.libTab
			}
			if(excel){	
				window.location.href = '/ajax/hdSale_h.jsp'+Fai.tool.parseJsonToUrlParam(urlParam,true);
				
			}else{
				
				Vue.http.post("/ajax/hdSale_h.jsp", urlParam, {emulateJSON:true}).then(response => {
					// success
					this.dataList = response.data.dataList;
					this.logicImgSrc = response.data.logicImgSrc;
					this.pageGhData.total = response.data.total;
					console.log("total = %s",this.pageGhData.total);
					//预览最后一条备注
					if(response.data.dataList != null){
					for(var i=0;i<response.data.dataList.length;i++){
						if(response.data.dataList[i].mark.length>0){
							//this.dataList[i].mark=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							var markTmp=response.data.dataList[i].mark.substring(response.data.dataList[i].mark.indexOf("】")+1);
							if(markTmp.indexOf("-")>0){
								markTmp=markTmp.substring(0,markTmp.indexOf("-"));
							}
							//console.log("size:"+this.dataList[i].aid);
							 if(markTmp.charAt(0)==('【')){
								this.dataList[i].mark=markTmp.substring(markTmp.indexOf("】")+1);
							}else{
								this.dataList[i].mark=markTmp;
							}
							if(response.data.dataList[i].intent =='无意向'){
								if(markTmp.indexOf("。")>0){
									markTmp = markTmp.substring(markTmp.indexOf("。")+1);
									this.dataList[i].mark=markTmp;
								}
							}
						}
					 }
				   }
				}, response => {
					console.info("err");
				});
                
			}
		
		},
		
		handleSizeChange(){

		},
		handleCheckedNoSendSmsChange(noSendCheck){
			if(noSendCheck && this.form.sendMessageTime.check){
				this.form.sendMessageTime.check = false
			}
		},
		handleCheckedSendSmsChange(sendCheck){
			if(sendCheck && this.form.sendMessageTime.noSendCheck){
				this.form.sendMessageTime.noSendCheck = false
			}
		},
		test(){
			console.log("test");
		},
		// handleCurrentChange(val){
		// 	console.info(val);
		// },
		
		
		//获取销售账号和电话信息
		getSaleMsg(){			
			//post获取销售信息 hdSale_h方法-->getPersonTelInfo
			var arg = {
				"cmd":"getPersonTelInfo",
			}
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				var data = response.data;
				console.log(data);
				
				var telInfo = data[data.length-1]						//个人信息
				this.personInfo.nickName = telInfo.nickName;
				this.personInfo.phone = telInfo.phone;
				
				data.pop();
				this.personInfo.modelInfoList = data									//短信模板列表
				
				
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		
		//报错个人电话信息
		setPreSaleTel(){
			var arg = {
				"cmd":"setPreSale",
				"name":this.personInfo.nickName,
				"phone":this.personInfo.phone,
			}
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				console.log(response.data.msg);
				if(response.data.msg == '设置成功'){
					this.$message({
					type: 'warning',
					message: '设置成功!'
					});
				}else{
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				}
				
								
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
			
		},
		
		reasonStatTabOnClick(){							//未购买原因标签点击事件，获取销售列表
			var arg = {
				"cmd":"getSidList",
			}
			//请求销售列表
			Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {	
				this.reasonStat.labelList = this.reasonStat.labelList.concat(response.data);
				this.reasonStat.hasGet = 1;					
			}, response => {
				this.$message({
				type: 'warning',
				message: '系统错误!'
				});
			});
		},
		
		getReasonStat(type){							//未购买统计&表格下载
			var arg = {
				"cmd":"getReasonStat",
				"regBegTime":Fai.tool.getDateTimeStart(this.reasonStat.regTime.start),
				"regEndTime":Fai.tool.getDateTimeEnd(this.reasonStat.regTime.end),
				"salesAcct":this.reasonStat.value,
				"type":type
			}
			if(type != ""){
				window.location.href =  '/ajax/hdSale_h.jsp'+Fai.tool.parseJsonToUrlParam(arg,true)
			
			}else{
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {					
					let data = response.data;
					this.reasonStat.totolList.intentList = data['intentList'];
					this.reasonStat.totolList.markList = data['markList'];
						
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});		
			}
			
		},
		//设置企微提醒时间
		addQWRemind(aid,QWRemindTime,QWRemindContent){
			var arg ={
			     "cmd":"addQWRemind",
			     "aid":aid,
			     "QWRemindTime":Fai.tool.dateFormatter(QWRemindTime.getTime(),"yyyy-MM-dd HH:mm:ss"),
			     "QWRemindContent":QWRemindContent
			}
			let url = "/ajax/hdSale_h.jsp";
			Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: '设置成功!'
                    });
                   
				}else{
                    this.$message({
                        type: 'warning',
                        message:response.body.msg
                    });
				}
				 this.aidMsgDialog.QWRemindContent = "";
				 this.aidMsgDialog.QWRemindTime=new Date();
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '添加失败!'
                });
            });
		},
		banSendMsg(aid,flag){
			var arg={
					"cmd":"banSendMsg",
					"aid":aid,
					"flag":flag,
			}
			let url = "/ajax/hdSale_h.jsp";
            Vue.http.post(url, arg, {emulateJSON:true}).then(response => {
				if(response.body.success == true){
                    this.$message({
                        type: 'success',
                        message: response.body.msg
                    });
                    this.getCorpInfo(aid);
				}else{
                    this.$message({
                        type: 'warning',
                        message: response.body.msg
                    });
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '关注失败!'
                });
            });
		},
		batchCouponStopStatus(type,stopType){
			otherBtn = true;
			var configKey = '';
			if(type == 'open'){
				configKey = 'batchNoBan';
			}else if(type == 'stop' && stopType == 'mtd1'){
				configKey = 'batchBan15Day';
			}else if(type == 'stop' && stopType == 'mtd2'){
				configKey = 'batchBan';
			}

			if(isNaN(element2count[this.tab][configKey])){
				element2count[this.tab][configKey] = 1;
			}else{
				element2count[this.tab][configKey] += 1;
			}


			var aidSize = this.aidList.dataList.length;  //aid长度
			var aidList = [];//组装参数
			 if(aidSize>=1){//证明是多选
					for(var index = 0;index<aidSize;index++){
						var message = this.aidList.dataList[index];
						var aid = message.aid;
						aidList.push(aid);
					}
				}
			 if(this.aidList.dataList == undefined ||  this.aidList.dataList.length == 0){
					this.$message({
						type: 'warning',
						message: "请至少选择一个客户",
					});
					return;
			   }else{
				   var con=confirm(aidList+(stopType=="mtd1"?"          15天自动解禁":"        手动解禁"));
				   if(con==false){
					   return;
				   }
			   }
				//var messageSize = this.message.messageList.length;  //aid长度
				
				/*if(messageSize>=1&&this.message.singleMobile==""){
					for(var index = 0;index<messageSize;index++){
						var message = this.message.messageList[index];
						var aid = message.aid;
						aidList.push(aid);
					}
				}*/
				/*if(aidSize>=1){//证明是多选
					for(var index = 0;index<aidSize;index++){
						var message = this.aidList.dataList[index];
						var aid = message.aid;
						aidList.push(aid);
					}
				}*/
				//console.log("aidList="+aidList);
				//console.log(this.tab + " : " + type);
				var arg = {
					"cmd":type=='open'?"batchCouponOpen":"batchCouponStop",
					"type":type,
					"aidList":JSON.stringify(aidList),
					"status":this.tab,
					"method":stopType=='mtd1'?"mth1":"mth2",
				}
				//console.log("arg = "+arg);
				Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
					if(response.data.success){
						this.$message({
							type: 'success',
							message: response.data.msg
						});

						otherBtn = true;
						invalidSearch = true;
						this.getDataListByArgs();
						invalidSearch = false;
					}else{
						this.$message({
							type: 'loading',
							message: response.data.msg,
						});
					}
				}, response => {
					this.$message({
					type: 'warning',
					message: '系统错误!'
					});
				});
		},

		// 获取无效库数据
		getInvalidDataList: function (isExport) {
			var arg = this.invalidForm;
            arg.cmd = "getInvalidDataList";
            arg.currentPage = this.pageInvalid.currentPage;
            
            if (isExport) {
            	arg.isExport = true;
                window.location.href = '/ajax/hdSale/hdSaleInvalid_h.jsp'+Fai.tool.parseJsonToUrlParam(arg, true);
            }else {
                Vue.http.post("/ajax/hdSale/hdSaleInvalid_h.jsp", arg, {emulateJSON: true}).then(response => {
                    if (response.data.success) {
                        this.dataList = response.data.list;
                        this.form.receiveSale.labelList = response.data.salesList;
                        this.pageInvalid.total = response.data.total;
                    } else {
                        this.$message.error(response.data.msg);
                    }
                }, response => {
                    this.$message({
                        type: 'warning',
                        message: '系统错误!'
                    });
                });
            }
        },
		showInvalidDialog: function () {
			this.isShowInvalidDialog = true;
		},
		// 转移进入无效库
		transToInvalid: function () {
            var arg = {
                "cmd":"transToInvalid",
				aid: this.aidMsgDialog.data.aid,
				invalidType: this.invalidType,
				invalidMark: this.invalidMark
            }
            Vue.http.post("/ajax/hdSale/hdSaleInvalid_h.jsp", arg, {emulateJSON:true}).then(response => {
            	if (response.data.success) {
                    this.$message.success(response.data.msg);
                    this.isShowInvalidDialog = false;
                } else {
                    this.$message.error(response.data.msg);
				}
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
        },
		// 管理员批量释放
        releaseInvalid: function () {
            var invalidAidList = []
			var tmpList = this.aidList.dataList;
            if (tmpList) {
            	for(var i = 0; i < tmpList.length; i++){
            		invalidAidList.push(tmpList[i].aid);
				}
			}
            console.log(invalidAidList);
            
            var arg = {
            	cmd: 'releaseInvalid',
            	aidList: JSON.stringify(invalidAidList),
			}

            Vue.http.post("/ajax/hdSale/hdSaleInvalid_h.jsp", arg, {emulateJSON: true}).then(response => {
                if (response.data.success) {
                    this.$message.success(response.data.msg);
                    this.getInvalidDataList(false);
                } else {
                    this.$message.error(response.data.msg);
                }
            }, response => {
                this.$message({
                    type: 'warning',
                    message: '系统错误!'
                });
            });
        },
		changePage: function (val) {
			this.pageInvalid.currentPage = val;
			this.getInvalidDataList(false);
        }
	}
});

window.addEventListener("beforeunload", function(event) {
	//如果一次查询都没有
	if(element2count["1"]['search'] <=0 && element2count["2"]['search'] <= 0 && element2count["3"]['search'] <=0 && element2count["4"]['search'] <= 0){
		//并且没有使用除了查找之外的按钮
		if(!otherBtn){
			return;
		}
	}

	//步骤一:创建异步对象
	var ajax = new XMLHttpRequest();
	//步骤二:设置请求的url参数,参数一是请求的类型,参数二是请求的url,可以带参数,动态的传递参数starName到服务端
	ajax.open('get','/ajax/hdSale_h.jsp?cmd=burialStatistics&element2count='+JSON.stringify(element2count));
	//步骤三:发送请求
	ajax.send();

});

// //键盘监听
// document.onkeydown=function(event){
//     var e = event || window.event || arguments.callee.caller.arguments[0];
//     //37 , 39
//     console.log(e.keyCode);
//     if(e.keyCode == 39){
//         // hdSale.pageData.currentPage += 1;
// 	}
//
// };


// window.addEventListener("storage", function(event) {
// 	if(event.key == "preSale-hd"){
// 		hdSale.getDataListByArgs();
// 		console.log("success");
// 	}else{
// 		return;
// 	}
// }, false);
