<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>

<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="fai.cli.*" %>
<%@ page import="fai.weboss.*"%>

<%!
    private Dao.SelectArg getAchievenmentSelectArg(){
        Dao.SelectArg sltArg = new Dao.SelectArg();
        sltArg.table = "hdSaleAchievement";

        //获取当前月的月头月尾
        Calendar now = Calendar.getInstance();
        now.set(Calendar.DAY_OF_MONTH, 1);
        now.set(Calendar.HOUR, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        String begDate = Parser.parseDateString(now, "yyyy-MM-dd HH:mm:ss");

        now.set(Calendar.DAY_OF_MONTH, now.getActualMaximum(Calendar.DAY_OF_MONTH));
        now.set(Calendar.HOUR, 23);
        now.set(Calendar.MINUTE, 59);
        now.set(Calendar.SECOND, 59);
        String endDate = Parser.parseDateString(now, "yyyy-MM-dd HH:mm:ss");

        sltArg.searchArg.matcher = new ParamMatcher("createTime", ParamMatcher.GE, begDate);
        sltArg.searchArg.matcher.and("createTime", ParamMatcher.LE, endDate);

        return sltArg;
    }


	private Dao.SelectArg getAchievenmentSelectArg(int month){
	    Dao.SelectArg sltArg = new Dao.SelectArg();
	    sltArg.table = "hdSaleAchievement";
	
	    //获取当前月的月头月尾
	    Calendar now = Calendar.getInstance();
	    int currentMonth = now.get(Calendar.MONTH)+1;
	    if(currentMonth != month && month==12 && currentMonth==1){		//跨年问题
	    	now.add(Calendar.YEAR, -1);
	    }
	    now.set(Calendar.MONTH, month-1);
	    now.set(Calendar.DAY_OF_MONTH, 1);
	    now.set(Calendar.HOUR, 0);
	    now.set(Calendar.MINUTE, 0);
	    now.set(Calendar.SECOND, 0);
	    String begDate = Parser.parseDateString(now, "yyyy-MM-dd HH:mm:ss");
	
	    now.set(Calendar.DAY_OF_MONTH, now.getActualMaximum(Calendar.DAY_OF_MONTH));
	    now.set(Calendar.HOUR, 23);
	    now.set(Calendar.MINUTE, 59);
	    now.set(Calendar.SECOND, 59);
	    String endDate = Parser.parseDateString(now, "yyyy-MM-dd HH:mm:ss");
	
	    sltArg.searchArg.matcher = new ParamMatcher("createTime", ParamMatcher.GE, begDate);
	    sltArg.searchArg.matcher.and("createTime", ParamMatcher.LE, endDate);
	
	    return sltArg;
	}
%>

<%!
    //获取所有互动销售的信息
    private FaiList<Param> getAllHdSaleList()throws Exception{
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher("sid", ParamMatcher.GT, 0);
        //获取所有互动销售
        FaiList<Param> saleList = new FaiList<Param>();
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), searchArg, saleList);
        if(rt != Errno.OK){
            Log.logStd("getAllHdSaleList err");
            return null;
        }

        return saleList;
    }

    private String getHdSaleAchievement(HttpServletRequest request)throws Exception{
        Param data = new Param();

        int month = Parser.parseInt(request.getParameter("month"),Calendar.getInstance().get(Calendar.MONTH)+1);
        //获取所有互动销售
        FaiList<Param> saleList = getAllHdSaleList();
        if(saleList == null){
            data.setBoolean("success", false);
            data.setString("msg", "获取销售信息失败");
        }

        FaiList<Integer> sidList = new FaiList<Integer>();
        for(Param p: saleList){
            sidList.add(p.getInt("sid", 0));
        }

        Dao ossDao = null;
        try{
            ossDao = WebOss.getOssBsDao();
            Dao.SelectArg sltArg = getAchievenmentSelectArg(month);

            FaiList<Param> saleAchievementList = ossDao.select(sltArg);
            if(saleAchievementList == null){
                Log.logStd("saleAchievementList is null");
                data.setBoolean("success", false);
                data.setString("msg", "获取销售业绩记录失败");
                return data.toJson();
            }

            data.setBoolean("success", true);
            //为空说明这个月还未设置，返回默认0
            if(saleAchievementList.isEmpty()){
                for(Param p: saleList){
                    p.setInt("goalMoney", 0);
                    p.setDouble("approveMoney", 0.0);
                    p.setInt("approveCount", 0);
                }

            }else{
                Param sid2Achievement = new Param();
                for(Param p: saleAchievementList){
                    Integer sid = p.getInt("sid", 0);
                    sid2Achievement.setParam(sid+"", p);
                }

                for(Param p: saleList){
                    Integer sid = p.getInt("sid", 0);
                    Param saleAchievement = sid2Achievement.getParam(sid+"", new Param());

                    p.setInt("goalMoney", saleAchievement.getInt("goalMoney", 0));
                    p.setDouble("approveMoney", saleAchievement.getDouble("approveMoney", 0.0));
                    p.setInt("approveCount", saleAchievement.getInt("approveCount", 0));
                }
            }

            data.setList("saleList", saleList);

        }finally {
            if(ossDao != null){
                ossDao.close();
            }
        }

        return data.toJson();
    }
%>

<%!
    //设置销售的目标业绩等
    private String setGoalMoney(HttpServletRequest request)throws Exception{
        Param data = new Param();
        data.setBoolean("success", false);
        data.setString("msg", "设置失败");

        FaiList<Param> saleList = getAllHdSaleList();
        if(saleList == null){
            data.setString("msg", "获取销售信息异常");
            return data.toJson();
        }

        //前端传递过来的参数
        Param goalMoneyMap = Param.parseParam(request.getParameter("goalMoneyMap"), new Param());
        int month = Parser.parseInt(request.getParameter("month") , Calendar.getInstance().get(Calendar.MONTH)+1);

        Dao ossDao = WebOss.getOssBsDao();
        int rt = Errno.ERROR;
        try{
            ossDao.setAutoCommit(false);

            Dao.SelectArg sltArg = getAchievenmentSelectArg(month);

            FaiList<Param> saleAchievementList = ossDao.select(sltArg);
            if(saleAchievementList == null){
                data.setString("msg", "获取销售业绩异常");
                return data.toJson();
            }
            Log.logStd("saleAchievementList =%s", saleAchievementList);

            for(Param p: saleList){
                int sid = p.getInt("sid", 0);
                String acct = p.getString("acct", "");
                Param saleGoalMoney = goalMoneyMap.getParam(acct, new Param());

                int goalMoney = Parser.parseInt(saleGoalMoney.getString("goalMoney"), 0);
                double approveMoney = Parser.parseDouble(saleGoalMoney.getString("approveMoney"), 0.0);
                int approveCount = Parser.parseInt(saleGoalMoney.getString("approveCount"), 0);
                Log.logStd("sid:%s; goalMoney=%s, approveMoney=%s, approveCount=%s", sid, goalMoney, approveMoney, approveCount);

                ParamMatcher matcher = new ParamMatcher("sid", ParamMatcher.EQ, sid);
                //拿到查到的记录
                Param info = Misc.getFirst(saleAchievementList, matcher);

                //该记录还没有插入过
                if(Str.isEmpty(info)){
                    Param newInfo = new Param();
                    newInfo.setInt("sid", sid);
                    newInfo.setInt("goalMoney", goalMoney);
                    newInfo.setInt("approveCount", approveCount);
                    newInfo.setDouble("approveMoney", approveMoney);
                    //这为防止修改记录导致的问题
                    Calendar createTime = Calendar.getInstance();
                    int currentMonth = createTime.get(Calendar.MONTH)+1;
                    createTime.set(Calendar.MONTH, month-1);
                    if(currentMonth != month && currentMonth == 1 && month==12){
                    	createTime.add(Calendar.YEAR,-1);
                    }
                    newInfo.setCalendar("createTime", createTime); 
                    newInfo.setCalendar("updateTime", Calendar.getInstance());
                    rt = ossDao.insert("hdSaleAchievement", newInfo);
                    if(rt != Errno.OK){
                        Log.logStd("insert err; sid:%s", sid);
                        break;
                    }
                }else{  //否则做更新操作
                    matcher.and("id", ParamMatcher.EQ, info.getInt("id", 0));
                    info.setInt("goalMoney", goalMoney);
                    info.setInt("approveCount", approveCount);
                    info.setDouble("approveMoney", approveMoney);
                    info.setCalendar("updateTime", Calendar.getInstance());

                    Ref<Integer> effectRows = new Ref<Integer>();
                    rt = ossDao.update("hdSaleAchievement", new ParamUpdater(info), matcher, effectRows);
                    if(rt != Errno.OK || effectRows.value.intValue() > 1){
                        Log.logStd("update err; sid:%s");
                        break;
                    }
                }
            }

            if(rt == Errno.OK){
                data.setBoolean("success", true);
                data.setString("msg", "设置成功");
            }
        }catch (Exception e){
            ossDao.rollback();
            Log.logStd("setGoalMoney catch err: %s", e);
        }finally {
            if(ossDao != null){
            	if(rt != Errno.OK){
            		ossDao.rollback();
            	}else{
            		ossDao.commit();
            	}
                ossDao.close();
            }
        }
        return data.toJson();
    }
%>


<%
    //cmd处理
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            output = "no cmd find";
        } else if(cmd.equals("getHdSaleAchievement")){
            output = getHdSaleAchievement(request);
        } else if(cmd.equals("setGoalMoney")){
            output = setGoalMoney(request);
        }
    } catch (Exception e) {
        PrintUtil.printErr(e);
        PrintUtil.printStackTrace(e, 1, "setGoalMoney");
        output = WebOss.checkAjaxException(e);
    }
    out.print(output);


%>