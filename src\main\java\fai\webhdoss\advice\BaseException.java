package fai.webhdoss.advice;

import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.hdUtil.exception.ProfessionException;
import fai.hdUtil.exception.SystemException;
import fai.web.App;
import fai.web.Core;
import fai.web.WebException;
import org.springframework.beans.ConversionNotSupportedException;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotAcceptableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.*;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
public class BaseException extends ResponseEntityExceptionHandler {

    /** 监控500异常数量 monitorId */
    private static final int MONITOR_500_ERR_NUM_ID = 10137;

    @ExceptionHandler(value = ProfessionException.class)
    public JsonResult customProfessionExceptionHandler(Exception ex){
        ProfessionException professionException = (ProfessionException) ex;
        if (professionException.getRt() == Errno.OK){
            return JsonResult.success(ex.getMessage());
        }
        if (!Str.isEmpty(professionException.getLog())){
            Log.logStd(professionException.getLog());
            printErr(ex, 2, 5);
        }
        return JsonResult.error(professionException.getRt(), ex.getMessage());
    }

    @ExceptionHandler(value = SystemException.class)
    public JsonResult customSystemExceptionHandler(Exception ex){
        SystemException systemException = (SystemException) ex;
        if (systemException.getRt() == Errno.OK){
            return JsonResult.success(ex.getMessage(), new Param().setInt("flow", systemException.getFlow()));
        }
        if (!Str.isEmpty(systemException.getLog())){
            App.logErr(systemException.getRt(), systemException.getLog());
            printErr(ex, 2, 5);
        }
        if (systemException.getMonitorId() > 0 ){
            Fdp.bssMonitor(systemException.getMonitorId());
        }
        Fdp.bssMonitor(MONITOR_500_ERR_NUM_ID);
        return JsonResult.error(systemException.getRt(), ex.getMessage(), new Param().setInt("code", Core.getFlow()));
    }

    @ExceptionHandler(value = WebException.class)
    public JsonResult webExceptionHandler(Exception ex){
        WebException webException = (WebException) ex;
        int errRt = webException.getErrno();
        String errMsg = ex.getMessage() + "(err_code: " + Core.getFlow() + ")";
        if (errRt == Errno.OK) {
            return JsonResult.success(ex.getMessage(), new Param().setInt("flow", Core.getFlow()));
        }
        if (errRt == Errno.WEB_NO_LOGIN){
            return JsonResult.error(errRt, errMsg, new Param().setInt("code", Core.getFlow()));
        }
        if (errRt != Errno.ARGS_ERROR && errRt != Errno.WEB_NO_AUTH){
            Fdp.bssMonitor(MONITOR_500_ERR_NUM_ID);
            printErr(webException, 1, 4);
        }
        return JsonResult.error(errRt, errMsg, new Param().setInt("code", Core.getFlow()));
    }

    @ExceptionHandler({
            Exception.class
    })
    public JsonResult exceptionHandler(Exception e) {
        int rt = Errno.ERROR;
        String errMsg = "系统错误";
        if (e instanceof MissingServletRequestParameterException || e instanceof BindException) {
            rt = Errno.ARGS_ERROR;
            errMsg = "参数错误";
        } else if (e instanceof HttpRequestMethodNotSupportedException) {
            rt = Errno.CODEC_ERROR;
            errMsg = "请求方式错误";
        } else {
            Fdp.bssMonitor(MONITOR_500_ERR_NUM_ID);
        }
        printErr(e, 1, 4);
        errMsg = errMsg + "(err_code: " + Core.getFlow() + ")";
        return JsonResult.fail(rt, errMsg, new Param().setInt("flow", Core.getFlow()).toString());
    }

    /**
     * 参数校验异常处理
     * @param ex
     * @param headers
     * @param status
     * @param request
     * @return
     * <AUTHOR>
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("请求参数有误");
        BindingResult bindingResult = ex.getBindingResult();
        for (ObjectError error : bindingResult.getAllErrors()) {
            errors.add(error.getDefaultMessage());
        }
        Map<String, Object> body = getResponseBody(status, errors);
        return new ResponseEntity<Object>(body, headers, status);
    }


    @Override
    public ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                               HttpHeaders headers,
                                                               HttpStatus status,
                                                               WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("传入的数据格式错误");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("http请求方式不支持");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotSupported(HttpMediaTypeNotSupportedException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("http媒体类型不支持");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMediaTypeNotAcceptable(HttpMediaTypeNotAcceptableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("http媒体类型不可接受");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingPathVariable(MissingPathVariableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("错误的路径变量");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("缺少请求参数");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleServletRequestBindingException(ServletRequestBindingException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("请求绑定异常");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleConversionNotSupported(ConversionNotSupportedException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("转换不支持");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(TypeMismatchException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("类型不匹配");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotWritable(HttpMessageNotWritableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("http消息不可写");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestPart(MissingServletRequestPartException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("缺少Servlet请求部分");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleBindException(BindException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("绑定异常");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        List<String> errors = new ArrayList<String>();
        errors.add("未发现处理器");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleAsyncRequestTimeoutException(AsyncRequestTimeoutException ex, HttpHeaders headers, HttpStatus status, WebRequest webRequest) {
        List<String> errors = new ArrayList<String>();
        errors.add("异步请求超时");
        errors.add("错误详情："+ex.getMessage());
        Map<String,Object> body = getResponseBody(status,errors);
        return new ResponseEntity<Object>(body, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatus status, WebRequest request) {
        return super.handleExceptionInternal(ex, body, headers, status, request);
    }

    private Map<String ,Object> getResponseBody(HttpStatus status, List<String> errors) {
        Map<String, Object> body = new LinkedHashMap<String, Object>();
        body.put("rt", Errno.ERROR);
        body.put("status", status.value());
        body.put("flow", Core.getFlow());
        body.put("errors", errors.toString());
        body.put("timestamp", new Date());
        return body;
    }

    private void printErr(Exception e, int startPoint, int endPoint) {
        StringBuilder sb = new StringBuilder();
        sb.append("Exception msg ===> ").append(" \"").append(e.toString()).append("\"");
        Log.logStd("aid ===> %d", Core.getAid());
        Log.logErr(sb.toString());
        for (int i = (startPoint - 1); i <= endPoint; i++) {
            sb = new StringBuilder();
            sb.append("Where the exception throw point ").append(i).append(" ===>  ");
            StackTraceElement stackTraceElement = e.getStackTrace()[i];
            sb.append(stackTraceElement.getClassName()).append("#");
            sb.append(stackTraceElement.getMethodName()).append(":");
            sb.append(stackTraceElement.getLineNumber()).append(" ");
            Log.logErr(sb.toString());
        }
    }
}
