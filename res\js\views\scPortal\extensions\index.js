import { Mark } from 'tiptap'

export default class TagMark extends Mark {
  get name() {
    return 'tag'
  }

  get schema() {
    return {
      attrs: {
        tagType: {
          default: 'highlight'
        },
        label: {
          default: null
        }
      },
      parseDOM: [{
        tag: 'span.tag',
        getAttrs: dom => ({
          tagType: dom.getAttribute('data-tag-type'),
          label: dom.getAttribute('data-label')
        })
      }],
      toDOM: node => {
        const tag = ['span', {
          class: `tag tag-${node.attrs.tagType}`,
          'data-tag-type': node.attrs.tagType,
          'data-label': node.attrs.label
        }, 0];
        
        if (node.attrs.label) {
          return ['span', {}, 
            tag,
            ['span', { 
              class: `tag-label tag-label-${node.attrs.tagType}`,
              contenteditable: 'false'
            }, node.attrs.label]
          ];
        }
        
        return tag;
      }
    }
  }

  commands({ type }) {
    return {
      setTag: attrs => (state, dispatch) => {
        const { from, to } = state.selection;
        return dispatch(state.tr.addMark(from, to, type.create(attrs)));
      },
      unsetTag: () => (state, dispatch) => {
        const { from, to } = state.selection;
        return dispatch(state.tr.removeMark(from, to, type));
      }
    }
  }
}
