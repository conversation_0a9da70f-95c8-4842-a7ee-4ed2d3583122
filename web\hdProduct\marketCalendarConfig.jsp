<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
    if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
        out.println("没有权限");
        return;
    }
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>营销日历配置</title>
    <%=Web.getToken()%>
    <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
    <style>
        body {
			margin: 0;
			font-size: 14px;
			line-height: 1.2;
		}
        #app {
            padding: 10px;
        }
        [v-cloak]{
			display: none;
		}
        .marketCalendar, .pagination {
            margin: 10px 0;
        }
        .el-form-item {
            margin-bottom: 10px;
        }
        .el-input {
            max-width: 400px;
        }
        .editBtn {
            margin-right: 12px;
        }
        #app .subTitle {
            font-size: 14px;
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <h2>营销日历<span class="subTitle">1. 同一日期不可超过两个节日或热点；2.热点名称字数不能超过5个字</span></h2>
        <div class="marketCalendar">
            <el-form :inline="true" :model="marketCalendarForm">
                <el-form-item>
                    <el-button type="primary" size="small" @click="editHotPoint(false)">新增</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="marketCalendarList" height="660" border>
                <el-table-column prop="name" label="热点名称" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="date" label="日期" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.date | formatDate }}
                    </template>
                </el-table-column>
                <el-table-column prop="jumpto" label="跳转到关键词结果" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="status" label="状态" min-width="140" :resizable="false" align="center">
                    <template scope="scope">
                        <el-switch
                            v-model="scope.row.status"
                            @change="handleSwitchStatus(scope.row)"
                        >
                        </el-switch>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="180" :resizable="false" align="center">
                    <template slot-scope="scope">
                        <el-button @click="editHotPoint(true, scope.row)" type="primary" size="small" class="editBtn">编辑</el-button>
                        <el-popconfirm
                            title="确定删除该热点吗？"
                            confirm-button-text="继续"
                            cancel-button-text="取消"
                            @confirm="deleteHotPoint(scope.$index, scope.row.id)"
                        >
                            <el-button slot="reference" type="danger" size="small">删除</el-button>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background
                @size-change="accountDetailSizeChange"
                @current-change="accountDetailPageChange"
                :current-page="marketCalendarForm.page"
                :page-sizes="[5, 10, 20, 30, 50, 100]"
                :page-size="marketCalendarForm.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="marketCalendarForm.total"
                class="pagination">
            </el-pagination>
        </div>
        <el-dialog :title="getDialogTitle()" :visible.sync="hotPointDialog">
            <el-form :model="hotPointDialogForm">
                <el-form-item label="热点名称：" :label-width="formLabelWidth">
                    <el-input v-model="hotPointDialogForm.name" clearable></el-input>
                </el-form-item>
                <el-form-item label="日期：" :label-width="formLabelWidth">
                    <el-date-picker v-model="hotPointDialogForm.date" type="date" size="small" :clearable="false" value-format="yyyy-MM-dd"></el-date-picker>
                </el-form-item>
                <el-form-item label="跳转到关键词结果：" :label-width="formLabelWidth">
                    <el-input v-model="hotPointDialogForm.jumpto" clearable></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="hotPointDialog = false">取 消</el-button>
                <el-button type="primary" @click="saveHotPoint">保 存</el-button>
            </div>
        </el-dialog>
    </div>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_faiHd")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
    <script>
        // 自定义时间格式化过滤器，默认返回yyyy-MM-dd HH:mm
        function formatDate(timestamp, format){
            return timestamp === 852048000000 ? '-' : $.format.date(new Date(timestamp), format || 'yyyy-MM-dd');
        }
        var app = new Vue({
            el: "#app",
            data:{
                formLabelWidth: '210px',
                hotPointDialog: false,
                marketCalendarForm: {
                    date: [],
                    page: 1,
                    limit: 10,
                    total: 0
                },
                isEditDialog: false,
                defHotPointDialogForm: {
                    name: '',
                    date: '',
                    jumpto: ''
                },
                hotPointDialogForm: {},
                marketCalendarList: []
            },
            created: function (){
                this.getMarketCalendarList();
            },
            methods: {
                getDialogTitle: function () {
                    return this.isEditDialog ? '编辑' : '新增';
                },
                editHotPoint: function (isEditDialog, data) {
                    this.isEditDialog = isEditDialog;
                    var originData = isEditDialog ? data : this.defHotPointDialogForm;
                    var hotPointDialogForm = $.extend(true, {}, originData);
                    hotPointDialogForm.date = hotPointDialogForm.date === '' ? formatDate(new Date().getTime()) : formatDate(hotPointDialogForm.date);
                    this.hotPointDialogForm = hotPointDialogForm;
                    this.hotPointDialog = true;
                },
                saveHotPoint: function () {
                    if($.trim(this.hotPointDialogForm.name).length === 0){
                        return this.alertErrorMsg('请输入热点名称');
                    }
                    if($.trim(this.hotPointDialogForm.name).length > 5){
                        return this.alertErrorMsg('热点名称不能超过5个字');
                    }
                    if($.trim(this.hotPointDialogForm.jumpto).length === 0){
                        return this.alertErrorMsg('请输入跳转到关键词结果');
                    }
                    if($.trim(this.hotPointDialogForm.jumpto).length > 20){
                        return this.alertErrorMsg('跳转到关键词结果长度不能超过20');
                    }
                    var hotPointDialogForm = $.extend(true, {}, this.hotPointDialogForm);
                    if(this.isEditDialog){
                        this.updateHotPoint(hotPointDialogForm);
                    }else{
                        this.addMarketCalendar(hotPointDialogForm);
                    }
                },
                getMarketCalendarList: function () {
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        dataType: 'json',
                        url: '/api/CalendConfig/calendConfigList',
                        data: {
                            pageNo: _this.marketCalendarForm.page,
                            pageLimt: _this.marketCalendarForm.limit
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.marketCalendarList = result.data.list;
                                _this.marketCalendarForm.total = result.data.size;
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                accountDetailSizeChange: function (val) {
                    this.marketCalendarForm.limit = val;
                    this.marketCalendarForm.page = 1;
                    this.getMarketCalendarList();
                },
                accountDetailPageChange: function (val) {
                    this.marketCalendarForm.page = val;
                    this.getMarketCalendarList();
                },
                addMarketCalendar: function (data) {
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '/api/CalendConfig/addCalendConfig',
                        data: data,
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.alertSuccessMsg('新增成功');
                                _this.hotPointDialog = false;
                                _this.getMarketCalendarList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                handleSwitchStatus: function (data) {
                    var hotPointDialogForm = $.extend(true, {}, data);
                    hotPointDialogForm.date = formatDate(hotPointDialogForm.date);
                    this.updateHotPoint(hotPointDialogForm);
                },
                updateHotPoint: function (data) {
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '/api/CalendConfig/updateCalendConfig',
                        data: data,
                        error: function(){
                            _this.alertErrorMsg();
                            !_this.hotPointDialog && _this.getMarketCalendarList();
                        },
                        success: function(result){
                            if(result.success){
                                if(_this.hotPointDialog){
                                    _this.alertSuccessMsg('编辑成功');
                                    _this.hotPointDialog = false;
                                    _this.getMarketCalendarList();
                                }
                            }else{
                                _this.alertErrorMsg(result.msg);
                                !_this.hotPointDialog && _this.getMarketCalendarList();
                            }
						}
                    });
                },
                deleteHotPoint: function(index, id){
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        dataType: 'json',
                        url: '/api/CalendConfig/deleteCalendConfig',
                        data: {
                            id: id
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.alertSuccessMsg('删除成功');
                                _this.getMarketCalendarList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                alertSuccessMsg: function(msg){
                    ELEMENT.Message({
                        type: 'success',
                        message: msg
                    });
                },
                alertErrorMsg: function(msg){
                    ELEMENT.Message({
                        type: 'error',
                        message: msg || '系统繁忙，请稍后重试'
                    });
                }
            },
            filters: {
                formatDate: formatDate
            }
        });
    </script>
</body>
</html>
