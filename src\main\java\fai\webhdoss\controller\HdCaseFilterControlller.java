package fai.webhdoss.controller;

import fai.comm.config.FaiConfig;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import fai.hdUtil.hostConf.HdBaseHostConf;
import fai.web.Web;
import fai.webhdoss.WebHdOss;
import fai.webhdoss.model.vo.HdCaseFilterVO;
import fai.webhdoss.service.HdCaseFilterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.*;


/**
 * 案例筛选库相关功能
 * <AUTHOR>
 */
@RestController
@Api(value = "案例筛选库相关业务接口")
@RequestMapping("/caseFilter")
public class HdCaseFilterControlller {

    @Autowired
    private HdCaseFilterService hdCaseFilterService;

    /**
     * 互动模板列表删除权限校验
     * <AUTHOR>
     * @date 20210712
     * @return JsonResul
     */
    @CrossOrigin("*")
    @RequestMapping("/DeleteCheck")
    @ApiOperation(value = "互动模板列表删除权限校验", httpMethod = "GET")
    public JsonResult deleteCheck() {
        try {
            int aid = WebHdOss.getSid();
            if (0 == aid) {
                return JsonResult.error("未登录");
            }
            //有删除权限为true，否则为false
            if (Web.getDebug()) {
                return JsonResult.ok(Arrays.asList(425,4294,4326).contains(aid));
            }else{
                return JsonResult.ok(Arrays.asList(995).contains(aid));
            }

        } catch (Exception e) {
            return JsonResult.error();
        }
    }

    /**
     * 将活动列表的某一个活动导入案例筛选库
     * @param hdCaseFilterVO
     * <AUTHOR>
     * @date 2021/05/10
     */
    @CrossOrigin("*")
    @PostMapping("/importCaseFilter")
    @ApiOperation(value = "将活动列表的某一个活动导入案例筛选库", httpMethod = "POST")
    public JsonResult importCaseFilter(@RequestBody HdCaseFilterVO hdCaseFilterVO){
        try{
            String result = hdCaseFilterService.importCaseFilter(hdCaseFilterVO);
            if(!"导入成功".equals(result)){
                return JsonResult.error(result);
            }
            return JsonResult.ok(new HashMap<String,String>(2){{put("data",result);}});
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 将案例筛选库的某一个活动导入案例库
     * @param id
     * <AUTHOR>
     * @date 2021/05/10
     */
    @PostMapping("/importCase")
    @ApiOperation(value = "将案例筛选库的某一个活动导入案例库", httpMethod = "POST")
    public JsonResult importCase(Integer id){
        try{
            String result = hdCaseFilterService.importCase(id);

            if(!"导入成功".equals(result)){
                return JsonResult.error(result);
            }
            Param param = hdCaseFilterService.getCaseFilterById(id);
            Log.logDbg("dkLog:"+param.toJson());
            String link = "http://o."+ HdBaseHostConf.getTopHost( Web.getEnvMode())+"/designerCenter/ajax/case_h.jsp?cmd=addHdCaseFromHdLibraries";

            String data = "aid="+param.getInt("oAid")+"&siteId="+param.getInt("oGameId")+"&eAid="+param.getInt("caseAid")+"&eGameId="+param.getInt("gameId");
            String httpContent = FaiUrlConnection.getHttpContent(link, data);
            Log.logDbg("dkLog:"+httpContent);


            return JsonResult.ok(result);
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 将案例筛选库的某一个活动删除
     * @param id
     * <AUTHOR>
     * @date 2021/05/10
     */
    @PostMapping("/deleteCaseFilter")
    @ApiOperation(value = "将案例筛选库的某一个活动删除", httpMethod = "POST")
    public JsonResult deleteCaseFilter(int id){
        try{
            return JsonResult.ok(hdCaseFilterService.deleteCaseFilter(id));
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 编辑案例筛选库的某一个活动
     * @param hdCaseFilterVO
     * <AUTHOR>
     * @date 2021/05/10
     */
    @PostMapping("/modifyCaseFilter")
    @ApiOperation(value = "编辑案例筛选库", httpMethod = "POST")
    public JsonResult modifyCaseFilter(@RequestBody HdCaseFilterVO hdCaseFilterVO){
        try{
            return JsonResult.ok(hdCaseFilterService.modifyCaseFilter(hdCaseFilterVO));
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 案例筛选库查询
     * @param oAid   原账户aid
     * @param oGameId 原活动id
     * @param gameId  复制后活动id
     * @param createPerson 复制人
     * @param createTimeStart   开始复制时间
     * @param createTimeEnd   结束复制时间
     * <AUTHOR>
     * @date 2021/05/10
     */
    @PostMapping("/getCaseFilterList")
    @ApiOperation(value = "案例筛选库查询", httpMethod = "POST")
    public JsonResult getCaseFilterList(Integer page,Integer limit,Integer oAid, Integer oGameId, Integer gameId, String createPerson, String createTimeStart, String createTimeEnd){
        try{
            Param param = new Param();
            param.setInt("page", page);
            param.setInt("limit", limit);
            param.setInt("oAid", oAid);
            param.setInt("oGameId", oGameId);
            param.setInt("gameId", gameId);
            param.setString("createPerson", createPerson);
            param.setString("createTimeStart", createTimeStart);
            param.setString("createTimeEnd", createTimeEnd);

            Ref<List> dataListRef = new Ref<>();
            Ref<Integer> totalSizeRef = new Ref<>();
            int rt = hdCaseFilterService.getCaseFilterList(param, dataListRef, totalSizeRef);
            if (rt != Errno.OK){
                return JsonResult.error("查询失败");
            }

            return JsonResult.ok(new HashMap<String, Object>(2){{
                put("totalSize",totalSizeRef.value);
                put("dataList",dataListRef.value);
            }});

        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     * 案例筛选库权限校验
     * <AUTHOR>
     * @date 2021/05/10
     */
    @GetMapping("/getAuthority")
    @ApiOperation(value = "案例筛选库权限校验", httpMethod = "GET")
    public JsonResult getAuthority(){
        try{
            String configName = "caseFilterAuthorityList";
            String sid = String.valueOf( WebHdOss.getSid());
            Log.logStd("sid----------"+sid);
            if ("0".equals(sid)) {
                return JsonResult.error("未登录");
            }
            //如果是本地环境则替换配置文件
            if (Web.getDebug()) {
                configName = "caseFilterAuthorityListDebug";
            }

            Param authorityParam = ConfPool.getConf(configName);

            FaiList<Object> caseFilterAuthorityList = authorityParam.getList("caseFilterAuthorityList");
            for (Object authority : caseFilterAuthorityList) {
                if ( sid.equals(authority.toString()) ){
                    return JsonResult.ok(true);
                }
            }
            return JsonResult.error("没有权限");
        }catch (Exception e){
            return JsonResult.error();
        }
    }

    /**
     *
     * @param oAid
     * @param oGameId
     * @param caseAid
     * <AUTHOR>
     * @date 2021/05/10
     */
    @CrossOrigin("*")
    @PostMapping("/getGameLinkandCode")
    @ApiOperation(value = "获取筛选案例的活动链接和二维码", httpMethod = "POST")
    public JsonResult getGameLinkandCode(Integer oAid, Integer oGameId, Integer caseAid){
        try{
            Param param = new Param();
            param.setInt("oAid",oAid);
            param.setInt("oGameId",oGameId);
            param.setInt("caseAid",caseAid);

            String paramJson = param.toJson();

            String result = hdCaseFilterService.getGameLinkandCode(paramJson);
            if (result == null || "".equals(result)){
                return JsonResult.error("没有数据");
            }

            Param param1 = Param.parseParam(result);

            return JsonResult.ok(new HashMap<String,String>(2){{
                put("gameUrl",param1.getString("gameUrl"));
                put("gameCode",param1.getString("gameCode"));
            }});
        }catch (Exception e){
            return JsonResult.ok("获取失败");
        }
    }
}