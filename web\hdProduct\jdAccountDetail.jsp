<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
    if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
        out.println("没有权限");
        return;
    }
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>京东礼品列表</title>
    <%=Web.getToken()%>
    <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
    <style>
        body {
			margin: 0;
			font-size: 14px;
			line-height: 1.2;
		}
        #app {
            padding: 10px;
        }
        [v-cloak]{
			display: none;
		}
        .accountDetail, .pagination {
            margin: 10px 0;
        }
        .el-form-item {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <h2>当前京东金额：{{currentJdMoney | formatMoney}}</h2>
        <div class="accountDetail">
            <el-form :inline="true" :model="accountDetailForm">
                <el-form-item label="时间：">
                    <el-date-picker
                        v-model="accountDetailForm.date"
                        type="daterange"
                        size="small"
                        unlink-panels
                        :clearable="false"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" icon="el-icon-search" @click="searchAccountDetail">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" @click="exportAccountDetail">导出</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="accountDetailList" height="660" border>
                <el-table-column prop="id" label="余额明细ID" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="amount" label="金额（元）" sortable min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.amount | formatMoney }}
                    </template>
                </el-table-column>
                <el-table-column prop="pin" label="京东Pin" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="orderId" label="订单号" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="tradeType" label="业务类型" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="tradeTypeName" label="业务类型名称" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="createdDate" label="余额变动日期" sortable min-width="140" :resizable="false" align="center">
                    <template scope="scope">
                        {{ scope.row.createdDate | formatDate }}
                    </template>
                </el-table-column>
                <el-table-column prop="notePub" label="备注信息" sortable min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="tradeNo" label="tradeNo" sortable min-width="140" :resizable="false" align="center"></el-table-column>
            </el-table>
            <el-pagination background
                @size-change="accountDetailSizeChange"
                @current-change="accountDetailPageChange"
                :current-page="accountDetailForm.page"
                :page-sizes="[5, 10, 20, 30, 50, 100]"
                :page-size="accountDetailForm.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="accountDetailForm.total"
                class="pagination">
            </el-pagination>
        </div>
    </div>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_faiHd")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
    <script>
        var app = new Vue({
            el: "#app",
            data:{
                currentJdMoney: 0,
                accountDetailForm: {
                    date: [],
                    page: 1,
                    limit: 10,
                    total: 0
                },
                accountDetailList: []
            },
            created: function (){
                this.initDate();
                this.getAccountDetailList();
            },
            methods: {
                initDate: function(){
                    var now = new Date();
                    var startDate = new Date(Date.UTC(now.getFullYear(), now.getMonth(), now.getDate() - 30)).toISOString().slice(0, 10);
                    var endDate = new Date(Date.UTC(now.getFullYear(), now.getMonth(), now.getDate())).toISOString().slice(0, 10);
                    this.accountDetailForm.date.push(startDate);
                    this.accountDetailForm.date.push(endDate);
                },
                searchAccountDetail: function(){
                    this.accountDetailForm.limit = 10;
                    this.accountDetailForm.page = 1;
                    this.getAccountDetailList();
                },
                getAccountDetailList: function(){
                    var _this = this;
                    $.ajax({
                        type:"post",
                        url:"/ajax/hdJdGift_h.jsp?cmd=getRemain",
                        data: {
                            startDate: _this.formatReqDate(_this.accountDetailForm.date[0]),
                            endDate: _this.formatReqDate(_this.accountDetailForm.date[1]),
                            page: _this.accountDetailForm.page,
                            limit: _this.accountDetailForm.limit
                        },
                        error: function(){
                            ELEMENT.Message({
                                type: "error",
                                message: "系统繁忙，请稍后重试"
                            });
                        },
                        success: function(data){
                            var result = $.parseJSON(data);
                            if(result.success){
                                _this.currentJdMoney = result.remainMoney;
                                _this.accountDetailList = result.list;
                                _this.accountDetailForm.total = result.total;
                            }else{
                                ELEMENT.Message({
                                    type: "error",
                                    message: result.msg
                                });
                            }
						}
                    });
                },
                accountDetailSizeChange: function(val){
                    this.accountDetailForm.limit = val;
                    this.accountDetailForm.page = 1;
                    this.getAccountDetailList();
                },
                accountDetailPageChange: function(val){
                    this.accountDetailForm.page = val;
                    this.getAccountDetailList();
                },
                exportAccountDetail: function(){
                    window.location.href = '/ajax/hdJdGift_h.jsp?cmd=exportAccountDetail&startDate=' + this.formatReqDate(this.accountDetailForm.date[0]) + '&endDate=' + this.formatReqDate(this.accountDetailForm.date[1]) + '&_TOKEN=' + $('#_TOKEN').attr('value');
                },
                formatReqDate: function(val){
                    return val.replace(/-/g, '');
                }
            },
            filters: {
                // 自定义时间格式化过滤器，默认返回yyyy-MM-dd HH:mm
                formatDate(timestamp, format){
                    return timestamp === ************ ? '-' : $.format.date(new Date(timestamp), format || 'yyyy-MM-dd HH:mm');
                },
                // 金额格式化过滤器
                formatMoney(value, unit){
                    unit = unit || '￥';
                    if(!value) return unit + '0.00';
                    value = value.toFixed(2);
                    var intPart = parseInt(value); // 获取整数部分
                    var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'); // 将整数部分逢三一断
                    var floatPart = '.00'; // 预定义小数部分
                    var value2Array = value.split('.');
                    // 长度等于2表示数据有小数位
                    if(value2Array.length === 2) {
                        floatPart = value2Array[1].toString(); // 拿到小数部分
                        if(floatPart.length === 1) { // 补0,实际上用不着
                            return unit + intPartFormat + '.' + floatPart + '0';
                        } else {
                            return unit + intPartFormat + '.' + floatPart;
                        }
                    } else {
                        return unit + intPartFormat + floatPart;
                    }
                }
            }
        });
    </script>
</body>
</html>
