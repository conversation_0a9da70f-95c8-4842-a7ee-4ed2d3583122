package fai.webhdoss.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class HdCalendConfigVO {

    int id;

    @NotNull
    @NotBlank
    String name;

    @NotNull
    @NotBlank
    String prop;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    Date date;

    @NotNull
    @NotBlank
    String jumpto;

    @NotNull
    @NotBlank
    int status;
}
