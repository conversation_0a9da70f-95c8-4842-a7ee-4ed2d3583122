<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>

<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.*" %>
<%@ page import="java.math.*" %>
<%@ page import="java.util.regex.Matcher" %>
<%@ page import="java.util.regex.Pattern" %>
<%@ page import="fai.cli.PreSaleUtilCli" %>
<%@ page import="fai.cli.BssStatCli" %>
<%@ page import="fai.cli.*" %>
<%@ page import="java.util.concurrent.LinkedBlockingQueue" %>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.comm.fdpdata.FdpDataParamMatcher" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.*" %>

<%!
    //获取taList
    private FaiList<Param> getTaList() throws Exception {
        FaiList<Param> taList = (FaiList<Param>) Core.getTmpData("_taList");
        if (taList == null) {
            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);
            SearchArg searchArg = new SearchArg();
            taList = sysBssStat.getTaList(searchArg);
            Core.setTmpData("_taList", taList);
        }
        return taList;
    }

    private Param getTaParam() {
        String taSql = "select * from ta";
        FaiList<Param> taList = new FaiList<Param>();
        Dao bssMainDao = WebOss.getBssMainDao();
        try {
            taList = bssMainDao.executeQuery(taSql);
            if(taList == null){
                return new Param();
            }
        } catch (Exception e) {
            PreSaleHdDef.printErr(e);
        } finally {
            bssMainDao.close();
        }

        Param info = new Param(true);
        for (Param item : taList) {
            int ta = item.getInt("ta", 0);
            info.setParam(String.valueOf(ta), item);
        }
        return info;
    }
%>

<%!

    //绑定分配资源数据
    private Param getAllotPersonData(Param allotInfo, String acct, int tag) {
        int aid = allotInfo.getInt("aid");
        Calendar calendar = Calendar.getInstance();
        int flag = 0;
        int tempTag = tag;

        // 执行更新
        Param data = new Param();
        data.setInt(PreSaleHdDef.Info.AID, aid);
        data.setInt(PreSaleHdDef.Info.DEL, 0);
        data.setString(PreSaleHdDef.Info.SALES_ACCT, acct);
        data.setInt(PreSaleHdDef.Info.TYPE, PreSaleHdDef.Type.AFTER);
        data.setInt(PreSaleHdDef.Info.STATUS, PreSaleHdDef.Status.PERSON);

        data.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
        data.setCalendar(PreSaleHdDef.Info.RECEIVE_TIME, Calendar.getInstance());
        data.setInt(PreSaleHdDef.Info.TAG, tempTag);
        data.setInt(PreSaleHdDef.Info.FLAG, flag);
        int ta = allotInfo.getInt(BssStatDef.Info.TA, 0);

        Param taParam = getTaParam();
        if (taParam != null) {
            Param taInfo = taParam.getParam(String.valueOf(ta), new Param());
            int groupId = taInfo.getInt(PreSaleHdDef.PreSaleTa.GROUP_ID, 0);
            data.setString(PreSaleHdDef.Info.TA_GROUP_NAME, PreSaleHdDef.getTaNameByTaAndGroupId(groupId, ta));
        }
        data.setInt(PreSaleHdDef.Info.TA, ta);
        data.setCalendar(PreSaleHdDef.Info.CREATE_TIME, Calendar.getInstance());
        return data;
    }

    private Param getAllotRecord(Param allotInfo, String acct,int tag) {
        int aid = allotInfo.getInt("aid");
        int flag = allotInfo.getInt(PreSaleHdDef.Info.FLAG, 0);
        int business = allotInfo.getInt(PreSaleHdDef.Info.BUSINESS,0);
        Param recordInfo = new Param();
        int tempTag = tag;
        recordInfo.setInt(HdSaleRecordDef.Info.AID, aid);
        recordInfo.setString(HdSaleRecordDef.Info.SACCT, acct);
        if(tempTag == PreSaleHdDef.Tag.RENEW_RESOURCE){
            recordInfo.setInt(HdSaleRecordDef.Info.ATTR_TYPE, HdSaleRecordDef.AttrType.MANAGER_ALLOT);
        }else{
            recordInfo.setInt(HdSaleRecordDef.Info.ATTR_TYPE, HdSaleRecordDef.AttrType.PHONE_ALLOT);
        }
        recordInfo.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
        recordInfo.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, Calendar.getInstance());
        recordInfo.setString(HdSaleRecordDef.Info.ACTION, "管理员分配（创建时间），客户： " + aid);
        recordInfo.setInt(HdSaleRecordDef.Info.TAG, allotInfo.getInt(PreSaleDef.Info.TAG, tempTag));
        recordInfo.setInt(HdSaleRecordDef.Info.FLAG, flag);
        recordInfo.setString(HdSaleRecordDef.Info.TA_GROUP_NAME, allotInfo.getString(PreSaleHdDef.Info.TA_GROUP_NAME, ""));
        recordInfo.setInt(HdSaleRecordDef.Info.TA, allotInfo.getInt(PreSaleDef.Info.TA, 0));
       
        return recordInfo;
    }

%>

<%!
    // 获取销售列表
    private String getPreSale(HttpServletRequest request) throws Exception {
        FaiList<Param> dataList = new FaiList<Param>();
        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        if(infoList == null){
            Log.logStd("getPreSale err: infoList is null");
            return "{\"success\":false, \"dataList\":" + dataList + "}";
        }
        for (Param p : infoList) {
            Param param = new Param();
            param.setInt("sid", p.getInt("sid", 0));
            param.setString("acct", p.getString("acct", ""));
            param.setString("name", p.getHtmlString("nickName"));
            param.setString("allotNum", "");
            dataList.add(param);
        }
        return "{\"success\":true, \"dataList\":" + dataList + "}";
    }
%>

<%!
    // 查询指定时间内续费资源量
    private FaiList<Param> getRenewResource(String overdueDateBeg, String overdueDateEnd) throws Exception {

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);
        OssVip oSysVip = new OssVip();

        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(VipDef.Info.TYPE,ParamMatcher.EQ,VipDef.Type.HD_VIP);
        searchArg.matcher.and(VipDef.Info.EXPIRE_TIME, ParamMatcher.GE, overdueDateBeg);
        searchArg.matcher.and(VipDef.Info.EXPIRE_TIME, ParamMatcher.LE, overdueDateEnd);
        searchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.IN, PreSaleHdDef.getHdVersionList());

        FaiList<Param> vipList = oSysVip.searchVip(searchArg);
        if(vipList == null){
            Log.logStd("vipList is null");
            return null;
        }
        Log.logStd("vipList = %s",vipList.size());

        FaiList<Integer> aidList = new FaiList<Integer>();
        for(Param p : vipList){
            aidList.add(p.getInt("aid",0));
        }

        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);
        fieldList.add(BssStatDef.Info.REG_MOBILE); //注册手机
        fieldList.add(BssStatDef.Info.NAME);      //企业名称
        fieldList.add(BssStatDef.Info.TA);        //注册来源
        fieldList.add(BssStatDef.Info.OPEN_HD_TIME); //开通互动时间

        //排除没有手机号的   
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BssStatDef.Info.AID,ParamMatcher.IN,aidList);
        searchArg.matcher.and(BssStatDef.Info.REG_MOBILE,ParamMatcher.NE,"");
        // <AUTHOR> 2019-09-12
        //添加排除内部员工手机号
		FaiList<String> internalPhone = getInternalPhone();//获取内部手机
        searchArg.matcher.and(BssStatDef.Info.PHONE,ParamMatcher.NOT_IN,internalPhone);
        //FaiList<Param> infoList = sysBssStat.getAllAcctInfoList(fieldList, searchArg);

        FaiList<Object> outerExpr = new FaiList<Object>();
        outerExpr.add(IN.contains("aid", aidList));
        outerExpr.add(NE.of("reg_mobile", ""));
        outerExpr.add(NOT_IN.contains("reg_phone", internalPhone));
        FdpDataParamMatcher matcher = AND.expr(outerExpr);

        ResultSet execute = FdpDataSDK.newOLTPQuery()
                .select("aid", "reg_mobile", "reg_name", "reg_ta", "open_hd_time")
                .from("fdpData", "dws_fkw_acct_info")
                .where(matcher)
                .execute(Core.getFlow());
        FaiList<Param> infoList = execute.getData();


        if(infoList == null){
            Log.logStd("infoList is null");
            return null;
        }

        aidList.clear();
        for(Param p : infoList){
            aidList.add(p.getInt(PreSaleHdDef.Info.AID, 0));
        }
        Log.logStd("after remove no phone aidList size = %s", aidList.size());

        //排除在acctPreSaleHd表中的
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);

        FaiList<Param> needRemoveList = sysPreSaleHd.getList(searchArg);
        for(Param p : needRemoveList){
            aidList.remove(p.getInt("aid", 0));
        }

        vipList = Misc.getList(vipList, new ParamMatcher("aid", ParamMatcher.IN, aidList));
        Log.logStd("after remove exist in acctPreSaleHd vipList size = %s", vipList.size());

        //最近付费时间（报表字段需要）
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidList);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.STATUS, ParamMatcher.EQ, BssStatDef.Status.FIN_PROCESS);
        searchArg.matcher.and(BssStatDef.OrderItemInfo.PRICE, ParamMatcher.GT, 0);
        searchArg.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.PAY_TIME, true);

        FaiList<Param> orderList = sysBssStat.getOrderItemList(searchArg);
        if (orderList == null) {
            Log.logStd("orderList is null");
            return null;
        }

        //绑定excel数据
        FaiList<Param> taList = getTaList();
        for(Param p : vipList){
            //acctStatus数据
            int aid = p.getInt("aid", 0);
            int siteId = p.getInt("sid", 0);
            faiTradeStationBaseApi.changeAid(aid);
            faiTradeStationBaseApi.changeSiteId(siteId);

            int productId = p.getInt("payProductId", 0);
            String productStr = faiTradeStationBaseApi.getName(productId);
            p.setString("productStr", productStr);
            Param bssInfo = Misc.getFirst(infoList, BssStatDef.Info.AID, aid);
            Param orderInfo = Misc.getFirst(orderList, BssStatDef.Info.AID, aid, new Param());

            if(bssInfo != null){
                p.setString(BssStatDef.Info.REG_MOBILE, bssInfo.getString(BssStatDef.Info.REG_MOBILE));
                p.setString(BssStatDef.Info.NAME, bssInfo.getString(BssStatDef.Info.NAME));
                int ta = bssInfo.getInt(BssStatDef.Info.TA, 0);
                p.setInt(BssStatDef.Info.TA, ta);
                p.setString("taName", PreSaleHdDef.getTaName(taList, ta));

                p.setString("payTime", Parser.parseDateString(orderInfo.getInt(BssStatDef.OrderItemInfo.PAY_TIME, 0), "yyyy-MM-dd HH:mm:ss"));
                p.setString("openHdTime", Parser.parseDateString(bssInfo.getInt(BssStatDef.Info.OPEN_HD_TIME, 0), "yyyy-MM-dd HH:mm:ss"));
            }else{
                Log.logStd("get bssInfo aid = %s err", aid);
            }
        }

        //好像vip表中(aid, payProductId)并不唯一，即可能其对应的expireTime有多个，故用set排除重复
        HashSet<Integer> aidSet = new HashSet<Integer>();
        //分配下去的资源
        FaiList<Param> allotList = new FaiList<Param>();
        for(Param p : vipList){
            int aid = p.getInt("aid", 0);
            if(p != null && aidSet.add(aid)){
                allotList.add(p);
            }
        }
        return allotList;

    }

%>

<%!
	//获取内部员工手机号
	public FaiList<String> getInternalPhone() throws Exception{
		Param signupConf = Web.getConf("signup");
		FaiList<String> internalPhone = signupConf.getList("mobile");
		if(internalPhone!=null){
			internalPhone = internalPhone.clone();
		}else{
			internalPhone = new FaiList<String>();	
		}
		FaiList<Param> innerMobileParamList =new FaiList<Param>();
		Dao dao = WebOss.getOssBsDao();
		try{
			innerMobileParamList = dao.executeQuery("select * from hdInnerMobile");
			for(Param p:innerMobileParamList){
				internalPhone.add(p.getString("mobile",""));
			} 
		}
		finally{
			dao.close();
		}
		return internalPhone;
	}


%>

<%!
    // request：查询/导出指定时间内续费资源量
    private String getRenewResource(HttpServletRequest request, HttpServletResponse response, JspWriter out)throws Exception{
        int sid = Session.getSid();
        boolean isManager = Auth.checkFaiscoAuth("authHDSaleManage", false);

        if(!isManager && sid != 1355 && sid != 1535){
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        String overdueDateBeg = request.getParameter("overdueDateBeg") + " 00:00:00";
        String overdueDateEnd = request.getParameter("overdueDateEnd") + " 23:59:59";

        boolean excel = Parser.parseBoolean(request.getParameter("exportFlag"), false);

        FaiList<Param> resourceList = getRenewResource(overdueDateBeg, overdueDateEnd);
        if(resourceList == null){
            Log.logStd("resourceList is null");
            return "{\"success\":false, \"count\":\"-1\"}";
        }else{
            Log.logStd("resourceList size=%s", resourceList.size());
            int count = resourceList.size();

            // 导出数据
            if(excel && count > 0){
                response.setContentType("application/x-excel");
                //浏览器都会使用本地编码，即GBK，浏览器收到这个文件名后，用ISO-8859-1来解码，然后用GBK来显示
                //所以我们用GBK解码。ISO-8859-1来编码，在浏览器那边会反过来执行，所以得到的不会是乱码
                response.setHeader("Content-Disposition", "attachment;filename=" + new String("续费用户数据.xls".getBytes("GBK"), "ISO-8859-1"));
                out.clear();//必须得加
                ServletOutputStream outputStream = response.getOutputStream();

                // 创建导出的表头跟需要导出的数据KEY值
                Param cellKey = new Param();
                cellKey.setString("aid", "aid");
                cellKey.setString("mobile", "注册手机");
                cellKey.setString("productStr", "产品类型");
                cellKey.setString("taName", "注册来源");
                cellKey.setString("name", "企业名称");
                cellKey.setString("openHdTime", "开通互动时间");
                cellKey.setString("payTime", "最近支付时间");

                OssPoi.exportExcel(cellKey, resourceList, outputStream);
            }

            return "{\"success\":true, \"count\":" + count +"}";
        }

    }
%>



<%!

    //分配续费资源
    private String allotResource(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        boolean isManager = Auth.checkFaiscoAuth("authHDSaleManage", false);

        if(!isManager && sid != 1355 && sid != 1535){
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        Param allotNumMap = Param.parseParam(request.getParameter("allotNumMap"), new Param());
        Log.logStd("allotNumMap=%s", allotNumMap);

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);
        SearchArg saleSearchArg = new SearchArg();
        FaiList<Param> infoList = new FaiList<Param>();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, infoList);
        if(infoList == null){
            Log.logStd("getPreSale err: infoList is null");
            return "{\"success\":false, \"msg\":\"分配失败，获取销售异常\"}";
        }

        //销售信息队列
        LinkedBlockingQueue<String> sidQueue = new LinkedBlockingQueue<String>();
        //分配数量map
        Param allotNumParam = new Param(true);
        //总分配量
        int allCount = 0;
        for(Param p: infoList){
            String acct = p.getString("acct", "");
            String allotNum = allotNumMap.getString(acct, "");
            if(acct.isEmpty() || allotNum.isEmpty()){
                continue;
            }else{
                int count = Parser.parseInt(allotNum, 0);
                if(count <= 0){
                    continue;
                }

                //销售入队并设置分配数量
                sidQueue.add(acct);
                allCount += count;
                allotNumParam.setInt(acct, count);
            }
        }
        Log.logStd("sidQueue: %s", sidQueue);
        Log.logStd("allotNumParam: %s", allotNumParam);

        String overdueDateBeg = request.getParameter("overdueDateBeg") + " 00:00:00";
        String overdueDateEnd = request.getParameter("overdueDateEnd") + " 23:59:59";

        //获取待分配资源
        FaiList<Param> resourceList = getRenewResource(overdueDateBeg, overdueDateEnd);

        if(resourceList == null){
            Log.logStd("resourceList is null");
            return "{\"success\":false, \"msg\":\"分配失败，获取分配资源异常\"}";
        }

        if(resourceList.size() < allCount){
            return "{\"success\":false, \"msg\":\"分配失败，分配资源不足\"}";
        }

        //==============执行分配逻辑==============
        FaiList<Param> preSaleList = new FaiList<Param>();
        FaiList<Param> recordList = new FaiList<Param>();

        Log.logStd("allotNumParam = %s", allotNumParam);
        for(Param p : resourceList){
            String acct = sidQueue.poll();
            if (acct == null || acct.isEmpty()) {
                Log.logStd("acct is null");
                break;
            }

            Param preSaleData = getAllotPersonData(p, acct, PreSaleHdDef.Tag.RENEW_RESOURCE);
            preSaleList.add(preSaleData);
            Param finalAllotRecord = getAllotRecord(preSaleData, acct, PreSaleHdDef.Tag.RENEW_RESOURCE);
            recordList.add(finalAllotRecord);

            int remainNum = allotNumParam.getInt(acct, 0) - 1;

            //更新还所需资源数
            allotNumParam.setInt(acct, remainNum);
            //分配资源数还没发够
            if(remainNum > 0){
                sidQueue.offer(acct);//重新入队。
            }
        }

        Log.logStd("allotNumParam map = %s", allotNumParam);
        Log.logStd("preSaleList size:%s, preSaleList=%s", preSaleList.size(), preSaleList);
        Log.logStd("recordList size:%s, recordList=%s", recordList.size(), recordList);

        Dao ossDao = WebOss.getOssBsDao();
        try{
            ossDao.setAutoCommit(false);
            int preSaleRt = ossDao.batchInsert("acctPreSaleHd", preSaleList);
            if(preSaleRt != Errno.OK){
           		Log.logStd("li test insert acctPreSaleHd err");
            }
            int recordRt = ossDao.batchInsert("hdSaleRecord", recordList);
            if(preSaleRt != Errno.OK || recordRt != Errno.OK){
       			ossDao.rollback();
       			Log.logStd("li test ossDao rollback");
       	 	}else{
       			ossDao.commit();
       			Log.logStd("batch insert success");
       		}
        }catch(Exception e){
            ossDao.rollback();
            Log.logStd("catch err ossDao rollback");
        }finally{
            ossDao.close();
        }
        return "{\"success\":true, \"msg\":\"分配成功\"}";
    }
%>


<%
    //cmd处理
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            output = "no cmd find";
        } else if(cmd.equals("getPreSale")){
            output = getPreSale(request);
        } else if(cmd.equals("getRenewResource")){
            output = getRenewResource(request, response, out);
        } else if(cmd.equals("allotResource")){
            output = allotResource(request);
        }

    } catch (Exception e) {
        PrintUtil.printErr(e);
        PrintUtil.printStackTrace(e, 1, "allotRenewResource");
        output = WebOss.checkAjaxException(e);
    }
    out.print(output);


%>