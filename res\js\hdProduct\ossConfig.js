/* eslint-disable no-undef */
// 路由组件 -- 跳转iframe
var wrapIframeComponent = {
	template: '#wrapIframe',
	data: function() {
		return {
			iframeSrc: '/hdProduct/manageAdvertising.jsp?id=3'
		};
	},
	watch: {
		'$route': function(to) {
			this.iframeSrc = to.query.src;
		}
	},
	created: function() {
		this.iframeSrc = this.$route.query.src;
	}
};
// 动态路由匹配
var routerObj = new VueRouter({
	routes: [
		{
			name: 'WrapIframe',
			path: '/wrapIframe',
			component: wrapIframeComponent,
			children: [
				{
					name: 'WrapIframe',
					path: 'wrapIframe/:src',
					component: wrapIframeComponent,
				}
			]
		}
	]
});
ossConfig = new Vue({
	el: '#ossConfig',
	data: {
		collapsed: false,
		bannerTabs: [
			{
				route: 'hdhome',
				name: '互动官网',
				key: 'sub2'
			}, {
				route: 'hdportal',
				name: '互动管理平台',
				key: 'sub3'
			}
		],
		bannerRoutes: {
			hdhome: [
				{
					key: 'sub21',
					bannerId: 3,
					name: '首页banner'
				}, {
					key: 'sub22',
					bannerId: 4,
					name: '价格页banner'
				}, {
					key: 'sub23',
					bannerId: 11,
					name: 'pro6页(pubParm=2)'
				}
			],
			hdportal: [
				{
					key: 'sub31',
					bannerId: 7,
					name: '轮播A位'
				}, {
					key: 'sub32',
					bannerId: 8,
					type: 2,
					name: 'B1位'
				}, {
					key: 'sub33',
					bannerId: 9,
					type: 3,
					name: 'B2位'
				}, {
					key: 'sub34',
					bannerId: 6,
					name: 'C1位'
				}, {
					key: 'sub35',
					bannerId: 'C3', // 其他iframe
					name: 'C3位'
				}, {
					key: 'sub36',
					bannerId: 10,
					type: 1,
					name: '活动指南'
				}, {
					key: 'sub37',
					bannerId: 2,
					name: '我的消息'
				}
			]
		}
	},
	router: routerObj,
	created: function() {
		this.handleClickBannerRouter('manageBanner', 3);
	},
	methods: {
		toggleCollapsed: function() {
			this.collapsed = !this.collapsed;
		},
		handleClickBannerRouter: function(route, bannerId, type) {
			if(route == 'manageBanner') {
       			var iframeSrc = '';
				if(bannerId == 'C3') {  //C3广告位
					iframeSrc = '/hdProduct/hotTopicSetting.jsp';
				}else{ //其他广告位
					type = type ? ('&type=' + type) : '';
					iframeSrc = '/hdProduct/manageAdvertising.jsp?id=' + bannerId + type;
				}
				this.$router.push({
					path: '/wrapIframe', 
					query: {
						src: iframeSrc
					}
				});
			}
		},
		error: function(msg) {
			this.$message.error(msg);
		},
	}
});