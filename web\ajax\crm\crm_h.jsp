<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.text.*"%>
<%@ page import="java.math.*"%>
<%@ page import="java.util.regex.Matcher"%>
<%@ page import="java.util.regex.Pattern"%>
<%@ page import="fai.cli.PreSaleUtilCli"%>
<%@ page import="fai.cli.BssStatCli"%>
<%@ page import="fai.cli.*"%>
<%@ page import="java.sql.PreparedStatement"%>
<%@ page import="fai.elasticSearch.product.Search" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.EQ" %>

<%!

private String getHdInfo(HttpServletRequest request) throws Exception {
	    int aid = Parser.parseInt(request.getParameter("aid"), 0);
	    String saleAcct = WebOss.getAcct();
	    
	    
	    if (aid <= 0) {
	        return "{\"success\":false, \"msg\":\"参数错误 \"}";
	    }
	    
	    SysCrmSale sysCrmSale =  Core.getSysKit(SysCrmSale.class);
	    SysCrmCustomer sysCrmCustomer = Core.getSysKit(SysCrmCustomer.class);
	    
	    Param saleInfo= sysCrmSale.getSaleInfoBySacct(salesAcct);
	    if(saleInfo == null){
	    	Log.logErr("getSaleInfoBySacct err;saleAcct = %s;aid = %s",saleAcct,aid);
	    	return "{\"success\":false, \"msg\":\"系统错误！ \"}";
	    }
	    
	    SearchArg searchArg = new SearchArg();
	    searchArg.matcher = new ParamMatcher(SalesSystemDef.AcctResource.AID,ParamMatcher.EQ,aid);
	    searchArg.matcher.and(SalesSystemDef.AcctResource.DEPARTMENT,ParamMatcher.EQ,saleInfo.getInt(SalesSystemDef.SaleInfo.DEPARTMENT,0));
	    
	    FaiList<Param> customerList = new FaiList<Param>();
	    
	    int rt = sysCrmCustomer.getResourceListBySearch(searchArg,customerList);
	    if(rt != Errno.OK){
	    	Log.logErr("getResourceListBySearch err;saleAcct = %s;aid = %s",saleAcct,aid);
	    	return "{\"success\":false, \"msg\":\"系统错误！ \"}";
	    }
	    
        Param info = customerList.get(0);	    
	
	    Param data = new Param();
	    data.setInt("intent", info.getInt(SalesSystemDef.AcctResource.SIGN, 0));
	    data.setString("reason", info.getString(SalesSystemDef.AcctResource.UNDEAL_REASON, ""));
	    data.setString("mark", info.getString(SalesSystemDef.AcctResource.MARK, ""));
	    data.setBoolean("showReasonAdmin", Auth.checkFaiscoAuth("authPreSaleLeader", false));
	    data.setString("reasonHref", "http://" + Web.getOssDomain() + "/cs/siteReason.jsp?type=12");
	    String talkNextTime = Parser.parseSimpleTime(info.getCalendar(SalesSystemDef.AcctResource.TALK_NEXT_TIME));
	    data.setString("intentName", SalesSystemDef.AcctResource.getSignName(info.getInt(SalesSystemDef.AcctResource.SIGN, 0)));
	    data.setString("talkNextTime", talkNextTime);
	    data.setInt("intentLevel", info.getInt(SalesSystemDef.AcctResource.INTENT_LEVEL, 0));
	    int status = info.getInt(SalesSystemDef.AcctResource.STATUS, 0);
	   
	    data.setString("status", SalesSystemDef.Status.getStatusName(status));
	    
	    data.setBoolean("isFocus",info.getInt(SalesSystemDef.AcctResource.FOCUS_ON, 0) == 1?true:false);
	    data.setBoolean("banMsg",info.getInt(SalesSystemDef.AcctResource.BAN_SEND_MSG, 0) == 1?true:false);
	    
	    //手机号，邮箱，验证邮箱
	    String loginSaleName = WebOss.getSacct();
	    Param acctInfo = new Param();
	    if (loginSaleName.equals(info.getString(SalesSystemDef.AcctResource.SALE_ACCT, ""))) {
	        acctInfo.setBoolean("show", true);
//	        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);//bss接口
//	        Param aidInfo = sysBssStat.getAcctStatusInfo(aid);
//	        String mobile = aidInfo.getString("mobile", "");
//	        if (mobile == "" || mobile.length() != 11) {
//	            mobile = aidInfo.getString("emailRegVerifyMobile", "");
//	        }
//	        acctInfo.setString("mobile", mobile);//拿手机号
//	        acctInfo.setString("email", aidInfo.getString("email", "无"));//拿邮箱
//	        FaiVerifiCation faiVerifiCation = (FaiVerifiCation) Core.getSysKit(Kid.SYS_VERIFICATION);
//	        int creatorSid = WebOss.getStaffCreatorSidByAid(aid);
//	        Param emailInfo = new Param();
//	        faiVerifiCation.getVerifiEmailInfoById(aid, creatorSid, emailInfo);
//	        acctInfo.setString("verifyEmail", emailInfo.getString(FaiVerifiCationDef.Email.EMAIL, "无"));

			//迁移到新的查询方式
			ResultSet execute = FdpDataSDK.newOLTPQuery()
					.select("*")
					.from("fdpData", "dws_fkw_acct_info")
					.where(AND.expr(EQ.of("aid", aid)))
					.execute(Core.getFlow());
			Log.logDbg("execute:"+execute);
			Param aidInfo = execute.getData().get(0);
			String mobile = aidInfo.getString("reg_mobile", "");
			if (mobile == "" || mobile.length() != 11) {
				mobile = aidInfo.getString("reg_verify_mobile", "");
			}
			acctInfo.setString("mobile", mobile);//拿手机号
			acctInfo.setString("email", aidInfo.getString("reg_email", "无"));//拿邮箱
			FaiVerifiCation faiVerifiCation = (FaiVerifiCation) Core.getSysKit(Kid.SYS_VERIFICATION);
			int creatorSid = WebOss.getStaffCreatorSidByAid(aid);
			Param emailInfo = new Param();
			faiVerifiCation.getVerifiEmailInfoById(aid, creatorSid, emailInfo);
			acctInfo.setString("verifyEmail", emailInfo.getString(FaiVerifiCationDef.Email.EMAIL, "无"));
	    } else {
	        acctInfo.setBoolean("show", false);
	    }
	    FaiList<Param> list = new FaiList<Param>();
	    list.add(data);
	    list.add(acctInfo);
	
	    return "{\"success\":true, \"data\":" + list.toJson() + "}";
}



%>

<%
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        
        if(cmd == null){
            output = "no cmd find";
        }else if(cmd.equals("getHdInfo")){
            Log.logStd("into getHdInfo");
        	output = getHdInfo(request,response,out);
        }

    }catch (Exception e){
        PreSaleHdDef.printErr(e);
        output = WebCrm.checkAjaxException(e);
    }
    out.print(output);


%>

