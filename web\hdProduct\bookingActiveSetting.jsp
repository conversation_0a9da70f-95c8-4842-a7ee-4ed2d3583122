<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
	if(!WebOss.checkSession(response)){
		return;
	}
	if(!Auth.checkFaiscoAuth("authHDSale|authHDSaleManage", false)){
		out.println("没有权限");
		return;
	}
	String hdOssUrl = WebHdOss.getDomainUrl();
%>

<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>即将上线调研</title>
	<%-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.4.2/lib/theme-chalk/index.css"> --%>
	<link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
	<style>
		body{
			margin: 0;
		}
		#app{
			padding: 30px 20px;
		}
		[v-cloak]{
			display: none;
		}
		.title{
			font-size: 20px;
			font-weight: bold;
			margin-bottom: 18px;
		}
		.marginLeft20 {
			margin-left: 20px;
		}
		.el-upload{
			width: 400px;
			text-align: left;
		}
		.imgBox{
			width: 304px;
			margin-bottom: 10px;
		}
		.imgBox img{
			display: block;
			width: 100%;
		}
		.bookingContainer .box-card {
			margin: 20px 0;
		}
		.bookingContainer .box-card .el-card__body {
			margin: 10px 0;
		}
		.bookingContainer .box-card .deleteBtn {
			display: inline-block;
			margin-left: 5px;
			vertical-align: middle;
			font-size: 14px;
			color: #4381fd;
			cursor: pointer;
		}
		.bookingContainer .box-card .deleteBtn:hover {
			color: #205eda;
		}
		.bookingContainer .box-card .el-button--mini {
			margin-left: 10px;
		}
		.bookingContainer .text.item {
			margin-bottom: 15px;
		}
	</style>
</head>
<body>
	<div id="app" v-cloak>
		<div v-show="!showRecord" class="bookingContainer">
			<div class="title marginLeft20">专题卡片 2</div>
			<p class="marginLeft20">当调研数量≥2时，会根据直销用户aid模2/模3后，展示不同的顺序。以减少排序对调研结果的干扰。</p>
			<el-form label-width="100px" size="mini">
				<el-card class="box-card">
					<div slot="header" class="clearfix">
						<span>即将上线调研（最多配置3个）</span>
						<el-button style="padding: 0 10px;" type="text" @click="addModel">新增活动</el-button>
					</div>
					<div 
						v-for="(model, index) in activeList"
						:key="index"
						class="text item"
					>
						{{'活动 ' + (index+1) + '：'}}
						{{model.name}}
						<el-button
							class="button"
							type="primary"
							size="mini"
							@click="editModel(index)"
						>设置</el-button>
						<el-button style="padding: 3px 0" type="text" @click="delNextGame(index)">删除</el-button>
					</div>
				</el-card>
			</el-form>
			<el-dialog :title="'活动 ' + (editActiveIndex + 1)" :visible.sync="settingFormVisible" :close-on-click-modal="false" >
				<el-form :model="activeSetting">
					<el-form-item label="名称：" :label-width="formLabelWidth">
						<el-input v-model="activeSetting.name"></el-input>
					</el-form-item>
					<el-form-item label="描述：" :label-width="formLabelWidth">
						<el-input type="textarea" v-model="activeSetting.detail"></el-input>
					</el-form-item>
					<el-form-item label="封面图：" :label-width="formLabelWidth">
						<el-upload
							action="/ajax/advanceUpload.jsp?cmd=upload&maxWidth=10000&maxHeight=10000&imgMode=2"
							accept="image/*"
							name="filedata"
							:show-file-list="false"
							:before-upload="beforeFileUpload"
							:on-success="fileUploadSuccess"
							:on-progress="fileUploadProgress"
							:on-error="fileUploadError"
						>
							<div class="imgBox" v-show="activeSetting.img">
								<img :src="activeSetting.img" />
							</div>
							<el-progress v-show="isUploading" :percentage="percentage"></el-progress>
							<el-button size="mini" type="primary">{{activeSetting.img ? '重新' : '点击'}}上传</el-button>
							<%-- <span style="color: #aaa;">图片尺寸建议为304*168（或等比例大小）</span> --%>
						</el-upload>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button type="primary" @click="save">提交</el-button>
				</div>
			</el-dialog>
			<el-button style="padding: 0 10px;" type="text" @click="showRecord = true">查看历史数据</el-button>
		</div>
		<div v-show="showRecord" class="exchangeRecord">
			<el-button type="text" @click="showRecord = false">&lt; 返回</el-button>
            <el-form :inline="true" :model="formSet" class="demo-form-inline">
                <el-form-item label="活动名称：">
					<el-select v-model="formSet.name" placeholder="请选择">
						<el-option
							v-for="(item, index) in historyActiveList"
							:key="'historyActiveList' + index"
							:label="item.name"
							:value="item.name">
						</el-option>
					</el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" icon="el-icon-search" @click="getHistoryNextGameList">查询</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="nextGameList" border style="width: 842px; margin-bottom: 10px;" :default-sort="{prop: 'id', order: 'descending'}">
                <el-table-column prop="name" label="活动名称" width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="onlineTimeFormat" label="调研-上线时间" width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="offlineTimeFormat" label="调研-下线时间" width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="agreeNum" label="点赞数" sortable width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="negativeNum" label="点踩数" sortable width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column label="操作" align="center">
					<template slot-scope="scope">
						<el-button
						size="mini"
						@click="exportNextGameVote(scope.$index)">导出aid</el-button>
					</template>
				</el-table-column>
            </el-table>
            <el-pagination background
                @size-change="exchangeSizeChange"
                @current-change="exchangeCurrentChange"
                :current-page="formSet.page"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="formSet.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="formSet.total"
                class="pagination">
            </el-pagination>
        </div>
	</div>
	<%-- <script src="https://cdn.jsdelivr.net/npm/vue@2.5.15/dist/vue.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/vue-resource@1.5.1/dist/vue-resource.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/element-ui@2.4.2/lib/index.js"></script> --%>
	<script src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script src="<%=HdOssResDef.getResPath("js_vue_resource")%>"></script>
	<script src="<%=HdOssResDef.getResPath("js_element")%>"></script>
	<script>
		new Vue({
			el: '#app',
			data: {
				isLoading: false,
				isUploading: false,
				percentage: 0,
				activeList: [],
				activeSetting: {},
				settingFormVisible: false,
				formLabelWidth: '80px',
				editActiveIndex: 0,

				showRecord: false,
				//表格数据
				formSet: {
					name: '',
                    page: 1,
                    limit: 10,
                    total: 0
				},
				historyActiveList: ['全部'],
                nextGameList: [],
			},
			created() {
				this.getNextGameList();
				this.getHistoryNextGameList(true);
			},
			methods: {
				beforeFileUpload(file) {
					if (file.size > 5 * 1024 * 1024) {
						return this.$message({
							type: 'error',
							message: '单个文件超过5MB！'
						});
					}
					this.isUploading = true;
					this.percentage = 0;
				},
				fileUploadSuccess(response) {
					if (response.type >= 1 && response.type <= 6) {
						this.$message({
							type: 'success',
							message: '文件 ' + response.name + ' 上传成功！'
						});
						this.activeSetting.img = response.path;
					} else {
						this.$message({
							type: 'error',
							message: '文件 ' + response.name + ' 类型不允许！'
						});
					}
					this.isUploading = false;
				},
				fileUploadProgress(event) {
					this.percentage = Math.round(event.percent);
				},
				fileUploadError(err) {
					this.$message({
						type: 'error',
						message: '系统繁忙，请稍后重试！'
					});
					this.isUploading = false;
				},
				getNextGameList() {
					var _this = this;
					Vue.http.get("/ajax/hdProduct_h.jsp?cmd=getNextGameList").then(response => {
						let data = response.data;
						console.log(data);
						if( data.success ){
							_this.activeList = data.list;
						}else{
							_this.showError(data.msg);
						}                  
					}, response => {
						_this.showError(data.msg);
					}); 
				},
				addModel() {
					if(this.activeList.length >= 3) {
						this.$message({
							type: 'warning',
							message: '最多配置3个活动'
						});
						return;
					}
					this.activeList.unshift({
						name: '',
						detail: '',
						img: ''
					});
				},
				editModel(index) {
					this.editActiveIndex = index;
					this.activeSetting = JSON.parse(JSON.stringify(this.activeList[index]));
					this.settingFormVisible = true;
				},
				delNextGame: function(index) {
					var _this = this;
					Vue.http.post(
						'/ajax/hdProduct_h.jsp?cmd=delNextGame',
						{
							id: _this.activeList[index].id
						},
						{ emulateJSON: true }
					).then(({data}) => {
						_this.isLoading = false;
						if(data.success) {
							_this.activeList.splice(index, 1);
							_this.$message({
								type: 'success',
								message: '删除成功',
								duration: 500
							});
						}else{
							showError(data.msg);
						}
					});
				},
				save() {
					this.isLoading = true;
					// 有信息尚未填写
					if(!(this.activeSetting.name && this.activeSetting.detail && this.activeSetting.img)) {
						this.$message({
							showClose: true,
							message: '有信息尚未填写，请修改后提交',
							type: 'warning'
						});
						return;
					}
					// 未修改
					var oldSetting = this.activeList[this.editActiveIndex];
					if(this.activeSetting.name == oldSetting.name && this.activeSetting.detail == oldSetting.detail && this.activeSetting.img == oldSetting.img ) {
						return;
					}
					// 新增
					this.addNextGame();
				},
				addNextGame() {
					var _this = this;
					var o = {};
					if(_this.activeSetting.id) {
						o = {
							cmd: 'setNextGame',
							msg: '修改',
						};
					}else{
						o = {
							cmd: 'addNextGame',
							msg: '新增',
						};
					}
					Vue.http.post(
						'/ajax/hdProduct_h.jsp?cmd=' + o.cmd,
						_this.activeSetting,
						{ emulateJSON: true }
					).then(({data}) => {
						_this.isLoading = false;
						_this.$message({
							type: data.success ? 'success' : 'error',
							message: o.msg + (data.success ? '成功' : '失败'),
							duration: 500,
							onClose: function() {
								if(data.success) {
									_this.activeSetting.id = data.id;
									_this.settingFormVisible = false;
									_this.activeList.splice(_this.editActiveIndex, 1, _this.activeSetting);
								}
							}
						});
					});
				},
				getHistoryNextGameList: function(isInit){
					var _this = this;
					if(this.formSet.name == '全部') {
						this.formSet.name = '';
					}
					Vue.http.post(
						"/ajax/hdProduct_h.jsp?cmd=getHistoryNextGameList",
						_this.formSet,
						{ emulateJSON: true }
					).then(response => {
						let data = response.data;
						console.log(data);
						if( data.success ){
							var nextGameList = data.list;
							nextGameList.forEach(function(model, index){
								nextGameList[index].onlineTimeFormat = _this.formateDate(model.onlineTime);
								nextGameList[index].offlineTimeFormat = _this.formateDate(model.offlineTime);
							})
							this.nextGameList = nextGameList;
							if(isInit == true) {
								// var historyActiveList = this.historyActiveList;
								// this.nextGameList.forEach(function(modal, index) {
								// 	historyActiveList.push(modal.name);
								// })
								nextGameList.unshift({
									name: '全部'
								})
								this.historyActiveList = nextGameList;
								_this.formSet.total = data.totalSize;
							}
						}else{
							_this.showError(data.msg);
						}                  
					}, response => {
						_this.showError(data.msg);
					});
				},
				showError:function(msg) {
					this.$message({
						type: 'warning',
						message: msg || '系统错误!'
					});
				},
				exportNextGameVote(index) {
					window.location.href = '<%=hdOssUrl%>/ajax/hdProduct_h.jsp?cmd=exportNextGameVote&id=' + this.nextGameList[index].id;
				},
				exchangeSizeChange: function(val){
                    this.formSet.limit = val;
                    this.formSet.page = 1;
                    this.getHistoryNextGameList();
                },
                exchangeCurrentChange: function(val){
                    this.formSet.page = val;
                    this.getHistoryNextGameList();
				},
				checkNum(input) { 
                　　var reg = /^[0-9]+.?[0-9]*$/;
                　　if (!reg.test(input)) { 
                　　　　return false;
                　　}
                    return true;
				},
				// 自定义时间格式化过滤器，返回YYYY-mm-dd
                formateDate(timestamp){
                    if(timestamp === 0 || !timestamp){
                        return '至今';
                    }else{
                        var date = new Date(timestamp);
                        var year = date.getFullYear();
                        var month = date.getMonth() + 1;
                        month = month < 10 ? '0' + month : month ;
                        var date = date.getDate();
                        date = date < 10 ? ("0" + date) : date;
                        var strDate = year + "-" + month + "-" + date;
                        return strDate;
                    }
				}
			},
			watch: {
                showRecord(newVal){
                    if(newVal){
                        this.getHistoryNextGameList();
                    }
                }
            }
		})
	</script>
</body>
</html>