package fai.webhdoss.model.vo;

import fai.comm.util.FaiList;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("活动方案排序vo")
public class HdActivityPlanListVO {
    @NotNull(message = "page不能为空")
    @Min(value = 1, message = "page must > 0")
    @ApiModelProperty("页数")
    private Integer page;

    @NotNull(message = "limit不能为空")
    @Min(value = 1, message = "limit must > 0")
    @ApiModelProperty("一页多少条")
    private Integer limit;

    @ApiModelProperty("是否关联传单")
    private Integer relationFlyerState;

    @ApiModelProperty("行业id")
    private Integer tradeId;

    @ApiModelProperty("排序情况")
    private FaiList<SortKey> sorts;

    @Data
    @Accessors(chain = true)
    public static class SortKey {
        @ApiModelProperty("需要排序的key")
        String key;

        @ApiModelProperty("是否倒序")
        Boolean desc;
    }
}
