<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<%
    if(!Auth.checkFaiscoAuth("authHdManage|authFinance|authAd", false)){
        out.println("没有权限");
        return;
    }
    FaiList<Param> tradeList = HdModelTradeDef.getTradeList();
%>

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>互动模板列表</title>
    <%=Web.getToken()%>
    <%=FrontEndDef.getJSsdkComponentScript(9000)%>
	<%=FrontEndDef.getPackageCss("fa-component", "~1.1.0")%>
    <link rel="stylesheet" href="<%=HdOssResDef.getResPath("css_element")%>">
    <style>
        body {
			margin: 0;
			font-size: 14px;
			line-height: 1.2;
		}
        #app {
            padding: 10px;
        }
        [v-cloak]{
			display: none;
		}
        .topTab {
            margin-bottom: 30px;
        }
        .itemSpace {
            margin-left: 20px;
        }
        .templateList, .pagination {
            margin: 10px 0;
        }
        .textLink {
            color: #1890ff;
            cursor: pointer;
        }
        .textLink:hover {
            color: #40a9ff;
        }
        .el-input, .el-textarea {
            width: 300px;
        }
        .ellipsis {
            white-space: nowrap;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app" v-cloak>
        <h2>活动方案列表（共{{totalNum}}条）<el-button type="primary" size="small" class="itemSpace" @click="editTemplate('创建')">新建方案</el-button></h2>
        <div class="topTab">
            <el-select v-model="templateForm.tradeId" size="small" @change="getTemplateList">
                <el-option
                    v-for="item in tradeListContainAll"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
            </el-select>
            <el-select v-model="templateForm.relationFlyerState" size="small" class="itemSpace" @change="getTemplateList">
                <el-option
                    v-for="item in relationFlyerStateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="templateList">
            <el-table :data="templateList" @sort-change="sortChange" height="660" border>
                <el-table-column fixed="left" prop="id" label="方案编号" sortable="custom" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="aid" label="账号aid" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="gameId" label="活动编号" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="flyerModId" label="传单识别码" min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.flyerModId || '-' }}
                    </template>
                </el-table-column>
                <el-table-column prop="planName" label="方案名称" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="tradeId" label="行业" min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.tradeId | getTradeName }}
                    </template>
                </el-table-column>
                <el-table-column prop="summary" label="方案简介" min-width="140" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="shinePoint" label="方案亮点" min-width="180" :resizable="false" align="center">
                    <template slot-scope="scope">
                        <fa-popover placement="top">
                            <template slot="content">
                                <pre style="max-width:300px;">{{ scope.row.shinePoint }}</pre>
                            </template>
                            <div class="ellipsis">{{ scope.row.shinePoint }}</div>
                        </fa-popover>
                    </template>
                </el-table-column>
                <el-table-column prop="target" label="活动目标" min-width="180" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="score" label="推荐分" sortable="custom" min-width="180" :resizable="false" align="center"></el-table-column>
                <el-table-column prop="published" label="属性" min-width="140" :resizable="false" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.published ? '发布' : '未发布' }}
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" min-width="180" :resizable="false" align="center">
                    <template slot-scope="scope">
                        <el-button @click="editTemplate('编辑', scope.row.id)" type="primary" size="small">编辑</el-button>
                        <fa-popconfirm
                            placement="topRight"
                            title="确定删除该方案吗？"
                            @confirm="deleteTemplate(scope.row.id)"
                            @cancel="deletePopovervisible = false"
                        >
                            <template slot="okText">继续</template>
                            <template slot="cancelText">取消</template>
                            <el-button type="danger" size="small">删除</el-button>
                        </fa-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background
                @size-change="templateListSizeChange"
                @current-change="templateListPageChange"
                :current-page="templateForm.page"
                :page-sizes="[5, 10, 20, 30, 50, 100]"
                :page-size="templateForm.limit"
                layout="sizes, prev, pager, next, jumper"
                :total="totalNum"
                class="pagination">
            </el-pagination>
        </div>
        <el-dialog :title="getDialogTitle()" :visible.sync="editTemplateDialog" :fullscreen="fullscreen">
            <el-form :model="templateDialogForm" :rules="rules" ref="templateDialogForm">
                <el-form-item label="企业账号：" :label-width="formLabelWidth">
                    <el-input v-model="templateDialogForm.accountId" :disabled="isEditActivePlan" clearable></el-input>
                    <span class="itemSpace">活动编号：</span>
                    <el-input v-model="templateDialogForm.gameId" @keyup.native="templateDialogForm.gameId = onInputNumber(templateDialogForm.gameId)" :disabled="isEditActivePlan" clearable></el-input>
                    <el-button v-show="templateDialogForm.dialogCmd === '创建'" @click="searchTemplateByAccountGameId" type="primary" class="itemSpace">查询</el-button>
                </el-form-item>
                <div v-show="isShowDialogDetail">
                    <el-form-item label="账号aid：" :label-width="formLabelWidth">
                        <span>{{ templateDialogForm.aid }}</span>
                    </el-form-item>
                    <el-form-item label="原型：" :label-width="formLabelWidth">
                        <span>{{ templateDialogForm.prototype }}</span>
                    </el-form-item>
                    <el-form-item label="传单模板识别码（非必填）：" prop="flyerModId" :label-width="formLabelWidth">
                        <el-input v-model="templateDialogForm.flyerModId" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="方案名称：" prop="planName" :label-width="formLabelWidth">
                        <el-input v-model="templateDialogForm.planName" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="行业：" prop="tradeId" :label-width="formLabelWidth">
                        <el-select v-model="templateDialogForm.tradeId" placeholder="请选择">
                            <el-option
                                v-for="item in tradeList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="活动目标：" prop="target" :label-width="formLabelWidth">
                        <el-input v-model="templateDialogForm.target" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="方案概述：" prop="summary" :label-width="formLabelWidth">
                        <el-input v-model="templateDialogForm.summary" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="方案亮点：" prop="shinePoint" :label-width="formLabelWidth">
                        <el-input type="textarea" :rows="4" v-model="templateDialogForm.shinePoint" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="推荐分数：" prop="score" :label-width="formLabelWidth">
                        <el-input v-model="templateDialogForm.score" @keyup.native="templateDialogForm.score = onInputNumber(templateDialogForm.score, 1)" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="属性：" prop="published" :label-width="formLabelWidth">
                        <el-select v-model="templateDialogForm.published">
                            <el-option
                                v-for="item in propertyOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发布时间：" :label-width="formLabelWidth">
                        <span>{{ templateDialogForm.publishedTime | formatDate('yyyy-MM-dd') }}</span>
                    </el-form-item>
                </div>
            </el-form>
            <div v-show="isShowDialogDetail" slot="footer" class="dialog-footer">
                <el-button @click="editTemplateDialog = false">取 消</el-button>
                <el-button type="primary" @click="submitForm('templateDialogForm')">提 交</el-button>
            </div>
        </el-dialog>
    </div>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_core")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_faiHd")%>"></script>
    <script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
	<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_element")%>"></script>
    <%=FrontEndDef.getPackageJs("fa-component", "~1.1.0")%>
    <script>
        var tradeList = <%=tradeList%>.filter(function(item) {
            return item.id != 3;
        });
        var tradeListContainAll = $.extend([], tradeList, true);
        tradeListContainAll.unshift({
            id: 0,
            name: '不限行业'
        });
        var app = new Vue({
            el: "#app",
            data: {
                editTemplateDialog: false,
                deletePopovervisible: false,
                isShowDialogDetail: false,
                fullscreen: true,
                formLabelWidth: '300px',
                cachePlanId: '',
                templateList: [],
                totalNum: 0,
                templateForm: {
                    tradeId: 0,
                    relationFlyerState: -1,
                    page: 1,
                    limit: 10,
                    sorts: [
                        {key: 'id', desc: false}
                    ]
                },
                templateDialogForm: {
                    dialogCmd: '编辑',
                    accountId: '',
                    gameId: '',
                    flyerModId: '',
                    planName: '',
                    tradeId: '',
                    target: '',
                    summary: '',
                    shinePoint: '',
                    score: '',
                    published: '',
                    publishedTime: ''
                },
                rules: {
                    flyerModId: [
                        { max: 20, message: '最长不可超过20个字符', trigger: ['blur', 'change'] }
                    ],
                    planName: [
                        { required: true, message: '方案名称不能为空', trigger: ['blur', 'change'] },
                        { max: 20, message: '最长不可超过20个字符', trigger: ['blur', 'change'] }
                    ],
                    tradeId: [
                        { required: true, message: '请选择行业', trigger: ['blur', 'change'] }
                    ],
                    target: [
                        { required: true, message: '活动目标不能为空', trigger: ['blur', 'change'] },
                        { max: 40, message: '最长不可超过40个字符', trigger: ['blur', 'change'] }
                    ],
                    summary: [
                        { required: true, message: '方案概述不能为空', trigger: ['blur', 'change'] },
                        { max: 40, message: '最长不可超过40个字符', trigger: ['blur', 'change'] }
                    ],
                    shinePoint: [
                        { required: true, message: '方案亮点不能为空', trigger: ['blur', 'change'] },
                        { max: 300, message: '最长不可超过300个字符', trigger: ['blur', 'change'] }
                    ],
                    score: [
                        { required: true, message: '推荐分数不能为空', trigger: ['blur', 'change'] }
                    ],
                    published: [
                        { required: true, message: '请选择属性', trigger: ['blur', 'change'] }
                    ]
                },
                tradeList: tradeList,
                tradeListContainAll: tradeListContainAll,
                relationFlyerStateOptions: [
                    { value: -1, label: '是否关联微传单' },
                    { value: 1, label: '是' },
                    { value: 0, label: '否' }
                ],
                propertyOptions: [
                    { value: false, label: '未发布' },
                    { value: true, label: '发布' }
                ]
            },
            computed: {
                isEditActivePlan: function() {
                    return this.templateDialogForm.dialogCmd === '编辑';
                }
            },
            created: function (){
                this.getTemplateList();
            },
            methods: {
                getDialogTitle: function(){
                    return this.templateDialogForm.dialogCmd + '活动方案';
                },
                getTemplateList: function(){
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/api/activity-plan/list',
                        dataType: 'json',
                        contentType: 'application/json;chast=utf-8',
                        data: $.toJSON(_this.templateForm),
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.totalNum = result.data.total;
                                _this.templateList = result.data.dataList;
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                sortChange: function (obj) { // sortable值为'custom'则为远程排序，监听sort-change 事件
                    this.templateForm.sorts[0].key = obj.prop;
                    this.templateForm.sorts[0].desc = obj.order === 'descending';
                    this.getTemplateList();
                },
                templateListSizeChange: function(val){
                    this.templateForm.limit = val;
                    this.templateForm.page = 1;
                    this.getTemplateList();
                },
                templateListPageChange: function(val){
                    this.templateForm.page = val;
                    this.getTemplateList();
                },
                editTemplate: function(cmd, id){
                    this.templateDialogForm.dialogCmd = cmd;
                    var isEditTemplate = cmd === '编辑';
                    if(isEditTemplate){
                        this.cachePlanId = id;
                        this.searchTemplateById();
                    }else{
                        this.templateDialogForm.accountId = '';
                        this.templateDialogForm.gameId = '';
                        this.isShowDialogDetail = false;
                        this.editTemplateDialog = true;
                    }
                },
                searchTemplateById: function(){
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/api/activity-plan/' + this.cachePlanId,
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.templateDialogForm = $.extend(true, _this.templateDialogForm, {publishedTime: ''}, result.data);
                                _this.isShowDialogDetail = true;
                                _this.editTemplateDialog = true;
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                deleteTemplate: function(id){
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/api/activity-plan/del',
                        data: {
                            planId: id
                        },
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.alertSuccessMsg('删除成功');
                                _this.deletePopovervisible = false;
                                _this.getTemplateList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                // 创建活动方案查询调用
                searchTemplateByAccountGameId: function(){
                    this.isShowDialogDetail = false;
                    var accountId = this.templateDialogForm.accountId;
                    var gameId = this.templateDialogForm.gameId;
                    if(this.checkEmpty(accountId) || this.checkEmpty(gameId)){
                        this.alertErrorMsg('请输入' + (this.checkEmpty(accountId) ? '企业账号' : '活动编号'));
                        return;
                    }
                    var _this = this;
                    $.ajax({
                        type: 'get',
                        url: '/api/activity-plan/aid/' + $.trim(accountId) + '/' + gameId,
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                var defTemplateDialogForm = {
                                    flyerModId: '',
                                    planName: '',
                                    tradeId: '',
                                    target: '',
                                    summary: '',
                                    shinePoint: '',
                                    score: '',
                                    published: '',
                                    publishedTime: ''
                                };
                                _this.templateDialogForm = $.extend(true, _this.templateDialogForm, defTemplateDialogForm, result.data);
                                _this.isShowDialogDetail = true;
                            }else{
                                _this.alertErrorMsg('请检查企业账号或活动编号');
                            }
						}
                    });
                },
                submitForm: function(formName){
                    if(this.checkEmpty(this.templateDialogForm.accountId) || this.checkEmpty(this.templateDialogForm.gameId)){
                        this.alertErrorMsg('请输入' + (this.checkEmpty(this.templateDialogForm.accountId) ? '企业账号' : '活动编号'));
                        return;
                    }
                    var _this = this;
                    this.$refs[formName].validate(function (valid) {
                        if (valid) {
                            var templateDialogForm = $.extend(true, {}, _this.templateDialogForm);
                            _this.reqHandleTemplate(templateDialogForm);
                        } else {
                            _this.alertErrorMsg('请检查设置项');
                            return false;
                        }
                    });
                },
                reqHandleTemplate: function(templateDialogForm){
                    var isAdd = templateDialogForm.dialogCmd === '创建';
                    if(isAdd){
                        delete templateDialogForm.id
                    }
                    var _this = this;
                    $.ajax({
                        type: 'post',
                        url: '/api/activity-plan/' + (isAdd ? 'add' : 'update'),
                        dataType: 'json',
                        contentType: 'application/json;chast=utf-8',
                        data: $.toJSON(templateDialogForm),
                        error: function(){
                            _this.alertErrorMsg();
                        },
                        success: function(result){
                            if(result.success){
                                _this.alertSuccessMsg('提交成功');
                                _this.editTemplateDialog = false;
                                _this.getTemplateList();
                            }else{
                                _this.alertErrorMsg(result.msg);
                            }
						}
                    });
                },
                checkEmpty: function(val){
                    return $.trim(val).length === 0;
                },
                onInputNumber: function(val){
                    return val.replace(/[^\d.]/g, '');
                },
                onInputDecimal: function(num, limit){
                    var str = num;
                    var len1 = str.substr(0, 1);
                    var len2 = str.substr(1, 1);
                    //如果第一位是0，第二位不是点，就用数字把点替换掉
                    if (str.length > 1 && len1 == 0 && len2 != ".") {
                        str = str.substr(1, 1);
                    }
                    //第一位不能是.
                    if (len1 == ".") {
                        str = "";
                    }
                    //限制只能输入一个小数点
                    if (str.indexOf(".") != -1) {
                        var str_ = str.substr(str.indexOf(".") + 1);
                        if (str_.indexOf(".") != -1) {
                            str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
                        }
                    }
                    //正则替换
                    str = str.replace(/[^\d^\.]+/g, ''); // 保留数字和小数点
                    if (limit / 1 === 1) {
                        str = str.replace(/^\D*([0-9]\d*\.?\d{0,1})?.*$/,'$1'); // 小数点后只能输 1 位
                    } else {
                        str = str.replace(/^\D*([0-9]\d*\.?\d{0,2})?.*$/,'$1'); // 小数点后只能输 2 位
                    }
                    return str;
                },
                alertErrorMsg: function(msg){
                    ELEMENT.Message({
                        type: 'error',
                        message: msg || '系统繁忙，请稍后重试'
                    });
                },
                alertSuccessMsg: function(msg){
                    ELEMENT.Message({
                        type: 'success',
                        message: msg
                    });
                },
                alertWarningMsg: function(msg){
                    ELEMENT.Message({
                        type: 'warning',
                        message: msg
                    });
                }
            },
            filters: {
                // 自定义时间格式化过滤器，默认返回yyyy-MM-dd HH:mm
                formatDate: function(timestamp, format) {
                    return typeof timestamp === 'undefined' || timestamp === '' || timestamp === 852048000000 ? '-' : $.format.date(new Date(timestamp), format || 'yyyy-MM-dd HH:mm');
                },
                // 根据行业id获取行业名称
                getTradeName: function(tradeId) {
                    var tradeObj = this.tradeList.filter( function(item) {
                        return item.id === tradeId;
                    })[0];
                    return typeof tradeObj === 'undefined' ? '-' : tradeObj.name;
                }
            }
        });
    </script>
</body>
</html>
