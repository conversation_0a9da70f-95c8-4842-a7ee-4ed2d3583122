package fai.webhdoss.model.vo.scProto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description 原型中script-graphic字段VO
 * <AUTHOR>
 * @Version V1.0.0
 * @Date 2025/4/17 18:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScProtoScriptGraphicVO {
    private List<Integer> styleList;
    private List<Integer> resList;
}
