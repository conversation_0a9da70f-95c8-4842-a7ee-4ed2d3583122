<template>
  <div class="mt-[30px]">
    <p class="mb-[10px] text-[16px] font-bold">表单配置</p>
    <table class="w-[1400px] overflow-hidden">
      <!-- 表头 -->
      <thead class="bg-gray-100 text-[#909399] text-sm font-semibold">
        <tr>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">表单名称（label）</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">字段名称（variable）</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">是否必填</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">字段类型</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">默认填入内容</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">描述</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">AI推荐词</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-center">操作</th>
        </tr>
      </thead>

      <!-- 拖拽 tbody -->
      <draggable
        v-model="templateInfo.inputFormList"
        tag="tbody"
        handle=".handle"
        @end="onDragEnd"
      >
        <tr
          v-for="(row, index) in templateInfo.inputFormList"
          :key="row.id"
          class="hover:bg-gray-50 transition-colors"
        >
          <!-- 表单名称 -->
          <td class="px-4 py-3 border-b border-gray-200">
            <el-form-item
              :prop="'inputFormList.' + index + '.label'"
              :rules="[{ validator: validateLabel, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-model="row.label"
                placeholder="请输入表单名称"
                class="w-56"
                :maxlength="50"
              />
            </el-form-item>
          </td>

          <!-- 字段名称 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.variable }}
          </td>

          <!-- 是否必填 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.required ? '是' : '否' }}
          </td>

          <!-- 字段类型 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.filedType == FieldTypeDef.TEXT ? '文本' : '标签' }}
          </td>

          <!-- 默认填入内容 -->
          <td class="px-4 py-3 border-b border-gray-200" :data-row-id="row.id">
            <el-form-item
              :prop="'inputFormList.' + index + '.desc'"
              :rules="[{ validator: validateDesc, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-if="!row.filedType || (row.filedType === FieldTypeDef.TEXT)"
                v-model="row.defaultContent"
                type="textarea"
                placeholder="请输入默认填入内容"
                class="!w-[250px]"
                show-word-limit
                :maxlength="row.maxLength || 100"
              />

              <div 
                class="tag-input-wrapper" 
                @click="focusInput(row)" 
                v-else
                :class="{ 'tag-input-wrapper--disabled': isTagsLimitReached(row) }"
              >
                <!-- 标签显示 -->
                <span 
                  v-for="(tag, tagIndex) in row._defaultContentTags" 
                  :key="tagIndex" 
                  class="tag"
                  :title="`标签: ${tag}`"
                >
                  {{ tag }}
                  <span 
                    class="tag-close" 
                    @click.stop="removeTag(tagIndex, row)"
                    title="删除标签"
                  >
                    ×
                  </span>
                </span>

                <!-- 输入框 -->
                <input
                  v-model="row._defaultContentTagsValue"
                  @keydown="handleTagsKeydown($event, row)"
                  @blur="addTag(row, row._defaultContentTagsValue)"
                  :placeholder="getInputPlaceholder(row)"
                  :disabled="isTagsLimitReached(row)"
                  class="tag-input"
                />
                
                <!-- 标签计数显示 -->
                <span 
                  v-if="row._defaultContentTags && row._defaultContentTags.length > 0"
                  class="tag-count"
                  :title="`已输入${getTagsCount(row)}/${getTagsTotalLengthLimit(row)}个字符，${row._defaultContentTags.length}/${getTagsLimit}个标签`"
                >
                  {{ getTagsCount(row) }}/{{ getTagsTotalLengthLimit(row) }}
                </span>
              </div>
            </el-form-item>
          </td>

          <!-- 描述 -->
          <td class="px-4 py-3 border-b border-gray-200">
            <el-form-item
              :prop="'inputFormList.' + index + '.desc'"
              :rules="[{ validator: validateDesc, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-model="row.desc"
                type="textarea"
                placeholder="请输入描述"
                class="!w-[250px]"
                :maxlength="60"
              />
            </el-form-item>
          </td>

          <!-- AI推荐词 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            <el-switch v-model="row.openAiTip" />
            <el-button
              type="text"
              v-show="row.openAiTip"
              @click="setAiTip(row)"
              class="ml-2 !p-0 text-blue-500 hover:text-blue-600"
            >
              编辑提示词
            </el-button>
          </td>

          <!-- 拖拽句柄 -->
          <td class="px-4 py-3 border-b border-gray-200 text-center">
            <i
              class="el-icon-rank handle text-gray-400 hover:text-gray-600 cursor-move transition-colors"
            ></i>
          </td>
        </tr>
      </draggable>
    </table>    
    <EditPromptDialog
      :dialog-visible.sync="dialogVisible"
      :promptInfo="promptInfo"
      :inputFormList="templateInfo.inputFormList"
      @confirm="(res) => confirmAiTip(res)"
    />
  </div>
</template>

<script>
import draggable from "vuedraggable";
import EditPromptDialog from "./editPromptDialog.vue";
import { FieldTypeDef } from "@/views/scPortal/config/index.js";

export default {
  name: "InputForm",
  components: {
    EditPromptDialog,
    draggable,
  },
  props: {
    templateInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      FieldTypeDef,
      dialogVisible: false,
      promptInfo: {},
      promptApiKey: auth.isDev ? 'app-3nC2vjJeiwD5MCWtiC433aFe' : 'app-K2OMOkKH109R7ed9N2IuGrYg',
      // 标签限制配置
      tagsConfig: {
        maxTagCount: 10, // 最大标签数量
        maxTotalLength: 100, // 所有标签总长度限制
      }
    };
  },
  computed: {
    /**
     * 获取标签数量限制
     */
    getTagsLimit() {
      return this.tagsConfig.maxTagCount;
    }
  },
  methods: {
    onDragEnd(evt) {
      console.log(evt, 'onDragEnd');
    },
    setAiTip(row) {
      this.dialogVisible = true;
      this.promptInfo = row;
      !this.promptInfo.apiKey && (this.promptInfo.apiKey = this.promptApiKey);
    },
    confirmAiTip(data, row) {
      this.promptInfo.apiKey = data.apikey;
      this.promptInfo.prompt = data.promptText;
    },
    
    /**
     * 验证表单名称
     */
    validateLabel(rule, value, callback) {
      if (!value) {
        callback(new Error('表单名称不能为空'));
        return;
      }
      callback();
    },
    
    /**
     * 验证描述
     */
    validateDesc(rule, value, callback) {
      if (!value) {
        callback(new Error('描述不能为空'));
        return;
      }
      callback();
    },
    
    /**
     * 验证AI推荐词
     */
    validateOpenAiTip(rule, value, callback) {
      console.log(value, 'validateOpenAiTip');
      if (value.openAiTip) {
        if (!value.apiKey) {
          callback(new Error('请输入apiKey'));
          return;
        }
        if (!value.prompt) {
          callback(new Error('请输入提示词'));
          return;
        }
      }
      callback();
    },

    /**
     * 获取标签总长度限制
     * @param {Object} row 当前行数据
     * @returns {number} 总长度限制
     */
    getTagsTotalLengthLimit(row) {
      return (row.maxLength - 10) || this.tagsConfig.maxTotalLength || 100;
    },

    /**
     * 更新标签容器的类名（用于样式控制）
     * @param {Object} row 当前行数据
     */
    updateTagWrapperClass(row) {
      const tagCount = row._defaultContentTags ? row._defaultContentTags.length : 0;
      
      // 在下一个tick中更新类名，确保DOM已更新
      this.$nextTick(() => {
        const wrapper = document.querySelector(`[data-row-id="${row.id}"] .tag-input-wrapper`);
        if (wrapper) {
          wrapper.classList.remove('has-many-tags', 'has-very-many-tags');
          
          if (tagCount >= 7) {
            wrapper.classList.add('has-very-many-tags');
          } else if (tagCount >= 4) {
            wrapper.classList.add('has-many-tags');
          }
        }
      });
    },

    /**
     * 添加标签（增强版本）
     * @param {Object} row 当前行数据
     * @param {string} inputValue 输入的标签值（可选，如果不传则使用row._defaultContentTagsValue）
     */
    addTag(row, inputValue) {
      // 获取输入值，优先使用传入的参数
      const val = (inputValue || row._defaultContentTagsValue || '').toString().trim();
      
      // 验证输入是否为空
      if (!val) {
        return;
      }

      // 去除所有空格
      const processedVal = val.replace(/\s/g, '');
      
      // 验证处理后的值是否为空
      if (!processedVal) {
        this.$message.warning('标签不能为空，请输入有效内容');
        row._defaultContentTagsValue = '';
        return;
      }

      // 初始化标签数组
      if (!row._defaultContentTags) {
        row._defaultContentTags = [];
      }

      // 检查重复标签
      if (row._defaultContentTags.includes(processedVal)) {
        this.$message.warning(`标签"${processedVal}"已存在，请输入其他标签`);
        row._defaultContentTagsValue = '';
        return;
      }

      // 检查标签数量限制
      if (row._defaultContentTags.length >= this.getTagsLimit) {
        this.$message.warning(`最多只能输入${this.getTagsLimit}个标签`);
        row._defaultContentTagsValue = '';
        return;
      }

      // 检查总长度限制（使用row.maxLength）
      const currentTotalLength = row._defaultContentTags.join('').length;
      const newTotalLength = currentTotalLength + processedVal.length;
      const maxTotalLength = this.getTagsTotalLengthLimit(row);
      
      if (newTotalLength > maxTotalLength) {
        this.$message.warning(`所有标签总长度不能超过${maxTotalLength}个字符`);
        row._defaultContentTagsValue = '';
        return;
      }

      // 添加标签
      row._defaultContentTags.push(processedVal);
      row.defaultContent = row._defaultContentTags.join(',');
      row._defaultContentTagsValue = '';

      // 更新容器类名
      this.updateTagWrapperClass(row);

      console.log('标签添加成功:', {
        newTag: processedVal,
        totalTags: row._defaultContentTags.length,
        totalLength: row._defaultContentTags.join('').length,
        maxTotalLength: maxTotalLength
      });
    },

    /**
     * 移除标签
     * @param {number} index 要移除的标签索引
     * @param {Object} row 当前行数据
     */
    removeTag(index, row) {
      if (!row._defaultContentTags || index < 0 || index >= row._defaultContentTags.length) {
        return;
      }
      
      const removedTag = row._defaultContentTags[index];
      row._defaultContentTags.splice(index, 1);
      row.defaultContent = row._defaultContentTags.join(',');
      
      // 更新容器类名
      this.updateTagWrapperClass(row);
      
      console.log('标签移除成功:', {
        removedTag,
        remainingTags: row._defaultContentTags.length
      });
    },

    /**
     * 退格删除标签
     * @param {Object} row 当前行数据
     */
    backspaceDelete(row) {
      // 只有在输入框为空且有标签时才删除最后一个标签
      if (!row._defaultContentTagsValue && row._defaultContentTags && row._defaultContentTags.length > 0) {
        const removedTag = row._defaultContentTags.pop();
        row.defaultContent = row._defaultContentTags.join(',');
        
        // 更新容器类名
        this.updateTagWrapperClass(row);
        
        console.log('退格删除标签:', {
          removedTag,
          remainingTags: row._defaultContentTags.length
        });
      }
    },

    /**
     * 处理标签输入的键盘事件
     * @param {KeyboardEvent} event 键盘事件
     * @param {Object} row 当前行数据
     */
    handleTagsKeydown(event, row) {
      const inputValue = event.target.value;

      if (event.key === 'Enter') {
        event.preventDefault();
        
        // 使用去空格后的值进行验证
        const processedValue = inputValue.replace(/\s/g, '');
        
        if (!processedValue) {
          this.$message.warning('标签不能为空，请输入有效内容');
          return;
        }

        // 检查重复标签（在添加之前检查）
        if (row._defaultContentTags && row._defaultContentTags.includes(processedValue)) {
          this.$message.warning(`标签"${processedValue}"已存在，请输入其他标签`);
          return;
        }

        // 检查标签数量限制
        const currentTagCount = row._defaultContentTags ? row._defaultContentTags.length : 0;
        if (currentTagCount >= this.getTagsLimit) {
          this.$message.warning(`最多只能输入${this.getTagsLimit}个标签`);
          return;
        }

        // 检查总长度限制
        const currentTotalLength = row._defaultContentTags ? row._defaultContentTags.join('').length : 0;
        const newTotalLength = currentTotalLength + processedValue.length;
        const maxTotalLength = this.getTagsTotalLengthLimit(row);
        
        if (newTotalLength > maxTotalLength) {
          this.$message.warning(`所有标签总长度不能超过${maxTotalLength}个字符`);
          return;
        }

        // 调用添加标签方法
        this.addTag(row, inputValue);
      } else if (event.key === 'Backspace') {
        // 退格删除逻辑
        this.backspaceDelete(row);
      }
    },

    /**
     * 聚焦到输入框
     * @param {Object} row 当前行数据
     */
    focusInput(row) {
      // 使用更精确的选择器
      const wrapper = event.currentTarget;
      const input = wrapper.querySelector('input');
      if (input) {
        input.focus();
      }
    },

    /**
     * 获取输入框占位符文本
     * @param {Object} row 当前行数据
     * @returns {string} 占位符文本
     */
    getInputPlaceholder(row) {
      // if (this.isTagsLimitReached(row)) {
      //   return `已达到最大标签数量限制(${this.getTagsLimit})`;
      // }
      
      // const currentLength = this.getTagsCount(row);
      // const maxLength = this.getTagsTotalLengthLimit(row);
      
      // if (currentLength >= maxLength) {
      //   return `已达到最大长度限制(${maxLength})`;
      // }
      
      if (!row._defaultContentTags || row._defaultContentTags.length === 0) {
        return '输入后按回车或移出创建标签';
      }
      // return '继续添加标签...';
    },

    /**
     * 获取当前标签总数
     * @param {Object} row 当前行数据
     * @returns {number} 标签总字符数
     */
    getTagsCount(row) {
      if (!row._defaultContentTags || !Array.isArray(row._defaultContentTags)) {
        return 0;
      }
      return row._defaultContentTags.join('').length;
    },

    /**
     * 检查是否达到标签数量限制
     * @param {Object} row 当前行数据
     * @returns {boolean} 是否达到限制
     */
    isTagsLimitReached(row) {
      const currentCount = row._defaultContentTags ? row._defaultContentTags.length : 0;
      return currentCount >= this.getTagsLimit;
    },

    /**
     * 获取标签状态信息（用于调试）
     * @param {Object} row 当前行数据
     * @returns {Object} 标签状态信息
     */
    getTagsStatus(row) {
      return {
        count: row._defaultContentTags ? row._defaultContentTags.length : 0,
        maxCount: this.getTagsLimit,
        totalLength: this.getTagsCount(row),
        maxTotalLength: this.getTagsTotalLengthLimit(row),
        isLimitReached: this.isTagsLimitReached(row)
      };
    }
  },
};
</script>

<style scoped>
.tag-input-wrapper {
  width: 250px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start; /* 改为flex-start以支持换行 */
  border: 1px solid #dcdfe6;
  padding: 4px 8px;
  min-height: 38px;
  cursor: text;
  box-sizing: border-box;
  border-radius: 4px;
  position: relative;
  transition: border-color 0.3s;
  /* 为标签计数预留右侧空间 */
  padding-right: 50px;
}

.tag-input-wrapper:hover {
  border-color: #c0c4cc;
}

.tag-input-wrapper:focus-within {
  border-color: #409eff;
}

.tag-input-wrapper--disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.tag-input-wrapper--disabled .tag-input {
  background-color: transparent;
  cursor: not-allowed;
}

.tag-input {
  line-height: 20px;
  border: none;
  outline: none;
  flex: 1;
  min-width: 60px;
  background: transparent;
  /* 确保输入框占据剩余空间但不会被覆盖 */
  margin-right: 4px;
}

.tag-input::placeholder {
  color: #c0c4cc;
  font-size: 12px;
}

.tag {
  line-height: 20px;
  background-color: #f0f2f5;
  color: #333;
  padding: 2px 6px;
  margin: 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  /* 移除最大宽度限制，允许标签自然换行 */
  /* max-width: 150px; */
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */
  transition: background-color 0.3s;
  /* 确保标签不会超出容器 */
  max-width: 100%; /* 减去右侧计数区域的宽度 */
  flex-shrink: 0; /* 防止标签被压缩 */
}

.tag:hover {
  background-color: #e6f7ff;
}

.tag-close {
  margin-left: 4px;
  cursor: pointer;
  color: #999;
  font-weight: bold;
  transition: color 0.3s;
  flex-shrink: 0; /* 防止关闭按钮被压缩 */
}

.tag-close:hover {
  color: #f56565;
}

.tag-count {
  position: absolute;
  right: 0;
  top: 2px; /* 改为固定位置，不再居中 */
  /* transform: translateY(-50%); 移除居中变换 */
  font-size: 12px;
  color: #999;
  padding: 0 4px;
  line-height: 30px;
  pointer-events: none;
  /* 确保计数区域不会被其他内容覆盖 */
  z-index: 1;
  white-space: nowrap;
  /* 设置固定宽度避免内容变化时的布局抖动 */
  min-width: 45px;
  text-align: center;
}

/* 当标签很多时，调整容器的最小高度 */
.tag-input-wrapper:has(.tag:nth-child(n+4)) {
  min-height: 60px;
}

.tag-input-wrapper:has(.tag:nth-child(n+7)) {
  min-height: 80px;
}

.tag-input-wrapper:has(.tag:nth-child(n+10)) {
  min-height: 100px;
}

/* 如果浏览器不支持:has选择器，使用类名替代 */
.tag-input-wrapper.has-many-tags {
  min-height: 60px;
}

.tag-input-wrapper.has-very-many-tags {
  min-height: 80px;
}

/* 响应式调整 - 当标签很多时 */
@media (max-width: 1200px) {
  .tag-input-wrapper {
    width: 280px; /* 稍微增加宽度 */
  }
}
</style>
