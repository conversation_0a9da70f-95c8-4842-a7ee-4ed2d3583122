<template>
  <div class="mt-[30px]">
    <p class="mb-[10px] text-[16px] font-bold">表单配置</p>
    <table class="w-[1400px] overflow-hidden">
      <!-- 表头 -->
      <thead class="bg-gray-100 text-[#909399] text-sm font-semibold">
        <tr>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">表单名称（label）</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">字段名称（variable）</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">是否必填</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">字段类型</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">默认填入内容</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">描述</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-left">AI推荐词</th>
          <th class="p-[12px_10px] border-b border-gray-200 text-center">操作</th>
        </tr>
      </thead>

      <!-- 拖拽 tbody -->
      <draggable
        v-model="templateInfo.inputFormList"
        tag="tbody"
        handle=".handle"
        @end="onDragEnd"
      >
        <tr
          v-for="(row, index) in templateInfo.inputFormList"
          :key="row.id"
          class="hover:bg-gray-50 transition-colors"
        >
          <!-- 表单名称 -->
          <td class="px-4 py-3 border-b border-gray-200">
            <el-form-item
              :prop="'inputFormList.' + index + '.label'"
              :rules="[{ validator: validateLabel, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-model="row.label"
                placeholder="请输入表单名称"
                class="w-56"
                :maxlength="50"
              />
            </el-form-item>
          </td>

          <!-- 字段名称 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.variable }}
          </td>

          <!-- 是否必填 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.required ? '是' : '否' }}
          </td>

          <!-- 字段类型 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            {{ row.filedType == FieldTypeDef.TEXT ? '文本' : '标签' }}
          </td>

          <!-- 默认填入内容 -->
          <td class="px-4 py-3 border-b border-gray-200">
            <el-form-item
              :prop="'inputFormList.' + index + '.desc'"
              :rules="[{ validator: validateDesc, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-if="!row.filedType || (row.filedType === FieldTypeDef.TEXT)"
                v-model="row.defaultContent"
                type="textarea"
                placeholder="请输入默认填入内容"
                class="!w-[250px]"
                :maxlength="100"
              />

              <div class="tag-input-wrapper" @click="focusInput" v-else>
                <!-- 标签 -->

                  <span v-for="(tag, index) in row._defaultContentTags" :key="index" class="tag">
                    {{ tag }}
                    <span class="tag-close" @click.stop="removeTag(index, row)">×</span>
                  </span>
      

                <!-- 输入框 -->
                <input
                  v-model="row._defaultContentTagsValue"
                  @keyup.enter="addTag(row, row._defaultContentTagsValue)"
                  @keydown.backspace="backspaceDelete(row)"
                  @blur="addTag(row, row._defaultContentTagsValue)"      
                  ref="inputRef"
                  :placeholder="row._defaultContentTags.length ? '' : '输入后按回车或移出创建标签'"
                />
              </div>
            </el-form-item>
          </td>

          <!-- 描述 -->
          <td class="px-4 py-3 border-b border-gray-200">
            <el-form-item
              :prop="'inputFormList.' + index + '.desc'"
              :rules="[{ validator: validateDesc, trigger: 'change' }]"
              class="mb-0"
            >
              <el-input
                v-model="row.desc"
                type="textarea"
                placeholder="请输入描述"
                class="!w-[250px]"
                :maxlength="60"
              />
            </el-form-item>
          </td>

          <!-- AI推荐词 -->
          <td class="px-4 py-3 border-b border-gray-200 text-gray-700">
            <el-switch v-model="row.openAiTip" />
            <el-button
              type="text"
              v-show="row.openAiTip"
              @click="setAiTip(row)"
              class="ml-2 !p-0 text-blue-500 hover:text-blue-600"
            >
              编辑提示词
            </el-button>
          </td>

          <!-- 拖拽句柄 -->
          <td class="px-4 py-3 border-b border-gray-200 text-center">
            <i
              class="el-icon-rank handle text-gray-400 hover:text-gray-600 cursor-move transition-colors"
            ></i>
          </td>
        </tr>
      </draggable>
    </table>    
    <EditPromptDialog
      :dialog-visible.sync="dialogVisible"
      :promptInfo="promptInfo"
      :inputFormList="templateInfo.inputFormList"
      @confirm="(res) => confirmAiTip(res)"
    />
  </div>
</template>

<script>
import draggable from "vuedraggable";
import EditPromptDialog from "./editPromptDialog.vue";
import { FieldTypeDef } from "@/views/scPortal/config/index.js";
export default {
  name: "InputForm  ",
  components: {
    EditPromptDialog,
    draggable,
  },
  props: {
    templateInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      FieldTypeDef,
      // tags: [],
      // inputValue: '',
      dialogVisible: false,
      promptInfo: {},
      promptApiKey: auth.isDev ? 'app-3nC2vjJeiwD5MCWtiC433aFe' : 'app-K2OMOkKH109R7ed9N2IuGrYg',
    };
  },
  methods: {
    onDragEnd(evt) {
      console.log(evt, 'onDragEnd');
    },
    setAiTip(row) {
      this.dialogVisible = true;
      this.promptInfo = row;
      !this.promptInfo.apiKey && (this.promptInfo.apiKey = this.promptApiKey);
    },
    confirmAiTip(data, row) {
      this.promptInfo.apiKey = data.apikey;
      this.promptInfo.prompt = data.promptText;
    },
    /**
     * 验证表单名称
     */
    validateLabel(rule, value, callback) {
      if (!value) {
        callback(new Error('表单名称不能为空'));
        return;
      }
      callback();
    },
    /**
     * 验证描述
     */
    validateDesc(rule, value, callback) {
      if (!value) {
        callback(new Error('描述不能为空'));
        return;
      }
      callback();
    },
    /**
     * 验证AI推荐词
     */
    validateOpenAiTip(rule, value, callback) {
      console.log(value, 'validateOpenAiTip');
      if (value.openAiTip) {
        if (!value.apiKey) {
          callback(new Error('请输入apiKey'));
          return;
        }
        if (!value.prompt) {
          callback(new Error('请输入提示词'));
          return;
        }
      }
      callback();
    },
    addTag(row) {
      const val = row._defaultContentTagsValue.trim()
      if (val && !row._defaultContentTags.includes(val)) {
        row._defaultContentTags.push(val)
        row.defaultContent = row._defaultContentTags.join(',')
      }
      row._defaultContentTagsValue = ''
    },
    removeTag(index, row) {
      row._defaultContentTags.splice(index, 1)
      row.defaultContent = row._defaultContentTags.join(',')
    },
    backspaceDelete(row) {
      if (!row._defaultContentTagsValue && row?._defaultContentTags?.length) {
        row._defaultContentTags.pop()
        row.defaultContent = row._defaultContentTags.join(',')
      }
    },
    focusInput() {
      this.$refs.inputRef.focus()
    }
  },
};
</script>

<style scoped>
.tag-input-wrapper {
  width: 250px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid #dcdfe6;
  padding: 4px 8px;
  min-height: 38px;
  cursor: text;
  box-sizing: border-box;
  border-radius: 4px;
}
.tag-input-wrapper input {
  line-height: 20px;
  border: none;
  outline: none;
  flex: 1;
  
}
.tag {
  line-height: 20px;
  background-color: #f0f0f0;
  color: #333;
  padding: 2px 6px;
  margin: 2px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.tag-close {
  margin-left: 4px;
  cursor: pointer;
}
</style>
