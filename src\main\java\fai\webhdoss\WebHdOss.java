package fai.webhdoss;

import fai.app.HdOssStaffDef;
import fai.cli.VipCli;
import fai.comm.util.*;
import fai.hdUtil.hostConf.HdBaseHostConf;
import fai.web.*;
import fai.web.inf.HdOss;
import fai.web.inf.Kid;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ConcurrentHashMap;


public class WebHdOss {
	
	private WebHdOss() {
		throw new AssertionError();
	}
	
	public static class CookieDef{
		public static final String TMP_AID = "__tmpaid";
	}

	public void doNothing(){

	}

	public static int getCid() throws Exception {
		int aid = fai.web.Session.getCid();
		if( aid == Web.getFaiCid() ){
			HttpServletRequest request = Core.getRequest();
			int tmpaid = Parser.parseInt( request.getParameter( CookieDef.TMP_AID ), aid);
			if( tmpaid == aid ){
				tmpaid = Parser.parseInt( Request.getCookie( CookieDef.TMP_AID ), aid);
			}
			if( tmpaid != aid ){
				Log.logStd( "faier view user hdoss. aid=%d; faierSid=%d;", tmpaid, getSid() );
			}
			//只有大于0时才改写aid
			if( tmpaid > 0 ){
				aid = tmpaid;
			}
		}
		return aid;
	}
	
	/**
	 * 检测是否faier客服管理员访问用户的hdportal
	 * @return
	 * @throws Exception
	 */
	public static boolean isFaier() throws Exception{
		int cid = getCid();
		int sessionCid = fai.web.Session.getCid();
		// 对于faisco管理员，做特殊支持
		if (sessionCid != cid && sessionCid == Web.getFaiCid()) {
			// 检查是否faisco的客服
			try {
				if (Auth.checkFaiscoAuth("authCs|authPdm|authDev|authIncubator", true)) {
					return true;
				}
			} catch (Exception e) {
				throw e;
			}
		}
		return false;
	}
	
	public static int getSid() throws Exception {
		return fai.web.Session.getSid();
	}
	
	public static Param getAcctInfo() throws Exception {
		return Acct.getAcctInfo(getCid());
	}

	public static Object getCorpKit(Kid id) throws Exception {
		
		int cid = getCid();
		if (cid == 0) {
			throw new WebException(Errno.WEB_NO_LOGIN);
		}
		return Core.getCorpKit(cid, id);
	}

	public static <T> T getCorpKit(Class<T> cls) throws Exception {
		int cid = getCid();
		if (cid == 0) {
			throw new WebException(Errno.WEB_NO_LOGIN);
		}
		return Core.getCorpKit(cid, cls);
	}

	public static Object getCorpKit(int aid, Kid id) throws Exception {
		if (aid == 0) {
			throw new WebException(Errno.WEB_NO_LOGIN);
		}
		return Core.getCorpKit(aid, id);
	}

	public static <T> T getCorpKit(int aid, Class<T> cls) throws Exception {
		if (aid <= 0) {
			throw new WebException(Errno.WEB_NO_LOGIN);
		}
		return Core.getCorpKit(aid, cls);
	}
	
	/**
	 * 通过配置文件修改登录模式
	 * true：使用单点登录模式
	 * @return
	 * @throws Exception
	 */
	public static boolean useFaiSession() throws Exception {
//		Param conf = Web.getConf("ossConf");
//		if(conf == null) {
//			conf = new Param();
//		}
//		Log.logStd("%s useFaiSession"+conf.getBoolean("useFaiSession", false));
//		return conf.getBoolean("useFaiSession", false);
		return true;
	}
	public static boolean checkSession(HttpServletResponse response) throws Exception {
		int cid =  fai.web.Session.getCid();
		if (cid != Web.getFaiCid()) {
			HdJumper.sendRedirect(response, "/login.jsp?url=" + Encoder.encodeUrl(Request.getFullUrl()));
			return false;
		}
		return true;
	}
	
	public static void checkSession() throws Exception {
		int cid =  fai.web.Session.getCid();
		if (cid != Web.getFaiCid()) {
			throw new WebException(Errno.WEB_NO_LOGIN);
		}
	}
	/** 
	 * 获取登录地址
	 * @return Param
	 * @throws Exception
	 */
	public static String getAccoutLoginUrl() {
		String loginUrl = "";
		if (Web.getDebug()) {
			loginUrl = "http://account.aaa.cn/page/login.jsp?backUrl=";
		}else if (Web.isPre()) {
			loginUrl = "http://account.faisco.cn.faidev.cc/page/login.jsp?backUrl=";
		}else{
			loginUrl = "http://account.faisco.cn./page/login.jsp?backUrl=";
		}
		return loginUrl;
	}
	public static String getDomainUrl() throws Exception{
		return "http://" + HdBaseHostConf.getHdOssHost(Web.getEnvMode());
	}
	public static String getOssDomainUrl() throws Exception{
		if(Web.getDebug()){
			return "http://o.aaa.cn";
		}

		return "http://o." + HdBaseHostConf.getTopHost(Web.getEnvMode());
	}
	
	public static String getCrmDomain(){
		String crmDomain =  "http://crm." + HdBaseHostConf.getTopHost(Web.getEnvMode());
		return crmDomain;
	}
	
	
	public static String checkAjaxException(Exception exp) throws Exception {
		
		if (exp instanceof WebException) {
			WebException wexp = (WebException)exp;
			int errno = wexp.getErrno();
			if (errno == Errno.WEB_NO_LOGIN) {
				return "{\"success\":false, \"msg\":\"请重新登录\"}";
			}
			if (errno == Errno.WEB_NO_AUTH) {
				Log.logErr(exp, "no auth");
				return "{\"success\":false, \"msg\":\"没有权限\"}";
			}
			if (errno == Errno.WEB_TOKEN_ERR) {
				Log.logErr(exp, "token err");
				return "{\"success\":false, \"msg\":\"权限错误，刷新重试。\"}";
			}
		}
		Log.logErr(exp);
		return "{\"success\":false, \"msg\":\"系统错误\"}";
	}
	public static boolean checkHdOssAuth(int needCheckAuth) throws Exception {
		return checkHdOssAuth(needCheckAuth,false) ;
	}
	/**
	 * 
	 * @param needCheckAuth
	 * @param actual 是否校验真实权限 
	 * @return
	 * @throws Exception
	 */
	public static boolean checkHdOssAuth(int needCheckAuth,boolean actual) throws Exception {
		int sid =  fai.web.Session.getSid();
		HdOss  hdOss = (HdOss)Core.getCorpKit(1,Kid.HD_OSS);
		Param staff = hdOss.getStaff(sid);
		if(staff == null || staff.isEmpty()){
			return false;
		}
		int auth = staff.getInt(HdOssStaffDef.Info.AUTH,0);
		if(!actual){//不是真实的权限 
			//权限校验
			if(Misc.checkBit(auth,HdOssStaffDef.Auth.ALL)){//管理员直接返回true
				return true;
			}
			//销售管理并且校验的是销售系统权限，直接返回true
			if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_MANAGE) && (needCheckAuth == HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER || needCheckAuth == HdOssStaffDef.Auth.HD_SALE) ){
				return true;
			}
			//销售组长并且校验的是销售权限，直接返回true
			if(Misc.checkBit(auth,HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER) && ( needCheckAuth == HdOssStaffDef.Auth.HD_SALE) ){
				return true;
			}
		}
		return Misc.checkBit(auth,needCheckAuth) ;
	}
	
	/**
	 * 用于查询助手版问题，其通过Vip表查找对应的助手版本
	 * 互动销售可查看
	 */
	public static FaiList<Param> searchVip(SearchArg searchArg) throws Exception {
		if(searchArg == null || searchArg.matcher == null){
			throw new WebException("searchArg or searchArg.matcher is null");
		}
		VipCli cli = new VipCli(Core.getFlow());
		if (!cli.init()) {
			throw new WebException("VipCli init error");
		}
		FaiList<Param> infoList = new FaiList<Param>();
		int rt = cli.searchVip(searchArg, infoList);
		if (rt == Errno.OK || rt == Errno.NOT_FOUND) {
			return infoList;
		}
		throw new WebException(rt, "get list error");
	}

	/**
	 * hd从库
	 */
	public static Dao getHdSlave(){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,"hd").getDao();
	}


	/**
	 * hdBss从库
	 */
	public static Dao getHdBssDaoSlave(){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,"hdBss").getDao();
	}

	/**
	 * hdBss主库
	 */
	public static Dao getHdBssDaoMaster(){
		return getDaoPool(FaiDbUtil.Type.PRO_MASTER,"hdBss").getDao();
	}
	
	/**
	 * hdOssAffair从库
	 */
	public static Dao getHdOssAffairDaoSlave(){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,"hdOssAffair").getDao();
	}
	
	/**
	 * hdOssAffair主库
	 */
	public static Dao getHdOssAffairDaoMaster(){
		return getDaoPool(FaiDbUtil.Type.PRO_MASTER,"hdOssAffair").getDao();
	}
	
	/**
	 * ykProf从库
	 */
	public static Dao getYkProfDao(){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,"ykProf").getDao();
	}
	
	
	/**
	 * ykOss主库
	 */
	public static Dao getYkOssDaoMaster(){
		return getDaoPool(FaiDbUtil.Type.PRO_MASTER,"ykOss").getDao();
	}
	
	/**
	 * ykOss从库
	 */
	public static Dao getYkOssDaoSlave(){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,"ykOss").getDao();		
	}

	/**
	 * 根据实例名获取数据库连接池，默认拿从库，本地环境本地库
	 */
	public static DaoPool getDaoPool(String instance){
		return getDaoPool(FaiDbUtil.Type.PRO_SLAVE,instance);
	}

	private static DaoPool getDaoPool(int type,String instance){
		if(Web.getDebug()){
			type = FaiDbUtil.Type.DEV_MASTER;			
		}
		String key = instance+type;
		//map里有DaoPool就直接拿，不然创建一个DaoPool，再放入map中
		if(s_daoPoolMap.containsKey(key)){
			return s_daoPoolMap.get(key);
		}else{
			return getDaoPooladdMap(type,instance);
		}
	}

	private static DaoPool getDaoPooladdMap(int type,String instance){
		//获取数据库连接信息
		Param p = FaiDbUtil.getDbInfo(type,instance);
		Log.logStd("dao info %s",p);
		if(p == null){
			return null;
		}		
		
		//数据库信息--------------------------------------------------------------------------
		int maxSize = 30;
		String ip = p.getString(FaiDbUtil.DbInfo.IP);
		int port = p.getInt(FaiDbUtil.DbInfo.PORT);
		String db = p.getString(FaiDbUtil.DbInfo.DATABASE);
		String user = p.getString(FaiDbUtil.DbInfo.USER);
		String pwd = p.getString(FaiDbUtil.DbInfo.PASSWORD);
		//------------------------------------------------------------------------------------
		String key = instance+type;
		DaoPool dp = new DaoPool(key,maxSize,ip,port,db,user,pwd);
		s_daoPoolMap.put(key,dp);
		return dp;
	}
	
	private static final ConcurrentHashMap<String, DaoPool> s_daoPoolMap = new ConcurrentHashMap<String, DaoPool>();
}