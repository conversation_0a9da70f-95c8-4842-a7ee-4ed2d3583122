<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%-- <%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}

if(!Web.getDebug() && WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
	out.println("请到新销售系统进行操作");
	return;
} 

%> --%>



<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>售前领取记录</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
		<style scoped>
			.qrcodeImage {
				width: 160px;
				height: 160px;
			}
		</style>
	</head>
	<body id="hdsale-approve">

		<!--查询条件 start-->
		<div class="fai-approve-search" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<el-form-item label="原活动aid">
					<el-input v-model="form.sourceAid" placeholder="" class="fai-wid-100"></el-input>
				</el-form-item>
			    <el-form-item label="复制至aid">
					<el-input v-model="form.targetAid" placeholder="" class="fai-wid-100"></el-input>
				</el-form-item>
				<el-form-item label="操作人">
					<el-input v-model="form.operator" placeholder="" class="fai-wid-100"></el-input>
				</el-form-item>
			    <el-form-item label="复制时间">
			    	<el-checkbox v-model="form.approveDateFlag"></el-checkbox>
					<el-date-picker class="fai-date" v-model="form.startTime" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.endTime" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
			    </el-form-item>
				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="fai-approve-list" v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border>
				<el-table-column label="原活动aid" prop="sourceAid" width="100px"></el-table-column>
				<el-table-column label="复制至aid" prop="targetAid" width="160px"></el-table-column>
				<el-table-column label="活动id" prop="targetGameId" width="160px"></el-table-column>
				<el-table-column label="活动链接" prop="gameLink" width="160px">
					<template slot-scope="scope">
						<el-popover
							placement="top">
						<a v-bind:href="scope.row.gameLink" target='_blank'>{{scope.row.gameLink}}</a>
						<el-button slot="reference">查看活动链接</el-button>
						</el-popover>
					</template>
				</el-table-column>
				<el-table-column label="活动二维码" prop="gameCode" width="160px">
					<template slot-scope="scope">
						<span v-if="scope.row.gameCode != ''">
							<el-popover placement="top" width="200">
								<img  v-bind:src="scope.row.gameCode"></img>
								<el-button slot="reference">查看二维码</el-button>
							</el-popover>
						</span>
						<span v-else>无</span>
					</template>
				</el-table-column>
				<el-table-column label="操作人" prop="operator" width="160px"></el-table-column>
				<el-table-column label="复制时间" prop="createTime" width="160px">
					<template slot-scope="scope">
						{{getCreateTime(scope.row.createTime)}}
					</template>
				</el-table-column>
			</el-table>
			<div class="block">
    			<el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" layout="total, sizes, prev, pager, next, jumper" 
      					:current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="limit" :total="total">
    			</el-pagination>
 			</div>
		</div>
		
		<!--数据表格 end-->

	</body>
	<%-- <script src="https://cdn.jsdelivr.net/npm/axios@0.18.0/dist/axios.js"></script> --%>
	<script src="<%=HdOssResDef.getResPath("js_axios")%>"></script>
	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.fai-approve-search',
			data: {
				form: {//这里是为了填充默认值
					sourceAid: "",
					targetAid: "",
					operator: "",
					approveDateFlag: false,
					startTime: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					endTime: Fai.tool.dateFormatter(new Date()),
					currentPage: 1,
					limit: 10
				},
				editable: false,
				clearable: false,
			},
			created:function(){
				getDataList(this.form);
				console.log("id = " + this.$route);
			},
			methods: {
				onSubmit() {
					console.log("test");
					getDataList(this.form);
				},
				
		    }
		});
		
		var faiDataList = new Vue({
			el: '.fai-approve-list',
			data: {
				tableData: [],
				currentPage: 1,
				limit: 10,
				total: 0,
			},
			created:function(){
			},
			methods: {
				handleSizeChange(val) {
					faiSearchObj.form.limit = val;
		        	getDataList(faiSearchObj.form);
			    },
			    handleCurrentChange(val) {
			    	faiSearchObj.form.currentPage = val;
			        getDataList(faiSearchObj.form);
				},
				getCreateTime(createTime) {
					return Fai.tool.dateFormatter(createTime);
				}
		    }
		});
        
		// 获取数据
		function getDataList(urlParam){
			// 查询数据
			var formData = JSON.parse(JSON.stringify(urlParam));
			if(!formData.approveDateFlag) {
				formData.startTime = '';
				formData.endTime = '';
			}
			delete formData.approveDateFlag;
			Fai.http.post("hdProduct_h.jsp?cmd=getCopyRecordList", formData, false).then(result => {
				if(result.success){
					faiDataList.tableData = result.list;
					faiDataList.total = result.totalSize;
					faiDataList.total = result.totalSize;
				}
			});
		}

	</script>

</html>



