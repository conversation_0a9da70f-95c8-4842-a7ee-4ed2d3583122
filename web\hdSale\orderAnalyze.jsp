<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE)){
	out.println("没有权限");
	return;
}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-resourceOrderAnalyze">
		<!--查询条件 start-->
		<div class="resource" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
				<span>
					<span class="demonstration">领取时间: <!-- </span><el-checkbox v-model="form.receiveTime.check"></el-checkbox></span> -->
					<el-date-picker class="fai-daterange"  type="date" v-model="form.receiveTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
					<el-date-picker class="fai-daterange" type="date" v-model="form.receiveTime.end" placeholder="选择日期" :editable=false></el-date-picker>
				
				<span>
				<!--排列顺序
				  <el-select v-model="form.sortBy">
				       <el-option  label="付费arpu倒序" value="0"></el-option>
					   <el-option  label="付费arpu顺序" value="1"></el-option>
					   <el-option  label="领取数量倒序" value="2"></el-option>
					   <el-option  label="领取数量顺序" value="3"></el-option>
					   <el-option  label="成单数量倒序" value="4"></el-option>
					   <el-option  label="成单数量顺序" value="5"></el-option>
				</el-select>-->
				<span class="demonstration">付款时间: <!-- </span><el-checkbox v-model="form.receiveTime.check"></el-checkbox></span> -->
					<el-date-picker class="fai-daterange"  type="date" v-model="form.payTime.start" placeholder="选择日期" :editable=false ></el-date-picker>-
					<el-date-picker class="fai-daterange" type="date" v-model="form.payTime.end" placeholder="选择日期" :editable=false></el-date-picker>
				
				</span>
				<span>
				销售组：
					<el-select v-model="form.type.value">
						<el-option v-for="select in form.type.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
					</el-select>
				</span>
				<el-form-item>
						<el-button type="primary"  @click="getDataListByArgs('select')">查询</el-button>
						<el-button type="primary"  @click="getDataListByArgs('export')">导出</el-button>
				</el-form-item>

				<div>
					<el-table :data="form.dataList" style="width: 1000" max-height="620" stripe>
						
						<el-table-column  prop="resourceType" label="资源类别" width="120"  align="center"></el-table-column>
						<el-table-column  prop="receiveCount" label="领取数量" width="160"  align="center"></el-table-column>
						<el-table-column  prop="orderCount" label="成单数量" width="160" align="center"></el-table-column>
						<el-table-column  prop="inversionRate" label="转化率" width="120"  align="center"></el-table-column>
						<el-table-column  prop="payPrice" label="付费金额" width="160"  align="center"></el-table-column>
						<el-table-column  prop="payArpu" label="付费arpu" width="160"  align="center"></el-table-column>
						
					</el-table>
				</div>
				<!-- <div class="block">
						<el-pagination  @current-change="getDataListByArgs('select')" :current-page.sync="pageData.currentPage" 
							:page-size="pageData.size" layout="total,prev, pager, next, jumper" :total="pageData.total">
						</el-pagination>
				</div> -->
			</el-form>
		</div>
		<!--查询条件 end-->
	</body>
	
	<script type="text/javascript">
		var rerourceAnalyze = new Vue({
			el: '.resource',
			data: {
				pageData:{
					size:10,
					total:0,
					currentPage:1
				},
				form : {
					receiveTime : {
						start :Fai.tool.dateFormatter(new Date().setDate(1)),
						end : Fai.tool.dateFormatter(new Date())
					},
					payTime:{
						start :Fai.tool.dateFormatter(new Date().setDate(1)),
						end : Fai.tool.dateFormatter(new Date())
					},
				   dataList:[],
				   exportFlag:false,
				   type:{
						value:0,
						name:"全部",
						labelList:[
							{name:"全部",label:0},
							{name:"真实销售",label:1},
							{name:"虚拟销售",label:2},
						]
					},
				},
		        sortBy:"0"
			},
			created:function(){
				this.getDataListByArgs('select');
			},
			methods: {
				getDataListByArgs(val) {
					var excel=val==='export'
					var recStart=this.form.receiveTime.start
					var recEnd=this.form.receiveTime.end
					var paStart=this.form.payTime.start
					var paEnd=this.form.payTime.end
					if(new Date(recStart).getTime()>new Date(recEnd).getTime()
							||new Date(paStart).getTime()>new Date(paEnd).getTime()||
							recStart==null||recEnd==null||paStart==null||paEnd==null){
						alert("时间参数异常");
						return;
					}
					if(new Date(recStart).getTime()>new Date(paStart).getTime()){
						alert("付款时间不能早于领取时间");
						return;
					}
					var arg = {
							"cmd":"resourceOrderAnalyze",
							"receiveTimeStart":Fai.tool.dateFormatter(this.form.receiveTime.start),
							"receiveTimeEnd":Fai.tool.dateFormatter(this.form.receiveTime.end),
							"payTimeStart":Fai.tool.dateFormatter(this.form.payTime.start),
							"payTimeEnd":Fai.tool.dateFormatter(this.form.payTime.end),
							"type":this.form.type.value,
							"exportFlag":excel,
					}
					if(val === 'export'){
						window.location.href = '/ajax/hdSale/orderAnalyze_h.jsp'+Fai.tool.parseJsonToUrlParam(arg,true);
					}else{
						Fai.http.post("hdSale/orderAnalyze_h.jsp",arg, false).then(result => {
							if(result.success){
								this.form.dataList = result.dataList;
							}
					   });
					}
					
				},
				
			}
		});


	</script>
</html>