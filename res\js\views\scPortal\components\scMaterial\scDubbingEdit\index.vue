<template>
  <div class="pt-[30px]">
    <el-breadcrumb separator="/" class="mb-[30px]">
      <el-breadcrumb-item
        class="text-[18px] font-bold cursor-pointer"
        @click.native="goBack"
        >素材管理</el-breadcrumb-item
      >
      <el-breadcrumb-item class="text-[18px]">{{
        curTitle
      }}</el-breadcrumb-item>
    </el-breadcrumb>
    <el-form ref="dubbingForm" :model="dubbingInfo" :rules="rules" class="w-[850px]">
      <div class="flex justify-start gap-x-12 ">
        <div class="w-[400px] flex-shrink-0">
          <el-form-item
            label="名称："
            prop="name"
            :rules="{ required: true, message: '请输入名称' }"
          >
            <el-input placeholder="请输入名称" v-model="dubbingInfo.name" class="!w-[250px]"/>
          </el-form-item>
          <el-form-item
            label="音色ID："
            prop="voiceType"
            :rules="{ required: true, message: '请输入音色ID' }"
          >
            <el-input placeholder="请输入音色ID" v-model="dubbingInfo.voiceType" class="!w-[250px]"/>
          </el-form-item>
          <el-form-item
            label="音色类型："
            prop="extraType"
            :rules="{ required: true, message: '请选择音色类型' }"
          >
            <el-select v-model="dubbingInfo.extraType" placeholder="请选择音色类型" class="!w-[250px]">
              <el-option
                v-for="item in voiceTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="分类："
            prop="categoryId"
            :rules="{ required: true, message: '请选择分类' }"
          >
            <el-select v-model="dubbingInfo.categoryId" placeholder="请选择分类" class="!w-[250px]">
              <el-option
                v-for="item in musicCategory"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="屏蔽自动推荐：">
            <el-switch v-model="dubbingInfo.blockAuto"></el-switch>
          </el-form-item>
          <el-form-item
            label="封面："
            prop="cover"
            :rules="{ required: true, message: '请上传封面' }"
          >
            <UploadImg actionUrl="/api/resource/uploadVoice" :file-list="dubbingInfo._coverList" @upload-success="uploadCoverSuccess" @upload-remove="uploadCoverRemove"/>
          </el-form-item>
        </div>
        <div v-if="previewAudioUrl" class="p-6 border border-gray-300 rounded-lg w-[400px] h-[150px]">
          <div class="text-base text-gray-700 mb-4 text-[14px]">音频试听</div>
          <audio :src="previewAudioUrl" controls class="w-full" />
        </div>
      </div>

      <div class="flex justify-center mt-[200px]">
        <el-button
          type="primary"
          class="!w-[200px] sc-edit-save-btn"
          @click="saveConfig"
          >保存</el-button
        >
      </div>
    </el-form>
  </div>
</template>

<script>
import UploadImg from "@/views/scPortal/components/scTemplate/common/uploadImg.vue";
import {ScMaterialType} from "@/views/scPortal/config/index.js";
import {addMaterial, getCategoryList, updateMaterial} from "@/views/scPortal/api/scMaterial";
import {getResUrl} from '@/views/scPortal/utils/index.js';


export default {
  name: "ScDubbingEdit",
  components: {
    UploadImg,
  },
  props: {
    /**
     * 编辑信息
     */
    editInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dubbingInfo: {
        type: ScMaterialType.DUBBING,
        name: "", // 标题
        voiceType: null, // 音色ID
        categoryId: null, // 分类id
        cover: "", // 封面id
        coverType: "", // 封面类型
        extraType: null, // 音色类型
        recommend: false, // 自动推荐（默认关闭）
      },
      rules: {
        name: [{ required: true, message: "请输入名称" }],
        categoryId: [{ required: true, message: "请选择分类" }],
        cover: [{ required: true, message: "请上传封面" }],
      },
      musicCategory: [], // 分类列表
      voiceTypeList: [
        {
          value: 1,
          label: '腾讯云-大模型音色',
        },
        {
          value: 2,
          label: '腾讯云-精品音色',
        },
        {
          value: 3,
          label: "火山引擎"
        }
      ]
    }; 
  },
  computed: {
    // voiceTypeList() {
    //   return Object.keys(DubbingExtraType).map(key => {
    //     return {
    //       value: Number(key),
    //       label: DubbingExtraType[key],
    //     }
    //   });
    // },
    isEdit() {
      return Object.keys(this.editInfo).length > 0;
    },
    curTitle() {
      return this.isEdit ? "编辑配音" : "添加配音";
    },
    previewAudioUrl() {
      if (this.isEdit && this.editInfo.resId) {
        return getResUrl(this.editInfo.fileType, this.editInfo.resId);
      }
      return '';
    },
  },
  watch: {
    editInfo: {
      handler(val) {
        if (Object.keys(val).length > 0) {
          this.dubbingInfo = val;
          if (val.cover) {
            this.dubbingInfo._coverList = [{
              id: val.cover,
              name: val.name,
              type: val.coverType,
              url: getResUrl(val.coverType, val.cover),
            }];
          }
        } else {
          this.dubbingInfo = {
            type: ScMaterialType.DUBBING,
            name: "", // 标题
            voiceType: null, // 音色ID
            categoryId: null, // 分类id
            cover: "", // 封面id
            coverType: "", // 封面类型
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getCategoryList();
  },
  methods: {
    handleSuccess(response, file, fileList) {
      console.log(response, file, fileList);
    },
    goBack() {
      this.$emit("changeComponent", "ScMaterialList");
    },
    /**
     * 获取分类列表
     */
    getCategoryList() {
      getCategoryList({
        type: Number(ScMaterialType.DUBBING),
      }).then(res => {
        if (res.success) {
          console.log(res.data, 'getCategoryList11');
          this.musicCategory = res.data.filter(item => item.id !== 0); // 去掉【全部】
        } else {
          this.$message.error(res.msg || '系统错误，请稍后再试');
        }
      });
    },
    /**
     * 上传封面成功
     * @param file 文件
     */
    uploadCoverSuccess(file) {
      this.$set(this.dubbingInfo, "cover", file.id);
      this.$set(this.dubbingInfo, "coverType", file.type);
      // 手动触发表单项的验证
      this.$nextTick(() => {
        this.$refs.dubbingForm.validateField("cover");
      });
    },
    /**
     * 上传封面移除
     * @param file 文件
     */
    uploadCoverRemove(file) {
      this.$set(this.dubbingInfo, "cover", "");
      this.$set(this.dubbingInfo, "coverType", "");
    },
    /**
     * 保存
     */
    saveConfig() {
      this.$refs.dubbingForm.validate(valid => {
        if (valid) {
          // const {
           
          //   name = '', // 标题
          //   voiceType = null, // 音色ID
          //   categoryId = null, // 分类id
          //   cover = "", // 封面id
          //   coverType = "", // 封面类型
          //   extraType = null, // 音色类型
          //   recommend = false, // 自动推荐（默认关闭）
          // } = 
          if (this.isEdit) {
            updateMaterial(this.dubbingInfo).then(res => {
              if (res.success) {
                this.$message.success('保存成功');
                this.$emit("changeComponent", "ScMaterialList", {
                  isReloadList: true,
                });
              } else {
                this.$message.error(res.msg || '系统错误，请稍后再试');
              }
            });
          } else {
            addMaterial(this.dubbingInfo).then(res => {
              if (res.success) {
                this.$message.success('保存成功');
                this.$emit("changeComponent", "ScMaterialList", {
                  isReloadList: true,
                });
              } else {
                this.$message.error(res.msg || '系统错误，请稍后再试');
              }
            });
          }
        } else {
          this.$message.error('请检查输入内容');
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.sc-edit-save-btn {
  ::v-deep span {
    float: none;
  }
}
</style>
