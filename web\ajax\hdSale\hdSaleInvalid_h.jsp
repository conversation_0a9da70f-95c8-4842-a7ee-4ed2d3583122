<%@ page import="fai.app.*" %>
<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.web.inf.Kid" %>
<%@ page import="fai.web.inf.SysBssStat" %>
<%@ page import="fai.web.inf.SysPreSaleHd" %>
<%@ page import="fai.webhdoss.WebHdOss" %>
<%@ page import="fai.weboss.OssPoi" %>
<%@ page import="fai.weboss.OssVip" %>
<%@ page import="fai.weboss.WebOss" %>
<%@ page import="java.util.Calendar" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.sdk.fdpdata.operator.IN" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ page import="fai.comm.fdpdata.ResultSet" %>
<%@ page import="fai.sdk.fdpdata.FdpDataSDK" %>
<%@ page import="fai.sdk.fdpdata.expression.AND" %>
<%@ page import="fai.sdk.fdpdata.operator.EQ" %>
<%@ page import="fai.comm.fdpdata.FdpDataParamMatcher" %>

<%!
    private static final class Cmd {
        public static final String TRANS_TO_INVALID = "transToInvalid";
        public static final String GET_INVALID_DATA_LIST = "getInvalidDataList";
        public static final String RELEASE_INVALID = "releaseInvalid";
    }
    private static final class Key {
        private static final String IS_EXPORT = "isExport";
        private static final String ORDER_INFO = "orderInfo";
        private static final String VIP_INFO = "vipInfo";
        private static final String MP_VERSION = "mpVersion";
        private static final String AID_LIST = "aidList";
        private static final String  CMD = "cmd";
        private static final String  LIST = "list";
        private static final String SALES_LIST = "salesList";
        private static final String  TOTAL = "total";
        private static final String  CURRENT_PAGE = "currentPage";
        private static final String  RT = "rt";
        private static final String  SUCCESS = "success";
        private static final String  DATA = "data";
        private static final String  MSG = "msg";

        private static final String  ACCT_INFO = "acctInfo";

        private static final String  IS_PAY = "isPay";
        private static final String  PAY_TIME_SELECT = "payTimeSelect";
        private static final String  CREATE_TIME_SELECT = "createTimeSelect";
        private static final String  LOGIN_TIME_SELECT = "loginTimeSelect";
        private static final String  PAY_TIME_BEG = "payTimeBeg";
        private static final String  PAY_TIME_END = "payTimeEnd";
        private static final String  CREATE_TIME_BEG = "createTimeBeg";
        private static final String  CREATE_TIME_END = "createTimeEnd";
        private static final String  LOGIN_TIME_BEG = "loginTimeBeg";
        private static final String  LOGIN_TIME_END = "loginTimeEnd";

    }
    private static final class Message {
        public static final String NO_METHOD = "没有找到方法";
        public static final String SYS_ERROR = "系统错误";
        public static final String ARG_ERR_AID = "aid错误";
        public static final String ARG_ERR_SALES = "销售账号错误，不能转移其他销售的资源";
        public static final String TRANS_ERROR = "转入无效库失败";
        public static final String TRANS_SUCCESS_LOG_ERROR = "转入无效库成功，但记录操作失败";
        public static final String TRANS_SUCCESS = "转入无效库成功";
        public static final String ARG_ERR_TYPE = "入库类型错误";
        public static final String ARG_ERR_TIME = "时间格式错误";
        public static final String ARG_ERR = "参数错误";
        public static final String NO_AUTH = "没有权限";
        public static final String RELEASE_ERROR = "系统错误，批量转出失败！";
        public static final String GET_LIST_SUCCESS = "查询无效库成功";
        public static final String TRANS_OUT_SUCCESS = "转出成功";
    }
    private static final class Value {
        public static final String DATE_FORMAT_ALL = "yyyy-MM-dd hh:mm:ss";
        public static final String TIME_START = " 00:00:00";
        public static final String TIME_END = " 23:59:59";
    }
    private Param getResult(int rt, boolean success, String msg) {
        Param param = new Param();
        param.setInt(Key.RT, rt);
        param.setBoolean(Key.SUCCESS, success);
        param.setString(Key.MSG, msg);
        return param;
    }
%>

<%!

    // 从当月库，A库，B库，转入无效库
    private String transToInvalid(HttpServletRequest request) throws Exception {
        // 首先释放，释放的逻辑是从acctPreSalHd表删除记录
        // 再把资源add进无效库，最终逻辑其实就是直接将status改为无效库即可
        int aid = Parser.parseInt(request.getParameter(PreSaleHdDef.Info.AID), 0);
        int invalidType = Parser.parseInt(request.getParameter(PreSaleHdDef.Info.INVALID_TYPE), 0);
        String invalidMark = Parser.parseString(request.getParameter(PreSaleHdDef.Info.INVALID_MARK), "");
        if (aid == 0) {
            return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_AID).toJson();
        }
        if (invalidType == 0) {
            return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TYPE).toJson();
        }

        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        Param info = sysPreSaleHd.getInfo(aid); // 获取当前信息

        String sacct = WebOss.getSacct();
        if (sacct.isEmpty() || !sacct.equals(info.getString(PreSaleHdDef.Info.SALES_ACCT))) {
            return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_SALES).toJson();
        }

        int rt = 0;
        final String TABLE = "acctPreSaleHd";
        Dao ossBsDao = null;
        try {
            ossBsDao = WebOss.getOssBsDao();

            Param data = new Param();
            data.setInt(PreSaleHdDef.Info.STATUS, PreSaleHdDef.Status.INVALID_LIB);
            data.setInt(PreSaleHdDef.Info.INVALID_TYPE, invalidType);
            data.setString(PreSaleHdDef.Info.INVALID_MARK, invalidMark);
            data.setCalendar(PreSaleHdDef.Info.UPDATE_TIME, Calendar.getInstance());
            data.setCalendar(PreSaleHdDef.Info.CREATE_TIME, Calendar.getInstance());

            ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.EQ, aid);
            rt = ossBsDao.update(TABLE, new ParamUpdater(data), matcher);
            if (rt != Errno.OK) {
                return getResult(rt, false, Message.TRANS_ERROR).toJson();
            }
            int attrType = HdSaleRecordDef.AttrType.ENTER_INVALID; // 主动转入无效库
            String statusName = PreSaleHdDef.getStatusName(info.getInt(PreSaleHdDef.Info.STATUS, 0));
            rt = setRecord(aid, sacct, attrType, statusName + "转入至无效库", info.getCalendar(PreSaleHdDef.Info.RECEIVE_TIME), info);
            if (rt != Errno.OK) {
                return getResult(rt, false, Message.TRANS_SUCCESS_LOG_ERROR).toJson();
            }
        } finally {
            if (ossBsDao != null) {
                ossBsDao.close();
            }
        }

        return getResult(Errno.OK, true, Message.TRANS_SUCCESS).setParam(Key.DATA, info).toJson();
    }

    // 查询无效库数据
    private String getInvalidDataList(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception {

        int aid = Parser.parseInt(request.getParameter(PreSaleHdDef.Info.AID), 0);
        int ta = Parser.parseInt(request.getParameter(PreSaleHdDef.Info.TA), -1);
        int invalidType = Parser.parseInt(request.getParameter(PreSaleHdDef.Info.INVALID_TYPE), 0);
        String salesAcct = Parser.parseString(request.getParameter(PreSaleHdDef.Info.SALES_ACCT), "");
        int isPay = Parser.parseInt(request.getParameter(Key.IS_PAY), 0);
        boolean payTimeSelect = Parser.parseBoolean(request.getParameter(Key.PAY_TIME_SELECT), false);
        boolean createTimeSelect = Parser.parseBoolean(request.getParameter(Key.CREATE_TIME_SELECT), false);
        boolean loginTimeSelect = Parser.parseBoolean(request.getParameter(Key.LOGIN_TIME_SELECT), false);
        boolean isExport = Parser.parseBoolean(request.getParameter(Key.IS_EXPORT), false);
        int currentPage = Parser.parseInt(request.getParameter(Key.CURRENT_PAGE), 1);

        String payTimeBegStr = Parser.parseString(request.getParameter(Key.PAY_TIME_BEG), "");
        String payTimeEndStr = Parser.parseString(request.getParameter(Key.PAY_TIME_END), "");
        String createTimeBegStr = Parser.parseString(request.getParameter(Key.CREATE_TIME_BEG), "");
        String createTimeEndStr = Parser.parseString(request.getParameter(Key.CREATE_TIME_END), "");
        String loginTimeBegStr = Parser.parseString(request.getParameter(Key.LOGIN_TIME_BEG), "");
        String loginTimeEndStr = Parser.parseString(request.getParameter(Key.LOGIN_TIME_END), "");
        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        faiTradeStationBaseApi.changeAid(aid);


        SysPreSaleHd sysPreSaleHd = (SysPreSaleHd) Core.getSysKit(Kid.SYS_PRESALE_HD);                // presaleHd 的接口
        SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口

//        String salesAcct = WebOss.getSacct();                                                    // 当前登录人
        int sid = Session.getSid();                                                        // 当前登录人的sid
        boolean saleManage = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE, true);        // 销售管理>销售组长>销售
        boolean adm = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL, true);                // adm
        if (sid == 1174 || sid == 1320 || sid == 863 || sid==1355 || sid == 1106 || sid == 1535) {
            saleManage = true;
        }
        boolean authHdSaleLeader1 = Auth.checkFaiscoAuth("authHdSaleLeader", false) && Auth.checkFaiscoAuth("authHDSale1", false);        // 销售组长1
        boolean authHdSaleLeader2 = Auth.checkFaiscoAuth("authHdSaleLeader", false) && Auth.checkFaiscoAuth("authHDSale2", false);        // 销售组长2
        boolean authHdSaleLeader3 = Auth.checkFaiscoAuth("authHdSaleLeader", false) && Auth.checkFaiscoAuth("authHDSale3", false);        // 销售组长3
        boolean authHdSaleLeader4 = Auth.checkFaiscoAuth("authHdSaleLeader", false) && Auth.checkFaiscoAuth("authHDSale4", false);        // 销售组长4
        boolean authHdSaleLeader5 = Auth.checkFaiscoAuth("authHdSaleLeader", false) && Auth.checkFaiscoAuth("authHDSale5", false);

        // 获取销售信息
        SearchArg saleSearchArg = new SearchArg();
        saleSearchArg.matcher = new ParamMatcher(HdSaleDef.Info.SID, ParamMatcher.GT, 0);
        FaiList<Param> saleList = new FaiList<Param>();
        int rt = sysPreSaleHd.getSalesList(new FaiList<String>(), saleSearchArg, saleList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr("get saleList err mat=%s", saleSearchArg.matcher);
        }
        Param saleParam = new Param(true);
        for (Param item : saleList) {
            saleParam.setString(item.getString(HdSaleDef.Info.ACCT), item.getString(HdSaleDef.Info.NICK_NAME));
        }

        // 获取无效库
        FaiList<String> fieldList_sale = new FaiList<String>();
        fieldList_sale.add(PreSaleHdDef.Info.AID);
        fieldList_sale.add(PreSaleHdDef.Info.SALES_ACCT);
        fieldList_sale.add(PreSaleHdDef.Info.TYPE);
        fieldList_sale.add(PreSaleHdDef.Info.STATUS);
        fieldList_sale.add(PreSaleHdDef.Info.INTENT);
        fieldList_sale.add(PreSaleHdDef.Info.CREATE_TIME);
        fieldList_sale.add(PreSaleHdDef.Info.RECEIVE_TIME);
        fieldList_sale.add(PreSaleHdDef.Info.UPDATE_TIME);
        fieldList_sale.add(PreSaleHdDef.Info.DEL);
        fieldList_sale.add(PreSaleHdDef.Info.DIS_BUY_REASON);
        fieldList_sale.add(PreSaleHdDef.Info.MARK);
        fieldList_sale.add(PreSaleHdDef.Info.TAG);
        fieldList_sale.add(PreSaleHdDef.Info.OPT_TIME);
        fieldList_sale.add(PreSaleHdDef.Info.FLAG);
        fieldList_sale.add(PreSaleHdDef.Info.BUSINESS);
        fieldList_sale.add(PreSaleHdDef.Info.LOGIN_HD_TIME);
        fieldList_sale.add(PreSaleHdDef.Info.INVALID_TYPE);
        fieldList_sale.add(PreSaleHdDef.Info.INVALID_MARK);

        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher();
        searchArg.totalSize = new Ref<Integer>();
        searchArg.cmpor = new ParamComparator(PreSaleHdDef.Info.CREATE_TIME, true);
        int limit = 20;
        int start = (currentPage - 1) * limit;
        searchArg.limit = limit;
        searchArg.start = start;
        if (isExport) {
            searchArg.start = 0;
            searchArg.limit = 1000;
        }

        // 参数拼接
        if (aid != 0) {
            searchArg.matcher.and(PreSaleHdDef.Info.AID, ParamMatcher.EQ, aid);
        }
        if (ta != -1) {
            FaiList<Param> taList = getTaList();
            FaiList<Integer> trackList = PreSaleHdDef.getTaList(taList,ta);
            if(ta != PreSaleHdDef.PreSaleTa.NOT_IN){
                searchArg.matcher.and("ta", ParamMatcher.IN, trackList);
            }else{
                //未知，剔除移动放量的ta
                trackList.add(491);
                trackList.add(493);
                trackList.add(2344);
                searchArg.matcher.and("ta", ParamMatcher.NOT_IN, trackList);
            }
        }
        if (invalidType != 0) {
            searchArg.matcher.and(PreSaleHdDef.Info.INVALID_TYPE, ParamMatcher.EQ, invalidType);
        }
        if (!salesAcct.isEmpty()) {
            if (salesAcct.equals("system")) {
                searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, "system");
            } else {
                searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, salesAcct);
            }
        }

        if (createTimeSelect) {
            if (createTimeBegStr.isEmpty() || createTimeEndStr.isEmpty()) {
                return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TIME).toJson();
            }
            Calendar createTimeBeg = Parser.parseCalendar(createTimeBegStr + Value.TIME_START, Value.DATE_FORMAT_ALL);
            Calendar createTimeEnd = Parser.parseCalendar(createTimeEndStr + Value.TIME_END, Value.DATE_FORMAT_ALL);
            if (createTimeBeg == null || createTimeEnd == null) {
                return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TIME).toJson();
            }
            searchArg.matcher.and(PreSaleHdDef.Info.CREATE_TIME, ParamMatcher.GE, createTimeBeg);
            searchArg.matcher.and(PreSaleHdDef.Info.CREATE_TIME, ParamMatcher.LE, createTimeEnd);
        }
        if (loginTimeSelect) {
            if (loginTimeBegStr.isEmpty() || loginTimeEndStr.isEmpty()) {
                return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TIME).toJson();
            }
            Calendar loginTimeBeg = Parser.parseCalendar(loginTimeBegStr + Value.TIME_START, Value.DATE_FORMAT_ALL);
            Calendar loginTimeEnd = Parser.parseCalendar(loginTimeEndStr + Value.TIME_END, Value.DATE_FORMAT_ALL);
            if (loginTimeBeg == null || loginTimeEnd == null) {
                return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TIME).toJson();
            }
            searchArg.matcher.and(PreSaleHdDef.Info.LOGIN_TIME, ParamMatcher.GE, loginTimeBeg);
            searchArg.matcher.and(PreSaleHdDef.Info.LOGIN_TIME, ParamMatcher.LE, loginTimeEnd);
        }

        boolean groupLearder = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER, true);
        // 如果是销售只能查询自己的库
        if (!saleManage && !groupLearder && !adm) {
            searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.EQ, WebOss.getSacct());
        }
        // 如果是组长只能查询自己组的
        if (groupLearder) {
            FaiList<Integer> authSidList = new FaiList<Integer>();
            if (authHdSaleLeader1) {
                authSidList = WebOss.getAuthSidList("authHDSale1");
            } else if (authHdSaleLeader2) {
                authSidList = WebOss.getAuthSidList("authHDSale2");
            } else if (authHdSaleLeader3) {
                authSidList = WebOss.getAuthSidList("authHDSale3");
            } else if (authHdSaleLeader4) {
                authSidList = WebOss.getAuthSidList("authHDSale4");
            } else if (authHdSaleLeader5) {
                authSidList = WebOss.getAuthSidList("authHDSale5");
            }
            FaiList<String> tmpSacctList = new FaiList<String>();
            for (int tmpSid : authSidList) {
                String t_sacct = Acct.getSacct(Web.getFaiCid(), tmpSid);
                tmpSacctList.add(t_sacct);
            }
            searchArg.matcher.and(PreSaleHdDef.Info.SALES_ACCT, ParamMatcher.IN, tmpSacctList);
        }

        searchArg.matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.INVALID_LIB);
        FaiList<Param> list = sysPreSaleHd.getList(fieldList_sale, searchArg);

        // 获取BSS数据
        FaiList<Integer> aidList = new FaiList<Integer>();
        for (Param p : list) {
            aidList.add(p.getInt(PreSaleHdDef.Info.AID, 0));
        }
        // 查询acctStatus表信息
        FaiList<Param> acctList = new FaiList<Param>();
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(BssStatDef.Info.AID);
        fieldList.add(BssStatDef.Info.ACCT);
        fieldList.add(BssStatDef.Info.NAME);
        fieldList.add(BssStatDef.Info.REG_PROVINCE);
        fieldList.add(BssStatDef.Info.REG_CITY);
        fieldList.add(BssStatDef.Info.REG_TIME);
        fieldList.add(BssStatDef.Info.COMPANYGOAL);
        fieldList.add(BssStatDef.Info.JZ_VERSION);
        fieldList.add(BssStatDef.Info.HD_VERSION);
        fieldList.add(BssStatDef.Info.FLYER_VERSION);
        fieldList.add(BssStatDef.Info.PAYTA);
        fieldList.add(BssStatDef.Info.PERSON);
        fieldList.add(BssStatDef.Info.REG_EMAIL);
        fieldList.add(BssStatDef.Info.REG_MOBILE);
        fieldList.add(BssStatDef.Info.TA);
        fieldList.add(BssStatDef.Info.TP);
        //fieldList.add(BssStatDef.Info.LOGIN_TIME);

        Param acctParam = new Param();
        SearchArg searchArgBss = new SearchArg();
        searchArgBss.matcher = new ParamMatcher(BssStatDef.Info.AID, ParamMatcher.IN, aidList);
        Log.logStd("select acctStatus aidList size = %s",aidList.size());
        if (aidList.size() > 0) {
            //acctList = sysBssStat.getAllAcctInfoList(fieldList, searchArgBss);

            FaiList<Object> outerExpr = new FaiList<Object>();
            outerExpr.add(IN.contains("aid", aidList));
            FdpDataParamMatcher matcher = AND.expr(outerExpr);

            ResultSet execute = FdpDataSDK.newOLTPQuery()
                    .select("aid", "reg_aacct", "reg_name", "reg_province", "reg_city", "reg_time", "reg_company_goal", "version_jz", "version_hd",
                            "version_wcd", "reg_person", "reg_email", "reg_mobile", "reg_ta", "reg_from_spider_keyword")
                    .from("fdpData", "dws_fkw_acct_info")
                    .where(matcher)
                    .execute(Core.getFlow());
            acctList = execute.getData();



            for (Param item : acctList) {
                acctParam.setParam("" + item.getInt("aid"), item);
            }
        }
        
		//获取助手版本号
        OssVip oSysVip = new OssVip();
        SearchArg vipSearchArg = new SearchArg();
        vipSearchArg.matcher = new ParamMatcher(VipDef.Info.AID, ParamMatcher.IN, aidList);
        FaiList<Integer> pay_productId = new FaiList<Integer>();
        pay_productId.add(FaiProductDef.Id.PUBLIC_ASSIST_QJ);
        pay_productId.add(FaiProductDef.Id.PUBLIC_ASSIST_PRO);
        pay_productId.add(FaiProductDef.Id.PUBLIC_ASSIST_JC);
        vipSearchArg.matcher.and(VipDef.Info.PAY_PRODUCT_ID, ParamMatcher.IN, pay_productId);
        FaiList<Param> wxastVipList = WebHdOss.searchVip(vipSearchArg);
        aidList.clear();
        if(wxastVipList.size() != 0){
        	for(Param p: wxastVipList){
        		acctParam.setParam(p.getInt(VipDef.Info.AID,0)+"-mp" , p);
        	}
        }
      	
        // 查vip信息
        FaiList<Integer> vipTypeList = new FaiList<Integer>();
        FaiList<Integer> aidPayList = new FaiList<Integer>();
        FaiList<Integer> aidPayList_forSale = new FaiList<Integer>();
        FaiList<Integer> productIdList = new FaiList<Integer>();

        vipTypeList.add(VipDef.Type.HD_VIP);
        vipTypeList.add(VipDef.Type.FLYER_VIP);
        vipTypeList.add(VipDef.Type.PUBLIC_ASSISTANT_VIP);
        Param vipParam = new Param();
        SearchArg searchArgVip = new SearchArg();
        searchArgVip.matcher = new ParamMatcher(VipDef.Info.AID, ParamMatcher.IN, aidList);
        searchArgVip.matcher.and(VipDef.Info.TYPE, ParamMatcher.IN, vipTypeList);
        if (payTimeSelect) {
            if (payTimeBegStr.isEmpty() || payTimeEndStr.isEmpty()) {
                return getResult(Errno.ARGS_ERROR, false, Message.ARG_ERR_TIME).toJson();
            }
            Calendar payTimeBeg = Parser.parseCalendar(payTimeBegStr + Value.TIME_START, Value.DATE_FORMAT_ALL);
            Calendar payTimeEnd = Parser.parseCalendar(payTimeEndStr + Value.TIME_END, Value.DATE_FORMAT_ALL);
            searchArgVip.matcher.and(VipDef.Info.START_TIME, ParamMatcher.GE, payTimeBeg);
            searchArgVip.matcher.and(VipDef.Info.START_TIME, ParamMatcher.LE, payTimeEnd);
            FaiList<Param> vipList = oSysVip.searchVip(searchArgVip);
            for (Param vipInfo : vipList) {
                aidPayList_forSale.add(vipInfo.getInt(VipDef.Info.AID));
            }
        }
        if (isPay != 0) {
            FaiList<Param> vipList = oSysVip.searchVip(searchArgVip);
            if (isPay == 1) {// 有支付
                for (Param vipInfo : vipList) {
                    aidPayList_forSale.add(vipInfo.getInt(VipDef.Info.AID));
                }
            } else { // 无支付
                aidPayList_forSale = aidList;
                for (Param vipInfo : vipList) {
                    aidPayList_forSale.remove(vipInfo.getInt(VipDef.Info.AID));
                }
            }
        }

        // 如果选择了支付时间，则需要重新查一次无效库
        if (payTimeSelect || isPay != 0) {
            searchArg.matcher.and(PreSaleHdDef.Info.AID, ParamMatcher.IN, aidPayList_forSale);
            list = sysPreSaleHd.getList(fieldList_sale, searchArg);
            aidList.clear();
            for (Param p : list) {
                aidList.add(p.getInt(PreSaleHdDef.Info.AID, 0));
            }
        }

        if (aidList.size() > 0) {
            FaiList<Param> vipList = oSysVip.searchVip(searchArgVip);
            for (int aidVip : aidList) {
                FaiList<Param> listVipTmp = Misc.getList(vipList, VipDef.Info.AID, aidVip);
                if (listVipTmp != null && !listVipTmp.isEmpty()) {
                    Param vipInfo = listVipTmp.get(0);
                    vipParam.setParam(aidVip + "", vipInfo); // 默认取第一个信息
                    productIdList.add(vipInfo.getInt(VipDef.Info.PAY_PRODUCT_ID, 0));
                    aidPayList.add(aidVip);
                }
            }
        }

        // 查订单信息
        Param orderParam = new Param();
        SearchArg searchArgOrder = new SearchArg();
        searchArgOrder.matcher = new ParamMatcher();
        searchArgOrder.matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.IN, aidPayList);
        searchArgOrder.matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.IN, productIdList);
        searchArgOrder.matcher.and(BssStatDef.OrderItemInfo.DEL, ParamMatcher.EQ, false);
        searchArgOrder.cmpor = new ParamComparator(BssStatDef.OrderItemInfo.CREATE_TIME, true);
        if (aidList.size() > 0) {
            FaiList<Param> orderItemList = sysBssStat.getOrderItemList(searchArgOrder);
            for (int aidOrder : aidList) {
                String _aid = String.valueOf(aidOrder);
                int productId = vipParam.getParam(_aid, new Param()).getInt(VipDef.Info.PAY_PRODUCT_ID, 0);
                ParamMatcher matcher = new ParamMatcher();
                matcher.and(BssStatDef.OrderItemInfo.AID, ParamMatcher.EQ, aidOrder);
                matcher.and(BssStatDef.OrderItemInfo.PRODUCT_ID, ParamMatcher.EQ, productId);
                FaiList<Param> itemList = Misc.getList(orderItemList, matcher);
                if (itemList != null && !itemList.isEmpty()) {
                    Param orderItem = itemList.get(0);
                    orderParam.setParam(_aid, orderItem); // 默认取第一个信息
                }
            }
        }


        String domain = WebHdOss.getOssDomainUrl();
        FaiList<Param> taList = getTaList();
        for (Param item : list) {
            int aidTemp = item.getInt(PreSaleHdDef.Info.AID);
            faiTradeStationBaseApi.changeAid(aidTemp);
            Param acctInfo = acctParam.getParam(aidTemp + "", new Param());
            Param acctMpInfo = acctParam.getParam(aidTemp + "-mp", new Param());
            //item.setString(BssStatDef.Info.LOGIN_TIME, Parser.parseDateString(acctInfo.getInt(BssStatDef.Info.LOGIN_TIME, 0), "yyyy-MM-dd HH:mm:ss"));//企业账号聚合到preSaleList
            item.assign(acctInfo, BssStatDef.Info.ACCT);//注册手机聚合到preSaleList
            item.assign(acctInfo, BssStatDef.Info.REG_MOBILE);
            int taItem = acctInfo.getInt(BssStatDef.Info.TA, 0);
            item.setInt(BssStatDef.Info.TA+"Int", taItem);//注册来源
            item.setString(BssStatDef.Info.TA, PreSaleHdDef.getTaName(taList, taItem));//注册来源
            item.setString(BssStatDef.Info.NAME, acctInfo.getString(BssStatDef.Info.NAME)); // 企业名称

            item.setString(BssStatDef.Info.HD_VERSION, faiTradeStationBaseApi.getName(acctInfo.getInt(BssStatDef.Info.HD_VERSION, 0)));
            item.setString(BssStatDef.Info.FLYER_VERSION, faiTradeStationBaseApi.getName(acctInfo.getInt(BssStatDef.Info.FLYER_VERSION, 0)));
            item.setString(Key.MP_VERSION, faiTradeStationBaseApi.getName(acctMpInfo.getInt(VipDef.Info.PAY_PRODUCT_ID , 0)));
            Log.logStd("mp version =="+acctMpInfo.toJson());
            item.setParam(Key.ACCT_INFO, acctInfo);

            item.setString(PreSaleHdDef.Info.LOGIN_TIME, Parser.parseSecondString(item.getCalendar(PreSaleHdDef.Info.LOGIN_TIME)));// 最后登录时间
            item.setString(PreSaleHdDef.Info.CREATE_TIME, Parser.parseSecondString(item.getCalendar(PreSaleHdDef.Info.CREATE_TIME)));// 入库时间
            item.setString(PreSaleHdDef.Info.SALES_ACCT, saleParam.getString(item.getString(PreSaleHdDef.Info.SALES_ACCT, "")));// 入库时间
            item.setString(PreSaleHdDef.Info.INVALID_TYPE, PreSaleHdDef.Info.InvalidType.getName(item.getInt(PreSaleHdDef.Info.INVALID_TYPE, 0)));// 入库时间
            Param vipInfo = vipParam.getParam(aidTemp + "", new Param());
            Param orderInfo = orderParam.getParam(aidTemp + "", new Param());
            item.setParam(Key.VIP_INFO, vipInfo);
            item.setParam(Key.ORDER_INFO, orderInfo);
            item.setString(BssStatDef.OrderItemInfo.PAY_TIME, Parser.parseSecondString(vipInfo.getCalendar(VipDef.Info.START_TIME)));
            item.setDouble(BssStatDef.OrderItemInfo.PRICE, orderInfo.getDouble(BssStatDef.OrderItemInfo.PRICE));

            item.setString("aidUrl", domain + "/index.jsp?t=hdSale&u=/cs/corp.jsp?aid=" + aidTemp);

        }

        if (isExport) {
            try {
                exportData(list, response, out);
            } catch (Exception e) {
                return getResult(Errno.ERROR, false, "导出失败" + e).toJson();
            }
            return getResult(Errno.OK, true, "导出成功").toJson();
        }

        Param none = new Param();
        none.setInt(HdSaleDef.Info.SID, 0);
        none.setString(HdSaleDef.Info.ACCT, "system");
        none.setString(HdSaleDef.Info.NICK_NAME, "自然转入");
        saleList.add(0, none);

        Param data = getResult(Errno.OK, true, Message.GET_LIST_SUCCESS);
        data.setList(Key.LIST, list);
        data.setList(Key.SALES_LIST, saleList);
        data.setObject("search", searchArg.matcher);
        data.setInt(Key.TOTAL, searchArg.totalSize.value);
        return data.toJson();
    }

    // 导出数据
    private void exportData (FaiList<Param> list, HttpServletResponse response, JspWriter out) throws Exception {
        response.setContentType("application/x-excel");
        out.clear();
        response.setHeader("Content-Disposition", "attachment;filename=" + new String("无效库列表.xls".getBytes("GBK"), "ISO-8859-1"));
        ServletOutputStream outputStream = response.getOutputStream();

        // 创建导出的表头跟需要导出的数据KEY值
        Param cellKey = new Param();
        cellKey.setString("aid", "aid");
        cellKey.setString("salesAcct", "转入销售");
        cellKey.setString("invalidType", "入库类型");
        cellKey.setString("ta", "注册来源");
        cellKey.setString("createTime", "入库时间");
        cellKey.setString("loginTime", "最后登录时间");
        cellKey.setString("payTime", "付款时间");
        cellKey.setString("price", "付款金额");
        cellKey.setString("invalidMark", "备注");
        cellKey.setString("hdVersion", "互动版本");
        cellKey.setString("flyerVersion", "微传单版本");
        cellKey.setString("mpVersion", "公众号助手版本");
        OssPoi.exportExcel(cellKey, list, outputStream);
    }

    // 管理员从无效库批量释放
    private String releaseInvalid(HttpServletRequest request) throws Exception {
        String aidListStr = Parser.parseString(request.getParameter(Key.AID_LIST), "");
        if (aidListStr.isEmpty()) {
            return getResult(Errno.ARGS_ERROR, false, "请选择客户").toJson();
        }
        FaiList<Integer> aidList = FaiList.parseIntList(aidListStr);
        if (aidList == null || aidList.isEmpty()) {
            return getResult(Errno.ARGS_ERROR, false, "请选择需要转出的客户").toJson();
        }

        boolean adm = Auth.checkFaiscoAuth("authAdm", false);
        boolean authHDSaleManage = Auth.checkFaiscoAuth("authHDSaleManage", false);

        if ( !adm && !authHDSaleManage) {
            return getResult(Errno.WEB_NO_AUTH, false, Message.NO_AUTH).toJson();
        }

        int delRt = Errno.ERROR;
        Dao ossDao = WebOss.getOssBsDao();
        Dao.SelectArg sltArg = new Dao.SelectArg();
        FaiList<Param> infoList;
        try{
            ossDao.setAutoCommit(false);

            ParamMatcher matcher = new ParamMatcher(PreSaleHdDef.Info.AID,ParamMatcher.IN, aidList);
            matcher.and(PreSaleHdDef.Info.STATUS, ParamMatcher.EQ, PreSaleHdDef.Status.INVALID_LIB);
            sltArg.searchArg.matcher = matcher;
            sltArg.table="acctPreSaleHd";
            // 先执行query，如果失败，则不执行删除
            infoList = ossDao.select(sltArg);
            if (infoList == null || infoList.isEmpty()) {
                return getResult(Errno.ERROR, false, Message.ARG_ERR).toJson();
            }
            delRt = ossDao.delete("acctPreSaleHd", matcher);

            if (delRt != Errno.OK) {
                return getResult(Errno.ERROR, false, Message.RELEASE_ERROR).toJson();
            }
        }finally{
            if(delRt != Errno.OK){
                ossDao.rollback();
            }else{
                ossDao.commit();
            }
            ossDao.close();
        }

        for (Param p : infoList) {
            int aidLog = p.getInt(PreSaleHdDef.Info.AID,0);
            int attrType = HdSaleRecordDef.AttrType.DROP_INVALID;
            String action = "释放出无效库";
            setRecord(aidLog, WebOss.getSacct(), attrType, action, p.getCalendar(HdSaleRecordDef.Info.RECEIVE_TIME), p);
            Log.logStd("batchRelease aid=%d;sid=%d", p.getInt(PreSaleHdDef.Info.AID,0), Session.getSid());
        }

        return getResult(Errno.OK, true, Message.TRANS_OUT_SUCCESS).toJson();
    }

    /**
     * 直销售前-记录动作
     */
    private int setRecord(int aid, String salesAcct, int attrType, String action, Calendar receiveTime, Param preSaleInfo) throws Exception {
        int rt = Errno.ERROR;

        Param recordInfo = new Param();
        recordInfo.setInt(HdSaleRecordDef.Info.AID, aid);
        recordInfo.setString(HdSaleRecordDef.Info.SACCT, salesAcct);
        recordInfo.setInt(HdSaleRecordDef.Info.ATTR_TYPE, attrType);
        recordInfo.setCalendar(HdSaleRecordDef.Info.CREATE_TIME, Calendar.getInstance());
        recordInfo.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, Calendar.getInstance());
        recordInfo.setString(HdSaleRecordDef.Info.ACTION, action);

        Log.logStd("preSaleInfo=%s", preSaleInfo);
        recordInfo.setInt(HdSaleRecordDef.Info.TAG, preSaleInfo.getInt(PreSaleHdDef.Info.TAG, 0));

        Dao bssDao = WebOss.getBssMainDaoMaster();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctStatus";
            sltArg.searchArg.matcher = new ParamMatcher(PreSaleHdDef.Info.AID, ParamMatcher.EQ, aid);
            Param bssData = bssDao.selectFirst(sltArg);
            FaiList<Param> taList = bssDao.executeQuery("select * from ta");
            Param taParam = new Param(true);
            for (Param item : taList) {
                taParam.setInt(item.getInt("ta", 0) + "", item.getInt("groupId", 0));
            }
            int ta = bssData.getInt("ta", 0);
            int groupId = taParam.getInt(ta + "", 0);
            recordInfo.setString(HdSaleRecordDef.Info.TA_GROUP_NAME, PreSaleHdDef.getTaNameByTaAndGroupId(groupId, ta));
            recordInfo.setInt(HdSaleRecordDef.Info.TA, ta);
            Log.logStd("recordInfo=%s", recordInfo);
        } finally {
            bssDao.close();
        }
        if (receiveTime != null) {
            recordInfo.setCalendar(HdSaleRecordDef.Info.RECEIVE_TIME, receiveTime);
        }

        Dao ossDao = null;
        try {
            ossDao = WebOss.getOssBsDao();
            rt = ossDao.insert("hdSaleRecord", recordInfo);
        } finally {
            if(ossDao != null){
                ossDao.close();
            }
        }

        if (rt != Errno.OK) {
            Log.logErr(rt, "error:Oss-PreSale addActionLog set err;info=%s;", recordInfo.toJson());
        }

        return rt;
    }

    private static FaiList<Param> getTaList() throws Exception {
        FaiList<Param> taList = (FaiList<Param>) Core.getTmpData("_taList");
        if (taList == null) {
            SysBssStat sysBssStat = (SysBssStat) Core.getSysKit(Kid.SYS_BSS_STAT);    // bss 的接口
            SearchArg searchArg = new SearchArg();
            taList = sysBssStat.getTaList(searchArg);
            Core.setTmpData("_taList", taList);
        }
        return taList;
    }
%>


<%
    String output = "";
    try {
        String cmd = Parser.parseString(request.getParameter(Key.CMD), "");
        if (Cmd.TRANS_TO_INVALID.equals(cmd)) {
            output = transToInvalid(request);
        }
        else if (Cmd.GET_INVALID_DATA_LIST.equals(cmd)) {
            output = getInvalidDataList(request, response, out);
        }
        else if (Cmd.RELEASE_INVALID.equals(cmd)) {
            output = releaseInvalid(request);
        }
        else {
            output = getResult(Errno.ARGS_ERROR, false, Message.NO_METHOD).toJson();
        }


    } catch (Exception e) {
        output = getResult(Errno.ERROR, false, Message.SYS_ERROR + e).toJson();
    }

    out.print(output);

%>
