﻿<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.web.Auth"%>
<script src="<%=HdOssResDef.getResPath("js_vue")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_vuex")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_vue_resource")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_vue_router")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_tailwindcss")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_element")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_hdoss")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_router")%>" type="text/javascript" charset="utf-8"></script>
<script src="<%=HdOssResDef.getResPath("js_store")%>" type="text/javascript" charset="utf-8"></script>
<%
SysSession oSession = (SysSession)Core.getSysKit(Kid.SYS_SESSION);
String sessionId = oSession.getId();
int sid = Session.getSid();
Staff oStaff = (Staff) Core.getCorpKit(Web.getFaiCid(), Kid.STAFF);

if (Web.getFaiCid() <= 0 ||  sid <= 0 ) {
    return;
}
Param staffInfo = oStaff.getStaffInfo(sid);
boolean authAdm = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL);
Log.logDbg("yansen authAdm=%s",authAdm);
//boolean authHdApprove = Auth.checkFaiscoAuth("authHdApprove", false);
boolean hdSaleGroupLeader = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_GROUP_LEADER);
boolean hdSale = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE);
boolean hdSaleManage = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE_MANAGE);
boolean authYkSaleLeader = Auth.checkFaiscoAuth("authYkSaleLeader", false);
boolean autYKSale1=Auth.checkFaiscoAuth("authYKSale1", false);
Boolean isDev = Web.isDev();
String token ="";
if (sessionId != null && !sessionId.isEmpty() && oSession.checkValid(Core.getWid())) {
	token = Encryptor.md5(sessionId+Request.TOKEN_TAIL);
}
HdOss  hdOss = (HdOss)Core.getCorpKit(1,Kid.HD_OSS);
Param staff = hdOss.getStaff(sid);
if(staff == null || staff.isEmpty()){
    staff = new Param();
    staff.setInt(HdOssStaffDef.Info.SID,sid);
    int auth = 0;
	auth = Misc.setFlag(auth, HdOssStaffDef.Auth.HD_OSS_LOGIN, true); 
    staff.setInt(HdOssStaffDef.Info.AUTH,auth);
    staff.setString(HdOssStaffDef.Info.NAME,staffInfo.getString("name",""));
    staff.setString(HdOssStaffDef.Info.SACCT,staffInfo.getString("sacct",""));
    int rt = hdOss.addStaff(staff);
    if(rt != Errno.OK){
        Log.logErr("yansen add staff err");
    }
}
Log.logDbg("yansen jj=%s",staff);
%>
<script type="text/javascript">
var auth = {
    authAdm:<%=authAdm%>,
    hdSaleGroupLeader:<%=hdSaleGroupLeader%>,
    hdSaleManage:<%=hdSaleManage%>,
    hdSale:<%=hdSale%>,
	authYkSaleLeader:<%=authYkSaleLeader%>,
	autYKSale1:<%=autYKSale1%>,
    staff:<%=staffInfo.toJson()%>,
    isDev:<%=isDev%>,
};
console.info(auth);
Vue.prototype.auth = auth;

document.domain = "<%=Web.getPortalDomain()%>";

Fai.hdoss = {
    url:{
        // 各个oss业务的域名
        getHdOssDomain: "<%=WebHdOss.getDomainUrl()%>",
        // 直销的域名
        getPortalHost: "<%=Web.getPortalHost()%>",
        getOssDomain: "<%=Web.getOssDomain()%>",
        getHdPortalDomain: "<%=Web.getHdPortalDomain()%>",
        getFlyerManageDomain: "<%=Web.getFlyerManageDomain()%>",
        getWXAppDomain: "<%=Web.getWXAppDomain()%>",
        getKtuManageDomain: "<%=Web.getKtuManageDomain()%>",
        getAllSiteDomain: "<%=Web.getAllSiteDomain()%>",
		getWxastDomain: "<%=Web.getWxAstDomain()%>",
        getSiteAdmHost: function(aacct, aid) {
            var param = [];
            param.webCmd = "getSiteAdmHost";
            param.aacct = aacct;
            param.aid = aid;
            return getWebHost(param);
        },
        getMobiAdmHost: function(aacct, siteId) {
            var param = [];
            param.webCmd = "getMobiAdmHost";
            param.aacct = aacct;
            param.siteId = siteId;
            return getWebHost(param);
        },
		
		//20190712增加获取悦客域名
		getYkDomain: "<%=Web.getYkDomain()%>",
		getWXAppDomain: "<%=Web.getWXAppDomain()%>"
		
    }
}
Vue.mixin({
    data: function() {
        return {
            HdDef: Fai.hdoss,
        }
    },
})



</script>