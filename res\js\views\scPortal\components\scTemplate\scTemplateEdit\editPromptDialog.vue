<template>
  <el-dialog
    title="编辑提示词"
    custom-class="editPromptDialog"
    width="540px"
    :visible.sync="dialogVisible"
    :before-close="handleClose"
    :close-on-click-modal="false">
    <!-- <span>这是一段信息</span> -->
    <div class="p-[20px]">
      <div class="mb-[20px]">
        <p class="mb-[10px]">apikey：<span class="text-[#999]">（默认使用当前apikey生成提示词，如需更换，请输入新的apikey）</span></p>
        <el-input v-model="apikey" placeholder="请输入apikey" class="!w-full" />
      </div>
      <div class="w-full">
        <div class="flex justify-between items-center mb-[10px]">
          <p>提示词：</p>
          <el-popover
            placement="bottom"
            trigger="click"
            v-model="showVarList">
            <el-menu>
              <el-menu-item 
                class="!h-[25px] !leading-[25px]"
                v-for="field in inputFormList" 
                :key="field.variable"
                @click="insertVariable(field.variable)">
                {{ field.label }}
                <span class="text-[12px] text-[#999]">{{ field.variable }}</span>
              </el-menu-item>
            </el-menu>
            <el-button 
              slot="reference" 
              type="primary" 
              size="small">
              插入变量
            </el-button>
          </el-popover>
        </div>
        <div class="editor-container">
          <el-input
            type="textarea"
            v-model="promptText"
            :autosize="{ minRows: 4, maxRows: 8 }"
            placeholder="请输入提示词，示例：为#店铺名称#书写三个用于描述店铺特色的关键词"
            ref="promptEditor"
          />
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditPromptDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    inputFormList: {
      type: Array,
      default: () => [],
    },
    promptInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      apikey: "",
      promptText: "",
      showVarList: false,
    };
  },
  watch: {
    promptInfo(newVal) {
      if (newVal) {
        this.apikey = newVal.apiKey
        this.promptText = newVal.prompt
      }
    },
  },
  methods: {
    handleClose() {
      this.cancel();
    },
    cancel() {
      this.$emit('update:dialogVisible', false);
    },
    confirm() {
      if (!this.apikey.trim()) {
        this.$message.error('请输入apikey');
        return;
      }
      if (!this.promptText.trim()) {
        this.$message.error('请输入提示词');
        return;
      }
      this.$emit('confirm', {
        apikey: this.apikey,
        promptText: this.promptText,
      });
      this.$emit('update:dialogVisible', false);
    },
    insertVariable(variable) {
      const editor = this.$refs.promptEditor.$refs.textarea;
      const cursorPos = editor.selectionStart;
      const textBefore = this.promptText.substring(0, cursorPos);
      const textAfter = this.promptText.substring(cursorPos);
      
      this.promptText = `${textBefore}#${variable}#${textAfter}`;
      this.showVarList = false;
      
      // 设置光标位置到插入变量后
      this.$nextTick(() => {
        const newPos = cursorPos + variable.length + 4;
        editor.setSelectionRange(newPos, newPos);
        editor.focus();
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.editor-container {
  margin-top: 8px;
}

</style>

<style lang="less">
.editPromptDialog {
  &.el-dialog {
    width: 500px;
  }
}
</style>
