// 修正IE6每次从服务器读取背景图片的BUG
try{
	if($.browser.msie && $.browser.version == '6.0') {
		document.execCommand('BackgroundImageCache', false, true);
	}
}catch(e){
}

if(typeof hdFai == 'undefined'){
	hdFai = {};
	var _top = _top || top;
	hdFai.top = _top;
}

(function(hdFai) {

	var arr_proto = Array.prototype;
	var arr_slice = arr_proto.slice;

	hdFai.isNumber = function (obj){ //数字则返回true
    	if(/[^\d]/.test(obj)){ return false; }
    	else{ return true; }
	};

	/*
     * 判断是否中文
     */
	//hdFai.isChinese = function(val){
	//	return /.*[\u4e00-\u9fa5]+.*$/.test(val);
	//}
	hdFai.isChinese = function(val){
    	if(val < '一' || val > '龥') {
    		return false;
    	}
    	
    	return true;
	};
	hdFai.isEmail = function(email){
    	var pattern = /^[a-zA-Z0-9_\&\-\.\+]+@[a-zA-Z0-9][a-zA-Z0-9_\-]*\.[a-zA-Z0-9\-][a-zA-Z0-9_\-\.]*[a-zA-Z0-9 ]$/;
    	return pattern.test(email);
	};

	//supAbsuRoot   是否支持绝对路径
	hdFai.isUrl = function(str_url, supAbsuRoot){
    	if(typeof supAbsuRoot == 'undefined') {
    		supAbsuRoot = true;
    	}
    	if(supAbsuRoot && str_url.length >= 1 && str_url.charAt(0) == '/'){
    		return true;
    	}
    	if(supAbsuRoot && str_url.length >= 1 && str_url.charAt(0) == '#'){
    		return true;
    	}

		//	var re=/^(\w+:\/\/).+/;
    	var re = /^(\w+:).+/;
    	var result = re.test(str_url);	
    	return result;
	};
	hdFai.fixUrl = function(url, supAbsuRoot){
    	if(hdFai.isUrl(url, supAbsuRoot)) {
    		return url;
    	}
    	return 'http://' + url;
	};
	hdFai.getUrlParam = function (url, name){
    	/*
    	//老版本  过度优化  小小的url没有提速的必要。且也不是根据传进来的url取parameter的
    	if (typeof hdFai._urlParams == 'undefined' || !hdFai._urlParams){
    		hdFai._urlParams  = new Object() ;
    		var paramStr = document.location.search.substr(1).split('&') ;
    		for (var i = 0; i < paramStr.length; ++i){
    			var param = paramStr[i].split('=');
    			var paramName = decodeURIComponent(param[0]);
    			var paramValue = decodeURIComponent(param[1]);
    			hdFai._urlParams[paramName] = paramValue;
    		}
    	}
    	return hdFai._urlParams[name];
    	*/
    	var paramStrings = url.substring(url.indexOf('?') + 1, url.indexOf('#')).split('&');
    	var value;
    	$.each( paramStrings, function( index, str ){
    		var tmpKey = decodeURIComponent( str.substring( 0, str.indexOf('=') ) );
    		if( tmpKey === name ){
    			value = decodeURIComponent( str.substring( str.indexOf('=') + 1, str.length ) );
    			return false;
    		}
    	});
    	return value;
	};
	/*
     * 判断是否字母
     */
	//hdFai.isLetter = function(val){
	//  return /^[a-zA-Z]+$/g.test(val);
	//}
	hdFai.isLetter = function(val){
		if((val < 'a' || val > 'z') && (val < 'A' || val > 'Z')) {
			return false;
		}
		return true;
	};
	/**
     * compareObjNew(obj1, obj2, order, key, type)
     */
	hdFai.compareObjNew = function(obj1, obj2, order, key, keyType){
    	var valA = obj1[key];
    	var valB = obj2[key];
    	
    	if(typeof valA === 'undefined' || typeof valB === 'undefined') {
    		return;
    	}
    	
    	// 如果有传type是number。这里特殊处理
    	if(keyType === 'number') {
    		return hdFai.ber(valA, valB, order);
    	}
    	
    	// 如果是IE 6\7\8，调用浏览器自带的排序，自带排序不会太卡
    	if(hdFai.isIE6() || hdFai.isIE7() || hdFai.isIE8()) {
    		return hdFai.compareObjLocale(obj1, obj2, order, key);
    	}
    	
    	return hdFai.compareStrings(valA, valB, order);
	};

	/**
     * compareStrings(s1, s2, order)
     */
	hdFai.compareStrings = function(s1, s2, order){
    	// change number to string
    	if(typeof s1 === 'number' || typeof s1 === 'boolean'){
    		s1 = s1.toString();
    	}
    	if(typeof s2 === 'number' || typeof s2 === 'boolean'){
    		s2 = s2.toString();
    	}
    	
    	var options = {
    		str1: s1,
    		str2: s2,
    		len1: s1.length,
    		len2: s2.length,
    		pos1: 0,
    		pos2: 0
    	};

    	var result = 0;
    	while(result == 0 && options.pos1 < options.len1 && options.pos2 < options.len2){
    		var ch1 = options.str1.charAt(options.pos1);
    		var ch2 = options.str2.charAt(options.pos2);

    		if(hdFai.isDigit(ch1)){
    			result = hdFai.isDigit(ch2) ? bers(options) : -1;
    		}else if(hdFai.isChinese(ch1)){
    			result = hdFai.isChinese(ch2) ? comparePinyin(options) : 1;
    		}else if(hdFai.isLetter(ch1)){
    			result = (hdFai.isLetter(ch2) || hdFai.isChinese(ch2)) ? compareOther(options, true) : 1;
    		}else{
    			result = hdFai.isDigit(ch2) ? 1 : (hdFai.isLetter(ch2) || hdFai.isChinese(ch2)) ? -1 : compareOther(options, false);
    		}

    		options.pos1++;
    		options.pos2++;
    	}
    	
    	if(order == 'asc'){
    		return result == 0 ? options.len1 - options.len2 : result;
    	}else{
    		return -(result == 0 ? options.len1 - options.len2 : result);
    	}
    	
    	// 内部方法bers
    	function bers(options){
    		var end1 = options.pos1 + 1;
    		while(end1 < options.len1 && hdFai.isDigit(options.str1.charAt(end1))){
    			end1++;
    		}
    		var fullLen1 = end1 - options.pos1;
    		while(options.pos1 < end1 && options.str1.charAt(options.pos1) == '0'){
    			options.pos1++;
    		}
    	
    		var end2 = options.pos2 + 1;
    		while(end2 < options.len2 && hdFai.isDigit(options.str2.charAt(end2))){
    			end2++;
    		}
    		var fullLen2 = end2 - options.pos2;
    		while(options.pos2 < end2 && options.str2.charAt(options.pos2) == '0'){
    			options.pos2++;
    		}
    	
    		var delta = (end1 - options.pos1) - (end2 - options.pos2);
    		if(delta != 0){
    			return delta;
    		}
    	
    		while(options.pos1 < end1 && options.pos2 < end2){
    			delta = options.str1.charCodeAt(options.pos1++) - options.str2.charCodeAt(options.pos2++);
    			if(delta != 0){
    				return delta;
    			}
    		}
    	
    		options.pos1--;
    		options.pos2--;
    	
    		return fullLen2 - fullLen1;
    	}
    	
    	// 内部方法compareOther
    	function compareOther(options, isLetters){
    		var ch1 = options.str1.charAt(options.pos1);
    		var ch2 = options.str2.charAt(options.pos2);
    	
    		if(ch1 == ch2){
    			return 0;
    		}
    	
    		if(isLetters){
    			ch1 = ch1.toUpperCase();
    			ch2 = ch2.toUpperCase();
    			if(ch1 != ch2){
    				ch1 = ch1.toLowerCase();
    				ch2 = ch2.toLowerCase();
    			}
    		}
    		
    		return ch1.charCodeAt(0) - ch2.charCodeAt(0);
    	}
    	
    	// 内部方法comparePinyin
    	function comparePinyin(options){
    		var ch1 = options.str1.charAt(options.pos1);
    		var ch2 = options.str2.charAt(options.pos2);
    		
    		if(ch1 == ch2){
    			return 0;
    		}
    		
    		var py1, py2;
    		if(typeof Pinyin != 'undefined'){
    			py1 = Pinyin.getStringStriped(ch1, Pinyin.mode.LOWERCASE, true);
    			py2 = Pinyin.getStringStriped(ch2, Pinyin.mode.LOWERCASE, true);
    			return -hdFai.compareStrings(py1, py2);
    		}else{
    			py1 = ch1.charCodeAt(0);
    			py2 = ch2.charCodeAt(0);
    			
    			if(py1 == py2){
    				return 0;
    			}else{
    				return py1 - py2;
    			}
    		}
    	}
    	
	};

	/*
    弹出框（在top显示）
    title               弹出框的标题
    width               内容区宽度（数字/字符串）
    height              内容区高度（数字/字符串）
    opacity             背景遮盖层的透明度，默认0.3
    displayBg           是否加背景遮盖层，默认true
    frameSrcUrl         内容区iframe的src地址
    frameScrolling      iframe是否有scrolling(yes/no/auto)，默认auto
    bannerDisplay       是否显示标题栏和边框，默认true
    closeBtnClass       关闭按钮样式
    framePadding        是否去掉内容区的padding
    bgClose             是否点击背景关闭，默认false
    divId               以div的html来作为内容
    divContent          以html来作为内容

    closeFunc           关闭popup window时执行的函数，可以通过Fai.closePopupWindow(popupID, closeArgs)来传递closeFunc的回调参数，即调用：closeFunc(closeArgs)
    helpLink            帮助按钮的link url
    */
	hdFai.popupWindow = function(options){
		var settings = {
			title: '',
			width: 500,
			height: 300,
			frameSrcUrl: 'about:_blank',
			frameScrolling: 'auto',
			bannerDisplay: true,
			framePadding: true,
			opacity: '0.3',
			displayBg: true,
			bgClose: false,
			closeBtnClass: ''
		};
		settings = $.extend(settings, options);

		var contentWidth = parseInt(settings.width);
		var contentHeight = parseInt(settings.height);

		var browserWidth = hdFai.top.document.documentElement.clientWidth;
		if(!$.browser.msie) {
			browserWidth = hdFai.top.document.body.clientWidth;
		}
		var browserHeight = hdFai.top.document.documentElement.clientHeight;

		var leftMar = (browserWidth - contentWidth) / 2;
		if(settings.leftMar != null) {
			leftMar = parseInt(settings.leftMar);
		}
		var topDiff = 80;
		if(!settings.bannerDisplay) {
			topDiff = 0;
		}
		var topMar = (browserHeight - contentHeight - topDiff) / 2;
		if(settings.topMar != null) {
			topMar = parseInt(settings.topMar);
		}

		var bgDisplay = '';
		var bannerStyle = '';
		var trans = '';
		if(!settings.bannerDisplay) {
			bannerStyle = 'display:none;';
			bgDisplay = 'background:none;';
			if(!settings.closeBtnClass) {
				settings.closeBtnClass = 'formX_old';   // 没有边框时使用另一个样式（如幻灯片）
			}
		}
		if(!settings.framePadding) {
			bgDisplay += 'padding:0;';
			trans = 'allowtransparency="true"';
		}
        
		var id = parseInt(Math.random() * 10000);
		var displayModeContent = '<iframe ' + trans + ' id="popupWindowIframe' + id + '" class="popupWindowIframe" src="" frameborder="0" scrolling="' + settings.frameScrolling + '" style="width:100%;height:100%;"></iframe>';
		var iframeMode = true;
		if(settings.divId != null) {
			iframeMode = false;
			displayModeContent = $(settings.divId).html();
		}
		if(settings.divContent != null) {
			iframeMode = false;
			displayModeContent = settings.divContent;
		}

		//加背景
		if(settings.displayBg) {
			hdFai.bg(id, settings.opacity);
		}

		var html = '';
		//加弹出窗口
		var scrollTop = hdFai.top.$('body').scrollTop();
		if(scrollTop == 0) {
			scrollTop = hdFai.top.$('html').scrollTop();
		}
        
		var winStyle = 'left:' + leftMar + 'px; top:' + (topMar + scrollTop) + 'px;';
		// ie6/7 popupWin的width无法自动，所以先写死再动态修正
		if(hdFai.isIE6() || hdFai.isIE7()) {
			winStyle += 'width:' + contentWidth + 'px;';
		}
		var formMSGStyle = 'position:relative;width:' + contentWidth + 'px;height:' + contentHeight + 'px;';
		var fixFlashIframe = '';
		if($.browser.msie) {
			fixFlashIframe = '<iframe id="fixFlashIframe' + id + '" style="position:absolute;z-index:-1;left:0;top:0;" frameborder="0" width="100%" height="100%" src="javascript:"></iframe>';
		}

		//fixFlashIframe防止在IE下Flash遮挡弹出层
		var html = [
			'<div id="popupWindow' + id + '" class="formDialog" style="' + winStyle + '">',
			// fixFlashIframe,
			'<div class="formTL" style=\'' + bannerStyle + '\'><div class="formTR"><div class="formTC">' + settings.title + '</div></div></div>',
			'<div class="formBL" style=\'' + bgDisplay + '\'>',
			'<div class="formBR" style=\'' + bgDisplay + '\'>',
			'<div class="formBC" id="formBC' + id + '" style="height:auto;' + bgDisplay + '">',
			'<div class="formMSG" style="' + formMSGStyle + '">',
			displayModeContent,
			'</div>',
			'<table cellpadding=\'0\' cellspacing=\'0\' class=\'formBtns\'>',
			'<tr><td align=\'center\' style=\'padding:15px 0px;\'></td></tr>',
			'</table>',
			'</div>',
			'<div id="waitingP' + id + '" class="waitingP" style="height:auto;"></div>',
			'</div>',
			'</div>',
			'<a href="javascript:;" class="formX ' + settings.closeBtnClass + '" hidefocus=\'true\' onclick=\'return false;\'></a>',
			'</div>'];

		var popupWin = hdFai.top.$(html.join('')).appendTo('body');

		// ie6/7 popupWin的width在一开始时先用了contentWidth，这里重新修正
		if(hdFai.isIE6() || hdFai.isIE7()) {
			var formBL = popupWin.find('.formBL');
			var formBLPadding = hdFai.getCssInt(formBL, 'padding-left') + hdFai.getCssInt(formBL, 'padding-right') + hdFai.getCssInt(formBL, 'border-left-width') + hdFai.getCssInt(formBL, 'border-right-width');
			var formBR = popupWin.find('.formBR');
			var formBRPadding = hdFai.getCssInt(formBR, 'padding-left') + hdFai.getCssInt(formBR, 'padding-right') + hdFai.getCssInt(formBR, 'border-left-width') + hdFai.getCssInt(formBR, 'border-right-width');
			var formBC = popupWin.find('.formBC');
			var formBCPadding = hdFai.getCssInt(formBC, 'padding-left') + hdFai.getCssInt(formBC, 'padding-right') + hdFai.getCssInt(formBC, 'border-left-width') + hdFai.getCssInt(formBC, 'border-right-width');
			popupWin.css('width', (contentWidth + formBLPadding + formBRPadding + formBCPadding) + 'px');
		}

		// fix height
		var btnsHeight = 40;
		var offHeight = 20;
		if(!settings.bannerDisplay) {
			btnsHeight = 0;
		}
		if(popupWin.height() + btnsHeight > (browserHeight - offHeight)) {
			var diffHeight = popupWin.height() + btnsHeight - popupWin.find('.formMSG').height();   // 40预留给button区
			popupWin.find('.formMSG').css('height', (browserHeight - offHeight - diffHeight) + 'px');
			popupWin.css('top', (10 + scrollTop) + 'px');
		}

		if(hdFai.isIE6()){
			$('#fixFlashIframe' + id).attr('height', popupWin.height() + 'px');
		}
		//重置加载层宽高度
		if(iframeMode) {
			hdFai.top.$('#waitingP' + id).height(hdFai.top.$('#formBC' + id).height());
			hdFai.top.$('#waitingP' + id).width(hdFai.top.$('#formBC' + id).width());
		}else{
			hdFai.top.$('#waitingP' + id).hide();
		}
        
		if(settings.divInit != null){
			settings.divInit(id);
		}
		hdFai.top.$('#popupWindow' + id).ready(function() {
			if(iframeMode) {
				var frameSrcUrlArgs = 'popupID=' + id;
				/*
                var mobiSettingState = parseInt( hdFai.getUrlParam( location.href, 'mobiSettingState' ) );
                if( !isNaN(mobiSettingState) && mobiSettingState > 0 ){
                    frameSrcUrlArgs += "&mobiSettingState=" + mobiSettingState;
                }
                */
				hdFai.top.$('#popupWindowIframe' + id).attr('src', hdFai.addUrlParams(settings.frameSrcUrl, frameSrcUrlArgs)).load(function(){
					hdFai.top.$('#waitingP' + id).hide();
				});
			}
			popupWin.draggable({
				start: function(){
					// 拖动不选中
					hdFai.top.$('body').disableSelection();
					// chrome,ie10以上 top选色板focusout失效。
					hdFai.top.$('#colorpanel').remove();
					hdFai.top.$('.faiColorPicker').remove();
				},
				handle: '.formTL',
				stop: function(){
					// 拖动不选中
					hdFai.top.$('body').enableSelection();
				}
			});
			popupWin.find('.formX').bind('click', function(){
				hdFai.closePopupWindow(id);
				return false;
			});
			popupWin.find('.formTL').disableSelection();
            
			// 如果开启了点击背景关闭
			if(settings.bgClose){
				hdFai.top.$('#popupBg' + id).bind('click', function(){
					hdFai.closePopupWindow(id);
					return false;
				});
			}
            
		});
        
		if(hdFai.isNull(hdFai.top._popupOptions)) {
			hdFai.top._popupOptions = {};
		}
		if(hdFai.isNull(hdFai.top._popupOptions['popup' + id])) {
			hdFai.top._popupOptions['popup' + id] = {};
		}
		if(!hdFai.isNull(options.callArgs)){
			hdFai.top._popupOptions['popup' + id].callArgs = options.callArgs;
		}
		hdFai.top._popupOptions['popup' + id].options = options;
		hdFai.top._popupOptions['popup' + id].change = false;
		return id;
	};

	hdFai.setPopupWindowChange = function(id, change) {
		if(hdFai.isNull(hdFai.top._popupOptions)){
			return;
		}
		if(hdFai.isNull(hdFai.top._popupOptions['popup' + id])) {
			return;
		}
		hdFai.top._popupOptions['popup' + id].change = change;
	};

	hdFai.closePopupWindow = function(id, closeArgs){
		if(id) {
			try{
				if(hdFai.isNull(hdFai.top._popupOptions['popup' + id])) {
					return;
				}
				var popupOption = hdFai.top._popupOptions['popup' + id];
				if(popupOption.change){
					if(!window.confirm('您的修改尚未保存，确定要离开吗？')){
						return;
					}
				}
				if(popupOption.refresh){
					hdFai.top.location.reload();
					return;
				}
				// remove first
				hdFai.top.hdFai.removeAllIng(false);
				var options = popupOption.options;
				if(!hdFai.isNull(options.closeFunc)) {
					if(closeArgs) {
						options.closeFunc(closeArgs);
					}else{
						options.closeFunc(hdFai.top._popupOptions['popup' + id].closeArgs);
					}
				}       
				hdFai.top._popupOptions['popup' + id] = {};
				//animate start
				var ppwObj = hdFai.top.$('#popupWindow' + id);
				if(options.animate){
					hdFai.top.hdFai.closePopupWindowAnimate(ppwObj, options.animateTarget, options.animateOnClose);
				}
				//animate finish
                
				// for ie9 bug
				hdFai.top.setTimeout('hdFai.closePopupWindow_Internal(\'' + id + '\')');
			}catch(err){
			}
		}else{
			hdFai.removeBg();
			hdFai.top.$('.formDialog').remove(); 
		}
	};

	hdFai.closePopupWindow_Internal = function(id) {
		if(typeof id == 'undefined') {
			//在关闭窗口之前，先把swfuplaod销毁了
			if($.browser.msie && $.browser.version == 10){
				var popupWindowIframe = hdFai.top.$('.formDialog').find('.popupWindowIframe')[0];
				if(popupWindowIframe){
					popupWindowIframeWindow = popupWindowIframe.contentWindow;
                    
					if(popupWindowIframeWindow){
						try{
							if( popupWindowIframeWindow.swfObj ){
								popupWindowIframeWindow.swfObj.destroy();
							}
							if( popupWindowIframeWindow.editor ){
								if(popupWindowIframeWindow.editor.swfObj){
									popupWindowIframeWindow.editor.swfObj.destroy();
								}
							}
						}catch( e ){
                            
						}
					}
				}
			}
			hdFai.top.$('.popupBg').remove();
			//hdFai.top.$("body").focus();    // 由于这里导致IE9下打开两次以前popupWindow之后。iframe里面的input无法focus进去，先注释掉
			hdFai.top.$('.formDialog').remove(); 
		}else{
			//fix ie10 下图片上传的BUG
			//在关闭窗口之前，先把swfuplaod销毁了
			if($.browser.msie && $.browser.version == 10){
				var popupWindowIframe = hdFai.top.document.getElementById('popupWindowIframe' + id);
				if(popupWindowIframe){
					popupWindowIframeWindow = popupWindowIframe.contentWindow;
                    
					if(popupWindowIframeWindow){
						try{
							if( popupWindowIframeWindow.swfObj ){
								popupWindowIframeWindow.swfObj.destroy();
							}
							if( popupWindowIframeWindow.editor ){
								if(popupWindowIframeWindow.editor.swfObj){
									popupWindowIframeWindow.editor.swfObj.destroy();
								}
							}
						}catch( e ){
                            
						}
					}
				}
                
			}

			hdFai.top.hdFai.removeBg(id);
			//hdFai.top.$("body").focus();    // 由于这里导致IE9下打开两次以前popupWindow之后。iframe里面的input无法focus进去，先注释掉
			hdFai.top.$('#popupWindowIframe' + id).remove();  // 这里尝试先remove iframe再后面remove div，看焦点会不会丢失
			hdFai.top.$('#popupWindow' + id).remove();
            
		}
	};

	hdFai.closePopupWindowAnimate = function (ppwObj, target, onclose) {
		var animateDiv = $('<div>');
		hdFai.top.$('body').append(animateDiv);
		animateDiv.css({
			border: '1px solid #ff4400',
			position: 'absolute',
			'z-index': '9999',
			top: ppwObj.offset().top,
			left: ppwObj.offset().left,
			height: ppwObj.height() + 'px',
			width: ppwObj.width() + 'px'
		});
		var guide = hdFai.top.$('body').find(target);
		animateDiv.animate({
			top: guide.offset().top + 'px',
			left: guide.offset().left + 'px',
			width: guide.width() + 'px',
			height: guide.height() + 'px'
		}, 'slow', function(){
			if(typeof onclose == 'function') {
				onclose();
			}
			animateDiv.remove();
		});
	};

	hdFai.addPopupWindowBtn = function(id, btnOptions) {
		var win = hdFai.top.$('#popupWindow' + id);
		win.find('.formBtns').show();
		var btnId = 'popup' + id + btnOptions.id;
		var btns = win.find('.formBtns td');
		var btn = btns.find('#' + btnId);
		if(btn.length > 0){
			btn.remove();
		}
		if( btnOptions.click != 'help' ){
			if(typeof btnOptions['extClass'] != 'undefined') {
				var extClass = btnOptions['extClass'];
				hdFai.top.$('<input id=\'' + btnId + '\' type=\'button\' value=\'' + btnOptions.text + '\' class=\'abutton faiButton\' extClass=\'' + extClass + '\'></input>').appendTo(btns);
			}else{
				hdFai.top.$('<input id=\'' + btnId + '\' type=\'button\' value=\'' + btnOptions.text + '\' class=\'abutton faiButton\'></input>').appendTo(btns);
			}
		}
		btn = btns.find('#' + btnId);   
		if(typeof btn.faiButton == 'function') {
			btn.faiButton();
		}
		if(btnOptions.callback && Object.prototype.toString.call(btnOptions.callback) === '[object Function]'){
			btn.click(function() {
				btnOptions.callback();
				hdFai.top.hdFai.closePopupWindow(id);
			});
		}
        
		if(btnOptions.click == 'close') {
			btn.click(function() {
				hdFai.top.hdFai.closePopupWindow(id);
			});
		}else if(btnOptions.click == 'help') {
			if( win.find('a.formH').length == 0 ){
				win.append( '<a class=\'formH\' href=\'' + btnOptions.helpLink + '\' target=\'_blank\' title=\'' + btnOptions.text + '\'></a>' );
			}
		}else{
			btn.click(btnOptions.click);
		}
        
		if(btnOptions.disable) {
			btn.attr('disabled', true);
			btn.faiButton('disable');
		}
	};

	hdFai.enablePopupWindowBtn = function(id, btnId, enable) {
		var win = hdFai.top.$('#popupWindow' + id);
		btnId = 'popup' + id + btnId;
		var btn = win.find('#' + btnId);
		if(enable){
			btn.removeAttr('disabled');
			btn.faiButton('enable');
		}else{
			btn.attr('disabled', true);
			btn.faiButton('disable');
		}
	};

	/*
    灰色透明背景
    */
	hdFai.bg = function(id, opacity) {
		var html = '';
		var opacityHtml = '';
		if(opacity){
			opacityHtml = 'filter: alpha(opacity=' + opacity * 100 + '); opacity:' + opacity + ';';
		}

		if(hdFai.isIE6()) {
			var scrollTop = hdFai.top.$('html').scrollTop();
			hdFai.top.$('html').data('scrollTop', scrollTop);
			hdFai.top.$('html').scrollTop(0);
			var scrollTop = hdFai.top.$('body').scrollTop();
			hdFai.top.$('body').data('scrollTop', scrollTop);
			hdFai.top.$('body').scrollTop(0);
			hdFai.top.$('html').data('overflow-x', hdFai.top.$('html').css('overflow-x'));
			hdFai.top.$('html').data('overflow-y', hdFai.top.$('html').css('overflow-y'));
			hdFai.top.$('html').css('overflow-x', 'hidden');
			hdFai.top.$('html').css('overflow-y', 'hidden');
			hdFai.top.$('body').data('overflow-x', hdFai.top.$('body').css('overflow-x'));
			hdFai.top.$('body').data('overflow-y', hdFai.top.$('body').css('overflow-y'));
			hdFai.top.$('body').css('overflow-x', 'hidden');
			hdFai.top.$('body').css('overflow-y', 'hidden');
		}
		// 处理网站置灰的情况
		if(hdFai.isIE6() || hdFai.isIE7() || hdFai.isIE8()) {
			if(hdFai.top.$('html').css('filter')) {
				hdFai.top.$('html').data('filter', hdFai.top.$('html').css('filter'));
				hdFai.top.$('html').css('filter', 'none');
			}
		}
        
		html = '<div id="popupBg' + id + '" class="popupBg" style=\'' + opacityHtml + '\' >' +
            ($.browser.msie && $.browser.version == 6.0 ?
            	'<iframe id="fixSelectIframe' + id + '" wmode="transparent" style="filter: alpha(opacity=0);opacity: 0;" class="popupBg" style="z-index:-111" src="javascript:"></iframe>'    
            	:
            	'')
        + '</div>';
        
		hdFai.top.$(html).appendTo('body');
		hdFai.stopInterval(null);
	};
    
	// 启动时间处理函数，id为null表示启动所有
	hdFai.startInterval = function(id) {
		if(hdFai.isNull(hdFai.intervalFunc)){
			return;
		}
		for(var i = 0; i < hdFai.intervalFunc.length; ++i){
			var x = hdFai.intervalFunc[i];
			if(id == null || x.id == id){
				if(x.timer){
					clearInterval(x.timer);
				}
				if(x.type == 1) {
					x.timer = setInterval(x.func, x.interval);
				}else{
					x.timer = setTimeout(x.func, x.interval);
				}
			}
		}
	};

	// 停止时间处理函数，id为null表示停止所有
	hdFai.stopInterval = function(id) {
		if(hdFai.isNull(hdFai.intervalFunc)){
			return;
		}
		for(var i = 0; i < hdFai.intervalFunc.length; ++i){
			var x = hdFai.intervalFunc[i];
			if(id == null || x.id == id){
				if(x.timer){
					clearInterval(x.timer);
				}
			}
		}
	};

	hdFai.removeBg = function(id){
		if(id){
			hdFai.top.$('#popupBg' + id).remove();
		}else{
			hdFai.top.$('.popupBg').remove();
		}
		if(hdFai.isIE6()) {
			hdFai.top.$('html').css('overflow-x', hdFai.top.$('html').data('overflow-x'));
			hdFai.top.$('html').css('overflow-y', hdFai.top.$('html').data('overflow-y'));
			hdFai.top.$('body').css('overflow-x', hdFai.top.$('body').data('overflow-x'));
			hdFai.top.$('body').css('overflow-y', hdFai.top.$('body').data('overflow-y'));
			hdFai.top.$('html').scrollTop(hdFai.top.$('html').data('scrollTop'));
			hdFai.top.$('body').scrollTop(hdFai.top.$('body').data('scrollTop'));
		}
		// 处理网站置灰的情况
		if(hdFai.isIE6() || hdFai.isIE7() || hdFai.isIE8()) {
			if(hdFai.top.$('html').data('filter')) {
				hdFai.top.$('html').css('filter', hdFai.top.$('html').data('filter'));
			}
		}

		hdFai.startInterval(null);
	};

	/*
     * jQuery Numeric
     * Version 1.3.1
     * Demo: http://www.texotela.co.uk/code/jquery/numeric/
     *
     */
	(function($) {
		/*
     * Allows only valid characters to be entered into input boxes.
     * Note: fixes value when pasting via Ctrl+V, but not when using the mouse to paste
      *      side-effect: Ctrl+A does not work, though you can still use the mouse to select (or double-click to select all)
     *
     * @name     numeric
     * @param    config      { decimal : "." , negative : true }
     * @param    callback     A function that runs if the number is not valid (fires onblur)
     * <AUTHOR> Collett (http://www.texotela.co.uk)
     * @example  $(".numeric").numeric();
     * @example  $(".numeric").numeric(","); // use , as separator
     * @example  $(".numeric").numeric({ decimal : "," }); // use , as separator
     * @example  $(".numeric").numeric({ negative : false }); // do not allow negative values
     * @example  $(".numeric").numeric(null, callback); // use default values, pass on the 'callback' function
     *
     */
		$.fn.numeric = function(config, callback)
		{
			if(typeof config === 'boolean')
			{
				config = { decimal: config };
			}
			config = config || {};
			// if config.negative undefined, set to true (default is to allow negative numbers)
			if(typeof config.negative == 'undefined') { config.negative = true; }
			// set decimal point
			var decimal = (config.decimal === false) ? '' : config.decimal || '.';
			// allow negatives
			var negative = (config.negative === true) ? true : false;
			// callback function
			callback = (typeof (callback) == 'function' ? callback : function() {});
			// set data and methods
			return this.data('numeric.decimal', decimal).data('numeric.negative', negative).data('numeric.callback', callback).keypress($.fn.numeric.keypress).keyup($.fn.numeric.keyup).blur($.fn.numeric.blur);
		};

		$.fn.numeric.keypress = function(e)
		{
			// get decimal character and determine if negatives are allowed
			var decimal = $.data(this, 'numeric.decimal');
			var negative = $.data(this, 'numeric.negative');
			// get the key that was pressed
			var key = e.charCode ? e.charCode : e.keyCode ? e.keyCode : 0;
			// allow enter/return key (only when in an input box)
			if(key == 13 && this.nodeName.toLowerCase() == 'input')
			{
				return true;
			}
			else if(key == 13)
			{
				return false;
			}
			var allow = false;
			// allow Ctrl+A
			if((e.ctrlKey && key == 97 /* firefox */) || (e.ctrlKey && key == 65) /* opera */) { return true; }
			// allow Ctrl+X (cut)
			if((e.ctrlKey && key == 120 /* firefox */) || (e.ctrlKey && key == 88) /* opera */) { return true; }
			// allow Ctrl+C (copy)
			if((e.ctrlKey && key == 99 /* firefox */) || (e.ctrlKey && key == 67) /* opera */) { return true; }
			// allow Ctrl+Z (undo)
			if((e.ctrlKey && key == 122 /* firefox */) || (e.ctrlKey && key == 90) /* opera */) { return true; }
			// allow or deny Ctrl+V (paste), Shift+Ins
			if((e.ctrlKey && key == 118 /* firefox */) || (e.ctrlKey && key == 86) /* opera */ ||
          (e.shiftKey && key == 45)) { return true; }
			// if a number was not pressed
			if(key < 48 || key > 57)
			{
				var value = $(this).val();
				/* '-' only allowed at start and if negative numbers allowed */
				if(value.indexOf('-') !== 0 && negative && key == 45 && (value.length === 0 || parseInt($.fn.getSelectionStart(this), 10) === 0)) { return true; }
				/* only one decimal separator allowed */
				if(decimal && key == decimal.charCodeAt(0) && value.indexOf(decimal) != -1)
				{
					allow = false;
				}
				// check for other keys that have special purposes
				if(
					key != 8 /* backspace */ &&
                key != 9 /* tab */ &&
                key != 13 /* enter */ &&
                key != 35 /* end */ &&
                key != 36 /* home */ &&
                key != 37 /* left */ &&
                key != 39 /* right */ &&
                key != 46 /* del */
				)
				{
					allow = false;
				}
				else
				{
					// for detecting special keys (listed above)
					// IE does not support 'charCode' and ignores them in keypress anyway
					if(typeof e.charCode != 'undefined')
					{
						// special keys have 'keyCode' and 'which' the same (e.g. backspace)
						if(e.keyCode == e.which && e.which !== 0)
						{
							allow = true;
							// . and delete share the same code, don't allow . (will be set to true later if it is the decimal point)
							if(e.which == 46) { allow = false; }
						}
						// or keyCode != 0 and 'charCode'/'which' = 0
						else if(e.keyCode !== 0 && e.charCode === 0 && e.which === 0)
						{
							allow = true;
						}
					}
				}
				// if key pressed is the decimal and it is not already in the field
				if(decimal && key == decimal.charCodeAt(0))
				{
					if(value.indexOf(decimal) == -1)
					{
						allow = true;
					}
					else
					{
						allow = false;
					}
				}
			}
			else
			{
				allow = true;
			}
			return allow;
		};

		$.fn.numeric.keyup = function(e)
		{
			var val = $(this).val();
			if(val && val.length > 0)
			{
				// get carat (cursor) position
				var carat = $.fn.getSelectionStart(this);
				var selectionEnd = $.fn.getSelectionEnd(this);
				// get decimal character and determine if negatives are allowed
				var decimal = $.data(this, 'numeric.decimal');
				var negative = $.data(this, 'numeric.negative');
            
				// prepend a 0 if necessary
				if(decimal !== '' && decimal !== null)
				{
					// find decimal point
					var dot = val.indexOf(decimal);
					// if dot at start, add 0 before
					if(dot === 0)
					{
						this.value = '0' + val;
					}
					// if dot at position 1, check if there is a - symbol before it
					if(dot == 1 && val.charAt(0) == '-')
					{
						this.value = '-0' + val.substring(1);
					}
					val = this.value;
				}
            
				// if pasted in, only allow the following characters
				var validChars = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, '-', decimal];
				// get length of the value (to loop through)
				var length = val.length;
				// loop backwards (to prevent going out of bounds)
				for(var i = length - 1; i >= 0; i--)
				{
					var ch = val.charAt(i);
					// remove '-' if it is in the wrong place
					if(i !== 0 && ch == '-')
					{
						val = val.substring(0, i) + val.substring(i + 1);
					}
					// remove character if it is at the start, a '-' and negatives aren't allowed
					else if(i === 0 && !negative && ch == '-')
					{
						val = val.substring(1);
					}
					var validChar = false;
					// loop through validChars
					for(var j = 0; j < validChars.length; j++)
					{
						// if it is valid, break out the loop
						if(ch == validChars[j])
						{
							validChar = true;
							break;
						}
					}
					// if not a valid character, or a space, remove
					if(!validChar || ch == ' ')
					{
						val = val.substring(0, i) + val.substring(i + 1);
					}
				}
				// remove extra decimal characters
				var firstDecimal = val.indexOf(decimal);
				if(firstDecimal > 0)
				{
					for(var k = length - 1; k > firstDecimal; k--)
					{
						var chch = val.charAt(k);
						// remove decimal character
						if(chch == decimal)
						{
							val = val.substring(0, k) + val.substring(k + 1);
						}
					}
				}
				// set the value and prevent the cursor moving to the end
				this.value = val;
				$.fn.setSelection(this, [carat, selectionEnd]);
			}
		};

		$.fn.numeric.blur = function()
		{
			var decimal = $.data(this, 'numeric.decimal');
			var callback = $.data(this, 'numeric.callback');
			var val = this.value;
			if(val !== '')
			{
				var re = new RegExp('^\\d+$|^\\d*' + decimal + '\\d+$');
				if(!re.exec(val))
				{
					callback.apply(this);
				}
			}
		};

		$.fn.removeNumeric = function()
		{
			return this.data('numeric.decimal', null).data('numeric.negative', null).data('numeric.callback', null).unbind('keypress', $.fn.numeric.keypress).unbind('blur', $.fn.numeric.blur);
		};

		// Based on code from http://javascript.nwbox.com/cursor_position/ (Diego Perini <<EMAIL>>)
		$.fn.getSelectionStart = function(o)
		{
			if(o.createTextRange)
			{
				var r = document.selection.createRange().duplicate();
				r.moveEnd('character', o.value.length);
				if(r.text === '') { return o.value.length; }
				return o.value.lastIndexOf(r.text);
			}else{ return o.selectionStart; }
		};

		// Based on code from http://javascript.nwbox.com/cursor_position/ (Diego Perini <<EMAIL>>)
		$.fn.getSelectionEnd = function(o)
		{
			if(o.createTextRange) {
				var r = document.selection.createRange().duplicate();
				r.moveStart('character', -o.value.length);
				return r.text.length;
			}else return o.selectionEnd;
		};

		// set the selection, o is the object (input), p is the position ([start, end] or just start)
		$.fn.setSelection = function(o, p)
		{
			// if p is number, start and end are the same
			if(typeof p == 'number') { p = [p, p]; }
			// only set if p is an array of length 2
			if(p && p.constructor == Array && p.length == 2)
			{
				if(o.createTextRange)
				{
					var r = o.createTextRange();
					r.collapse(true);
					r.moveStart('character', p[0]);
					r.moveEnd('character', p[1]);
					r.select();
				}
				else if(o.setSelectionRange)
				{
					o.focus();
					o.setSelectionRange(p[0], p[1]);
				}
			}
		};

	})(jQuery);


	hdFai.Img = {};

	hdFai.Img = {
    	MODE_SCALE_FILL: 1,				// 根据区域能够填满的最大值等比例缩放。图片100x50，区域50x50，结果50x25。
    	MODE_SCALE_WIDTH: 2,			// 根据区域宽度等比例缩放，结果高度将不受区域高度限制，即可能撑大高度。图片100x50，区域50x10，结果50x25。
    	MODE_SCALE_HEIGHT: 3,			// 根据区域高度等比例缩放，结果宽度将不受区域宽度限制，即可能撑大宽度。图片100x50，区域50x50，结果100x50。
    	MODE_SCALE_DEFLATE_WIDTH: 4,	// 根据区域宽度等比例缩小，不放大，结果高度将不受区域高度限制。图片100x50，区域50x10，结果50x25；图片100x50，区域200x100，结果100x50。
    	MODE_SCALE_DEFLATE_HEIGHT: 5,	// 根据区域高度等比例缩小，不放大，结果宽度将不受区域高度限制。图片100x50，区域50x50，结果100x50；图片100x50，区域200x100，结果100x50。
    	MODE_SCALE_DEFLATE_FILL: 6,		// 根据区域能够填满的最大值等比例缩小，不放大。图片100x50，区域50x50，结果50x25。
    	MODE_SCALE_DEFLATE_MAX: 7 		// 根据区域等比例缩小，不放大，结果的宽度和高度不能同时超过区域限制。图片200x100，区域100x100，结果200x100；图片100x200，区域100x100，结果100x200。
	};

	// 使用此函数时，不要在img标签中先设置大小，会使得调整img大小时失败；先隐藏图片，避免出现图片从原始图片变为目标图片的过程
	// 	<img src="xx.jpg" style="display:none;" onload="hdFai.Img.optimize(this, {width:100, height:50, mode:hdFai.Img.MODE_SCALE_FILL});"/>
	hdFai.Img.optimize = function(img, option, callback){
    	// ie下对于display:none的img不会加载
    	// 这里要用临时图片，是因为当动态改变图片src时，由于图片的大小已经被设置，因此再次获取会失败
    	var imgTmp = new Image();   
    	// 这里还不能先置空，否则将会引起对''文件的一次访问
    	//	imgTmp.src = '';
    	imgTmp.src = img.src;
    	var imgWidth = imgTmp.width;
		var imgHeight = imgTmp.height;

		// 加载图片之后再获取图片尺寸,防止获取不到图片尺寸时,并且img标签中存在大小时,会导致图片变形
		imgTmp.onload = function () {
			var imgWidth = imgTmp.width;
			var imgHeight = imgTmp.height;
			var size = hdFai.Img.calcSize(imgWidth, imgHeight, option.width, option.height, option.mode);
			img.width = size.width;
			img.height = size.height;
			if(option.display == 1)
			{
				img.style.display = 'inline';
			}else if(option.display == 2){
				img.style.display = 'none';
			}else{
				img.style.display = 'block';
			}
			callback && callback(img, {imgWidth: imgWidth, imgHeight: imgHeight, width: img.width, height: img.height});
		}

    	// if(hdFai.isNull(imgWidth) || imgWidth == 0 || hdFai.isNull(imgHeight) || imgHeight == 0) {
		// 	// chrome似乎对临时图片的加载会有延迟，立即取大小会失败
    	// 	imgWidth = img.width;
		// 	imgHeight = img.height;
		// }
		// //	alert(imgTmp.width + ":" + imgTmp.height + "\n" + img.width + ":" + img.height);
		// var size = hdFai.Img.calcSize(imgWidth, imgHeight, option.width, option.height, option.mode);
    	// img.width = size.width;
    	// img.height = size.height;
    	// if(option.display == 1)
    	// {
    	// 	img.style.display = 'inline';
    	// }else if(option.display == 2){
    	// 	img.style.display = 'none';
    	// }else{
    	// 	img.style.display = 'block';
    	// }
    	// return {width: img.width, height: img.height};
	};
	hdFai.Img.calcSize = function(width, height, maxWidth, maxHeight, mode){
		var size = {width: width, height: height};      
		if(mode == hdFai.Img.MODE_SCALE_FILL){
    		var rateWidth = width / maxWidth;      
    		var rateHeight = height / maxHeight;      
    			  
    		if(rateWidth > rateHeight){      
    			size.width = maxWidth;      
    			size.height = height / rateWidth;      
    		}else{      
    			size.width = width / rateHeight;      
    			size.height = maxHeight;      
			}     
		}
    	else if(mode == hdFai.Img.MODE_SCALE_WIDTH){
    		var rateWidth = width / maxWidth;      
    		size.width = maxWidth;      
    		size.height = height / rateWidth;      
    	}
    	else if(mode == hdFai.Img.MODE_SCALE_HEIGHT){
    		var rateHeight = height / maxHeight;      
    		size.width = width / rateHeight;      
    		size.height = maxHeight;      
    	}
    	else if(mode == hdFai.Img.MODE_SCALE_DEFLATE_WIDTH){
    		var rateWidth = width / maxWidth; 
    		if(rateWidth > 1){
    			size.width = maxWidth;      
    			size.height = height / rateWidth;      
    		}
    	}
    	else if(mode == hdFai.Img.MODE_SCALE_DEFLATE_HEIGHT){
    		var rateHeight = height / maxHeight;      
    		if(rateHeight > 1){
    			size.width = width / rateHeight;      
    			size.height = maxHeight;      
    		}
    	}
		else if(mode == hdFai.Img.MODE_SCALE_DEFLATE_FILL){
    		var rateWidth = width / maxWidth;
    		var rateHeight = height / maxHeight;
    		
    		if(rateWidth > rateHeight){   
    			if(rateWidth > 1) {
    				size.width = maxWidth;      
    				size.height = height / rateWidth;
    			}
    		}else{
    			if(rateHeight > 1)	{
    				size.width = width / rateHeight;      
    				size.height = maxHeight;      
    			}
    		}
    	}else if(mode == hdFai.Img.MODE_SCALE_DEFLATE_MAX) {
    		if(width > maxWidth && height > maxHeight) {
    			var rateWidth = width / maxWidth;
    			var rateHeight = height / maxHeight;

    			if(rateWidth < rateHeight) {
    				size.width = maxWidth;
    				size.height = height / rateWidth;
    			}else{
    				size.width = width / rateHeight;
    				size.height = maxHeight;
    			}
    		}
		}
    	size.width = Math.floor(size.width);
    	size.height = Math.floor(size.height);
    	if(size.width == 0) {
    		size.width = 1;
    	}
    	if(size.height == 0) {
    		size.height = 1;
    	}
		return size;
	};


	hdFai.checkBit = function (flag, bitFlag) {
    	//return (flag & bitFlag) == bitFlag;
    	/*
    	因为js位操作有关的超过了32位后无效。所有位置0。第32位代表负数。且32位的左移1位就是直接跳回到第1位。  与java long类型移位操作不符。
    	20131225修改  支持long 类型62位内的checkBit 
    	*/
    	var bit_31 = true;
    	//31位皆置1为：2147483647
    	if( flag > 2147483647 || flag < 0 || bitFlag > 2147483647 || bitFlag < 0 ){
    		bit_31 = false;
    	}
    	if( bit_31 ){
    		return (flag & bitFlag) == bitFlag;
    	}
    	
    	var flagBinary = flag.toString(2);
    	var bitFlagBinary = bitFlag.toString(2);
    	if( flagBinary.length > 62 || bitFlagBinary.length > 62 ){
    		alert( 'Does not support more than 62 bit. flagBinary.length=' + flagBinary.length + ',bitFlagBinary.length' + bitFlagBinary.length + '.' );
    		return false;
    	}
    	//flagBinary = flagBinary.split("").reverse().join("");
    	//bitFlagBinary = bitFlagBinary.split("").reverse().join("");
    	var flagHight = flagLow = bitFlagHight = bitFlagLow = 0;
    	//拆分高低位处理
    	if( flagBinary.length > 31 ){
    		var hightStr = flagBinary.slice(0, flagBinary.length - 31);
    		var lowStr = flagBinary.slice(flagBinary.length - 31);
    		flagHight = parseInt( hightStr, '2' );
    		flagLow = parseInt( lowStr, '2' );
    	}else{
    		flagLow = parseInt( flagBinary.slice(0, flagBinary.length), '2' );
    	}
    	if( bitFlagBinary.length > 31 ){
    		var hightStr = bitFlagBinary.slice(0, bitFlagBinary.length - 31);
    		var lowStr = bitFlagBinary.slice(bitFlagBinary.length - 31);
    		bitFlagHight = parseInt( hightStr, '2' );
    		bitFlagLow = parseInt( lowStr, '2' );
    	}else{
    		bitFlagLow = parseInt( bitFlagBinary.slice(0, bitFlagBinary.length), '2' );
    	}
    	
    	var result = (flagLow & bitFlagLow) == bitFlagLow;
    	if( result ){
    		result = (flagHight & bitFlagHight) == bitFlagHight;
    	}
    	return result;
	};

	hdFai.isNumberKey = function (e, iSminus){ //按下数字键则返回true,用法：<input type="text" onkeypress="javascript:return hdFai.isNumberKey(event);"/>
    	 if($.browser.msie) {
    		 if(iSminus && event.keyCode == 45){
    			return true; 
    		}   
			if( ((event.keyCode > 47) && (event.keyCode < 58)) ||   
                  (event.keyCode == 8) ) {   
				return true;   
			}else{   
				return false;   
			}   
		}else{
    		if(iSminus && e.which == 45){
    			return true; 
    		}
			if( ((e.which > 47) && (e.which < 58)) ||   
                  (e.which == 8) ) {   
				return true;   
			}else{   
				return false;   
			}   
		} 
	};
	hdFai.isFloatKey = function (e){ //按下数字键和小数点则返回true,用法：<input type="text" onkeypress="javascript:return hdFai.isFloatKey(event);"/>
    	 if($.browser.msie) {   
			if( ((event.keyCode > 47) && (event.keyCode < 58)) ||   
                  (event.keyCode == 8) || (event.keyCode == 46) ) {   
				return true;   
			}else{   
				return false;   
			}   
		}else{   
			if( ((e.which > 47) && (e.which < 58)) ||   
                  (e.which == 8) || (e.which == 46) ) {   
				return true;   
			}else{   
				return false;   
			}   
		}   
	};

	/*
     * 判断是否整数数字
     */
	//hdFai.isDigit = function(numVal){
	//	return /^\d+$/.test(numVal);
	//};
	hdFai.isDigit = function(numVal){
    	if(numVal < '0' || numVal > '9') {
    		return false;
    	}
    	
    	return true;
	};
	// 注意不能用于未声明的顶层变量的判断，例如不能Fai.isNull(abc)，只能Fai.isNull(abc.d)
	hdFai.isNull = function (obj){
    	return (typeof obj == 'undefined') || (obj == null);
	};

	/**
     * Leyewen check is IE
     * @return boolean :true/false
     *     $.isIE()
     */
	hdFai.isIE = function(){
    	return $.browser.msie ? true : false;
	};
	hdFai.isIE6 = function () {
		if($.browser.msie) {
			if($.browser.version == '6.0')return true;
		}
		return false;
	};
	hdFai.isIE7 = function () {
		if($.browser.msie) {
			if($.browser.version == '7.0')return true;
		}
		return false;
	};
	hdFai.isIE8 = function () {
		if($.browser.msie) {
			if($.browser.version == '8.0')return true;
		}
		return false;
	};

	hdFai.isIE9 = function () {
		if($.browser.msie) {
			if($.browser.version == '9.0')return true;
		}
		return false;
	};
	hdFai.isIE10 = function () {
		if($.browser.msie) {
			if($.browser.version == '10.0')return true;
		}
		return false;
	};

	/**
     * Leyewen check is Safari
     * @return boolean :true/false
     *     $.isSafari()
     */
	hdFai.isSafari = function(){
    	//alert($.toJSON($.browser));
    	return $.browser.safari ? true : false;
	};

	/**
     * Leyewen check is Webkit
     * @return boolean :true/false
     *     $.isWebkit()
     */
	hdFai.isWebkit = function(){
    	return $.browser.webkit ? true : false;
	};

	/**
     * Leyewen check is Chrome
     * @return boolean :true/false
     *     $.isChrome()
     */
	hdFai.isChrome = function(){
    	return $.browser.chrome ? true : false;
	};

	/**
     * Leyewen check is Mozilla
     * @return boolean :true/false
     *     $.isMozilla()
     */
	hdFai.isMozilla = function(){
    	return $.browser.mozilla ? true : false;
	};

	hdFai.isAppleWebKit = function(){
    	var ua = window.navigator.userAgent;
    	if(ua.indexOf('AppleWebKit') >= 0) {
    		return true;
    	}
    	return false;
	};

	/**
     * Leyewen check is Opera
     * @return boolean :true/false
     *     $.isOpera()
     */
	hdFai.isOpera = function(){
    	return $.browser.opera || $.browser.opr ? true : false;
	};

	hdFai.isAndroid = function() {
    	return $.browser.android ? true : false;
	};

	hdFai.isIpad = function() {
    	return $.browser.ipad ? true : false;
	};

	hdFai.isIphone = function() {
    	return $.browser.iphone ? true : false;
	};


	/**
     * 屏幕类型定义
     * type的定义在JAVA的fai.web.Request中定义，要求一模一样
     */
	hdFai.BrowserType = {
    	'UNKNOWN': 0,
    	'SPIDER': 1,
    	'CHROME': 2,
    	'FIREFOX': 3,			// Mozilla
    	'MSIE8': 4,
    	'MSIE7': 5,
    	'MSIE6': 6,
    	'MSIE9': 7,
    	'SAFARI': 8,
    	'MSIE10': 9,
    	'MSIE11': 10,
    	'OPERA': 11,
    	'APPLE_WEBKIT': 12
	};

	hdFai.getBrowserType = function(){
    	if(hdFai.isIE6()) {
    		return hdFai.BrowserType.MSIE6;
    	}else if(hdFai.isIE7()) {
    		return hdFai.BrowserType.MSIE7;
    	}else if(hdFai.isIE8()) {
    		return hdFai.BrowserType.MSIE8;
    	}else if(hdFai.isIE9()) {
    		return hdFai.BrowserType.MSIE9;
    	}else if(hdFai.isIE10()) {
    		return hdFai.BrowserType.MSIE10;
    	}else if(hdFai.isIE11()) {
    		return hdFai.BrowserType.MSIE11;
    	}else if(hdFai.isMozilla()) {
    		return hdFai.BrowserType.FIREFOX;
    	}else if(hdFai.isOpera()) {
    		return hdFai.BrowserType.OPERA;
    	}else if(hdFai.isChrome()) {
    		return hdFai.BrowserType.CHROME;
    	}else if(hdFai.isSafari()) {
    		return hdFai.BrowserType.SAFARI;
    	}else if(hdFai.isAppleWebKit()) {
    		return hdFai.BrowserType.APPLE_WEBKIT;
    	}else{
    		return hdFai.BrowserType.UNKNOWN;
    	}
	};
	// 进行html编码
	// .replace(/ /g, "&nbsp;").replace(/\b&nbsp;+/g, " ")用于替换空格 再查找单个或者连续的空格，把单个或连续串中第一个替换为原来的“ ”。
	hdFai.encodeHtml = function(html){
    	return html && html.replace ? (html.replace(/&/g, '&amp;').replace(/ /g, '&nbsp;').replace(/\b&nbsp;+/g, ' ').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\/g, '&#92;').replace(/\'/g, '&#39;').replace(/\"/g, '&quot;').replace(/\n/g, '<br/>').replace(/\r/g, '')) : html;
	};
	// 进行html解码
	hdFai.decodeHtml = function(html){
    	return html && html.replace ? (html.replace(/&nbsp;/gi, ' ').replace(/&lt;/gi, '<').replace(/&gt;/g, '>')
    		.replace(/&#92;/gi, '\\').replace(/&#39;/gi, '\'').replace(/&quot;/gi, '"').replace(/\<br\/\>/gi, '\n').replace(/&amp;/gi, '&')) : html;
	};
	// 把字符串转换为写在html标签中javascript的字符串，例如<div onclick="alert('xxx')">
	hdFai.encodeHtmlJs = function(html){
    	return html && html.replace ? (html.replace(/\\/g, '\\\\').replace(/\'/g, '\\x27').replace(/\"/g, '\\x22').replace(/\n/g, '\\n').replace(/</g, '\\x3c').replace(/>/g, '\\x3e')) : html;
	};
	// 把字符串转换为写在html中属性值，例如<div title="xxx">
	hdFai.encodeHtmlAttr = function(html){
    	return html && html.replace ? (html.replace(/\"/g, '&#x22;').replace(/\'/g, '&#x27;').replace(/</g, '&#x3c;').replace(/>/g, '&#x3e;').replace(/&/g, '&#x26;')).replace(/\\/g, '&#5c;') : html;
	};
	// 进行url编码
	hdFai.encodeUrl = function (url){
    	/*
    	return url && url.replace ?
    		url.replace(/%/ig, "%25").replace(/\+/ig, "%2B").replace(/&/ig, "%26").replace(/#/ig, "%23") 
    		: url;
        str = str.replace(/,/g, '%2C');
        str = str.replace(/\//g, '%2F');
        str = str.replace(/\?/g, '%3F');
        str = str.replace(/:/g, '%3A');
        str = str.replace(/@/g, '%40');
        str = str.replace(/&/g, '%26');
        str = str.replace(/=/g, '%3D');
        str = str.replace(/\+/g, '%2B');
        str = str.replace(/\$/g, '%24');
        str = str.replace(/#/g, '%23');
        return str;
    	*/
    	return typeof url === 'undefined' ? '' : encodeURIComponent(url);
	};
	// 进行url解码
	hdFai.decodeUrl = function (url){
    	return typeof url === 'undefined' ? '' : decodeURIComponent(url);
	};


	/*
    灰色透明背景，正在运行的图标
    提交的时候使用Fai.ing(str, autoClose):str为要显示的语句，如果为空则显示"正在处理"
    如果autoClose == true，则在2秒钟后自动close便签，否则不自动关闭。
    得到结果后使用Fai.removeIng()
    */
	hdFai.ing = function(str, autoClose) {
		if(window !== top && top.$('.bg-mask').is(':visible')){
			return top.hdFai.ing.apply(top.hdFai, arguments);
		}
    	var msg = (str == null || str == '') ? '正在处理...' : str;
    	var windowWidth = hdFai.top.document.body.clientWidth;
    	var windowHeight = hdFai.top.document.body.clientHeight;
    	var html = '';
    	var ingStyle = 'position:fixed; top:50px; left: 50%; margin:0px auto; width:auto;  height:auto; z-index:9999;';
    	var animateStyle = 'transition: opacity ease .6s; -moz-transition: opacity ease .6s; -webkit-transition: opacity ease .6s; -o-transition: opacity ease .6s; opacity: 0; -webkit-opacity: 0; -moz-opacity: 0; -khtml-opacity: 0; filter:alpha(opacity=0);';
    	html = '<div id=\'ing\' style=\'' + ingStyle + animateStyle + '\'></div>';
    	if(hdFai.top.$('#ing').length == 0) {
    		hdFai.top.$(html).appendTo('body');
    	}
    	var ing = hdFai.top.$('#ing');
    	var bodyTop = hdFai.top.$('body').scrollTop();
    	if(hdFai.isIE() && bodyTop == 0) {
    		bodyTop = hdFai.top.$('html').scrollTop();
    	}
    	if(bodyTop > 0) {
    		ing.css('top', (bodyTop) + 50 + 'px');
    	}
    	
    	var id = parseInt(Math.random() * 10000);
    	var tips = '';
    	tips += '<div id="' + id + '" class="tips">'
    			+ 	'<div class="msg">' + msg + '</div><div class=\'close\' onclick="hdFai.top.hdFai.removeIng(false, ' + id + ');"></div>'
    			+ '</div>';
    	ing.find('.tips').remove();
    	hdFai.top.$(tips).appendTo(ing);
    	
    	// 居中处理
    	var ingWidth = hdFai.top.$(ing).width();
    	hdFai.top.$(ing).css('left', (hdFai.top.document.documentElement.clientWidth - ingWidth) / 2 );

    	if(autoClose){
    		hdFai.top.hdFai.removeIng(autoClose, id);
    	}

    	/* 渐变出现 */
    	var isIe = hdFai.isIE6() || hdFai.isIE7() || hdFai.isIE8();
    	if(isIe) {
    		hdFai.top.$('#ing').animate({ opacity: 1, filter: 'alpha(opacity=100)'}, 300);
    	}else{
    		hdFai.top.$('#ing').css({ opacity: 1 });
    	}
    	
    	hdFai.top.$('#ing').find('.close').bind('mouseenter', function () {
    		$(this).addClass('close_hover');
    	}).bind('mouseleave', function () {
    		$(this).removeClass('close_hover');
    	});
	};

	hdFai.removeAllIng = function() {
    	hdFai.top.$('#ing').remove();
	};

	hdFai.removeIng = function(autoClose, id){
    	if(autoClose){
    		if(typeof id != 'undefined' && hdFai.top.$('#' + id).length > 0){
    			hdFai.top.window.setTimeout(function(){ $('#' + id).fadeOut(1000); }, 3000);
    			hdFai.top.window.setTimeout(function(){ $('#' + id).remove(); }, 4500);
    		}else{
    			hdFai.top.$('.tips').fadeOut(1000);
    			hdFai.top.window.setTimeout(function(){ $('#ing').remove(); }, 3000);
    		}
    	}else{
    		if(typeof id != 'undefined' && hdFai.top.$('#' + id).length > 0){
    			hdFai.top.$('#' + id).fadeOut(500);
    			hdFai.top.window.setTimeout(function(){ $('#' + id).remove(); }, 1000);
    		}
    		else{
    			hdFai.top.$('.tips').fadeOut(500);
    			hdFai.top.window.setTimeout(function(){ $('#ing').remove(); }, 1000);
    		}
    	}
    	hdFai.top.$('#ing').css('opacity', 0);
	};

	/**
     * 文件单位转换(B)转成(KB/MB)
     */
	hdFai.parseFileSize = function(bit){
    	
    	if(typeof bit != 'undefined' && typeof bit == 'number'){
    		var newFileSize;
    		if(bit < 1024){
    			newFileSize = bit + 'B';
    		}else if(bit < 1024 * 1024){
    			var tmpSize = bit / 1024;
    			//alert(tmpSize);
    			newFileSize = tmpSize.toFixed(2) + 'KB';
    		}else{
    			var tmpSize = bit / (1024 * 1024);
    			newFileSize = tmpSize.toFixed(2) + 'MB';
    		}
    		
    		return newFileSize;
    	}else{
    		return '-';
    	}
	};

	(function(hdFai){
		function HdList(primaryKey, list, like){
			if(primaryKey instanceof HdList){
				return primaryKey;
			}
			if(list instanceof HdList){
				return list;
			}
			if(!(this instanceof HdList)){
				return new HdList(primaryKey, list, like);
			}
			this.noPrimaryKey = false;
			if($.isArray(primaryKey)){
				like = list;
				list = primaryKey;
				primaryKey = undefined;
			}else if($.type(primaryKey) == 'boolean'){
				like = primaryKey;
				primaryKey = undefined;
			}
			if(primaryKey === undefined){
				this.noPrimaryKey = true;
			}
			if(list === undefined){
				list = [];
			}
			this.list = list;
			if(like !== undefined){
				this.like = like;
			}
			if(!this.noPrimaryKey){
				this.primaryKey = primaryKey;
			}
		}
        
		var addList = ['concat', 'push', 'splice', 'unshift'];
		HdList.fn = HdList.prototype;
		$.forEach(['copyWithin', 'entries', 'concat', 'every', 'fill', 'filter', 'find', 'findIndex', 'forEach', 'includes', 'indexOf', 'join', 'keys', 'lastIndexOf', 'map', 'pop', 'push', 'reduce', 'reduceRight', 'reverse', 'shift', 'slice', 'some', 'sort', 'splice', 'toLocaleString', 'toString', 'unshift'], function(key){
			if(!arr_proto[key]) return;
			var isAdd = addList.indexOf(key) != -1;
			HdList.fn[key] = function(){
				var args = arr_slice.call(arguments), _this = this, start, rmDuplicate;
				if(isAdd){
					start = 0;
					rmDuplicate = function(arr){
						for( var i = start; i < arr.length; i++ ) {
							if( _this.contains(arr[i]) ){
								arr.splice(i, 1);
								i--;
							}
						}
					};
					if(key == 'concat'){
						$.forEach(args, rmDuplicate);
					}else{
						if(key == 'splice'){
							start = 2;
						}
						rmDuplicate(args);
					}
				}
				return _this.list[key].apply(_this.list, args);
			};
		});
		$.extend(HdList.fn, {
			size: function(){
				return this.list.length;
			},
			empty: function(){
				this.list.length = 0;
			},
			get: function(index){
				return this.list[index];
			},
			set: function(index, val){
				this.list[index] = val;
				return this;
			},
			getIndex: function(findName, like){
				like === undefined && (like = this.like);
				var len = this.size(), name;
				for(var i = 0; i < len; i++ ) {
					name = this.getName(i);
					if(like ? name == findName : name === findName ) {
						return i;
					}
				}
				return -1;
			},
			contains: function(name, like){
				return this.getIndex(name, like) != -1;
			},
			setName: function(name, newName){
				if(this.contains(newName))return this;
				if(this.noPrimaryKey){
					var index = this.getIndex(name);
					index != -1 && this.set(index, newName);
				}else{
					var val = this.getByName(name);
					if(!val){
						console.warn('HdList setName err name:' + name);
						return this;
					}
					val[this.primaryKey] = newName;
				}
				return this;
			},
			getName: function(index){
				var val = this.get(index);
				return this.noPrimaryKey ? val : (val ? val[this.primaryKey] : undefined);
			},
			getByName: function(name){
				return this.get(this.getIndex(name));
			},
			setByName: function(name, val){
				var index = this.getIndex(name);
				if(index === -1){
					console.warn('HdList setByName err name:' + name);
					return this;
				}
				this.set(index, val);
				return this;
			},
			not: function(name){
				if(arguments.length < 2){
					var index = this.getIndex(name);
					if(index !== -1){
						return this.splice(index, 1)[0];
					}
				}else{
					var notList = [];
					for(var i = 0; i < arguments.length; i++) {
						notList.push(this.not(arguments[i]));
					}
					return HdList(this.primaryKey, notList);
				}
			},
			add: function(){
				this.push.apply(this, arguments);
				return this;
			},
			clone: function(){
				return HdList(this.primaryKey, [].concat(this.list));
			},
		});
		$.each(['not', 'add'], function(i, key) {
			HdList.fn[key + 'List'] = function(list){
				if(list instanceof HdList){
					list = list.list;
				}
				return this[key].apply(this, list);
			};
		});
		$.each({
			'replace': [0, 1],
			'after': [1, 0],
			'before': [0, 0],
		}, function(key, val) {
			var index_key = key + 'ByIndex';
			HdList.fn[index_key] = function(index){
				arr_proto.splice.call(arguments, 0, 1, index + val[0], val[1]);
				this.splice.apply(this, arguments);
				return this;
			};
			HdList.fn[key] = function(name){
				var args = arr_slice.call(arguments);
				args[0] = this.getIndex(name);
				return this[index_key].apply(this, args);
			};
		});

		if(!HdList.fn.forEach){
			HdList.fn.forEach = function(fn){
				$.forEach(this.list, fn);
			};
		}

		hdFai.HdList = HdList;
        
	})(hdFai);

	(function (hdFai){
		var empty = {};
		hdFai.watch = newWatch();
		hdFai.watch.$new = newWatch;
		hdFai.watch.$create = createWatch;

		function newWatch(){
			var cache = {};
			return function watch(key, val, fn, uFn){
				if(typeof val == 'function'){
					var args = arr_slice.call(arguments);
					args.unshift(val.toString());
					return watch.apply(this, args);
				}
				var obj = check(!(cache.hasOwnProperty(key)), val, cache[key], fn, uFn);
				obj.hasOwnProperty('last') && (cache[key] = obj.last);
				return obj.rt;
			};
		}
		function createWatch(){
			//var lastVal = empty;
			var lastVal = arguments.length > 0 ? arguments[0] : empty;
			return function(val, fn, uFn){
				var obj = check(lastVal === empty, val, lastVal, fn, uFn);
				obj.hasOwnProperty('last') && (lastVal = obj.last);
				return obj.rt;
			};
		}
		function check(first, val, lastVal, fn, uFn){
			var last = val, change;
			if(!first){
				last = lastVal;
				change = false;
				if($.isArray(val) && $.isArray(last)){
					$.each(last, function(index, v) {
						if(v !== val[index]){
							change = true;
							return false;
						}
					});
				}else{
					change = val !== last;
				}
				if(!change)return { rt: uFn && uFn(last) };
			}
			return {
				rt: fn(val, last),
				last: $.isArray(val) ? $.extend([], val) : val,
			};
		}
	})(hdFai);

	(function(hdFai){
		var ONE_NO_FIRE = 1;
		var ONE_HAS_FIRE = 2;

		function CallBack(free){
			if(!(this instanceof CallBack)){
				return new CallBack(free);
			}
			this.callbacks = {};
			this._ones = {};
			this.free = free;
		}

		var p = {
			register: function(name, isOne){    //注册回调句柄
				var _this = this;
				if($.isArray(name)){
					$.each(name, function(index, item) {
						if($.isArray(item)){
							_this.register.apply(_this, item);
						}else{
							_this.register(item);
						}
					});
					return _this;
				}
				if(typeof name == 'string' && typeof this.callbacks[name] == 'undefined'){
					this.callbacks[name] = null;
					if(isOne){
						this._ones[name] = ONE_NO_FIRE;
					}
				}
				return _this;
			},
			on: function(name, callback){ //绑定回调事件
				var _this = this, obj;
				if(_this.checkFire(name)){
					return callback();
				}
				if(!_this.callbacks.hasOwnProperty(name)){
                    if(!_this.free){
                        return _this;
                    }
                    if($.type(_this.free) == 'function' && _this.free(name)){
                        _this._ones[name] = ONE_NO_FIRE;
                    }
                }
				if(!(obj = _this.callbacks[name])){
					_this.callbacks[name] = obj = $.Callbacks( 'unique stopOnFalse' + (_this._ones[name] ? ' onec' : '') );
				}
				obj.add(callback);
				return _this;
			},
			one: function(name, callback){
				var _this = this;
				callback.$$oneCallback = function() {
					callback.apply(this, arguments);
					_this.off(name, callback);
				};
				return _this.on(name, callback.$$oneCallback);
			},
			off: function(name, callback){ //解绑回调事件
				var _this = this, obj;
				if(arguments.length == 0){
					$.each(this.callbacks, function(key, val) {
						_this.off(val, callback);
					});
					return _this;
				}
				if(!(obj = this.callbacks[name])){
					return _this;
				}
				if(arguments.length == 1){
					obj.empty();
				}else if(typeof callback == 'function'){
					if(callback.$$oneCallback){
						obj.remove(callback.$$oneCallback);
						delete callback.$$oneCallback;
					}else{
						obj.remove(callback);
					}
				}
				return _this;
			},
			checkFire: function(name){
				return this._ones[name] == ONE_HAS_FIRE;
			},
			getApiKeys: function(){
				return $.map(p, function(val, key){ return key; });
			}
		};
		$.each(['fire', 'fireWith'], function(index, key) {
			p[key] = function(){ //触发回调
				var argsArray = arr_slice.call(arguments);
				var name = argsArray.shift();
				var obj = this.callbacks[name];
				if(this._ones[name]){
					this._ones[name] = ONE_HAS_FIRE;
				}
				if(obj)return obj[key].apply(obj, argsArray);
				return true;
			};
		});
		$.extend(CallBack.prototype, p);

		hdFai.CallBack = CallBack;

	})(hdFai);

	(function($){
		$.requestAnimFrame = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function(a) {
			window.setTimeout(a, 1e3 / 60, (new Date).getTime());
		};

		$.throttle = function(defer, delay, memory){
			if(arguments.length == 1 && $.isPlainObject(defer)){
				return throttleOpts(defer);
			}
			return throttleOpts({defer: defer, delay: delay, memory: memory});
		};

		function throttleOpts(setting) {
			var opts = $.extend({
				defer: null,        //延迟回调
				sync: null,         //同步回调
				delay: 0,           //延迟时间
				memory: false,      //是否累积参数
			}, setting);

			if(!opts.defer){
				return function() {};
			}

			var data = {list: [], step: -1};

			var timeout = opts.delay == 'animFrame' ? $.requestAnimFrame : function(fn){
				return setTimeout(fn, opts.delay);
			};

			return function(){
				var args = arr_slice.call(arguments);
				data.step++;
				opts.sync && opts.sync.call(this, data.step, args);
				opts.memory ? data.list.push(args) : (data.list = args);
				if(data.step > 0)return;
				var _this = this;
				timeout(function(){
					opts.defer[opts.memory ? 'call' : 'apply'](_this, data.list);
					data.list = [];
					data.step = -1;
				});
			};
		}

	})(jQuery);

	(function($) {
    	$.cookie = function (key, value, options) {
    	
    		// key and value given, set cookie...
    		if(arguments.length > 1 && (value === null || typeof value !== 'object')) {
    			options = $.extend({}, options);
    	
    			if(value === null) {
    				options.expires = -1;
    			}
    	
    			if(typeof options.expires === 'number') {
    				var days = options.expires, t = options.expires = new Date();
    				t.setDate(t.getDate() + days);
    			}
    	
    			return (document.cookie = [
    				encodeURIComponent(key), '=',
    				options.raw ? String(value) : encodeURIComponent(String(value)),
    				options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE
    				options.path ? '; path=' + options.path : '',
    				options.domain ? '; domain=' + options.domain : '',
    				options.secure ? '; secure' : ''
    			].join(''));
    		}
    	
    		// key and possibly options given, get cookie...
    		options = value || {};
    		var result, decode = options.raw ? function (s) { return s; } : decodeURIComponent;
    		return (result = new RegExp('(?:^|; )' + encodeURIComponent(key) + '=([^;]*)').exec(document.cookie)) ? decode(result[1]) : null;
    	};

	})(jQuery);


	/**
     * jQuery JSON Plugin
     * Time 2011-09-17 version: 2.3
     *
     * Usage:
     * Serializes a javascript object, number, string, or array into JSON. 
     *     $.toJSON()
     * Converts from JSON to Javascript, quickly, and is trivial.
     *     $.evalJSON()
     * Converts from JSON to Javascript, but does so while checking to see if the source is actually JSON, and not with other Javascript statements thrown in. 
     *     $.secureEvalJSON()
     * Places quotes around a string, and intelligently escapes any quote, backslash, or control characters. 
     *     $.quoteString()
     */
	(function ($) {
    	'use strict';

    	var escape = /["\\\x00-\x1f\x7f-\x9f]/g,
    		meta = {
    			'\b': '\\b',
    			'\t': '\\t',
    			'\n': '\\n',
    			'\f': '\\f',
    			'\r': '\\r',
    			'"': '\\"',
    			'\\': '\\\\'
    		},
    		hasOwn = Object.prototype.hasOwnProperty;

    	/**
    	 * jQuery.toJSON
    	 * Converts the given argument into a JSON representation.
    	 *
    	 * @param o {Mixed} The json-serializable *thing* to be converted
    	 *
    	 * If an object has a toJSON prototype, that will be used to get the representation.
    	 * Non-integer/string keys are skipped in the object, as are keys that point to a
    	 * function.
    	 *
    	 */
    	//$.toJSON = typeof JSON === 'object' && JSON.stringify ? JSON.stringify : function (o) {
    	$.toJSON = function (o) {
    		if(o === null) {
    			return 'null';
    		}

    		var pairs, k, name, val,
    			type = $.type(o);

    		if(type === 'undefined') {
    			return undefined;
    		}

    		// Also covers instantiated Number and Boolean objects,
    		// which are typeof 'object' but thanks to $.type, we
    		// catch them here. I don't know whether it is right
    		// or wrong that instantiated primitives are not
    		// exported to JSON as an {"object":..}.
    		// We choose this path because that's what the browsers did.
    		if(type === 'number' || type === 'boolean') {
    			return String(o);
    		}
    		if(type === 'string') {
    			return $.quoteString(o);
    		}
    		if(typeof o.toJSON === 'function') {
    			return $.toJSON(o.toJSON());
    		}
    		if(type === 'date') {
    			var month = o.getUTCMonth() + 1,
    				day = o.getUTCDate(),
    				year = o.getUTCFullYear(),
    				hours = o.getUTCHours(),
    				minutes = o.getUTCMinutes(),
    				seconds = o.getUTCSeconds(),
    				milli = o.getUTCMilliseconds();

    			if(month < 10) {
    				month = '0' + month;
    			}
    			if(day < 10) {
    				day = '0' + day;
    			}
    			if(hours < 10) {
    				hours = '0' + hours;
    			}
    			if(minutes < 10) {
    				minutes = '0' + minutes;
    			}
    			if(seconds < 10) {
    				seconds = '0' + seconds;
    			}
    			if(milli < 100) {
    				milli = '0' + milli;
    			}
    			if(milli < 10) {
    				milli = '0' + milli;
    			}
    			return '"' + year + '-' + month + '-' + day + 'T' +
    				hours + ':' + minutes + ':' + seconds +
    				'.' + milli + 'Z"';
    		}

    		pairs = [];

    		if($.isArray(o)) {
    			for(k = 0; k < o.length; k++) {
    				pairs.push($.toJSON(o[k]) || 'null');
    			}
    			return '[' + pairs.join(',') + ']';
    		}

    		// Any other object (plain object, RegExp, ..)
    		// Need to do typeof instead of $.type, because we also
    		// want to catch non-plain objects.
    		if(typeof o === 'object') {
    			for(k in o) {
    				// Only include own properties,
    				// Filter out inherited prototypes
    				if(hasOwn.call(o, k)) {
    					// Keys must be numerical or string. Skip others
    					type = typeof k;
    					if(type === 'number') {
    						name = '"' + k + '"';
    					}else if(type === 'string') {
    						name = $.quoteString(k);
    					}else{
    						continue;
    					}
    					type = typeof o[k];

    					// Invalid values like these return undefined
    					// from toJSON, however those object members
    					// shouldn't be included in the JSON string at all.
    					if(type !== 'function' && type !== 'undefined') {
    						val = $.toJSON(o[k]);
    						pairs.push(name + ':' + val);
    					}
    				}
    			}
    			return '{' + pairs.join(',') + '}';
    		}
    	};

    	/**
    	 * jQuery.evalJSON
    	 * Evaluates a given json string.
    	 *
    	 * @param str {String}
    	 */
    	$.evalJSON = typeof JSON === 'object' && JSON.parse ? JSON.parse : function (str) {
    		/*jshint evil: true */
    		return eval('(' + str + ')');
    	};

    	/**
    	 * jQuery.secureEvalJSON
    	 * Evals JSON in a way that is *more* secure.
    	 *
    	 * @param str {String}
    	 */
    	$.secureEvalJSON = typeof JSON === 'object' && JSON.parse ? JSON.parse : function (str) {
    		var filtered =
    			str
    			.replace(/\\["\\\/bfnrtu]/g, '@')
    			.replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']')
    			.replace(/(?:^|:|,)(?:\s*\[)+/g, '');

    		if(/^[\],:{}\s]*$/.test(filtered)) {
    			/*jshint evil: true */
    			return eval('(' + str + ')');
    		}
    		throw new SyntaxError('Error parsing JSON, source is not valid.');
    	};

    	/**
    	 * jQuery.quoteString
    	 * Returns a string-repr of a string, escaping quotes intelligently.
    	 * Mostly a support function for toJSON.
    	 * Examples:
    	 * >>> jQuery.quoteString('apple')
    	 * "apple"
    	 *
    	 * >>> jQuery.quoteString('"Where are we going?", she asked.')
    	 * "\"Where are we going?\", she asked."
    	 */
    	$.quoteString = function (str) {
    		if(str.match(escape)) {
    			return '"' + str.replace(escape, function (a) {
    				var c = meta[a];
    				if(typeof c === 'string') {
    					return c;
    				}
    				c = a.charCodeAt();
    				return '\\u00' + Math.floor(c / 16).toString(16) + (c % 16).toString(16);
    			}) + '"';
    		}
    		return '"' + str + '"';
    	};

	})(jQuery);

	/*
    * @Author: zhijiang
    * @Date: 2018-05-07 16:40:48 
    * @Last Modified by: zhijiang
    * @Last Modified time: 2018-5-23 09:51:22
    */
	(function (jQuery) { 'use strict';
    	jQuery.format = (function () {
    		/**
    		 * 补零
    		 * @param {String | Number} val 要补零的数
    		 * @param {Number} len 补零后的长度
    		 * @return {String} 补零后的字符串
    		 */
    		function leadingZero (val, len) {
    			var zero = '';
    			var valLen, key;

    			val += '';
    			valLen = val.length;

    			if(valLen >= len) {
    			    return val;
    			}

    			while(len--) {
    			    zero += '0';
    			}

    			return (zero + val).substr(valLen);
    		}

    		function Duration (duration) {
    			var years = duration.years || 0;
    			var months = duration.months || 0;
    			var weeks = duration.weeks || 0;
    			var days = duration.days || 0;
    			var hours = duration.hours || 0;
    			var minutes = duration.minutes || 0;
    			var seconds = duration.seconds || 0;
    			var milliseconds = duration.milliseconds || 0;
    	
    			this.milliseconds = +milliseconds +
    					seconds * 1e3 + // 1000
    					minutes * 6e4 + // 1000 * 60
    					hours * 1000 * 60 * 60; // 使用 1000 * 60 * 60 而不使用 36e5 避免浮点舍入误差
    			this.days = +days + weeks * 7;
    			this.months = +months + years * 12;
    	
    			return this;
    		}
    	
    		function createDuration (val, key) {
    			var duration = val;
    	
    			if(typeof val === 'number') {
    				duration = {};
    				if(key) {
    					duration[key] = val;
    				}else{
    					duration.milliseconds = val;
    				}
    			}
    	
    			return new Duration(duration);
    		}

    		/**
    		 * 把月份转换成天数
    		 * @param  {Number} months 月份，可超过12个月
    		 * @return {Number} 月份对应的天数
    		 */
    		function monthsToDays (months) {
    			// 每 400 年有 146097 天(包含闰年)
    			// 400 年每年有 12 个月 所以 400 * 12 = 4800 (月)
    			// 得出式子
    			// 
    			//  months      4800
    			// -------- = --------
    			//   days      146097
    			//
    			// days = months * 146097 / 4800
    			return months * 146097 / 4800;
    		}

    		/**
    		 * 绝对向上取整
    		 * @param  {Number} number 要取整的数
    		 * @return {Number} 取整后的数
    		 * @example
    		 * absCeil(5.6); // 6
    		 * absCeil(-5.6); // -6
    		 */
    		function absCeil (number) {
    			if(number < 0) {
    		        return Math.floor(number);
    			}else{
    			    return Math.ceil(number);
    			}
    		}

    		/**
    		 * 对日期的加减法运算
    		 * @param {Date} date 要运算的日期
    		 * @param {Number | Object} val 要增加或减少的数，Object格式：{ key: Number }
    		 * @param {String} key 对年月日时分秒哪一位运算，可选值：[years|months|weeks|days|hours|minutes|seconds|milliseconds]
    		 * @param {String} dir 增加还是减少，可选值[1|-1]
    		 * @return {Date} 运算完后的日期
    		 */
    		function addSubtract (date, val, key, dir) {
    			var duration = createDuration(val, key);
    			var days = absCeil(monthsToDays(duration.months) + duration.days);
    			var milliseconds = duration.milliseconds + days * 864e5;
    			var timestamp = dir * (days + milliseconds);

    			return new Date(timestamp + date.getTime());
    		}

    		return {
    			date: function (date, format, notfillZero) {
    				if(!(date instanceof Date)) {
    					return date;
    				}
    				var key, result;
    				var regexDateObject = {
    					'y+': date.getFullYear(), // 年份
    					'M+': date.getMonth() + 1, // 月份
    					'd+': date.getDate(), // 日期
    					'H+': date.getHours(), // 小时
    					'm+': date.getMinutes(), // 分钟
    					's+': date.getSeconds(), // 秒
    					'S': date.getMilliseconds() // 毫秒
    				};

    				for(key in regexDateObject) {
    					format = format.replace(new RegExp('(' + key + ')'), function (match, keyMatch, index, input) {
    						result = regexDateObject[key];

    						if(!notfillZero) {
    							result = leadingZero(result, keyMatch.length);
    						}

    						return result;
    					});
    				}

    				return format;
    			},
    			addDate: function (date, key, val) {
    				return addSubtract(date, key, val, 1);
    			},
    			subDate: function (date, key, val) {
    				return addSubtract(date, key, val, -1);
    			}
    		};
    	})();
	})(jQuery);


	/*
     * jQuery MD5 Plugin 1.2.1
     * https://github.com/blueimp/jQuery-MD5
     *
     * Copyright 2010, Sebastian Tschan
     * https://blueimp.net
     *
     * Licensed under the MIT license:
     * http://creativecommons.org/licenses/MIT/
     * 
     * Based on
     * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message
     * Digest Algorithm, as defined in RFC 1321.
     * Version 2.2 Copyright (C) Paul Johnston 1999 - 2009
     * Other contributors: Greg Holt, Andrew Kepert, Ydnar, Lostinet
     * Distributed under the BSD License
     * See http://pajhome.org.uk/crypt/md5 for more info.
     */

	/*jslint bitwise: true */
	/*global unescape, jQuery */

	(function ($) {
		'use strict';

		/*
        * Add integers, wrapping at 2^32. This uses 16-bit operations internally
        * to work around bugs in some JS interpreters.
        */
		function safe_add(x, y) {
			var lsw = (x & 0xFFFF) + (y & 0xFFFF),
				msw = (x >> 16) + (y >> 16) + (lsw >> 16);
			return (msw << 16) | (lsw & 0xFFFF);
		}

		/*
        * Bitwise rotate a 32-bit number to the left.
        */
		function bit_rol(num, cnt) {
			return (num << cnt) | (num >>> (32 - cnt));
		}

		/*
        * These functions implement the four basic operations the algorithm uses.
        */
		function md5_cmn(q, a, b, x, s, t) {
			return safe_add(bit_rol(safe_add(safe_add(a, q), safe_add(x, t)), s), b);
		}
		function md5_ff(a, b, c, d, x, s, t) {
			return md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);
		}
		function md5_gg(a, b, c, d, x, s, t) {
			return md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);
		}
		function md5_hh(a, b, c, d, x, s, t) {
			return md5_cmn(b ^ c ^ d, a, b, x, s, t);
		}
		function md5_ii(a, b, c, d, x, s, t) {
			return md5_cmn(c ^ (b | (~d)), a, b, x, s, t);
		}

		/*
        * Calculate the MD5 of an array of little-endian words, and a bit length.
        */
		function binl_md5(x, len) {
			/* append padding */
			x[len >> 5] |= 0x80 << ((len) % 32);
			x[(((len + 64) >>> 9) << 4) + 14] = len;

			var i, olda, oldb, oldc, oldd,
				a = 1732584193,
				b = -271733879,
				c = -1732584194,
				d = 271733878;

			for(i = 0; i < x.length; i += 16) {
				olda = a;
				oldb = b;
				oldc = c;
				oldd = d;

				a = md5_ff(a, b, c, d, x[i], 7, -680876936);
				d = md5_ff(d, a, b, c, x[i + 1], 12, -389564586);
				c = md5_ff(c, d, a, b, x[i + 2], 17, 606105819);
				b = md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);
				a = md5_ff(a, b, c, d, x[i + 4], 7, -176418897);
				d = md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);
				c = md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);
				b = md5_ff(b, c, d, a, x[i + 7], 22, -45705983);
				a = md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);
				d = md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);
				c = md5_ff(c, d, a, b, x[i + 10], 17, -42063);
				b = md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);
				a = md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);
				d = md5_ff(d, a, b, c, x[i + 13], 12, -40341101);
				c = md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);
				b = md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);

				a = md5_gg(a, b, c, d, x[i + 1], 5, -165796510);
				d = md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);
				c = md5_gg(c, d, a, b, x[i + 11], 14, 643717713);
				b = md5_gg(b, c, d, a, x[i], 20, -373897302);
				a = md5_gg(a, b, c, d, x[i + 5], 5, -701558691);
				d = md5_gg(d, a, b, c, x[i + 10], 9, 38016083);
				c = md5_gg(c, d, a, b, x[i + 15], 14, -660478335);
				b = md5_gg(b, c, d, a, x[i + 4], 20, -405537848);
				a = md5_gg(a, b, c, d, x[i + 9], 5, 568446438);
				d = md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);
				c = md5_gg(c, d, a, b, x[i + 3], 14, -187363961);
				b = md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);
				a = md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);
				d = md5_gg(d, a, b, c, x[i + 2], 9, -51403784);
				c = md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);
				b = md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);

				a = md5_hh(a, b, c, d, x[i + 5], 4, -378558);
				d = md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);
				c = md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);
				b = md5_hh(b, c, d, a, x[i + 14], 23, -35309556);
				a = md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);
				d = md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);
				c = md5_hh(c, d, a, b, x[i + 7], 16, -155497632);
				b = md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);
				a = md5_hh(a, b, c, d, x[i + 13], 4, 681279174);
				d = md5_hh(d, a, b, c, x[i], 11, -358537222);
				c = md5_hh(c, d, a, b, x[i + 3], 16, -722521979);
				b = md5_hh(b, c, d, a, x[i + 6], 23, 76029189);
				a = md5_hh(a, b, c, d, x[i + 9], 4, -640364487);
				d = md5_hh(d, a, b, c, x[i + 12], 11, -421815835);
				c = md5_hh(c, d, a, b, x[i + 15], 16, 530742520);
				b = md5_hh(b, c, d, a, x[i + 2], 23, -995338651);

				a = md5_ii(a, b, c, d, x[i], 6, -198630844);
				d = md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);
				c = md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);
				b = md5_ii(b, c, d, a, x[i + 5], 21, -57434055);
				a = md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);
				d = md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);
				c = md5_ii(c, d, a, b, x[i + 10], 15, -1051523);
				b = md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);
				a = md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);
				d = md5_ii(d, a, b, c, x[i + 15], 10, -30611744);
				c = md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);
				b = md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);
				a = md5_ii(a, b, c, d, x[i + 4], 6, -145523070);
				d = md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);
				c = md5_ii(c, d, a, b, x[i + 2], 15, 718787259);
				b = md5_ii(b, c, d, a, x[i + 9], 21, -343485551);

				a = safe_add(a, olda);
				b = safe_add(b, oldb);
				c = safe_add(c, oldc);
				d = safe_add(d, oldd);
			}
			return [a, b, c, d];
		}

		/*
        * Convert an array of little-endian words to a string
        */
		function binl2rstr(input) {
			var i,
				output = '';
			for(i = 0; i < input.length * 32; i += 8) {
				output += String.fromCharCode((input[i >> 5] >>> (i % 32)) & 0xFF);
			}
			return output;
		}

		/*
        * Convert a raw string to an array of little-endian words
        * Characters >255 have their high-byte silently ignored.
        */
		function rstr2binl(input) {
			var i,
				output = [];
			output[(input.length >> 2) - 1] = undefined;
			for(i = 0; i < output.length; i += 1) {
				output[i] = 0;
			}
			for(i = 0; i < input.length * 8; i += 8) {
				output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << (i % 32);
			}
			return output;
		}

		/*
        * Calculate the MD5 of a raw string
        */
		function rstr_md5(s) {
			return binl2rstr(binl_md5(rstr2binl(s), s.length * 8));
		}

		/*
        * Calculate the HMAC-MD5, of a key and some data (raw strings)
        */
		function rstr_hmac_md5(key, data) {
			var i,
				bkey = rstr2binl(key),
				ipad = [],
				opad = [],
				hash;
			ipad[15] = opad[15] = undefined;                        
			if(bkey.length > 16) {
				bkey = binl_md5(bkey, key.length * 8);
			}
			for(i = 0; i < 16; i += 1) {
				ipad[i] = bkey[i] ^ 0x36363636;
				opad[i] = bkey[i] ^ 0x5C5C5C5C;
			}
			hash = binl_md5(ipad.concat(rstr2binl(data)), 512 + data.length * 8);
			return binl2rstr(binl_md5(opad.concat(hash), 512 + 128));
		}

		/*
        * Convert a raw string to a hex string
        */
		function rstr2hex(input) {
			var hex_tab = '0123456789abcdef',
				output = '',
				x,
				i;
			for(i = 0; i < input.length; i += 1) {
				x = input.charCodeAt(i);
				output += hex_tab.charAt((x >>> 4) & 0x0F) +
                    hex_tab.charAt(x & 0x0F);
			}
			return output;
		}

		/*
        * Encode a string as utf-8
        */
		function str2rstr_utf8(input) {
			return unescape(encodeURIComponent(input));
		}

		/*
        * Take string arguments and return either raw or hex encoded strings
        */
		function raw_md5(s) {
			return rstr_md5(str2rstr_utf8(s));
		}
		function hex_md5(s) {
			return rstr2hex(raw_md5(s));
		}
		function raw_hmac_md5(k, d) {
			return rstr_hmac_md5(str2rstr_utf8(k), str2rstr_utf8(d));
		}
		function hex_hmac_md5(k, d) {
			return rstr2hex(raw_hmac_md5(k, d));
		}
        
		$.md5 = function (string, key, raw) {
			if(!key) {
				if(!raw) {
					return hex_md5(string);
				}else{
					return raw_md5(string);
				}
			}
			if(!raw) {
				return hex_hmac_md5(key, string);
			}else{
				return raw_hmac_md5(key, string);
			}
		};
        
	})(typeof jQuery === 'function' ? jQuery : this);


	/**
     * jQuery open Url
     * <AUTHOR>
     * @Open
     * 		String $.openURL(String url, String target, String extendOptions)
     */
	(function($){
    	$.openURL = function(url, target, extendOptions){
    		if(target){
    			window.open(url, target, extendOptions);
    		}else{
    			window.open(url);
    		}
    	};
	})(jQuery);


	/**
     * jQuery jump Url
     * <AUTHOR>
     * @Open
     * 		String $.jumpURL(String url)
     */
	(function($){
    	$.jumpURL = function(url){
    		window.location.href = url;
    	};
	})(jQuery);


	/*
     * jQuery Browser Plugin v0.0.2
     * https://github.com/gabceb/jquery-browser-plugin
     * 重写jQuery默认的$.browser方法，以支持更多的浏览器及新版浏览器
     *
     * Date: 2013-07-29T17:23:27-07:00
     */

	(function( jQuery, window, undefined ) {
    	'use strict';
    	
    	var matched, browser;
    	
    	jQuery.uaMatch = function( ua ) {
    		ua = ua.toLowerCase();
    		
    		var match = /(opr)[\/]([\w.]+)/.exec( ua ) ||
    			/(chrome)[ \/]([\w.]+)/.exec( ua ) ||
    			/(webkit)[ \/]([\w.]+)/.exec( ua ) ||
    			/(opera)(?:.*version|)[ \/]([\w.]+)/.exec( ua ) ||
    			/(msie) ([\w.]+)/.exec( ua ) ||
    			ua.indexOf('trident') >= 0 && /(rv)(?::| )([\w.]+)/.exec( ua ) ||
    			ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\w.]+)|)/.exec( ua ) ||
    			[];
    		
    		var platform_match = /(ipad)/.exec( ua ) ||
    			/(iphone)/.exec( ua ) ||
    			/(android)/.exec( ua ) ||
    			[];
    		
    		return {
    			browser: match[1] || '',
    			version: match[2] || '0',
    			platform: platform_match[0] || ''
    		};
    	};
    	
    	matched = jQuery.uaMatch( window.navigator.userAgent );
    	browser = {};
    	
    	if( matched.browser ) {
    		browser[matched.browser] = true;
    		browser.version = matched.version;
    	}
    	
    	if( matched.platform) {
    		browser[matched.platform] = true;
    	}
    	
    	// Chrome and Opera 15+ are Webkit, but Webkit is also Safari.
    	if( browser.chrome || browser.opr) {
    		browser.webkit = true;
    	}else if( browser.webkit ) {
    		browser.safari = true;
    	}
    	
    	// IE11 has a new token so we will assign it msie to avoid breaking changes
    	if(browser.rv) {
    		browser.msie = true;
    	}
    	
    	// Opera 15+ are identified as opr
    	if(browser.opr) {
    		browser.opera = true;
    	}
    	
    	jQuery.browser = browser;

	})( jQuery, window );

})(hdFai);