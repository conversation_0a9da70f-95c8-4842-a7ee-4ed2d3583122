<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" session="false"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.hdUtil.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="java.util.*"%>
<%@ page import="java.net.HttpURLConnection" %>
<%@ page import="java.net.Proxy" %>
<%@ page import="java.net.InetSocketAddress" %>
<%@ page import="fai.web.FaiTradeStationBaseApi" %>

<%!
    public String getJdGiftList(HttpServletRequest request) throws Exception{
        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);
        int page = Parser.parseInt(request.getParameter("page"), 1);
        int limit = Parser.parseInt(request.getParameter("limit"), 10);

        SearchArg searchArg = new SearchArg();
        searchArg.start = (page - 1) * limit;
        searchArg.limit = limit;
        searchArg.totalSize = new Ref<Integer>();

        FaiList<String> fields = new FaiList<String>();
        fields.add(HdJdGiftDef.Info.TYPE_B);
        fields.add(HdJdGiftDef.Info.SKU);
        fields.add(HdJdGiftDef.Info.JD_NAME);
        fields.add(HdJdGiftDef.Info.FK_NAME);
        fields.add(HdJdGiftDef.Info.PRODUCT_ID);
        fields.add(HdJdGiftDef.Info.OEM_PRODUCT_ID);
        fields.add(HdJdGiftDef.Info.CREATE_TIME);
        fields.add(HdJdGiftDef.Info.SELL_TIME);
        fields.add(HdJdGiftDef.Info.IMAGE_PATH);
        fields.add(HdJdGiftDef.Info.JD_PRICE);
        fields.add(HdJdGiftDef.Info.ORIGINAL_PRICE);
        fields.add(HdJdGiftDef.Info.PRICE);
        fields.add(HdJdGiftDef.Info.FLAG);
        fields.add(HdJdGiftDef.Info.CREATE_TIME);
        fields.add(HdJdGiftDef.Info.SELL_TIME);

        FaiList<Param> list = hdGift.getJdGiftList(searchArg, fields, 0, false);
        if(list == null){
            list = new FaiList<Param>();
            searchArg.totalSize.value = 0;
        }else{
            for(Param item : list){
                if(!item.containsKey(HdJdGiftDef.Info.SELL_TIME)){
                    item.setCalendar(HdJdGiftDef.Info.SELL_TIME, Parser.parseCalendar("1997-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"));
                }
            }
        }

        SearchArg sellSearchArg = new SearchArg();
        sellSearchArg.matcher = new ParamMatcher(HdJdGiftDef.Info.FLAG, ParamMatcher.LAND, HdJdGiftDef.Flag.FK_SALE, HdJdGiftDef.Flag.FK_SALE);
        sellSearchArg.limit = 1;
        sellSearchArg.totalSize = new Ref<Integer>(0);
        hdGift.getJdGiftList(sellSearchArg, fields, 0, false);
        int onSellAmount = sellSearchArg.totalSize.value;

        return new HdGameDef.ErrorInfo(Errno.OK).add("list", list).add("total", searchArg.totalSize.value).add("onSellAmount", onSellAmount).toString();
    }

    public String getJdGift(HttpServletRequest request) throws Exception{
        int typeB = Parser.parseInt(request.getParameter("typeB"), 0);
        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);

        FaiTradeStationBaseApi faiTradeStationBaseApi = new FaiTradeStationBaseApi();
        Param info = hdGift.getJdGift(typeB);
        if(Str.isEmpty(info)){
            return new HdGameDef.ErrorInfo(Errno.ERROR).toString();
        }
        int productId = info.getInt(HdJdGiftDef.Info.PRODUCT_ID, 0);
        if(productId != 0 ){
            Param productInfo = faiTradeStationBaseApi.getProductInfo(productId);
            if (productInfo != null) {
                double realPrice = faiTradeStationBaseApi.getPrice(productId, 1);
                info.setDouble("realPrice", realPrice);
            }
            /*FaiProductDef.IProduct product = FaiProductDef.getProduct(productId);
            if(product != null){
                double realPrice = product.getPrice(1);
                info.setDouble("realPrice", realPrice);
            }*/
        }

        return new HdGameDef.ErrorInfo(Errno.OK).add("info", info).toString();
    }

    public String getJdGiftByJd(HttpServletRequest request) throws Exception{
        String sku = request.getParameter("sku");
        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);

        Param info = hdGift.getJdGiftByJD(sku);
        if(Str.isEmpty(info)){
            return new HdGameDef.ErrorInfo(Errno.ERROR).toString();
        }
        return new HdGameDef.ErrorInfo(Errno.OK).add("info", info).toString();
    }

    public String addJdGift(HttpServletRequest request) throws Exception{
        int rt = Errno.OK;
        String infoStr = request.getParameter("info");
        Param recvInfo = Param.parseParam(infoStr);

        Param info = new Param();
        info.assign(recvInfo, HdJdGiftDef.Info.TYPE_B);
        info.assign(recvInfo, HdJdGiftDef.Info.SKU);
        info.assign(recvInfo, HdJdGiftDef.Info.PRODUCT_ID);
        info.assign(recvInfo, HdJdGiftDef.Info.OEM_PRODUCT_ID);
        info.assign(recvInfo, HdJdGiftDef.Info.JD_NAME);
        info.assign(recvInfo, HdJdGiftDef.Info.FK_NAME);
        info.assign(recvInfo, HdJdGiftDef.Info.IMAGE_PATH);
        info.assign(recvInfo, HdJdGiftDef.Info.SALE_UNIT);
        info.assign(recvInfo, HdJdGiftDef.Info.PARAM);
        info.assign(recvInfo, HdJdGiftDef.Info.BRAND_NAME);
        info.assign(recvInfo, HdJdGiftDef.Info.SHOU_HOU);
        info.assign(recvInfo, HdJdGiftDef.Info.INTRODUCTION);
        info.assign(recvInfo, HdJdGiftDef.Info.NAPPINTRODUCTION);
        info.assign(recvInfo, HdJdGiftDef.Info.NINTRODUCTION);
        info.assign(recvInfo, HdJdGiftDef.Info.WXINTRODUCTION);
        info.assign(recvInfo, HdJdGiftDef.Info.JD_PRICE);
        info.assign(recvInfo, HdJdGiftDef.Info.ORIGINAL_PRICE);
        info.assign(recvInfo, HdJdGiftDef.Info.PRICE);
        info.assign(recvInfo, HdJdGiftDef.Info.FLAG);
        info.setCalendar(HdJdGiftDef.Info.CREATE_TIME, Calendar.getInstance());

        int flag = info.getInt(HdJdGiftDef.Info.FLAG, 0);
        if(Misc.checkBit(flag, HdJdGiftDef.Flag.FK_SALE)){
            if(!info.containsKey(HdJdGiftDef.Info.PRODUCT_ID) || !info.containsKey(HdJdGiftDef.Info.OEM_PRODUCT_ID)){
                rt = Errno.ARGS_ERROR;
                Log.logErr(rt, "sale with out productId or oemProductId; info=%s", info);
                return new HdGameDef.ErrorInfo(rt).add("msg", "必须填写凡科订单id才能上架").toString();
            }
        }

        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);

        rt = hdGift.addJdGift(info);
        if(rt != Errno.OK){
            return new HdGameDef.ErrorInfo(rt).add("msg", "系统繁忙，请稍后再试").toString();
        }
        return new HdGameDef.ErrorInfo(rt).toString();
    }

    public String setJdGift(HttpServletRequest request) throws Exception{
        int rt = Errno.OK;
        int typeB = Parser.parseInt(request.getParameter("typeB"), 0);
        String infoStr = request.getParameter("info");
        Param recvInfo = Param.parseParam(infoStr);

        Param update = new Param();
        update.assign(recvInfo, HdJdGiftDef.Info.TYPE_B);
        update.assign(recvInfo, HdJdGiftDef.Info.FK_NAME);
        update.assign(recvInfo, HdJdGiftDef.Info.ORIGINAL_PRICE);
        update.assign(recvInfo, HdJdGiftDef.Info.PRICE);
        update.assign(recvInfo, HdJdGiftDef.Info.PRODUCT_ID);
        update.assign(recvInfo, HdJdGiftDef.Info.OEM_PRODUCT_ID);
        update.assign(recvInfo, HdJdGiftDef.Info.FLAG);

        int flag = update.getInt(HdJdGiftDef.Info.FLAG, 0);
        if(Misc.checkBit(flag, HdJdGiftDef.Flag.FK_SALE)){
            if(!update.containsKey(HdJdGiftDef.Info.PRODUCT_ID) || !update.containsKey(HdJdGiftDef.Info.OEM_PRODUCT_ID)){
                rt = Errno.ARGS_ERROR;
                Log.logErr(rt, "sale with out productId or oemProductId; info=%s", update);
                return new HdGameDef.ErrorInfo(rt).add("msg", "必须填写凡科订单id才能上架").toString();
            }else{
                update.setCalendar(HdJdGiftDef.Info.SELL_TIME, Calendar.getInstance());
            }
        }

        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);
        ParamUpdater updater = new ParamUpdater(update);

        Ref<Integer> typeBRef = new Ref<Integer>();
        rt = hdGift.setJdGift(typeB, updater);
        if(rt != Errno.OK){
            return new HdGameDef.ErrorInfo(rt).add("msg", "系统繁忙，请稍后再试").toString();
        }
        return new HdGameDef.ErrorInfo(rt).add("typeB", typeBRef.value).toString();
    }

    public String delJdGift(HttpServletRequest request) throws Exception{
        int rt = Errno.OK;
        int typeB = Parser.parseInt(request.getParameter("typeB"), 0);
        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);

        Param info = hdGift.getJdGift(typeB);
        int productId = info.getInt(HdJdGiftDef.Info.PRODUCT_ID, 0);
        int oemProductId = info.getInt(HdJdGiftDef.Info.OEM_PRODUCT_ID, 0);
        if(productId != 0 || oemProductId != 0){
            rt = Errno.ERROR;
            return new HdGameDef.ErrorInfo(rt).add("msg", "关联运营商品id的京东礼品不允许删除").toString();
        }

        rt = hdGift.delJdGift(typeB);
        if(rt != Errno.OK){
            return new HdGameDef.ErrorInfo(rt).add("msg", "系统繁忙，请稍后再试").toString();
        }
        return new HdGameDef.ErrorInfo(rt).toString();
    }

    private String getRemain(HttpServletRequest request) throws Exception{
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");

        int page = Parser.parseInt(request.getParameter("page"), 1);
        int limit = Parser.parseInt(request.getParameter("limit"), 10);

        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);
        String jdToken = hdGift.getJdToken();

        Param res1 = getBalance(jdToken);
        if(!res1.getBoolean("success")){
            return new HdGameDef.ErrorInfo(Errno.ERROR).add("msg", "系统繁忙，请稍后再试").toString();
        }
        Param result1 = res1.getParam("result");
        Param balance = result1.getParam("balance");
        double remainMoney = balance.getDouble("remainLimit");


        Param res2 = getBalanceDetail(jdToken, page, limit, startDate, endDate);
        if(!res2.getBoolean("success")){
            return new HdGameDef.ErrorInfo(Errno.ERROR).add("msg", "系统繁忙，请稍后再试").toString();
        }
        Param result2 = res2.getParam("result");
        FaiList<Param> list = result2.getList("data");
        int total = result2.getInt("total");

        return new HdGameDef.ErrorInfo(Errno.OK).add("list", list).add("total", total).add("remainMoney", remainMoney).toString();
    }

    private String exportAccountDetail(HttpServletRequest request, HttpServletResponse response, JspWriter out) throws Exception{
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");

        int page = 1;
        int limit = 1000;

        HdGift hdGift = (HdGift) WebHdOss.getCorpKit(Kid.HD_GIFT);
        String jdToken = hdGift.getJdToken();

        int total = 0;
        FaiList<Param> list = new FaiList<Param>();
        do{
            Param res = getBalanceDetail(jdToken, page, limit, startDate, endDate);
            if(!res.getBoolean("success")){
                return new HdGameDef.ErrorInfo(Errno.ERROR).add("msg", "系统繁忙，请稍后再试").toString();
            }
            Param result = res.getParam("result");
            FaiList<Param> tmpList = result.getList("data");

            page++;
            list.addAll(tmpList);
            total = result.getInt("total");
        }while (list.size() < total);

        FaiList<Param> colList = new FaiList<Param>();
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "id");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "amount");
            k.setInt(MSOfficeConverter.Col.WIDTH, 10);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "pin");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "orderId");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "tradeType");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "tradeTypeName");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "createdDate");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "notePub");
            k.setInt(MSOfficeConverter.Col.WIDTH, 40);
            colList.add(k);
        }
        {
            Param k = new Param();
            k.setString(MSOfficeConverter.Col.KEY, "tradeNo");
            k.setInt(MSOfficeConverter.Col.WIDTH, 20);
            colList.add(k);
        }
        FaiList<Param> rowList = new FaiList<Param>();                                          //要导出Excel文件的列头信息 --  第一行
        Param p = new Param();
        p.setString("id", "余额明细ID");
        p.setString("amount", "金额（元）");
        p.setString("pin", "京东Pin");
        p.setString("orderId", "订单号");
        p.setString("tradeType", "业务类型");
        p.setString("tradeTypeName", "业务类型名称");
        p.setString("createdDate", "余额变动日期");
        p.setString("notePub", "备注信息");
        p.setString("tradeNo", "tradeNo");
        rowList.add(p);
        rowList.addAll(list);
        FaiList<Param> optionList = new FaiList<Param>();
        Param option = null;
        int outputCount = rowList.size();
        if (outputCount <= 65535) {                                                                                        //65536是office2003 sheet的行上限
            option = new Param();
            option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet");
            option.setList(MSOfficeConverter.Option.COL_LIST, colList);
            option.setList(MSOfficeConverter.Option.ROW_LIST, rowList);
            optionList.add(option);
        } else {
            //xls一张sheet只能存储65536条数据，因此超出分成多张sheet
            for (int i = 1; i <= outputCount; i = i + 65535) {
                option = new Param();
                int indexCount = (i + 65535) >= outputCount ? outputCount : i + 65535;
                option.setString(MSOfficeConverter.Option.SHEET_NAME, "Sheet" + i + "~" + (indexCount - 1));
                option.setList(MSOfficeConverter.Option.COL_LIST, colList);
                FaiList<Param> sheetData = new FaiList<Param>(rowList.subList(i, indexCount));
                sheetData.add(0, p);                                                                                       //在每个工作表首行加上列名字段
                option.setList(MSOfficeConverter.Option.ROW_LIST, sheetData);
                optionList.add(option);
            }
        }
        String fileName = "导出数据.xls";
        dealBeforeConvert( request,response,out,fileName, optionList );
        return "suc";
    }

    private Param getBalance(String jdToken){
        String url = "https://bizapi.jd.com/api/price/getUnionBalance";
        Param data = new Param();
        data.setString("token", jdToken);
        data.setString("pin", HdAssetDef.JD_USER);
        data.setString("type", "1");

        FaiUrlConnection.SocketCallbackAdaptor apiCb = new HdGiftAffairCallback("HdGiftAffairProc checkByJD");
        apiCb.setRequestMethod(FaiUrlConnection.URL_CONNECTION_POST);
        apiCb.setReadTimeout(3000);
        Param res = Param.parseParam(FaiUrlConnection.getHttpContent(url, HdTool.getPairFromParam(data), apiCb));

        return res;
    }

    private Param getBalanceDetail(String jdToken, int page, int limit, String startDate, String endDate){
        String url = "https://bizapi.jd.com/api/price/getBalanceDetail";
        Param data = new Param();
        data.setString("token", jdToken);
        data.setInt("pageNum", page);
        data.setInt("pageSize", limit);
        if(!Str.isEmpty(startDate)){
            data.setString("startDate", startDate);
        }
        if(!Str.isEmpty(endDate)){
            data.setString("endDate", endDate);
        }

        FaiUrlConnection.SocketCallbackAdaptor apiCb = new HdGiftAffairCallback("HdGiftAffairProc checkByJD");
        apiCb.setRequestMethod(FaiUrlConnection.URL_CONNECTION_POST);
        apiCb.setReadTimeout(3000);
        Param res = Param.parseParam(FaiUrlConnection.getHttpContent(url, HdTool.getPairFromParam(data), apiCb));

        return res;
    }

    private static void dealBeforeConvert ( HttpServletRequest request, HttpServletResponse response, JspWriter out , String fileName , FaiList<Param> optionList )throws Exception{
        out.clear();
        String agent = request.getHeader("User-Agent");
        agent = agent == null ? "" : agent.toUpperCase();
        response.setHeader("Content-Disposition", "attachment;filename=\"" + Encoder.encodeAttFileName(agent,fileName) + "\"");
        response.setContentType("application/vnd.ms-excel");
        MSOfficeConverter.excelConverter(response.getOutputStream(),optionList);
        //out.close();               //resin4 jsp页面的out close后jsp页面不能再输出内容。所以只clear好了
    }


    private static class HdGiftAffairCallback extends FaiUrlConnection.SocketCallbackFai {
        public HdGiftAffairCallback(String business) {
            super(business);
        }

        @Override
        public String getRequestMethod(HttpURLConnection uc) {
            return FaiUrlConnection.URL_CONNECTION_POST;
        }

        @Override
        public int getConnectTimeout() {
            return 2000;
        }

        @Override
        public int getReadTimeout() {
            return 3000;
        }

        @Override
        public Proxy getProxy() {
            Proxy proxy = null;
            Param proxyInfo = ProxyPool.getVps("hdWxProxy");
            if (proxyInfo != null) {
                String proxyIp = proxyInfo.getString(ProxyPool.Info.IP);
                int proxyPort = proxyInfo.getInt(ProxyPool.Info.PORT);
                InetSocketAddress proxyAddr = new InetSocketAddress(proxyIp, proxyPort);
                proxy = new Proxy(Proxy.Type.SOCKS, proxyAddr);
            }
            return proxy;
        }
    }
%>

<%
    String output = "";

    try {
        String cmd = request.getParameter("cmd");
        if (cmd == null) {
            output = "no cmd find";
        } else if (cmd.equals("getJdGiftList")) {
            output = getJdGiftList(request);
        }else if (cmd.equals("getJdGift")) {
            output = getJdGift(request);
        }else if (cmd.equals("getJdGiftByJd")) {
            output = getJdGiftByJd(request);
        }else if (cmd.equals("addJdGift")) {
            output = addJdGift(request);
        }else if (cmd.equals("setJdGift")) {
            output = setJdGift(request);
        }else if (cmd.equals("delJdGift")) {
            output = delJdGift(request);
        }else if(cmd.equals("getRemain")){
            output = getRemain(request);
        }else if(cmd.equals("exportAccountDetail")){
            output = exportAccountDetail(request, response, out);
        }
        //TODO
       /*  else if (cmd.equals("getNewAndOldList")) {
            output = getNewAndOldList(request);
        }  */
        else {
            output = "no cmd find";
        }


    } catch (Exception exp) {
        PreSaleHdDef.printErr(exp);
        output = WebOss.checkAjaxException(exp);
    }

    out.print(output);

%>