package fai.webhdoss.model.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
@ApiModel("o系统活动列表VO")
public class HdActivityListVO {

    /** 模板分类  节日 */
    int key1 = -1;

    /**
     * 模板分类
     */
    int key2 = -1;

    /**
     * 模板分类
     */
    int key3 = -1;

    /**
     * 客户行业
     */
    int acctTrade = -1;

    int aid = 0;

    int agentAid = 0;

    int pageNo = 1;

    int pageLimit = 100;

    /**
     * 游戏类型
     */
    int gstyle = -1;

    /**
     * 游戏状态
     */
    int gstatus = -1;

    /** 游戏版本 */
    int gbuy = -1;

    /** 游戏平台 */
    int gplatform = -1;

    /** 浏览人数 */
    int view = 0;

    /** 玩家人数 */
    int play = 0;

    /** 玩家小于人数 */
    int play1 = 0;

    /** 分享人数 */
    int share = 0;

    /** 举报人数 */
    int inform = 0;

    /** 游戏id */
    int gameId = 0;

    /** 筛选时间类别 */
    int timetype = 0;

    /** 游戏版本 */
    int openPurpose = -1;

    /** 首页图片替换量 */
    int indexReplaceNum = 0;

    /** 其他图片替换量 */
    int otherReplaceNum = 0;

    /** 销售成交 */
    int saleCover = -1;

    /** 属于哪里创建的游戏 */
    int createType = 0;

    @NotNull(message = "begDate is null")
    String begDate;

    @NotNull(message = "endDate is null")
    String endDate;

    @NotNull(message = "begPay is null")
    String begPay;

    @NotNull(message = "endPay is null")
    String endPay;

    int trade =0;

    int trade2 = 0;

    int category1 = 0;

    int category2 = 0;

    String searchWord = "*";
}
