<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>

<!DOCTYPE html>
<html>
<head>
	<title>互动案例库</title>
	<%@ page import="fai.web.inf.*"%>
	<%@ page import="fai.comm.util.*"%>
	<%=Web.getToken()%>
	<%
		int sid = Session.getSid();
		boolean authMktDepartment = Auth.checkFaiscoAuth("authMktDepartment", false) || sid == 949 || sid == 1609; //营销业务部

		//电销一部aid
		Integer[] salesDepartmentOneArr = new Integer[]{119,702,1017,1269,1240,1715,2501,2516,1096,1273,1384,1975,1538,719,2582,398,831,163,347,1677,226,
				314,763,2315,188,1948,1998,417,2098,793,1184,2029,1797,1667,194,2354,1941,1811,1536,2594,1778};
		//判断该sid是不是电销一部
		//boolean salesDepartmentOneAuth = Arrays.stream(salesDepartmentOneArr).anyMatch(i->sid==i);
		boolean salesDepartmentOneAuth = false;
		for (Integer integer : salesDepartmentOneArr) {
			if (sid == integer){
				salesDepartmentOneAuth = true;
				break;
			}
		}

		boolean authSalesCenterAdm = Auth.checkFaiscoAuth("authSalesCenterAdm", false) || salesDepartmentOneAuth; //销售中心管理员
		Log.logDbg("liqin d=%s,s=%s",authMktDepartment, authSalesCenterAdm);
	%>
	<%=FrontEndDef.getJSsdkComponentScript(9000)%>
	<%=FrontEndDef.getPackageCss("fa-component", "~1.1.0")%>
	<style>
		/*去除input number 类型时的默认样式 */
	  	input[type=number]::-webkit-inner-spin-button,
		input[type=number]::-webkit-outer-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}

		input[type="number"]{
			-moz-appearance: textfield;
		}
		/*---------------------------*/

		.block {
			display: block;
		}
		.inlineBlock {
			display: inline-block;
		}
		.hide {
			display: none;
		}
		.flex {
			display: flex;
		}
		.flex .fa-form-item-children {
			display: flex;
		}
		.fa-btn-primary {
			margin-right: 10px;
		}
		.container {
			padding-bottom: 50px;
		}
		.container .form-item {
			position: relative;
			display: inline-block;
			width: 190px;
			height: 40px;
			margin-right: 10px;
			margin-bottom: 0;
			vertical-align: top;
		}

		.timePicker .fa-form-item-label {
			height: 30px;
		}
		
		.timePicker .fa-form-item-control-wrapper,
		.fa-btn-primary {
			/* margin-top: 3px; */
		}
		.container .copyLink {
			margin-top: 10px;
		}
		.container .copyLink .code {
			width: 0;
			height: 0;
			opacity: 0;
		}
		.container .copyLink .fa-btn {
			width: 100px;
		}
		.fa-table-body {
			overflow-y: hidden;
		}
		.activeTag ul {
			opacity: 0;
		}
		.activeTag .fa-select-selection--multiple{
			height: 0;
		}
		.activeTagLen {
			position: absolute;
			left: 10px;
			top: -3px;
			z-index: 10;
			height: 25px;
			line-height: 25px;
			padding: 0 20px 0 10px;
			overflow: hidden;
			color: rgba(0, 0, 0, 0.65);
			background-color: #fafafa;
			border: 1px solid #e8e8e8;
		}
		.fa-table-bordered .fa-table-thead > tr > th, .fa-table-bordered .fa-table-tbody > tr > td, .fa-btn-link {
			font-size: 12px;
		}
		.actvieFuncLink {
			padding-left: 0;
			margin-bottom: 10px;
		}
		#components-form-demo-validate-other .dropbox {
			height: 180px;
			line-height: 1.5;
		}
	</style>
</head>
<body>
  <div id="app" class="container">
    <fa-tabs default-activeKey="1" @change="changeActiveKey">
		<fa-tab-pane 
			v-for="(tab, index) in tabs"
			:tab="tab.name" 
			:key="tab.dataName"
		>
		
		<swiper-slide-form
			:classify="formData[tab.dataName]"
			:tabname="tab.dataName"
			:curkey="curkey"
		></swiper-slide-form>
		</fa-tab-pane>
    </fa-tabs>
  </div>

<script type="text/x-template" id="swiper-slide-form">
	<div class="classify">
		<div style="font-size: 16px;margin-bottom:5px">总共查询有{{totalSize}}条数据</div>
		<!-- 数据筛选 -->
		<fa-form :form="classify" style="margin-bottom: 8px;">
			<%-- 行业 --%>
			<div v-if="['caseFilterData', 'recordData'].indexOf(tabname) === -1">
				<fa-form-item class="form-item">
					<fa-select placeholder="行业" allow-clear @change="changeTrade" show-search >
						<fa-select-option :value="trade" v-for="trade in tradeList" :key="trade">{{trade}}</fa-select-option>
					</fa-select>
				</fa-form-item>
				<%-- 场景 --%>
				<fa-form-item class="form-item">
					<fa-select placeholder="场景" allow-clear @change="handleClickScene" show-search>
						<fa-select-option :value="scene" v-for="scene in sceneList" :key="scene">{{scene}}</fa-select-option>
					</fa-select>
				</fa-form-item>
				<%-- 原型 --%>
				<fa-form-item class="form-item" v-if="tabname == 'previewCaseData'">
					<fa-select placeholder="选择原型" allow-clear @change="selectPrototype" show-search>
						<fa-select-option :value="prototypeName" v-for="prototypeName in prototypeList" :key="prototypeName">{{prototypeName}}</fa-select-option>
					</fa-select>
				</fa-form-item>
				
				<%-- 活动分类 --%>
				<fa-form-item class="form-item">
					<fa-select
						v-model="activeTag"
						placeholder="请选择活动分类"
						option-label-prop="label"
						class="activeTag"
						style="width: 100%;height:30px"
						allow-clear
					>
						<fa-select-option :value="tag" :label="tag" v-for="tag in lableList" :key="tag">
							{{tag}}
						</fa-select-option>
					</fa-select>
				</fa-form-item>
				<%-- 活动功能 --%>
				<fa-form-item class="form-item" v-if="tabname != 'previewCaseData'">
					<fa-input placeholder="请输入活动功能" allow-clear v-model="activeFunc" />
				</fa-form-item>
				<%-- 关键词 --%>
				<fa-form-item class="form-item">
					<fa-input placeholder="请输入关键词" allow-clear v-model="keyWork" />
				</fa-form-item>
				<%-- 导入时间 --%>
				<fa-form-item class="form-item timePicker" style="width: 400px" v-if="tabname != 'previewCaseData'">
					<fa-form-item
						class="flex"
						label="导入时间"
						style="margin-right: 10px"
						>
						<fa-range-picker @change="getImportTime" />
					</fa-form-item>
				</fa-form-item>
				<fa-button type="primary" @click="getDataList">查询</fa-button>
				<form id="uploadFileForm" class="hide" enctype="multipart/form-data">
					<span class="btn">
						<input @change="uploadHdCaseList" ref="fileInput" type="file" class="uploadInput" name="ctrl" accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" />
					</span>
				</form>
				<fa-button type="primary" @click="handleClickUploadBtn" v-if="tabname == 'caseData' && (authMktDepartment || authSalesCenterAdm)">导入</fa-button>
				<fa-button type="primary" @click="handleClickTabs" v-if="tabname == 'caseData' && (authMktDepartment || authSalesCenterAdm)">导入记录</fa-button>
			</div>

			<div v-if="tabname === 'caseFilterData'">
				<fa-form-item class="form-item">
					<fa-input placeholder="请输入原账号id" type="number" allow-clear v-model="caseFilterFormData.oAid" />
				</fa-form-item>
				<fa-form-item class="form-item">
					<fa-input placeholder="请输入原账号活动id" type="number" allow-clear v-model="caseFilterFormData.oGameId" />
				</fa-form-item>
				<fa-form-item class="form-item">
					<fa-input placeholder="请输入案例账号活动id" type="number" allow-clear v-model="caseFilterFormData.gameId" />
				</fa-form-item>
				<fa-form-item class="form-item">
					<fa-input placeholder="请输入操作人" allow-clear v-model="caseFilterFormData.createPerson" />
				</fa-form-item>
				<%-- 复制时间 --%>
				<fa-form-item class="form-item timePicker" style="width: 400px">
					<fa-form-item
						class="flex"
						label="复制时间"
						style="margin-right: 10px"
						>
						<fa-range-picker @change="getCreateTime" />
					</fa-form-item>
				</fa-form-item>
				<fa-button type="primary" @click="getDataList">查询</fa-button>
			</div>
		</fa-form>
		<!-- 表格 -->
		<fa-button v-if="'caseFilterData' !== tabname" class="actvieFuncLink" type="link" @click="handleActvieFunc">查看活动功能表</fa-button>
		<fa-table :columns="columns" bordered :data-source="dataList" :rowKey="getRowKey" :scroll="tableScrollParam" :pagination="false">
			<span slot="number" slot-scope="number, record, index">{{(classify.page-1)*10 + index+1}}</span>
			<span slot="createTime" slot-scope="createTime">{{createTime | filterCreateTime}}</span>
			<span slot="isPutOnShelf" slot-scope="isPutOnShelf">{{isPutOnShelf | filterIsPutOnShelf}}</span>
			<div slot="companyLogoUrl" slot-scope="companyLogoUrl, record, index">
				<fa-popover placement="top" width="40">
					<template slot="content">
						<img v-bind:src="companyLogoUrl" />
					</template>
					<fa-button style="font-size: 12px;">查看LOGO</fa-button>
				</fa-popover>
			</div>
			<div slot="gameUrl" slot-scope="gameUrl, record, index">
				<fa-popover placement="top" width="200">
					<template slot="content">
						<img v-bind:src="gameUrl | filterGameUrl" />
					</template>
					<fa-button style="font-size: 12px;">查看二维码</fa-button>
				</fa-popover>
				<fa-popover placement="top" width="200">
					<template slot="content">
						<div id="code" class="code">{{gameUrl}}</div>
					</template>
					<fa-button style="font-size: 12px;">查看链接</fa-button>
				</fa-popover>
				<%-- <span class="copyLink">
					<div id="code" class="code">{{gameUrl}}</div>
					<div id="code" class="code">{{gameUrl}}</div>
					<fa-button
						:ref="'copy' + ((classify.page-1) * 10 + index)"
						 style="font-size: 12px;width: 90px"
						 class="copy-btn pointer"
						data-clipboard-action="copy"
						data-clipboard-target="#code"
						@click="copyCode(index)"
					>
						查看链接
					</fa-button>
				</span> --%>
			</div>
			// 原账号活动数据
			<div slot="activeData" slot-scope="activeData, record, index">
				<div>浏览人数 {{dataList[index].view}}</div>
				<div>参与人数 {{dataList[index].player}}</div>
				<div>分享人数 {{dataList[index].share}}</div>
			</div>
			// 案例账号活动数据
			<div slot="caseActiveData" slot-scope="text, record">
				<div>浏览人数 {{record.caseView}}</div>
				<div>参与人数 {{record.casePlayer}}</div>
				<div>分享人数 {{record.caseShare}}</div>
			</div>
			<div slot="action" slot-scope="id, record, index">
				<fa-button type="link" @click="setHdCaseCollect(index, id)">{{dataList[index].hasCollect ? '取消收藏' : '我要收藏'}}</fa-button>
				<fa-button type="link" @click="dataList[index].isPut ? putPreview(id, index, true) : toShowAddCaseModal(index)" :disabled="dataList[index].isPut">{{dataList[index].isPut ? '已放到预览窗' : '放到预览窗'}}</fa-button>
				<fa-popconfirm
					v-if="dataList.length && (authMktDepartment || authSalesCenterAdm)"
					title="确定删除该案例吗？"
					placement="topRight"
					@confirm="deleteHdCase(index, record)"
				>
					<template slot="okText">继续</template>
					<template slot="cancelText">取消</template>
					<fa-button type="link" style="margin-left: 13px;">删除</fa-button>
				</fa-popconfirm>
			</div>
			<div slot="previewCaseAction" slot-scope="id, record, index">
				<fa-button class="block" type="link" @click="upPreviewStatus(id, index)">{{dataList[index].isPutOnShelf ? '下架' : '上架'}}</fa-button>
				<fa-upload
				 	class="block"
					accept=".jpg, .jpeg, .png, .bmp, .gif"
					name="filedata"
					action="/ajax/advanceUpload.jsp?cmd=upload&maxWidth=10000&maxHeight=10000&imgMode=2"
                    :show-upload-list="false"
					:before-upload="beforeUploadImg"
					@change="reUploadLogo"
					@click.native="handleClickReUploadBtn(index)"
				>
					<fa-button type="link"> 上传LOGO </fa-button>
				</fa-upload>
				<fa-popconfirm
					v-if="dataList.length && (authMktDepartment || authSalesCenterAdm)"
					title="确定删除吗？"
					placement="topRight"
					@confirm="putPreview(id, index, false)"
				>
					<template slot="okText">继续</template>
					<template slot="cancelText">取消</template>
					<fa-button type="link" class="block">删除</fa-button>
				</fa-popconfirm>
			</div>

			<div slot="caseFilterAction" slot-scope="id, record, index">
				<fa-button type="link" class="block" @click="toShowEditCaseModal(index)">编辑</fa-button>
				<fa-button type="link" class="block" @click="showDeleteConfirm(deleteCaseFilter)(id)">删除</fa-button>
				<fa-button type="link" class="block" @click="showConfirm(importCaseFilter)(id)">导入案例库</fa-button>
			</div>
		</fa-table>
		<!-- 分页 -->
		<fa-pagination show-quick-jumper :default-current="1" :total="totalSize" @change="paginationChange" />
		<!-- 弹窗 -->
		<fa-modal
			title="导入案例"
			:visible="showUploadBox"
			@ok="uploadHdCaseList"
			@cancel="showUploadBox = false"
		>
		</fa-modal>
		<fa-modal v-model="showAddCaseModal" title="设置公司LOGO及原型后，活动预览窗将会显示对应案例" :footer="null">
			<fa-form
				id="components-form-demo-validate-other"
				:form="caseformData"
				v-bind="formItemLayout"
				@submit="handleSubmit"
			>
				<fa-form-item label="公司LOGO">
					<fa-upload
						v-decorator="[
							'imgList',
							{
								rules: [{ required: true, message: '请先上传LOGO' }],
								getValueFromEvent: uploadLogoImgChange,
							},
						]"
                        accept=".jpg, .jpeg, .png, .bmp, .gif"
						name="filedata"
						action="/ajax/advanceUpload.jsp?cmd=upload&maxWidth=10000&maxHeight=10000&imgMode=2"
                        :before-upload="beforeUploadImg"
						:file-list="fileList"
					>
						<fa-button> <fa-icon type="upload" /> 点击上传 </fa-button>
						<span class="tip"> 建议尺寸40*40 </span>
					</fa-upload>
				</fa-form-item>
				<fa-form-item label="活动原型" has-feedback>
					<fa-select
						v-decorator="[
							'prototypeName',
							{ 
								rules: [{ required: true, message: '请选择活动原型' }] 
							},
						]"
						allow-clear
						show-search
						placeholder="选择原型"
					>
						<fa-select-option :value="prototypeName" v-for="prototypeName in prototypeList" :key="prototypeName">{{prototypeName}}</fa-select-option>
					</fa-select>
				</fa-form-item>
				<fa-form-item label="上架状态" has-feedback>
					<fa-select
						v-decorator="['isPutOn', 
							{ 
								rules: [{ required: true }],
								initialValue: 'true'
							}
						]"
					>
						<fa-select-option value="true">
						上架
						</fa-select-option>
						<fa-select-option value="false">
						下架
						</fa-select-option>
					</fa-select>
				</fa-form-item>
				<fa-form-item :wrapper-col="{ span: 12, offset: 6 }">
					<fa-button type="primary" html-type="submit">
						提交
					</fa-button>
				</fa-form-item>
			</fa-form>
		</fa-modal>

		<fa-modal v-model="showEditCaseModal" title="编辑"  okText="提交" cancelText="取消" @ok="submitCaseFilterFormData">
			<fa-row style="margin-bottom: 20px;">
				<fa-col :span="8">原账号aid：{{ currentEditCase.oAid }}</fa-col>
				<fa-col :span="8">原账号活动id：{{ currentEditCase.oGameId }}</fa-col>
				<fa-col :span="8">案例号活动id：{{ currentEditCase.gameId }}</fa-col>
			</fa-row>
			<fa-form :form="caseFilterForm">
				<fa-form-item label="企业名字" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
				  <fa-input
				    placeholder="请输入企业名字"
					v-decorator="['oAcctName', { rules: [{ required: true, message: '请输入企业名字' }] }]"
				  />
				</fa-form-item>
				<fa-form-item label="行业" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
				  <fa-select
				    showSearch
					v-decorator="[
					  'trade',
					  { rules: [{ required: true, message: '请选择行业' }] },
					]"
					placeholder="请选择行业"
				  >
						<fa-select-option v-for="trade in tradeList" :value="trade" :key="trade">{{trade}}</fa-select-option>
					</fa-select>
				</fa-form-item>
				<fa-form-item label="场景" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
					<fa-select
					  v-decorator="[
						'scene',
						{ rules: [{ required: true, message: '请选择场景' }] },
					  ]"
					  placeholder="请选择场景"
					>
						<fa-select-option :value="scene" v-for="scene in sceneList" :key="scene">{{scene}}</fa-select-option>
					</fa-select>
				  </fa-form-item>
				  <fa-form-item label="活动分类" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
					<fa-select
					  v-decorator="[
						'label',
						{ rules: [{ required: true, message: '请选择活动分类' }] },
					  ]"
					  placeholder="请选择活动分类"
					>
						<fa-select-option :value="tag" :label="tag" v-for="tag in lableList" :key="tag">{{tag}}</fa-select-option>
					</fa-select>
				  </fa-form-item>
				  <fa-form-item label="活动亮点" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
						<fa-input
						type="textarea"
						placeholder="请输入活动亮点"
						v-decorator="['activityBright', { rules: [{ required: true, message: '请输入活动亮点' }] }]"/>
				  </fa-form-item>
				  <fa-form-item label="活动功能" :label-col="{ span: 5 }" :wrapper-col="{ span: 12 }">
						<fa-input
						type="textarea"
						placeholder="请输入活动功能"
						v-decorator="['activeFunc', { rules: [{ required: true, message: '请输入活动功能' }] }]"/>
			 		</fa-form-item>
			  </fa-form>
		</fa-modal>
  	</div>
</script>
<script type="text/javascript" src="<%= HdOssResDef.getResPath("js_moment") %>"></script>
<script type="text/javascript" src="<%= HdOssResDef.getResPath("js_clipboard") %>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_jquery_min")%>"></script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_vue")%>"></script>
<%-- <script src="https://cdn.jsdelivr.net/npm/vue-resource@1.5.1/dist/vue-resource.min.js"></script> --%>
<script src="<%=HdOssResDef.getResPath("js_vue_resource")%>"></script>
<%=FrontEndDef.getPackageJs("fa-component", "~1.1.0")%>
<script>
	var authMktDepartment = <%=authMktDepartment%>;
	var authSalesCenterAdm = <%=authSalesCenterAdm%>;
	var components = (function () {
		var columns = [
			{ 
				key: '1', 
				title: '序号', 
				width: 50,
				dataIndex: 'number', 
				slots: { title: 'number' },
				scopedSlots: { customRender: 'number' },
				fixed: 'left'
			},
			{ title: 'aid（原账号）', width: 100, dataIndex: 'oAid', key: '2'},
			{ title: '企业名称（原账号）', dataIndex: 'oAcctName', key: '3', width: 150 },
			{ title: '活动id（原账号）', dataIndex: 'oGameId', key: '4', width: 150 },
			{ title: '活动id（案例账号）', dataIndex: 'gameId', key: '5', width: 150 },
			{ title: '活动模板', dataIndex: 'gameName', key: '6', width: 150 },
			{ title: '行业', dataIndex: 'trade', key: '7', width: 150 },
			{ title: '场景', dataIndex: 'scene', key: '8', width: 150 },
			{
				title: '活动二维码（案例账号）',
				dataIndex: 'gameUrl',
				key: '9',
				width: 150,
				slots: { title: 'gameUrl' },
				scopedSlots: { customRender: 'gameUrl' },
			},
			{ title: '活动分类', dataIndex: 'label', key: '10', width: 150 },
			{ title: '活动亮点', dataIndex: 'activityBright', key: '11', width: 150 },
			{ title: '活动功能', dataIndex: 'activeFunc', key: '12', width: 150 },
			{ 
				title: '活动数据(原账号)',
				dataIndex: 'activeData', 
				key: '13', 
				width: 150,
				slots: { title: 'activeData' },
				scopedSlots: { customRender: 'activeData' }
			},
			{
				title: '活动数据(案例账号)',
				dataIndex: 'caseActiveData',
				key: '14',
				width: 150,
				slots: { title: 'caseActiveData' },
				scopedSlots: { customRender: 'caseActiveData' }
			},
			{ 
				title: '导入时间', 
				dataIndex: 'createTime', 
				key: '15', 
				width: 150,
				slots: { title: 'createTime' },
				scopedSlots: { customRender: 'createTime' },
			},
			{ title: '备注', dataIndex: 'mark', key: '16', width: 150 },
			{
				title: '操作',
				dataIndex: 'id',
				key: 'id',
				width: 190,
				scopedSlots: { customRender: 'action' },
				fixed: 'right'
			},
		];
		var recordColumns = [
			{ 
				key: '1', 
				title: '序号', 
				width: 50,
				dataIndex: 'number', 
				slots: { title: 'number' },
				scopedSlots: { customRender: 'number' }
			},
			{ 
				key: '2', 
				title: '操作人', 
				dataIndex: 'staffName', 
			},
			{ 
				key: '3', 
				title: '导入时间', 
				dataIndex: 'createTime', 
				slots: { title: 'createTime' },
				scopedSlots: { customRender: 'createTime' },
			},
			{ 
				key: '4', 
				title: '文件名', 
				dataIndex: 'fileName', 
			},
			{ 
				key: '5', 
				title: '导入状态', 
				dataIndex: 'statusView', 
			},
			{ 
				key: '6', 
				title: '失败原因', 
				dataIndex: 'failReason', 
			},
		];
		var previewCaseColumns = [
			{ 
				key: '1', 
				title: '序号', 
				width: 50,
				dataIndex: 'number', 
				slots: { title: 'number' },
				scopedSlots: { customRender: 'number' },
			},
			{ title: '活动id（案例账号）', dataIndex: 'gameId', key: '2' },
			{ title: '企业名称（原账号）', dataIndex: 'oAcctName', key: '3', width: 150 },
			{
				title: '公司logo',
				dataIndex: 'companyLogoUrl',
				key: '4',
				width: 150,
				slots: { title: 'companyLogoUrl' },
				scopedSlots: { customRender: 'companyLogoUrl' },
			},
			{ title: '活动名称', dataIndex: 'gameName', key: '5', width: 150 },
			{ title: '对应原型', dataIndex: 'prototypeName', key: '6', width: 150 },
			{ title: '行业', dataIndex: 'trade', key: '7', width: 150 },
			{ title: '场景', dataIndex: 'scene', key: '8', width: 150 },
			{
				title: '活动二维码',
				dataIndex: 'gameUrl',
				key: '9',
				width: 150,
				slots: { title: 'gameUrl' },
				scopedSlots: { customRender: 'gameUrl' },
			},
			{ title: '活动分类', dataIndex: 'label', key: '10', width: 150 },
			{ title: '活动亮点', dataIndex: 'activityBright', key: '11', width: 150 },
			{ 
				title: '活动数据', 
				dataIndex: 'activeData', 
				key: '12', 
				width: 150,
				slots: { title: 'activeData' },
				scopedSlots: { customRender: 'activeData' }
			},
			{ 
				title: '未上架状态', 
				dataIndex: 'isPutOnShelf', 
				key: '13', 
				width: 150,
				slots: { title: 'isPutOnShelf' },
				scopedSlots: { customRender: 'isPutOnShelf' },
			},
			{
				title: '操作',
				dataIndex: 'id',
				key: 'id',
				width: 190,
				scopedSlots: { customRender: 'previewCaseAction' },
				fixed: 'right'
			},
		];
		 
		var caseFilterData = [
			{ 
				key: '1', 
				title: '序号', 
				width: 50,
				dataIndex: 'number', 
				slots: { title: 'number' },
				scopedSlots: { customRender: 'number' },
				fixed: 'left'
			},
			{ title: 'aid（原账号）', width: 100, dataIndex: 'oAid', key: '2'},
			{ title: '企业名称（原账号）', dataIndex: 'oAcctName', key: '3', width: 150 },
			{ title: '活动id（原账号）', dataIndex: 'oGameId', key: '4', width: 150 },
			{ title: '活动id（案例账号）', dataIndex: 'gameId', key: '5', width: 150 },
			{ title: '活动模板', dataIndex: 'gameName', key: '6', width: 150 },
			{ title: '行业', dataIndex: 'trade', key: '7', width: 150 },
			{ title: '场景', dataIndex: 'scene', key: '8', width: 150 },
			{
				title: '活动二维码（案例账号）',
				dataIndex: 'gameUrl',
				key: '9',
				width: 150,
				slots: { title: 'gameUrl' },
				scopedSlots: { customRender: 'gameUrl' },
			},
			{ title: '活动分类', dataIndex: 'label', key: '10', width: 150 },
			{ title: '活动亮点', dataIndex: 'activityBright', key: '11', width: 150 },
			{ title: '活动功能', dataIndex: 'activeFunc', key: '12', width: 150 },
			{ 
				title: '复制时间', 
				dataIndex: 'createTime', 
				key: '13', 
				width: 150,
				slots: { title: 'createTime' },
				scopedSlots: { customRender: 'createTime' },
			},
			{ title: '复制者', dataIndex: 'createPerson', key: '14', width: 150 },
			{
				title: '操作',
				dataIndex: 'id',
				key: 'id',
				width: 190,
				scopedSlots: { customRender: 'caseFilterAction' },
				fixed: 'right'
			},
		];

		var pad = function(num, n) {
			num = num + '';
			while(num.length < n) {
				num = '0' + num;
			}
			return num;
		};

		var swiperSlideForm = {
			props: ["classify", 'tabname', 'curkey'], //props必须用小写，否则无效
			template: "#swiper-slide-form",
			data: function () {
				return {
					dataList: [],
					totalSize: 0,
					headers: {
						authorization: 'authorization-text',
					},
					showUploadBox: false,
					tradeList: [],
					sceneList: [],
					lableList: [],
					prototypeList: [],
					activeTag: [],
					copyBtnList: [],
					keyWork: '',
					activeFunc: '',
					authMktDepartment: authMktDepartment,
					authSalesCenterAdm: authSalesCenterAdm,
					showAddCaseModal: false,
					showEditCaseModal: false,
					addCaseloading: false,
					curCaseIndex: '',
					formItemLayout: {
						labelCol: { span: 6 },
						wrapperCol: { span: 14 },
					},
					fileList: [],
					caseformData: [],
					caseFilterFormData: {
						oAid: "",              // 原账号Aid
						oGameId: "",       // 原活动账号id
						gameId: "",       // 案例活动账号id
						createPerson: "",       // 操作人
					},
					caseFilterForm: [],
					currentEditCase: {}
				}
			},
			computed: {
				columns: function() {
					if(this.tabname == 'recordData') {
						return recordColumns;
					}else if(this.tabname == 'previewCaseData') {
						return previewCaseColumns;
					}else if(this.tabname == 'caseFilterData') {
						return caseFilterData;
					}else {
						return columns;
					}
				},
				tableScrollParam: function() {
					if(this.tabname == 'recordData') {
						return { y: 600 };
					}else{
						return { x: '135%', y: 600 }
					}
				},
			},
			watch: {
				keyWork: function(val) {
					this.classify.keyWork = val;
				},
				activeFunc: function(val) {
					this.classify.activeFunc = val;
				},
				// 选择活动分类
				activeTag: function(tag) {
					this.classify.label = tag;
				},
				curkey: function (newKey) {
					if(newKey === this.tabname){
						this.getDataList();
					}
				},
				caseFilterFormData: {
					handler(newVal) {
						var keys = Object.keys(newVal);
						for (var i = 0; i < keys.length; i++) {
							var key = keys[i];
							this.classify[key] = newVal[key];
						}
						console.log(this.classify)
					},
					deep: true
				}
			},
			created: function() {
				this.getHdCaseSelectData();
				this.getDataList();
			},
			updated: function() {
				// 初始化剪切板
				var _this = this;
				this.$nextTick(function() {
					for(var i = 0, len = _this.classify.limit; i < len; i++) {
						var ref = (_this.classify.page - 1) * 10 + i;
						_this.$refs['copy' + ref] && (_this.copyBtnList[i] = new ClipboardJS(_this.$refs['copy' + ref].$el));
					}
				})
			},
			filters: {
				filterCreateTime(time) {
					var date = new Date(time);
					return pad(date.getFullYear(), 4) + '-' + pad(date.getMonth() + 1, 2) + '-' + pad(date.getDate(), 2) + ' ' + pad(date.getHours(), 2) + ':' + pad(date.getMinutes(), 2);
				},
				filterGameUrl(gameUrl) { // 链接 => 二维码图片
					return 'https://i.hd.fkw.com/qrCode.jsp?cmd=qrurl&siteUrl=' + encodeURIComponent(gameUrl) + '&fromQrcode=true&editQrcode=true';
				},
				filterIsPutOnShelf(isPutOnShelf) {
					return isPutOnShelf ? '已上架' : '未上架';
				}
			},
			methods: {
				moment: moment,
				getImportTime: function(moment, dateArr){
					var time = this.changeTime(moment, dateArr);
					this.classify.startTime = time.startTime;
					this.classify.endTime = time.endTime;
				},
				changeTime: function(moment, dateArr) {
					var startTime, endTime;
					if(!dateArr[0]) {
						startTime = new Date().getTime() - 30 * 24 * 60 * 60 * 1000;
						endTime = new Date().getTime();
					}else{
						startTime = this.getDateForLong(dateArr[0]);
						endTime = this.getDateForLong(dateArr[1]) + 24*60*60*1000;
					}

					return {
						startTime: startTime,
						endTime: endTime
					}
				},
				getCreateTime: function(moment, dateArr){
					this.classify.createTimeStart = dateArr[0];
					this.classify.createTimeEnd = dateArr[1];
				},
				pad: function (num, n) {
					num = num + '';
					while(num.length < n) {
						num = '0' + num;
					}
					return num;
				},
				getDateForLong: function (time) {
					var date = new Date(time);
					var year = this.pad(date.getFullYear(), 4);
					var month = this.pad(date.getMonth(), 2);
					var day = this.pad(date.getDate(), 2);
					return new Date(year, month, day).getTime();
				},
				// 选择行业
				changeTrade: function(trade) {
					this.classify.trade = trade;
				},
				// 选择场景
				handleClickScene: function(scene) {
					this.classify.scene = scene;
				},
				// 选择原型
				selectPrototype: function(prototypeName) {
					this.classify.prototypeName = prototypeName;
				},
				// 错误提示
				showError: function(msg) {
					this.$message.error(msg || '系统繁忙，请稍候再试');
				},
				showSucces: function(msg) {
					this.$message.success(msg);
				},
				// 点击页码
				paginationChange: function(pageNumber) {
					this.classify.page = pageNumber;
					this.getDataList();
				},
				getDataList: function() {
					if(this.tabname == 'recordData'){
						this.getHdCaseInputRecordList()
					} else if(this.tabname == 'caseFilterData'){
						this.getCaseFilterList();
					} else {
						this.getHdCaseList();
					}
				},
				formatTimeStamp: function (timeStamp, hours, minutes) {
					hours = hours || 0;
					minutes = minutes || 0;
					timeStamp = new Date(timeStamp);
					timeStamp.setHours(hours);
					timeStamp.setMinutes(minutes);
					return timeStamp.getTime();
				},
				// 获取案例库
				getHdCaseList: function() {
					var _this = this,
						classify = this.classify;
					classify.startTime && (classify.startTime = this.formatTimeStamp(classify.startTime, 0, 0));
					classify.endTime && (classify.endTime = this.formatTimeStamp(classify.endTime, 23, 59));
					$.ajax({
						type: 'post',
						url: '/ajax/hdProduct_h.jsp?cmd=getHdCaseList',
						dataType: 'json',
						data: classify,
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								console.log(res);
								_this.totalSize = res.totalSize;
								_this.dataList = res.dataList;
							}
						}
					});
				},
				// 获取案例筛选库列表数据
				getCaseFilterList: function(){
					var _this = this;
					$.ajax({
						type: 'post',
						url: '/api/caseFilter/getCaseFilterList',
						dataType: 'json',
						data: _this.classify,
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								console.log(res.data);
								var data = res.data;
								_this.totalSize = data.totalSize;
								_this.dataList = data.dataList;
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				showDeleteConfirm: function(callback) {
					var _this = this;
					return function() {
						var args = arguments;
						_this.$confirm({
							title: '删除',
							content: '确定删除此案例数据吗？',
							okType: 'danger',
							okText: '确定',
							cancelText: '取消',
							onOk: function() {
								callback.apply(_this, args);
							},
							onCancel: function() {}
						});
					}
				},
				showConfirm: function(callback) {
					var _this = this;
					return function() {
						var args = arguments;
						_this.$confirm({
							title: '提示',
							content: '是否将此案例导入案例库？',
							okText: '确定',
							cancelText: '取消',
							onOk: function() {
								callback.apply(_this, args);
							},
							onCancel: function() {}
						});
					}
				},
				// 导入案例库 案例筛选库列表数据
				importCaseFilter: function(id) {
					var _this = this;
					$.ajax({
						type: 'post',
						url: '/api/caseFilter/importCase',
						dataType: 'json',
						data: {id: id},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								_this.showSucces("导入成功");
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				// 删除 案例筛选库列表数据
				deleteCaseFilter: function(id) {
					var _this = this;
					$.ajax({
						type: 'post',
						url: '/api/caseFilter/deleteCaseFilter',
						dataType: 'json',
						data: {id: id},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								_this.getDataList();
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				// 触发上传按钮
				handleClickUploadBtn: function() {
					// this.$refs.fileInput.click();
					$('.uploadInput').click();
				},
				handleClickTabs: function() {
					$('.fa-tabs-tab')[2].click();
				},
				handleActvieFunc: function () {
					window.open('https://shimo.im/sheets/WWRwXxHrgkvJ6TtQ/MODOC');
				},
				// 上传案例excel
				uploadHdCaseList: function(){
					var file = this.$refs.fileInput.files[0],
						_this = this;
					if(!file){
						return;
					}
					var fd = new FormData();
					fd.append('filedata', file);
					Vue.http.post(
						'/ajax/advanceUpload.jsp?cmd=uploadHdCaseList',
						fd,
						{
							headers: {
								"Content-Type": "multipart/form-data"
							}
						}
					).then(({data}) => {
						if(data.success) {
							var result = data.data;
							if(result && result.failNum > 0) {
								return _this.showError(result && result.failReason);
							}
							_this.$message.success(file.name + '导入成功');
							_this.getDataList();
						}else{
							_this.showError(data.msg);
						}
					});
				},
				// 获取下拉框选项
				getHdCaseSelectData: function() {
					var _this = this;
					$.ajax({
						type: 'get',
						url: '/ajax/hdProduct_h.jsp?cmd=getHdCaseSelectData',
						dataType: 'json',
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								var data = res.data;
								_this.lableList = data.lableList;
								_this.sceneList = data.sceneList;
								_this.tradeList = data.tradeList;
								_this.prototypeList = data.prototypeList; // 原型列表
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				// 收藏/取消收藏
				setHdCaseCollect: function(index, id, fromDel) {
					var _this = this,
						hasCollect = _this.dataList[index].hasCollect;
					$.ajax({
						type: 'post',
						url: '/ajax/hdProduct_h.jsp?cmd=setHdCaseCollect',
						dataType: 'json',
						data: {
							id: id
						},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								if(!fromDel){
									if(_this.tabname == 'collectCaseData') {
										_this.dataList.splice(index, 1);
									}else{
										_this.dataList[index].hasCollect = !hasCollect;
									}
								}
							}else{
								_this.showError(res.msg);
							}
						}
					});
					
				},
				// 复制链接
				copyCode: function(index) {
					var clipboard = this.copyBtnList[index];
					var _this = this;
					clipboard.on('success', function() {
						_this.$message.success('复制成功！');
					});
					clipboard.on('error', function(e) {
						console.log(e);
						_this.showError('复制失败，请手动选择复制');
					});
				},
				// 删除案例
				deleteHdCase: function (index, record) {
					var _this = this,
						hasCollect = _this.dataList[index].hasCollect;
					if(hasCollect){
						this.setHdCaseCollect(index, record.id, true);
					}
					var gameId = _this.filterOGameId(record.oGameId);
					$.ajax({
						type: 'post',
						url: '/ajax/hdProduct_h.jsp?cmd=deleteHdCase',
						dataType: 'json',
						data: {
							aid: record.oAid,
							gameId: parseInt(gameId || 0)
						},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								_this.$message.success('删除成功');
								_this.getDataList();
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				// 获取导入记录
				getHdCaseInputRecordList: function() {
					var _this = this;
					this.classify.startTime = this.formatTimeStamp(this.classify.startTime, 0, 0);
					this.classify.endTime = this.formatTimeStamp(this.classify.endTime, 23, 59);
					$.ajax({
						type: 'post',
						url: '/ajax/hdProduct_h.jsp?cmd=getHdCaseInputRecordList',
						dataType: 'json',
						data: _this.classify,
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								console.log(res);
								_this.totalSize = res.totalSize;
								_this.dataList = res.dataList;
								console.log(_this.dataList);
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				getRowKey: function(record) {
					return this.tabname === 'recordData' ? record.id : (record.id + "_" + record.gameId + "_" + record.oGameId);
				},
				/****************  编辑弹窗窗案例 start ****************/
				toShowEditCaseModal: function(index) {
					this.showEditCaseModal = true;
					this.currentEditCase = this.dataList[index];
					this.caseFilterForm = this.$form.createForm(this, {
					 name: 'oAcctName',
					 name: 'trade',
					 name: 'scene' ,
					 name: 'label' ,
					 name: 'activityBright',
					 name: 'activeFunc'
					});
				},
				submitCaseFilterFormData: function(){
					var _this = this;
					this.caseFilterForm.validateFields(function(err, values) {
						if(!err){
							_this.updateCaseFilter(values);
						}
					})
				},
				updateCaseFilter: function(values) {
					var _this = this;
					values.id = this.currentEditCase.id;
					$.ajax({
						type: 'post',
						url: '/api/caseFilter/modifyCaseFilter',
						dataType: 'json',
						contentType : 'application/json',
						data: JSON.stringify(values),
						error: function(res) {
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success && res.data) {
								_this.showEditCaseModal = false;
								_this.showSucces("修改成功");
								_this.getDataList();
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				/****************  预览窗案例 start ****************/
				toShowAddCaseModal: function(index) {
					this.showAddCaseModal = true;
					// 重新初始化
					this.curCaseIndex = index;
					this.fileList = [];
					this.caseformData = this.$form.createForm(this, { name: 'validate_other' });
				},
				beforeUploadImg: function(file) {
					var fileType = file.type ? file.type.split('/')[1] : file.name.split('.')[1];
					fileType = fileType.toUpperCase();
					if(['JPG', 'JPEG', 'PNG', 'BMP', 'GIF'].indexOf(fileType) == -1) {
						this.showError('文件' + file.name + '类型不允许');
						return false;
					}
				},
				uploadLogoImgChange: function(obj) {
					var fileList = obj.fileList,
						file = obj.file,
						response = file.response;
					if(file.status == 'done' && response) {
						if(!response.success) {
							return this.showError(response.msg);
						}
        				fileList = fileList.slice(-1);
						fileList = fileList.map(file => {
							if (file.response) {
								file.url = file.response.path;
							}
							return file;
						});
					}else{
						fileList = fileList.slice(-1);
					}
					this.fileList = fileList;
					return obj && fileList;
				},
				// 表单提交
				handleSubmit(e) {
					var _this = this;
					var curData = _this.dataList[_this.curCaseIndex];
					e.preventDefault();
					this.caseformData.validateFields((err, values) => {
						var logoUrl = values.imgList[0].url;
						if(err || !logoUrl)return;
						console.log('Received values of form: ', values);
						values.isPut = true;
						values.caseId = curData.id;
						values.logoUrl = logoUrl;
						values.oAid = curData.oAid;
						values.oGameId = _this.filterOGameId(curData.oGameId);
						delete values.imgList;
						_this.setPreviewCase(values).then(function() {
							curData.companyLogoUrl = logoUrl;
							curData.isPutOn = values.isPutOn;
							curData.isPut = values.isPut;
							curData.prototypeName = values.prototypeName;
							_this.showSucces('已放到预览窗');
						});
					});
				},
				handleClickReUploadBtn: function(index) {
					this.curCaseIndex = index;
				},
				// 上传案例logo
				reUploadLogo: function(e, index) {
					var _this = this;
					var fileList = this.uploadLogoImgChange(e);
					if(fileList[0].url) {
						var curData = _this.dataList[_this.curCaseIndex],
							logoUrl = fileList[0].url;
						_this.setPreviewCase({
							caseId: curData.id,
							isPutOn: curData.isPutOnShelf,
							isPut: true,
							prototypeName: curData.prototypeName,
							logoUrl: logoUrl,
							oAid: curData.oAid,
							oGameId: _this.filterOGameId(curData.oGameId)
						}).then(function() {
							curData.companyLogoUrl = logoUrl;
							_this.showSucces('上传成功');
						});
					}
				},
				// 提交案例预览窗
				setPreviewCase: function(data) {
					var _this = this;
					data.isPutOn = data.isPutOn == 'true' || data.isPutOn == true;
					return $.Deferred(function(def){
						$.ajax({
							type: 'post',
							url: '/api/case/preview',
							dataType: 'json',
							contentType: 'application/json;chast=utf-8',
							data: JSON.stringify(data),
							error: function(res){
								_this.showError(res.msg);
							},
							success: function(res){
								if(res.success) {
									_this.showAddCaseModal = false;
									def.resolve();
								}else{
									_this.showError(res.msg);
									def.reject();
								}
							}
						});
					});
				},
				// 更新案例预览窗上架状态
				upPreviewStatus: function(caseId, index) {
					var _this = this,
						curData = this.dataList[index],
						isPutOnShelf = !curData.isPutOnShelf;
					$.ajax({
						type: 'post',
						url: '/api/case/preview-status',
						dataType: 'json',
						contentType: 'application/x-www-form-urlencoded;chast=utf-8',
						data: {
							oAid: curData.oAid,
							oGameId: _this.filterOGameId(curData.oGameId),
							caseId: caseId,
							isPutOnShelf: isPutOnShelf // 是否上架
						},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								_this.dataList[index].isPutOnShelf = isPutOnShelf;
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				// 投放案例预览窗
				putPreview: function(caseId, index, isPut) {
					var _this = this,
						curData = this.dataList[index];
					$.ajax({
						type: 'post',
						url: '/api/case/put-preview',
						dataType: 'json',
						contentType: 'application/x-www-form-urlencoded;chast=utf-8',
						data: {
							oAid: curData.oAid,
							oGameId: _this.filterOGameId(curData.oGameId),
							caseId: caseId,
							isPut: isPut // 是否投放
						},
						error: function(res){
							_this.showError(res.msg);
						},
						success: function(res){
							if(res.success) {
								!isPut && _this.getDataList();
							}else{
								_this.showError(res.msg);
							}
						}
					});
				},
				filterOGameId: function(oGameId) {
					return oGameId.replace('HD', '');
				}
				/****************  预览窗案例 end ****************/
			}

		};
		return {
			swiperSlideForm: swiperSlideForm
		};
	})();
</script>
<script type="text/javascript" src="<%=HdOssResDef.getResPath("js_hdCaseLibrary")%>"></script>
</body>
</html>