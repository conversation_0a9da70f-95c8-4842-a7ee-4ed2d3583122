<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){out.println("没有权限");return;}%>

<%
	boolean isOther = Parser.parseBoolean(request.getParameter("other"), false);
	String tabStyle = "font-weight: bold;color:black;";
%>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>付费记录</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body>
		<!--页面顶部标题-->
		<div class="titleDiv" style="text-align: center;margin-bottom: 10px;" v-cloak>
			<a href="payRecord.jsp"><span v-if="this.auth.authAdm || this.auth.hdSaleManage" >互动销售</span>付款记录</a>&nbsp;&nbsp;
			<a target="_blank" href="meritStatistics.jsp" style="margin-left: 20px;">互动销售月度业绩统计</a>
			<a href="payRecordNew.jsp?other=true" style="margin-left: 15px;<%=isOther ? tabStyle : ""%>">其他业务付款记录</a>&nbsp;&nbsp;
			<a v-if="this.auth.authAdm || this.auth.hdSaleManage" href="payRecordNew.jsp?other=false" style="margin-left: 15px;<%=!isOther ? tabStyle : ""%>">顾问式销售付款记录</a>&nbsp;&nbsp;
		</div>
		<div id="hdsale-payRrecord">
			<!--查询条件 start-->
			<div class="fai-payRecord-search" v-cloak>
				<el-form :inline="true" :model="form" class="demo-form-inline" size="mini" >
					<el-form-item label="注册时间" v-if="isShowInPage('regDateFlag')">
						<el-checkbox v-model="form.regDateFlag"></el-checkbox>
						<el-date-picker class="fai-date" v-model="form.regDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="form.regDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
					</el-form-item>
					<el-form-item label="创建时间" v-if="isShowInPage('creDateFlag')">
						<el-checkbox v-model="form.creDateFlag"></el-checkbox>
						<el-date-picker class="fai-date" v-model="form.creDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="form.creDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
					</el-form-item>
					<el-form-item label="支付时间" v-if="isShowInPage('payDateFlag')">
						<el-checkbox v-model="form.payDateFlag"></el-checkbox>
						<el-date-picker class="fai-date" v-model="form.payDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="form.payDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="editable" :clearable="clearable"></el-date-picker>
					</el-form-item>
					<el-form-item label="退款时间" v-if="isShowInPage('refDateFlag')">
						<el-checkbox v-model="form.refDateFlag"></el-checkbox>
						<el-date-picker class="fai-date" v-model="form.refDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="form.refDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
					</el-form-item>
					<el-form-item label="领取时间" v-if="isShowInPage('receiveDateFlag')">
						<el-checkbox v-model="form.receiveDateFlag"></el-checkbox>
						<el-date-picker class="fai-date" v-model="form.receiveDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>
						- <el-date-picker class="fai-date" v-model="form.receiveDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>
					</el-form-item>
					<%--<el-form-item label="旧账号领取时间" v-if="isShowInPage('oldReceiveDateFlag')">--%>
						<%--<el-checkbox v-model="form.oldReceiveDateFlag"></el-checkbox>--%>
						<%--<el-date-picker class="fai-date" v-model="form.oldReceiveDateBeg" type="date" value-format="yyyy-MM-dd" placeholder="开始日期" :editable="editable" :clearable="clearable"></el-date-picker>--%>
						<%--- <el-date-picker class="fai-date" v-model="form.oldReceiveDateEnd" type="date" value-format="yyyy-MM-dd" placeholder="结束日期" :editable="editable" :clearable="clearable"></el-date-picker>--%>
					<%--</el-form-item>--%>

					<el-form-item label="付款类型" v-if="isShowInPage('payType')">
						<el-select v-model="form.payType" filterable>
							<el-option label="全部" value="-1"></el-option>
							<el-option label="首购" value="1"></el-option>
							<el-option label="重购" value="0"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="排序" id="sort" v-if="isShowInPage('sort')">
						<el-select v-model="form.sort" filterable>
							<el-option label="按创建时间倒序" value="0"></el-option>
							<el-option label="按注册时间倒序" value="1"></el-option>
							<el-option label="按支付时间倒序" value="2"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="订单状态" v-if="isShowInPage('status')">
						<el-select v-model="form.status" filterable>
							<el-option label="所有" value="0"></el-option>
							<el-option label="处理完成" value="1"></el-option>
							<el-option label="待支付" value="2"></el-option>
							<el-option label="已退款" value="3"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="支付情况" v-if="isShowInPage('pay')">
						<el-select v-model="form.pay" filterable>
							<el-option label="所有" value="0"></el-option>
							<el-option label="有支付" value="1"></el-option>
							<el-option label="无需支付" value="2"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item label="金额" id="price" v-if="isShowInPage('minPrice')">
						<el-input v-model="form.minPrice" placeholder="￥最小金额"></el-input>
						~ <el-input v-model="form.maxPrice" placeholder="￥最大金额"></el-input>
					</el-form-item>
					<el-form-item label="产品类型" v-if="isShowInPage('productType')">
						<el-select v-model="form.productType.value" filterable>
							<el-option v-for="select in form.productType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>
						</el-select>
					</el-form-item>				
					<%--<el-form-item label="购买数量" v-if="isShowInPage('amount')">--%>
						<%--<el-select v-model="form.amount.value" filterable>--%>
							<%--<el-option v-for="select in form.amount.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<el-form-item label="注册用途" v-if="isShowInPage('goal')">		
						<el-select v-model="form.goal" filterable>
							<el-option label="所有" value="-1"></el-option>
							<el-option label="企业/组织官网" value="1"></el-option>
							<el-option label="推广产品" value="2"></el-option>
							<el-option label="在线商城" value="3"></el-option>
							<el-option label="个人网站" value="5"></el-option>
							<el-option label="发布信息" value="9"></el-option>
							<el-option label="其他" value="7"></el-option>
							<el-option label="互动-微信吸粉" value="100"></el-option>
							<el-option label="互动-门店引流" value="101"></el-option>
							<el-option label="互动-电商引流" value="102"></el-option>
							<el-option label="互动-现场活动" value="103"></el-option>
							<el-option label="互动-品牌传播" value="104"></el-option>
							<el-option label="互动-其他" value="105"></el-option>
<%--							<el-option label="互动-学生" value="107"></el-option>--%>
							<el-option label="未知" value="106"></el-option>
						</el-select>
					</el-form-item>
					<%--<el-form-item label="注册来源" v-if="isShowInPage('ta')">--%>
						<%--<el-select v-model="form.ta" filterable>--%>
						     <%--<el-option label="所有" value="-1"></el-option>--%>
							<%--<el-option label="建站来源" value="1"></el-option>--%>
							<%--<el-option label="互动推广" value="3"></el-option>--%>
							<%--<el-option label="微传单推广" value="4"></el-option>--%>
							<%--<el-option label="线下广告" value="6"></el-option>--%>
							<%--<el-option label="移动端推广" value="7"></el-option>--%>
							<%--<el-option label="自来" value="8"></el-option>--%>
							<%--<el-option label="SEO" value="10"></el-option>--%>
							<%--<el-option label="活动引流" value="11"></el-option>--%>
							<%--<el-option label="未知" value="9"></el-option>--%>
							<%--<el-option label="移动端放量" value="12"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<%--<el-form-item label="销售分组" v-if="isShowInPage('staff_group')">--%>
						<%--<el-select v-model="form.staff_group" filterable>--%>
							<%--<el-option label="所有" value="all"></el-option>--%>
							<%--<el-option label="真实销售组" value="authHDSaleEnergy"></el-option>--%>
							<%--<el-option label="虚拟销售组" value="authHDSaleAI"></el-option>--%>
							<%--<el-option label="销售1组" value="authHDSale1"></el-option>--%>
							<%--<el-option label="销售2组" value="authHDSale2"></el-option>--%>
							<%--<el-option label="销售3组" value="authHDSale3"></el-option>--%>
							<%--<el-option label="销售4组" value="authHDSale4"></el-option>--%>
							<%--<el-option label="方案销售组" value="authHDSale5"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<el-form-item label="领取人" v-if="isShowInPage('staff_sid')">
						<el-select v-model="form.staff_sid" filterable>
							<el-option label="所有" value="0"></el-option>
							<el-option v-for="preSale in preSaleList" :label="preSale.name" :value="preSale.sid" :key="preSale.sid"></el-option>
						</el-select>
					</el-form-item>
					<%--<el-form-item label="标签" v-if="isShowInPage('tag')">--%>
						<%--<el-select v-model="form.tag.value" filterable>--%>
							<%--<el-option v-for="select in form.tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<%--<el-form-item label="时间段" v-if="isShowInPage('time_tag')">--%>
						<%--<el-select v-model="form.time_tag.value" filterable>--%>
							<%--<el-option v-for="select in form.time_tag.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					
					<el-form-item label="AID" v-if="isShowInPage('aid')">
						<el-input v-model="form.aid" placeholder="AID" class="fai-wid-100"></el-input>
					</el-form-item>
					<el-form-item label="订单号" v-if="isShowInPage('orderId')">
						<el-input v-model="form.orderId" placeholder="订单号" class="fai-wid-100"></el-input>
					</el-form-item>

					 <%--<el-form-item label="新A/B库" v-if="isShowInPage('newA_BLib')">--%>
						<%--<el-select v-model="form.newA_BLib.value" filterable>--%>
						   <%--<el-option v-for="select in form.newA_BLib.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<%--<el-form-item label="业务" v-if="isShowInPage('Business')">--%>
						<%--<el-select v-model="form.Business.value" filterable>--%>
						   <%--<el-option v-for="select in form.Business.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
						<%--<el-form-item label="ab类资源" v-if="isShowInPage('abType')">--%>
						  <%--<el-select v-model="form.abType.value" filterable>--%>
						    <%--<el-option v-for="select in form.abType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						  <%--</el-select>--%>
					    <%--</el-form-item>--%>
						<%--</el-form-item>--%>
					<%--<el-form-item label="是否周末分配资源" v-if="isShowInPage('isWeekendResource')">--%>
						<%--<el-select v-model="form.isWeekendResource.value" filterable>--%>
						   <%--<el-option v-for="select in form.isWeekendResource.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<%--<el-form-item label="分配方式" v-if="isShowInPage('allotType')">--%>
						<%--<el-select v-model="form.allotType.value" filterable>--%>
						   <%--<el-option v-for="select in form.allotType.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<%--<el-form-item label="报备类型" v-if="isShowInPage('approveStyle')">		--%>
						<%--<el-select v-model="form.approveStyle.value" filterable>--%>
							<%--<el-option v-for="select in form.approveStyle.labelList" :label="select.name" :value="select.label" :key="select.label"></el-option>--%>
						<%--</el-select>--%>
					<%--</el-form-item>--%>
					<el-form-item>
						<el-button icon="el-icon-search" type="primary"  @click="onSubmit" v-if="isShowInPage('search')">查询</el-button>
						<el-button v-if="(isShowInPage('excel'))&&(this.auth.authAdm ||this.auth.hdSaleManage)" icon="el-icon-download" type="primary"  @click="exportExcel">导出</el-button>
						<el-button type="primary" icon="el-icon-message" @click="batchSendMessag()" v-if="isShowInPage('batchMessange')">批量短信</el-button>
						<el-button class="el-icon-setting" @click="showCustomPageInfo()" ></el-button>
					</el-form-item>
				</el-form>
			</div>
			<!--查询条件 end-->

			<!--数据表格 start-->
			<div class="fai-payRecord-list" v-cloak>
				<el-table :data="tableData" row-key="rowkey" stripe border  @selection-change="handleSelectionChange" max-height="700" >
					<el-table-column type="selection" width="55">全选</el-table-column>
					<el-table-column label="序列号" type="index" width="65px" fixed></el-table-column><!--fixed:固定-->
					<el-table-column fixed="left" label="操作" width="100"  align="center"> 
						<template slot-scope="scope">
							<el-button @click.native.prevent="sendMessag(scope.$index, tableData)" type="text" size="small">发送短信</el-button>
						</template>
					</el-table-column>
					<el-table-column label="AID" width="90px" fixed>
						<template slot-scope="scope">
							<a v-bind:href="scope.row.aidUrl" target='_blank'>{{scope.row.aid}}</a>
						</template>
					</el-table-column>
					<el-table-column label="售前销售" prop="sales" width="100px" fixed></el-table-column>
					<el-table-column label="金额" prop="price" fixed></el-table-column>
					<el-table-column label="订单号">
						<template slot-scope="scope">
							<a v-bind:href="scope.row.orderUrl" target='_blank'>{{scope.row.orderId}}</a>
						</template>
					</el-table-column>
					<el-table-column label="产品类型" prop="productStr" width="140px"></el-table-column>
					<el-table-column label="支付时间" prop="payTimeStr" width="160px"></el-table-column>
					<el-table-column label="支付企业" width="180px" :show-Overflow-Tooltip=true>
						<template slot-scope="scope">
							<a v-bind:href="'http://' + scope.row.payCorpSite" target='_blank'>{{scope.row.payCorp}}</a>
						</template>
					</el-table-column>
					<%--<el-table-column label="注册来源" prop="ta"></el-table-column>--%>
					<%--<el-table-column label="标签" prop="tagName"  width="250px"></el-table-column>--%>
					<el-table-column label="创建时间" prop="createTimeStr" width="100px"></el-table-column>
					<el-table-column label="领取时间" prop="receiveTime" width="160px"> </el-table-column>
					<el-table-column label="类型" width="80px" prop="type"></el-table-column>
					<el-table-column label="状态" prop="statusStr"></el-table-column>
					<el-table-column label="用途" prop="corpGoalName" width="120px"></el-table-column>
					<el-table-column label="注册时间" prop="regTimeStr" width="160px"></el-table-column>
					<el-table-column label="退款时间" prop="refundTimeStr" width="160px"></el-table-column>
					<%--<el-table-column label="旧账号" prop="oldAid"  width="120px"></el-table-column>--%>
					<%--<el-table-column label="旧账号领取时间" prop="oldReceiveTime"  width="180px"></el-table-column>--%>
					<!-- <el-table-column label="操作" prop="orderId"></el-table-column> -->
				</el-table>
				<div>
					成交客户总金额：<span>{{totalPrice}}</span><br/>
					成交客户总数：<span>{{totalClient}}</span>
				</div>
				<!--分页 end-->
				<div class="block">
						<el-pagination @size-change="handleSizeChange"  @current-change="getDataList" :current-page.sync="pageData.currentPage"
								:page-size="10" layout="prev, pager, next, jumper" :total="pageData.totalSize">
						</el-pagination>
				</div>
			</div>
			<el-dialog title="发送短信给" :visible.sync="dialogVisible" width="30%">
					<div class="messageBox">
						<div>
							短信模板
							<el-select v-model="message.messageModel">
								<el-option v-for="select in message.messageModelList" :label="select.modelName" :value="select.id" :key="select.id"></el-option>
							</el-select>
						</div>
						<div v-if="message.singleMobile!=''">客户手机<el-input  v-model="message.singleMobile" placeholder="请输入号码" ></el-input></div>
					</div>
					<span slot="footer" class="dialog-footer">
						<el-button @click="handleMessage(false)">取 消</el-button>
						<el-button type="primary" @click="handleMessage(true)">确 定</el-button>
					</span>
			</el-dialog>
			<!--数据表格 end-->
			
			<!-- 用户自定义页面  zhs  -->
			<el-dialog  v-cloak title="用户自定义页面" :visible.sync="showCustomPagelog.show" @close="commitCustomPageInfo()" width="30%" center >
						<div style="text-align: center">
						    <el-transfer
						      style="text-align: left; display: inline-block"
						      v-model="showCustomPagelog.value"
						      filterable
						      :titles="['选项', '需要选项']"
						      :button-texts="['到左边', '到右边']"
						      :format="{
						        noChecked:'${total}' ,
						        hasChecked: '${checked}/${total}'
						      }"
						      :data="showCustomPagelog.data">
						      <!-- <el-button class="transfer-footer" slot="right-footer" size="small" v-on:click="commitCustomPageInfo()">确认</el-button> -->
						    </el-transfer>
						</div>
			</el-dialog>
		</div>
	</body>


	<script type="text/javascript">
	
		//数据埋点数据
		var element2default = {};
		var element2count = {
			"5": {}
		};

		var invalidSearch = false;
		var otherBtn = false;

		var titleObj = new Vue({
			el: '.titleDiv',
		})
		var localTime = new Date();
		var faiSearchObj = new Vue({
			el: '#hdsale-payRrecord',
			data: {
				tableData: [],
				totalPrice: "0.0",
				totalClient: "0",
				pageData:{
					currentPage:1,
					totalSize:0,
				},
				message:{
					messageModelList:[],//短信模板
					messageModel:"",//选择的短信模板
					singleMobile:"",//单个客户手机
					page:1,//当前页
					dataList:[],
					addTimeStart:new Date(),
					addTimeEnd:new Date(),
					sendTime:{
						start:localTime,
						end:localTime,
						check:false
					},
				},
				dialogVisible:false,//短信弹窗
				showCustomPagelog:{
		        	show: false,
		        	data:[],
		        	value:[],
		        	originalValue:[]
		        },	
				form: {//这里是为了填充默认值
					regDateFlag:false,//是否勾选注册时间
					regDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					regDateEnd: Fai.tool.dateFormatter(new Date()),
					creDateFlag:false,
					creDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					creDateEnd: Fai.tool.dateFormatter(new Date()),
					payDateFlag:true,
					payDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					payDateEnd: Fai.tool.dateFormatter(new Date()),
					refDateFlag:false,
					refDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					refDateEnd: Fai.tool.dateFormatter(new Date()),
					receiveDateFlag:false,
					receiveDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					receiveDateEnd: Fai.tool.dateFormatter(new Date()),
					oldReceiveDateFlag:false,
					oldReceiveDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 7*24*3600*1000),
					oldReceiveDateEnd: Fai.tool.dateFormatter(new Date()),
					payType:"-1",
					sort:"2",//默认按支付时间排序
					status:"0",//默认所有订单状态
					pay:"0",
					minPrice:"",
					maxPrice:"",
					productType: {
						value:0,
						name:"产品类型",
						labelList:[]
					},
					amount:{
						value:0,
						name:"所有",
						labelList:[
							{name:"所有",label:0},
							{name:"1年/月",label:1},
							{name:"多年/月",label:2},
						]
					},
					goal:"-1",
					ta:"-1",
					staff_group:"all",
					staff_sid:"0",
					tag: {
						value:-1,
						name:"标签",
						labelList:[]
					},
					time_tag:{
						value:-1,
						name:"所有",
						labelList:[
							{name:"所有",label:-1},
							{name:"15:45之前",label:0},
							{name:"15:45之后",label:1},
						]
					},
				
					aid:"",
					orderId:"",
					exportFlag:false,

					newA_BLib:{
						name:"所有",
						value:-1,
						labelList:[
							{name:"所有",label:-1},
							{name:"当月",label:0},
							{name:"A库",label:1},
							{name:"B库",label:2},
							{name:"当月库+A库",label:3},
						]
					},
					Business:{
						name:"全部",
						value:-1,
						labelList:[
						    {name:"全部",label:-1},
							{name:"互动",label:0},
							{name:"悦客",label:1},
							{name:"公众号助手",label:2},
						]
					},
					
					abType:{
						name:"全部",
						value:-1,
						labelList:[
						    {name:"全部",label:-1},
							{name:"a类",label:0},
							{name:"b类",label:1},
							
						]
					},
					isWeekendResource:{
						name:"不限",
						value:-1,
						labelList:[
						    {name:"不限",label:-1},
							{name:"否",label:0},
							{name:"是",label:1},
							
						]
					},
					allotType:{
						name:"不限",
						value:-1,
						labelList:[
						    {name:"全部",label:-1},
							{name:"平均分配",label:0},
							{name:"奖励资源",label:1},
							{name:"续费资源",label:2},
							{name:"公海库领取",label:3},
							{name:"审批领取",label:4},
						]
					},
					approveStyle:{
						name:"全部",
						value:-1,
						labelList:[
						     {name:"全部",label:-1},
						     {name:"新旧账号",label:1},
						     {name:"其他",label:0},
						     {name:"大满贯",label:11},
						     {name:"七鱼",label:13},
						     {name:"工单资源",label:2},
						     {name:"客户转介绍",label:3},
						     {name:"自主开发",label:4},
						     {name:"离职销售跟进",label:5},
						     {name:"客户主动添加",label:9},
						     {name:"小A",label:10},
						     {name:"同账号资源跟进",label:6}
						]
					}
					
				},
				preSaleList: [],
				editable: false,
				unlinkPanels: true,
				clearable: false,
			},
			created:function(){
				element2count["5"]['search'] = -1;

				var urlParam = {
                    other: <%=isOther%>,
                };
				Fai.http.post("/hdSale/payRecordNew_h.jsp?cmd=getPreSale", urlParam, false).then(result => {
					if(result.success){
						this.preSaleList = result.dataList;
					}
				});
				this.getDefList();
				this.getDataList();
				this.getMessageModelList();
				this.getCustomPageInfo();
			},
			methods: {
				//zhs   2019-11-14 对用户自定义页面代码
				showCustomPageInfo(){
					//备注信息
		            this.showCustomPagelog.show = true;
				},
				//zhs 2019-11-14 用于提交用户自定义页面数据	
				commitCustomPageInfo(){
					var configInfo = "";
					for(var i in this.showCustomPagelog.value){
			              if(i < this.showCustomPagelog.value.length-1){
			            	  configInfo = configInfo +this.showCustomPagelog.value[i]+",";
			              }else{
			            	  configInfo= configInfo +this.showCustomPagelog.value[i];
			              }
			            }
					var arg={
							"cmd": "setCustomPageConfigInfo",
							"moduleId": 5,
							"configInfo" : configInfo
					}
					let url = "/ajax/hdSale_h.jsp";
					
					//判断用户有没有进行修改过内容,保证不用重复提交，避免后台的请求压力
					var isChange = false;
					if (this.showCustomPagelog.value.length !== this.showCustomPagelog.originalValue.length) {
			            isChange = true;
			        } else {
			        	this.showCustomPagelog.value.forEach(item => {
			                if (this.showCustomPagelog.originalValue.indexOf(item) === -1) {
			                    flag = true;
			                }
			            })
			        }
					if(isChange){
						Vue.http.post(url,arg, {emulateJSON:true}).then(response =>{
							if(response.body.success == true){
			                    this.$message({
			                        type: 'success',
			                        message: "已根据你的喜好重新布局"
			                    });	
			                    this.showCustomPagelog.originalValue = this.showCustomPagelog.value;
							}else{
			                    this.$message({
			                        type: 'warning',
			                        message: '保存失败，请重试!'
			                    });
							}
			            }, response => {
			                this.$message({
			                    type: 'warning',
			                    message: '保存失败，请重试!'
			                });
			            });
					}
				},
				//zhs 2019-11-14  获取用户自定义页面的信息
				getCustomPageInfo(){
					var arg={
							"cmd":"getCustomPageConfigInfo",
							"moduleId":5
						}
					let url = "/ajax/hdSale_h.jsp";
					Vue.http.post(url,arg, {emulateJSON:true}).then(response =>{
						this.showCustomPagelog.value = [];
						this.showCustomPagelog.originalValue= [];
						if(response.body.success == true){
		                    this.showCustomPagelog.value=response.body.customPageInfo.split(",");
		                    this.showCustomPagelog.originalValue= response.body.customPageInfo.split(",");
						}else{
		                    this.$message({
		                        type: 'warning',
		                        message: response.body.msg
		                    });
		                    this.showCustomPagelog.value.splice(0,this.showCustomPagelog.value.length);
		                    this.showCustomPagelog.originalValue.splice(0,this.showCustomPagelog.originalValue.length);
						}
		            }, response => {
		                this.$message({
		                    type: 'warning',
		                    message: '获取用户定义页面信息失败!'
		                });
		            });
				},
				//zhs 2019-11-14判断用户页面元素是否显示
				isShowInPage(element){
					if(this.showCustomPagelog.value.length-1 == 0 ){
						return true;
					}else{
						var index = this.showCustomPagelog.value.indexOf(element);
						if(index < 0 ){
							return false;
						}else{
							return true;
						}
					}
					return false;
				},
				onSubmit() {					
					//检查至少填写一个日期条件
					var checkDateFlag = false;
					var dateCondList = ['regDateFlag', 'creDateFlag', 'payDateFlag', 'refDateFlag'];
					for(var i = 0; i < dateCondList.length; i++){
						var dateTime = this.form[dateCondList[i]];
						if(dateTime != null && dateTime != ""){
							checkDateFlag = true;
							//this.$message({showClose: true, message: '日期校验通过！', type: 'success'});
							break;
						}
					}
					
					if(!checkDateFlag){
						this.$message({showClose: true, type: 'error', message: '必须勾选其中一个时间（注册时间、创建时间、支付时间、退款时间）！'});
						return ;
					}
					this.getDataList();
				},
				exportExcel(){
					this.form.exportFlag = true;
					this.getDataList("excel");
					this.form.exportFlag = false;
				},
				getDefList(){
					var arg = {
						"cmd":"getPageDef",
						"key":"hdPayRecord"
					}
					Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
						var hdPayRecord = response.data;
						this.form.productType = hdPayRecord.productType;
						this.form.tag = hdPayRecord.tag;

						element2default = hdPayRecord.element2default;
						for(var i in hdPayRecord.element2value){
							this.showCustomPagelog.data.push({"key":i,"label":hdPayRecord.element2value[i]});
						}
					}, response => {
						this.$message({
						type: 'warning',
						message: '系统错误!'
						});
					});
				},	
				sendMessag(index,dataList){
					this.dialogVisible = true;
					this.message.messageList = [];
					var single = dataList[index];
					this.message.messageList.push(single);
					this.message.singleMobile = single.mobile;
					console.info(single);
					var arg = {
						"cmd":"getMessageModelList",
					}
					Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
						this.message.messageModelList = response.data.dataList;
						console.info(this.message.messageModelList);
					}, response => {
						this.$message({
						type: 'warning',
						message: '系统错误!'
						});
					});
				},
				batchSendMessag(){
					otherBtn = true;
					if(isNaN(element2count["5"]['batchMessange'])){
						element2count["5"]['batchMessange'] = 1;
					}else{
						element2count["5"]['batchMessange'] += 1;
					}

					this.message.singleMobile = "";
					if(this.message.messageList == undefined ||  this.message.messageList.length == 0){
						this.$message({
							type: 'warning',
							message: "请至少选择一个客户",
						});
						return;
					}
					this.dialogVisible = true;
				},
				getMessageModelList(){
					var arg = {
						"cmd":"getMessageModelList",
					}
					Vue.http.post("/ajax/dataDef.jsp", arg, {emulateJSON:true}).then(response => {
						this.message.messageModelList = response.data.dataList;
					}, response => {
						this.$message({
						type: 'warning',
						message: '系统错误!'
						});
					});
				},
				handleSelectionChange(val){
					this.message.messageList = val;
				},
				handleMessage(send) {//关闭发送短信的弹窗
					this.dialogVisible = false;
					if(send){//如果确定
						if(this.message.messageModel == ""){
							this.$message({
								type: 'warning',
								message: "尚未选择短信模板！",
							});
							return;
						}
						console.info(this.message.messageModel);
						var messageSize = this.message.messageList.length;//发送列表Length
						if(messageSize<=0){
							this.message.singleMobile = "";
							return;
						}
						var realMessageList = [];//组装参数
						if(messageSize>1&&this.message.singleMobile==""){//证明是多选
							for(var index = 0;index<messageSize;index++){
								var message = this.message.messageList[index];
								if(message.mobile==""){
									continue;
								}
								var item = {};
								item.aid = message.aid;
								item.mobile = message.mobile;
								realMessageList.push(item);
							}
						}else{//单个发送
							var message = this.message.messageList[0];
							var item = {};
							item.aid = message.aid;
							item.mobile = this.message.singleMobile;
							realMessageList.push(item);
						}
						console.info(realMessageList);
						var arg = {
							"cmd":"batchSendMessage",
							"aidList": JSON.stringify(realMessageList),
							"smsId":this.message.messageModel,
						}
						Vue.http.post("/ajax/hdSale_h.jsp", arg, {emulateJSON:true}).then(response => {
							if(response.data.success){
								this.$message({
									type: 'success',
									message: response.data.msg
								});
							}else{
								this.$message({
									type: 'warning',
									message: response.data.msg,
								});
							}
						}, response => {
							this.$message({
							type: 'warning',
							message: '系统错误!'
							});
						});
					}
				},

				//数据埋点记录次数
				dataPointCount(urlParam){
					//非查询按钮引发的查询，才算上有效的查询
					if(!invalidSearch){
						//遍历筛选项的值是否与默认值一致，不一致说明该值用过查询，则记录次数+1
						//最终传给后端的都是有使用过筛选的表单
						for(let key in element2default){
							if(urlParam[key] != element2default[key]){
								if(isNaN(element2count["5"][key])){
									element2count["5"][key] = 1;
								}else{
									element2count["5"][key] += 1;
								}
							}
						}
						element2count["5"]['search'] += 1;
					}


				},

				getDataList(param){
					var excel = "excel"===param;
					
					var urlParam = {
						cmd:'getPayRecordNewList',
						regDateFlag: this.form.regDateFlag,//是否勾选注册时间
						regDateBeg: this.form.regDateBeg,
						regDateEnd: this.form.regDateEnd,
						creDateFlag:this.form.creDateFlag,
						creDateBeg: this.form.creDateBeg,
						creDateEnd: this.form.creDateEnd,
						payDateFlag:this.form.payDateFlag,
						payDateBeg: this.form.payDateBeg,
						payDateEnd: this.form.payDateEnd,
						refDateFlag:this.form.refDateFlag,
						refDateBeg: this.form.refDateBeg,
						refDateEnd: this.form.refDateEnd,
						receiveDateFlag:this.form.receiveDateFlag,
						receiveDateBeg: this.form.receiveDateBeg,
						receiveDateEnd: this.form.receiveDateEnd,
						oldReceiveDateFlag: this.form.oldReceiveDateFlag,
						oldReceiveDateBeg: this.form.oldReceiveDateBeg,
						oldReceiveDateEnd: this.form.oldReceiveDateEnd,
						payType:this.form.payType,
						sort:this.form.sort,//默认按支付时间排序
						status:this.form.status,
						pay:this.form.pay,
						minPrice:this.form.minPrice,
						maxPrice:this.form.maxPrice,
						productType:this.form.productType.value,
						amount:this.form.amount.value,
						goal:this.form.goal,
						ta:this.form.ta,
						staff_group:this.form.staff_group,
						staff_sid:this.form.staff_sid,
						tag: this.form.tag.value,
						time_tag:this.form.time_tag.value,
						aid:this.form.aid,
						orderId:this.form.orderId,
						exportFlag:this.form.exportFlag,
						currentPage:this.pageData.currentPage,
						newA_BLib:this.form.newA_BLib.value,
						Business:this.form.Business.value,
						abType:this.form.abType.value,
						isWeekendResource:this.form.isWeekendResource.value,
						allotType:this.form.allotType.value,
						approveStyle:this.form.approveStyle.value,
						other: <%=isOther%>,
					};

					// this.dataPointCount(urlParam);

					if(excel){
						window.location.href =  '/ajax/hdSale/payRecordNew_h.jsp'+Fai.tool.parseJsonToUrlParam(urlParam,true);
					}else{
							// 查询数据
							Vue.http.post("/ajax/hdSale/payRecordNew_h.jsp", urlParam, {emulateJSON:true}).then(response => {
								console.log(response.data);
								if(response.data.success){
									this.tableData = response.data.dataList;
									this.totalPrice = response.data.totalPrice;
									this.totalClient = response.data.totalClient;
									this.pageData.totalSize = response.data.total;
								}
							});
					}
				
					
				},
                handleSizeChange(){}
		    }
		});

		window.addEventListener("beforeunload", function(event) {
			//如果一次查询都没有
			if(element2count["5"]['search'] <=0){
				//并且没有使用除了查找之外的按钮
				if(!otherBtn){
					return;
				}
			}

			//步骤一:创建异步对象
			var ajax = new XMLHttpRequest();
			//步骤二:设置请求的url参数,参数一是请求的类型,参数二是请求的url,可以带参数,动态的传递参数starName到服务端
			ajax.open('get','/ajax/hdSale_h.jsp?cmd=burialStatistics&element2count='+JSON.stringify(element2count));
			//步骤三:发送请求
			ajax.send();

		});
	</script>

</html>



