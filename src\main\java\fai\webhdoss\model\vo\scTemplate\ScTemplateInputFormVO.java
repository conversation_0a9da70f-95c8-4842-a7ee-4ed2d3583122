package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateInputFormVO {
    // 关联原型中inputForm-variable
    private String variable;

    // 名称
    private String label;

    // 描述
    private String desc;

    // 是否开启ai推荐词
    private boolean openAiTip;

    // ai推荐词的apiKey
    private String apiKey;

    // 提示词内容，通过#variable#格式关联数据
    private String prompt;

    // 默认内容
    private String defaultContent;
}
