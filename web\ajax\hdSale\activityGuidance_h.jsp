<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" session="false" %>

<%@ page import="fai.comm.util.*" %>
<%@ page import="fai.web.*" %>
<%@ page import="fai.hdUtil.*" %>
<%@ page import="fai.app.*" %>
<%@ page import="fai.web.inf.*" %>
<%@ page import="fai.webhdoss.*" %>
<%@ page import="fai.weboss.*" %>
<%@ page import="java.util.*" %>
<%@ page import="java.text.*" %>
<%@ page import="java.math.*" %>
<%@ page import="java.util.regex.Matcher" %>
<%@ page import="java.util.regex.Pattern" %>
<%@ page import="fai.cli.PreSaleUtilCli" %>
<%@ page import="fai.cli.BssStatCli" %>
<%@ page import="fai.cli.*" %>
<%@ page import="java.util.concurrent.LinkedBlockingQueue" %>
<%@ page import="fai.weboss.*"%>

<%!
    //获取指导库资源列表
    public String getActivityList(HttpServletRequest request) throws Exception {

        Param result = new Param();
        FaiList<Param> dataList = new FaiList<Param>();
        FaiList<Integer> aidList = new FaiList<Integer>();

        //接受参数
        int aid = Parser.parseInt(request.getParameter("aid"), 0);
        String acct = Parser.parseString(request.getParameter("acct"), "");
        int intent = Parser.parseInt(request.getParameter("intent"), -1);
        int currentPage = Parser.parseInt(request.getParameter("currentPage"), 1);
        int size = Parser.parseInt(request.getParameter("size"), 1);

        Log.logStd("acct = %s",acct);

        //注册时间
        boolean regTimeCheck = Parser.parseBoolean(request.getParameter("regTimeCheck"), false);
        //领取时间
        boolean receiveTimeCheck = Parser.parseBoolean(request.getParameter("receiveTimeCheck"), false);

        Boolean acctTableFirst = regTimeCheck;

        //这种情况要到acctStatus筛选一次
        if (acctTableFirst) {
            ParamMatcher bssMatcher = new ParamMatcher();
            //注册时间筛选
            if (regTimeCheck) {
                String regTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("regTimeStart"), 0), "yyyy-MM-dd") + " 00:00:00";
                String regTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("regTimeEnd"), 0), "yyyy-MM-dd") + " 23:59:59";
                bssMatcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.GE, regTimeStart);
                bssMatcher.and(BssStatDef.Info.REG_TIME, ParamMatcher.LT, regTimeEnd);
            }

        }

        //hdActivityGuidance 条件
        ParamMatcher hdMatcher = new ParamMatcher();
        //aid筛选
        if (aid != 0) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.AID, ParamMatcher.EQ, aid);
        }
        if (!acct.isEmpty()) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.ACCT,ParamMatcher.EQ,acct);
        }
        //标记筛选
        if (intent != -1) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.INTENT, ParamMatcher.EQ, intent);
        }

        //领取时间筛选
        if (receiveTimeCheck) {
            String receiveTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("receiveTimeStart"), 0), "yyyy-MM-dd") + " 00:00:00";
            String receiveTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("receiveTimeEnd"), 0), "yyyy-MM-dd") + " 23:59:59";
            Log.logStd("receive time start = %s , end = %s", receiveTimeStart, receiveTimeEnd);
            hdMatcher.and(HdPmOptDef.ActGuiLib.RECEIVE_TIME, ParamMatcher.GE, receiveTimeStart);
            hdMatcher.and(HdPmOptDef.ActGuiLib.RECEIVE_TIME, ParamMatcher.LT, receiveTimeEnd);
        }

        //查询指导库
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdActivityGuidance";
        selectArg.field = "aid,acct,tag,receiveTime,intent,updateTime";
        selectArg.searchArg.matcher = hdMatcher;
        selectArg.searchArg.start = (currentPage - 1) * size;
        selectArg.searchArg.limit = size;
        selectArg.searchArg.totalSize = new Ref<Integer>();
        Log.logStd("sql = %s", selectArg.getSql());
        Dao dao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            dataList = dao.select(selectArg);
        } finally {
            dao.close();
        }
        int totalCount = selectArg.searchArg.totalSize.value;

        aidList.clear();
        for (Param p : dataList) {
            aidList.add(p.getInt(HdPmOptDef.ActGuiLib.AID, 0));
        }

        //备注
        selectArg = new Dao.SelectArg();
        selectArg.table = "corpMark";
        selectArg.field = "aid,mark";
        selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
        FaiList<Param> markInfo = new FaiList<Param>();
        dao = WebOss.getOssBsDao();
        try {
            markInfo = dao.select(selectArg);
        } finally {
            dao.close();
        }

        //acctStatus信息
        selectArg = new Dao.SelectArg();
        selectArg.table = "acctStatus";
        selectArg.field = "aid,name,openHdTime,mobile,regTime";
        selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
        FaiList<Param> acctInfo = new FaiList<Param>();
        dao = WebOss.getBssMainDao();
        try {
            acctInfo = dao.select(selectArg);
        } finally {
            dao.close();
        }

        //最后登录时间信息2019
        selectArg = new Dao.SelectArg();
        selectArg.table = "hdLogin2019";
        selectArg.field = "aid,from_unixtime(max(time),'%Y-%m-%d %H:%i:%s') as loginTime";
        selectArg.group = "aid";
        selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);

        FaiList<Param> loginInfo2019 = new FaiList<Param>();
        dao = WebOss.getBssMainDao();
        try {
            loginInfo2019 = dao.select(selectArg);
        } finally {
            dao.close();
        }

        //最后登录时间信息2018
        selectArg = new Dao.SelectArg();
        selectArg.table = "hdLogin2018";
        selectArg.field = "aid,from_unixtime(max(time),'%Y-%m-%d %H:%i:%s') as loginTime";
        selectArg.group = "aid";
        selectArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);

        FaiList<Param> loginInfo2018 = new FaiList<Param>();
        dao = WebOss.getBssMainDao();
        try {
            loginInfo2018 = dao.select(selectArg);
        } finally {
            dao.close();
        }

        //oss域名
        String domain = WebHdOss.getOssDomainUrl();

        //数据翻译&组合
        for (Param p : dataList) {
            int innerAid = p.getInt(HdPmOptDef.ActGuiLib.AID, 0);

            //aidUrl
            String aidUrl = domain + "/index.jsp?t=hdSale&u=/cs/corp.jsp?aid=" + innerAid;
            p.setString("aidUrl", aidUrl);

            //领取时间
            Calendar receiveTime = p.getCalendar(HdPmOptDef.ActGuiLib.RECEIVE_TIME);
            if (receiveTime != null) {
                String receive = Parser.parseDateString(receiveTime, "yyyy-MM-dd HH:mm:ss");
                p.setString(HdPmOptDef.ActGuiLib.RECEIVE_TIME, receive);
            } else {
                p.setString(HdPmOptDef.ActGuiLib.RECEIVE_TIME, "无");
            }

            //更新时间
            Calendar updateTime = p.getCalendar(HdPmOptDef.ActGuiLib.UPDATE_TIME);
            if (receiveTime != null) {
                String update = Parser.parseDateString(receiveTime, "yyyy-MM-dd HH:mm:ss");
                p.setString(HdPmOptDef.ActGuiLib.UPDATE_TIME, update);
            } else {
                p.setString(HdPmOptDef.ActGuiLib.UPDATE_TIME, "无");
            }

            //标记
            String innerIntent = HdPmOptDef.ActGuiLib.getIntentName(p.getInt(HdPmOptDef.ActGuiLib.INTENT, 0));
            p.setString(HdPmOptDef.ActGuiLib.INTENT, innerIntent);

            //备注
            Param innerInfo = Misc.getFirst(markInfo, "aid", innerAid, new Param());
            if (!innerInfo.isEmpty()) {
                p.assign(innerInfo, "mark");
            }

            //企业名称，电话，注册时间
            innerInfo = Misc.getFirst(acctInfo, "aid", innerAid, new Param());
            if (!innerInfo.isEmpty()) {
                p.assign(innerInfo, "name");
                p.assign(innerInfo, "mobile");
                p.setString("regTime", Parser.parseDateString(innerInfo.getInt("regTime", 0), "yyyy-MM-dd HH:mm:ss"));
            }

            //登录时间
            innerInfo = Misc.getFirst(loginInfo2019, "aid", innerAid, new Param());
            if (!innerInfo.isEmpty()) {
                p.assign(innerInfo, "loginTime");
            } else {
                innerInfo = Misc.getFirst(loginInfo2018, "aid", innerAid, new Param());
                if (!innerInfo.isEmpty()) {
                    p.assign(innerInfo, "loginTime");
                } else {
                    p.setString("loginTime", "无");
                }
            }
        }

        result.setList("dataList", dataList);
        result.setInt("total", totalCount);

        return result.toJson();
    }

%>

<%!
    //排除不下发的销售
    private FaiList<Integer> removeNotAllotAid(FaiList<Integer> aidList) throws Exception {
        Log.logStd("before remove aid list size = %d", aidList.size());

        //1.排除已经领取的
        FaiList<Param> removeList = new FaiList<Param>();
        Dao affairDao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdActivityGuidance";
            sltArg.field = "aid";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
            removeList = affairDao.select(sltArg);
            if (removeList == null) {
                Log.logStd("hdActivityGuidance removeList is null err");
                return new FaiList<Integer>();
            }
        } finally {
            affairDao.close();
        }
        for (Param p : removeList) {
            aidList.remove(p.getInt("aid", 0));
        }
   /*
        //2.排除互动销售领取的
        Dao ossBs = WebOss.getOssBsDao();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdSaleRecord";
            sltArg.field = "aid";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
            removeList = ossBs.select(sltArg);
        } finally {
            ossBs.close();
        }
        if (removeList == null) {
            Log.logStd("hdSaleRecord removeList is null err");
        }
        for (Param p : removeList) {
            aidList.remove(p.getInt("aid", 0));
        }

        //排除品牌商
        BrandCli cli = new BrandCli(Core.getFlow());
        if (!cli.init()) {
            Log.logErr(Errno.ARGS_ERROR, "BrandCli init error");
            Log.logStd("init BrandCli err");
            return new FaiList<Integer>();
        }
        SearchArg searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(BrandDef.BrandClient.AID, ParamMatcher.IN, aidList);
        int rt = cli.searchBrandClient(searchArg, removeList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "searchBrandClient list err;");
            return new FaiList<Integer>();
        }
        for (Param p : removeList) {
            aidList.remove(p.getInt("aid", 0));
        }

        //排除代理商
        AgcyCli agcyCli = new AgcyCli(Core.getFlow());
        if (!agcyCli.init()) {
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "init AgcyCli err;flow=%d");
            return new FaiList<Integer>();
        }
        rt = agcyCli.getBanClientForSale(aidList, removeList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "get agcyClient list err;");
            return new FaiList<Integer>();
        }
        for (Param p : removeList) {
            aidList.remove(p.getInt("aid", 0));
        }

        //排除建站领取
        ossBs = WebOss.getOssBsDao();
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "acctPreSale";
            sltArg.field = "aid";
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, aidList);
            sltArg.searchArg.matcher.and(PreSaleDef.Info.SALES_ACCT, ParamMatcher.NE, "");
            removeList = ossBs.select(sltArg);
        } finally {
            ossBs.close();
        }
        for (Param p : removeList) {
            aidList.remove(p.getInt("aid", 0));
        }

        //排除增值领取
        PreSaleDjCli djCli = new PreSaleDjCli(1);
        if (!djCli.init()) {
            rt = Errno.ARGS_ERROR;
            Log.logErr(rt, "init PreSaleDjCli err;flow=%d");
            return new FaiList<Integer>();
        }
        FaiList<String> fieldList = new FaiList<String>();
        fieldList.add(PreSaleDjDef.Info.AID);
        searchArg = new SearchArg();
        searchArg.matcher = new ParamMatcher(PreSaleDjDef.Info.AID, ParamMatcher.IN, aidList);
        FaiList<Param> djSaleList = new FaiList<Param>();
        FaiList<Integer> removeStatusList = new FaiList<Integer>();
        removeStatusList.add(PreSaleDjDef.Status.DEAL);
        removeStatusList.add(PreSaleDjDef.Status.VIP);
        removeStatusList.add(PreSaleDjDef.Status.PERSON);
        searchArg.matcher.and(PreSaleDjDef.Info.STATUS, ParamMatcher.IN, removeStatusList);
        rt = djCli.getPreSaleListByFields(fieldList, searchArg, djSaleList);
        if (rt != Errno.OK && rt != Errno.NOT_FOUND) {
            Log.logErr(rt, "get djSaleList list err;");
            return new FaiList<Integer>();
        }
        for (Param p : djSaleList) {
            aidList.remove(p.getInt(PreSaleDjDef.Info.AID, 0));
        }
      */
        Log.logStd("after remove aid list size = %d", aidList.size());

        return aidList;
    }
%>

<%!
    /**发放指导库资源
     * 必须是付费用户，必须不是代理商，没有在指导库中
     * （2019-08-21 update）18年8月1，到19年4月28登陆过	
     * 满足其一：
     * 1.截止到今天为止3个月未登录
     * 2.以前做过活动，但活动效果不佳（浏览人数连续超过3场以上，少于100人）
     * 3.付费或已续费用户超过3个月及以上没有做过活动
     */
    public String setActivityGuidance(HttpServletRequest request) throws Exception {

        int sid = Session.getSid();  //当前登录员工账号
        boolean adm = WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.ALL, true);   //管理员

        //接收参数
        boolean loginTimeCheck = Parser.parseBoolean(request.getParameter("loginTimeCheck"), false);
        String sacct = Parser.parseString(request.getParameter("sacct"), "");
        int allotCount = Parser.parseInt(request.getParameter("allotCount"), 0);
        if (sacct.equals("") || allotCount <= 0) {
            return "{\"success\":false, \"msg\":\"请输入正确参数\"}";
        }
        Log.logStd("li test sid=%s,sacct=%s,allotCount=%s", sid, sacct, allotCount);
        if (sid != 112 && sid != 1224 && sid != 1355 && sid != 1535 && sid != 1610) {
            return "{\"success\":false, \"msg\":\"没有权限\"}";
        }

        FaiList<Integer> aidList = new FaiList<Integer>();   //存放符合条件的aid
        FaiList<Integer> allotedAidList = new FaiList<Integer>(); //存放已经被分配在hdActivityGuidance的aid,防止批量插入失败
        FaiList<Integer> hdGameAidList = new FaiList<Integer>();     //存放有做过活动，3场以上浏览人数少于100
        Set<Integer> tmpSet =  new HashSet<Integer>();//去重,有可能同一个aid满足多个条件。
        FaiList<Integer>  payAidList = new FaiList<Integer>();    // 存付费用户
		 FaiList<Integer>  doNotGameAidList = new FaiList<Integer>();    // 两个月以上没做过活动
        //1.hdprof表拿出一些aid进行筛选
        FaiList<Param> resource = new FaiList<Param>();
        Dao hdDao = WebHdOss.getHdSlave();
        try {
            //非免费版，非代理商，先选出1W条aid
            String sql = "select aid from hdProf where ver > 0 and agentAid = 0 and createTime >= '2017-01-01 00:00:00' ORDER BY createTime  DESC limit 40000 ";
            resource = hdDao.executeQuery(sql);
            if (resource == null) {
                Log.logStd("resource is null err");
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        } finally {
            hdDao.close();
        }
        for (Param p : resource) {
            allotedAidList.add(p.getInt("aid", 0));
            
        }
        Log.logStd("liqin before remove allotedAidList size = %d", allotedAidList.size());

        HashSet<Integer> loginSet18_19 = new HashSet<Integer>();
        if(loginTimeCheck) {
            FaiList<Param> login18List = new FaiList<Param>();
            FaiList<Param> login19List = new FaiList<Param>();

            String loginTimeStart = Parser.parseDateString(Parser.parseInt(request.getParameter("loginTimeStart"), 0), "yyyy-MM-dd") + " 00:00:00";
            String loginTimeEnd = Parser.parseDateString(Parser.parseInt(request.getParameter("loginTimeEnd"), 0), "yyyy-MM-dd") + " 23:59:59";
            Log.logStd("loginTimeStart=%s, loginTimeEnd=%s", loginTimeStart, loginTimeEnd);
            long startDate = Parser.parseCalendar(loginTimeStart, "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000;
            long endDate = Parser.parseCalendar(loginTimeEnd, "yyyy-MM-dd HH:mm:ss").getTimeInMillis() / 1000;

            Log.logStd("startDate=%s, endDate=%s", startDate, endDate);

            //（2019-08-26 update）现在登录时间段在前端配置
            Dao bssMain1 = null;
            try {
                bssMain1 = WebOss.getBssMainDao();
                Dao.SelectArg sltArg = new Dao.SelectArg();
                sltArg.table = "hdLogin2018";
                sltArg.field = "distinct(aid) as aid";
                sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, allotedAidList);
                sltArg.searchArg.matcher.and("time", ParamMatcher.GE, startDate);
                sltArg.searchArg.matcher.and("time", ParamMatcher.LE, endDate);

                login18List = bssMain1.select(sltArg);
                if (login18List == null) {
                    Log.logStd("login188888List is null");
                    return "{\"success\":false, \"msg\":\"login18List is null\"}";
                }
                Log.logStd("login18List size:%s", login18List.size());

                for (Param p : login18List) {
                    loginSet18_19.add(p.getInt("aid", 0));
                }

                sltArg.table = "hdLogin2019";
                sltArg.field = "distinct(aid) as aid";
                sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, allotedAidList);
                sltArg.searchArg.matcher.and("time", ParamMatcher.GE, startDate);
                sltArg.searchArg.matcher.and("time", ParamMatcher.LE, endDate);
                login19List = bssMain1.select(sltArg);

                if (login19List == null) {
                    Log.logStd("login19List is null");
                    return "{\"success\":false, \"msg\":\"login19List is null\"}";
                }
                Log.logStd("login199999List size:%s", login19List.size());

                for (Param p : login19List) {
                    loginSet18_19.add(p.getInt("aid", 0));
                }

            } catch (Exception e) {
                Log.logStd("get hdLogin2018~9 err : %s", e);
            } finally {
                if (bssMain1 != null) {
                    bssMain1.close();
                }
            }
        }
        Log.logStd("loginSet18_19 size=%s", loginSet18_19.size());

        allotedAidList.clear();
        for(Integer aid : loginSet18_19){
        	allotedAidList.add(aid);
        }
        
        //3.排除代理商，品牌商，建站领取，销售领取
        allotedAidList = removeNotAllotAid(allotedAidList);
        if (allotedAidList.isEmpty()) {
            Log.logStd("liqin aid list is empty");
            return "{\"success\":false, \"msg\":\"操作失败\"}";
        }
		for(Integer p:allotedAidList){
			hdGameAidList.add(p);
            payAidList.add(p);
			doNotGameAidList.add(p);
		}
		

        Log.logStd("liqin after remove allotedAidList size = %d ", allotedAidList.size());
   
        //4.是否三个月未登陆
        FaiList<Param> loginList = new FaiList<Param>();
        Dao bssMain = WebOss.getBssMainDao();
        Log.logStd("bssMain dao = %s", bssMain);
        int threeMonthAgo = HdOssUtil.getIntDate(-90, 0, 0, 0);
        try {
            Dao.SelectArg sltArg = new Dao.SelectArg();
            sltArg.table = "hdLogin2019";
            sltArg.field = "aid,max(time) as time";
            sltArg.group = "aid having max(time) > " + threeMonthAgo;
            sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, allotedAidList);
            loginList = bssMain.select(sltArg);
            if (loginList == null) {
                Log.logStd("loginList is null");
                return "{\"success\":false, \"msg\":\"loginList is null\"}";
            }
        } finally {
            bssMain.close();
        }
        for (Param p : loginList) {
            allotedAidList.remove(p.getInt("aid", 0));
        }

       
        Log.logStd("li test size of aidList=%s", allotedAidList.size());
        for(Integer aid : allotedAidList){
        	tmpSet.add(aid);
        }

        //5.连续3场活动浏览人数少于100人
        bssMain = WebOss.getBssMainDao();
        FaiList<Param> hdGameList = new FaiList<Param>();//存放办过3场或以上活动
        FaiList<Param> hdGameViewList = new FaiList<Param>(); //存放每个aid每一场的浏览情况。
        try{
        	Dao.SelectArg sltArg = new Dao.SelectArg();
        	sltArg.table ="hdTemplateGame";
        	sltArg.field ="aid";
        	sltArg.group ="aid having count(*)>=3";//办过3场以上活动
        	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, hdGameAidList);
        	hdGameList = bssMain.select(sltArg);
        	if(hdGameList == null ){
        		Log.logStd("li test select  hdTemplateGame err");
        	}
        	Log.logStd("li test size of over three times  hdGameList =%s",hdGameList.size());
        	hdGameAidList.clear();
        	for(Param p : hdGameList){
        		hdGameAidList.add(p.getInt("aid",0));
        	}
        	sltArg = new Dao.SelectArg();
         	sltArg.table ="hdTemplateGame";
         	sltArg.field ="aid,viewNum";
         	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, hdGameAidList);
         	hdGameViewList = bssMain.select(sltArg);
        }finally{
        	bssMain.close();
        }
        Log.logStd("li test size of hdGameViewList =%s",hdGameViewList.size());
        hdGameAidList.clear();
        for(Param p : hdGameList){
        	hdGameAidList.add(p.getInt("aid",0));
        }
        
        Param viewParam = new Param(true);
        //是否连续有3场小于100人浏览:
        for(Param p : hdGameViewList){
        	int aid = p.getInt("aid",0);
        	int viewNum = p.getInt("viewNum",0);
        	int count = 0;//活动少于100人浏览的场数。
        	if(viewParam.getInt(aid+"",-1) == -1){
        		viewParam.setInt(aid+"",0);
        	}
            count = viewParam.getInt(aid+"",0);
            if(count < 3){ //如果已经符合连续3场小于100人就不需要走下面的逻辑。
            	if(viewNum<100){
            	   	viewParam.setInt(aid+"",++count);
            	}else{
            		viewParam.setInt(aid+"",0);//如果有1场大于100人就重置为0
            	}
            }
        }
        for(Param p : hdGameList){
        	 Integer aid = p.getInt("aid",0);
             if(viewParam.getInt(aid+"",0)>=3){
            	 if(tmpSet.add(aid)){
            	    allotedAidList.add(aid);
            	 }
             }
        }
		
		//6.三个月内做过活动的排除，即拿三个月以上没做过活动的
		FaiList<Param>  doActivityList = new FaiList<Param>();    // 存三个月内做过活动的
		bssMain = WebOss.getBssMainDao();
        try{
        	Dao.SelectArg sltArg = new Dao.SelectArg();
        	sltArg.table ="hdTemplateGame";
        	sltArg.field ="DISTINCT(aid)";
			sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, doNotGameAidList);
        	sltArg.searchArg.matcher.and("createTime", ParamMatcher.GE, threeMonthAgo);
        	doActivityList = bssMain.select(sltArg);
        	if(doActivityList == null ){
				doActivityList=new FaiList<Param>();
        		Log.logStd("li test select  hdTemplateGame err");
        	}
        	Log.logStd("liqin threeMonthUp donot activity doActivityList =%s",doActivityList.size());
      
        }finally{
        	bssMain.close();
        }
		for(Param p:doActivityList){
			doNotGameAidList.remove(p.getInt("aid",0));
		}
		for(Integer aidTemp:doNotGameAidList){
			if(tmpSet.add(aidTemp)){
            	    allotedAidList.add(aidTemp);
            }
		}
		
        allotedAidList = removeNotAllotAid(allotedAidList);
        Log.logStd("liqin size of aidList=%s", allotedAidList.size());
        
        /* //付费用户
        bssMain = WebOss.getBssMainDao();
        FaiList<Param> orderList = new FaiList<Param>();
        try{
        	Dao.SelectArg sltArg = new Dao.SelectArg();
        	sltArg.table ="acctOrderItem";
        	sltArg.field ="aid";
        	sltArg.searchArg.matcher = new ParamMatcher("aid", ParamMatcher.IN, payAidList);
        }finally{
        	bssMain.close();
        } */
        
        //7.下发资源
        int rt = averageResource(allotedAidList, allotCount,sacct);
        if (rt != Errno.OK) {
            Log.logStd("liqin batchInsert err");
        }
 
        return "{\"succes1s\":true, \"msg\":\"操作成功\"}";
    }

%>


<%!
    //平均分配
    private int averageResource(FaiList<Integer> aidList, int allotCount,String sacct) throws Exception {

        //1.获取要下发的指导人员
        LinkedBlockingQueue<String> pmQueue = new LinkedBlockingQueue<String>();
        Dao affairDao = WebHdOss.getHdOssAffairDaoMaster();
        try {
        	  if(sacct.equals("all")){
        		  String sql = "select * from hdPmAndOpt";
                  FaiList<Param> pmInfo = affairDao.executeQuery(sql);
                  
                  for (Param p : pmInfo) {
                      pmQueue.add(p.getString("acct", ""));
                  } 
              }else{
              	pmQueue.add(sacct);
              }
            
        } finally {
            affairDao.close();
        } 
      


        //2.平均分配
        FaiList<Param> insertList = new FaiList<Param>();
        int count = 0;
        for (int aid : aidList) {
            String acct = pmQueue.take();
            Param info = new Param();
            info.setString(HdPmOptDef.ActGuiLib.ACCT, acct);
            info.setInt(HdPmOptDef.ActGuiLib.AID, aid);
            info.setCalendar(HdPmOptDef.ActGuiLib.RECEIVE_TIME, Calendar.getInstance());
            info.setCalendar(HdPmOptDef.ActGuiLib.UPDATE_TIME, Calendar.getInstance());
            insertList.add(info);
            pmQueue.put(acct);
            if (++count >= allotCount) {
                break;
            }
        }
        Log.logStd("insert count=%s", count);
        int rt = Errno.OK;
        affairDao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            rt = affairDao.batchInsert("hdActivityGuidance", insertList);
        } finally {
            affairDao.close();
        }
        return rt;
    }
%>

<%!
    //获取活动指导库页面信息
    private String getPageMessage(HttpServletRequest request) throws Exception {

        Param info = new Param();

        //意向度
        FaiList<Integer> intentList = HdPmOptDef.ActGuiLib.getIntentList();
        FaiList<Param> intentInfoList = new FaiList<Param>();
        for (int intent : intentList) {
            String intentName = HdPmOptDef.ActGuiLib.getIntentName(intent);
            Param temp = new Param();
            temp.setString("name", intentName);
            temp.setInt("label", intent);
            intentInfoList.add(temp);
        }
        info.setList("intent", intentInfoList);

        //领取人
        FaiList<Param> pmList = getPmList();
        FaiList<Param> pmAcctList = new FaiList<Param>();
        Param pm = new Param();
        pm.setString("name", "all");
        pm.setString("label", "all");
        pmAcctList.add(pm);
        for (Param p : pmList) {
            String acct = p.getString(HdPmOptDef.PmAndOpt.ACCT);
            Param temp = new Param();
            temp.setString("name", acct);
            temp.setString("label", acct);
            pmAcctList.add(temp);
        }
        info.setList("pm", pmAcctList);

        Log.logStd("info = %s", info);
        return info.toString();
    }

    /**
     * 获取Pm和运营列表
     */
    private FaiList<Param> getPmList() {
        FaiList<Param> info = null;
        String sql = "select * from hdPmAndOpt";
        Dao dao = null;
        try {
            dao = WebHdOss.getHdOssAffairDaoSlave();
            info = dao.executeQuery(sql);
        } finally {
            dao.close();
        }
        if (info == null) {
            Log.logStd("get hdPmAndOpt null err");
        }
        return info;
    }


%>


<%!
    //更改用标记
    private String changeIntent(HttpServletRequest request) throws Exception {
        int intent = Parser.parseInt(request.getParameter("intent"), 0);
        int aid = Parser.parseInt(request.getParameter("aid"), 0);

        ParamUpdater updater = new ParamUpdater();
        updater.getData().setInt(HdPmOptDef.ActGuiLib.INTENT, intent);
        Dao dao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            int rt = dao.update("hdActivityGuidance", updater, new ParamMatcher(HdPmOptDef.ActGuiLib.AID, ParamMatcher.EQ, aid));
            if (rt != Errno.OK) {
                Log.logStd("update err , aid = %d , sid = %d", aid, Session.getSid());
                return "{\"success\":false, \"msg\":\"操作失败\"}";
            }
        } finally {
            dao.close();
        }
        return "{\"success\":true, \"msg\":\"更新成功\"}";
    }

%>

<%!
    // 回显释放数量
    private String checkReleaseCount(HttpServletRequest request) throws Exception {

        // 接受参数
        String acct = Parser.parseString(request.getParameter("acct"), "");
        int intent = Parser.parseInt(request.getParameter("intent"), -1);

        //hdActivityGuidance 条件
        ParamMatcher hdMatcher = new ParamMatcher();
        //aid筛选
        if (!acct.isEmpty()) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.ACCT,ParamMatcher.EQ,acct);
        }
        //标记筛选
        if (intent != -1) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.INTENT, ParamMatcher.EQ, intent);
        }

        //查询指导库
        Dao.SelectArg selectArg = new Dao.SelectArg();
        selectArg.table = "hdActivityGuidance";
        selectArg.searchArg.matcher = hdMatcher;
        selectArg.searchArg.totalSize = new Ref<Integer>();

        Dao dao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            FaiList<Param> dataList = dao.select(selectArg);
        } finally {
            dao.close();
        }
        int totalCount = selectArg.searchArg.totalSize.value;
        return "{\"success\":true, \"count\":" +totalCount+ "}";
    }

%>

<%!
    // 释放活动指导库数据
    private String releaseActivityGuidance(HttpServletRequest request) throws Exception {
        int sid = Session.getSid();
        if(sid != 112 && sid != 1535 && sid != 1355){
            return "{\"success\":true, \"msg\":\"没有权限\"}";
        }

        // 接受参数
        String acct = Parser.parseString(request.getParameter("acct"), "");
        int intent = Parser.parseInt(request.getParameter("intent"), -1);

        //hdActivityGuidance 条件
        ParamMatcher hdMatcher = new ParamMatcher();
        //aid筛选
        if (!acct.isEmpty()) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.ACCT,ParamMatcher.EQ,acct);
        }
        //标记筛选
        if (intent != -1) {
            hdMatcher.and(HdPmOptDef.ActGuiLib.INTENT, ParamMatcher.EQ, intent);
        }

        // 防止没有条件
        if(hdMatcher.isEmpty()){
            return "{\"success\":false, \"count\":\"释放失败，请设置条件！\"}";
        }

        Dao dao = WebHdOss.getHdOssAffairDaoMaster();
        try {
            dao.setAutoCommit(false);
            int rt = dao.delete("hdActivityGuidance", hdMatcher);
            if(rt != Errno.OK){
                dao.rollback();
                Log.logStd("delete err: rt=%s", rt);
                return "{\"success\":false, \"msg\":\"释放失败，数据库异常！\"}";
            }
            dao.commit();
        }catch (Exception e){
            dao.rollback();
            Log.logStd("catch err: %s", e);
            return "{\"success\":false, \"msg\":\"释放失败，捕获异常！\"}";
        }finally {
            dao.close();
        }

        return "{\"success\":true, \"msg\":\"释放成功\"}";
    }
%>


<%
    //cmd处理
    String output = "";
    try {
        String cmd = request.getParameter("cmd");
        Log.logStd("li test cmd = %s", cmd);
        if (cmd == null) {
            output = "no cmd find";
        } else if (cmd.equals("getActivityList")) {
            output = getActivityList(request);
        } else if (cmd.equals("setActivityGuidance")) {
            output = setActivityGuidance(request);
        } else if (cmd.equals("getPageMessage")) {
            output = getPageMessage(request);
        } else if (cmd.equals("changeIntent")) {
            output = changeIntent(request);
        } else if (cmd.equals("checkReleaseCount")) {
            output = checkReleaseCount(request);
        } else if (cmd.equals("releaseActivityGuidance")) {
            output = releaseActivityGuidance(request);
        }

    } catch (Exception e) {
        PrintUtil.printErr(e);
        PrintUtil.printStackTrace(e, 1, "hdoss");
        output = WebOss.checkAjaxException(e);
    }
    out.print(output);


%>