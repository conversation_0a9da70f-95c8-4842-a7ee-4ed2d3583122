<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.webhdoss.*"%>
<%@ page import="fai.webhdoss.app.*"%>
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>HD-OSS</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
	</head>
	<script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
	<style type="text/css">
	</style>
	<body>
			<form method="post" action="" id = "selectedForm">
					<input type="file" id="importExcel" name="importExcel" value="导入EXCEL数据"  style="width:165px;"/>
					<input type="submit" class="btn-import" onclick="query()" value="批量aid查询"/>
			</form>
	</body>
	<script>
		function query() {
			var url = "";
			url = "/greakjackTest.jsp?cmd=65";		

			$('#selectedForm').attr("method","post");
			$('#selectedForm').attr("action",url);
			$('#selectedForm').attr("enctype","multipart/form-data");
			$('#selectedForm').submit();
		}
	</script>
</html>