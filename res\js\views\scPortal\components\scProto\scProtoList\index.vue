<template>
  <div class="sc-list">
    <el-tabs v-model="activeName">
      <el-tab-pane
        label="视频"
        :name="String(ScProductType.VIDEO)"
      ></el-tab-pane>
      <el-tab-pane
        label="图文"
        :name="String(ScProductType.IMGTEXT)"
      ></el-tab-pane>
    </el-tabs>
    <div class="mb-[20px] sc-list-header">
      <el-button
        type="primary"
        @click="addProto(ScProductType.VIDEO)"
        v-if="activeName === String(ScProductType.VIDEO)"
        >添加视频原型</el-button
      >
      <el-button type="primary" @click="addProto(ScProductType.IMGTEXT)" v-else
        >添加图文原型</el-button
      >
    </div>
    <div class="flex mb-[20px] sc-list-filter">
      <!-- 关键词搜索 -->
      <div>
        <el-input
          size="small"
          :placeholder="curPlaceholder"
          :clearable="true"
          v-model.trim="filterData.keyword"
          class="!w-[400px] mr-[30px] multiple-input"
          @keyup.enter.native="searchList"
          @clear="searchList"
        >
          <template slot="prepend">
            <el-select v-model="filterData.key" placeholder="请选择">
              <el-option
                v-for="item in filterTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </template>
        </el-input>
      </div>
      <!-- 上架状态 -->
      <div class="mr-[30px]">
        <span class="mr-[5px] text-[14px]">状态:</span>
        <el-select v-model="filterData.status" placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </div>
      <!-- 创建时间 -->
      <div class="mr-[30px]">
        <span class="mr-[5px] text-[14px]">创建时间:</span>
        <el-date-picker
          class="!w-[240px]"
          :pickerOptions="pickerOptions"
          size="small"
          v-model="filterData.timeString"
          type="daterange"
        ></el-date-picker>
      </div>

      <el-button size="small" type="primary" @click="searchList"
        >搜索</el-button
      >
    </div>
    <div class="sc-list-table">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="原型ID" width="80" />
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="apiKey" label="APIKEY" width="280" />
        <el-table-column prop="updateTime" label="最近编辑时间" width="200" />
        <el-table-column prop="createTime" label="创建时间" width="200" />
        <el-table-column prop="statusName" label="状态" width="80" />
        <el-table-column fixed="right" label="操作栏" width="150">
          <template slot-scope="scope">
            <el-button type="text" @click="editProto(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.status === ProtoStatus.NORMAL"
              @click="abandonedProto(scope.row)"
              >废弃</el-button
            >
            <el-button
              type="text"
              v-if="scope.row.status === ProtoStatus.ABANDON"
              @click="restoreProto(scope.row)"
              >恢复</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt-[20px] flex justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageConfig.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageConfig.pageLimit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageConfig.total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { ScProductType, ProtoStatus } from "@/views/scPortal/config/index.js";
import {
  getScProtoList,
  setScProtoStatus,
} from "@/views/scPortal/api/scProto.js";
import { checkPassOperateFunc } from "@/views/scPortal/utils/index.js";

export default {
  name: "ScProtoList",
  props: {
    isReloadList: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ScProductType,
      ProtoStatus,
      activeName: String(ScProductType.VIDEO), // 默认值
      pageConfig: {
        pageNo: 1,
        pageLimit: 10,
        total: 0,
      },
      filterData: {
        key: "all", // 类型
        status: ProtoStatus.NORMAL, // 状态
        keyword: "", // 关键字
        timeString: "", // 时间
      },
      filterTypeOptions: [
        {
          value: "all",
          label: "全部",
          placeholder: "请输入原型名称/原型ID/APIKEY"
        },
        {
          value: "name",
          label: "原型名称",
          placeholder: "请输入原型名称",
        },
        {
          value: "id",
          label: "原型ID",
          placeholder: "请输入原型ID",
        },
        {
          value: "apiKey",
          label: "APIKEY",
          placeholder: "请输入APIKEY",
        },
      ],
      statusOptions: [
        {
          value: -1,
          label: "全部",
        },
        {
          value: ProtoStatus.NORMAL,
          label: "使用中",
        },
        {
          value: ProtoStatus.ABANDON,
          label: "已废弃",
        },
      ],
      tableData: [],
      pickerOptions: {
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近七天",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近一个月",
            onClick(picker) {
              const end = new Date(new Date().setHours(0, 0, 0, 0));
              const start = new Date(new Date().setHours(0, 0, 0, 0));
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
    };
  },
  created() {
    const storeActiveName = this.$store.state.scPortal.curActiveName;
    if (storeActiveName) {
      /**
       * 特殊处理：
       * 第一次切换组件后再切换回来，keep-alive没用缓存组件，
       * 而是新建了组件（应该是vue内部机制判断问题），
       * 所以特殊处理，用于第一次切换的特殊情况，保持切换到相应tab
       * 第二次就正常用缓存了
       */
      this.activeName = storeActiveName;
    }

    console.log("getScProtoList1");
    this.getScProtoList();
  },
  activated() {
    if (this.isReloadList) {
      this.getScProtoList();
    }
  },
  computed: {
    curPlaceholder() {
      return this.filterTypeOptions.find(
        (item) => item.value === this.filterData.key
      ).placeholder;
    },
  },
  watch: {
    activeName(val) {
      // 当 activeName 变化时，更新 Vuex 中的值
      this.$store.commit("scPortal/curActiveName", val);
      this.pageConfig.pageNo = 1;
      this.getScProtoList();
    },
  },
  methods: {
    /**
     * 后端取日期零点，需要加上一天
     */
    addOneDay(date) {
      if (date) {
        var dateTime = new Date(date);
        dateTime = dateTime.setDate(dateTime.getDate() + 1);
        return new Date(dateTime).getTime();
      }
      return 0;
    },
    searchList() {
      this.pageConfig.pageNo = 1;
      this.getScProtoList();
    },
    /**
     * 编辑原型
     * @param row
     */
    editProto(row) {
      this.$emit("changeComponent", "scProtoEdit", {
        editId: row.id,
      });
    },
    /**
     * 废弃原型
     * @param row
     */
    abandonedProto(row) {
      if (!checkPassOperateFunc()) {
        this.$message({
          showClose: true,
          type: 'error',
          message: '当前账号无废弃原型权限'
        });
        return;
      }
      this.$confirm("确定废弃该原型吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        setScProtoStatus({
          id: row.id,
          status: ProtoStatus.ABANDON,
        }).then((res) => {
          if (res && res.success) {
            this.$message.success("废弃成功");
            this.getScProtoList();
          }
        });
      });
    },
    /**
     * 恢复原型
     * @param row
     */
    restoreProto(row) {
      setScProtoStatus({
        id: row.id,
        status: ProtoStatus.NORMAL,
      }).then((res) => {
        if (res && res.success) {
          this.$message.success("恢复成功");
          this.getScProtoList();
        }
      });
    },
    /**
     * 添加原型
     * @param type
     */
    addProto(type) {
      this.$emit("changeComponent", "scProtoEdit", {
        createType: type,
      });
    },
    /**
     * 确认修改apikey
     * @param row
     */
    editApiKey(row) {
      row._showEditApiKey = false;
    },
    /**
     * 显示修改apikey弹窗
     * @param row
     */
    showEditApiKey(row) {
      row._showEditApiKey = true;
      row._apiKey = row.apiKey;
    },
    /**
     * 获取原型列表
     */
    getScProtoList() {
      let newParams = {
        type: Number(this.activeName), // 产品类型
        key: this.filterData.key, // 搜索类型
        keyword: this.filterData.keyword, // 搜索关键字
        pageNo: this.pageConfig.pageNo, // 当前页
        status: this.filterData.status, // 状态
        pageLimit: this.pageConfig.pageLimit, // 每页条数
      };

      if (this.filterData.timeString && this.filterData.timeString.length) {
        // 筛选时间
        let [startDate, endDate] = this.filterData.timeString;
        Object.assign(newParams, {
          createTimeStart: this.formatDate(new Date(startDate)),
          createTimeEnd: this.formatDate(new Date(this.addOneDay(endDate))),
        });
      }
      getScProtoList(newParams)
        .then((res) => {
          if (res && res.success && Array.isArray(res.data)) {
            this.tableData = res.data;
            this.pageConfig.total = res.totalSize;
          } else {
            this.tableData = [];
          }
        })
        .catch((error) => {
          console.error("获取列表失败:", error);
          this.tableData = [];
        });
    },
    handleSizeChange(size) {
      this.pageConfig.pageLimit = size;
      this.getScProtoList();
    },
    handleCurrentChange(page) {
      this.pageConfig.pageNo = page;
      this.getScProtoList();
    },
    handleTypeChange() {
      this.pageConfig.pageNo = 1;
      this.getScProtoList();
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "../../styles/tailwind.css";
.sc-list-input {
  ::v-deep input {
    height: 32px;
  }
}
.sc-list-select {
  ::v-deep .el-input__icon {
    line-height: 30px;
  }
}
.multiple-input {
  ::v-deep {
    .el-input-group__prepend {
      background: #fff;
    }
    .el-input__inner {
      color: #606266 !important;
    }
  }
}
::v-deep .el-tabs__item {
  font-size: 16px;
  font-weight: bold;
}
.el-table {
  ::v-deep .cell {
    word-break: break-word;
    white-space: pre-wrap;
  }
}
</style>
