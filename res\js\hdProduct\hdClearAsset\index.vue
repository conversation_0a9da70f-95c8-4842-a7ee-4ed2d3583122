<template>
  <div id="hdClearAsset" class="hdClearAsset">
    <h3>清空第三方礼品资产（话费和京东e卡）</h3>
    <el-form :inline="true" :model="form">
      <el-form-item label="aid：">
        <el-input
          v-model="form.clearAsset.aid"
          placeholder="填入aid"
          size="small"
        />
      </el-form-item>
      <el-form-item label="礼品类型：">
        <el-select
          v-model="form.clearAsset.giftType"
          filterable
          clearable
          placeholder="选择礼品类型"
          size="small"
        >
          <el-option
            v-for="item in giftList"
            :key="item.type"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="handleClearAssetClick"
          >清空</el-button
        >
      </el-form-item>
    </el-form>

    <el-divider class="hr" />

    <h3>清空部分第三方礼品资产（话费和京东e卡以及实物礼品件）</h3>
    <el-form :inline="true" :model="form">
      <el-form-item label="aid：">
        <el-input
          v-model="form.clearPartAsset.aid"
          placeholder="填入aid"
          size="small"
        />
      </el-form-item>
      <el-form-item label="礼品类型：">
        <el-select
          v-model="form.clearPartAsset.giftType"
          filterable
          clearable
          placeholder="选择礼品类型"
          size="small"
        >
          <el-option
            v-for="(item, index) in giftList"
            :key="index"
            :label="item.name"
            :value="item.type"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="扣除数量：">
        <el-input-number
          v-model="form.clearPartAsset.clearPartNum"
          placeholder="扣除数量"
          size="small"
          :min="0"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          size="small"
          @click="handleClearPartAssetClick"
          >清空</el-button
        >
      </el-form-item>
    </el-form>

    <el-divider class="hr" />

    <h3>礼品清空日志</h3>
    <el-table
      :data="tableData"
      stripe
      :height="tableData.length ? 500 : 'auto'"
      style="width: 100%"
    >
      <el-table-column prop="action" :min-width="200" label="操作行为" />
      <el-table-column prop="details" :min-width="370" label="操作详情" />
      <el-table-column prop="createTime" :min-width="200" label="操作时间" />
      <el-table-column prop="specifics" :min-width="150" label="明细" />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'hdClearAsset',
  data() {
    return {
      form: {
        clearAsset: {
          aid: '',
          giftType: '',
        },
        clearPartAsset: {
          aid: '',
          giftType: '',
          clearPartNum: 0,
        },
      },

      giftList: [],

      tableData: [],
    };
  },
  created() {
    this.getGiftTypeList();
    this.getDeleteFlowList();
  },
  methods: {
    handleClearAssetClick() {
      const { aid, giftType } = this.form.clearAsset;
      if (!aid || !giftType) return this.$message.error('参数错误');

      this.clearAsset(
        `/api/gift/clearAsset?aid=${aid}&isClearPart=false&giftType=${giftType}`
      );
    },

    handleClearPartAssetClick() {
      const { aid, giftType, clearPartNum } = this.form.clearPartAsset;
      if (!aid || !giftType) return this.$message.error('参数错误');
      if (clearPartNum <= 0) return this.$message.error('清空数量需要大于0');

      this.clearAsset(
        `/api/gift/clearAsset?aid=${aid}&giftType=${giftType}&isClearPart=true&clearPartNum=${clearPartNum}`
      );
    },

    request(opts) {
      return new Promise((resolve, reject) => {
        $.ajax(opts)
          .then((res) => {
            try {
              res = JSON.parse(res);
            } catch (err) {}

            const { success, msg } = res;
            if (!success) return reject(msg);

            resolve(res);
          })
          .fail(() => {
            reject();
          });
      }).catch((msg = '服务器繁忙，请稍后重试') => {
        this.$message.error(msg);

        return Promise.reject(msg);
      });
    },

    getGiftTypeList() {
      this.request({
        type: 'get',
        url: '/api/gift/getGiftTypeList',
      }).then(({ data }) => {
        const { giftListBak, jdgiftListBak } = data;

        this.giftList = [
          ...giftListBak.map((item) => {
            const type = +Object.keys(item)[0];
            return { type, name: item[type] };
          }),
          ...jdgiftListBak.map((item) => {
            const type = +Object.keys(item)[0];
            return { type, name: item[type] };
          }),
        ];
      });
    },

    clearAsset(url) {
      this.$confirm('确定要清空资产？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.request({
          type: 'post',
          url,
        }).then(() => {
          this.getDeleteFlowList();

          this.$message({
            message: '操作成功',
            type: 'success',
          });
        });
      });
    },

    getDeleteFlowList() {
      this.request({
        type: 'get',
        url: '/api/gift/getDeleteFlowList',
      }).then(({ list }) => {
        this.tableData = list.map((item) => {
          const { aid, createTime, isAll, num, staffLabel, typeBname } = item;

          return {
            action: `清空${isAll ? '全部' : '部分'}礼品：aid${aid}`,
            details: `${staffLabel} 清空礼品：${typeBname}`,
            createTime: moment(createTime).format('YYYY年MM月DD日 HH:mm:ss'),
            specifics: `清空数量：${num}`,
          };
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.hdClearAsset {
  margin: 20px;

  h3 {
    color: #535353;
  }

  .hr {
    margin-top: 0;
  }
}
</style>
