package fai.webhdoss.service;

import fai.comm.util.FaiList;
import fai.comm.util.Param;
import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scRes.ScCategoryVO;

public interface ScCategoryService {

    // 分类列表
    JsonResult getList(ScCategoryVO vo);

    // 新增
    JsonResult add(ScCategoryVO vo);

    // 更新
    JsonResult set(ScCategoryVO vo);

    // 删除
    JsonResult del(ScCategoryVO vo);


    FaiList<Param> getCategoryList(ScCategoryVO vo);

}
