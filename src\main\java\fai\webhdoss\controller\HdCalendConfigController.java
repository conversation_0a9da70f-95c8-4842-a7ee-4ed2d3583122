package fai.webhdoss.controller;


import fai.app.HdCalendConfigDef;
import fai.cli.HdOssAffairCli;
import fai.comm.util.*;
import fai.hdUtil.JsonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.*;

/**
 * 互动营销日历配置相关
 * @date 2021-12-01
 * <AUTHOR>
 */
@RestController
@Api(value = "互动营销日历配置相关")
@RequestMapping("/CalendConfig")
public class HdCalendConfigController {

    @Autowired
    HdOssAffairCli ossAffairCli;

    /**
     * 查询日历配置列表
     */
    @GetMapping("/calendConfigList")
    @ApiOperation(value = "查询所有日历配置", httpMethod = "GET")
    public JsonResult calendConfigList(@RequestParam("pageNo")@NotNull int pageNo,
                                       @RequestParam("pageLimt") @NotNull int pageLimit) throws Exception {

        Ref<List> dataListRef = new Ref<>();
        Ref<Integer> sizeRef = new Ref<>();

        int rt = ossAffairCli.HdCalendConfigList(pageNo, pageLimit, dataListRef, sizeRef);
        if (rt!=Errno.OK){
            return JsonResult.error(rt);
        }

        return JsonResult.success(new Param().setList("list", new FaiList<>(dataListRef.value)).setInt("size", sizeRef.value));
    }


    /**
     * 新增日历配置
     */
    @PostMapping("/addCalendConfig")
    @ApiOperation(value = "新增日历配置", httpMethod = "GET")
    public JsonResult addCalendConfig(@RequestParam("name") String name,
                                      @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                      @RequestParam("jumpto") String jumpto) throws Exception {

        if (name == null || date == null|| jumpto == null){
            return JsonResult.error("数据有问题");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        Param param = new Param().setString(HdCalendConfigDef.Info.NAME, name).
                setCalendar(HdCalendConfigDef.Info.DATE, calendar).
                setString(HdCalendConfigDef.Info.JUMPTO, jumpto).
                setInt(HdCalendConfigDef.Info.STATUS, 0);

        int rt = ossAffairCli.HdCalendConfigAdd(param);
        if (rt!=Errno.OK){
            return JsonResult.error(rt);
        }
        return JsonResult.success();
    }

    /**
     * 更新日历配置
     */
    @PostMapping("/updateCalendConfig")
    @ApiOperation(value = "更新日历配置", httpMethod = "GET")
    public JsonResult updateCalendConfig(@RequestParam("id") Integer id,
                                         String name,
                                         @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
                                         String jumpto,
                                         Boolean status) throws Exception {

        if (id<=0){
            return JsonResult.error("id<0");
        }

        Param param = new Param().setInt(HdCalendConfigDef.Info.ID, id);

        Optional.ofNullable(name).ifPresent(i->param.setString(HdCalendConfigDef.Info.NAME, i));
        Optional.ofNullable(jumpto).ifPresent(i->param.setString(HdCalendConfigDef.Info.JUMPTO, i));
        Optional.ofNullable(status).ifPresent(i->param.setInt(HdCalendConfigDef.Info.STATUS, status?1:0));
        Optional.ofNullable(date).ifPresent(i->{
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            param.setCalendar(HdCalendConfigDef.Info.DATE, calendar);
        });

        int rt = ossAffairCli.updateCalendConfig(param);
        if (rt!=Errno.OK){
            return JsonResult.error(rt);
        }
        return JsonResult.success(param);
    }


    /**
     * 删除某一个日历配置
     */
    @GetMapping("/deleteCalendConfig")
    @ApiOperation(value = "删除某一个日历配置", httpMethod = "GET")
    public JsonResult deleteCalendConfig(@RequestParam("id")@NotNull Integer id) throws Exception {
        if (id<=0){
            return JsonResult.error("id<0");
        }

        int rt = ossAffairCli.deleteCalendConfig(id);
        if (rt!=Errno.OK){
            return JsonResult.error(rt);
        }
        return JsonResult.success();
    }




    /**
     * 查询某一个时间段的日历配置
     */
    @GetMapping("/CalendConfigListPeriod")
    @ApiOperation(value = "新增日历配置", httpMethod = "GET")
    public JsonResult addCalendConfig(@RequestParam("startTime")@NotNull @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                      @RequestParam("endTime")@NotNull @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime) throws Exception {

        Calendar start = Calendar.getInstance();
        start.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        Param param = new Param().setCalendar("start", start).setCalendar("end", end);
        Ref<FaiList<Param>> dataListRef = new Ref<>();
        Ref<Integer> sizeRef = new Ref<>();

        int rt = ossAffairCli.HdCalendConfigListPeriod(param,dataListRef,sizeRef);
        if (rt!=Errno.OK){
            return JsonResult.error(rt);
        }


        return JsonResult.success(new Param().setList("list", new FaiList<>(dataListRef.value)).setInt("size", sizeRef.value));
    }



}
