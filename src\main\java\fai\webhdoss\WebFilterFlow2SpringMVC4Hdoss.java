package fai.webhdoss;

import fai.comm.util.Parser;
import fai.web.Core;
import fai.web.Response;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 专门处理 Spring Api 的流水号的 过滤器
 * <AUTHOR>
 *
 */
public class WebFilterFlow2SpringMVC4Hdoss implements Filter {
	
	public static final String HEADER_FLOW_KEY = "FAI-W-FLOW";//流水号header key
	public static final String HEADER_AID_KEY = "FAI-W-AGENT_AID";//aid header key
	
	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest requestTmp, ServletResponse responseTmp, FilterChain chain) throws ServletException, IOException {
		if (requestTmp == null || responseTmp == null) {
			return;
		}
		
		if (requestTmp instanceof HttpServletRequest && responseTmp instanceof HttpServletResponse) {
			HttpServletRequest request = (HttpServletRequest) requestTmp;
			HttpServletResponse response = (HttpServletResponse) responseTmp;
			
			//设置流水号
			response.addIntHeader(HEADER_FLOW_KEY, Core.getFlow());
			//设置aid header
			response.addIntHeader(HEADER_AID_KEY, Core.getAid());
			
			
			//处理request带有_flow参数的cookies。_flow是用来init core 流水号的。init的逻辑在fai.web.CoreCenter.java setRequest方法里
			int flow = Parser.parseInt(request.getParameter("_flow"), 0);
			if(flow < 0){
				//flow 小于0，删除cookies的值
				Response.addCookie(response, "_flow", "");
			}else if(flow > 0){
				//flow 大于0，set cookies
				Response.addCookie(response, "_flow", String.valueOf(flow));
			}
			
		}
		chain.doFilter(requestTmp, responseTmp);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {
	}
}
