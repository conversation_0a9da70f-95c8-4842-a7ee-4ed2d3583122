<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.app.*"%>
<%@ page import="fai.web.*"%>
<%@ page import="fai.web.inf.*"%>
<%@ page import="fai.weboss.*"%>
<%@ page import="fai.comm.util.*"%>
<%@ page import="fai.webhdoss.app.*"%>


<%if(!WebHdOss.checkSession(response)){return;}%>
<%
if(!WebHdOss.checkHdOssAuth(HdOssStaffDef.Auth.HD_SALE)){
	out.println("没有权限--请让管理员设置新销售系统权限");
	return;
}%>

<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>领取数量查询</title>
		<%@ include file="/comm/link.jsp.inc"%>
		<%@ include file="/comm/script.jsp.inc"%>
		<link rel="stylesheet" type="text/css" href="<%=HdOssResDef.getResPath("css_hdSale")%>" />
	</head>
	<body id="hdsale-saleArpu">
		<!--页面顶部标题-->
		<!-- <div style="text-align: center;margin-bottom: 10px;">
			<b style="font-size: 20px;">互动销售领取情况</b>
		</div> -->

		<!--查询条件 start-->
		<div class="saleArpuDiv" v-cloak>
			<el-form :inline="true" :model="form" class="demo-form-inline" size="mini">
			    <el-form-item label="arpu日期">
					<el-date-picker class="fai-date" v-model="form.receiveDateBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="false"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.receiveDateEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="false" ></el-date-picker>
				</el-form-item>
				<el-form-item label="领取日期">
					<el-date-picker class="fai-date" v-model="form.receiveBeg" type="date"  placeholder="开始日期" value-format="yyyy-MM-dd" :editable="false"></el-date-picker>
					- <el-date-picker class="fai-date" v-model="form.receiveEnd" type="date"  placeholder="结束日期" value-format="yyyy-MM-dd" :editable="false" ></el-date-picker>
			    </el-form-item>
				<el-form-item label="销售">
					<el-select v-model="form.se_sacct" filterable>
						<el-option label="所有" value="all"></el-option>
						<el-option v-for="sale in preSaleList" :label="sale.nickName" :value="sale.acct" :key="sale.acct"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="注册来源">
					<el-select v-model="form.taGroup" filterable>
						<el-option v-for="taInfo in taList.labelList" :label="taInfo.name" :value="taInfo.label" :key="taInfo.label"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item label="排序">
					<el-select v-model="form.sort" filterable>
						<el-option v-for="info in arpuSort.labelList" :label="info.name" :value="info.label" :key="info.label"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item>
					<el-button icon="el-icon-search" type="primary"  @click="onSubmit">查询</el-button>
					<el-button icon="el-icon-download" type="export"  @click="exportExcel">导出</el-button>
				</el-form-item>
			</el-form>
		</div>
		<!--查询条件 end-->

		<!--数据表格 start-->
		<div class="saleArpuList"  v-cloak>
			<el-table :data="tableData" row-key="rowkey" stripe border show-summary class="arpuTable" height="750" >
				
				<el-table-column width="78px" label="注册来源" prop="groupName"></el-table-column>
				<el-table-column width="70px" label="人员" prop="nickName" ></el-table-column>
				<el-table-column width="70px" label="账号" prop="acct" ></el-table-column>
				<el-table-column width="100px" label="获取资源数(arpu日期)" prop="receiveCount" ></el-table-column>
				<el-table-column width="70px" label="付费数" prop="payCount" ></el-table-column>
				<el-table-column width="70px" label="付费率" prop="rate" ></el-table-column>
				<el-table-column width="80px" label="付费金额" prop="price" ></el-table-column>
				<el-table-column width="70px" label="arpu" prop="arpu" ></el-table-column>
				<el-table-column width="100px" label="arpu提升" prop="upArpu" ></el-table-column>
				<!-- 优化删除权重数量 -->
				<!--  <el-table-column width="80px" label="权重数量" prop="nNum" ></el-table-column>-->
				<el-table-column width="80px" label="领取数量（全部）" prop="receiveCountSelect"></el-table-column>
				<el-table-column width="90px" label="领取数量(不含奖励)" prop="notInAwardCount"></el-table-column>
				<el-table-column width="90px" label="领取数量（奖励）" prop="awardCount"></el-table-column>
			</el-table>
		<!-- 
		 <div class="rule">
				注：
				<div>1.权重系数=arpu提升=销售arpu-虚拟销售组的arpu</div>
				<div>2.虚拟销售权重数量设定为2</div>
				<div>3.各来源组资源，根据权重数量进行分配</div>
				<div>4.假设A销售自来的权重数量是1，B销售自来的权重数量是2 ，现有6条自来的资源，则按照权重系数高的优先分配，分配顺序为 B A B B A B</div>
				<div>5.每月1号-15号以上月下旬arpu进行分配，每月16-月末以本月1-15号的arpu进行分配。如8月31号分配的依据是8月1-15号销售的arpu</div>
			</div> -->
			
			<!-- <el-table :data="arpuRule" row-key="rowkey" stripe border  class="ruleTable">
					<el-table-column width="150px" label="权限系数" prop="upArpu"></el-table-column>
					<el-table-column width="150px" label="权重数量" prop="nNum" ></el-table-column>
			</el-table>
		</div>  
		 -->
		
		<!--数据表格 end-->
		<!--数据表格 start-->
		<!-- <div class="arpuRule" style="width: 265px;display:inline;" v-cloak>
				<el-table :data="arpuRule" row-key="rowkey" stripe border show-summary >
					<el-table-column width="150px" label="权限系数" prop="upArpu"></el-table-column>
					<el-table-column width="150px" label="权重数量" prop="nNum" ></el-table-column>
				</el-table>
		</div> -->
			<!--数据表格 end-->
	</body>


	<script type="text/javascript">
		var faiSearchObj = new Vue({
			el: '.saleArpuDiv',
			data: {
				form: {//这里是为了填充默认值
					receiveDateBeg: Fai.tool.dateFormatter(new Date().getTime() - 15*24*3600*1000),
					receiveDateEnd: Fai.tool.dateFormatter(new Date()),
					receiveBeg: Fai.tool.dateFormatter(new Date().getTime() - 24*3600*1000),
					receiveEnd: Fai.tool.dateFormatter(new Date()),
					se_sacct: "all",
					taGroup: "all",
					sort: "upArpu",
					export:false
				},
				preSaleList: [],
				arpuSort: [],
				taList: [],
			},
			created:function(){
				Fai.http.post("dataDef.jsp?cmd=getSaleArpuTag", "", false).then(result => {
					if(result.success){
						this.taList = result.taList;
						this.arpuSort = result.arpuSort;
						this.preSaleList = result.saleList;
					}
				});
				getDataList(this.form);
			},
			methods: {
				onSubmit() {
					this.form.export = false;
					getDataList(this.form);
				},
				exportExcel(){
					this.form.export = true;
					window.open("/ajax/hdSale_h.jsp?cmd=getSaleArpu" + Fai.tool.parseJsonToUrlParam(this.form));
				},
		    }
		});
		
		var faiDataList = new Vue({
			el: '.saleArpuList',
			data: {
				tableData: [],
				arpuRule:[
					{upArpu:"N＜0",nNum:1},
					{upArpu:"0＜N≤5",nNum:2},
					{upArpu:"5＜N≤10",nNum:3},
					{upArpu:"10＜N≤20",nNum:4},
					{upArpu:"20＜N≤30",nNum:5},
					{upArpu:"30＜N≤40",nNum:6},
					{upArpu:"40＜N≤50",nNum:7},
					{upArpu:"N>50",nNum:8}
				],
			},
			created:function(){
			},
	        updated:function(){
	        	//Fai.tool.scrollbar(this);
			},
			methods: {
		    }
		});
		// var arpuRule = new Vue({
		// 	el: '.arpuRule',
		// 	data: {
		// 		arpuRule:[
		// 			{upArpu:"N＜0",nNum:1},
		// 			{upArpu:"0＜N≤5",nNum:2},
		// 			{upArpu:"5＜N≤10",nNum:3},
		// 			{upArpu:"10＜N≤20",nNum:4},
		// 			{upArpu:"20＜N≤30",nNum:5},
		// 			{upArpu:"30＜N≤40",nNum:6},
		// 			{upArpu:"40＜N≤50",nNum:7},
		// 			{upArpu:"N>50",nNum:8}
		// 		],
		// 	},
		// 	created:function(){
		// 	},
	    //     updated:function(){
	    //     	//Fai.tool.scrollbar(this);
		// 	},
		// 	methods: {
		//     }
		// });
		// 获取数据
		function getDataList(urlParam){
			// 查询数据
			Fai.http.post("hdSale_h.jsp?cmd=getSaleArpu", urlParam, false).then(result => {
				if(result.success){
					faiDataList.tableData = result.dataList;
				}
			});
		}

	</script>

</html>



