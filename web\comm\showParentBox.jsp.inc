<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page session="false"%>
<%@ page import="java.util.*"%>
<%@ page import="fai.comm.util.*"%>

<!-- 用iframe的形式弹窗，给各个业务需要调用父页面弹窗的时候调用 -->
<fai-iframe-box-dialog 
  class="fai-iframe-box-dialog show-paren-box" 
  :src="src"
  :title="title"
  :visible.sync="dialogVisible" 
  :close="beforeClose" 
  :width.sync="width" 
  :height.sync="height">
</fai-iframe-box-dialog>


<script type="text/javascript">
	// 弹窗对象
	var faiParenBoxDailog = new Vue({
		el:".show-paren-box",
		data: {
			src: '',
			dialogVisible: false,
			width:"600px",
	        height:"500px",
	        title: "",
		},
		methods:{
			beforeClose: function() {
				closeFaiBoxDailog();
			}
		},
	});
	
	// 显示弹窗信息
	function showParentBox(iframeId){
		var iframe = Fai.iframe.getWindowName(iframeId);
		
		// 设置弹窗信息，并且打开
		faiParenBoxDailog.src =  iframe.src;;
	 	faiParenBoxDailog.title = iframe.title;
	 	if(iframe.width!=undefined && iframe.height!=undefined){
	 		faiParenBoxDailog.width =  iframe.width;
	 		faiParenBoxDailog.height =  iframe.height;
	 	}
	 	
	 	faiParenBoxDailog.dialogVisible = true;
		
		// 重设iframe高度和宽度，由iframe页面调用
		resetIframe = function(){
			// 没有设置高度跟宽度的时候才允许自适应页面
			if(iframe.width==undefined && iframe.height==undefined){
				Fai.iframe.setIframeAttr(faiParenBoxDailog);
			}
		}
		
		// iframe关闭
		closeFaiBoxDailog = function(isUp){
			// 关闭的时候设置回默认值
			faiParenBoxDailog.src = '';
			faiParenBoxDailog.dialogVisible = false;
			if(isUp){
				// 重新获取数据  getCorpInfo再corp页面
				getCorpInfo("aid", corpForm.form.aid);
			}
		}
	}
</script>