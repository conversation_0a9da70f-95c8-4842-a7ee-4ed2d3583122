<template>
  <div class="flex flex-wrap">
    <el-upload
      v-bind="$attrs" 
      v-on="$attrs"
      :action="curActionUrl"
      accept=".jpg,.png,.jpeg"
      name="filedata"
      :file-list="curFileList"
      :show-file-list="false"
      :class="{'hideUploadBtn': hideUploadBtn}"
      :on-preview="handlePictureCardPreview"
      :before-upload="beforeFileUpload"
      :on-remove="fileRemove"
      :on-success="fileUploadSuccess"
      :on-progress="fileUploadProgress"
      :on-error="fileUploadError"
      :limit="limit"
      :disabled="isUploading"
    >
      <!-- 上传按钮 -->
      <div v-if="!hideUploadBtn" class="upload-btn">
        <div class="upload-btn-content">
          <p class="text-[14px] text-center">{{ title }}</p>
          <el-progress 
            v-if="isUploading" 
            type="circle" 
            :percentage="percentage" 
            :width="30"
            :stroke-width="3"
            class="mt-[5px]">
          </el-progress>
        </div>
      </div>
    </el-upload>

    <!-- 图片列表 -->
    <div class="image-list">
      <div 
        v-for="item in curFileList" 
        :key="item.id" 
        class="image-item"
        @mouseenter="item.showMask = true"
        @mouseleave="item.showMask = false"
      >
        <img :src="item.url" :alt="item.name" class="image-preview">
        
        <!-- 悬停遮罩 -->
        <div v-show="item.showMask" class="image-mask">
          <div class="mask-buttons">
            <el-button 
              type="text" 
              size="mini" 
              @click="handlePreview(item)"
              class="mask-btn preview-btn"
            >
              <i class="el-icon-zoom-in"></i>
            </el-button>
            <el-button 

              type="text" 
              size="mini" 
              @click="handleDelete(item)"
              class="mask-btn delete-btn"
            >
              <i class="el-icon-delete"></i>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-dialog title="图片预览" :visible.sync="dialogVisible" height="60%" custom-class="preview-dialog">
      <img class="m-auto" mode="heightFix" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UploadImg',
  props: {
    title: {
      type: String,
      default: '上传图片'
    },
    fileList: {
      type: Array,
      default: () => []
    },
    limit: {
      type: Number,
      default: 1
    },
    actionUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isUploading: false,
      percentage: 0,
      curFileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
    }
  },
  computed: {
    hideUploadBtn() {
      return this.curFileList.length >= this.limit;
    },
    curActionUrl() {
      return this.actionUrl || '/api/template/uploadTmpFile';
    }
  },
  created() {
    this.curFileList = this.fileList.map(item => ({
      ...item,
      showMask: false
    }));
  },
  methods: {
    beforeFileUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$message.error('上传图片大小不能超过 5MB!');
        return false;
      }
      this.isUploading = true;
      this.percentage = 0;
      return true;
    },
    fileUploadSuccess(response) {
      this.percentage = 100;
      setTimeout(() => {
        this.isUploading = false;
        this.percentage = 0;
      }, 500);

      if (response.success) {
        this.$message({
          type: "success", 
          message: "文件上传成功！",
        });
        const data = response.data;
        const fileInfo = {
          name: data.name,
          url: data.path,  
          id: data.id,
          type: data.type,
          showMask: false
        }
        this.$emit('upload-success', fileInfo);
        this.curFileList.push(fileInfo);
      } else {
        this.$message({
          type: "error",
          message: response.msg || "文件类型不允许！",
        });
      }
    },
    fileUploadProgress(event) {
      this.percentage = Math.round(event.percent);
    },
    fileUploadError(err) {
      this.$message({
        type: "error",
        message: "系统繁忙，请稍后重试！",
      });
      this.isUploading = false;
    },
    fileRemove(file) {
      const index = this.curFileList.findIndex(item => item.uid === file.uid);
      if (index > -1) {
        this.curFileList.splice(index, 1);
      }
      this.$emit('upload-remove', file);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handlePreview(item) {
      this.dialogImageUrl = item.url;
      this.dialogVisible = true;
    },
    handleDelete(item) {
      const index = this.curFileList.findIndex(file => file.id === item.id);
      if (index > -1) {
        this.curFileList.splice(index, 1);
        this.$emit('upload-remove', item);
        this.$message.success('删除成功');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.hideUploadBtn {
  display: none;
}
.upload-btn {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  
  &:hover {
    border-color: #409eff;
  }
  
  .upload-btn-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 8px;
  }
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 90px;
  height: 90px;
  margin-right: 10px;
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  
  .image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .image-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: space-around;
    align-items: center;
    transition: opacity 0.3s;
    
    .mask-buttons {
      display: flex;
      // gap: 10px;
      
      .mask-btn {
        display: flex;
        justify-content: center;
        width: 20px;
        color: white;
        background: transparent;
        border: none;
        padding: 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
        
        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }
        
        &.preview-btn:hover {
          background: rgba(64, 158, 255, 0.8);
        }
        
        &.delete-btn:hover {
          background: rgba(245, 108, 108, 0.8);
        }
      }
    }
  }
}

::v-deep .preview-dialog {
  margin: 100px auto;
  height: 70vh;
  .el-dialog__body {
    height: 100%;
    img {
      height: 100%;
    }
  }
}
</style>
