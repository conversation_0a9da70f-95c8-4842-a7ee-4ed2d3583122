package fai.webhdoss.model.vo.scTemplate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ScTemplateResFormVO extends ScTemplateCommResVO {
    // id，来源于原型resForm
    private int id;

    // 素材名称
    private String label;

    // 视频封面图资源id，由设计师手动上传
    private String coverId;

    // 视频封面图资源类型
    private int coverType;
}
