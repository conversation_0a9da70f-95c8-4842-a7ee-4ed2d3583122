package fai.webhdoss.controller;

import fai.app.ScResDef;
import fai.hdUtil.JsonResult;
import fai.webhdoss.model.vo.scRes.ScResListVO;
import fai.webhdoss.model.vo.scRes.ScResVO;
import fai.webhdoss.service.ScResService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 素材库
 *
 * <AUTHOR>
 * @since 25-05-28 17:55
 */
@RestController
@RequestMapping("/resource")
public class ScResController {

    @Autowired
    private ScResService scResService;

    @GetMapping("/categoryList")
    public JsonResult categoryList(ScResVO vo) {
        return scResService.getScCategoryList(vo);
    }

    @GetMapping("/list")
    public JsonResult list(ScResListVO vo) {
        return scResService.getScResList(vo);
    }

    @PostMapping("/update")
    public JsonResult update(@RequestBody ScResVO vo) {
        return scResService.setScRes(vo);
    }

    @PostMapping("/delete")
    public JsonResult delete(@RequestBody ScResVO vo) {
        /*
        拿到 resId 和 cover，一并进行删除
         */
        return scResService.delScRes(vo);
    }

    // 添加背景音乐/配音
    @PostMapping("/add")
    public JsonResult add(@RequestBody ScResVO vo) {
        /*
        打开弹窗
        上传封面 -> scRes 写入 img 信息，状态为临时
        上传音频 -> scRes 写入 aud 信息，状态为临时
        此时有两个分支
        1.点击保存 -> 转正，更新音频 scRes 中的封面数据，同时封面、音频的 scRes 状态改为正式
        2.直接关闭弹窗 -> 不做任何操作，等待 job 删除临时 res
         */
        return scResService.addScRes(vo);
    }

    // 临时文件上传
    @RequestMapping("/uploadBgm")
    public JsonResult uploadBgm(HttpServletRequest request) {
        return scResService.uploadTmpFile(request, ScResDef.Type.OPT_BGM);
    }

    @RequestMapping("/uploadVoice")
    public JsonResult uploadVoice(HttpServletRequest request) {
        return scResService.uploadTmpFile(request, ScResDef.Type.OPT_VOICE);
    }
}
