﻿[v-cloak] { display: none !important; }

body{margin: 0;}
li{list-style-type:none;height:35px;line-height: 35px;}
a{color: #409EFF; background: 0 0; padding-left: 0; padding-right: 0;text-decoration:none;}
#fai-slider{width: 200px; z-index: 1999; position: fixed; bottom: 120px; margin-left: 40%;}
#fai-slider span{left: 0%; background: rgb(30, 159, 255);}



/*整体部分*/
::-webkit-scrollbar { width: 8px; height: 8px;}
::-webkit-scrollbar-track{background: #f2f4f7;}
/*滑块*/
::-webkit-scrollbar-thumb{ border-radius: 5px; -webkit-box-shadow: inset 0 0 5px #d8e0ea; background: #d8e0ea; }
/*滑块效果*/
::-webkit-scrollbar-thumb:hover{ border-radius: 5px; -webkit-box-shadow: inset 0 0 5px #c3cbd6; background: #c3cbd6; }

/*通用部分*/

.el-form-item { margin-bottom: 12px; backface-visibility: hidden;}
.el-form-item--mini.el-form-item, .el-form-item--small.el-form-item{margin-bottom: 12px; }
.el-form-item__error{padding-top: 0px;}
.el-form-item.is-success .el-input__inner{border-color: #dcdfe6;}
.el-form-item.is-success .el-input__inner:focus{border-color: #409eff;}
.el-input{width: 180px;}
.el-input input{height: 30px;}
.el-button--mini, .el-button--mini.is-round{ padding: 7px 15px !important;}
.el-collapse-item__content{padding: 10px 0;float: left;width: 100%;}
.el-collapse-item__header { height: 35px; line-height: 35px; background: #409eff; color: #fff; border-radius: 5px; text-align: right; }
i.el-collapse-item__arrow.el-icon-arrow-right{line-height: 35px; padding: 0 10px; margin: 0;}
.el-table a{color: #409EFF; background: 0 0; padding-left: 0; padding-right: 0;text-decoration:none;}
.el-button{margin-right: 10px;}
.el-button+.el-button{margin-left: 0;}
.el-button:last-child{margin-right: 0px;}
.el-button span{float: left;}
.el-select{width: 120px;}
.el-select .el-input{width: 100%;}
.el-slider__runway{background-color: #e4e7ed87;}
/*日期控件*/
/* .el-date-range-picker{width: 400px;} */
.el-date-table td{padding:0;}
.el-picker-panel{position: fixed !important;}
.el-picker-panel.el-date-picker.el-popper{width: 300px;}
.el-picker-panel.el-date-picker.el-popper.has-time{width: 440px;}
.el-date-picker .el-picker-panel__content{width: inherit;}
.el-table th{background:#f5f7fa;}


/*弹窗*/
.el-dialog{border-radius: 3px;max-width: 1400px; min-width: 400px; width: max-content;}
.el-dialog__wrapper{margin-left: -180px !important;}
.el-dialog__header{ padding: 10px 20px; border-bottom: 1px solid #e8ebef;}
.el-dialog__footer{padding: 10px 20px;}
.dialog-footer .el-button{padding: 9px 16px;}
.el-dialog__body{padding: 10px 2px; float: left; background: #fff;width: -webkit-fill-available;}
.el-dialog__wrapper .el-form-item{margin-bottom: 5px;}
.el-dialog__headerbtn{top: auto; line-height: 24px;}

/*弹窗内容  calc 因为这里是根据内容的宽度去适应的，当有滚动条的时候，会导致里面有一部分控件给滚动条给占用了，所以这里宽度要百分比加上滚动条的宽度*/
.fai-box-main{max-height: 400px;overflow-y: auto;padding: 0 10px;box-sizing: border-box; width: calc(100% + 8px); }
.fai-box-span{min-width: 200px; height: 35px;line-height: 35px; float: left;padding-right: 10px;box-sizing: border-box;}
.el-dialog__body>.fai-box-main{float: right;padding-left: 18px;}
.el-dialog__body>iframe{width: calc(100% + 4px) !important; float: right;padding-left: 8px;}


/*FAI通用部分*/
.fai-body{margin: 0; min-width: 1400px;}
.fai-wid-100 {width: 100px !important; float: left;}
.fai-wid-200{width: 200px !important; float: left;}
.fai-wid-200>div{width: 200px !important;}

/*链接按钮形状*/
.fai-but-a{ margin: 10px 10px 0 0; text-decoration: none; padding: 8px 10px; font-size: 12px; background: #3a8ee6; border-color: #3a8ee6; color: #fff; display: inline-block; line-height: 1; white-space: nowrap; cursor: pointer; -webkit-appearance: none; text-align: center; -webkit-box-sizing: border-box; box-sizing: border-box; outline: 0; -webkit-transition: .1s; transition: .1s; font-weight: 500; border-radius: 3px; }
.fai-but-a:hover{ background: #66b1ff; border-color: #66b1ff; color: #fff; }
.fai-a{ background: #F2F6FF !important; font-size: 12px; padding: 5px 14px !important; color: #409EFF !important;}
.fai-a:hover{ background: #e5edff !important; color: #409EFF !important;}


.fai-textarea{color: #000; margin-bottom: 10px;}
.fai-textarea .el-textarea__inner{color: #000 !important; }
.fai-page { margin: 20px 0; }

.fai-checkbox-normal>.el-checkbox__label{white-space: normal;}
.el-checkbox+.el-checkbox{margin-left: 0px;margin-right: 30px;}

/*日期控件*/
.fai-daterange{width: 135px !important; padding-right: 2px !important;}
.fai-daterange>.el-range-separator{float: left;}
.fai-daterange>.el-range-input{float: left;width: 70px !important;}

.fai-date{width: 124px !important; }
.fai-date>.el-range-separator{float: left;}
.fai-date>.el-range-input{float: left;width: 70px !important;}

/*表格expand*/
.fai-table-expand { font-size: 0; }
.fai-table-expand label { width: 120px; color: #99a9bf; text-align: right !important;}
.fai-table-expand .el-form-item { margin-right: 0; margin-bottom: 0;  }
.fai-table-expand .el-form-item__content{min-width: 80px;}

/*loading*/
.fai-loading{width: 100%; height: 100%; position: absolute;}



/*头部*/
.el-header.fai-header {background: #F4F7FE; min-height: 65px;z-index: 999; padding: 0;}
.el-header .fai-header-left{float: left;width: 170px;min-height: 65px;margin-right: 10px; background: #EAEEF9;}
.el-header .fai-header-middle{float: left;min-height: 65px; box-sizing: border-box;}
.el-header .fai-header-rigth{display: flex;align-items: center;float: right;width: 200px;min-height: 65px;}
.el-menu--horizontal{background: #F4F7FE;}
.el-menu--horizontal>.el-menu-item.is-active{color: #4182FF; font-size: 16px;}
.el-menu--horizontal>.el-menu-item{color: #000000;font-size: 16px;}

/*login*/
.fai-login { position: absolute; width: 170px; font-size: 55px; height: 65px; line-height: 65px; z-index: 1001; background: url(/image/login.png?v=202102011124) no-repeat 13px 13px;}

/*主体部分*/
.fai-main { color: #333; height: 100%; min-height: 600px; padding: 0; float: left; position: absolute; width: 100%; padding-top: 65px; box-sizing: border-box; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; min-width: 1400px;}


/*侧边导航*/
.fai-main-left { max-width: 170px; min-width: 56px; height: 100%; float: left; overflow-y: auto; background: #F4F7FE;}
.fai-main-left .el-menu-item.is-active{color: #4182FF; background: #fff; }
.fai-main-left .el-menu-item{color: #000000;}
.fai-main-left .el-menu-item:hover{background: #fff;}

.fai-main-nav{ text-align: left; overflow: hidden;}
.fai-main-nav .el-menu--collapse{background: #F4F7FE;}
.fai-main-nav .el-menu-vertical:not(.el-menu--collapse) { width: 170px; min-height: 100%; background: #F4F7FE;}

/*右边内容模块*/
.fai-main-rigth { padding: 15px 10px 0 10px;height: 100%; overflow: hidden; box-sizing: border-box; }

#fai-iframe {
    padding: 5px;
}

/*cs公共部分*/
.fai-cs .corp-comm-info{ width: 100%; float: left; }
.fai-cs .corp-comm-info .el-collapse-item{ width: 100%; }
.fai-cs .el-button--primary{padding: 9px 16px; font-size: 12px; border-radius: 3px;}

/*中间共用部分*/
.fai-cs .fai-cs-mid .el-form-item{margin-bottom: 5px;}
.fai-cs .fai-cs-mid .el-form-item__content{line-height: 24px;}
.fai-cs .fai-cs-mid .el-form-item__label{line-height: 24px; }
.fai-cs .fai-cs-mid .el-collapse{border:none;}


/*cs corp*/
/*ca 布局*/
.fai-cs{ width: 100%; height: 100%; /*position: fixed;*/ }
.fai-cs .fai-cs-left{ width: 350px; height: 100%; float: left; margin-right: 20px; }
.fai-cs .fai-cs-mid{ max-width: 800px; min-width: 500px;height: 100%; float: left; overflow: hidden;}

/*cs查询部分*/
.corp-form,.corp-acct-list,.corp-qq-list { width: 350px; float: left; }
.corp-form .coro-form-showBut{float: right;margin-right: 10px;}
.corp-form .coro-form-showBut button{padding: 5px 5px !important;margin-left: 0px;}
.corp-form-opt{float: left;}
.corp-form-opt a{ margin: 2px 10px 2px 2px; color: #8f9aaf; white-space:nowrap;}
.corp-form-opt a:hover{ color: #102041;}

.corp-form-opt-cut a{ margin: 2px 0px 2px 2px; padding-right: 8px; border-right: 1px solid #8f9aaf; white-space:nowrap;}
.corp-form-opt-cut a:last-child{ border-right: 0;}

/*多用户*/
.corp-acct-list{ max-height: 500px; overflow-y: auto;}
.corp-acct-list .el-form-item { height: 22px; line-height: 22px; }
.corp-acct-list .el-form-item__content { height: 22px; line-height: 22px; }
.corp-acct-list .el-form-item__label { height: 22px; line-height: 22px; }

/*常用QQ*/
.corp-qq-list .el-form-item { height: 22px; line-height: 22px; }
.corp-qq-list .el-form-item__content { height: 22px; line-height: 22px; }
.corp-qq-list .el-form-item__label { height: 22px; line-height: 22px; }

/*共用信息部分*/
.fai-cs .corp-comm-info .corp-comm-info-wid50 .el-form-item{width: 50%; float: left;}
.fai-cs .corp-comm-info .corp-comm-info-wid50 .el-form-item>.el-form-item__label { text-align: left; }
.corp-comm-info-but{width: 100%; margin-bottom: 10px; float: left;}
.corp-comm-info-but .el-button{margin: 10px 10px 0 0;padding: 8px 10px;}

/*域名列表*/
.show-bind-domain-list li{width:100%;float: left;}
.show-bind-domain-list .el-dialog__header{height: 45px; box-sizing: border-box;}
.show-bind-domain-list .el-dialog__title{width: 100%;margin-top: -45px;padding: 0 20px;}
.show-bind-domain-list .fai-box-main{ margin-top: 15px;}

.show-bind-email-domain-list li{float: left;}
.show-bind-email-domain-list li:nth-child(2n+1){clear: both;}

/*添加常用QQ*/
.show-acct-qq-list .el-form-item__label{ text-align: left; }
.show-acct-qq-list .fai-box-span{width: 120px; min-width: 120px;}

/*完整信息*/
.show-acct-full-info .el-form{height: 580px;overflow-y: auto;}
.show-acct-full-info .el-form-item__content{line-height: 24px;}
.show-acct-full-info .el-form-item__label{line-height: 24px;}
.show-acct-full-info .el-collapse{border:none;}
.show-acct-full-info .tag{width: 100%; margin: 5px 0;}

/*域名分析*/
.show-domain-analyze li{width: 100%; }


/*员工列表*/
.show-staff-list .el-form-item__label{line-height: 24px;}
.show-staff-list .el-form-item__content{line-height: 24px;}

/*iframe弹窗嵌套*/
.fai-iframe-box-div{ max-height: -webkit-fill-available; overflow-y: auto;padding: 0 5px; box-sizing: border-box;}
.fai-iframe-box .fai-dialog__footer { position: fixed; bottom: 0;  background: #fff; width: calc(100% - 4px) !important;right: 0; float: left; padding: 10px 20px;}
.fai-iframe-box{ padding-bottom: 44px; height: 100%;backface-visibility: hidden;}